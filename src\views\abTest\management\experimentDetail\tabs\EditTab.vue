<template>
  <!-- 实验详情 -->
  <a-form ref="editFormRef" :model="form" :rules="rules" label-align="left" style="width: 100%; padding-right: 40px">
    <div class="form-block-label">
      <span class="form-block-label-title">基本配置</span>
      <div class="form-block-label-extra"></div>
    </div>
    <a-form-item field="name" label="实验名称" validate-trigger="blur">
      <a-input v-model="form.name" max-length="200" placeholder="请输入专属名称，最长不超过200字符，例：App端场景实验-版本3.0" />
    </a-form-item>
    <a-form-item field="description" label="实验描述">
      <a-textarea v-model="form.description" max-length="1000" placeholder="请输入描述信息，可简述当前实验的含义、目的、内容等信息，便于后期查看和管理，支持1000个字符以内  例：测试苹果和安卓手机App端双十二运营场景推送形式及策略" />
    </a-form-item>
    <a-form-item field="tags" label="实验标签" tooltip="在实验列表中可以通过实验标签快速搜索到相关实验，一般采用业务线、优化方向等实验内容关键词作为标签。">
      <a-select v-model:model-value="form.tags" :loading="tagLoading" placeholder="请选择" multiple allow-search allow-create>
        <a-option v-for="item of tagOptions" :key="item" :value="item">{{ item }}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="duration" label="实验时长" validate-trigger="blur">
      <a-input-number v-model="form.duration" :step="1" :min="1" style="width: 100px">
        <template #suffix> 天 </template>
      </a-input-number>
      <template #extra>
        <div> 建议至少覆盖两个完整的实验周期(14天) </div>
      </template>
    </a-form-item>
    <a-form-item field="dataSource" label="数据来源">
      <selectDataSource v-model:data-source="form.dataSource" disable-storage />
    </a-form-item>
    <div class="form-block-label">
      <span class="form-block-label-title">生效逻辑设置</span>
      <div class="form-block-label-extra"></div>
    </div>
    <a-form-item field="isMutualExclusion" label="是否互斥">
      <a-radio-group v-model:model-value="form.isMutualExclusion" @change="exclusionChange">
        <a-radio :value="0">否</a-radio>
        <a-radio :value="1">流量层互斥</a-radio>
        <a-radio :value="2">直属互斥域</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item v-if="form.isMutualExclusion === 1" field="flowLayerCode" label="" :hide-asterisk="true">
      <a-select v-model:model-value="form.flowLayerCode" :style="{ width: '320px' }" :loading="layerLoading" placeholder="请选择流量层" :filter-option="false" allow-search @search="handleLayerSearch" @change="layerChange">
        <a-option v-for="layer of layerOptions" :key="layer.code" :value="layer.code">{{ layer?.flowDomain?.domainGroup?.name }} - {{ layer?.flowDomain?.name }} - {{ layer.name }}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item v-if="form.isMutualExclusion === 2" field="flowDomainCode" label="" :hide-asterisk="true">
      <a-select v-model:model-value="form.flowDomainCode" :style="{ width: '320px' }" :loading="domainLoading" placeholder="请选择互斥域" :filter-option="false" allow-search @search="handleDomainSearch">
        <a-option v-for="domain of domainOptions" :key="domain.code" :value="domain.code">{{ domain?.domainGroup?.name }} - {{ domain.name }}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="flowPercentage" label="实验流量" validate-trigger="blur">
      <a-input-number v-model="form.flowPercentage" :step="0.1" :precision="1" :formatter="formatter" :parser="parser" :max="remainFlowPercentage" style="width: 330px">
        <template #suffix> 该流量层最多可占用{{ remainFlowPercentage }}% </template>
      </a-input-number>
    </a-form-item>
    <a-form-item field="enableGradual" label="流量生效方式">
      <a-radio-group v-model:model-value="form.enableGradual">
        <a-radio :value="false">立即生效</a-radio>
        <a-radio :value="true">平滑生效</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item v-if="form.enableGradual" field="gradualDuration" label="平滑生效时间">
      <a-input-number v-model="form.gradualDuration" :step="1" :min="0" style="width: 200px" placeholder="建议0-1440区间">
        <template #suffix> min </template>
      </a-input-number>
    </a-form-item>
    <a-form-item field="userScope" label="受众范围">
      <a-select v-model="form.userScope" :style="{ width: '330px' }" placeholder="请选择受众范围" @change="scopeChange">
        <a-option value="all">活跃用户</a-option>
        <a-option value="new">新增用户</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="userRules" label="受众规则">
      <div>
        <cluster-index ref="tagConditionRef" :only-evt="true" :is-provide-client="true" :user-filter="form.userRules" @change="filtersChange" />
        <a-dropdown trigger="hover" :popup-translate="[18, 0]">
          <div class="ta-filter-button">
            <div class="ta-filter-button-icon">
              <icon-plus />
            </div>
            添加筛选条件
          </div>
          <template #content>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('sequentially')">做过/没做过的事件</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('inOrder')">依次/没依次做过的事件</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('userProperty')">用户属性满足</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('groupProperty')">用户分群满足</a-doption>
            <a-doption @click="addEvent('clientParams')">客户端参数</a-doption>
          </template>
        </a-dropdown>
      </div>
    </a-form-item>
    <a-form-item field="consistency" label="体验一致性" tooltip="开启后，实验期间用户生效版本不随流量、受众条件调整发生变化">
      <a-switch v-model="form.consistency" disabled :checked-value="true" :unchecked-value="false" />
    </a-form-item>
    <div class="form-block-label">
      <span class="form-block-label-title">实验版本参数配置</span>
      <div class="form-block-label-extra"></div>
    </div>
    <a-form-item field="associatedRemote" label="是否关联远程配置">
      {{ form.associatedRemote === 1 ? '关联' : '不关联' }}
    </a-form-item>
    <a-form-item v-if="form.associatedRemote" field="remoteConfigCode" label="关联的远程配置">
      {{ form.remoteConfigCode }}
    </a-form-item>
    <a-form-item field="domains" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
      <div v-if="versionList.length" class="domains-content" style="width: 100%">
        <div v-for="(item, index) in versionList" :key="index" class="domain-item">
          <div class="head">
            <div class="justify" style="height: 100%">
              <div class="content">
                <a-space>
                  <icon-up v-if="item.showContent" class="icon-style" @click="() => (item.showContent = false)" />
                  <icon-down v-else class="icon-style" @click="() => (item.showContent = true)" />
                  <span v-if="!item.showNameInput">{{ item.name }}</span>
                  <a-input v-else v-model="item.name" max-length="50" style="width: 100px" @blur="item.showNameInput = false" />
                  <icon-edit class="icon-style" @click="() => (item.showNameInput = !item.showNameInput)" />
                </a-space>
              </div>
              <div class="extra">
                <icon-copy v-if="versionList.length > 1" style="margin-right: 8px; cursor: pointer" @click="copyVersion(index)" />
                <a-popconfirm content="确认删除吗?" @ok="deleteList(item, index)">
                  <icon-delete v-if="versionList.length > 1" style="cursor: pointer" />
                </a-popconfirm>
              </div>
            </div>
          </div>
          <div v-if="item.showContent" class="content-info">
            <div class="content-box">
              <div class="item-left">
                <a-form-item field="type" label="版本类型">
                  <a-radio-group v-model:model-value="item.type" @change="versionTypeChange">
                    <a-radio :value="0">对照组</a-radio>
                    <a-radio :value="1">实验组</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item field="description" label="描述">
                  <a-textarea v-model="item.description" />
                </a-form-item>
                <a-form-item field="testUserCodes" label="测试用户">
                  <a-textarea v-model="item.testUserCodes" placeholder="多个用户请用,逗号分割" />
                </a-form-item>
                <a-form-item field="whitelistCodes" label="白名单">
                  <a-select v-model:model-value="item.whitelistCodes" placeholder="请选择测试白名单" multiple allow-search :max-tag-count="2" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }">
                    <a-option v-for="white in whiteList" :key="white.code" :value="white.code">
                      {{ white.name }}
                    </a-option>
                  </a-select>
                </a-form-item>
              </div>
              <div class="item-right">
                <div style="color: var(--color-text-2); font-size: 14px; line-height: 32px">参数配置</div>
                <div class="form-content">
                  <div v-for="(config, configIndex) in item.configParams" :key="configIndex" class="config-box">
                    <div class="wrapper">
                      <a-select v-model:model-value="config.name" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" allow-create style="width: 180px" :disabled="form.associatedRemote === 1" @change="nameChange(configIndex, $event)">
                        <a-option v-for="remote in remoteList" :key="remote.name" :value="remote.name">
                          {{ remote.name }}
                        </a-option>
                      </a-select>
                    </div>
                    <div class="wrapper">
                      <a-select v-model:model-value="config.dataType" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" style="width: 120px" :disabled="form.associatedRemote === 1" @change="dataTypeChange(configIndex, $event)">
                        <a-option v-for="type in dataTypeList" :key="type.value" :value="type.value">
                          {{ type.label }}
                        </a-option>
                      </a-select>
                    </div>
                    <div class="wrapper flex1">
                      <a-input v-if="config.dataType === 'string'" v-model="config.value" />
                      <a-input-number v-if="config.dataType === 'int'" v-model="config.value" :min="0" :max="9999999999" :step="1" :precision="0" />
                      <a-input-number v-if="config.dataType === 'float'" v-model="config.value" :min="0" :max="9999999999" :step="0.00001" :precision="5" />
                      <a-select v-if="config.dataType === 'boolean'" v-model:model-value="config.value" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }">
                        <a-option value="true">True</a-option>
                        <a-option value="false">False</a-option>
                      </a-select>
                      <a-input v-if="config.dataType === 'json'" v-model="config.value" />
                      <div v-if="config.dataType === 'json'" class="globalEdit" @click="openJsonModal(index, configIndex)">
                        <a-tooltip content="全局编辑器">
                          <icon-expand />
                        </a-tooltip>
                      </div>
                    </div>
                    <div v-if="item.configParams.length > 1" class="delete-icon" @click="deleteConfigParam(configIndex)">
                      <icon-delete />
                    </div>
                  </div>
                  <div v-if="form.associatedRemote === 0" class="ta-filter-button" @click="addConfigParam">
                    <div class="ta-filter-button-icon">
                      <icon-plus />
                    </div>
                    添加配置参数
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="text-align: left">
          <a-button type="outline" style="border-radius: 4px" @click="addList">
            <template #icon>
              <icon-plus />
            </template>
            添加实验版本
          </a-button>
          <div v-if="validateText" class="error-text">{{ validateText }}</div>
        </div>
      </div>
      <span v-else>暂无实验版本</span>
    </a-form-item>
    <div class="form-block-label">
      <span class="form-block-label-title">流量分配</span>
      <div class="form-block-label-extra">
        <span style="color: #8b8ba6">流量均匀分配：</span>
        <a-switch v-model:model-value="isEvenDistribution" @change="handleDistributionChange" />
      </div>
    </div>
    <a-form-item field="ratios" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }" validate-trigger="change">
      <a-table :columns="columns" :data="tableData" :pagination="false" :style="{ width: '100%' }">
        <template #flowPercentage="{ record, rowIndex }">
          <a-input-number v-model="record.flowPercentage" :min="0" :max="100" :step="0.1" :precision="1" :disabled="isEvenDistribution || rowIndex === tableData.length - 1" :style="{ width: '120px' }" @change="(value) => handleRatioChange(value, rowIndex)">
            <template #suffix>%</template>
          </a-input-number>
        </template>
        <template #actualFlow="{ record }">
          {{ `${record.flowPercentage}% 实验流量 * 流量分配占比` }}
        </template>
      </a-table>
    </a-form-item>
    <div class="form-block-label">
      <span class="form-block-label-title">实验核心指标配置</span>
      <div class="form-block-label-extra"></div>
    </div>
    <div class="group-select">
      <a-form-item field="coreIndicatorCode" label="" :label-col-props="{ span: 0 }">
        <a-cascader v-model:model-value="form.coreIndicatorCode" :options="options" :style="{ width: '300px' }" placeholder="请选择核心指标" allow-search />
      </a-form-item>
    </div>
    <div class="form-block-label">
      <span class="form-block-label-title">实验关注指标配置</span>
      <div class="form-block-label-extra"></div>
    </div>
    <div class="indicator-config">
      <a-input-search v-model="searchValue" :style="{ width: '320px', marginBottom: '16px' }" placeholder="输入指标关键词" />
      <a-tree v-model:checked-keys="form.concernIndicatorCodes" :checkable="true" :data="filteredTreeData" :default-expanded-keys="[]" />
    </div>
    <JsonEditModal ref="jsonRef" @update-json="updateJson" />
    <CheckModal ref="checkRef" @skip-check="handleSubmit" />
  </a-form>
</template>

<script setup lang="ts">
  import { computed, reactive, ref, watch } from 'vue';
  import { useSessionStorage } from '@vueuse/core';
  import ClusterIndex from '@/views/analyse/components/userFilterComponent/ClusterIndex.vue';
  import { toolStore } from '@/store';
  import { cloneDeep } from 'lodash';
  import { getConfigParamList } from '@/api/marketing/api';
  import { getUserWhiteList, getExperimentTagList, getLayerList, getDomainList, saveExperiment, checkExperimentConflict } from '@/api/ab/api';
  import selectDataSource from '@/components/selected-data-source/index.vue';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { removeEnumList } from '@/views/analyse/components/util/verify';
  import { validateClientConditionRules, paramsVerify } from '@/views/abTest/commonUtil';
  import JsonEditModal from '@/views/abTest/management/createExperiment/components/JsonEditModal.vue';
  import CheckModal from '@/views/abTest/management/createExperiment/components/CheckModal.vue';

  const props = defineProps({
    detailData: {
      type: Object,
      default: () => ({}),
    },
  });
  const route = useRoute();
  const appId = ref(useSessionStorage('app-id', '')?.value);
  const toolData = toolStore();
  const emits = defineEmits(['updateDetail']);
  const form = reactive({
    code: '',
    name: '',
    description: '',
    tags: [],
    duration: 30,
    flowPercentage: 0,
    enableGradual: false,
    gradualDuration: 0,
    userScope: 'all',
    userRules: {},
    consistency: true,
    isMutualExclusion: 0,
    flowLayerCode: '',
    flowDomainCode: '',
    associatedRemote: 0,
    remoteConfigCode: '',
    coreIndicatorCode: '',
    concernIndicatorCodes: [],
    dataSource: '',
  });
  const rules = {
    name: [
      {
        required: true,
        message: '请填写实验名称',
      },
    ],
    duration: [
      {
        required: true,
        message: '请填写实验时长',
      },
    ],
    enableGradual: [
      {
        required: true,
        message: '请选择生效方式',
      },
    ],
    consistency: [
      {
        required: true,
        message: '请选择体验一致性',
      },
    ],
    gradualDuration: [
      {
        required: true,
        message: '请输入平滑生效时间',
      },
    ],
    userScope: [
      {
        required: true,
        message: '请选择受众范围',
      },
    ],
    flowLayerCode: [
      {
        required: true,
        message: '请选择流量层',
      },
    ],
  };
  const versionList = ref<any>([]);
  const whiteList = ref<any>([]);
  // 表格数据
  const tableData = computed(() => {
    return versionList.value.map((item) => ({
      name: item.name,
      flow: '100%',
      flowPercentage: item.flowPercentage,
      actualFlow: '',
    }));
  });
  const isEvenDistribution = ref(false); // 是否开启均匀分配
  const columns = [
    {
      title: '版本名称',
      dataIndex: 'name',
      width: 200,
      slotName: 'name',
    },
    {
      title: '实验流量',
      dataIndex: 'flow',
      width: 200,
      slotName: 'flow',
    },
    {
      title: '流量分配占比',
      dataIndex: 'flowPercentage',
      width: 200,
      slotName: 'flowPercentage',
    },
    {
      title: '实际生效流量',
      dataIndex: 'actualFlow',
      width: 200,
      slotName: 'actualFlow',
    },
  ];
  const dataTypeList = [
    {
      value: 'string',
      label: '字符串',
    },
    {
      value: 'int',
      label: '整数',
    },
    {
      value: 'float',
      label: '小数',
    },
    {
      value: 'boolean',
      label: '布尔值',
    },
    {
      value: 'json',
      label: 'JSON',
    },
  ];
  const searchValue = ref('');
  const options = computed(() => {
    const indicatorList = toolData.toolModelList
      .map((category) => ({
        ...category,
        items: (category.items || []).filter((item) => item.objectType === 'indicator'),
      }))
      .filter((category) => category.items && category.items.length > 0);
    return indicatorList.map((category) => ({
      value: category.category,
      label: category.category,
      children: (category.items || []).map((item) => ({
        value: item.code,
        label: item.name ? `${item.name}-${item.displayName}` : item.displayName,
      })),
    }));
  });
  const tagOptions = ref<any>([]);
  const tagLoading = ref(false);
  const layerLoading = ref(false);
  const layerOptions = ref<any>([]);
  const domainLoading = ref(false);
  const domainOptions = ref<any>([]);
  const remoteList = ref<any>([]);
  const editFormRef = ref();
  const getTags = async () => {
    tagLoading.value = true;
    try {
      const res = await getExperimentTagList();
      tagOptions.value = res || [];
    } catch (e) {
      //   options.value = [];
    } finally {
      tagLoading.value = false;
    }
  };
  // 获取最多可占用流量
  const remainFlowPercentage = ref(0);
  // 是否互斥切换
  const exclusionChange = (value) => {
    if (value === 0) {
      form.flowLayerCode = '';
      remainFlowPercentage.value = 100;
    }
  };
  // 获取流量层列表
  const getLayerOptions = async (name?: string) => {
    layerLoading.value = true;
    try {
      const params = {
        name: name || '',
        current: 1,
        pageSize: 20,
      };
      const res = await getLayerList(params);
      layerOptions.value = res.items || [];
    } catch (e) {
      layerOptions.value = [];
    } finally {
      layerLoading.value = false;
    }
  };
  const handleLayerSearch = async (value) => {
    if (value) {
      await getLayerOptions(value);
    } else {
      layerOptions.value = [];
    }
  };
  const layerChange = (value) => {
    const selectedLayer = layerOptions.value.find((item) => item.code === value);
    remainFlowPercentage.value = selectedLayer?.remainFlowPercentage;
  };
  // 获取互斥域列表
  const getDomainOptions = async (name?: string) => {
    domainLoading.value = true;
    try {
      const params = {
        name: name || '',
        current: 1,
        pageSize: 20,
      };
      const res = await getDomainList(params);
      domainOptions.value = res.items || [];
    } catch (e) {
      domainOptions.value = [];
    } finally {
      domainLoading.value = false;
    }
  };
  const handleDomainSearch = async (value) => {
    if (value) {
      await getDomainOptions(value);
    } else {
      domainOptions.value = [];
    }
  };
  const formatter = (value: number | string) => {
    return `${value}%`;
  };

  const parser = (value: string) => {
    return value.replace('%', '');
  };
  const scopeChange = () => {
    form.userRules = {};
  };
  const filtersChange = (v) => {
    form.userRules = v;
  };
  const tagConditionRef = ref();
  /**
   * 添加筛选条件
   * @param type 条件类型
   */
  const addEvent = async (type: string) => {
    switch (type) {
      case 'sequentially':
        tagConditionRef.value.add();
        break;
      case 'inOrder':
        tagConditionRef.value.addDone();
        break;
      case 'userProperty':
        tagConditionRef.value.addUser();
        break;
      case 'groupProperty':
        tagConditionRef.value.addGroup();
        break;
      case 'clientParams':
        tagConditionRef.value.addClientParams();
        break;
      default:
        break;
    }
  };
  // 校验文字
  const validateText = ref('');
  // 校验版本类型
  const validateType = () => {
    const hasControlGroup = versionList.value.some((item) => item.type === 0);
    const hasExperimentGroup = versionList.value.some((item) => item.type === 1);
    if (!hasControlGroup || !hasExperimentGroup) {
      validateText.value = '至少需要一个对照组或实验组';
      return false; // 校验失败
    }
    validateText.value = '';
    return true; // 校验成功
  };
  // 校验版本配置
  const validateConfigParams = () => {
    let isValid = true;
    versionList.value.forEach((version) => {
      version.configParams.forEach((config) => {
        if (form.associatedRemote === 1) {
          if (String(config.value) === '' || config.value === undefined || config.value === null) {
            isValid = false;
            validateText.value = '参数配置的值不能为空';
          }
        }
        if (form.associatedRemote === 0) {
          if (!config.name || String(config.value) === '' || config.value === undefined || config.value === null) {
            isValid = false;
            validateText.value = '参数配置的名称和值不能为空';
          }
        }
      });
    });
    if (isValid) {
      validateText.value = '';
    }
    return isValid;
  };
  // 类型change
  const versionTypeChange = () => {
    validateType();
  };
  const nameChange = (configIndex: number, value: any) => {
    // console.log(value,'value');
    const matchedRemote = remoteList.value.find((remote) => remote.name === value);
    versionList.value.forEach((version) => {
      version.configParams[configIndex].name = value;
      version.configParams[configIndex].dataType = matchedRemote?.valueType || 'string';
      version.configParams[configIndex].value = matchedRemote?.conditions?.[0]?.value;
    });
  };
  const dataTypeChange = (configIndex: number, value: any) => {
    versionList.value.forEach((version) => {
      version.configParams[configIndex].dataType = value;
      version.configParams[configIndex].value = undefined;
    });
  };

  // json编辑器
  const jsonRef = ref();
  // 卡片index
  const versionIndex = ref(0);
  // 参数值index
  const valueIndex = ref(0);
  const openJsonModal = (index: number, configIndex: number) => {
    versionIndex.value = index;
    valueIndex.value = configIndex;
    jsonRef.value.openModal(versionList.value[index].configParams[configIndex].value);
  };
  const updateJson = (val: any) => {
    versionList.value[versionIndex.value].configParams[valueIndex.value].value = val;
  };
  const tableKey = ref(0);
  // 如果开启了均匀分配，需要重新计算所有版本的比例
  const evenDistributionComputed = () => {
    if (isEvenDistribution.value) {
      const evenRatio = 100 / versionList.value.length;
      const ratioValue = Number(evenRatio.toFixed(1));
      // 用新数组整体替换，确保响应式
      versionList.value = versionList.value.map((item) => ({
        ...item,
        flowPercentage: ratioValue,
      }));
    }
    tableKey.value++; // 强制刷新
    editFormRef.value.validate(['ratios']);
  };
  // 添加配置参数
  const addConfigParam = () => {
    versionList.value.forEach((version) => {
      version.configParams.push({
        name: '',
        dataType: 'string',
        value: '',
      });
    });
  };
  // 删除配置参数
  const deleteConfigParam = (configIndex: number) => {
    versionList.value.forEach((version) => {
      version.configParams.splice(configIndex, 1);
    });
  };
  // 添加实验版本
  const addList = () => {
    const lastVersion = versionList.value[versionList.value.length - 1];
    const newConfigParams = lastVersion
      ? JSON.parse(JSON.stringify(lastVersion.configParams))
      : [
          {
            name: '',
            dataType: 'string',
            value: '',
          },
        ];
    versionList.value.push({
      name: `版本${versionList.value.length + 1}`,
      type: 1,
      description: '',
      showContent: true,
      showNameInput: false,
      flowPercentage: 0,
      whitelistCodes: [],
      testUserCodes: '',
      configParams: newConfigParams,
    });
    // 如果开启了均匀分配，需要重新计算所有版本的比例
    evenDistributionComputed();
  };
  // 复制实验版本
  const copyVersion = (index: number) => {
    const targetVersion = versionList.value[index];
    const copyItem = {
      ...targetVersion,
      code: '',
      name: `${targetVersion.name}-copy`,
      flowPercentage: 0,
      showContent: true,
      showNameInput: false,
    };
    versionList.value.splice(index + 1, 0, copyItem);
    // 如果开启了均匀分配，需要重新计算所有版本的比例
    evenDistributionComputed();
  };
  const deleteList = async (data: any, index: number) => {
    versionList.value.splice(index, 1);
    if (versionList.value.length === 1) {
      versionList.value[0].flowPercentage = 100;
    }
    evenDistributionComputed();
  };
  // 处理均匀分配开关
  const handleDistributionChange = (value) => {
    evenDistributionComputed();
  };
  // 处理比例变化
  const handleRatioChange = (value: number, index: number) => {
    // 计算其他非禁用选项的总和
    const sum = versionList.value.reduce((acc, item, idx) => {
      if (idx !== versionList.value.length - 1) {
        return acc + (idx === index ? value : item.flowPercentage || 0);
      }
      return acc;
    }, 0);
    // 更新值
    if (sum <= 100) {
      versionList.value[index].flowPercentage = value;
      // 更新最后一个版本的 flowPercentage
      versionList.value[versionList.value.length - 1].flowPercentage = Number((100 - sum).toFixed(1));
    }
    editFormRef.value.validate(['ratios']);
  };
  const pageParams = reactive({
    current: 1,
    pageSize: 20,
    text: '',
  });
  // 获取参数配置列表
  const getRemoteList = async () => {
    const data = {
      appId: appId.value,
      ...pageParams,
    };
    try {
      await getConfigParamList(data).then((res) => {
        remoteList.value = res.items;
      });
    } catch (error) {
      console.error('失败:', error);
    }
  };
  // 树形数据
  type TreeNode = {
    title: string;
    key: string;
    children?: TreeNode[];
  };
  const treeData = ref<TreeNode[]>([]);
  // 搜索过滤方法
  const filterTreeNode = (node: any, keyword: string) => {
    return node.title.toLowerCase().includes(keyword.toLowerCase());
  };

  // 递归过滤树节点
  const filterTree = (data: any[], keyword: string) => {
    return data.filter((node) => {
      if (filterTreeNode(node, keyword)) {
        return true;
      }
      if (node.children) {
        node.children = filterTree(node.children, keyword);
        return node.children.length > 0;
      }
      return false;
    });
  };

  // 计算过滤后的树数据
  const filteredTreeData = computed(() => {
    if (!searchValue.value) {
      return treeData.value;
    }
    return filterTree(cloneDeep(treeData.value), searchValue.value);
  });
  const buildTreeData = (list) => {
    return list.map((category) => ({
      title: category.category,
      key: category.category,
      children: (category.items || []).map((item) => ({
        title: item.name ? `${item.name}-${item.displayName}` : item.displayName,
        key: item.code,
      })),
    }));
  };
  const initTreeData = () => {
    if (toolData.toolModelList && toolData.toolModelList.length) {
      const indicatorList = toolData.toolModelList
        .map((category) => ({
          ...category,
          items: (category.items || []).filter((item) => item.objectType === 'indicator'),
        }))
        .filter((category) => category.items && category.items.length > 0);
      treeData.value = buildTreeData(indicatorList);
    }
  };
  const getWhiteList = async () => {
    try {
      const res = await getUserWhiteList();
      whiteList.value = res.filter((item) => item.status === 1);
    } catch (e) {
      console.error(e);
    }
  };
  // 初始化阶段调用一次
  const init = async () => {
    getWhiteList();
    await toolData.fetchAllModalList();
    await Promise.all([getTags(), getLayerOptions(), getDomainOptions(), getRemoteList()]);
    if (form.flowLayerCode) {
      layerChange(form.flowLayerCode);
    } else {
      remainFlowPercentage.value = 100;
    }
    initTreeData();
  };
  init();
  watch(
    () => props.detailData,
    (val) => {
      if (val) {
        form.name = val.name || '';
        form.description = val.description || '';
        form.tags = val.tags || [];
        form.duration = val.duration;
        form.flowPercentage = val.flowPercentage;
        form.enableGradual = val.enableGradual || false;
        form.gradualDuration = val.gradualDuration;
        form.userScope = val.userScope || 'all';
        form.userRules = val.userRules || {};
        form.consistency = val.consistency || true;
        form.isMutualExclusion = val?.flowLayer?.code ? 1 : 0;
        form.flowLayerCode = val?.flowLayer?.code || '';
        form.remoteConfigCode = val?.remoteConfig?.name || '';
        form.associatedRemote = form.remoteConfigCode ? 1 : 0;
        const variantsList = cloneDeep(val.variants || []);
        if (variantsList?.length) {
          versionList.value = variantsList.map((item) => {
            return {
              code: item.code,
              name: item.name,
              type: item.type,
              description: item.description,
              showContent: true,
              flowPercentage: item.flowPercentage,
              configParams: item.configParams,
              whitelistCodes: item.whitelistCodes || [],
              testUserCodes: item.testUserCodes?.length ? item.testUserCodes.join(',') : '',
            };
          });
          if (!versionList.value.length) {
            isEvenDistribution.value = false;
          } else {
            const first = versionList.value[0].flowPercentage;
            isEvenDistribution.value = versionList.value.every((item) => item.flowPercentage === first);
          }
        }
        form.coreIndicatorCode = val?.coreIndicator?.code || '';
        form.concernIndicatorCodes = val?.concernIndicator.map((item) => item.code) || [];
        form.dataSource = val?.dataSource || '';
      }
    },
    { immediate: true, deep: true }
  );

  const handleSubmit = async () => {
    try {
      await editFormRef.value.validate(async (valid: any) => {
        if (!valid) {
          try {
            form.code = (route.query.code as string) || '';
            const data = removeEnumList(form.userRules);
            const versionData = versionList.value.map((item) => {
              return {
                name: item.name,
                type: item.type,
                code: item.code,
                experimentCode: form.code,
                description: item.description,
                whitelistCodes: item.whitelistCodes,
                testUserCodes: item.testUserCodes
                  ? item.testUserCodes
                      .split(',')
                      .map((code) => code.trim())
                      .filter(Boolean)
                  : [],
                flowPercentage: item.flowPercentage,
                configParams: item.configParams.map((config) => {
                  let value = config.value;
                  if ((config.dataType === 'int' || config.dataType === 'float') && typeof value === 'number') {
                    value = value.toString();
                  }
                  return {
                    ...config,
                    value,
                  };
                }),
              };
            });
            const params = {
              code: form.code,
              name: form.name,
              description: form.description,
              tags: form.tags,
              duration: form.duration,
              dataSource: form.dataSource,
              flowPercentage: form.flowPercentage,
              flowLayerCode: form.flowLayerCode,
              flowDomainCode: form.flowDomainCode,
              enableGradual: form.enableGradual,
              gradualDuration: form.gradualDuration,
              userScope: form.userScope,
              userRules: data,
              consistency: form.consistency,
              remoteConfigCode: form.remoteConfigCode,
              variants: versionData,
              coreIndicatorCode: form.coreIndicatorCode,
              concernIndicatorCodes: form.concernIndicatorCodes,
              confidenceLevel: 0.95,
            };
            await saveExperiment({
              ...params,
              remainEditStep: 0,
            });
            emits('updateDetail');
          } catch (err) {
            console.error('保存实验失败', err);
          }
        }
      });
    } catch (e) {
      // 校验异常处理
      console.error('表单校验异常', e);
    }
  };
  const checkRef = ref();
  const checkSubmit = async () => {
    editFormRef.value.validate(async (valid: any) => {
      if (!valid) {
        try {
          if (!paramsVerify(form.userRules)) {
            Message.error('筛选条件参数错误');
            return;
          }
          if (!validateClientConditionRules(form.userRules?.clientCondition?.rules)) {
            Message.error('客户端条件参数错误');
            return;
          }
          if (!validateType() || !validateConfigParams()) {
            Message.error(validateText.value);
            return; // 阻止提交
          }
          const res = await checkExperimentConflict(route.query.code as string);
          if (res?.length) {
            checkRef.value.openModal(res);
          } else {
            handleSubmit();
          }
        } catch (e) {
          console.error(e);
        }
      }
    });
  };

  defineExpose({
    checkSubmit,
  });
</script>

<style lang="less" scoped>
  @import '../style/form.less';

  .ta-filter-button {
    display: inline-flex;
    color: var(--tant-primary-color-primary-default);
    align-items: center;
    padding: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: var(--tant-bg-white-color-bg1-1);
      color: var(--tant-primary-color-primary-hover);
    }
  }
  .error-text {
    min-height: 20px;
    color: rgb(var(--danger-6));
    font-size: 12px;
    line-height: 20px;
  }
</style>
