<template>
    <a-spin :loading="dataLoading" class="form-content">
        <a-form ref="formRef" :model="form" :rules="rules" label-align="left" style="width: 100%;">
            <div class="form-label">
                <div class="title">实验核心指标配置</div>
                <div class="extra"></div>
            </div>
            <a-alert type="warning" style="margin-bottom: 20px;">核心指标是用来决策实验功能是否符合预期的「直接效果指标」或「成功指标」，建议合理的进行实验设计，谨慎选择。</a-alert>
            <div class="group-select">
                <a-form-item field="coreIndicatorCode" label="" :label-col-props="{ span: 0 }" validate-trigger="change">
                    <a-cascader v-model:model-value="form.coreIndicatorCode" :options="options" :style="{width:'300px'}" placeholder="请选择核心指标" allow-search/>
                </a-form-item>
            </div>
            <div class="form-label">
                <div class="title">实验关注指标配置</div>
                <div class="extra"></div>
            </div>
            <div class="indicator-config">
                <a-input-search v-model="searchValue" :style="{width:'320px',marginBottom:'16px'}" placeholder="输入指标关键词" @change="handleSearch"/>
                <a-tree
                    v-model:checked-keys="form.concernIndicatorCodes"
                    :checkable="true"
                    :data="filteredTreeData"
                    :default-expanded-keys="[]"
                />
            </div>
            <div class="form-label">
                <div class="title">
                    置信水平
                    <a-tooltip content="置信水平（也称置信度、置信系数、统计显著性），是指实验组与对照组之间存在真正性能差异的概率。例如在置信水平是95%的情况下，如果某个实验指标的置信度p值<0.05，则说明这个指标相比对照组，是有显著(超过置信水平)差异的。如需设置，请联系管理员">
                        <icon-question-circle />
                    </a-tooltip>
                    95%
                </div>
                <div class="extra"></div>
            </div>
            <a-form-item label="" :label-col-props="{ span: 0 }">
                <a-button style="border-radius: 4px;" :loading="loading" @click="backCurrent">
                    上一步
                </a-button>
                <a-button
                    type="primary"
                    style="border-radius: 4px;margin: 0 24px;"
                    :loading="loading"
                    @click="checkSubmit">
                    确认
                </a-button>
                <a-button style="border-radius: 4px;" :loading="loading" @click="cancleEdit">
                    取消
                </a-button>
            </a-form-item>
        </a-form>
        <CheckModal ref="checkRef" @skip-check="handleSubmit"/>
    </a-spin>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import {cloneDeep} from "lodash";
import {toolStore} from '@/store';
import {checkExperimentConflict, getExperimentDetail, saveExperiment} from "@/api/ab/api";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import CheckModal from "./CheckModal.vue";

const props = defineProps({
    code: {
        type: String,
        default: ''
    }
})
const toolData = toolStore();
const form = reactive({
    coreIndicatorCode:'',
    concernIndicatorCodes:[],
})
const rules = {
    coreIndicatorCode: [
        {
            required: true,
            message:'请选择核心指标',
        }
    ],
}
const options = computed(() => {
    const indicatorList = toolData.toolModelList
            .map(category => ({
                ...category,
                items: (category.items || []).filter(item => item.objectType === 'indicator')
            }))
            .filter(category => category.items && category.items.length > 0);
    return indicatorList.map(category => ({
        value: category.category,
        label: category.category,
        children: (category.items || []).map(item => ({
            value: item.code,
            label: item.name ? `${item.name}-${item.displayName}` : item.displayName
        }))
    }));
});


// 树形数据
type TreeNode = {
    title: string;
    key: string;
    children?: TreeNode[];
};
const treeData = ref<TreeNode[]>([]);

const buildTreeData = (list) => {
    return list.map(category => ({
        title: category.category,
        key: category.category,
        children: (category.items || []).map(item => ({
            title: item.name ? `${item.name}-${item.displayName}` : item.displayName,
            key: item.code
        }))
    }));
};

const initTreeData = () => {
    if (toolData.toolModelList && toolData.toolModelList.length) {
        // console.log(toolData.toolModelList,'tt');
        // 先筛选 objectType === 'indicator' 的数据
        const indicatorList = toolData.toolModelList
            .map(category => ({
                ...category,
                items: (category.items || []).filter(item => item.objectType === 'indicator')
            }))
            .filter(category => category.items && category.items.length > 0);
        treeData.value = buildTreeData(indicatorList);
    }
};

// 初始化阶段调用一次
initTreeData();

// 监听 toolData.toolModelList 变化时手动调用
watch(
    () => toolData.toolModelList,
    () => {
        initTreeData();
    },
    { deep: true }
);
const searchValue = ref('');
const loading = ref(false)
const formRef = ref();
const emits = defineEmits(['toNext','toPrev','cancleEdit'])

const handleSubmit = async () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                const params = {
                    code:props.code,
                    coreIndicatorCode:form.coreIndicatorCode,
                    concernIndicatorCodes:form.concernIndicatorCodes,
                  // 置信水平定值
                  confidenceLevel:0.95,
                  remainEditStep: 0
                }
                await saveExperiment(params)
                // emits('toNext')
                router.push({
                    name: ROUTE_NAME.AB_TEST_EXPERIMENT,
                });
            } catch (e) {
                // Message.error('保存失败，请重试')
                console.error(e)
            } finally {
                loading.value = false
            }
        }
    })
};
const checkRef = ref()
const checkSubmit = async () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                const res = await checkExperimentConflict(props.code)
                if(res?.length){
                    checkRef.value.openModal(res)
                }else{
                    handleSubmit()
                } 
            }catch(e){
                console.error(e)
            }finally{
                loading.value = false
            }
        }
    })
    
}
const backCurrent = () => {
    emits('toPrev')
}
// 搜索过滤方法
const filterTreeNode = (node: any, keyword: string) => {
    return node.title.toLowerCase().includes(keyword.toLowerCase());
};

// 递归过滤树节点
const filterTree = (data: any[], keyword: string) => {
    return data.filter(node => {
        if (filterTreeNode(node, keyword)) {
            return true;
        }
        if (node.children) {
            node.children = filterTree(node.children, keyword);
            return node.children.length > 0;
        }
        return false;
    });
};

// 计算过滤后的树数据
const filteredTreeData = computed(() => {
    if (!searchValue.value) {
        return treeData.value;
    }
    return filterTree(cloneDeep(treeData.value), searchValue.value);
});

// 搜索处理方法
const handleSearch = () => {
    // 可以在这里添加额外的搜索逻辑
    console.log('搜索关键词：', searchValue.value);
};
const cancleEdit = () => {
    emits('cancleEdit')
}
const dataLoading = ref(false)
const init = async () => {
    if(!toolData.toolModelList.length){
        await toolData.fetchAllModalList()
    }
    if(props.code){
        dataLoading.value = true
        try{
          const res = await getExperimentDetail(props.code)
          form.coreIndicatorCode = res?.coreIndicator?.code || ''
          form.concernIndicatorCodes = res?.concernIndicator?.map(item => item.code) || []
        }catch(e){
          console.error(e)
        }finally{
          dataLoading.value = false
        }
    }
}
init()
</script>

<style scoped lang="less">
.form-content{
  width: 100%;
  height: 100%;
}
.form-label{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .title{
        color: #141414e6;
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
    }
    .extra{
        margin-left: 16px;
    }
}
.indicator-config{
    border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 6px;
    font-size: 13px;
    min-width: 800px;
    width: 100%;
    padding: 20px;
    margin-bottom: 24px;
    max-height: 300px;
    overflow: auto;
}
.source-content{
    margin-bottom: 24px;
}
.group-select{
    padding: 16px 25px 0px 46px;
    border-radius: 4px;
    background-color: var(--color-primary-light-1);
    margin-bottom: 24px;
}
</style>