<template>
  <div style="height: 26px;">
    <!-- 新事件指标下拉筛选 -->
    <a-trigger
        v-model:popup-visible="triggerVisible"
        trigger="click"
        :unmount-on-close="false"
        position="bl"
        :update-at-scroll="true"
        style="z-index: 1002;"
        @click="handleTriggerVisible"
    >
      <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
        <div v-if="!props.disabled" class="select-btn">
          <IconFont v-if="selectObject.type == 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon"/>
          <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon"/>
          <span class="btn-label">
            {{ selectObject.eventName }}
            <span v-if="selectObject.eventName">-</span>
            {{ selectObject.eventDisplayName }}
          </span>
        </div>
        <template #content>
          <div class="trigger-box">
            <div class="card-header">
              <div class="card-header-container">
                <div class="header-icon">
                  <IconFont v-if="selectObject.type == 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon"/>
                  <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon"/>
                </div>
                <div class="header-title">
                  <div class="name">{{ selectObject.eventDisplayName }}</div>
                </div>
                <div class="header-type">{{ getTypeText(selectObject.eventType) }}</div>
              </div>
              <div class="title-sub">{{ selectObject.eventName }}</div>
            </div>
            <div class="card-desc">
              <div v-if="selectObject.eventNote" class="span-desc">{{ selectObject.eventNote }}</div>
              <div v-else class="span-desc">暂无备注</div>
            </div>
            <div class="card-footer">
              <div></div>
              <div class="action">
                <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                <a-tooltip v-if="selectObject.type === 'indicator'" content="编辑" position="top">
                  <div class="action-icon" @click="goIndexItem(selectObject.eventCode)">
                    <icon-edit/>
                  </div>
                </a-tooltip>
                <a-tooltip v-if="selectObject.type === 'event'" content="前往事件详情" position="top">
                  <div class="action-icon" @click="toEventDetail(selectObject)">
                    <icon-launch/>
                  </div>
                </a-tooltip>
                <a-tooltip v-else content="前往指标列表" position="top">
                  <div class="action-icon" @click="toIndexList(selectObject)">
                    <icon-launch/>
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <template #content>
        <div class="select-panel">
          <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
            <a-input ref="searchInputRef" v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
              <template #prefix>
                <icon-search/>
              </template>
            </a-input>
          </div>
          <div v-if="searchInput === ''" class="list-container">
            <div class="list-category">
              <div class="category-container">
                <div
                    v-for="(item, index) in originList"
                    :key="index"
                    class="category-item"
                    :class="{ 'item-active': activeIndex === index,}"
                    @click="categoryChange(item.categoryName,index)">
                  {{ item.categoryName }}
                </div>
              </div>
              <a-button @click="toCategoryGroup">管理分组</a-button>
            </div>
            <div class="item-list">
              <div ref="selectListRef" class="select-list">
                <a-list ref="virtualListRef" :virtual-list-props="{ height: 350, buffer: 10 }" :data="virtualItemList" :scrollbar="false" @scroll="handleScroll">
                  <template #item="{ item, index }">
                    <a-list-item :key="`panel-${item.eventCode || item?.categoryName}-${index}`">
                      <div class="list-content">
                        <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <!-- <icon-star-fill v-if="index == 0" class="star"/> -->
                          <span>{{ item.categoryName }}</span>
                        </div>
                        <div v-if="index == 0 && !originList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                          <!-- 点击选项右<icon-star/>侧添加为常用 -->
                          暂无数据
                        </div>
                        <div v-if="item.eventDisplayName" class="list-box" :class="{ 'list-active': item.eventCode == selectObject.eventCode }" style="height: 30px; width: calc(100% - 10px)"
                             @click="listChange(item)">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                            <div>
                              <IconFont v-if="item.type === 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" style="margin-right:4px"/>
                              <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right:4px"/>
                              <span class="desc">
                                  {{ item.eventName }}
                                  <span v-if="item.eventName">-</span>
                                  {{ item.eventDisplayName }}
                              </span>
                              <a-tooltip v-if="!item.isFavorite" content="点击收藏" position="top">
                                <div class="icon" @click.stop="starClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                              <a-tooltip v-else content="取消收藏" position="top">
                                <div class="isStar" @click.stop="cancelClick(item)">
                                  <icon-star-fill/>
                                </div>
                              </a-tooltip>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <IconFont v-if="item.type == 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon"/>
                                      <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon"/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.eventDisplayName }}</div>
                                    </div>
                                    <!-- <div class="header-type">自定义事件</div> -->
                                    <div class="header-type">{{ getTypeText(item.eventType) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.eventName }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.eventNote" class="span-desc">{{ item.eventNote }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                    <a-tooltip v-if="item.type === 'indicator'" content="编辑" position="top">
                                      <div class="action-icon" @click="goIndexItem(item.eventCode)">
                                        <icon-edit/>
                                      </div>
                                    </a-tooltip>
                                    <a-tooltip v-if="item.type === 'event'" content="前往事件详情" position="top">
                                      <div class="action-icon" @click="toEventDetail(item)">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                    <a-tooltip v-else content="前往指标列表" position="top">
                                      <div class="action-icon" @click="toIndexList(item)">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </div>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
          <div v-else class="list-container" style="padding-top: 16px;">
            <div class="item-list">
              <div class="list-metric">
                <a-list :virtual-list-props="{ height: 350 }" :data="filteredLists">
                  <template #item="{ item, index }">
                    <a-list-item :key="index">
                      <div class="list-content">
                        <div v-if="item.typeName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <icon-star-fill v-if="item.typeName.includes('常用')" class="star"/>
                          {{ item.typeName }}
                        </div>
                        <div v-if="item.eventDisplayName" class="list-box" :class="{ 'list-active': item.eventDisplayName == selectObject.eventDisplayName, }"
                             style="height: 30px; width: calc(100% - 10px)" @click="itemChange(item)">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                            <div>
                              <IconFont v-if="item.type === 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" style="margin-right:4px"/>
                              <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right:4px"/>
                              <span class="desc1">{{ item.eventDisplayName }}</span>
                              <span class="desc2">{{ item.eventName }}</span>
                              <a-tooltip v-if="!item.isFavorite" content="点击收藏" position="top">
                                <div class="icon" @click.stop="itemClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                              <a-tooltip v-else content="取消收藏" position="top">
                                <div class="isStar" @click.stop="cancelItemClick(item)">
                                  <icon-star-fill/>
                                </div>
                              </a-tooltip>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <IconFont v-if="item.type == 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon"/>
                                      <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon"/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.eventDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getTypeText(item.eventType) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.eventName }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.eventNote" class="span-desc">{{ item.eventNote }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                    <a-tooltip v-if="item.type === 'indicator'" content="编辑" position="top">
                                      <div class="action-icon" @click="goIndexItem(item.eventCode)">
                                        <icon-edit/>
                                      </div>
                                    </a-tooltip>
                                    <a-tooltip v-if="item.type === 'event'" content="前往事件详情" position="top">
                                      <div class="action-icon" @click="toEventDetail(item)">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                    <a-tooltip v-else content="前往指标列表" position="top">
                                      <div class="action-icon" @click="toIndexList(item)">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </div>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-trigger>
    <div v-if="props.disabled">
      <div class="select-btn-disabled">
        <IconFont v-if="selectObject.type == 'event'" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon"/>
        <IconFont v-else type="icon-zhibiao" :size="14" class="btn-icon"/>
        <span class="btn-label">
        {{ selectObject.eventName }}
        <span v-if="selectObject.eventName">-</span>
        {{ selectObject.eventDisplayName }}
      </span>
      </div>
    </div>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {toolStore} from '@/store';
import router from "@/router";
import {storeToRefs} from 'pinia';
import {cloneDeep,debounce} from "lodash";
import {cancelFavorite, saveFavorite} from "@/api/analyse/api";
import {ROUTE_NAME} from "@/router/constants";

const toolData = toolStore();
const triggerVisible = ref(false);
const emits = defineEmits(['analysisIndexChange']);
const handleTriggerVisible = () => {
  triggerVisible.value = !triggerVisible.value;
};
const props = defineProps({
  // 传入回显数据
  panelData: {
    type: Object,
    default() {
      return {};
    },
  },
  // 禁用只读
  disabled: {
    type: Boolean,
    default: false
  },
  // 只选事件
  onlyEvt: {
    type: Boolean,
    default: false
  },
  // 只选指标
  onlyIndicator: {
    type: Boolean,
    default: false
  },
  // 控制不请求属性分类数据,比如说运营分析是单独属性分类组件，不需要通用请求
  noFetchAttr: {
    type: Boolean,
    default: false
  },
});

// 类型映射
const EVENT_TYPE_MAP = {
  1: '预置事件',
  2: '自定义事件',
  3: '虚拟事件',
  'event': '事件指标',
  'retention': '留存指标',
  'operation': '运营指标'
} as const;

// 类型显示文本
const getTypeText = (type: number | string) => EVENT_TYPE_MAP[type] || '';
// 前往事件详情
const toEventDetail = (record:any) => {  
  const appId = virtualItemList.value.find(item => item.eventCode && record.eventCode === item.eventCode)?.appId || ''
  if(appId){
    router.push({name: ROUTE_NAME.SETTING_OPERATION_ANALYSE_EVENT, query: {text: record.eventName || record.eventDisplayName}})
  }else{
    router.push({name: ROUTE_NAME.SETTING_ANALYSE_EVENT,query: {text: record.eventName || record.eventDisplayName}})
  }
}
// 前往指标列表
const toIndexList = (record:any) => {
  const appId = virtualItemList.value.find(item => item.eventCode && record.eventCode === item.eventCode)?.appId || ''
  if(appId){
    router.push({name: ROUTE_NAME.SETTING_OPERATION_ANALYSE_INDICATO, query: {text: record.eventName || record.eventDisplayName}})
  }else{
    router.push({name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR,query: {text: record.eventName || record.eventDisplayName}})
  }
}
// 前往指标详情编辑
const goIndexItem = (codeId?: string) => {
  router.push({
    name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR_CREATE,
    query: {code: codeId, type: 'edit'},
  });
};
const selectObject = ref({
  eventName: props.panelData.eventName || '',
  eventDisplayName: props.panelData.eventDisplayName || '',
  eventType: props.panelData.eventType || '',
  eventCode: props.panelData.eventCode || '',
  type: props.panelData.type || 'event', // 事件 | 指标
  eventNote: '' // 备注
});

//   原数组
const originList = ref<any[]>([]);

interface originItem {
  eventCode: string;
  eventName: string;
  eventDisplayName: string;
  eventType: string;
  eventNote: string;
  isFavorite: boolean;
  favoriteCode?: string;
}

interface CategoryItem {
  categoryName: string;
}

type virtualItem = CategoryItem | originItem;
const searchInput = ref('')
// 虚拟列表  事件数组
const virtualItemList = ref<virtualItem[]>([]);
const getVirtualItemList = (arr) => {
  virtualItemList.value = arr.flatMap((category) => {
    // 根据props过滤 只选事件或指标
    let filteredItemData = category.itemData;
    if (props.onlyEvt) {
      filteredItemData = filteredItemData.filter(item => item.type === 'event');
    } else if (props.onlyIndicator) {
      filteredItemData = filteredItemData.filter(item => item.type === 'indicator');
    }
    const categoryItem: CategoryItem = {
      categoryName: category.categoryName,
    };
    return [categoryItem, ...filteredItemData];
  });
}
const getDataList = async () => {
  if (toolData.temporaryList.length) {
    originList.value = cloneDeep(toolData.temporaryList)
  } else {
    // if(!toolData.toolModelList.length){
    //   (await toolData.fetchAllModalList()).flatMap(category => category.items || [])
    // }
    const data = cloneDeep(toolData.toolModelList) || []
    if (data && data.length) {
      originList.value = data.map((item) => {
        return {
          categoryName: item.category,
          itemData: item?.items.map(el => {
            return {
              eventName: el.name,
              eventDisplayName: el.displayName,
              eventType: el.type,
              eventCode: el.code,
              eventNote: el.note,
              type: el.objectType,
              isFavorite: el.isFavorite,
              favoriteCode: el?.favoriteCode,
              appId: el.appId,
            }
          })
        }
      })
    }
  }
  getVirtualItemList(originList.value)
};

//   事件容器，左右点击滚动效果联动
const activeIndex = ref(0)
const virtualListRef = ref(null);
const selectListRef = ref(null);
// 添加一个标志来控制是否响应滚动事件
const isManualScrolling = ref(false);

const categoryChange = async (categoryName: string, index: number) => {
  // 设置标志，表示正在手动滚动
  isManualScrolling.value = true;
  
  await nextTick();
  activeIndex.value = index; // 先更新 activeIndex，确保左侧高亮正确

  // 计算目标分类在 virtualItemList 中的位置
  const targetIndex = virtualItemList.value.findIndex(item => item.categoryName === categoryName);
  
  if (targetIndex !== -1) {
    // 如果有数据，正常滚动
    virtualListRef.value?.scrollIntoView({ index: targetIndex });
  } else {
    // 如果没有数据，找到该分类在 originList 中的位置，计算应该滚动到的位置
    const categoryPosition = originList.value.findIndex(item => item.categoryName === categoryName);
    if (categoryPosition !== -1) {
      // 假设每个分类占固定高度，或者动态计算
      const estimatedScrollPosition = categoryPosition * 20; // 调整这个值
      virtualListRef.value?.$el.querySelector('.arco-virtual-list')?.scrollTo({
        top: estimatedScrollPosition,
        behavior: 'smooth'
      });
    }
  }
  
  // 使用 setTimeout 延迟重置标志，确保滚动动画完成后再恢复
  setTimeout(() => {
    isManualScrolling.value = false;
  }, 500); // 500ms 应该足够滚动动画完成
};

const handleScroll = () => {
  const scrollContainer = virtualListRef.value?.$el.querySelector('.arco-virtual-list');
  if (!scrollContainer) return;

  let lastKnownIndex = -1;

  const observer = new IntersectionObserver((entries) => {
    // 如果正在手动滚动，不响应滚动事件
    if (isManualScrolling.value) return;
    
    if (scrollContainer.scrollTop <= 10) {
      activeIndex.value = 0;
      return;
    }

    // 找出最接近顶部的可见分类（且必须完全可见）
    let closestEntry = null;
    let minDistance = Infinity;

    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio >= 0.9) { // 确保大部分可见
        const distance = Math.abs(entry.boundingClientRect.top);
        if (distance < minDistance) {
          minDistance = distance;
          closestEntry = entry;
        }
      }
    });

    if (closestEntry) {
      const categoryName = closestEntry.target.querySelector('span')?.textContent?.trim();
      if (categoryName) {
        const newIndex = originList.value.findIndex(el => categoryName === el.categoryName);
        if (newIndex !== -1 && newIndex !== lastKnownIndex) {
          lastKnownIndex = newIndex;
          activeIndex.value = newIndex;
        }
      }
    }
  }, {
    root: scrollContainer,
    threshold: [0.5, 0.9], // 检测部分可见和完全可见
    rootMargin: '0px 0px -30% 0px' // 底部减少检测区域，避免误判
  });

  const categoryElements = scrollContainer.querySelectorAll('.list-name');
  categoryElements.forEach(element => {
    observer.observe(element);
  });

  // 防抖处理
  const handleScrollEvent = debounce(() => {
    // 如果正在手动滚动，不响应滚动事件
    if (isManualScrolling.value) return;
    
    if (scrollContainer.scrollTop <= 10) {
      activeIndex.value = 0;
      lastKnownIndex = 0;
    }
  }, 100);

  scrollContainer.addEventListener('scroll', handleScrollEvent);

  return () => {
    observer.disconnect();
    scrollContainer.removeEventListener('scroll', handleScrollEvent);
  };
};

onMounted(() => {
  if (originList.value && originList.value.length > 0) {
    activeIndex.value = 0;
  }
  handleScroll();
});

onUnmounted(() => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    const cleanup = handleScroll();
    if (cleanup) cleanup();
  }
});
// 管理分组
const toCategoryGroup = () => {
  router.push({name: ROUTE_NAME.SETTING_COMMON_EVENT_INDICATOR_GROUP})
}
//   事件点击
const listChange = async (item: any) => {
  selectObject.value = {
    ...selectObject.value,
    eventName: item.eventName,
    eventDisplayName: item.eventDisplayName,
    type: item.type,
    eventType: item.eventType,
    eventCode: item.eventCode,
    eventNote: item.eventNote
  }
  let processedItem = {...selectObject.value}
  if (item.type === 'indicator') {
    const {eventCode, eventName, eventDisplayName, ...rest} = selectObject.value;
    processedItem = {
      ...rest,
      indicatorCode: eventCode,
      indicatorName: eventName,
      indicatorDisplayName: eventDisplayName
    };
  }
  triggerVisible.value = false;
  if (!props.noFetchAttr) {
    await toolData.addEvtIndCodes(item.eventCode);
  }
  emits('analysisIndexChange', processedItem);
};

// 搜索时数据
const filteredLists = computed(() => {
  const str = searchInput.value.trim();
  if (!str) return [];

  // 获取所有分类的数据并保持原有分类
  const searchResults = originList.value.map(category => {
    const filteredItems = category.itemData.filter(
        item => item?.eventDisplayName?.toLowerCase().includes(str.toLowerCase()) ||
            item?.eventName?.toLowerCase().includes(str.toLowerCase())
    );
    if (filteredItems.length > 0) {
      return [
        {typeName: category.categoryName},
        ...filteredItems
      ];
    }
    return [];
  });
  // 扁平化结果数组
  return searchResults.flat().filter(item => item);
});
// 搜索点击
const itemChange = async (item: any) => {
  selectObject.value = {
    ...selectObject.value,
    eventName: item.eventName,
    eventDisplayName: item.eventDisplayName,
    type: item.type,
    eventType: item.eventType,
    eventCode: item.eventCode,
    eventNote: item.eventNote
  };

  let processedItem = {...selectObject.value}
  if (item.type === 'indicator') {
    const {eventCode, eventName, eventDisplayName, ...rest} = selectObject.value;
    processedItem = {
      ...rest,
      indicatorCode: eventCode,
      indicatorName: eventName,
      indicatorDisplayName: eventDisplayName
    };
  }
  if (!props.noFetchAttr) {
    await toolData.addEvtIndCodes(item.eventCode);
  }
  triggerVisible.value = false;
  emits('analysisIndexChange', processedItem);
}
// 事件收藏
const starClick = async (item: any) => {
  try {
    const data = {
      objectType: item.type === 'event' ? 1 : 2,
      objectCode: item.eventCode,
    }
    await saveFavorite(data)
    // 找到对应分类中的项目
    const targetCategory = originList.value.find(category =>
        category.itemData.some(data => data.eventCode === item.eventCode)
    );
    if (targetCategory) {
      // 更新原项目的收藏状态
      const targetItem = targetCategory.itemData.find(data => data.eventCode === item.eventCode);
      if (targetItem) {
        targetItem.isFavorite = true;
        // 检查收藏列表中是否已存在该项目
        const existingItem = originList.value[0].itemData.find(data => data.eventCode === targetItem.eventCode);
        if (!existingItem) {
          originList.value[0].itemData.push(targetItem);
        }
        toolData.updateTemporaryList(originList.value);
      }
    }
    // 更新虚拟列表
    getVirtualItemList(originList.value);
  } catch (error) {
    console.error('收藏失败:', error);
  }
};

// 取消收藏
const cancelClick = async (item: any) => {
  try {
    await cancelFavorite(item.favoriteCode)
    // 更新原项目的收藏状态
    originList.value.forEach(category => {
      category.itemData.forEach(data => {
        if (data.eventCode === item.eventCode) {
          data.isFavorite = false;
        }
      });
    });
    const index = originList.value[0].itemData.findIndex(data => data.eventCode === item.eventCode);
    if (index > -1) {
      originList.value[0].itemData.splice(index, 1);
    }
    toolData.updateTemporaryList(originList.value);
    // 更新虚拟列表
    getVirtualItemList(originList.value);
  } catch (error) {
    console.error('取消收藏失败:', error);
  }
};

// 搜索列表的收藏/取消收藏
const itemClick = (item: any) => {
  starClick(item);
};

const cancelItemClick = (item: any) => {
  cancelClick(item);
};
watch(
    () => props.panelData,
    (newVal) => {
      selectObject.value.eventName = newVal.eventName || newVal.indicatorName || '';
      selectObject.value.eventDisplayName = newVal.eventDisplayName || newVal.indicatorDisplayName || '';
      selectObject.value.eventType = newVal.eventType || '';
      selectObject.value.eventCode = newVal.eventCode || newVal.indicatorCode || '';
      selectObject.value.type = newVal.type || 'event';
      selectObject.value.eventNote = newVal.eventNote || ''; 
      getDataList();
      if (!props.noFetchAttr) {
        toolData.addEvtIndCodes(selectObject.value.eventCode);
      }
    },
    {immediate: true}
);

// 使用 storeToRefs 来保持响应性
const {temporaryList, toolModelList} = storeToRefs(toolData);
// 添加对 temporaryList 的监听
watch(temporaryList, () => {
  // 更新列表
  getDataList()
}, {deep: true});
watch(toolModelList, () => {
  // 更新列表
  if (toolData.toolModelList.length) {
    getDataList()
  }
}, {deep: true});

const searchInputRef = ref();
// 默认聚焦
watch(triggerVisible, (val) => {
  if (val) {
    nextTick(() => {
      if (searchInputRef.value?.focus) {
        searchInputRef.value.focus();
      }
    });
  }
});
defineExpose({
  handleTriggerVisible
})
</script>

<style scoped lang="less">
.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.select-panel {
  position: relative;
  width: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-bottom);
}

.list-container {
  position: relative;
  display: flex;
  flex-direction: row;

  .list-category {
    height: 350px;
    position: relative;
    flex-shrink: 0;
    width: 100px;
    padding: 10px 0 0 10px;
    border-right: 1px solid var(--tant-border-color-border1-1);

    .category-container {
      height: calc(100% - 32px);
      overflow-y: auto;

      .category-item {
        width: 100%;
        margin-bottom: 10px;
        padding: 2px 0 2px 2px;
        color: var(--tant-text-gray-color-text1-3);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item-active {
        font-weight: 500;
        border-right: 2px solid var(--tant-primary-color-primary-default);
      }
    }

    .arco-btn {
      margin-left: -10px;
      width: 100px;
      border-radius: 0 0 0 4px;
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-2);
      text-shadow: none;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: 1px solid var(--tant-border-color-border1-1);
      box-shadow: none;
      border-left: none;
      border-bottom: none;
    }
  }

  .item-list {
    flex: 1 1;

    .select-list {
      // position: relative;
      height: 350px;
      width: 260px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }

    .list-group {
      width: 100%;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    .favorite {
      height: 30px;
      width: 100%;
      padding-left: 18px;
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
    }

    .list-name {
      border-top: 1px solid var(--tant-border-color-border1-1);
      margin-top: 8px;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      height: 45px;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    //.list-content:first-child .list-name{
    //    border-top: none;
    //    margin-top: 0;
    //}
    .list-box {
      margin: 0 8px;
      padding-left: 8px;
      border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 28px;
      cursor: pointer;

      .desc {
        display: inline-block;
        width: 180px;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1, .desc2 {
        display: inline-block;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1 {
        width: 140px;
      }

      .desc2 {
        width: 120px;
      }

      .icon {
        opacity: 0;
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
      }

      .icon:hover {
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);

        .icon {
          opacity: 100;
          pointer-events: all;
        }
      }

      .isStar {
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
        opacity: 100;
        pointer-events: all;
      }
    }

    .list-active {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }

    .list-metric {
      position: relative;
      height: 350px;
      width: 360px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }
  }
}

.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);
  .tip{
    opacity: 0;
    padding-right: 4px;
  }
  &:hover{
    .tip{
      opacity: 1;
    }
  }
  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;

      .header-icon {
        // margin-top: 2px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }

      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }

  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;

    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
        font-weight: 600;
      }

      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}
</style>
