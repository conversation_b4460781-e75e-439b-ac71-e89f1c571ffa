export const colorList = [
    {
        value:'rgba(251, 140, 0, .24)',
        label:'橙色'
    },
    {
        value:'rgba(244, 81, 30, .24)',
        label:'深橙色'
    },
    {
        value:'rgba(216, 27, 96, .24)',
        label:'粉色'
    },
    {
        value:'rgba(142, 36, 170, .24)',
        label:'紫色'
    },
    {
        value:'rgba(57, 73, 171, .24)',
        label:'靛蓝色'
    },
    {
        value:'rgba(30, 136, 229, .24)',
        label:'蓝色'
    },
    {
        value:'rgba(0, 172, 193, .24)',
        label:'蓝绿色'
    },
    {
        value:'rgba(0, 137, 123, .24)',
        label:'青色'
    },
    {
        value:'rgba(67, 160, 71, .24)',
        label:'绿色'
    },
    {
        value:'rgba(124, 179, 66, .24)',
        label:'绿黄色'
    },
    {
        value:'rgba(109, 76, 65, .24)',
        label:'棕色'
    }
]
export const includeList = [
    {
        groupName:'适用于文本',
        children:[
            {
                value:'con',
                label:'包含'
            },
            {
                value:'ncon',
                label:'不包含'
            },
            {
                value:'normal',
                label:'完全匹配'
            },
            {
                value:'rm',
                label:'包含正则表达式'
            },
        ]
    },
    {
        groupName:'适用于数字',
        children:[
            {
                value:'lt',
                label:'<'
            },
            {
                value:'lteq',
                label:'<='
            },
            {
                value:'eq',
                label:'='
            },
            {
                value:'neq',
                label:'!='
            },
            {
                value:'gt',
                label:'>'
            },
            {
                value:'gteq',
                label:'>='
            }
        ]
    }
]
// 文本选项
export const textRangeList = [
    {value: 'con', label: '包括'},
    {value: 'ncon', label: '不包括'},
    {value: 'eq', label: '等于'},
    {value: 'neq', label: '不等于'},
    {value: 'lt', label: '小于'},
    {value: 'lteq', label: '小于等于'},
    {value: 'gt', label: '大于'},
    {value: 'gteq', label: '大于等于'},
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'},
    {value: 'rm', label: '正则匹配'},
    {value: 'nrm', label: '正则不匹配',}
]
  // 数值选项
  export const valueRangeList = [
    {value: 'eq', label: '等于'},
    {value: 'neq', label: '不等于'},
    {value: 'lt', label: '小于'},
    {value: 'lteq', label: '小于等于'},
    {value: 'gt', label: '大于'},
    {value: 'gteq', label: '大于等于'},
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'},
    {value: 'scope', label: '区间'}
  ]
  
  // 列表选项
  export const itemRangeList = [
    {value: 'con', label: '存在元素'},
    {value: 'ncon', label: '不存在元素'},
    // {value:'scope',label:'元素位置'},
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'}
  ]
  // 时间选项
  export const dateRangeList = [
    {value: 'scope', label: '位于区间'},
    {value: 'lteq', label: '小于等于'},
    {value: 'gteq', label: '大于等于'},
    {value: 'rct', label: '相对当前日期'},
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'}
  ]
  // 布尔选项
  export const booleanRangeList = [
    // {value:'true',label:'为真'},
    // {value:'false',label:'为假'},
    {value: 'eq', label: '等于'},
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'}
  ]
  // 分群
  export const groupingList = [
    {value: 'con', label: '属于分群'},
    {value: 'ncon', label: '不属于分群'},
  ]