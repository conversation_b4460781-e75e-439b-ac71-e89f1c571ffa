<template>
  <div class="nav">
    <div class="nav-left">
      <div class="nav-left-board">
        <a-tooltip v-if="showText" :content="`点击修改：${inputValue}`">
          <span v-if="showText" ref="spanRef" @click="showInput">{{ inputValue }}</span>
        </a-tooltip>
        <input ref="InputRef" v-model="inputValue" class="inputText" :style="{ width: inputWidth + 'px' }" @focus="handleFocus" @input="updateWidth" @blur="handleBlur" @keyup.enter="handleEnter" />
      </div>
      <div class="detail-info">
        <span>
          创建人：<a-tag color="blue">{{ dashboard?.creator?.name }}</a-tag>
        </span>
        <span style="padding-left: 8px">
          共享状态：<a-tag :color="getShareTypeColor(dashboard?.dashboardShare?.shareType)">{{ getShareTypeText(dashboard?.dashboardShare?.shareType) }}</a-tag>
        </span>
      </div>
    </div>
    <div class="nav-right">
      <select-app />
      <select-data-source />
      <!--时区选择-->
      <time-zone-select @selected="onChange" />
      <!--日期选择-->
      <date-picker :date-range="params.boardDate" position="tr" @date-pick="datePick" />
      <a-tooltip :content="`最后更新：${props.lastRefreshTime}`">
        <a-button class="button-background" @click="refreshData">
          <template #icon>
            <icon-refresh class="nav-icon" />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip content="分享">
        <a-button :disabled="dashboardSelected.authority === 4" class="button-background" @click="shareHandleClick">
          <template #icon>
            <icon-share-internal class="nav-icon" />
          </template>
        </a-button>
        <boardShare ref="shareRefs" />
      </a-tooltip>
      <a-tooltip content="更多">
        <a-dropdown :hide-on-select="true" position="br" @select="moreHandleSelect">
          <a-button class="button-background">
            <template #icon>
              <icon-more class="nav-icon" />
            </template>
          </a-button>
          <template #content>
            <a-doption value="setting">看板设置</a-doption>
            <a-doption value="push">面板推送</a-doption>
            <a-doption value="preview">演示模式</a-doption>
            <a-doption value="download">下载数据</a-doption>
            <a-doption value="pdf">另存为PDF</a-doption>
          </template>
        </a-dropdown>
      </a-tooltip>
      <div class="nav-right-add">
        <a-dropdown trigger="click" position="br" :popup-max-height="false" @select="handleSelect">
          <a-tooltip content="添加内容">
            <a-button type="primary">
              <template #icon>
                <icon-plus />
              </template>
            </a-button>
          </a-tooltip>
          <template #content>
            <a-dsubmenu trigger="hover">
              <template #default>新建报表</template>
              <template #content>
                <a-doption v-for="(item, index) in selectOptions" :key="index" :value="item.value" @click="gotoItem(item.path)">
                  <template #icon>
                    <img :src="`/icon/topMenu/${item.value}.svg`" style="width: 14px; height: 14px" alt="" />
                  </template>
                  {{ item.label }}
                </a-doption>
              </template>
            </a-dsubmenu>
            <a-doption value="exist">已存报表</a-doption>
            <a-doption value="note">便签</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <exist-report ref="existRef" />
    <boardSet ref="settingRefs" :dashboard-share="dashboard.dashboardShare" />
    <board-push ref="boardPushRef" />
  </div>
</template>

<script setup lang="ts">
// -------------------- 组件/依赖导入 --------------------
import DatePicker from '@/components/date-picker/index.vue';
import timeZoneSelect from '@/components/time-zone-select/index.vue';
import {nextTick, onMounted, reactive, ref, watch} from 'vue';
import boardPush from '@/components/navbar-board/board-push.vue';
import boardShare from '@/components/navbar-board/board-share.vue';
import boardSet from '@/components/navbar-board/BoardSet.vue';
import {CacheEventBus, DashboardEventBus, LocalStorageEventBus, RefreshEvent, RenameEvent, TreeMenuEventBus} from '@/types/event-bus';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {saveDashboard, saveNote} from '@/api/dashboard/api';
import {DashboardDto} from '@/api/dashboard/type';
import existReport from '@/components/navbar-board/exist-report.vue';
import {useDashboardStore} from '@/store';
import router from '@/router';
import {ReportAnalyseModel} from '@/api/analyse/type';
import selectApp from '@/components/selected-game-app/index.vue';
import selectDataSource from '@/components/selected-data-source/index.vue';
import {ROUTE_NAME} from '@/router/constants';
import {usePageFilter} from '@/utils/filterConfigUtil';
import {useRoute} from 'vue-router';

interface Props {
    dashboard: DashboardDto; // 报表详情
    refreshAllReports: () => void; // 刷新所有报表的方法
    lastRefreshTime: string; // 最后刷新时间
  }

  const route = useRoute();
  // -------------------- 事件/状态/引用 --------------------
  const localStorageEventBus = useEventBus(LocalStorageEventBus); // 本地存储事件总线
  const appData = useSessionStorage('app-data', {}); // 当前应用数据
  const emits = defineEmits(['addNote', 'dateRangeChange']); // 组件自定义事件
  const dashboardSelected = useSessionStorage('dashboardSelected', { spaceId: '', folderId: '', dashboardId: '', authority: null }); // 当前选中的看板
  const dashboardStore = useDashboardStore(); // 看板 store
  const props = defineProps<Props>(); // 组件 props
  const appIdRef = useSessionStorage('app-id', '');
  const params = reactive({
    appId: appIdRef,
    boardDate: {},
  });
  const { saveFilter, saveFilterDebounced, filterUtil } = usePageFilter(params);
  // -------------------- 选项/常量 --------------------
  const selectOptions = [
    { label: '事件分析', value: ReportAnalyseModel.EVENT, path: ROUTE_NAME.ANALYSE_EVENT },
    { label: '留存分析', value: ReportAnalyseModel.RETENTION, path: ROUTE_NAME.ANALYSE_RETENTION },
    { label: '漏斗分析', value: ReportAnalyseModel.FUNNEL, path: ROUTE_NAME.ANALYSE_FUNNEL },
    { label: '属性分析', value: ReportAnalyseModel.PROPERTY, path: ROUTE_NAME.ANALYSE_PROPERTY },
    { label: '路径分析', value: ReportAnalyseModel.TRACE, path: ROUTE_NAME.ANALYSE_TRACE },
    { label: '分布分析', value: ReportAnalyseModel.SCATTER, path: ROUTE_NAME.ANALYSE_SCATTER },
    { label: 'SQL分析', value: ReportAnalyseModel.CUSTOM, path: ROUTE_NAME.ANALYSE_CUSTOM_SQL },
    { label: '间隔分析', value: ReportAnalyseModel.INTERVAL, path: ROUTE_NAME.ANALYSE_INTERVAL },
    { label: '归因分析', value: ReportAnalyseModel.ATTRIBUTION, path: ROUTE_NAME.ANALYSE_ATTRIBUTION },
    { label: '运营分析', value: ReportAnalyseModel.APPLICATION, path: ROUTE_NAME.ANALYSE_APPLICATION },
    { label: '群组分析', value: ReportAnalyseModel.GROUP, path: ROUTE_NAME.ANALYSE_GROUP },
  ];

  // -------------------- 控制弹窗/抽屉/可见性 --------------------
  const createModalShow = ref<boolean>(false); // 新建弹窗
  const noteModalShow = ref<boolean>(false); // 便签弹窗
  const timeTriggerVisible = ref<boolean>(false); // 时区选择器可见性

  // -------------------- 组件引用 --------------------
  const boardPushRef = ref(null); // 推送面板组件引用
  const existRef = ref(); // 已存报表组件引用
  const settingRefs = ref(); // 看板设置组件引用
  const shareRefs = ref(); // 分享组件引用

  // -------------------- 看板日期/时区相关 --------------------
  const lastDate = ref('0'); // 最近日期

  // -------------------- 输入框相关 --------------------
  const InputRef = ref(); // 输入框 DOM 引用
  const spanRef = ref(); // span DOM 引用
  const showText = ref(true); // 是否显示文本
  const inputValue = ref(''); // 输入框内容
  const inputWidth = ref(100); // 输入框宽度

  // -------------------- 事件总线 --------------------
  const cacheEventBus = useEventBus(CacheEventBus);
  const dashboardEventBus = useEventBus<string>(DashboardEventBus);
  const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus);

  // -------------------- 方法定义 --------------------

  /**
   * 跳转到指定路由
   */
  const gotoItem = (value: string) => {
    router.push({
      name: value,
    });
  };

  /**
   * 更多操作下拉菜单选择
   */
  const moreHandleSelect = (value: string) => {
    switch (value) {
      case 'setting':
        settingRefs.value?.openModal();
        break;
      case 'push':
        Message.info('功能正在开发中...');
        // todo boardPushRef.value?.handleClick()
        break;
      case 'preview':
        // todo 看板预览
        Message.info('功能正在开发中...');
        break;
      case 'download':
        // todo 看板下载
        Message.info('功能正在开发中...');
        break;
      case 'pdf':
        // todo 看板导出
        Message.info('功能正在开发中...');
        break;
      default:
    }
  };

  /**
   * 新增便签
   */
  function addNote() {
    const newNote = {
      content: '',
      title: '',
      type: '#fff',
      dashboardId: dashboardStore.dashboardSelected?.dashboardId,
    };
    saveNote(newNote)
      .then((res) => {
        emits('addNote', res.id, 'middle');
      })
      .catch((e) => {
        console.error(e);
      });
  }

  /**
   * 添加内容下拉菜单选择
   */
  const handleSelect = (value: string) => {
    switch (value) {
      case 'create':
        createModalShow.value = true;
        break;
      case 'exist':
        existRef.value?.openModal();
        break;
      case 'note':
        noteModalShow.value = true;
        addNote();
        break;
      default:
    }
  };

  /**
   * 打开分享弹窗
   */
  const shareHandleClick = () => {
    shareRefs.value.openModal();
  };

  /**
   * 获取共享类型颜色
   */
  const getShareTypeColor = (shareType: string) => {
    switch (shareType) {
      case 'public':
        return 'green';
      case 'group':
        return 'orange';
      default:
        return 'gray';
    }
  };

  /**
   * 获取共享类型文本
   */
  const getShareTypeText = (shareType: string) => {
    switch (shareType) {
      case 'public':
        return '全局公开';
      case 'group':
        return '组内公开';
      default:
        return '不公开';
    }
  };

  /**
   * 时区选择回调
   */
  const onChange = (value: string) => {
    lastDate.value = value;
    timeTriggerVisible.value = false;
  };

  /**
   * 刷新数据
   */
  const refreshData = () => {
    props.refreshAllReports();
  };

  /**
   * 日期选择回调
   */
  const datePick = (date: any) => {
    params.boardDate = date;
    emits('dateRangeChange', params.boardDate);
  };

  /**
   * 输入框宽度自适应
   */
  const updateWidth = () => {
    nextTick(() => {
      if (InputRef.value) {
        const inputElement = InputRef.value as HTMLInputElement;
        inputWidth.value = Math.max(inputElement.scrollWidth, 100); // 最小宽度为 100px
      }
    });
  };

  /**
   * 输入框聚焦
   */
  const handleFocus = () => {
    showText.value = false;
    updateWidth();
  };

  /**
   * 显示输入框
   */
  const showInput = () => {
    InputRef.value.style.display = 'inline-block';
    InputRef.value.style.border = 'none';
    spanRef.value.style.display = 'none';
    nextTick(() => {
      const inputElement = document.querySelector('.inputText');
      if (inputElement) {
        inputElement.focus();
        updateWidth();
      }
    });
  };

  // 监听 inputValue 变化，动态调整输入框宽度
  watch(inputValue, () => {
    nextTick(() => {
      updateWidth();
    });
  });

  /**
   * 看板重命名逻辑
   */
  const renameDashboard = (dashboardId: string, newName: string) => {
    let found = false;
    const findInFolders = (folders: any[]): boolean => {
      return folders?.some((folder: any) => {
        // 先检查当前文件夹的 dashboards
        const remakeDashboard = folder.dashboards?.find((dashboard: any) => dashboard.dashboardId === dashboardId);
        if (remakeDashboard) {
          if (newName) {
            remakeDashboard.name = newName;
          }
          inputValue.value = remakeDashboard.name;
          found = true;
          return true;
        }
        // 如果当前文件夹有子文件夹，递归检查
        if (folder.folders?.length) {
          return findInFolders(folder.folders);
        }
        return false;
      });
    };
    dashboardStore.spaceList?.some((space) => {
      // 先检查文件夹中的看板
      if (findInFolders(space.folders)) {
        return true;
      }
      // 如果在文件夹中未找到，检查空间顶层的看板
      if (!found) {
        const remakeDashboard = space.dashboards?.find((dashboard: any) => dashboard.dashboardId === dashboardId);
        if (remakeDashboard) {
          if (newName) {
            remakeDashboard.name = newName;
          }
          inputValue.value = remakeDashboard.name;
          return true;
        }
      }
      return false;
    });
  };

  // 监听缓存事件总线，处理重命名
  cacheEventBus.on((event, { type, dashboardId, name }) => {
    if (event === RenameEvent && type === 'dashboard') renameDashboard(dashboardId, name);
  });

  /**
   * 输入框失焦保存
   */
  const handleBlur = () => {
    showText.value = true;
    InputRef.value.style.display = 'none';
    updateWidth();
    saveDashboard({ name: inputValue.value, dashboardId: props.dashboard.dashboardId, timeZone: lastDate.value })
      .then((resp) => {
        cacheEventBus.emit(RenameEvent, {
          type: 'dashboard',
          dashboardId: props.dashboard.dashboardId,
          name: inputValue.value,
        });
      })
      .catch((e) => {
        Message.error('重命名失败！', e);
      });
  };

  /**
   * 输入框回车保存
   */
  const handleEnter = () => {
    showText.value = true;
    InputRef.value.style.display = 'none';
    updateWidth();
    saveDashboard({ name: inputValue.value, dashboardId: props.dashboard.dashboardId, timeZone: lastDate.value })
      .then((resp) => {
        cacheEventBus.emit(RenameEvent, {
          type: 'dashboard',
          dashboardId: props.dashboard.dashboardId,
          name: inputValue.value,
        });
      })
      .catch((e) => {
        Message.error('重命名失败！', e);
      });
  };

  /**
   * 绑定看板到应用
   */
  const bindDashboardApp = () => {
    saveDashboard({ name: inputValue.value, appId: appData.value.code, dashboardId: props.dashboard.dashboardId, timeZone: lastDate.value })
      .then(() => {
        // 发送刷新事件
        dashboardEventBus.emit(RefreshEvent);
        treeMenuEventBus.emit(RefreshEvent);
        Message.info('绑定成功！');
      })
      .catch((e) => {
        Message.error('绑定失败！', e);
      });
  };

  // 监听空间列表变化，触发重命名事件
  watch(
    () => dashboardStore.spaceList,
    () => {
      cacheEventBus.emit(RenameEvent, {
        type: 'dashboard',
        dashboardId: props.dashboard.dashboardId,
      });
    },
    { deep: true }
  );
  // 组件挂载时，触发重命名事件并加载URL参数
  onMounted(async () => {
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件到params
      Object.assign(params, { ...savedConfig });
    }
    cacheEventBus.emit(RenameEvent, {
      type: 'dashboard',
      dashboardId: props.dashboard.dashboardId,
    });
  });

  // 监听本地存储事件，刷新数据
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id' || name === 'data-source') {
      refreshData();
    }
    if (name === 'app-id') {
      params.appId = value;
    }
  });
  /**
   * 统一的参数和urlParams保存方法
   */
  const saveParamsAndUrlParams = () => {
    // 直接使用route.query中的数据
    const currentUrlParams = JSON.stringify(route.query);
    filterUtil.updateUrlParams(currentUrlParams);
    saveFilterDebounced();
  };
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      // 统一保存参数和urlParams
      saveParamsAndUrlParams();
    },
    { deep: true }
  );

  // 监听看板切换，触发保存并处理路由
  watch(
    () => dashboardSelected.value,
    async (newVal, oldVal) => {
      // 确保是不同的看板才触发保存
      if (newVal && oldVal && newVal.dashboardId !== oldVal.dashboardId) {
        // 保存当前筛选条件和URL参数
        saveParamsAndUrlParams();
      }
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  :deep(.arco-select-view-multiple),
  :deep(.arco-select-view-single) {
    border: 0;
  }

  .nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    background-color: var(--tant-bg-gray-color-bg2-1);

    .nav-left {
      display: flex;
      flex: 1 1;
      align-items: flex-end;
      justify-content: flex-start;
      height: 38px;
      margin-right: 100px;
      overflow: hidden;

      .detail-info {
        flex-shrink: 0;
        padding-left: 16px;
      }

      .nav-left-board {
        border-radius: 4px;
        width: auto;
        display: flex;
        height: 38px;
        color: var(--tant-text-gray-color-text1-1);
        font: var(--tant-header-font-header3-medium);
        font-size: var(--font-size-title-larger);
        line-height: 38px;
        white-space: nowrap;
        text-overflow: ellipsis;

        .inputText {
          border: none;
          display: none;
          outline: none;
        }
      }

      .nav-left-board:hover {
        background-color: var(--tant-secondary-color-secondary-transp-active);
      }
    }

    .nav-right {
      display: flex;
      align-items: center;
      justify-content: end;
      gap: 8px;

      .nav-right-user {
        padding-right: 16px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        cursor: pointer;
      }

      .nav-right-add {
        margin-left: 16px;
      }
    }
  }

  .button-background {
    background-color: #ffffff;
    padding: 8px;
    border-radius: 4px;
    color: var(--tant-text-gray-color-text1-2);
  }

  .button-background:hover {
    color: #bdbebf;
    background-color: #fff;
  }

  .nav-icon {
    font-size: 16px;
    stroke-linecap: square;
    stroke-linejoin: round;
    stroke-width: 5;
  }

  .link-icon {
    cursor: pointer;
    color: var(--tant-text-gray-color-text1-2);
  }

  .link-icon:hover {
    color: var(--tant-primary-color-primary-hover);
  }

  :deep(.arco-tabs-nav-tab) {
    justify-content: center;
  }
</style>
