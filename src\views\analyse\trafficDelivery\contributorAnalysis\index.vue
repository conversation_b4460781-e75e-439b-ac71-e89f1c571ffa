<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app />
        </div>
        <div class="filter-item">
          <selectCountry v-model:country="params.country" @country-change="countryChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick" />
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <a-tabs v-model:active-key="activeView">
          <a-tab-pane key="all" title="总览">
            <div v-if="contributorSumList.length" class="view-card">
              <div class="title">助攻平台</div>
              <PieView :chart-data="contributorSumList" style="height: calc(100% - 24px)" />
            </div>
            <div v-if="mediaSumList.length" class="view-card">
              <div class="title">投流平台</div>
              <PieView :chart-data="mediaSumList" style="height: calc(100% - 24px)" />
            </div>
            <a-empty v-if="!mediaSumList.length && !contributorSumList.length && !loading" description="暂无数据" style="background: #fff; height: calc(100vh - 240px); display: flex; flex-direction: column; justify-content: center"> </a-empty>
          </a-tab-pane>
          <a-tab-pane key="contributor" title="助攻平台">
            <div v-for="(item, index) in contributorList" :key="index" class="view-card">
              <div class="title">助攻{{ index + 1 }}</div>
              <EcharsView :chart-data="item" style="height: calc(100% - 24px)" />
            </div>
            <a-empty v-if="!contributorList.length" description="暂无数据" style="background: #fff; height: calc(100vh - 240px); display: flex; flex-direction: column; justify-content: center"> </a-empty>
          </a-tab-pane>
          <a-tab-pane key="media" title="投流平台">
            <div v-for="(item, index) in mediaList" :key="index" class="view-card">
              <div class="title">助攻{{ index + 1 }}</div>
              <EcharsView :chart-data="item" style="height: calc(100% - 24px)" />
            </div>
            <a-empty v-if="!mediaList.length" description="暂无数据" style="background: #fff; height: calc(100vh - 240px); display: flex; flex-direction: column; justify-content: center"> </a-empty>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch} from 'vue';
import {useRoute} from 'vue-router';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {getContributorAnalysisData} from '@/api/analyse/api';
import {LocalStorageEventBus} from '@/types/event-bus';
import selectApp from '@/components/selected-game-app/index.vue';
import selectCountry from '@/components/selected-country/index.vue';
import DatePicker from '@/components/date-picker/index.vue';
import {usePageFilter} from '@/utils/filterConfigUtil';
import EcharsView from './components/EchartView.vue';
import PieView from './components/PieView.vue';

const route = useRoute();
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const appIdRef = useSessionStorage('app-id', '');
  const params = reactive({
    view: 'media',
    appId: appIdRef,
    country: 'global',
    date: {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天',
    },
  });
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(params);
  const loading = ref(false);
  const allData = ref();
  const activeView = ref('all');
  const contributorList = ref<any>([]);
  const mediaList = ref<any>([]);
  const contributorSumList = ref<any>([]);
  const mediaSumList = ref<any>([]);

  const getData = async () => {
    loading.value = true;
    const data = {
      appId: params.appId,
      country: params.country,
      dateRange: params.date,
    };
    try {
      const res = await getContributorAnalysisData(data);
      allData.value = res;
      contributorList.value = res ? res.contributor : [];
      mediaList.value = res ? res.media : [];
      contributorSumList.value = res?.summary?.contributor || [];
      mediaSumList.value = res?.summary?.media || [];
    } catch (e) {
      console.log(e);
    } finally {
      loading.value = false;
    }
  };
  const refresh = async () => {
    await getData();
  };
  const init = async () => {
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, { ...savedConfig });
    }
    refresh();
  };
  onMounted(() => {
    init();
  });
  const countryChange = (v) => {
    refresh();
  };
  const datePick = (date: any) => {
    params.date = date;
    refresh();
  };
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id') {
      params.appId = value;
      refresh();
    }
  });
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .page-body {
    padding: 0;
    scrollbar-width: none;
    background-color: var(--tant-bg-gray-color-bg2-1);
  }
  .view-card {
    width: 100%;
    height: 380px;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 16px;
    flex-shrink: 0;
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #475466;
    }
  }
</style>
