<template>
  <!-- 广告操作确认弹窗 -->
    <a-modal
      v-model:visible="visible"
      width="416px"
      :hide-title="true"
      :footer="false"
      @cancel="handleCancel"
    >
      <div class="modal-content">
        <div class="modal-header">
          <icon-question-circle-fill style="color: rgb(var(--warning-6));font-size: 28px;margin-right: 8px;"/>
          提示
        </div>
        <div class="modal-body">
            {{ confirmText }}
        </div>
      </div>
      <div class="modal-footer">
        <a-button style="margin-right: 24px;" @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </a-modal>
  </template>
  
  <script setup lang="ts">
  import { ref, watch,computed } from 'vue';

  const props = defineProps({
    visible: Boolean,
    confirmType:String,
  });
  const confirmText = computed(() => {
    const textMap = {
      'unbind': '仅可操作已授权的广告账户, 请确定是否解绑？',
      'delete': '仅可操作授权失效、已解绑的广告账户, 请确定是否删除？',
      'open': '请确认是否启用?',
      'pause': '请确认是否暂停?',
    }
    
    return textMap[props.confirmType] || ''
  })
  const emit = defineEmits(['update:visible', 'confirm']);
  
  const visible = ref(props.visible);
  watch(() => props.visible, v => {
    visible.value = v;
  });
  function handleOk() {
    emit('confirm');
    emit('update:visible', false);
  }
  function handleCancel() {
    emit('update:visible', false);
  }
  
  </script>
  
  <style scoped>
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }
  .modal-header{
    display: flex;
    align-items: center;
  }
  .modal-body{
    padding-left: 40px;
    font-size: 14px;
  }
  </style>