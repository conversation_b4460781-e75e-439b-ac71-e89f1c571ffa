<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <a-table
          :columns="columns"
          :data="filterData"
          size="small"
          :pagination="pagination"
          column-resizable
          summary-text="阶段总结"
          :scroll="scroll"
          @page-size-change="pageSizeChange"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from "vue";
import {WsCustomAnalysisResultData} from "@/api/report/type";
import * as XLSX from 'xlsx';
import {DateRange} from "@/api/analyse/type";
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";

interface Props {
  // 页面数量
  pageSize: number;

  /**
   * 报表数据
   */
  reportAnalysisData: WsCustomAnalysisResultData;
}

const props = defineProps<Props>()
const emits = defineEmits(['pageSizeChange']);
const columns = ref()
const data = ref()
const filterData = ref()
const loading = ref(true)

const pagination = ref<any>({
  pageSize: props.pageSize || 10,
  pageSizeOptions: [30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  showPageSize: 'true',
  showTotal: 'true'
})

const pageSizeChange = (size) => {
  pagination.value.pageSize = size;
  emits('pageSizeChange', size)
}

// 监听外部卡片大小变化
watch(() => props.pageSize, (newData, oldValue) => {
  pagination.value.pageSize = newData
})

function renderChart(reportAnalysisData: WsCustomAnalysisResultData) {
  if (!reportAnalysisData) {
    return
  }
  // 定义提取数字的辅助函数
  const extractNumber = (value) => {
    if (value === null || value === undefined) return null;
    if (typeof value !== 'string') return value;

    // 处理百分比格式 (如 "9.7%")
    if (value.includes('%')) {
      return parseFloat(value.replace(/%/g, '')) / 100;
    }

    // 处理千分位格式 (如 "1,234.56")
    if (value.includes(',')) {
      return parseFloat(value.replace(/,/g, ''));
    }

    // 尝试直接转换为数字
    const num = parseFloat(value);
    return isNaN(num) ? value : num;
  };

  columns.value = reportAnalysisData.headers?.map((record: any, index: number) => {
    return {
      title: record,
      dataIndex: `${index}`,
      align: 'center',
      width: record === 'params' ? 2600 : 160,
      sortable: {
        sortDirections: ['ascend', 'descend'],
        sorter: (a, b, extra) => {
          // 使用 extra.direction 获取排序方向
          const direction = extra.direction;
          const dataIndex = `${index}`;

          // 获取单元格值并转换
          const valueA = extractNumber(a[dataIndex]);
          const valueB = extractNumber(b[dataIndex]);

          // 处理空值
          if (valueA === null && valueB === null) return 0;
          if (valueA === null) return direction === 'ascend' ? -1 : 1;
          if (valueB === null) return direction === 'ascend' ? 1 : -1;

          // 数字比较
          if (typeof valueA === 'number' && typeof valueB === 'number') {
            return direction === 'ascend'
                ? valueA - valueB  // 升序：从小到大
                : valueB - valueA; // 降序：从大到小
          }

          // 字符串比较
          const strA = String(valueA).toLowerCase();
          const strB = String(valueB).toLowerCase();

          return direction === 'ascend'
              ? strA.localeCompare(strB)  // 升序：从小到大
              : strB.localeCompare(strA); // 降序：从大到小
        }
      }
    }
  });

  data.value = reportAnalysisData.rows?.map((dataRecord, index) => {
    const yData = {};
    dataRecord.forEach((record: any, index: number) => {
      yData[`${index}`] = record;
    })
    return {'key': index, ...yData};
  })

  // 确保数据更新后立即更新过滤后的数据
  filterData.value = data.value;
}

watch([data], ([newData1, newData2]) => {
  filterData.value = newData1
  if (newData2?.startTimeData && newData2?.endTimeData && newData1) {
    filterData.value = filterData.value.filter((record: any) => {
      return record[0] >= newData2.startTimeData && record[0] <= newData2.endTimeData;
    });
  }
});

function getCurrentTime() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  return `${year}${month}${day}`
}

// 导出
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  filterData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  const dateString = date ? `${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}` : ''
  XLSX.writeFile(newWorkbook, `sql分析${name ? `_${name}` : ''}${dateString ? `_${dateString}` : ''}.xlsx`);
};

watch(() => props.reportAnalysisData, async (newData, oldData) => {
  if (!newData){
    return
  }
  renderChart(newData);
  loading.value = false
})


const scroll = {
  y: '100%',
}


onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
  loading.value = false
})

defineExpose({
  exportXlsx
})

</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
