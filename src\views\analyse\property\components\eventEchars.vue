<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import {cloneDeep} from "lodash";
import dataSortBtn from "../../components/dataSortBtn.vue";

interface Props {
  /**
   * 展示label
   */
  showlabel: boolean
  /**
   * 展示百分比
   */
  showRate: boolean
  /**
   * 报表数据
   */
  eventData: any;
}

const props = defineProps<Props>()


const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const pieSeries = ref<any>([]);
const legendData = ref<any>([]);
const selected = ref<string[]>([]);
const indeterminate = ref(false)
const checkedAll = ref(false)

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])
const chartType = ref('distribution')


function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);


const {chartOption} = useChartOption(() => {
  if(chartType.value === 'distribution'){
    return {
      grid: {
        left: '0%',
        right: '0%',
        top: '40',
        bottom: '100',
        containLabel: true
      },
      legend: {
        data: legendData.value,
        bottom: '60',
        type: 'scroll', // 设置图例为滚动类型
        orient: 'horizontal', // 横向显示图例

      },
      xAxis: {
        type: 'category',
        data: xAxis.value,
      },
      yAxis: {
        type: 'value'
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        className: 'echarts-tooltip-diy',
        axisPointer: {
          type: axisPointerType.value
        },
      },
      graphic: {
        elements: graphicElements.value,
      },
      series: ySeries.value,
    };
  }
  if(chartType.value === 'pieTrend') {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}({d}%)'
      },
      series: pieSeries.value,
    };
  }
 
});

// 数组相加
const calculateSum = (array:any) => {
  return array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
};

// 饼图
const freshPieData = () => {
  axisPointerType.value = 'line'
  pieSeries.value = [{
      type: 'pie',
      radius: ['35%', '58%'],
      center: ['50%', '50%'],
      label:{
        formatter: '{b}'
      },
      labelLine: {
          normal: {
              show: true
          }
      },
      data: [
        { value: 1048, name: '不属于 今日登陆，不属于test' },
        { value: 735, name: '属于 今日登陆，不属于test' },
    ],
  }]
  // ySeriesData.value = JSON.parse(JSON.stringify(ySeries.value))
};

// 柱状图
const freshDistributionData = () => {
  axisPointerType.value = 'shadow'
  xAxis.value = ['不属于 今日登陆','属于 今日登陆']
  ySeries.value = [
    {
      name:'不属于test',
      data:[10220,722],
      type: 'bar',
      barWidth: 40,
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: props.showlabel,
        position: 'top'
      }
    },
    {
      name:'属于test',
      data:[55533,332],
      type: 'bar',
      barWidth: 40,
      stack: 'Ad',
      emphasis: {
        focus: 'series'
      },
      label: {
        show: props.showlabel,
        position: 'top'
      }
    }
  ]
  xAxisData.value = cloneDeep(xAxis.value)
  ySeriesData.value = cloneDeep(ySeries.value)
  legendData.value = ['不属于test','属于test']
  selected.value = legendData.value;
  checkedAll.value = true;
};

const handleXAxis = (values) => {
  xAxis.value = [`不属于 ${values}`,`属于 ${values}`]
}

const changeType = (type:string)=>{
  chartType.value = type
  switch (type){
    case 'pieTrend':
      freshPieData()
      break;
    case 'distribution':
      freshDistributionData()
      break;
    default:
      break;
  }
}

const changeLegend = (value:any,ev:any)=>{

}

const updateYAxisSeries = () => {
  if(chartType.value === 'distribution'){
    ySeries.value = ySeriesData.value.filter(range => selected.value.includes(range.name))
  }
  
};


// freshPieData()
freshDistributionData()

watch(selected, (newValue:any,oldValue:any) => {
  if(newValue){
    updateYAxisSeries();
  }
})
// 处理 显示数值 显示百分比下 饼图显示格式
const handlePieLabel = () => {
  if(chartType.value === 'pieTrend'){
    pieSeries.value.forEach((item:any)=>{
      if(props.showRate && props.showlabel){
        item.label = {
          formatter:'{b}:{d}%({c})'
        }
      }else if(props.showRate){
        item.label = {
          formatter:'{b}:{d}%'
        }
      }else if(props.showlabel){
        item.label = {
          formatter:'{b}:({c})'
        }
      }else{
        item.label = {
          formatter:'{b}'
        }
      }
    })
  }
}
watch(() => props.showlabel,(newValue:any,oldValue:any) => {
  if(chartType.value === 'distribution'){
    ySeries.value.forEach((item:any)=>{
      item.label = {
        show: props.showlabel,
        position: 'top'
      }
    })
  }
  handlePieLabel()
  
},{immediate:true})
watch(() => props.showRate,(newValue:any,oldValue:any) => {
  handlePieLabel()
  
},{immediate:true})


// 全选
const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    selected.value = legendData.value
  } else {
    checkedAll.value = false;
    selected.value = []
  }
}
const handleChange = (values) => {
  if (values.length === legendData.value.length) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
}

defineExpose({
  changeType,
  handleXAxis
})

</script>

<template>
  <div class="chart-content">
    <a-spin  style="width: 100%;height: 500px;">
      <div style="display: flex;justify-content: space-between;margin-top: 5px;">
        <div>
        </div>
        <div v-show="chartType === 'distribution'" style="display: flex;align-items: center;">
          <a-dropdown :hide-on-select="false" :update-at-scroll="true">
            <div style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;">指标( {{selected.length}}/{{legendData.length}})<icon-down/></div>
            <template #content>
                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                  <a-input placeholder="请输入搜索" style="border: none;height: 40px;">
                    <template #prefix>
                      <icon-search />
                    </template>
                  </a-input>
                </div>
                <a-checkbox-group v-model="selected" direction="vertical" style="width: 100%;" @change="handleChange">
                    <a-checkbox v-for="(item,index) in legendData" :key="index" :value="item">
                      <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                    </a-checkbox>
                </a-checkbox-group>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);">
                <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" style="width: 100%;" @change="handleChangeAll">全选</a-checkbox>
              </div>
            </template>
          </a-dropdown>
          <!-- 数据量排序 -->
          <dataSortBtn/>
        </div>
      </div>
      <div v-if="chartType === 'pieTrend'" class="pie-name">用户数</div>
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
:deep(.arco-dropdown-option){
  padding: 0;
}
:deep(.arco-checkbox){
  padding: 0 12px;
  &:hover{
    background-color: var(--color-fill-2);
  }
}
.pie-name{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header5-medium);
  margin-top: 24px;
}
</style>