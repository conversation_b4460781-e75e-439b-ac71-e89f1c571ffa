<script setup lang="ts">
import draggable from "vuedraggable";
import {FolderDto} from "@/api/folder/type";
import {CacheEventBus, TreeMenuEventBus} from "@/types/event-bus";
import {Message} from "@arco-design/web-vue";
import {useEventBus} from "@vueuse/core";
import {moveFolder} from "@/api/folder/api";
import {useDashboardStore} from "@/store";
import folderItem from "./item.vue"


interface Props {

  /**
   * 空间编码
   */
  spaceId?: string

  /**
   * 文件夹列表
   */
  folders?: FolderDto[]
}

const emit = defineEmits(['add']);
const props = defineProps<Props>()
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const dashboardStore = useDashboardStore()
const cacheEventBus = useEventBus(CacheEventBus)
const draggableFolder = (e) => {
  const index=e.newIndex;
  moveFolder({folderId:props.folders[index].folderId,spaceId:props.spaceId,order:index+1})
      .then((resp) => {

          cacheEventBus.emit('move-event', {
            type: 'folder',
            folderId: props.folders[index].folderId,
            spaceId: props.spaceId,
            order:index,
          });

      })
      .catch((e) => {
        Message.error("重命名失败！", e);
      });
}

</script>

<template>
  <draggable
      :disabled="dashboardStore.dashboardSearchStore"
      :list="folders || []"
      :group="spaceId+'_folder'"
      item-key="folderId"
      @update="draggableFolder"
  >
    <template #item="{ element }">
      <folder-item  :space-id="spaceId" :folder="element"  @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"/>
    </template>
  </draggable>
</template>

<style scoped lang="less">

</style>