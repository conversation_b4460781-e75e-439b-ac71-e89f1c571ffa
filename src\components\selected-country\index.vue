<template>
      <a-select
        v-model:model-value="country"
        :loading="loading"
        :style="{width:'240px',backgroundColor: 'var(--tant-bg-white-color-bg1-1)'}"
        placeholder="选择国家"
        :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
        :filter-option="false"
        :allow-search="true"
        @search="searchCountry"
        @change="countryChange"
        @popup-visible-change="popupVisibleChange">
        <template #label="{ data }">
            <span>国家-{{ data?.label }}</span>
        </template>
        <a-option v-for="item in countryList" :key="item.code" :value="item.code">
            {{ item.name }}
        </a-option>
    </a-select>
  </template>
  
  <script lang="ts" setup>
  import {ref, watch} from 'vue';
  import {getCountryList,countrySearch} from "@/api/marketing/api";
  import { cloneDeep } from 'lodash';
  
  const country = defineModel<string>('country', { default: 'global' })
  const loading = ref(false)
  const countryList = ref()
  const emits = defineEmits(['countryChange'])
  const allData = ref()
  const searchCountry = async (v) => {
    loading.value = true
    if (!v || v.trim() === '') {
      // 搜索内容为空时，恢复全量数据
      countryList.value = cloneDeep(allData.value)
    } else {
      // 有搜索内容时，执行搜索
      const data = {
        selectCountryCode: country.value ? [country.value] : [],
        content: v
      }
      await countrySearch(data).then(res => {
        countryList.value = res;
      })
    }
    loading.value = false
  }
  const countryChange = async () => {
    if(country.value){
        emits('countryChange', country.value)
    }else{
        searchCountry('')
    }
  }
  const popupVisibleChange = (v) => {
    if (v) {
      countryList.value = cloneDeep(allData.value)
    }
  }
  const init = async () => {
    loading.value = true
    await getCountryList().then(res => {
        countryList.value = res
        allData.value = res
    })
    loading.value = false
  }
  init()
  </script>
  
  <style scoped lang="less">
  </style>
    
