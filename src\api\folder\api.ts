import {AxiosPromise} from "axios";
import {getRequest, postRequest} from "@/api/request";
import {FolderSaveDto} from "@/api/folder/type";
import _ from "lodash";

// 节流后的 saveFolder 函数，确保每 300 毫秒内最多执行一次请求
export const saveFolder = _.throttle(
    (folder: FolderSaveDto): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/folder/save', {
        ...folder,
        folderName: folder.name,
        spaceId: folder.spaceId,
      });
    },
    300, // 节流间隔时间（毫秒），可根据需要调整
    { trailing: false } // 配置项：关闭最后一次触发
);
// 节流后的 moveFolder 函数
export const moveFolder = _.throttle(
    (folder: FolderSaveDto): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/folder/move', {
        ...folder,
        folderName: folder.name,
        spaceId: folder.spaceId,
      });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

// 节流后的 removeFolder 函数
export const removeFolder = _.throttle(
    (folderId: string): AxiosPromise<any> => {
      return getRequest<any>('/api/dashboard/folder/remove', { folderId });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);