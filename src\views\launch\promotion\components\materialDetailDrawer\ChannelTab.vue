

<template>
    <div class="page-content">
        <div class="table-wrap">
            <div class="wrap-left">
                <div class="text">详细数据</div>
            </div>
            <div class="wrap-right">
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-download />
                    </template>
                    <template #default>导出</template>
                </a-button>
            </div>
        </div>
        <div class="table-area">
          <a-table
              :columns="columns"
              :loading="loading"
              :data="tableData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :table-layout-fixed="true"
              :filter-icon-align-left="true"
              :column-resizable="true"
              :scroll="scroll"
              :pagination="false"
              :summary="true"
            >
            <template #name="{ record }">
              <div class="cell-content">
                <div class="text">{{ record.name }}</div>
              </div>
            </template>
            <template #summary-cell="{ column,record }">
              <div v-if="column.dataIndex === 'date'">
                <div class="text">汇总</div>
              </div>
              <div v-else-if="column.dataIndex === 'name'">-</div>
              <div v-else>{{record[column.dataIndex]}}</div>
            </template>
          </a-table>
          <div class="pagination">
              <a-pagination :total="total" show-total @change="pageChange"/>
          </div>
        </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { reactive, ref} from "vue";
  import dayjs from 'dayjs';
  import {Message} from '@arco-design/web-vue';
  
  const total = ref(0)
  // 模拟table数据
  const loading = ref(false)
  const tableData = ref<any>([
    {
      name: 'mockName1',
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
    {
      name: 'mockName2',
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
  ])
  const columns = ref<any>([
    { title: '日期', dataIndex: 'date',slotName:'date',width:200,fixed: 'left', },
    { title: '渠道', dataIndex: 'name',slotName:'name',width:200,fixed: 'left', },
    { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  ])
  
  // Table页面滑动
  const scroll = {
    y: 'calc(100% - 56px)'
  };
  const init = () => {
  }
  init()
  // 分页
  const pageChange = (v) => {
    init()
  }
  </script>
  
  <style scoped lang="less">
  .page-content {
    width: 100%;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
  }
  .setting{
    display: flex;
    .setting-content{
      flex-wrap: wrap;
      display: flex;
      row-gap: 16px;
    }
    .label{
      width: 60px;
    }
  }
  .table-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .wrap-left{
      .text{
        color: rgb(var(--gray-10));
        font-weight: 500;
        
      }
    }
    .wrap-right{
    }
  }
  .table-area {
    flex: 1 1 0%;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
  .br4{
    border-radius: 4px;
  }
  .cell-content{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text{
      // color: #0b75ff;
      // background: transparent;
      // cursor: pointer;
      // transition: color .2s ease;
      display: -webkit-box;
      overflow: hidden;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
      text-overflow: ellipsis;
    }
  }
  </style>