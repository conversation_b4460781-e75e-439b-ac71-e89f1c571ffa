<script setup lang="ts">
import {inject, ref, watch} from "vue";
import * as XLSX from 'xlsx';
import {cloneDeep} from 'lodash'
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {createCustomSorter} from "@/utils/strUtil";

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  stepValueName:string
  chartType:string
  stepEvents:any
}

const props = defineProps<Props>()
const groups = ref<any>([]) // 分组
const dateRange = inject('boardDate') as DateRange;

const columns = ref<any>([]);
const tableData = ref<any>([]);

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])
const tableRadio = ref<string>('transform')
const scroll = {x: 1000}
// 处理列表数据
const handleTableData = () =>{
  if(props.chartType === 'distribution'){
    // 转化图table
    let rateData
    let numData
    if(tableRadio.value === 'loss'){
      // 流失
      rateData = props.eventData?.funnelQueryResult.find(item => item.type === 'churn_rate')?.groupData || []
      numData = props.eventData?.funnelQueryResult.find(item => item.type === 'churn_num')?.groupData || []
    }else{
      // 转化
      rateData = props.eventData?.funnelQueryResult.find(item => item.type === 'conversion_rate')?.groupData || []
      numData = props.eventData?.funnelQueryResult.find(item => item.type === 'conversion_num')?.groupData || []
    }
    tableData.value = groups.value.map((name,index) => {
      // 找到对应的转化率和转化数数据
      const rateItem = rateData.find(item => item.group[0] === name).values
      const numItem = numData.find(item => item.group[0] === name).values
      // 创建基础对象
      const baseObj = { groupName: name }
      
      // 动态添加 num 和 rate 属性
      return rateItem.reduce((acc, _, index) => {
        acc[`num${index + 1}`] = numItem[index] || 0
        acc[`rate${index + 1}`] = (Math.round(rateItem[index] * 100 * 100) / 100) || 0
        return acc
      }, baseObj)
    })
  }else{
    // 趋势图table
  }
}
const getFilterOptions = () => {
  return Array.from(new Set(tableData.value.map(item => item.groupName)))
    .map(value => ({
      label: value,
      value: value
    }));
};
// 处理列表头
const handleColumns = () => {
  if(props.chartType === 'distribution'){
    // 转化图table
    const groupsDescName = props.eventData?.groupsDesc[0]?.name
    const groupsFormat = props.eventData?.groupsDesc[0]?.format
    const groupColumn = [
      {
        title:groups.value.length > 1 ? groupsDescName : '总体',
        slotName:'groupName',
        dataIndex:'groupName',
        sortable: {
          sortDirections: ['ascend', 'descend'],
          sorter: (a, b, extra) => {
            const { direction } = extra;
            const valueA = a[`groupName`];
            const valueB = b[`groupName`];
            return createCustomSorter(groupsFormat)(valueA, valueB, direction);
          }
        },
        filterable: groups.value.length > 1 ? {
          filter: (value, row) => !value?.length || value.includes(row.groupName),
          slotName: 'group-filter',
          multiple: true
        } : undefined
      }
    ]
    let stepColumn
    if(tableRadio.value === 'loss'){
      // 流失
      stepColumn = props.eventData?.stepEvents.map((item,index) => {
          return {
            title:index > 0 ? `${item.eventDisplayName}(步骤${index}到步骤${index+1})`:`${item.eventDisplayName}(步骤${index + 1})`,
            dataIndex:`num${index + 1}`,
            slotName:`num${index + 1}`,
            minWidth:200,
            sortable: { sortDirections: ['ascend', 'descend'] }
          }
      })
    }else{
      // 转化
      stepColumn = props.eventData?.stepEvents.map((item,index) => {
          return {
            title:`${item.eventDisplayName}(步骤${index + 1})`,
            dataIndex:`num${index + 1}`,
            slotName:`num${index + 1}`,
            minWidth:200,
            sortable: { sortDirections: ['ascend', 'descend'] }
          }
      })
    }
    columns.value = groupColumn.concat(stepColumn)
  }else{
    // 趋势图table
  }
  
}

const freshData = () => {
  groups.value = props.eventData?.groups?.map(item => {
      return item.map(value => value === null ? 'null' : value).join(',');
  }) || [];
  handleColumns()
  handleTableData()
  copyColumns.value = cloneDeep(columns.value)
  copyTableData.value = cloneDeep(tableData.value)
};

freshData()

// 处理columns
watch(() => props.chartType,()=>{
  handleColumns()
})
const handleChangeTable = (value) => {
  handleColumns()
  handleTableData()
}
// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `漏斗分析_${getDateRangeStartDate(dateRange.value)}-${getDateRangeEndDate(dateRange.value)}.xlsx`);
};
const createVisible = ref(false)
const form = ref({
  name: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  createVisible.value = false;
}
const saveReport = async () => {
}
defineExpose({
  freshData,
  exportXlsx
})
// 处理表格颜色深浅
const getBackgroundColor = (index, value) => {
  if (index ===0) {
    return {background: 'rgb(255, 255, 255)'};
  }
  if (index === 1) {
    return {background: 'rgb(249, 249, 251)'};
  }
  // 如果没有值，返回白色背景
  if (value === null || value === undefined || value === '') {
    return {background: 'rgb(255, 255, 255)'};
  }
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return {background: 'rgb(255, 255, 255)'};
  }
  // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
  const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
  const backgroundColor = `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
  return {background: backgroundColor};
}
</script>

<template>
  <div class="chart-content" style="margin-bottom: 24px;">
    <div style="display: flex;justify-content: space-between;">
      <div>
        <a-radio-group v-model="tableRadio" type="button" style="margin-bottom: 18px;" @change="handleChangeTable">
          <a-tooltip content="转化">
            <a-radio value="transform">转化</a-radio>
          </a-tooltip>
          <a-tooltip content="流失">
            <a-radio value="loss">流失</a-radio>
          </a-tooltip>
        </a-radio-group>
      </div>
      <div>
        <a-button style="margin-left: 12px;" @click="exportXlsx">导出</a-button>
      </div>
    </div>
    <a-spin style="width: 100%;height: 100%">
      <div class="table-title">
        {{props.stepValueName}}
        （共{{props.stepEvents?.length}}步）的用户
        <span v-if="tableRadio === 'transform' && chartType === 'distribution'">累计</span>
        <span v-if="tableRadio === 'loss' && chartType === 'distribution'">单步</span>
        <span v-if="tableRadio === 'transform'">转化率</span>
        <span v-else>流失率</span>
      </div>
      <a-table :columns="columns" :data="tableData" :scroll="scroll" :pagination="tableData.length>50" size="small" column-resizable :bordered="{cell:true}" :filter-icon-align-left="true">
        <!-- 添加筛选器插槽 -->
        <template #group-filter="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions()"
                :style="{ width: '200px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record }">
          <div class="cell-class" :style="getBackgroundColor(index,record[`rate${index}`])">
            <div class="hover-box">
              <span>{{record[column.dataIndex]}}</span>
              <svg v-if="column.dataIndex != 'groupName'" class="add-group" @click="() => createVisible = true" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-4 0a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z"></path><path d="M1 10a2 2 0 012-2h3a2 2 0 012 2v3H7v-3a1 1 0 00-1-1H3a1 1 0 00-1 1v3H1v-3z"></path><path d="M13 10h-1V8h-2V7h2V5h1v2h2v1h-2v2z"></path></svg></svg>
            </div>
            <div v-if="index >1" class="rate">{{(record[`rate${index}`]).toFixed(2)}}%</div>
          </div>
        </template>
      </a-table>
    </a-spin>
    <!-- 创建弹窗 -->
    <a-modal v-model:visible="createVisible" :on-before-ok="validateForm" width="480px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">
          创建结果分群
          <a-tooltip content="选择计算结果保存为分群，可用于进一步下钻分析。" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <div class="form-label">分群显示名</div>
        <a-form-item field="name" :hide-label="true" :hide-asterisk="true" required :rules="[{ required: true, message: '分群显示名不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <div class="form-label">分群备注(选填)</div>
        <a-form-item field="description" :hide-label="true">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:3, maxRows:10}" :show-word-limit="true" :max-length="200"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="boe-foot">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" @click="saveReport">创建</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
:deep(tbody .arco-table-cell) {
  padding: 0;
}
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
.cell-class {
  padding: 5px 16px;
  height: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.table-title {
    width: 100%;
    color: var(--tant-text-gray-color-text1-1);
    font-size: 14px;
    line-height: 24px;
    text-align: center;
}
.hover-box{
  display: flex;
  align-items: center;
  &:hover{
    .add-group{
      opacity: 1;
    }
  }
  .add-group{
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;
    &:hover{
      color: #8d0088;
    }
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
.form-label{
  line-height: 32px;
  font-weight: 500;
}
</style>