export const androidPermissions = [
    {id: "ACCEPT_HANDOVER", desc: ""},
    {id: "ACCESS_BACKGROUND_LOCATION",  desc: ""},
    {id: "ACCESS_BLOBS_ACROSS_USERS",  desc: ""},
    {id: "ACCESS_CHECKIN_PROPERTIES",  desc: ""},
    {id: "ACCESS_COARSE_LOCATION",  desc: ""},
    {id: "ACCESS_FINE_LOCATION",  desc: ""},
    {id: "ACCESS_HIDDEN_PROFILES",  desc: ""},
    {id: "ACCESS_LOCATION_EXTRA_COMMANDS",  desc: ""},
    {id: "ACCESS_MEDIA_LOCATION",  desc: ""},
    {id: "ACCESS_NETWORK_STATE",  desc: ""},
    {id: "ACCESS_NOTIFICATION_POLICY",  desc: ""},
    {id: "ACCESS_WIFI_STATE",  desc: ""},
    {id: "ACCOUNT_MANAGER",  desc: ""},
    {id: "ACTIVITY_RECOGNITION",  desc: ""},
    {id: "ADD_VOICEMAIL",  desc: ""},
    {id: "ANSWER_PHONE_CALLS",  desc: ""},
    {id: "APPLY_PICTURE_PROFILE",  desc: ""},
    {id: "BATTERY_STATS",  desc: ""},
    {id: "BIND_ACCESSIBILITY_SERVICE",  desc: ""},
    {id: "BIND_APPWIDGET",  desc: ""},
    {id: "BIND_APP_FUNCTION_SERVICE",  desc: ""},
    {id: "BIND_AUTOFILL_SERVICE",  desc: ""},
    {id: "BIND_CALL_REDIRECTION_SERVICE",  desc: ""},
    {id: "BIND_CARRIER_MESSAGING_CLIENT_SERVICE",  desc: ""},
    {id: "BIND_CARRIER_MESSAGING_SERVICE",  desc: ""},
    {id: "BIND_CARRIER_SERVICES",  desc: ""},
    {id: "BIND_CHOOSER_TARGET_SERVICE",  desc: ""},
    {id: "BIND_COMPANION_DEVICE_SERVICE",  desc: ""},
    {id: "BIND_CONDITION_PROVIDER_SERVICE",  desc: ""},
    {id: "BIND_CONTROLS",  desc: ""},
    {id: "BIND_CREDENTIAL_PROVIDER_SERVICE",  desc: ""},
    {id: "BIND_DEVICE_ADMIN",  desc: ""},
    {id: "BIND_DREAM_SERVICE",  desc: ""},
    {id: "BIND_INCALL_SERVICE",  desc: ""},
    {id: "BIND_INPUT_METHOD",  desc: ""},
    {id: "BIND_MIDI_DEVICE_SERVICE",  desc: ""},
    {id: "BIND_NFC_SERVICE",  desc: ""},
    {id: "BIND_NOTIFICATION_LISTENER_SERVICE",  desc: ""},
    {id: "BIND_PRINT_SERVICE",  desc: ""},
    {id: "BIND_QUICK_ACCESS_WALLET_SERVICE",  desc: ""},
    {id: "BIND_QUICK_SETTINGS_TILE",  desc: ""},
    {id: "BIND_REMOTEVIEWS",  desc: ""},
    {id: "BIND_SCREENING_SERVICE",  desc: ""},
    {id: "BIND_TELECOM_CONNECTION_SERVICE",  desc: ""},
    {id: "BIND_TEXT_SERVICE",  desc: ""},
    {id: "BIND_TV_AD_SERVICE",  desc: ""},
    {id: "BIND_TV_INPUT",  desc: ""},
    {id: "BIND_TV_INTERACTIVE_APP",  desc: ""},
    {id: "BIND_VISUAL_VOICEMAIL_SERVICE",  desc: ""},
    {id: "BIND_VOICE_INTERACTION",  desc: ""},
    {id: "BIND_VPN_SERVICE",  desc: ""},
    {id: "BIND_VR_LISTENER_SERVICE",  desc: ""},
    {id: "BIND_WALLPAPER",  desc: ""},
    {id: "BLUETOOTH",  desc: ""},
    {id: "BLUETOOTH_ADMIN",  desc: ""},
    {id: "BLUETOOTH_ADVERTISE",  desc: ""},
    {id: "BLUETOOTH_CONNECT",  desc: ""},
    {id: "BLUETOOTH_PRIVILEGED",  desc: ""},
    {id: "BLUETOOTH_SCAN",  desc: ""},
    {id: "BODY_SENSORS",  desc: ""},
    {id: "BODY_SENSORS_BACKGROUND",  desc: ""},
    {id: "BROADCAST_PACKAGE_REMOVED",  desc: ""},
    {id: "BROADCAST_SMS",  desc: ""},
    {id: "BROADCAST_STICKY",  desc: ""},
    {id: "BROADCAST_WAP_PUSH",  desc: ""},
    {id: "CALL_COMPANION_APP",  desc: ""},
    {id: "CALL_PHONE",  desc: ""},
    {id: "CALL_PRIVILEGED",  desc: ""},
    {id: "CAMERA",  desc: ""},
    {id: "CAPTURE_AUDIO_OUTPUT",  desc: ""},
    {id: "CHANGE_COMPONENT_ENABLED_STATE",  desc: ""},
    {id: "CHANGE_CONFIGURATION",  desc: ""},
    {id: "CHANGE_NETWORK_STATE",  desc: ""},
    {id: "CHANGE_WIFI_MULTICAST_STATE",  desc: ""},
    {id: "CHANGE_WIFI_STATE",  desc: ""},
    {id: "CLEAR_APP_CACHE",  desc: ""},
    {id: "CONFIGURE_WIFI_DISPLAY",  desc: ""},
    {id: "CONTROL_LOCATION_UPDATES",  desc: ""},
    {id: "CREDENTIAL_MANAGER_QUERY_CANDIDATE_CREDENTIALS",  desc: ""},
    {id: "CREDENTIAL_MANAGER_SET_ALLOWED_PROVIDERS",  desc: ""},
    {id: "CREDENTIAL_MANAGER_SET_ORIGIN",  desc: ""},
    {id: "DELETE_CACHE_FILES",  desc: ""},
    {id: "DELETE_PACKAGES",  desc: ""},
    {id: "DELIVER_COMPANION_MESSAGES",  desc: ""},
    {id: "DETECT_SCREEN_CAPTURE",  desc: ""},
    {id: "DETECT_SCREEN_RECORDING",  desc: ""},
    {id: "DIAGNOSTIC",  desc: ""},
    {id: "DISABLE_KEYGUARD",  desc: ""},
    {id: "DUMP",  desc: ""},
    {id: "ENFORCE_UPDATE_OWNERSHIP",  desc: ""},
    {id: "EXECUTE_APP_ACTION",  desc: ""},
    {id: "EXECUTE_APP_FUNCTIONS",  desc: ""},
    {id: "EXPAND_STATUS_BAR",  desc: ""},
    {id: "FACTORY_TEST",  desc: ""},
    {id: "FOREGROUND_SERVICE",  desc: ""},
    {id: "FOREGROUND_SERVICE_CAMERA",  desc: ""},
    {id: "FOREGROUND_SERVICE_CONNECTED_DEVICE",  desc: ""},
    {id: "FOREGROUND_SERVICE_DATA_SYNC",  desc: ""},
    {id: "FOREGROUND_SERVICE_HEALTH",  desc: ""},
    {id: "FOREGROUND_SERVICE_LOCATION",  desc: ""},
    {id: "FOREGROUND_SERVICE_MEDIA_PLAYBACK",  desc: ""},
    {id: "FOREGROUND_SERVICE_MEDIA_PROCESSING",  desc: ""},
    {id: "FOREGROUND_SERVICE_MEDIA_PROJECTION",  desc: ""},
    {id: "FOREGROUND_SERVICE_MICROPHONE",  desc: ""},
    {id: "FOREGROUND_SERVICE_PHONE_CALL",  desc: ""},
    {id: "FOREGROUND_SERVICE_REMOTE_MESSAGING",  desc: ""},
    {id: "FOREGROUND_SERVICE_SPECIAL_USE",  desc: ""},
    {id: "FOREGROUND_SERVICE_SYSTEM_EXEMPTED",  desc: ""},
    {id: "GET_ACCOUNTS",  desc: ""},
    {id: "GET_ACCOUNTS_PRIVILEGED",  desc: ""},
    {id: "GET_PACKAGE_SIZE",  desc: ""},
    {id: "GET_TASKS",  desc: ""},
    {id: "GLOBAL_SEARCH",  desc: ""},
    {id: "HIDE_OVERLAY_WINDOWS",  desc: ""},
    {id: "HIGH_SAMPLING_RATE_SENSORS",  desc: ""},
    {id: "INSTALL_LOCATION_PROVIDER",  desc: ""},
    {id: "INSTALL_PACKAGES",  desc: ""},
    {id: "INSTALL_SHORTCUT",  desc: ""},
    {id: "INSTANT_APP_FOREGROUND_SERVICE",  desc: ""},
    {id: "INTERACT_ACROSS_PROFILES",  desc: ""},
    {id: "INTERNET",  desc: ""},
    {id: "KILL_BACKGROUND_PROCESSES",  desc: ""},
    {id: "LAUNCH_CAPTURE_CONTENT_ACTIVITY_FOR_NOTE",  desc: ""},
    {id: "LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK",  desc: ""},
    {id: "LOADER_USAGE_STATS",  desc: ""},
    {id: "LOCATION_HARDWARE",  desc: ""},
    {id: "MANAGE_DEVICE_LOCK_STATE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ACCESSIBILITY",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ACCOUNT_MANAGEMENT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ACROSS_USERS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ACROSS_USERS_FULL",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ACROSS_USERS_SECURITY_CRITICAL",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_AIRPLANE_MODE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_APPS_CONTROL",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_APP_FUNCTIONS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_APP_RESTRICTIONS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_APP_USER_DATA",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ASSIST_CONTENT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_AUDIO_OUTPUT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_AUTOFILL",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_BACKUP_SERVICE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_BLOCK_UNINSTALL",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_BLUETOOTH",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_BUGREPORT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_CALLS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_CAMERA",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_CAMERA_TOGGLE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_CERTIFICATES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_COMMON_CRITERIA_MODE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_CONTENT_PROTECTION",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_DEBUGGING_FEATURES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_DEFAULT_SMS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_DEVICE_IDENTIFIERS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_DISPLAY",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_FACTORY_RESET",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_FUN",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_INPUT_METHODS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_INSTALL_UNKNOWN_SOURCES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_KEEP_UNINSTALLED_PACKAGES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_KEYGUARD",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_LOCALE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_LOCATION",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_LOCK",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_LOCK_CREDENTIALS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_LOCK_TASK",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MANAGED_SUBSCRIPTIONS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_METERED_DATA",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MICROPHONE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MICROPHONE_TOGGLE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MOBILE_NETWORK",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MODIFY_USERS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_MTE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_NEARBY_COMMUNICATION",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_NETWORK_LOGGING",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_ORGANIZATION_IDENTITY",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_OVERRIDE_APN",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PACKAGE_STATE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PHYSICAL_MEDIA",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PRINTING",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PRIVATE_DNS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PROFILES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PROFILE_INTERACTION",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_PROXY",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_QUERY_SYSTEM_UPDATES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_RESET_PASSWORD",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_RESTRICT_PRIVATE_DNS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_RUNTIME_PERMISSIONS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_RUN_IN_BACKGROUND",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SAFE_BOOT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SCREEN_CAPTURE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SCREEN_CONTENT",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SECURITY_LOGGING",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SETTINGS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SMS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_STATUS_BAR",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SUPPORT_MESSAGE",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SUSPEND_PERSONAL_APPS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SYSTEM_APPS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SYSTEM_DIALOGS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_SYSTEM_UPDATES",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_THREAD_NETWORK",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_TIME",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_USB_DATA_SIGNALLING",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_USB_FILE_TRANSFER",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_USERS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_VPN",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_WALLPAPER",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_WIFI",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_WINDOWS",  desc: ""},
    {id: "MANAGE_DEVICE_POLICY_WIPE_DATA",  desc: ""},
    {id: "MANAGE_DOCUMENTS",  desc: ""},
    {id: "MANAGE_EXTERNAL_STORAGE",  desc: ""},
    {id: "MANAGE_MEDIA",  desc: ""},
    {id: "MANAGE_ONGOING_CALLS",  desc: ""},
    {id: "MANAGE_OWN_CALLS",  desc: ""},
    {id: "MANAGE_WIFI_INTERFACES",  desc: ""},
    {id: "MANAGE_WIFI_NETWORK_SELECTION",  desc: ""},
    {id: "MASTER_CLEAR",  desc: ""},
    {id: "MEDIA_CONTENT_CONTROL",  desc: ""},
    {id: "MEDIA_ROUTING_CONTROL",  desc: ""},
    {id: "MODIFY_AUDIO_SETTINGS",  desc: ""},
    {id: "MODIFY_PHONE_STATE",  desc: ""},
    {id: "MOUNT_FORMAT_FILESYSTEMS",  desc: ""},
    {id: "MOUNT_UNMOUNT_FILESYSTEMS",  desc: ""},
    {id: "NEARBY_WIFI_DEVICES",  desc: ""},
    {id: "NFC",  desc: ""},
    {id: "NFC_PREFERRED_PAYMENT_INFO",  desc: ""},
    {id: "NFC_TRANSACTION_EVENT",  desc: ""},
    {id: "OVERRIDE_WIFI_CONFIG",  desc: ""},
    {id: "PACKAGE_USAGE_STATS",  desc: ""},
    {id: "PERSISTENT_ACTIVITY",  desc: ""},
    {id: "POST_NOTIFICATIONS",  desc: ""},
    {id: "PROCESS_OUTGOING_CALLS",  desc: ""},
    {id: "PROVIDE_OWN_AUTOFILL_SUGGESTIONS",  desc: ""},
    {id: "PROVIDE_REMOTE_CREDENTIALS",  desc: ""},
    {id: "QUERY_ADVANCED_PROTECTION_MODE",  desc: ""},
    {id: "QUERY_ALL_PACKAGES",  desc: ""},
    {id: "RANGING",  desc: ""},
    {id: "READ_ASSISTANT_APP_SEARCH_DATA",  desc: ""},
    {id: "READ_BASIC_PHONE_STATE",  desc: ""},
    {id: "READ_CALENDAR",  desc: ""},
    {id: "READ_CALL_LOG",  desc: ""},
    {id: "READ_COLOR_ZONES",  desc: ""},
    {id: "READ_CONTACTS",  desc: ""},
    {id: "READ_DROPBOX_DATA",  desc: ""},
    {id: "READ_EXTERNAL_STORAGE",  desc: ""},
    {id: "READ_HOME_APP_SEARCH_DATA",  desc: ""},
    {id: "READ_INPUT_STATE",  desc: ""},
    {id: "READ_LOGS",  desc: ""},
    {id: "READ_MEDIA_AUDIO",  desc: ""},
    {id: "READ_MEDIA_IMAGES",  desc: ""},
    {id: "READ_MEDIA_VIDEO",  desc: ""},
    {id: "READ_MEDIA_VISUAL_USER_SELECTED",  desc: ""},
    {id: "READ_NEARBY_STREAMING_POLICY",  desc: ""},
    {id: "READ_PHONE_NUMBERS",  desc: ""},
    {id: "READ_PHONE_STATE",  desc: ""},
    {id: "READ_PRECISE_PHONE_STATE",  desc: ""},
    {id: "READ_SMS",  desc: ""},
    {id: "READ_SYNC_SETTINGS",  desc: ""},
    {id: "READ_SYNC_STATS",  desc: ""},
    {id: "READ_SYSTEM_PREFERENCES",  desc: ""},
    {id: "READ_VOICEMAIL",  desc: ""},
    {id: "REBOOT",  desc: ""},
    {id: "RECEIVE_BOOT_COMPLETED",  desc: ""},
    {id: "RECEIVE_MMS",  desc: ""},
    {id: "RECEIVE_SMS",  desc: ""},
    {id: "RECEIVE_WAP_PUSH",  desc: ""},
    {id: "RECORD_AUDIO",  desc: ""},
    {id: "REORDER_TASKS",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_APP_STREAMING",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_AUTOMOTIVE_PROJECTION",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_COMPUTER",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_GLASSES",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_NEARBY_DEVICE_STREAMING",  desc: ""},
    {id: "REQUEST_COMPANION_PROFILE_WATCH",  desc: ""},
    {id: "REQUEST_COMPANION_RUN_IN_BACKGROUND",  desc: ""},
    {id: "REQUEST_COMPANION_SELF_MANAGED",  desc: ""},
    {id: "REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND",  desc: ""},
    {id: "REQUEST_COMPANION_USE_DATA_IN_BACKGROUND",  desc: ""},
    {id: "REQUEST_DELETE_PACKAGES",  desc: ""},
    {id: "REQUEST_IGNORE_BATTERY_OPTIMIZATIONS",  desc: ""},
    {id: "REQUEST_INSTALL_PACKAGES",  desc: ""},
    {id: "REQUEST_OBSERVE_COMPANION_DEVICE_PRESENCE",  desc: ""},
    {id: "REQUEST_OBSERVE_DEVICE_UUID_PRESENCE",  desc: ""},
    {id: "REQUEST_PASSWORD_COMPLEXITY",  desc: ""},
    {id: "RESTART_PACKAGES",  desc: ""},
    {id: "RUN_USER_INITIATED_JOBS",  desc: ""},
    {id: "SCHEDULE_EXACT_ALARM",  desc: ""},
    {id: "SEND_RESPOND_VIA_MESSAGE",  desc: ""},
    {id: "SEND_SMS",  desc: ""},
    {id: "SET_ALARM",  desc: ""},
    {id: "SET_ALWAYS_FINISH",  desc: ""},
    {id: "SET_ANIMATION_SCALE",  desc: ""},
    {id: "SET_BIOMETRIC_DIALOG_ADVANCED",  desc: ""},
    {id: "SET_DEBUG_APP",  desc: ""},
    {id: "SET_PREFERRED_APPLICATIONS",  desc: ""},
    {id: "SET_PROCESS_LIMIT",  desc: ""},
    {id: "SET_TIME",  desc: ""},
    {id: "SET_TIME_ZONE",  desc: ""},
    {id: "SET_WALLPAPER",  desc: ""},
    {id: "SET_WALLPAPER_HINTS",  desc: ""},
    {id: "SIGNAL_PERSISTENT_PROCESSES",  desc: ""},
    {id: "SMS_FINANCIAL_TRANSACTIONS",  desc: ""},
    {id: "START_FOREGROUND_SERVICES_FROM_BACKGROUND",  desc: ""},
    {id: "START_VIEW_APP_FEATURES",  desc: ""},
    {id: "START_VIEW_PERMISSION_USAGE",  desc: ""},
    {id: "STATUS_BAR",  desc: ""},
    {id: "SUBSCRIBE_TO_KEYGUARD_LOCKED_STATE",  desc: ""},
    {id: "SYSTEM_ALERT_WINDOW",  desc: ""},
    {id: "TRANSMIT_IR",  desc: ""},
    {id: "TURN_SCREEN_ON",  desc: ""},
    {id: "TV_IMPLICIT_ENTER_PIP",  desc: ""},
    {id: "UNINSTALL_SHORTCUT",  desc: ""},
    {id: "UPDATE_DEVICE_STATS",  desc: ""},
    {id: "UPDATE_PACKAGES_WITHOUT_USER_ACTION",  desc: ""},
    {id: "USE_BIOMETRIC",  desc: ""},
    {id: "USE_EXACT_ALARM",  desc: ""},
    {id: "USE_FINGERPRINT",  desc: ""},
    {id: "USE_FULL_SCREEN_INTENT",  desc: ""},
    {id: "USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER",  desc: ""},
    {id: "USE_SIP",  desc: ""},
    {id: "UWB_RANGING",  desc: ""},
    {id: "VIBRATE",  desc: ""},
    {id: "WAKE_LOCK",  desc: ""},
    {id: "WRITE_APN_SETTINGS",  desc: ""},
    {id: "WRITE_CALENDAR",  desc: ""},
    {id: "WRITE_CALL_LOG",  desc: ""},
    {id: "WRITE_CONTACTS",  desc: ""},
    {id: "WRITE_EXTERNAL_STORAGE",  desc: ""},
    {id: "WRITE_GSERVICES",  desc: ""},
    {id: "WRITE_SECURE_SETTINGS",  desc: ""},
    {id: "WRITE_SETTINGS",  desc: ""},
    {id: "WRITE_SYNC_SETTINGS",  desc: ""},
    {id: "WRITE_SYSTEM_PREFERENCES",  desc: ""},
    {id: "WRITE_VOICEMAIL",  desc: ""},
]

export const configsGroup = [
    // { name: "应用选择" , link: "#appid"},
    { name: "打包选项" , link: "#jsonversion"},
    { name: "SDK配置" , link: "#sdkconfig"},
    { name: "应用信息" , link: "#appinfo"},
    { name: "ICON" , link: "#icon"},
    { name: "应用名多语言" , link: "#names"},
    { name: "代码仓库" , link: "#git"},
    { name: "CPU架构" , link: "#cpulib"},
    { name: "签名" , link: "#keystore"},
    { name: "FIREBASE" , link: "#googleservice"},
    { name: "插件版本" , link: "#pluginversion"},
    { name: "注包配置" , link: "#buildconfig"},
    { name: "META数据" , link: "#metadata"},
    { name: "权限移除" , link: "#removepermissions"},
    { name: "GLOBAL GRADLE" , link: "#globalgradle"},
    { name: "APP GRADLE" , link: "#appgradle"}
]

export const packTypeOptions = ['cocos', 'unity']
export const gradleVersionOptions = ['6.7.1', '7.5.1', '8.9']
export const packModeOptions = [{id: 'normal', label: '普通打包'}, {id: 'pad', label: 'PAD打包'}]
export const cpuLibOptions = ['arm64-v8a', 'armeabi-v7a', 'x86']
export const implementationTypeOptions = [
    {id: '', label: '默认'},
    {id: 'fileTree', label: 'fileTree'},
    {id: 'platform', label: 'platform'},
    {id: 'project', label: 'project'},
    {id: 'coreLibraryDesugaring', label: 'coreLibraryDesugaring'}
]
export const bundleOptions = [
    {id: 'language', value: 'enableSplit = false'},
    {id: 'density ', value: 'enableSplit = false'},
    {id: 'abi', value: 'enableSplit = true'}
]
export const langOptions = [
    {id: 'en_US', value: 'en_US'},
    {id: 'zh-rCN ', value: 'zh-rCN'},
    {id: 'ja_JP', value: 'ja_JP'},
    {id: 'it_IT', value: 'it_IT'},
    {id: 'ko_KR', value: 'ko_KR'},
    {id: 'de_DE', value: 'de_DE'}
]

export const taskColumns = [
    {
        title: '创建时间',
        dataIndex: 'time',
    }, {
        title: '任务名称',
        dataIndex: 'name',
    },{
        title: '数据版本',
        dataIndex: 'buildVersion',
    },{
        title: 'SDK版本',
        dataIndex: 'sdkVersion',
    }, {
        title: '打包人员',
        dataIndex: 'creator',
    }, {
        title: '状态',
        slotName: 'status',
    }
]

export const backupColumns = [
    {
        title: '备份名称',
        dataIndex: 'name',
    }, {
        title: '操作',
        slotName: 'optional',
    }
]

export const metaDefaultConfigs = [
    {id: 'ivy_debug', value: 'false'},
    {id: 'ivy_app_id', value: ''},
    {id: 'facebook_app_id', value: ''},
    {id: 'facebook_client_token', value: ''},
    {id: 'admob_application_id', value: ''},
    {id: 'gms_play_services_id', value: ''},
    {id: 'applovin_sdk_key', value: ''},
    {id: 'ivy_notch', value: 'true'},
    {id: 'aps_id', value: ''}
]

export const metaDefaultConfigsOld = [
    {id: 'ivy_debug', value: 'false'},
    {id: 'facebook_appId', value: ''},
    {id: 'facebook_clientToken', value: ''},
    {id: 'google_admob_application_id', value: ''},
    {id: 'parfka_appToken', value: ''},
    {id: 'smaato_appId', value: ''},
    {id: 'applovin_sdk_key', value: ''},
    {id: 'gms_games_app_id', value: ''},
    {id: 'applovin_sdk_key', value: ''},
    {id: 'ivy_notch', value: 'true'},
    {id: 'din', value: 'true'},
    {id: 'aps_id', value: ''}
]

//ios
export const iosConfigsGroup = [
    { name: "打包选项" , link: "#jsonversion"},
    { name: "SDK配置" , link: "#sdkconfig"},
    { name: "应用信息" , link: "#appinfo"},
    { name: "ICON" , link: "#icon"},
    { name: "IMAGES" , link: "#images"},
    { name: "应用名多语言" , link: "#names"},
    { name: "代码仓库" , link: "#git"},
    { name: "FIREBASE" , link: "#filemanage"},
    { name: "注包配置" , link: "#buildconfig"},
    { name: "FB配置" , link: "#fbConfig"},
    { name: "广告配置" , link: "#adConfig"},
    { name: "3D TOUCH" , link: "#3dtouch"},
]

export const adDefaultConfigs = [
    {id: 'admob_appid', value: ''},
    {id: 'app_loving_sdk_key', value: ''},
    {id: 'aps_appid', value: ''},
    {id: 'max_consent', value: {privacy: '', terms: ''}}
]

export const iosLangOptions = [
    {id: 'af', value: 'af'},
    {id: 'af-ZA', value: 'af-ZA'},
    {id: 'ar', value: 'ar'},
    {id: 'ar-AE', value: 'ar-AE'},
    {id: 'ar-BH', value: 'ar-BH'},
    {id: 'ar-DZ', value: 'ar-DZ'},
    {id: 'ar-EG', value: 'ar-EG'},
    {id: 'ar-IQ', value: 'ar-IQ'},
    {id: 'ar-JO', value: 'ar-JO'},
    {id: 'ar-KW', value: 'ar-KW'},
    {id: 'ar-LB', value: 'ar-LB'},
    {id: 'ar-LY', value: 'ar-LY'},
    {id: 'ar-MA', value: 'ar-MA'},
    {id: 'ar-OM', value: 'ar-OM'},
    {id: 'ar-QA', value: 'ar-QA'},
    {id: 'ar-SA', value: 'ar-SA'},
    {id: 'ar-SY', value: 'ar-SY'},
    {id: 'ar-TN', value: 'ar-TN'},
    {id: 'ar-YE', value: 'ar-YE'},
    {id: 'az', value: 'az'},
    {id: 'az-AZ', value: 'az-AZ'},
    {id: 'be', value: 'be'},
    {id: 'be-BY', value: 'be-BY'},
    {id: 'bg', value: 'bg'},
    {id: 'bg-BG', value: 'bg-BG'},
    {id: 'bg-BA', value: 'bg-BA'},
    {id: 'ca', value: 'ca'},
    {id: 'ca-ES', value: 'ca-ES'},
    {id: 'cs', value: 'cs'},
    {id: 'cs-CZ', value: 'cs-CZ'},
    {id: 'cy', value: 'cy'},
    {id: 'cy-GB', value: 'cy-GB'},
    {id: 'da', value: 'da'},
    {id: 'da-DK', value: 'da-DK'},
    {id: 'de', value: 'de'},
    {id: 'de-AT', value: 'de-AT'},
    {id: 'de-CH', value: 'de-CH'},
    {id: 'de-DE', value: 'de-DE'},
    {id: 'de-LI', value: 'de-LI'},
    {id: 'de-LU', value: 'de-LU'},
    {id: 'dv', value: 'dv'},
    {id: 'dv-MV', value: 'dv-MV'},
    {id: 'el', value: 'el'},
    {id: 'el-GR', value: 'el-GR'},
    {id: 'en', value: 'en'},
    {id: 'en-AU', value: 'en-AU'},
    {id: 'en-BZ', value: 'en-BZ'},
    {id: 'en-CA', value: 'en-CA'},
    {id: 'en-CB', value: 'en-CB'},
    {id: 'en-GB', value: 'en-GB'},
    {id: 'en-IE', value: 'en-IE'},
    {id: 'en-JM', value: 'en-JM'},
    {id: 'en-NZ', value: 'en-NZ'},
    {id: 'en-PH', value: 'en-PH'},
    {id: 'en-TT', value: 'en-TT'},
    {id: 'en-US', value: 'en-US'},
    {id: 'en-ZA', value: 'en-ZA'},
    {id: 'en-ZW', value: 'en-ZW'},
    {id: 'eo', value: 'eo'},
    {id: 'es', value: 'es'},
    {id: 'es-AR', value: 'es-AR'},
    {id: 'es-BO', value: 'es-BO'},
    {id: 'es-CL', value: 'es-CL'},
    {id: 'es-CO', value: 'es-CO'},
    {id: 'es-CR', value: 'es-CR'},
    {id: 'es-DO', value: 'es-DO'},
    {id: 'es-EC', value: 'es-EC'},
    {id: 'es-ES', value: 'es-ES'},
    {id: 'es-GT', value: 'es-GT'},
    {id: 'es-HN', value: 'es-HN'},
    {id: 'es-MX', value: 'es-MX'},
    {id: 'es-NI', value: 'es-NI'},
    {id: 'es-PA', value: 'es-PA'},
    {id: 'es-PE', value: 'es-PE'},
    {id: 'es-PR', value: 'es-PR'},
    {id: 'es-PY', value: 'es-PY'},
    {id: 'es-SV', value: 'es-SV'},
    {id: 'es-UY', value: 'es-UY'},
    {id: 'es-VE', value: 'es-VE'},
    {id: 'et', value: 'et'},
    {id: 'et-EE', value: 'et-EE'},
    {id: 'eu', value: 'eu'},
    {id: 'eu-ES', value: 'eu-ES'},
    {id: 'fa', value: 'fa'},
    {id: 'fa-IR', value: 'fa-IR'},
    {id: 'fi', value: 'fi'},
    {id: 'fi-FI', value: 'fi-FI'},
    {id: 'fo', value: 'fo'},
    {id: 'fo-FO', value: 'fo-FO'},
    {id: 'fr', value: 'fr'},
    {id: 'fr-BE', value: 'fr-BE'},
    {id: 'fr-CA', value: 'fr-CA'},
    {id: 'fr-CH', value: 'fr-CH'},
    {id: 'fr-FR', value: 'fr-FR'},
    {id: 'fr-LU', value: 'fr-LU'},
    {id: 'fr-MC', value: 'fr-MC'},
    {id: 'gl', value: 'gl'},
    {id: 'gl-ES', value: 'gl-ES'},
    {id: 'gu', value: 'gu'},
    {id: 'gu-IN', value: 'gu-IN'},
    {id: 'he', value: 'he'},
    {id: 'he-IL', value: 'he-IL'},
    {id: 'hi', value: 'hi'},
    {id: 'hi-IN', value: 'hi-IN'},
    {id: 'hr', value: 'hr'},
    {id: 'hr-BA', value: 'hr-BA'},
    {id: 'hr-HR', value: 'hr-HR'},
    {id: 'hu', value: 'hu'},
    {id: 'hu-HU', value: 'hu-HU'},
    {id: 'hy', value: 'hy'},
    {id: 'hy-AM', value: 'hy-AM'},
    {id: 'id', value: 'id'},
    {id: 'id-ID', value: 'id-ID'},
    {id: 'is', value: 'is'},
    {id: 'is-IS', value: 'is-IS'},
    {id: 'it', value: 'it'},
    {id: 'it-CH', value: 'it-CH'},
    {id: 'it-IT', value: 'it-IT'},
    {id: 'ja', value: 'ja'},
    {id: 'ja-JP', value: 'ja-JP'},
    {id: 'ka', value: 'ka'},
    {id: 'ka-GE', value: 'ka-GE'},
    {id: 'kk', value: 'kk'},
    {id: 'kk-KZ', value: 'kk-KZ'},
    {id: 'kn', value: 'kn'},
    {id: 'kn-IN', value: 'kn-IN'},
    {id: 'ko', value: 'ko'},
    {id: 'ko-KR', value: 'ko-KR'},
    {id: 'kok', value: 'kok'},
    {id: 'kok-IN', value: 'kok-IN'},
    {id: 'ky', value: 'ky'},
    {id: 'ky-KG', value: 'ky-KG'},
    {id: 'lt', value: 'lt'},
    {id: 'lt-LT', value: 'lt-LT'},
    {id: 'lv', value: 'lv'},
    {id: 'lv-LV', value: 'lv-LV'},
    {id: 'mi', value: 'mi'},
    {id: 'mi-NZ', value: 'mi-NZ'},
    {id: 'mk', value: 'mk'},
    {id: 'mk-MK', value: 'mk-MK'},
    {id: 'mn', value: 'mn'},
    {id: 'mn-MN', value: 'mn-MN'},
    {id: 'mr', value: 'mr'},
    {id: 'mr-IN', value: 'mr-IN'},
    {id: 'ms', value: 'ms'},
    {id: 'ms-BN', value: 'ms-BN'},
    {id: 'ms-MY', value: 'ms-MY'},
    {id: 'mt', value: 'mt'},
    {id: 'mt-MT', value: 'mt-MT'},
    {id: 'nb', value: 'nb'},
    {id: 'nb-NO', value: 'nb-NO'},
    {id: 'nl', value: 'nl'},
    {id: 'nl-BE', value: 'nl-BE'},
    {id: 'nl-NL', value: 'nl-NL'},
    {id: 'nn-NO', value: 'nn-NO'},
    {id: 'ns', value: 'ns'},
    {id: 'ns-ZA', value: 'ns-ZA'},
    {id: 'pa', value: 'pa'},
    {id: 'pa-IN', value: 'pa-IN'},
    {id: 'pl', value: 'pl'},
    {id: 'pl-PL', value: 'pl-PL'},
    {id: 'pt', value: 'pt'},
    {id: 'pt-BR', value: 'pt-BR'},
    {id: 'pt-PT', value: 'pt-PT'},
    {id: 'qu', value: 'qu'},
    {id: 'qu-BO', value: 'qu-BO'},
    {id: 'qu-EC', value: 'qu-EC'},
    {id: 'qu-PE', value: 'qu-PE'},
    {id: 'ro', value: 'ro'},
    {id: 'ro-RO', value: 'ro-RO'},
    {id: 'ru', value: 'ru'},
    {id: 'ru-RU', value: 'ru-RU'},
    {id: 'sa', value: 'sa'},
    {id: 'sa-IN', value: 'sa-IN'},
    {id: 'se', value: 'se'},
    {id: 'se-FI', value: 'se-FI'},
    {id: 'se-NO', value: 'se-NO'},
    {id: 'se-SE', value: 'se-SE'},
    {id: 'sk', value: 'sk'},
    {id: 'sk-SK', value: 'sk-SK'},
    {id: 'sl', value: 'sl'},
    {id: 'sl-SI', value: 'sl-SI'},
    {id: 'sq', value: 'sq'},
    {id: 'sq-AL', value: 'sq-AL'},
    {id: 'sr-BA', value: 'sr-BA'},
    {id: 'sr-SP', value: 'sr-SP'},
    {id: 'sv', value: 'sv'},
    {id: 'sv-FI', value: 'sv-FI'},
    {id: 'sv-SE', value: 'sv-SE'},
    {id: 'sw', value: 'sw'},
    {id: 'sw-KE', value: 'sw-KE'},
    {id: 'syr', value: 'syr'},
    {id: 'syr-SY', value: 'syr-SY'},
    {id: 'ta', value: 'ta'},
    {id: 'ta-IN', value: 'ta-IN'},
    {id: 'te', value: 'te'},
    {id: 'te-IN', value: 'te-IN'},
    {id: 'th', value: 'th'},
    {id: 'th-TH', value: 'th-TH'},
    {id: 'tl', value: 'tl'},
    {id: 'tl-PH', value: 'tl-PH'},
    {id: 'tn', value: 'tn'},
    {id: 'tn-ZA', value: 'tn-ZA'},
    {id: 'tr', value: 'tr'},
    {id: 'tr-TR', value: 'tr-TR'},
    {id: 'ts', value: 'ts'},
    {id: 'tt', value: 'tt'},
    {id: 'tt-RU', value: 'tt-RU'},
    {id: 'uk', value: 'uk'},
    {id: 'uk-UA', value: 'uk-UA'},
    {id: 'ur', value: 'ur'},
    {id: 'ur-UZ', value: 'ur-UZ'},
    {id: 'vi', value: 'vi'},
    {id: 'vi-VN', value: 'vi-VN'},
    {id: 'xh', value: 'xh'},
    {id: 'xh-ZA', value: 'xh-ZA'},
    {id: 'zh', value: 'zh'},
    {id: 'zh-CN', value: 'zh-CN'},
    {id: 'zh-HK', value: 'zh-HK'},
    {id: 'zh-MO', value: 'zh-MO'},
    {id: 'zh-SG', value: 'zh-SG'},
    {id: 'zh-TW', value: 'zh-TW'},
    {id: 'zu', value: 'zu'},
    {id: 'zu-ZA', value: 'zu-ZA'},
]

export const touch3dIcons = [
    {label: 'Add', value: 'UIApplicationShortcutIconTypeAdd'},
    {label: 'Alarm', value: 'UIApplicationShortcutIconTypeAlarm'},
    {label: 'Audio', value: 'UIApplicationShortcutIconTypeAudio'},
    {label: 'Bookmark', value: 'UIApplicationShortcutIconTypeBookmark'},
    {label: 'CapturePhoto', value: 'UIApplicationShortcutIconTypeCapturePhoto'},
    {label: 'CaptureVideo', value: 'UIApplicationShortcutIconTypeCaptureVideo'},
    {label: 'Cloud', value: 'UIApplicationShortcutIconTypeCloud'},
    {label: 'Confirmation', value: 'UIApplicationShortcutIconTypeConfirmation'},
    {label: 'Compose', value: 'UIApplicationShortcutIconTypeCompose'},
    {label: 'Contact', value: 'UIApplicationShortcutIconTypeContact'},
    {label: 'Date', value: 'UIApplicationShortcutIconTypeDate'},
    {label: 'Favorite', value: 'UIApplicationShortcutIconTypeFavorite'},
    {label: 'Home', value: 'UIApplicationShortcutIconTypeHome'},
    {label: 'Invitation', value: 'UIApplicationShortcutIconTypeInvitation'},
    {label: 'Location', value: 'UIApplicationShortcutIconTypeLocation'},
    {label: 'Love', value: 'UIApplicationShortcutIconTypeLove'},
    {label: 'Mail', value: 'UIApplicationShortcutIconTypeMail'},
    {label: 'MarkLocation', value: 'UIApplicationShortcutIconTypeMarkLocation'},
    {label: 'Message', value: 'UIApplicationShortcutIconTypeMessage'},
    {label: 'Pause', value: 'UIApplicationShortcutIconTypePause'},
    {label: 'Play', value: 'UIApplicationShortcutIconTypePlay'},
    {label: 'Prohibit', value: 'UIApplicationShortcutIconTypeProhibit'},
    {label: 'Search', value: 'UIApplicationShortcutIconTypeSearch'},
    {label: 'Share', value: 'UIApplicationShortcutIconTypeShare'},
    {label: 'Shuffle', value: 'UIApplicationShortcutIconTypeShuffle'},
    {label: 'Task', value: 'UIApplicationShortcutIconTypeTask'},
    {label: 'TaskCompleted', value: 'UIApplicationShortcutIconTypeTaskCompleted'},
    {label: 'Time', value: 'UIApplicationShortcutIconTypeTime'},
    {label: 'Update', value: 'UIApplicationShortcutIconTypeUpdate'}
]
