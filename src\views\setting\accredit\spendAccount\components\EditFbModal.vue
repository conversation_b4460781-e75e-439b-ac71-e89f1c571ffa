
<template>
    <!-- meta修改fb个人号 -->
    <a-modal v-model:visible="modalVisible" :width="900" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert style="margin-bottom: 16px;">如果广告账户与授权账号之间不存在关联关系，则系统不会对这些账户变更授权账号</a-alert>
        <a-table v-model:selectedKeys="selectedKeys" :columns="columns" :data="tableData" :row-selection="rowSelection" />
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" :disabled="!selectedKeys.length" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改FB个人号')
const rowSelection = {
    type: 'radio'
};
const selectedKeys = ref<any>([])
const columns = [
    {
        title: 'FB个人号',
        dataIndex: 'fbName',
    },
    {
        title: 'FB个人号ID',
        dataIndex: 'fbId',
    },
    {
        title: '用户名',
        dataIndex: 'xmpName',
    },
    {
        title: '授权时间',
        dataIndex: 'authTime',
    },
]
const tableData = ref<any>([
    {
        key: '1',
        fbName: 'John Brown',
        xmpName: '1213',
    }
])
const emits = defineEmits(['updateData']);
const openModal = async (channel?:string) => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    // button{
    //     border-radius: 4px;
    // }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>