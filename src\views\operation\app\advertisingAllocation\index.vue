<script setup lang="ts">
import {reactive, ref} from "vue";
import {useRoute} from 'vue-router';

const route = useRoute();
const columns = [
  {
    title: '变现平台',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
    fixed: 'left'
  },
  {
    title: '广告ID',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '广告名称',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '广告类型',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '次级标签',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '广告状态',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '授权CP',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '授权CP比例',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
  },
  {
    title: '操作',
    dataIndex: 'eventName',
    ellipsis: "true",
    slotName: 'bx-name',
    minWidth: 200,
    fixed: 'right'
  },
]
const data = ref()
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const scrollbar = ref(true)
const scroll = {y: 'calc(100vh - 260px)', x: 1000}

</script>

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <a-input style="background-color: #FFF;margin-right: 30px" placeholder="请输入...">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
        <a-button class="button" type="primary">
          <icon-plus style="margin-right: 3px"/>
          新增广告
        </a-button>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :data="data"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :row-selection="rowSelection"
          :scrollbar="scrollbar"
          :scroll="scroll">

      </a-table>
    </div>
  </div>
</template>

<style scoped lang="less">
</style>