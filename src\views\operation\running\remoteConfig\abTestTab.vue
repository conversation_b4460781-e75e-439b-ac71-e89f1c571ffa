

<template>
    <div class="ab-content">
        <div class="params-content">
            <div class="params-title"></div>
            <div class="config-content">
                <a-button class="button" type="primary" style="margin-right: 30px">
                    <icon-plus style="margin-right: 3px"/>
                    创建 Remote Config 实验
                </a-button>
            </div>
        </div>
        <div class="action-content">
            <div class="search">
                <a-input style="width: 300px;background-color: #FFF;" placeholder="请输入...">
                <template #prefix>
                    <icon-search/>
                </template>
                </a-input>
            </div>
            <div class="btn-content">
                <div v-for="(item,index) in btnList" :key="index" class="btn-item" :class="btnIndex === index ? 'active' : ''" @click="filterSelect(index)">
                    <span>{{ item.name }}</span>
                    <span v-if="item.value !== 'all'">（{{ item.num }}）个</span>
                </div>
            </div>
        </div>
        <div class="card-content">
            <a-table
                  :columns="columns"
                  :data="tableData"
                  :bordered="false"
                  :hoverable="true"
                  sticky-header
                  :table-layout-fixed="true"
                  :filter-icon-align-left="true"
                  :column-resizable="true"
                  >
                  <template #action="{ record }">
                      <a-dropdown @select="handleSelect">
                          <a-button class="setting">
                              <template #icon>
                                  <icon-more-vertical />
                              </template>
                          </a-button>
                          <template #content>
                            <a-doption value="edit">修改</a-doption>
                              <a-doption value="copy">复制</a-doption>
                              <a-doption value="delete">删除</a-doption>
                          </template>
                      </a-dropdown>
                  </template>
              </a-table>
        </div>
    </div>
</template>
  
  <script setup lang="ts">
  import {ref} from "vue";


  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: "true",
      slotName: 'name',
      minWidth: 300,
    },
    {
      title: '剩余时间',
      dataIndex: 'remainTime',
      ellipsis: "true",
      slotName: 'remainTime',
      minWidth: 180,
    },
    {
      title: '开始时间',
      dataIndex: 'beginTime',
      ellipsis: "true",
      slotName: 'beginTime',
      minWidth: 180,
    },
    {
      title: '上次更新时间',
      dataIndex: 'updateTime',
      ellipsis: "true",
      slotName: 'updateTime',
      minWidth: 180,
    },
    {
      title: '用户',
      dataIndex: 'uesrName',
      ellipsis: "true",
      slotName: 'uesrName',
      minWidth: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      ellipsis: "true",
      slotName: 'status',
      minWidth: 200,
    },
    {
      title: '',
      dataIndex: 'action',
      ellipsis: "true",
      slotName: 'action',
      minWidth: 140,
      align:'right',
      fixed: 'right'
    },
  ]
  const tableData=ref([
      {
          name:'1234',
          expandDate: {
              name:'1234444'
          }
      }
  ])
  const btnIndex = ref(0)
  const btnList = ref([
    {
        name:'正在运行',
        num:0,
        value:'running'
    },
    {
        name:'草稿',
        num:14,
        value:'draft'
    },
    {
        name:'已完成',
        num:0,
        value:'done'
    },
    {
        name:'全部',
        value:'all'
    }
  ])
  const filterSelect = (v) => {
    btnIndex.value = v
  }
  const handleSelect = () => {

  }
  </script>
  
<style scoped lang="less">
  .params-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  .config-content {
    display: flex;
    justify-content: flex-end;
    .button {
      border: none;
      border-radius: 5px;
    }
  }
  .action-content{
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 56px;
    padding: 0 8px 0 16px;
    border-radius: 8px 8px 0 0;
    border: 1px solid #f7f6f6;
    border-bottom: none;
  }
  .card-content{
      border: 1px solid #f7f6f6;
      border-top: none;
      width: 100%;
      overflow: hidden;
      padding: 24px;
      margin-bottom: 24px;
  }
  .btn-content{
      display: flex;
      align-items: center;
      .btn-item{
          margin-left: 8px;
          border-radius: 8px;
          cursor: pointer;
          height: 36px;
          line-height: 36px;
          padding: 0 16px;
          &:hover{
              background: #f7f6f6;
          }
      }
      .active{
          color: rgb(var(--primary-5));
          border: 1px solid rgb(var(--primary-5));
          &:hover{
              background: #fff;
          }
      }
  }
  .setting {
    width: 20px;
    height: 20px;
    background: transparent;
  }

  .setting:hover {
    background-color: var(--tant-dblue-dblue-20);
    border-radius: 3px;
  }
</style>