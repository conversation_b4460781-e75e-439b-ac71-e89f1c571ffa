<template>
    <a-modal v-model:visible="modalVisible" width="80%" title-align="start" title="冲突提示" :footer="false" @cancel="closeModal">
        <div class="content">
            <a-alert type="error" style="margin-bottom: 20px;">以下字段存在问题，请在页面修改后重试</a-alert>
            <div class="suggest">
                <div>
                    <div class="label">实验配置生效规则</div>
                    <div>1. 不同流量层原则上不允许创建包含相同配置参数 Key 的实验。请根据使用场景评估合理性，如有冲突，配置将随机生效</div>
                    <div class="label">推荐处理方式</div>
                    <div>1. 将当前实验与冲突实验建在同一个流量层上</div>
                    <div>2. 返回修改配置参数</div>
                    <div>3. 关闭冲突的实验</div>
                    <div>4. 若评估无冲突，可选择跳过（例如实验在不同互斥域、实验受众命中规则不重叠）</div>
                </div>
            </div>
            <div class="label" style="font-weight: normal;">冲突实验</div>
            <a-table :columns="columns" :data="tableData" :pagination="false" :style="{ width: '100%' }" :scroll="{ y: 300 }">
                <template #status="{ record }">
                    <a-tag :color="getStatusColor(record.status)">
                    {{ getStatusLabel(record.status) }}
                    </a-tag>
                </template>
            </a-table>
        </div>
        <div class="footer">
            <a-button class="cancel" @click="closeModal">返回修改</a-button>
            <a-button type="primary" @click="skipCheck">跳过</a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {ref} from "vue";
import dayjs from 'dayjs';

const modalVisible = ref(false)
const emits = defineEmits(['skipCheck'])
const tableData = ref<any>([])
const columns = ref<any>([
    {
        title: 'ID',
        dataIndex: 'code',
        width: 100,
        ellipsis: true,
        tooltip: true,
        slotName: 'code'
    },
    {
        title: '实验名称',
        dataIndex: 'name',
        width: 150,
        ellipsis: true,
        tooltip: true,
        slotName: 'name'
    },
    {
        title: '实验冲突Key',
        dataIndex: 'conflictParams',
        width: 150,
        ellipsis: true,
        tooltip: true,
        slotName: 'conflictParams',
        render: (value) => {
            const {record} = value;
            return record.conflictParams.map(item => item.name).join(',')
        },
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        slotName: 'status'
    },
    {
        title: '创建人',
        dataIndex: 'creator',
        width: 150,
        slotName: 'creator',
        render: (value) => {
            const {record} = value;
            return record.creator.name
        },
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        slotName: 'createTime',
        render: (record:any) => {
            return dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
        }
    },
])
const STATUS_OPTIONS = [
  { value: 0, label: '草稿' },
  { value: 1, label: '进行中' },
  { value: 2, label: '已终止' },
  { value: 3, label: '已结束' },
] as const;
const getStatusColor = (status: string) => {
  const colorMap = {
    0: 'lime',    // 预支付
    1: 'blue', // 进行中
    2: 'red',      // 已终止
    3: 'gray'     // 已结束
  };
  return colorMap[status] || 'gray';
};

const getStatusLabel = (status: string) => {
  return STATUS_OPTIONS.find(option => option.value === status)?.label || '未知状态';
};
const openModal = async (data?:any) => {
    tableData.value = data || []
    console.log(tableData.value,'ccc-data');
    modalVisible.value = true
}


const closeModal = () => {
    modalVisible.value = false
}
const skipCheck = () => {
    emits('skipCheck')
    modalVisible.value = false
}
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.suggest{
    background: linear-gradient(#f0f4fb, #fff);
    color: #10389f;
    padding: 10px 26px;
}
.label{
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
    margin: 14px 0;
}
</style>