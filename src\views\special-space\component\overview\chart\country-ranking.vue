<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {onMounted, ref, watch} from "vue";

interface Props {
  /**
   * 分组
   */
  group: string[]

  /**
   * 数据
   */
  data: number[][]

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()

const option = ref<any>();

const refresh = () => {
  const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#235894']
  const nameStyle = {}
  colorList.forEach((color, i) => {
    nameStyle[`name${i}`] = {
      color,
      padding: [0, 4, 0, 6],
      fontSize: 14,
      fontWeight: 900,
      lineHeight: 33
    }
  })
  
  // 限制只显示前10条数据
  const limitedData = props.data?.slice(0, 10) || [];
  const limitedGroup = props.group?.slice(0, 10) || [];
  
  const {chartOption} = useChartOption(() => {
    return {
      grid: {
        left: '36',
        right: '0',
        top: '16',
        bottom: '24',
      },
      tooltip: {
        trigger: 'item',
        formatter: (params) => {
          const {value, dataIndex, percent} = params;
          const diffRateArray = limitedData[dataIndex].totalDiffRate;
          const diffRate = diffRateArray && diffRateArray.length > 0 
            ? diffRateArray[diffRateArray.length - 1] 
            : 0;
          const diffRateStr = diffRate < 0 ? `下降 ${-diffRate}%` : `上涨 ${diffRate}%`
          return `${limitedGroup[dataIndex]}<br/>${props.dataName}: ${value} 占比: ${percent}%<br/>日环比: ${diffRateStr}<br/>`
        }
      },
      color: colorList,
      series: [
        {
          name: props.dataName,
          type: 'pie',
          radius: ['65%', '80%'],
          labelLine: {
            length: 10,
            length2: 40,
            lineStyle: {
              width: 2
            }
          },
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'top',
            formatter: (params) => {
              let value = Math.round(params.value);
              const {dataIndex} = params;
              const diffRateArray = limitedData[dataIndex].totalDiffRate;
              const diffRate = diffRateArray && diffRateArray.length > 0 
                ? diffRateArray[diffRateArray.length - 1] 
                : 0;
              const rate = diffRate < 0 ? `{down|↓ ${diffRate}%}` : `{up|↑${diffRate}%}`
              if (!params.value || typeof params.value !== 'number') {
                value = params.value;
              }
              if (params.value >= 1000000) {
                value = `${Math.round(params.value / 1000000)}M`;
              }
              if (params.value >= 1000) {
                value = `${Math.round(params.value / 1000)}k`;
              }
              return `{name${dataIndex}|${limitedGroup[dataIndex]}} {value|${value}} (${rate})`
            },
            backgroundColor: '#F6F8FC',
            borderColor: '#8C8D8E',
            borderWidth: 1,
            borderRadius: 4,
            padding: [0, 5],
            rich: {
              ...nameStyle,
              value: {
                color: '#6E7079',
                fontSize: 14,
                fontWeight: 900,
                lineHeight: 22,
                align: 'center'
              },
              up: {
                color: '#91cc75',
                lineHeight: 22,
                align: 'center',
                fontWeight: 900,
              },
              down: {
                color: '#ee6666',
                lineHeight: 22,
                align: 'center',
                fontWeight: 900,
              }
            }
          },
          data: limitedData?.map(item => {
            const totalArray = item.total;
            if (!totalArray || totalArray.length === 0) {
              return 0;
            }
            // 取最后一个索引的值
            return totalArray[totalArray.length - 1] || 0;
          }),
        }
      ]
    };
  });
  option.value = chartOption.value
}

watch(props, () => {
  refresh()
})

onMounted(() => {
  refresh()
})


</script>

<template>
  <Chart :option="option"/>
</template>

<style scoped lang="less">


</style>