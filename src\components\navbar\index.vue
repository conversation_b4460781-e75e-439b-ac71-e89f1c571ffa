<template>
  <div class="navbar">
    <img
        alt="logo"
        :width="80"
        :height="14"
        style="margin-left: 20px;margin-right:6px;z-index:999;cursor: pointer"
        src="/logo.png"
        @click="gotoItem(ROUTE_NAME.ANALYSE_MENU)"
    />
    <div class="center-side">
      <TopMenu/>
    </div>
    <ul class="right-side">
      <li>
        UTC：{{ UTCTime }}
      </li>
      <li>
        <a-select v-model:model-value="evnDataPlatform" style="width: 115px" @change="refreshPage">
          <a-option value="offshore-tencent">海外-腾讯云</a-option>
          <!--          <a-option value="offshore-gcp">海外-GCP</a-option>-->
          <a-option value="onshore-tencent">国内-腾讯云</a-option>
        </a-select>
      </li>
      <li>
        <a-tooltip :content="$t(ROUTE_NAME.SECURE_MENU)">
          <a-button
              class="nav-btn"
              type="outline"
              :shape="'circle'"
              @click="gotoItem(ROUTE_NAME.SECURE_MENU)"
          >
            <template #icon>
              <icon-safe/>
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip :content="$t(ROUTE_NAME.SETTING_MENU)">
          <div class="message-box-trigger">
            <a-button
                class="nav-btn"
                type="outline"
                :shape="'circle'"
                @click="gotoItem(ROUTE_NAME.SETTING_MENU)"
            >
              <icon-settings/>
            </a-button>
          </div>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip :content="$t('settings.navbar.alerts')">
          <div class="message-box-trigger">
            <a-badge :count="9" dot>
              <a-button
                  class="nav-btn"
                  type="outline"
                  :shape="'circle'"
                  @click="setPopoverVisible"
              >
                <icon-notification/>
              </a-button>
            </a-badge>
          </div>
        </a-tooltip>
        <a-dropdown trigger="click" :popup-max-height="false">
          <div ref="refBtn" class="ref-btn"></div>
          <template #content>
            <message-box/>
          </template>
        </a-dropdown>
      </li>
      <li>
        <a-dropdown trigger="click" :popup-max-height="false">
          <a-avatar
              v-if="avatar"
              :size="26"
              :style="{ marginRight: '8px', cursor: 'pointer' }"
          >
            <img alt="avatar" :src="avatar"/>
          </a-avatar>
          <a-avatar
              v-else
              :size="26"
              :style="{ marginRight: '8px', cursor: 'pointer',backgroundColor: '#1E76F0' }"
          >
            <div>{{ processedName }}</div>
          </a-avatar>
          <template #content>
            <div class="user-trigger-header">
              {{ userStore.name }}
            </div>
            <a-divider :margin="1"/>
            <a-doption>
              <a-space @click="toUser">
                <icon-user/>
                <span>
                  {{ $t('messageBox.userCenter') }}
                </span>
              </a-space>
            </a-doption>
            <a-doption class="logout-option">
              <a-space @click="handleLogout">
                <icon-export/>
                <span>
                  {{ $t('messageBox.logout') }}
                </span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import {computed, onBeforeUnmount, onMounted, ref} from 'vue';
import {useUserStore} from '@/store';
import useUser from '@/hooks/user';
import TopMenu from '@/components/top-menu/index.vue';
import router from "@/router";
import {getCurrentUTCDateTime} from "@/utils/dateUtil";
import {ROUTE_NAME} from "@/router/constants";
import MessageBox from '../message-box/index.vue';
import {useSessionStorage} from "@vueuse/core";
import useAppStore from "../../store/modules/app";
import {DEFAULT_ROUTE_NAME} from "@/router/routes";

const evnDataPlatform = useSessionStorage('env-data-platform', 'offshore-tencent');
const refBtn = ref();
const userStore = useUserStore();
const appStore = useAppStore();
const {logout} = useUser();
const UTCTime = ref<string>();
const timer = ref<any>();
const avatar = computed(() => {
  return userStore.avatar;
});
const processedName = computed(() => {
  if (!userStore.name) return ""; // 如果 name 为空，返回空字符串
  // 判断开头是汉字
  const startsWithChinese = /^[\u4e00-\u9fa5]/.test(userStore.name);

  if (startsWithChinese) {
    return userStore.name.slice(-2); // 开头是汉字，取后两位
  }
  return userStore.name.slice(0, 3);

});
const refreshPage = async () => {
  await router.push({ name: DEFAULT_ROUTE_NAME });
  userStore.clearAppData();
  window.location.reload();
};
const setPopoverVisible = () => {
  const event = new MouseEvent('click', {
    view: window,
    bubbles: true,
    cancelable: true,
  });
  refBtn.value.dispatchEvent(event);
};
const toUser = () => {
  router.push({
    name: ROUTE_NAME.USER,
  });
};
const handleLogout = () => {
  logout();
};
const gotoItem = (item: string) => {
  router.push({
    name: item,
  });
};
const refreshTime = () => {
  UTCTime.value = getCurrentUTCDateTime();
}
onMounted(() => {
  UTCTime.value = getCurrentUTCDateTime();
  timer.value = setInterval(refreshTime, 1000);
})
onBeforeUnmount(() => {
  clearInterval(timer.value);
})

</script>

<style scoped lang="less">
.navbar {
  display: flex;
  justify-content: space-between;
  height: 100%;
  align-items: center;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
}

.left-side {
  display: flex;
  flex: 1;
  align-items: center;
  padding-left: 20px;
}

.center-side {
  flex: 1;
}


.right-side {
  display: flex;
  padding-right: 20px;
  list-style: none;

  :deep(.locale-select) {
    border-radius: 20px;
  }

  li {
    display: flex;
    align-items: center;
    padding: 0 8px;
  }

  a {
    color: var(--color-text-1);
    text-decoration: none;
  }

  .nav-btn {
    border-color: rgb(var(--gray-2));
    color: rgb(var(--gray-8));
    font-size: 14px;
  }

  .trigger-btn,
  .ref-btn {
    position: absolute;
    bottom: 14px;
  }

  .trigger-btn {
    margin-left: 14px;
  }
}

.user-trigger-header {
  height: 36px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-weight: 500;
  font-size: 16px;
}

.logout-option:hover {
  background-color: var(--tant-status-danger-color-danger-fill);
  color: var(--tant-status-danger-color-danger-default);
}

.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}

.toolbox-trigger {
  max-height: 480px;
  overflow-y: scroll;
  overflow-x: hidden;
  background-color: var(--tant-bg-white-color-bg1-1);
  box-sizing: border-box;
  width: 280px;
  padding: 12px 4px 0 16px;
  border-radius: 8px;
  box-shadow: var(--tant-small-shadow-small-bottom);

  .toolbox-header {
    height: 20px;
    margin-bottom: 12px;
    padding-left: 8px;
    color: var(--tant-text-gray-color-text1-3);
    font: var(--tant-description-font-description-medium);
  }

  .toolbox-content {
    .app {
      display: flex;
      margin-bottom: 16px;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: 0.3s all;

      .app-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        min-width: 40px;
        height: 40px;
        margin-right: 12px;
        font-size: 20px;
        background-color: var(--tant-fill-color-fill1-1);
        border-radius: 4px;
      }

      .app-desc {
        .header {
          margin-bottom: 4px;
          color: var(--tant-text-gray-color-text1-2);
          font: var(--tant-header-font-header5-medium);
        }

        .desc {
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }
      }
    }
  }
}

.game-select {
  display: flex;
  justify-content: center;
  align-items: center;

  .game-name {
    margin: 0 2px 0 6px;
    font-weight: 500;
  }
}

.game-select-option {
  .game-name {
    margin: 0 0 0 6px;
  }
}

.search-input {
  width: 190px;
  margin-right: 8px;
  padding: 0 8px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-bg-white-color-bg1-1);
  border-radius: 8px;
  line-height: 32px;
  height: 32px;
}

</style>