const eventDataJson = {
    "start_time": 1725585015917,
    "end_time": 1725585046489,
    "groupsDesc": [
        {
            "name": "VIP等级",
            "format": "range"
        },
        {
            "name": "用户注册日期",
            "format": "day"
        }
    ],
    "totalNum": 1106,
    "groupNum": 605,
    "groups": [
        [
            "200~240",
            "2024-07-20"
        ],
        [
            "0~40",
            "2024-08-17"
        ]
    ],
    "x": [
        "2024-08-30",
        "2024-08-31",
        "2024-09-01",
        "2024-09-02",
        "2024-09-03",
        "2024-09-04",
        "2024-09-05"
    ],
    "xComparedList": [
        [
            "2024-08-23",
            "2024-08-24",
            "2024-08-25",
            "2024-08-26",
            "2024-08-27",
            "2024-08-28",
            "2024-08-29"
        ]
    ],
    "y": [
        {
            "indicator_code": "",
            "indicator_name": "",
            "displayName": "用户登录.总次数",
            "groupNum": 561,
            "groups": [
                [
                    "200~240",
                    "2024-07-20"
                ],
                [
                    "0~40",
                    "2024-08-17"
                ]
            ],
            "yData": [
                {
                    "group": [
                        "200~240",
                        "2024-07-20"
                    ],
                    "values": [
                        61,
                        74,
                        73,
                        63,
                        75,
                        65,
                        69
                    ],
                    "valuesCompared": [
                        [
                            15,
                            60,
                            70,
                            51,
                            45,
                            41,
                            40
                        ]
                    ],
                    "summaryValue": {
                        "mean": 68.57,
                        "max": 75,
                        "max_at": [
                            "2024-09-03"
                        ],
                        "min": 61,
                        "min_at": [
                            "2024-08-30"
                        ],
                        "sum": 480
                    },
                    "summaryValueCompared": {
                        "mean": 46,
                        "max": 70,
                        "max_at": [
                            "2024-08-25"
                        ],
                        "min": 15,
                        "min_at": [
                            "2024-08-23"
                        ],
                        "sum": 322
                    },
                    "last_data": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-05 00:00:00",
                        "end_time": "2024-09-06 00:00:00",
                        "x_value": "2024-09-05",
                        "y_value": 69
                    },
                    "huan_bi": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-04 00:00:00",
                        "end_time": "2024-09-05 00:00:00",
                        "x_value": "2024-09-04",
                        "y_value": 74
                    },
                    "tong_bi": [
                        {
                            "time_particle_size": "W1",
                            "start_time": "2024-08-29 00:00:00",
                            "end_time": "2024-08-30 00:00:00",
                            "x_value": "2024-08-29",
                            "y_value": 56
                        }
                    ]
                },
                {
                    "group": [
                        "0~40",
                        "2024-08-17"
                    ],
                    "values": [
                        0,
                        233,
                        109,
                        28,
                        7,
                        7,
                        1
                    ],
                    "valuesCompared": [
                        [
                            0,
                            57,
                            20,
                            4,
                            0,
                            0,
                            0
                        ]
                    ],
                    "summaryValue": {
                        "mean": 55,
                        "max": 233,
                        "max_at": [
                            "2024-08-31"
                        ],
                        "min": 0,
                        "min_at": [
                            "2024-08-30"
                        ],
                        "sum": 385
                    },
                    "summaryValueCompared": {
                        "mean": 11.57,
                        "max": 57,
                        "max_at": [
                            "2024-08-24"
                        ],
                        "min": 0,
                        "min_at": [
                            "2024-08-23",
                            "2024-08-27",
                            "2024-08-28",
                            "2024-08-29"
                        ],
                        "sum": 81
                    },
                    "last_data": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-05 00:00:00",
                        "end_time": "2024-09-06 00:00:00",
                        "x_value": "2024-09-05",
                        "y_value": 132
                    },
                    "huan_bi": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-04 00:00:00",
                        "end_time": "2024-09-05 00:00:00",
                        "x_value": "2024-09-04",
                        "y_value": 189
                    },
                    "tong_bi": [
                        {
                            "time_particle_size": "W1",
                            "start_time": "2024-08-29 00:00:00",
                            "end_time": "2024-08-30 00:00:00",
                            "x_value": "2024-08-29",
                            "y_value": 127
                        }
                    ]
                }
            ]
        },
        {
            "indicator_code": "",
            "indicator_name": "",
            "displayName": "商城购买.总次数",
            "groupNum": 561,
            "groups": [
                [
                    "200~240",
                    "2024-07-20"
                ],
                [
                    "0~40",
                    "2024-08-17"
                ]
            ],
            "yData": [
                {
                    "group": [
                        "200~240",
                        "2024-07-20"
                    ],
                    "values": [
                        6,
                        7,
                        7,
                        6,
                        7,
                        6,
                        6
                    ],
                    "valuesCompared": [
                        [
                            1,
                            6,
                            7,
                            5,
                            4,
                            4,
                            4
                        ]
                    ],
                    "summaryValue": {
                        "mean": 6.57,
                        "max": 7,
                        "max_at": [
                            "2024-09-03"
                        ],
                        "min": 6,
                        "min_at": [
                            "2024-08-30"
                        ],
                        "sum": 48
                    },
                    "summaryValueCompared": {
                        "mean": 4,
                        "max": 7,
                        "max_at": [
                            "2024-08-25"
                        ],
                        "min": 1,
                        "min_at": [
                            "2024-08-23"
                        ],
                        "sum": 32
                    },
                    "last_data": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-05 00:00:00",
                        "end_time": "2024-09-06 00:00:00",
                        "x_value": "2024-09-05",
                        "y_value": 13
                    },
                    "huan_bi": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-04 00:00:00",
                        "end_time": "2024-09-05 00:00:00",
                        "x_value": "2024-09-04",
                        "y_value": 18
                    },
                    "tong_bi": [
                        {
                            "time_particle_size": "W1",
                            "start_time": "2024-08-29 00:00:00",
                            "end_time": "2024-08-30 00:00:00",
                            "x_value": "2024-08-29",
                            "y_value": 12
                        }
                    ]
                },
                {
                    "group": [
                        "0~40",
                        "2024-08-17"
                    ],
                    "values": [
                        0,
                        23,
                        10,
                        28,
                        7,
                        7,
                        1
                    ],
                    "valuesCompared": [
                        [
                            0,
                            57,
                            20,
                            4,
                            0,
                            0,
                            0
                        ]
                    ],
                    "summaryValue": {
                        "mean": 5,
                        "max": 28,
                        "max_at": [
                            "2024-08-31"
                        ],
                        "min": 0,
                        "min_at": [
                            "2024-08-30"
                        ],
                        "sum": 38
                    },
                    "summaryValueCompared": {
                        "mean": 11.57,
                        "max": 57,
                        "max_at": [
                            "2024-08-24"
                        ],
                        "min": 0,
                        "min_at": [
                            "2024-08-23",
                            "2024-08-27",
                            "2024-08-28",
                            "2024-08-29"
                        ],
                        "sum": 81
                    },
                    "last_data": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-05 00:00:00",
                        "end_time": "2024-09-06 00:00:00",
                        "x_value": "2024-09-05",
                        "y_value": 132
                    },
                    "huan_bi": {
                        "time_particle_size": "D1",
                        "start_time": "2024-09-04 00:00:00",
                        "end_time": "2024-09-05 00:00:00",
                        "x_value": "2024-09-04",
                        "y_value": 189
                    },
                    "tong_bi": [
                        {
                            "time_particle_size": "W1",
                            "start_time": "2024-08-29 00:00:00",
                            "end_time": "2024-08-30 00:00:00",
                            "x_value": "2024-08-29",
                            "y_value": 187
                        }
                    ]
                }
            ]
        }
    ]
}
export default eventDataJson