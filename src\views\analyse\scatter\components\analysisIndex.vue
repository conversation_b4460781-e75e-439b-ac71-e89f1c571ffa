

<template>
    <div class="guide">
        <div class="stickyBar">
            <div class="modal" style="width: calc(100% - 42px);">
                <img src="/icon/analysis/performAnalysis.svg" alt="">
                <span class="title">对</span>
                <analysisSubjectSelect :style="{width:'120px',margin:'0 8px'}" @subject-change="subjectChange"/>
                <span class="title">进行分析</span>
            </div>
            <a-tooltip content="重置" position="top">
                <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="resetParams">
                    <template #icon>
                        <icon-loop style="font-size: 16px;"/>
                    </template>
                </a-button>
            </a-tooltip>
        </div>
        <div class="event-filter-box">
            <div class="subTitle">{{subjectData.subjectName}} 参与事件</div>
            <handleIndicatorItem :show-rename="true" :show-sub="true" :is-custom="true" :is-scatter="true" :handle-list="beginData" @indicators-change="involvedChange"/>
            <div class="subTitle">使用同时展示</div>
            <div style="padding: 6px 4px 0 24px;margin-bottom: 16px;">
                <a-switch v-model:model-value="meanwhile" @change="meanwhileSwitch"/>
            </div>
            <div v-if="meanwhile" class="subTitle">同时展示各区间 {{subjectData.subjectName}} 参与</div>
            <handleIndicatorItem v-if="meanwhile" :show-rename="true" :handle-list="meanwhileData" :show-sub="true" :show-decimal="true" :is-custom="true" @indicators-change="meanwhileChange"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref,reactive,watch} from "vue";
import handleIndicatorItem from "@/views/analyse/components/handleIndicatorItem/index.vue";
import analysisSubjectSelect from "@/views/analyse/components/analysisSubjectSelect.vue"

const props = defineProps({
    indicatorData:{
        type:Object,
        default:() => {}
    },
    secondaryData:{
        type:Object,
        default:() => {}
    }
})
const subjectData = reactive({
    subject:'',
    subjectName:''
})
const emits = defineEmits(['analysisSubjectChange','resetParams','indicatorChange','secondaryIndicatorChange'])

const subjectChange = (v) => {
    subjectData.subject = v.subject
    subjectData.subjectName = v.subjectName 
    emits('analysisSubjectChange',subjectData)
}
const beginData = ref<any>([])

const meanwhileData = ref<any>([])
const meanwhile = ref(false)

const involvedChange = (v) => {
    emits('indicatorChange',v)
}
const meanwhileChange = (v) => {
    if(meanwhile.value){
        emits('secondaryIndicatorChange',v)
    }
}
const meanwhileSwitch = (v) => {
    if(!v){
        emits('secondaryIndicatorChange',[])
    }
}
const resetParams = () => {
    emits('resetParams')
}
watch(() => props.indicatorData,(v) => {
    if(v && Object.keys(v).length > 0){
        beginData.value = [v]
    }
},{immediate:true,deep:true})
watch(() => props.secondaryData,(v) => {
    if(v && Object.keys(v).length > 0){
        meanwhile.value = true
        meanwhileData.value = [v]
    }else{
        meanwhile.value = false
        meanwhileData.value = []
    }
},{immediate:true,deep:true})
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .title{
                font-weight: 600;
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .subTitle{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
        }
        :deep(.filter-row-eventRow){
            display: flex;
        }
        :deep(.action-right){
            position: static !important;
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}

</style>