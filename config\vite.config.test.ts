import {mergeConfig} from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configImageminPlugin from './plugin/imagemin';

export default mergeConfig(
  {
    mode: 'production',
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 5173,      // 指定端口
      strictPort: false, // 如果端口被占用，自动尝试下一个可用端口
    },
    plugins: [
      configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configImageminPlugin(),
    ],
    define: {
      'process.env': {
        "BASE_API_URL": "http://***********:8888",
        "BASE_WS_URL": "ws://***********:8888",
        "BASE_PACK_URL": "ws://***********:8810",
      },
      'process.env_offshore_tencent': {
        "BASE_API_URL": "http://***********:8888",
        "BASE_WS_URL": "ws://***********:8888",
        "BASE_PACK_URL": "ws://***********:8810",
      },
      'process.env_offshore_gcp': {
        "BASE_API_URL": "http://***********:8888",
        "BASE_WS_URL": "ws://***********:8888",
        "BASE_PACK_URL": "ws://***********:8810",
      },
      'process.env_onshore_tencent': {
        "BASE_API_URL": "http://***********:8888",
        "BASE_WS_URL": "ws://***********:8888",
        "BASE_PACK_URL": "ws://***********:8810",
      }
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            arco: ['@arco-design/web-vue'],
            chart: ['echarts', 'vue-echarts'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
  },
  baseConfig
);
