<script setup lang="ts">
import {defineEmits, reactive, ref} from 'vue';
import {Message} from "@arco-design/web-vue";
import useClipboard from 'vue-clipboard3'
import {getAnalyseColumns, getAnalyseEvent, getAnalyseEventList, getAnalyseList} from "@/api/analyse/api";
import {cloneDeep} from "lodash";


const selectedKeys = ref([]);
const selectedKeys2 = ref([]);
const showEvent = ref<boolean>(false)
const showEventTable = ref<boolean>(false)
const { toClipboard } = useClipboard()
const showSearch = ref<boolean>(false)
const showSearch2 = ref<boolean>(false)
const showSearch3 = ref<boolean>(false)
const showSearch4 = ref<boolean>(false)
const isIconVisible = ref<Array<boolean>>(Array(0)); // Initialize empty array to store visibility states
const isIconVisible2 = ref<Array<boolean>>(Array(0)); // Initialize empty array to store visibility states
const items = ref([]);
const itemsFilter = ref([]);
const eventData = ref([]);
const eventDataFilter = ref([]);
const eventDataItem = ref([]);
const eventOneValue = ref()
const emit = defineEmits<{
  (event: 'column', data: any): void;
}>();




const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const pagination = {pageSize: 5}

const rowSelection2 = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const pagination2 = {pageSize: 5}

const Tabledata = ref([]);
const TabledataFilter = ref([]);

const copySelected = async () => {
  try {
    await toClipboard(JSON.stringify(selectedKeys.value.join(',')))
    Message.success('复制成功')
    selectedKeys.value = []
  } catch (e) {
    Message.info('复制失败')
  }
}
const copySelected2 = async () => {
  try {
    await toClipboard(JSON.stringify(selectedKeys2.value.join(',')))
    Message.success('复制成功')
    selectedKeys2.value = []
  } catch (e) {
    Message.info('复制失败')
  }
}
const copy= async (value:any) => {
  try {
    await toClipboard(value)
    Message.success('复制成功')
  } catch (e) {
    Message.info('复制失败')
  }
}

const eventTypeName = (type:string) => {
  switch (type) {
    case 'eventTable':
      return '事件表'
    case 'userTable':
      return '用户表'
    case 'tagClusterTable':
      return '标签分群表'
    case 'historyTagTable':
      return '历史标签表'
    case 'eventPropDimTable':
      return '事件属性维度表'
    case 'userPropDimTable':
      return '用户属性维度表'
    case 'projectTempTable':
      return '项目临时表'
    default :
      return type
  }
}

const AnalyseList =  () => {
   getAnalyseList().then((res) =>{
     items.value = res
     itemsFilter.value = cloneDeep(items.value)
  })
}
AnalyseList()

const searchItems = (e:any) => {
  const value = e.target.value
  if (value === '') {
     itemsFilter.value = cloneDeep(items.value)
  } else {
    itemsFilter.value = items.value.filter((item: any) => item.tableName.includes(value))
  }
}

const selectLable = async (obj:any) => {
  await getAnalyseColumns(obj.catalog,obj.schema,obj.tableName).then((res) =>{
    Tabledata.value=res
    TabledataFilter.value=res
  })
  if (items.value[value].tableType == 'eventTable'){
    showEvent.value = true
    const data = {
      types:0,
      inApp:1
    }
    await getAnalyseEventList(data).then((res) =>{
      eventData.value =res
      eventDataFilter.value =res
    })
  }else {
    showEvent.value = false
  }
  showEventTable.value = false
}

const searchEventData = (e:any) => {
  const value = e.target.value
  if (value === '') {
     eventDataFilter.value = cloneDeep(eventData.value)
  } else {
    eventDataFilter.value = cloneDeep(eventData.value).filter((item: any) => item.eventName.includes(value))
  }
}

const selectEvent = async (value:any) => {
  const data = {
      event:value.eventCode,
      inApp:1
  }
  await getAnalyseEvent(data).then((res) =>{
    eventDataItem.value =res
  })

  // const res = {
  //   "code": 73,
  //   "msg": "pariatur officia ut",
  //   "data": [
  //     {
  //       "columnType": "varchar",
  //       "columnDesc": "事件属性描述",
  //       "columnName": "事件属性"
  //     }
  //   ],
  //   "timestamp": 641178336353
  // }
  // eventDataItem.value = res.data
  eventOneValue.value = value.eventName
  showEvent.value = false
  showEventTable.value =true

}

const tableStructure = async (value:any) => {
  await getAnalyseColumns(items.value[value].catalog,items.value[value].schema,items.value[value].tableName).then((res) =>{
    Tabledata.value=res
    TabledataFilter.value=res
  })
  const name = Tabledata.value.map((item:any) => item.column)
  emit('column',[name,items.value[value].tableName])
}

const searchTable = (e:any) => {
  const value = e.target.value
  if (value === '') {
    TabledataFilter.value = Tabledata.value
  } else {
    TabledataFilter.value = Tabledata.value.filter((item: any) => item.column.includes(value))
  }
}


const eventStructure = async (value:any) => {

  // const res = {
  //   "code": 73,
  //   "msg": "pariatur officia ut",
  //   "data": [
  //     {
  //       "columnType": "varchar",
  //       "columnDesc": "事件属性描述",
  //       "columnName": "事件属性"
  //     }
  //   ],
  //   "timestamp": 641178336353
  // }
  // eventDataItem.value = res.data
  await selectEvent(value)
  const name = eventDataItem.value.map((item:any) => item.code)
  emit('column',[name,value])

}
const showIcon = (index: number, value: boolean) => {
  isIconVisible.value[index] = value;
};
const showIcon2 = (index: number, value: boolean) => {
  isIconVisible2.value[index] = value;
};
isIconVisible.value = Array(items.value.length).fill(false);
isIconVisible2.value = Array(items.value.length).fill(false);

const vFocus = {
  mounted: (el) => el.focus()
}
const noSearch = () => {
  showSearch.value = false
  itemsFilter.value = cloneDeep(items.value)
}
const noEventSearch = () => {
  showSearch3.value = false
  eventDataFilter.value = cloneDeep(eventData.value)
}
</script>

<template>
  <div style="display: flex;">
    <div style="flex: 2;margin-right: 10px;border-right: 1px solid #e6e6e6;">
      <div v-if="!showSearch" style="display: flex;justify-content: space-between;">
        <div>
          <span style="font-weight: 500;margin-left: 25px;font-size: 15px;padding-bottom: 25px;line-height: 30px;">{{itemsFilter[0]?.catalog}} </span>
          <span>({{itemsFilter?.length}})</span>
        </div>
        <div style="padding-right: 30px;cursor: pointer;">
          <icon-search @click="showSearch = true"/>
        </div>
      </div>
      <div v-if="showSearch" style="display: flex;justify-content: space-between;">
        <div>
          <icon-search style="margin-left: 25px;"/>
          <input v-focus style="all: unset;margin-left: 10px;padding-bottom: 10px;" placeholder="请搜索" @input="searchItems">
        </div>
        <div style="padding-right: 30px;cursor: pointer;">
          <icon-close  @click="noSearch"/>
        </div>
      </div>
      <div style="overflow-y: scroll;overflow-x: hidden; max-height: 501px;">
        <a-space
          v-for="(item, index) in itemsFilter"
          :key="index"
          align="center"
          class="itemList"
          style="position: relative;"
          @mouseover="showIcon(index, true)"
          @mouseleave="showIcon(index, false)"
          @click="selectLable(item)">
          <span style="color: #333333;margin-left: 10px;">{{item.tableName}}</span>
          <span style="color: #7e7f80;font-size: 12px;">&nbsp;&nbsp; {{eventTypeName(item.tableType) }} </span>
          <div
            v-show="isIconVisible[index]"
            style="position: absolute;top: 10px;right: 30px;background-color: #e6e6e6;">
            <a-tooltip content="表结构" position="top">
              <icon-branch :style="{fontSize:'16px'}" @click="tableStructure(index)"/>
            </a-tooltip>
            <a-tooltip content="复制表名" position="top">
              <icon-copy :style="{fontSize:'16px'}" @click="copy(item.tableName)" />
            </a-tooltip>
          </div>
        </a-space>
      </div>
    </div>

    <div v-if="showEvent" style="flex: 3;margin-right: 10px;">
      <div v-if="!showSearch3" style="display: flex;justify-content: space-between;">
        <div>
             <span style="font-weight: 500;margin-left: 25px;font-size: 15px;padding-bottom: 25px;line-height: 30px;">
             <img src="/icon/eventAttr.svg" style="width: 14px;height: 14px;" alt="">&nbsp;  事件列表 </span><br>
        </div>
        <div style="padding-right: 30px;cursor: pointer;">
          <icon-search @click="showSearch3 = true"/>
        </div>
      </div>
      <div v-if="!showSearch3" style="" class="eventTitle">
        <span style="margin-left: 30px;padding: 9px 16px;">事件名</span>
        <span style="margin-right: 40%;padding: 9px 16px;">显示名</span>
      </div>
      <div v-if="showSearch3" style="display: flex;justify-content: space-between;">
        <div>
          <icon-search style="margin-left: 25px;"/>
          <input style="all: unset;margin-left: 10px;padding-bottom: 10px;" placeholder="请搜索" @input="searchEventData">
        </div>
        <div style="padding-right: 30px;cursor: pointer;">
          <icon-close  @click="noEventSearch"/>
        </div>
      </div>
      <div style="overflow-y: scroll;overflow-x: hidden; max-height: 501px;">
        <a-space
          v-for="(item, index) in eventDataFilter" :key="index" align="center" class="itemList"
           style="position: relative;width: 100%;"
          @mouseover="showIcon2(index, true)"
          @mouseleave="showIcon2(index, false)"
          @click="selectEvent(item)">
              <div>
                <span style="color: #333333;margin-left: 10px;">{{item.eventName}}</span>
              </div>
              <div> 
                <span style="color: #7e7f80;font-size: 12px;">{{item.eventDisplayName }}</span>
              </div>
              <div
                v-show="isIconVisible2[index]"
                style="position: absolute;top: 10px;right: 30px;background-color: #e6e6e6;">
                <a-tooltip content="表结构" position="top">
                  <icon-branch :style="{fontSize:'16px'}" @click="eventStructure(eventData[index].eventName)"/>
                </a-tooltip>
                <a-tooltip content="复制表名" position="top">
                  <icon-copy :style="{fontSize:'16px'}" @click="copy(eventData[index].eventName)" />
                </a-tooltip>
              </div>
        </a-space>
      </div>
    </div>

    <div v-if="showEventTable" style="flex: 3;margin-left: 10px;">
      <div>
        <div v-if="!showSearch4" style="display: flex;justify-content: space-between;">
          <div>
            <span style="font-weight: 500;margin-left: 25px;font-size: 15px;padding-bottom: 25px;line-height: 30px;">
             <icon-nav /> {{eventOneValue}} </span>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <div v-if="selectedKeys2.length !== 0" style="display: initial;">
              已选择{{selectedKeys2.length}}项
              <span style="color: #1e76f0;cursor: pointer;" @click="copySelected2">
          <icon-copy :style="{fontSize:'18px'}" style="margin-left: 10px;" />批量复制</span>
            </div>
          </div>
          <div style="padding-right: 30px;cursor: pointer;">
            <icon-search @click="showSearch4 = true"/>
          </div>

        </div>
        <div v-if="showSearch4" style="display: flex;justify-content: flex-end;">
          <div >
            <div style="width: 200px;overflow: hidden;">
              <icon-search />
              <input style="all: unset;margin-left: 10px;padding-bottom: 10px;" placeholder="请搜索">
            </div>
          </div>
          <div style="padding-right: 30px;cursor: pointer;">
            <icon-close  @click="showSearch4 = false"/>
          </div>
        </div>
        <div style="margin-left: 10px;margin-right: 10px;margin-bottom: 20px;">
          <a-table 
            v-model:selectedKeys="selectedKeys2"
            row-key="columnName" 
            :data="eventDataItem"
            :row-selection="rowSelection2"
            :pagination="pagination2">
            <template #columns >
              <a-table-column title="属性名" data-index="code"></a-table-column>
<!--              <a-table-column title="类型" data-index="type" ></a-table-column>-->
              <a-table-column title="显示名" data-index="name"></a-table-column>
              <a-table-column title="">
                <template #cell="{ record }">
                  <a-tooltip content="复制字段名" position="top">
                    <icon-copy :style="{fontSize:'16px'}" style="margin-left: 10px;cursor: pointer;" @click="copy(record.columnName)"/>
                  </a-tooltip>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <div style="flex: 3;margin-left: 10px;border-left: 1px solid #e6e6e6;">
      <div>
        <div v-if="!showSearch2" style="display: flex;justify-content: space-between;">
          <div>
            <span style="font-weight: 500;margin-left: 25px;font-size: 15px;padding-bottom: 25px;line-height: 30px;">
             <icon-nav /> 字段列表 </span>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <div v-if="selectedKeys.length !== 0" style="display: initial;">
              已选择{{selectedKeys.length}}项
              <span style="color: #1e76f0;cursor: pointer;" @click="copySelected">
          <icon-copy :style="{fontSize:'18px'}" style="margin-left: 10px;" />批量复制</span>
            </div>
          </div>
          <div style="padding-right: 30px;cursor: pointer;">
            <icon-search @click="showSearch2 = true"/>
          </div>

        </div>
        <div v-if="showSearch2" style="display: flex;justify-content: flex-end;">
          <div >
            <div style="width: 200px;overflow: hidden;">
              <icon-search />
              <input style="all: unset;margin-left: 10px;padding-bottom: 10px;" @input="searchTable" placeholder="请搜索">
            </div>
          </div>
          <div style="padding-right: 30px;cursor: pointer;">
            <icon-close  @click="showSearch2 = false"/>
          </div>
        </div>
        <div style="margin-left: 10px;margin-right: 10px;margin-bottom: 20px;">
          <a-table row-key="column"  :data="TabledataFilter" :row-selection="rowSelection"
                   v-model:selectedKeys="selectedKeys" :pagination="pagination" >
            <template #columns>
              <a-table-column title="字段名" data-index="column"></a-table-column>
              <a-table-column title="类型" data-index="type"></a-table-column>
              <a-table-column title="显示名" data-index="comment"></a-table-column>
              <a-table-column title="">
                <template #cell="{ record }">
                  <a-tooltip content="复制字段名" position="top">
                    <icon-copy :style="{fontSize:'16px'}" style="margin-left: 10px;cursor: pointer;" @click="copy(record.column)"/>
                  </a-tooltip>
                </template>
              </a-table-column>
            </template>
          </a-table>

        </div>
    </div>
    </div>

  </div>

</template>

<style scoped lang="less">
.cursor{
  cursor: pointer;
}
.itemList{
  margin: 0 20px 0 20px;
  cursor: pointer;
  width: 100%;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}
.itemList:hover{
  background-color: #e6e6e6;
}

.eventTitle{
  display: flex;
  justify-content: space-between;
  background-color: #f9f9fb;
  font-weight: 500;
  border-bottom: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
}
</style>