<template>
  <div class="guide">
    <div class="stickyBar">
      <div class="modal" style="width: calc(100% - 42px);">
        <img src="/icon/analysis/performAnalysis.svg" alt="">
        <span class="title">分析指标</span>
        <div class="model-btn">
          <a-button @click="addAnalysisIndex">
            <template #icon>
              <icon-plus class="nav-icon"/>
            </template>
          </a-button>
        </div>
      </div>
      <a-tooltip content="添加公式" position="top">
        <a-button style="width: 36px;height: 40px;" class="btn-bg">
          <template #icon>
            <icon-formula/>
          </template>
        </a-button>
      </a-tooltip>
    </div>
    <div class="event-filter-box">
      <draggable :list="eventListData" handle=".hover-drag">
        <template #item="{ element,index }">
          <div class="action-row">
            <div class="filter-row-eventRow">
              <div class="action-left">
                <i class="drag-index">{{ index + 1 }}</i>
                <icon-drag-dot-vertical class="hover-drag"/>
                <div class="row-content">
                  <div class="rename">
                    <a-tooltip content="点击修改指标名称" position="top">
                      <a-dropdown style="margin-right: 60px" @select="handleAddUpSelect(index,$event)">
                        <div class="filter-btn">
                          <span class="filter-label">{{ element?.isAddUp ? '累加' : '当天' }}</span>
                        </div>
                        <template #content>
                          <a-doption :value="false">当天</a-doption>
                          <a-doption :value="true">累加</a-doption>
                        </template>
                      </a-dropdown>
                      <a-input v-model="element.displayName"  :style="!element.isBasic ? {minWidth: '80px',maxWidth: 'calc(100% - 190px)'} :  {minWidth: '80px',maxWidth: 'calc(100% - 100px)'}"/>
                      <a-dropdown v-if="!element.isBasic" style="margin-right: 60px" @select="handleUnitSelect(index,$event)">
                        <div class="filter-btn">
                          <span class="filter-label">{{ element.unitName }}</span>
                        </div>
                        <template #content>
                          <a-doption :value="{obj:{type:'default',decimalNum:2,thousandSep: 1},name:'2位小数'}">2位小数</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:3,thousandSep: 1},name:'3位小数'}">3位小数</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:4,thousandSep: 1},name:'4位小数'}">4位小数</a-doption>
                          <a-doption :value="{obj:{type:'percent',decimalNum:2,thousandSep: 1},name:'百分比'}">百分比</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:0,thousandSep: 1},name:'取整'}">取整</a-doption>
                        </template>
                      </a-dropdown>
                    </a-tooltip>
                  </div>
                  <div v-if="element.isBasic">
                    <div v-for="(event,eventIndex) in element.eventList" :key="eventIndex" class="event-element">
                      <!-- <panelSelect :panel-data="event" @analysis-index-change="panelSelectChange(index,eventIndex,$event)"/> -->
                      <EventIndicatorSelect :panel-data="event" @analysis-index-change="panelSelectChange(index,eventIndex,$event)"/>
                      <span v-if="event.type == 'event'" class="row-word">的</span>
                      <subSelect v-if="event.type == 'event'" :panel-data="event" @sub-change="subChange(index,eventIndex,$event)"/>
                    </div>
                  </div>
                  <div v-if="!element.isBasic">
                    <customEventSelect :list="element.customList" :formula="element.formula" @custom-filters-change="customFiltersChange(index,$event)"
                                       @formula-value-change="formulaValueChange(index,$event)"/>
                  </div>
                </div>
              </div>
              <div class="action-right">
                <a-space align="center">
                  <a-tooltip content="添加筛选" position="top">
                    <a-button class="btn-bg btn-26" @click="add(index)">
                      <template #icon>
                        <icon-filter/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip v-if="element.isBasic" content="切换自定义公式" position="top">
                    <a-button class="btn-bg btn-26" @click="custom(index)">
                      <template #icon>
                        <icon-formula/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip v-if="!element.isBasic" content="返回指标选择" position="top">
                    <a-button class="btn-bg btn-26" @click="switchEvent(index)">
                      <template #icon>
                        <icon-formula/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="复制" position="top">
                    <a-button class="btn-bg btn-26" @click="copyItem(index)">
                      <template #icon>
                        <icon-copy/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-dropdown trigger="hover" @select="handleSelect(index,$event)">
                    <a-button class="btn-bg btn-26">
                      <template #icon>
                        <icon-more-vertical/>
                      </template>
                    </a-button>
                    <template #content>
                      <a-doption value="create">
                        <template #icon>
                          <icon-save/>
                        </template>
                        <template #default>创建指标</template>
                      </a-doption>
                      <a-doption v-if="eventListData.length>1" value="delete">
                        <template #icon>
                          <icon-close-circle/>
                        </template>
                        <template #default>删除</template>
                      </a-doption>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </div>
            <eventQueryFilter :ref="el => queryFilterRefs[index] = el" :filter="element.filter" :show-detail-filter="true" :code-list="!element.isBasic ? element.customList : element.eventList"
                              @query-filters-change="queryFiltersChange(index,$event)"/>
            <div v-if="element.filter&&element.filter.filters&&element.filter.filters.length" class="row-foot">
              <div class="ta-filter-button" @click="add(index)">
                <icon-plus class="action"/>
                <span class="label">筛选条件</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
  <!-- 创建指标 -->
  <a-modal v-model:visible="createVisible" width="520px" @cancel="handleCreateCancel">
    <template #title>
      <div class="modal-title">创建指标</div>
    </template>
    <a-form ref="createFormRef" :model="form" :rules="rules" layout="vertical">
      <a-form-item field="displayName" label="显示名">
        <a-input v-model="form.displayName" placeholder="80字以内" :max-length="80"/>
      </a-form-item>
      <a-form-item field="name" label="指标名">
        <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
      </a-form-item>
      <a-form-item field="description" label="备注">
        <a-textarea v-model="form.description" placeholder="请输入" :max-length="200" allow-clear show-word-limit style="height: 100px;"/>
      </a-form-item>
    </a-form>
    <div class="metric">
      <div class="metricHead">
        <div class="metricHeadTitleContainer">
          <div class="metricHeadTitle">指标口径</div>
        </div>
        <div v-if="!formData.isBasic" class="formulaType">展示格式：{{ FormatDisplayType(formData.displayType) }}</div>
      </div>
      <div class="indicator-content">
        <div v-for="(item,index) in formData.eventList" :key="index" class="event-item">
                      <span style="line-height: 24px;">
                          <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;margin-right: 4px;">
                          <span class="event-label">{{ item.eventDisplayName }}</span>
                      </span>
          <eventScreenPopup v-if="!formData.isBasic" :read-only="true" :info="item" :code-list="formData.eventList"/>
          <span style="margin: 0 4px;">的</span>
          <span class="event-label">{{ item.eventAttrName }}</span>
        </div>
        <eventQueryFilter v-if="createVisible" :filter="formData.filter" :disabled="true" :show-detail-filter="true" :code-list="formData.eventList"/>
      </div>
    </div>
    <template #footer>
      <div class="boe-foot">
        <a-button class="cancel" @click.stop="handleCreateCancel">取消</a-button>
        <a-button type="primary" @click="saveIndex">保存</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import {Indicator, IndicatorEvent} from "@/api/analyse/type";
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import subSelect from "@/views/analyse/components/SubSelect.vue"
import customEventSelect from "@/views/analyse/components/CustomEventSelect.vue";
import {analyseStore} from '@/store';
import {cloneDeep, omit} from "lodash";
import {saveIndicatorData} from "@/api/setting/api"
import {Message} from '@arco-design/web-vue'
import draggable from 'vuedraggable'
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import eventScreenPopup from "@/views/analyse/components/EventScreenPopup.vue"

const props = defineProps({
  analysisIndexData: {
    type: Array,
    default: () => []
  },
  eventLists: {
    type: Array,
    default: () => []
  },
})
const analyseData = analyseStore();

// const eventBus = useEventBus('eventList');
const emits = defineEmits(['indicatorsChange'])

const indicator = ref<Indicator[]>([])

const eventListData = ref<any>([])

const evtLists = ref<any>([])
// 监听传入事件列表
watch(() => props.eventLists, () => {
  evtLists.value = cloneDeep(props.eventLists)
}, {immediate: true, deep: true})
watch(() => props.analysisIndexData, () => {
  eventListData.value = cloneDeep(props.analysisIndexData)
}, {immediate: true, deep: true})

const handleUnitSelect = (index: number, v) => {
  eventListData.value[index].unitName = v.name
  eventListData.value[index].displayType = v.obj
}
const handleAddUpSelect = (index: number, v) => {
  eventListData.value[index].isAddUp = v
}
const panelSelectChange = (index: number, eventIndex: number, e: any) => {
  const filter = {
    ...eventListData.value[index].eventList[eventIndex],
    ...e
  }
  const fieldsToRemove = e.type === 'indicator'
    ? ['eventCode', 'eventName', 'eventDisplayName']
    : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

  const cleanedFilter = omit(filter, fieldsToRemove);
  eventListData.value[index].eventList[eventIndex] = cleanedFilter;
  eventListData.value[index].name = e.eventName || e.indicatorName
  eventListData.value[index].displayName = `${e.eventName || e.indicatorName}${filter.type === 'event' ? (filter.eventAttrName ? `.${filter.eventAttrName}` : '') + (filter.statisticalName ? `.${filter.statisticalName}` : '') : ''}`
}
const customFiltersChange = (index: number, e: any) => {
  eventListData.value[index].customList = e
}
const formulaValueChange = (index: number, e: any) => {
  eventListData.value[index].formula = e
}
const showName = ref('')
const subChange = (index: number, eventIndex: number, e: any) => {
  const {eventAttrName, eventAttrCode, eventAttrDisplayName, statisticalName, statisticalType, subjectName, subjectCode, objectType} = e
  const filter = {...eventListData.value[index].eventList[eventIndex]};
  filter.eventAttrName = eventAttrName;
  filter.eventAttrCode = eventAttrCode;
  filter.eventAttrDisplayName = eventAttrDisplayName
  if (statisticalName) {
    filter.statisticalName = statisticalName;
  } else {
    delete filter?.statisticalName;
  }
  if (statisticalType) {
    filter.statisticalType = statisticalType;
  } else {
    delete filter?.statisticalType;
  }
  if (subjectName) {
    filter.subjectName = subjectName;
  } else {
    delete filter?.subjectName;
  }
  if (subjectCode) {
    filter.subjectCode = subjectCode;
  } else {
    delete filter?.subjectCode;
  }
  filter.objectType = objectType;
  eventListData.value[index].eventList[eventIndex] = filter;
  if (filter.eventAttrName === '触发用户数' && subjectName) {
    showName.value = `触发${subjectName}数`
  } else if (filter.eventAttrName === '人均次数' && subjectName) {
    showName.value = `${subjectName}均次数`
  } else {
    showName.value = filter.eventAttrDisplayName
  }
  eventListData.value[index].displayName = `${filter.eventDisplayName}${filter.type === 'event' ? (showName.value ? `.${showName.value}` : '') + (statisticalName ? `.${statisticalName}` : '') : ''}`

}
const queryFiltersChange = (index: number, v) => {
  eventListData.value[index].filter = v;
}
// 创建事件项的工具函数
const createEventItem = (eventData) => ({
  eventName: eventData.name,
  eventCode: eventData.code,
  eventDisplayName: eventData.displayName,
  type: eventData.objectType,
  eventAttrCode: '',
  eventAttrName: eventData.objectType === 'event' ? '总次数' : '',
  filter: {}
});
// 添加事件指标
const addAnalysisIndex = () => {
  const firstEvent = evtLists.value[0];
  const eventItem = createEventItem(firstEvent);
  eventListData.value.push({
    name: firstEvent.name,
    type: 'event',
    isBasic: true,
    displayType: {},
    displayName: firstEvent.objectType === 'event' 
      ? `${firstEvent.name}-${firstEvent.displayName}.总次数` 
      : `${firstEvent.name}-${firstEvent.displayName}`,
    unitName: '',
    eventList: [eventItem],
    customList: [eventItem],
    filter: {}
  });
  // eventBus.emit(eventListData.value);
}
// 添加并列条件
const queryFilterRefs = ref<any>([])
const add = (index1: number) => {
  queryFilterRefs.value[index1].add()
}
// 切换自定义公式
const custom = (index: number) => {
  eventListData.value[index].isBasic = false
  eventListData.value[index].displayName = '自定义指标'
  if (!eventListData.value[index].displayType.type) {
    eventListData.value[index].displayType = {
      type: 'default',
      decimalNum: 2,
      thousandSep: 1
    }
  }
  eventListData.value[index].unitName = eventListData.value[index].unitName ? eventListData.value[index].unitName : '2位小数'

}
// 返回指标选择
const switchEvent = (index: number) => {
  eventListData.value[index].isBasic = true
  eventListData.value[index].displayType = {}
  const filter = eventListData.value[index].eventList[0]
  eventListData.value[index].displayName = `${filter.eventDisplayName + (filter.eventAttrName ? `.${filter.eventAttrName}` : '') + (filter.statisticalName ? `.${filter.statisticalName}` : '')}`
}
// 复制
const copyItem = (index: number) => {
  const newItem = cloneDeep(eventListData.value[index]);
  eventListData.value.splice(index + 1, 0, newItem);
}

watch(eventListData.value, (newValue, oldValue) => {
  if (newValue) {
    indicator.value = newValue.map(item => {
      return {
        displayName: item.displayName,
        displayType: item.displayType,
        unitName: item.unitName,
        name: item.name,
        type: item.type,
        isBasic: item.isBasic,
        formula: !item.isBasic ? item.formula : '',
        eventList: !item.isBasic ? item.customList : item.eventList,
        filter: item.filter,
        isAddUp: item.isAddUp || false
      }
    })
    emits('indicatorsChange', indicator.value)
    // eventBus.emit(newValue);
  }
}, {immediate: true, deep: true})

// 创建指标
const createVisible = ref(false)
const createFormRef = ref(null)
const form = reactive({
  name: '',
  displayName: '',
  description: '',
})
const rules = {
  name: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入指标名')
          }
          if (value && value.length > 80) {
            cb('指标名不能超过 80 个字符');
          }
          if (!(/^[a-z][a-z0-9_]{0,79}$/).test(value)) {
            cb('小写字母开头，可含小写字母/数字/下划线，不超过 80 个字符')
          }
          resolve(value)
        })
      }
    },
  ],
  displayName: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入显示名')
          }
          if (value && value.length > 80) {
            cb('显示名不能超过 80 个字符');
          }
          resolve(value);
        });
      }
    },
  ]
}
// 格式化displayType
const FormatDisplayType = (v) => {
  if (v) {
    const formatData = [
      {label: '2位小数', value: 'default2'},
      {label: '3位小数', value: 'default3'},
      {label: '4位小数', value: 'default4'},
      {label: '百分比', value: 'percent'},
      {label: '取整', value: 'default0'},
    ]
    const labels = {
      'default2': {type: 'default', decimalNum: 2, thousandSep: 1},
      'default3': {type: 'default', decimalNum: 3, thousandSep: 1},
      'default4': {type: 'default', decimalNum: 4, thousandSep: 1},
      'percent': {type: 'percent', decimalNum: 2, thousandSep: 1},
      'default0': {type: 'default', decimalNum: 0, thousandSep: 1},
    };
    const matchingValue = Object.keys(labels).find(key => {
      const label = labels[key];
      return label.type === v.type && label.decimalNum === v.decimalNum && label.thousandSep === v.thousandSep;
    });
    if (matchingValue) {
      const format = formatData.find(item => item.value === matchingValue);
      return format ? format.label : null;
    }
    return '2位小数';
  }

}
// 创建指标弹窗取消
const handleCreateCancel = () => {
  createVisible.value = false
}

const formData = ref({
  displayName: '',
  displayType: {},
  description: '',
  name: '',
  type: '',
  eventList: [] as IndicatorEvent[],
  filter: {},
  formula: '',
  isBasic: true
})
// 保存指标
const saveIndex = () => {
  if (createFormRef.value) {
    createFormRef.value.validate((errors: any) => {
      if (!errors) {
        const params = {
          displayName: form.displayName,
          displayType: formData.value.displayType,
          description: form.description,
          name: form.name,
          type: formData.value.type,
          formula: formData.value.formula,
          eventList: formData.value.eventList,
          filter: formData.value.filter,
        }
        saveIndicatorData(params).then(res => {
          if (res) {
            Message.success('创建成功！')
          } else {
            Message.error('创建失败')
          }
        })
        createVisible.value = false
      }
    });
  }
}
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  const firstList = params.filter?.filters || [];
  const secondList = params.eventList.filter(event => event.filter?.filters?.length > 0)
      .map(event => event.filter.filters)
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList);
};
const handleSelect = (index: number, v) => {
  if (v === 'delete') {
    eventListData.value.splice(index, 1)
    queryFilterRefs.value[index].deleteAll()
  }
  if (v === 'create') {
    if (indicator.value.length) {
      formData.value = indicator.value[index]
      form.displayName = formData.value.displayName
      if (paramsVerify(formData.value)) {
        createVisible.value = true
      } else {
        Message.error('筛选条件参数错误')
      }
    }
  }
  // eventBus.emit(eventListData.value);
};
</script>

<style scoped lang="less">
.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 7px;
          margin-left: 31px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
          cursor: grab;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            // font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }

            :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
              font-weight: 600 !important;
            }

            :deep(.arco-input-wrapper) {
              border: none;
              background-color: transparent;
              font-weight: 600;

              &:hover {
                border: none;
                background-color: transparent;
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .action-left .row-content :deep(.filter-icon) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }
      }
    }

    .row-foot {
      margin: 0;
      padding-left: 34px;
      transition: all .3s;

      .ta-filter-button {
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-hover);
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-textarea) {
  border: none;
}

.metric {
  margin-top: 38px;
  margin-left: 8px;

  .metricHead {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .metricHeadTitleContainer {
      display: flex;

      .metricHeadTitle {
        margin-left: 6px;
        color: var(--color-deep-blue-8);
        font-weight: 500;
      }
    }

    .formulaType {
      padding-left: 8px;
      color: var(--color-gray-blue-7);
      font-size: 12px;
    }
  }

  .indicator-content {
    padding: 16px 24px;
    background-color: var(--tant-fill-color-fill1-2);
    border-radius: 4px;

    .event-item {
      display: inline-flex;
      align-items: center;
      margin-right: 16px;
    }

    .event-label {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }
  }
}
</style>