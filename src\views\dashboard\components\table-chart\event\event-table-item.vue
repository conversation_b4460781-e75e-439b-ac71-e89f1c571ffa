<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <a-table
          v-if="tableData?.length>0"
          :columns="columns"
          style="white-space: nowrap"
          :data="tableData"
          size="small"
          :pagination="pagination"
          column-resizable
          :filter-icon-align-left="true"
          @page-size-change="pageSizeChange">
        <template v-for="(_, index) in props.reportAnalysisData?.groupsDesc" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                  :model-value="filterValue"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :options="getFilterOptionsByIndex(index)"
                  :style="{ width: '200px' }"
                  :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                  @update:model-value="(val) => setFilterValue(val)">
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record }">
          {{ formatNumber(record[column.dataIndex], tableType === 'event' ? column.displayType : record.displayType) }}
          <span v-if="(tableType === 'event' ? column.displayType?.type === 'percent' : record.displayType?.type === 'percent')">%</span>
        </template>
      </a-table>
      <a-empty v-else-if="!loading" style="width: 100%;height: 100%"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref, watch} from "vue";
import {WsEventAnalysisResultData} from "@/api/report/type";
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {createCustomSorter} from "@/utils/strUtil";

interface Props {
  /**
   * 报表数据
   */
  reportAnalysisData: WsEventAnalysisResultData;
  // 时间格式化
  timeFormat: string;
  // 表格按事件，按日期
  tableType: string;
  // 事件分析 / 运营分析
  modelType: string;
  // 页面数量
  pageSize: number;
}

const props = defineProps<Props>()
const emits = defineEmits(['pageSizeChange']);
const columns = ref()
const loading = ref(true)
const tableData = ref<any>([])
const pagination = ref<any>({
  pageSize: props.pageSize || 10,
  pageSizeOptions: [30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  showPageSize: 'true',
  showTotal: 'true'
})

// 判断周几
const getDayOfWeek = (dateString) => {
  if (props.timeFormat === 'D1') {
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if (props.timeFormat === 'W1') {
    return `${dateString}当周`;
  }
  if (props.timeFormat === 'M1') {
    return `${dateString}月`;
  }
  if (props.timeFormat === 'Q1') {
    return `${dateString}季度`;
  }
  if (props.timeFormat === 'Y1') {
    return `${dateString}年`;
  }
  return dateString;
}
// 时间格式化数据
const formatterData = computed(() => {
  const formatMap = {
    'm1': 'YYYY-MM-DD HH:mm:ss',
    'm5': 'YYYY-MM-DD HH:mm:ss',
    'm10': 'YYYY-MM-DD HH:mm:ss',
    'h1': 'YYYY-MM-DD HH:mm',
    'D1': 'YYYY-MM-DD',
    'W1': 'YYYY-MM-DD',
    'M1': 'YYYY/MM',
    'Q1': 'YYYY-M',
    'Y1': 'YYYY'
  };
  return formatMap[props.timeFormat]
})
// 千分位
const formatNumber = (num: number, displayType?: { type: string; decimalNum: number }) => {
  if (num === null || num === undefined) return '0';
  let formattedValue = ''
  if (displayType && displayType.type) {
    if (displayType.type === 'default') {
      formattedValue = num.toFixed(displayType.decimalNum);
    } else if (displayType.type === 'percent') {
      // 乘以 100
      formattedValue = (num * 100).toFixed(displayType.decimalNum);
    }
  }
  return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}

function renderChart(wsResultData: WsEventAnalysisResultData) {
  if (!wsResultData) {
    return
  }
  const data = wsResultData.y.flatMap(item =>
      item.yData.map(el => ({
        name: item.displayName,
        displayType: item.displayType,
        groupValue: el.group.map(value => value === null ? 'null' : value).join(','),
        groupList: el.group.map(value => value === null ? 'null' : value),
        values: props.tableType === 'dateDown' ? [...el.values].reverse() : el.values
      }))
  );
  const uniqueData = Array.from(new Set(data.map(item => item.name)))
      .map(name => data.find(item => item.name === name)); // 根据 name 去重
  const xDateList = props.tableType === 'dateDown' ? [...wsResultData.x].reverse() : wsResultData.x;
  const columnsDate = xDateList && xDateList.length > 0 ? xDateList.map(item => props.timeFormat !== 'T0' ? dayjs(item).format(formatterData.value) : item) : []
  const spliceList = [] as any
  const groupsList = wsResultData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }) || [];
  if (props.tableType === 'event') {
    columns.value = [
      {
        title: '日期',
        dataIndex: 'date',
        width: 140,
        fixed: 'left',
        sortable: {sortDirections: ['ascend', 'descend']}
      },
      ...uniqueData.map((item, index) => ({
        title: item.name,
        dataIndex: `num${index + 1}`,
        slotName: `num${index + 1}`,
        displayType: item.displayType,
        sortable: {sortDirections: ['ascend', 'descend']}
      }))
    ];

    const result = [] as any
    const dates = columnsDate.map(item => getDayOfWeek(item))
    const yLength = wsResultData.y.length;
    dates.forEach((date, dateIndex) => {
      groupsList.forEach(groupValue => {
        const entry = {
          date,
          groupValue,
          groupList: [],
          displayType: {}
        };
        // 初始化所有 num 值为 null
        for (let i = 0; i < yLength; i++) {
          entry[`num${i + 1}`] = null;
        }
        // 遍历数据,填充对应的 num 值
        data.forEach((item) => {
          if (item.groupValue === groupValue) {
            for (let i = 0; i < yLength; i++) {
              if (entry[`num${i + 1}`] === null) {
                entry[`num${i + 1}`] = item.values[dateIndex];
                entry.groupList = item.groupList;
                entry.displayType = item.displayType
                break; // 找到第一个为null的num并赋值后,跳出循环
              }
            }
          }
        });
        result.push(entry);
      });
    });

    tableData.value = [...result];
    // 当有分组项的情况下
    if (wsResultData.groupsDesc && wsResultData.groupsDesc.length > 0) {
      wsResultData.groupsDesc.forEach((item, index) => {
        spliceList.push({
          title: item.name,
          dataIndex: `group-${index}`,
          sortable: {
            sortDirections: ['ascend', 'descend'],
            sorter: (a, b, extra) => {
              const {direction} = extra;
              const format = item.format;
              const valueA = a[`group-${index}`];
              const valueB = b[`group-${index}`];
              return createCustomSorter(format)(valueA, valueB, direction);
            }
          },
          filterable: {
            filter: (value, row) => {
              if (!value || value.length === 0) return true;
              return value.includes(row.groupList[index]);
            },
            slotName: `group-filter-${index}`, // 使用唯一的插槽名
            multiple: true,
          },
        })
        tableData.value.forEach(el => {
          el[`group-${index}`] = el.groupList[index]
        })
      })
      columns.value.splice(1, 0, ...spliceList);
    }

  } else {
    columns.value = [
      {
        title: '指标',
        dataIndex: 'name',
        width: 140,
        sortable: {sortDirections: ['ascend', 'descend']},
        filterable: {
          // 直接从 uniqueData 获取筛选选项，因为它包含了所有的指标名称
          filters: uniqueData.map(item => ({
            text: item.name,
            value: item.name
          })),
          filter: (value, row) => value.includes(row.name),
          multiple: true
        }
      },
      ...columnsDate.map((item, index) => ({
        title: getDayOfWeek(item),
        dataIndex: `date-${index + 1}`,
        slotName: `date-${index + 1}`,
        width: 140,
        sortable: {sortDirections: ['ascend', 'descend']}
      }))
    ];

    tableData.value = data.map(item => ({
      name: item.name,
      groupValue: item.groupValue,
      groupList: item.groupList,
      displayType: item.displayType,
      // sum: calculateSum(item.values),
      ...columnsDate.reduce((acc, _, index) => {
        acc[`date-${index + 1}`] = item.values[index] || 0;
        return acc;
      }, {})
    }));
    tableData.value.sort((a, b) => {
      if (a.groupValue === b.groupValue) {
        return 0; // 相同的 groupValue 保持原有顺序
      }
      return a.groupValue < b.groupValue ? -1 : 1; // 按 groupValue 排序
    });

    // 当有分组项的情况下
    if (wsResultData.groupsDesc && wsResultData.groupsDesc.length > 0) {
      wsResultData.groupsDesc.forEach((item, index) => {
        spliceList.push({
          title: item.name,
          dataIndex: `group-${index}`,
          sortable: {
            sortDirections: ['ascend', 'descend'],
            sorter: (a, b, extra) => {
              const {direction} = extra;
              const format = item.format;
              const valueA = a[`group-${index}`];
              const valueB = b[`group-${index}`];
              return createCustomSorter(format)(valueA, valueB, direction);
            }
          },
          filterable: {
            filter: (value, row) => {
              if (!value || value.length === 0) return true;
              return value.includes(row.groupList[index]);
            },
            slotName: `group-filter-${index}`, // 使用唯一的插槽名
            multiple: true,
          },
        })
        tableData.value.forEach((el, elIndex) => {
          el[`group-${index}`] = el.groupList[index]
        })
      })
    }
    columns.value.unshift(...spliceList)
  }
}

const pageSizeChange = (size) => {
  pagination.value.pageSize = size;
  emits('pageSizeChange', size)
}

watch(() => props.reportAnalysisData, async (newData, oldData) => {
  if (!newData) {
    return
  }
  renderChart(newData)
  loading.value = false
})
watch(() => props.tableType, async (newData, oldData) => {
  renderChart(props.reportAnalysisData)
})

// 监听外部卡片大小变化
watch(() => props.pageSize, (newData, oldValue) => {
  pagination.value.pageSize = newData
})

onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
  loading.value = false
})
// 导出
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  const dateString = date ? `${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}` : ''
  XLSX.writeFile(newWorkbook, `${props.modelType === 'event' ? '事件分析' : '运营分析'}${name ? `_${name}` : ''}${dateString ? `_${dateString}` : ''}.xlsx`);
};
defineExpose({
  exportXlsx
})
// 添加一个获取筛选选项的函数
const getFilterOptionsByIndex = (index: number) => {
  if (!tableData.value?.length) return [];
  return Array.from(new Set(tableData.value.map(row => row.groupList[index])))
      .filter(value => value !== undefined && value !== null)
      .map(value => ({
        label: value,
        value: value
      }));
};
</script>

<style scoped lang="less">
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}

.chart-content {
  position: relative;
  padding-top: 8px;
  width: 100%;
  height: 100%;
}
</style>
