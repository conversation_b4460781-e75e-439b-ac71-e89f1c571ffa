<template>
  <div class="chart-content">
    <a-table :columns="columns" :data="tableData" :pagination="false" size="small" :scroll="scroll" column-resizable :filter-icon-align-left="true">
      <!-- 添加筛选器插槽 -->
      <template #group-filter="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
        <div class="custom-filter">
          <a-space direction="vertical" :size="8">
            <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions()"
                :style="{ width: '200px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
            </a-select>
            <div class="custom-filter-footer">
              <a-button size="mini" @click="handleFilterReset">重置</a-button>
              <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
            </div>
          </a-space>
        </div>
      </template>
      <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record }">
        <div class="cell-class" :style="getBackgroundColor(index,record[`rate${index}`])">
          <div class="hover-box">
            <span>{{ record[column.dataIndex] }}</span>
          </div>
          <div v-if="index >1" class="rate">{{ (record[`rate${index}`]).toFixed(2) }}%</div>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref, watch} from "vue";
import * as XLSX from 'xlsx';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {createCustomSorter} from "@/utils/strUtil";

interface Props {
  /**
   * 表格大小
   */
  size: number
  // 表格类型
  tableType: string
  /**
   * 报表数据
   */
  reportAnalysisData: object;
}

const props = defineProps<Props>()
const columns = ref()
const tableData = ref()
const groups = ref<any>([])

const getFilterOptions = () => {
  return Array.from(new Set(tableData.value.map(item => item.groupName)))
      .map(value => ({
        label: value,
        value: value
      }));
};
// 处理列表头
const handleColumns = (wsResultData) => {
  // 转化图table
  const groupsDescName = wsResultData?.groupsDesc[0]?.name
  const groupsFormat = wsResultData?.groupsDesc[0]?.format
  const groupColumn = [
    {
      title: groups.value.length > 1 ? groupsDescName : '总体',
      slotName: 'groupName',
      dataIndex: 'groupName',
      fixed: 'left',
      sortable: {
        sortDirections: ['ascend', 'descend'],
        sorter: (a, b, extra) => {
          const {direction} = extra;
          const valueA = a[`groupName`];
          const valueB = b[`groupName`];
          return createCustomSorter(groupsFormat)(valueA, valueB, direction);
        }
      },
      filterable: groups.value.length > 1 ? {
        filter: (value, row) => !value?.length || value.includes(row.groupName),
        slotName: 'group-filter',
        multiple: true
      } : undefined
    }
  ]
  let stepColumn
  if (props.tableType === 'loss') {
    // 流失
    stepColumn = wsResultData?.stepEvents.map((item, index) => {
      return {
        title: index > 0 ? `${item.eventDisplayName}(步骤${index}到步骤${index + 1})` : `${item.eventDisplayName}(步骤${index + 1})`,
        dataIndex: `num${index + 1}`,
        slotName: `num${index + 1}`,
        minWidth: 200,
        sortable: {sortDirections: ['ascend', 'descend']}
      }
    })
  } else {
    // 转化
    stepColumn = wsResultData?.stepEvents.map((item, index) => {
      return {
        title: `${item.eventDisplayName}(步骤${index + 1})`,
        dataIndex: `num${index + 1}`,
        slotName: `num${index + 1}`,
        minWidth: 200,
        sortable: {sortDirections: ['ascend', 'descend']}
      }
    })
  }
  columns.value = groupColumn.concat(stepColumn)
}
// 处理列表数据
const handleTableData = (wsResultData) => {
  // 转化图table
  let rateData
  let numData
  if (props.tableType === 'loss') {
    // 流失
    rateData = wsResultData?.funnelQueryResult.find(item => item.type === 'churn_rate')?.groupData || []
    numData = wsResultData?.funnelQueryResult.find(item => item.type === 'churn_num')?.groupData || []
  } else {
    // 转化
    rateData = wsResultData?.funnelQueryResult.find(item => item.type === 'conversion_rate')?.groupData || []
    numData = wsResultData?.funnelQueryResult.find(item => item.type === 'conversion_num')?.groupData || []
  }
  tableData.value = groups.value.map((name, index) => {
    // 找到对应的转化率和转化数数据
    const rateItem = rateData.find(item => item.group[0] === name)?.values
    const numItem = numData.find(item => item.group[0] === name)?.values
    // 创建基础对象
    const baseObj = {groupName: name}
    // 动态添加 num 和 rate 属性
    return rateItem.reduce((acc, _, index) => {
      acc[`num${index + 1}`] = numItem[index] || 0
      acc[`rate${index + 1}`] = (Math.round(rateItem[index] * 100 * 100) / 100) || 0
      return acc
    }, baseObj)
  })
}

function renderChart(wsResultData: any) {
  if (!wsResultData) {
    return
  }
  if (!wsResultData?.funnelQueryResult?.length) {
    return
  }
  groups.value = wsResultData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(',');
  }) || [];
  handleColumns(wsResultData)
  handleTableData(wsResultData)
}

watch(() => props.reportAnalysisData, async (newData, oldData) => {
  renderChart(newData);
})
watch(() => props.tableType, async (newData, oldData) => {
  renderChart(props.reportAnalysisData);
})

const scroll = computed(() => {
  return {
    y: '100%'
  }
})

// 处理表格颜色深浅
const getBackgroundColor = (index, value) => {
  if (index === 0) {
    return {background: 'rgb(255, 255, 255)'};
  }
  if (index === 1) {
    return {background: 'rgb(249, 249, 251)'};
  }
  // 如果没有值，返回白色背景
  if (value === null || value === undefined || value === '') {
    return {background: 'rgb(255, 255, 255)'};
  }
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return {background: 'rgb(255, 255, 255)'};
  }
  // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
  const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
  const backgroundColor = `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
  return {background: backgroundColor};
}
onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }

  renderChart(props.reportAnalysisData)
})
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  const dateString = date ? `${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}` : ''
  XLSX.writeFile(newWorkbook, `漏斗分析${name ? `_${name}` : ''}}${dateString ? `_${dateString}` : ''}.xlsx`);
};
defineExpose({
  exportXlsx
})
</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  padding-top: 8px;
  width: 100%;
  height: 100%;
}

:deep(tbody .arco-table-cell) {
  padding: 0;
}

.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}

.cell-class {
  padding: 5px 16px;
  height: 54px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hover-box {
  display: flex;
  align-items: center;

  &:hover {
    .add-group {
      opacity: 1;
    }
  }

  .add-group {
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;

    &:hover {
      color: #8d0088;
    }
  }
}
</style>
