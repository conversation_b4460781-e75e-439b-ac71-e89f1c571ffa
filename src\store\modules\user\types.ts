export type RoleType = '' | '*' | 'admin' | 'user';
export interface UserState {
  code?: string;
  name?: string;
  avatar?: string;
  accountNo?:string;
  gender?:number;
  mobile?:string;
  email?: string;
  note?:string;
  nickname?:string;
  // job?: string;
  // organization?: string;
  // location?: string;
  // introduction?: string;
  // personalWebsite?: string;
  // jobName?: string;
  // organizationName?: string;
  // locationName?: string;
  // phone?: string;
  // registrationDate?: string;
  // accountId?: string;
  // certification?: number;
  role?: RoleType;
}
