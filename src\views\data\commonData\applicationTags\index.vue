<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import {Message} from '@arco-design/web-vue';
import {useRoute} from 'vue-router';
import {deleteTag, getOpTagList} from "@/api/marketing/api";
import dayjs from 'dayjs';
import handleModal from "./handleModal.vue";

const route = useRoute();

const handleRefs = ref()
// 模拟table数据
const loading = ref(false)

const tableData = ref<any>([])
const init = () => {
  loading.value = true
  getOpTagList().then(res => {
    if (res) {
      tableData.value = res
    }
    loading.value = false
  })
}
init()

const pageOptions = [
  10, 20, 50, 100
]
const createIndex = () => {
  handleRefs.value.openModal()
}
const editIndex = (record) => {
  handleRefs.value.openModal(record)
}
// input搜索
const searchTerm = ref('');

// 图标改变
const scrollbar = ref(true);

const filteredTableData = computed(() => {
  const str = searchTerm.value.trim();
  return tableData.value.filter(item => item?.name.includes(str));
});
const columns = [
  {
    title: '标签名称',
    dataIndex: 'name',
    ellipsis: "true",
    tooltip:true,
    slotName: 's-name',
    minWidth: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: "true",
    tooltip:true,
    slotName: 'description',
    minWidth: 120,
  },
  {
    title: '最后更新人',
    dataIndex: 'creatorName',
    slotName: 'creatorName',
    ellipsis: "true",
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '最后更新时间',
    dataIndex: 'updateTime',
    slotName: 'updateTime',
    ellipsis: "true",
    sortable: {
      sortDirections: ['ascend', 'descend'],
    }

  },
  {
    title: '操作',
    dataIndex: 'operate',
    ellipsis: "true",
    slotName: 's-operate',
    minWidth: 120,
  },
]

// modal
// 初始化 deleteModalVisible 为一个与 tableData 长度一致的数组
const deleteModalVisible = ref(Array(filteredTableData.value.length).fill(false));
// 点击删除按钮时，打开对应行的模态框
const deleteModalClick = (item) => {
  const index = filteredTableData.value.findIndex((row) => row.code === item.code);
  deleteModalVisible.value[index] = true;
};
const deleteOk = (code: string) => {
  deleteTag(code).then(res => {
    if (res) {
      init()
      Message.success('删除成功')
    }
  })
};
const deleteCancel = () => {
  deleteModalVisible.value = Array(filteredTableData.value.length).fill(false);
};
// Table页面滑动
const scroll = {
  y: 'calc(100vh - 186px)'
};
const totalPages = computed(() => {
  return filteredTableData.value.length;
});
const pageNumber = ref(1)

const pagination = reactive({
  total: totalPages.value,  // 使用过滤后的数据长度
  PageSize: pageOptions[0],
  showPageSize: true,
  pageSizeOptions: pageOptions,
  showJumper: true,
  hideOnSinglePage: false,
  autoAdjust: true,
});
const pageChange = (v: number) => {
  pageNumber.value = v
}
const pageSizeChange = (v: number) => {
  pagination.PageSize = v
}
watch(totalPages, (newTotal) => {
  pagination.total = newTotal;
});

</script>

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <a-input v-model="searchTerm" allow-clear class="title-input" placeholder="搜索应用标签">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            <template #icon>
              <icon-plus/>
            </template>
            创建应用标签
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :loading="loading"
          :data="filteredTableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :scrollbar="scrollbar"
          :pagination="pagination"
          @page-change="pageChange"
          @page-size-change="pageSizeChange"
      >
        <template #updateTime="{record}">
          <span v-if="record.updateTime">{{ dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
          <span v-else>{{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </template>
        <template #s-operate="{record,rowIndex}">
          <div style="display:flex;align-items: center;gap:10px">
            <a-tooltip content="编辑">
              <div class="setting" @click="editIndex(record)">
                <icon-edit size="16px"/>
              </div>
            </a-tooltip>
            <a-tooltip content="删除">
              <div class="delete" @click="deleteModalClick(filteredTableData[rowIndex])">
                <icon-delete size="16px"/>
              </div>
            </a-tooltip>
            <a-modal
                v-model:visible="deleteModalVisible[rowIndex]"
                :mask-closable="false"
                :top="150"
                :ok-button-props="{status:'danger'}"
                :align-center="false"
                title-align="start"
                ok-text="删除"
                @ok="deleteOk(record.code)" @cancel="deleteCancel">
              <template #title>
                删除应用标签
              </template>
              <div>确定删除【{{ record.name }}】？该操作不可恢复。</div>
            </a-modal>
          </div>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ filteredTableData.length }}条记录
          </div>
        </template>

      </a-table>
    </div>
    <handleModal ref="handleRefs" @update-data="init"/>
  </div>

</template>

<style scoped lang="less">
:deep(.arco-input-prefix) {
  padding-left: 8px;
}


:deep(.arco-checkbox-icon) {
  width: 16px;
  height: 16px;
}


.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}



:deep(.arco-table-tr) {
  height: 66px;
}

.pagination-left {
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}


:deep(.arco-pagination-jumper > span) {
  color: var(--tant-text-gray-color-text1-1);
}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}

</style>