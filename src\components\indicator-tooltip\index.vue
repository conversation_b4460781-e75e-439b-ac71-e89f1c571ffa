<template>
  <a-popover
    v-model:popup-visible="popupVisible"
    trigger="hover"
    position="top"
    :popup-max-width="400"
    :content-style="{ padding: '12px' }"
  >
    <slot />
    <template #content>
      <div v-if="indicatorDetail" class="indicator-content">
        <div class="indicator-title">{{ indicatorDetail.displayName }}</div>
        <div v-if="indicatorDetail.description" class="indicator-description">
          描述：{{ indicatorDetail.description }}
        </div>
        <component 
          :is="CustomIndicatorDisplay.default" 
          v-if="indicatorDetail?.eventList?.length" 
          :indicator="indicatorDetail || {}" 
        />
        <div v-else style="display: flex; align-items: center; margin-top: 8px;">
          <span style="flex-shrink: 0;">聚合数据：</span>
          <div class="select-btn-disabled">
            <span class="btn-label">
              {{ indicatorDetail.name }}-{{ indicatorDetail.displayName }}
            </span>
          </div>
        </div>
        <component 
          :is="EventQueryFilter.default" 
          v-if="indicatorDetail?.filter?.filters?.length > 0" 
          :filter="indicatorDetail?.filter" 
          :disabled="true" 
          :show-detail-filter="true" 
          :code-list="indicatorDetail?.eventList" 
        />
      </div>
      <div v-else class="indicator-loading">
        <a-spin tip="加载中..."/>
      </div>
    </template>
  </a-popover>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import * as CustomIndicatorDisplay from '@/views/analyse/components/CustomIndicatorDisplay.vue'
import * as EventQueryFilter from '@/views/analyse/components/AttrCategoryFilter.vue'

interface Props {
  // 指标编码
  indicatorCode?: string
  // 指标详情数据（可直接传入，避免重复请求）
  indicatorData?: any
  // 获取指标详情的方法
  getIndicatorDetail?: (code: string) => Promise<any>
}

const props = defineProps<Props>()

const popupVisible = ref(false)
const indicatorDetail = ref<any>(null)

// 监听悬浮显示状态，延迟加载指标详情
watch(popupVisible, async (visible) => {
  if (visible && !indicatorDetail.value) {
    try {
      if (props.indicatorData) {
        // 如果直接传入了指标数据，直接使用
        indicatorDetail.value = props.indicatorData
      } else if (props.indicatorCode && props.getIndicatorDetail) {
        // 通过方法获取指标详情
        indicatorDetail.value = await props.getIndicatorDetail(props.indicatorCode)
      }
    } catch (error) {
      console.error('获取指标详情失败:', error)
    }
  }
})

// 重置指标详情（当指标编码变化时）
watch(() => props.indicatorCode, () => {
  indicatorDetail.value = null
})

watch(() => props.indicatorData, (newData) => {
  if (newData) {
    indicatorDetail.value = newData
  }
})
</script>

<style scoped lang="less">
.indicator-content {
  max-width: 350px;
  
  .indicator-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    color: var(--color-text-1);
  }
  
  .indicator-description {
    font-size: 12px;
    color: var(--color-text-3);
    margin-bottom: 8px;
    line-height: 1.4;
  }
}

.indicator-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  color: var(--color-text-3);
  font-size: 12px;
}

.select-btn-disabled {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  max-width: 100%;
  height: 26px;
  padding: 0 8px;
  font-size: 14px;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.3s;
  box-sizing: border-box;
  
  .btn-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
}
</style>