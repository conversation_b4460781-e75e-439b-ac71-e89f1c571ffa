<script setup lang="ts">
  import useChartOption from '@/hooks/chart-option';
  import { onMounted, ref, watch } from 'vue';

  interface Props {
    /**
     * 日期
     */
    date: string[] | undefined;

    /**
     * 分组
     */
    group: string[];

    /**
     * 数据
     */
    data: number[][];

    /**
     * 数据名
     */
    dataName: string;
  }

  const props = defineProps<Props>();

  const option = ref<any>();

  const refresh = () => {
    // 提取数据并创建包含索引的数组，用于保持原始索引关系
    const rawData =
      props.data?.map((item, index) => {
        const totalArray = item.total;
        const value = totalArray && totalArray.length > 0 ? totalArray[totalArray.length - 1] || 0 : 0;
        return {
          value,
          index,
          group: props.group[index],
          data: item,
        };
      }) || [];

    // 按数值从大到小排序
    const sortedData = [...rawData].sort((a, b) => b.value - a.value);

    // 提取排序后的数据、分组和原始索引
    const sortedValues = sortedData.map((item) => item.value);
    const sortedGroups = sortedData.map((item) => item.group);
    const sortedOriginalIndices = sortedData.map((item) => item.index);

    const colorList = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#235894',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#235894',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#235894',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#235894',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#235894',
    ];

    // 根据排序后的数据长度创建颜色列表的子集
    const sortedColorList = sortedOriginalIndices.map((originalIndex, newIndex) => colorList[originalIndex % colorList.length]);

    const nameStyle = {};
    sortedColorList.forEach((color, i) => {
      nameStyle[`name${i}`] = {
        color,
        padding: [0, 4, 0, 6],
        fontSize: 14,
        fontWeight: 900,
        lineHeight: 33,
      };
    });

    const { chartOption } = useChartOption(() => {
      return {
        grid: {
          left: '36',
          right: '0',
          top: '16',
          bottom: '24',
        },
        tooltip: {
          trigger: 'item',
          appendToBody: true,
          enterable: true,
          confine: true,
          formatter: (params) => {
            // 计算总值
            const totalValue = props.data.reduce((sum, item) => {
              const totalArray = item.total;
              const value = totalArray && totalArray.length > 0 ? totalArray[totalArray.length - 1] || 0 : 0;
              return sum + value;
            }, 0);

            // 构建包含所有数据项的数组并按数值大小排序
            const dataItems = props.group
              .map((group, index) => {
                const item = props.data[index];
                if (item) {
                  const totalArray = item.total;
                  const value = totalArray && totalArray.length > 0 ? totalArray[totalArray.length - 1] || 0 : 0;
                  const percentage = totalValue > 0 ? ((value / totalValue) * 100).toFixed(2) : 0;
                  return {
                    group,
                    value,
                    percentage: parseFloat(percentage),
                    originalIndex: index, // 保存原始索引用于获取颜色
                  };
                }
                return null;
              })
              .filter((item) => item !== null); // 过滤掉空值
            // 按数值从大到小排序
            dataItems.sort((a, b) => b.value - a.value);
            // 构建tooltip内容
            let tooltipContent = '';
            // 添加排序后的每个分组的详细信息
            dataItems.forEach((item) => {
              // 获取对应的颜色
              const color = colorList[item.originalIndex % colorList.length];
              // 创建圆点样式
              const colorDot = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`;
              tooltipContent += `${colorDot}${item.group}: ${item.value.toLocaleString()} <span style="padding-left:8px;color:${color}">(${item.percentage}%)</span><br/>`;
            });

            return tooltipContent;
          },
          // 添加最大高度和滚动条样式
          extraCssText: 'max-height: 400px; overflow-y: auto;',
        },
        color: sortedColorList,
        series: [
          {
            name: props.dataName,
            type: 'pie',
            radius: ['65%', '80%'],
            labelLine: {
              length: 10,
              length2: 40,
              lineStyle: {
                width: 2,
              },
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: true,
              position: 'top',
              formatter: (params) => {
                let value = Math.round(params.value);
                const { dataIndex } = params;
                // 使用排序后的原始索引来获取正确的数据
                const originalIndex = sortedOriginalIndices[dataIndex];
                const diffRateArray = props.data[originalIndex].totalDiffRate;
                const diffRate = diffRateArray && diffRateArray.length > 0 ? diffRateArray[diffRateArray.length - 1] : 0;
                const rate = diffRate < 0 ? `{down|↓ ${diffRate}%}` : `{up|↑${diffRate}%}`;
                if (!params.value || typeof params.value !== 'number') {
                  value = params.value;
                }
                if (params.value >= 1000000) {
                  value = `${Math.round(params.value / 1000000)}M`; // 转换为M单位
                }
                if (params.value >= 1000) {
                  value = `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
                }
                return `{name${dataIndex}|${sortedGroups[dataIndex]}} {value|${value}}`;
              },
              backgroundColor: '#F6F8FC',
              borderColor: '#8C8D8E',
              borderWidth: 1,
              borderRadius: 4,
              padding: [0, 5],
              rich: {
                ...nameStyle,
                value: {
                  color: '#6E7079',
                  fontSize: 14,
                  fontWeight: 900,
                  lineHeight: 22,
                  align: 'center',
                },
                up: {
                  color: '#91cc75',
                  lineHeight: 22,
                  align: 'center',
                  fontWeight: 900,
                },
                down: {
                  color: '#ee6666',
                  lineHeight: 22,
                  align: 'center',
                  fontWeight: 900,
                },
              },
            },
            data: sortedValues,
          },
        ],
      };
    });
    option.value = chartOption.value;
  };

  watch(props, () => {
    refresh();
  });

  onMounted(() => {
    refresh();
  });
</script>

<template>
  <Chart :option="option" />
</template>

<style scoped lang="less"></style>
