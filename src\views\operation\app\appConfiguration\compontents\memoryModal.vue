<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" title="编辑设备内存" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="设备内存">
                <a-select v-model:model-value="form.name" allow-create :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="channelChange">
                    <a-option v-for="(item,index) in props.memoryList" :key="index" :value="item.name">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="minMem" label="起始内存">
                <a-input-number v-model="form.minMem" :min="0">
                    <template #append>
                        G
                    </template>
                </a-input-number>
            </a-form-item>
            <a-form-item field="maxMem" label="最大内存">
                <a-input-number v-model="form.maxMem" :min="0">
                    <template #append>
                        G
                    </template>
                </a-input-number>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {memAdd, memUpdate} from "@/api/marketing/api";

const props = defineProps({
    memoryList: {
      type: Array,
      default: () => [],
    }
})
const modalVisible = ref(false)
const form = reactive({
    name: '',
    code: '',
    minMem:undefined,
    maxMem:undefined
})
const rules = {
    name: [
        {
            required: true,
            message:'请选择设备内存',
        }
    ],
    minMem: [
        {
            required: true,
            validator: (value, cb) => {
                return new Promise((resolve) => {
                    if (!value) {
                        cb('请填写起始内存')
                    }
                    if (value && form.maxMem && value >= form.maxMem) {
                        cb('起始内存需小于最大内存');
                    }
                    resolve(value)
                })
            }
        }
    ],
    maxMem: [
        {
            required: true,
            validator: (value, cb) => {
                return new Promise((resolve) => {
                    if (!value) {
                        cb('请填写最大内存')
                    }
                    if (value && form.minMem && value <= form.minMem) {
                        cb('最大内存必须大于起始内存');
                    }
                    resolve(value)
                })
            }
        }
    ],
}
const formRef = ref()

const openModal = (value:string) => {
    form.code = value
    props.memoryList.forEach(item => {
        if(item.code === value) {
            form.name = item.name
            form.minMem = item?.minMem
            form.maxMem = item?.maxMem
        }
    })
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
    formRef.value.resetFields()
    formRef.value.clearValidate()
}
const channelChange = (v) => {
    const hasNameEqualToV = props.memoryList.some(item => item.name === v);
    if(hasNameEqualToV){
        form.name = v
        props.memoryList.forEach(item => {
            if(item.name === v) {
                form.code = item.code
                form.minMem = item?.minMem
                form.maxMem = item?.maxMem
            }
        })
    }else{
        form.code = ''
        form.name = v
        form.minMem = undefined
        form.maxMem = undefined
    }
    
}
const emits = defineEmits(['updateData']);
const saveData = () => {
    formRef.value.validate((valid:any) => {
        if (!valid) {
            // 
            console.log(form,'form');
            if(form.code){
                // 更新
                try {
                    memUpdate(form).then(res => {
                        Message.success('更新成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('更新失败:', error);
                }
            }else{
                // 添加
                try {
                    memAdd(form).then(res => {
                        Message.success('创建成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('创建失败:', error);
                }
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>