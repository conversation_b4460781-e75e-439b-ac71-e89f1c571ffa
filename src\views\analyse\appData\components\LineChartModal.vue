<template>
  <a-modal 
    v-model:visible="visible" 
    :title="modalTitle"
    title-align="start"
    width="800px" 
    :footer="false"
    @cancel="handleClose"
  >
    <div class="chart-container">
      <Chart v-if="chartOption" :option="chartOption" :height="'400px'" />
      <a-spin v-else :loading="true" style="width: 100%; height: 400px;" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Chart from '@/components/chart/index.vue';
import useChartOption from '@/hooks/chart-option';

interface Props {
  visible: boolean;
  title: string;
  dates: string[];
  values: number[];
  indicatorName: string;
  groupInfo?: string;
}

const props = defineProps<Props>();
const emits = defineEmits(['update:visible']);

const visible = computed({
  get: () => props.visible,
  set: (val) => emits('update:visible', val)
});

const modalTitle = computed(() => {
  let title = props.indicatorName;
  if (props.groupInfo) {
    title += ` (${props.groupInfo})`;
  }
  return title;
});

const chartOption = computed(() => {
  if (!props.dates?.length || !props.values?.length) return null;

  const { chartOption } = useChartOption(() => ({
    legend: {
      data: [props.indicatorName],
      bottom: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        const formattedValue = param.value.toLocaleString();
        return `${param.axisValue}<br/>${props.indicatorName}: ${formattedValue}`;
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      top: '10%',
      bottom: '25%'
    },
    xAxis: {
      type: 'category',
      data: props.dates,
      axisLabel: {
        rotate: 45,
        interval: Math.ceil(props.dates.length / 10) - 1 // 控制标签显示密度
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 1000000) {
            return `${(value / 1000000).toFixed(1)}M`;
          }
          if (value >= 1000) {
            return `${(value / 1000).toFixed(1)}k`;
          }
          return value.toString();
        }
      }
    },
    series: [
      {
        name: props.indicatorName,
        type: 'line',
        data: props.values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
      }
    ]
  }));

  return chartOption.value;
});

const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped lang="less">
.chart-container {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>