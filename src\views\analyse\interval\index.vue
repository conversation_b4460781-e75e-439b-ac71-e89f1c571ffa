<template>
  <div id="intervalRoot">
    <div class="analyse-content">
      <analyse-header :header-info="headerInfo"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <div class="box">
                <div class="query-condition">
                  <!-- 分析指标 -->
                  <analysisIndex @indicators-change="indicatorsChange"/>
                  <!-- 全局筛选 -->
                  <globalFilter @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem @aggregates-change="aggregatesChange"/>
                </div>
                <div class="left-footer">
                  <a-button @click="() => saveVisible = true">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
                <!-- 保存弹窗 -->
                <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
                  <template #title>
                    <div class="modal-title">保存报表</div>
                  </template>
                  <a-form ref="saveFormRef" :model="form">
                    <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
                      <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
                    </a-form-item>
                    <a-form-item field="dashboard" label="保存至看板">
                      <dashboard-select v-model:selected="form.dashboard" type="dashboard"/>
                    </a-form-item>
                    <a-form-item field="description" label="备注">
                      <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5 }"/>
                    </a-form-item>
                  </a-form>
                  <template #footer>
                    <a-button @click.stop="handleSaveCancel">取消</a-button>
                    <a-button type="primary" @click="saveReport">保存</a-button>
                  </template>
                </a-modal>
              </div>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <div class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="datePick"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        颗粒度：
                        <dateSet @time-change="timeChange"/>
                      </div>
                    </a-space>
                    <div style="display: flex;">
                      <a-popover v-model:popup-visible="numberGroupPopup" trigger="click" position="br">
                        <a-button v-if="isChartType === 'zfTrend'" style="margin-right: 20px;" @click="() => numberGroupPopup = true">间隔区间</a-button>
                        <template #content>
                          <div class="number-group-set-interval-root">
                            <a-tabs v-model:active-key="activeKey" default-active-key="1">
                              <a-tab-pane key="1" title="等分区间">
                                <div class="number-group-set-read">
                                  <div class="interval-range-wrap">
                                    区间数：
                                    <a-select v-model:model-value="numberAvg" default-value="12" style="width: 100px; margin: 0px 8px;">
                                      <a-option>8</a-option>
                                      <a-option>12</a-option>
                                      <a-option>16</a-option>
                                      <a-option>20</a-option>
                                      <a-option>自定义</a-option>
                                    </a-select>
                                    <a-input-number v-if="numberAvg === '自定义'" v-model="customNumberAvg" style="width: 80px; margin-right: 8px;" :precision="0" :min="2"/>
                                    等分
                                  </div>
                                </div>
                              </a-tab-pane>
                              <a-tab-pane key="2" title="自定义区间">
                                <div class="number-group-set-read">
                                  <div class="interval-range-wrap">
                                    <a-radio-group v-model:model-value="customRadioType" type="button">
                                      <a-radio value="d">天</a-radio>
                                      <a-radio value="h">小时</a-radio>
                                      <a-radio value="m">分钟</a-radio>
                                      <a-radio value="s">秒</a-radio>
                                    </a-radio-group>
                                  </div>
                                </div>
                                <div class="number-custom-range-container">
                                  <div v-show="customRadioType === 'd'" class="number-group-set-read">
                                    <div v-for="(item,index) in dRangeList" :key="index" class="interval-range-wrap">
                                      <a-input-number v-model:model-value="item.number" :precision="0" :min="0" @blur="dNumberChange(item.number,index)"/>
                                    </div>
                                    <div class="interval-range-wrap">
                                      <a-input-number v-model:model-value="addNumber" :precision="0" :min="0" @blur="addNumberChange"/>
                                    </div>
                                  </div>
                                  <div v-show="customRadioType === 'h'" class="number-group-set-read">
                                    <div v-for="(item,index) in hRangeList" :key="index" class="interval-range-wrap">
                                      <a-input-number v-model:model-value="item.number" :precision="0" :min="0" @blur="hNumberChange(item.number,index)"/>
                                    </div>
                                    <div class="interval-range-wrap">
                                      <a-input-number v-model:model-value="addNumber" :precision="0" :min="0" @blur="addNumberChange"/>
                                    </div>
                                  </div>
                                  <div v-show="customRadioType === 'm'" class="number-group-set-read">
                                    <div v-for="(item,index) in mRangeList" :key="index" class="interval-range-wrap">
                                      <a-input-number v-model:model-value="item.number" :precision="0" :min="0" @blur="mNumberChange(item.number,index)"/>
                                    </div>
                                    <div class="interval-range-wrap">
                                      <a-input-number v-model:model-value="addNumber" :precision="0" :min="0" @blur="addNumberChange"/>
                                    </div>
                                  </div>
                                  <div v-show="customRadioType === 's'" class="number-group-set-read">
                                    <div v-for="(item,index) in sRangeList" :key="index" class="interval-range-wrap">
                                      <a-input-number v-model:model-value="item.number" :precision="0" :min="0" @blur="sNumberChange(item.number,index)"/>
                                    </div>
                                    <div class="interval-range-wrap">
                                      <a-input-number v-model:model-value="addNumber" :precision="0" :min="0" @blur="addNumberChange"/>
                                    </div>
                                  </div>
                                </div>
                                <div class="number-custom-range-batch-add">
                                  <a-button type="text" @click="batchAdd">
                                    <template #icon>
                                      <icon-plus/>
                                    </template>
                                    批量添加
                                  </a-button>
                                </div>
                                <div v-show="customRadioType === 'd'" class="number-custom-range-display">{{ dRangeString }}</div>
                                <div v-show="customRadioType === 'h'" class="number-custom-range-display">{{ hRangeString }}</div>
                                <div v-show="customRadioType === 'm'" class="number-custom-range-display">{{ mRangeString }}</div>
                                <div v-show="customRadioType === 's'" class="number-custom-range-display">{{ sRangeString }}</div>
                              </a-tab-pane>
                            </a-tabs>
                            <div v-if="activeKey === '2' && showAddBatch" class="number-custom-range-batch" style="left: -301px;">
                              <div class="number-custom-range-batch-header">
                                <a-radio-group v-model:model-value="batchType">
                                  <a-radio value="batch">
                                    批量替换
                                    <a-tooltip content="使用回车换行分隔" position="top">
                                      <icon-info-circle/>
                                    </a-tooltip>
                                  </a-radio>
                                  <a-radio value="fast">快速添加</a-radio>
                                </a-radio-group>
                              </div>
                              <div class="number-custom-range-batch-content">
                                <a-textarea v-if="batchType === 'batch'" v-model:model-value="textAreaValue" placeholder="使用回车换行分割" style="height: 150px;"/>
                                <a-form v-else :model="stepForm">
                                  <a-form-item field="stepSize" label="步数">
                                    <a-input-number v-model:model-value="stepForm.stepNum" :precision="0" :min="1"/>
                                  </a-form-item>
                                  <a-form-item field="stepNum" label="步长">
                                    <a-input-number v-model:model-value="stepForm.stepSize" :precision="0" :min="0"/>
                                  </a-form-item>
                                </a-form>
                              </div>
                              <div class="number-custom-range-batch-footer">
                                <a-button class="cancel" @click="() => showAddBatch = false">取消</a-button>
                                <a-button type="primary" :disabled="stepForm.stepNum<1" @click="numberCustomBatch">批量添加</a-button>
                              </div>
                            </div>
                            <div class="number-group-set-footer">
                              <span></span>
                              <div class="number-group-set-footer-actions">
                                <a-button class="cancel" @click="cancelRoot">取消</a-button>
                                <a-button type="primary">应用</a-button>
                              </div>
                            </div>
                          </div>
                        </template>
                      </a-popover>
                      <a-button-group v-if="isChartType === 'zfTrend'" style="background-color: #fff;margin-right: 20px;">
                        <a-button>
                          人数
                        </a-button>
                        <a-button>
                          次数
                        </a-button>
                      </a-button-group>
                      <a-button-group style="background-color: #fff;margin-right: 20px;">
                        <a-popover title="盒须图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('hxTrend')" @click="selectChart('hxTrend')"
                          >
                            <template #icon>
                              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor"
                                   data-testid="tga-result--result-toolbar--trans-former--L15-radio-icon">
                                <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"
                                     data-testid="tantui--ta-radio-raido--tga-result--result-toolbar--trans-former--radio-group--dep7-0-0-0-0-1-0-1-0">
                                  <path d="M5 3h4v2H8v3h2a1 1 0 011 1v8a1 1 0 01-1 1H8v2H6v-2H4a1 1 0 01-1-1V9a1 1 0 011-1h2V5H5V3zm4 7H5v6h4v-6z"
                                        data-testid="tantui--ta-radio-raido--tga-result--result-toolbar--trans-former--radio-group--dep8-0-0-0-0-1-0-1-0-0"></path>
                                  <path d="M21 14a1 1 0 01-1 1h-2v3h1v2h-4v-2h1v-3h-2a1 1 0 01-1-1V6a1 1 0 011-1h2V3h2v2h2a1 1 0 011 1v8zm-6-7v6h4V7h-4z"
                                        data-testid="tantui--ta-radio-raido--tga-result--result-toolbar--trans-former--radio-group--dep8-0-0-0-0-1-0-1-0-1"></path>
                                </svg>
                              </svg>
                            </template>
                          </a-button>
                          <template #content>
                            展示事件间隔时长的分布情况 <br>
                            <img :src="hxTrend" alt="" style="width: 100%;height: 90%;"/>
                          </template>
                        </a-popover>
                        <a-popover title="直方图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('zfTrend')" @click="selectChart('zfTrend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示各个间隔区间的人数分布情况 <br>
                            <img :src="zfTrend" alt="" style="width: 100%;height: 90%;"/>
                          </template>
                        </a-popover>
                      </a-button-group>
                      <a-tooltip content="可视化配置">
                        <a-button @click="onCollapse">
                          <template #icon>
                            <icon-settings/>
                          </template>
                        </a-button>
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="right-content">
                    <a-layout style="height: 490px;width: 100%" :style="showDrawer?'':''">
                      <a-layout-content>
                        <event-echars-vue ref="eventechars" :event-data="eventData" :showlabel="showlabel" :y-max="yMax" :y-min="yMin"/>
                      </a-layout-content>
                      <a-layout-footer>
                        <event-table-vue ref="eventtable" :event-data="eventData" :is-chart-type="isChartType"/>
                      </a-layout-footer>
                    </a-layout>
                  </div>
                </div>
              </div>
              <div class="drawer" :style="showDrawer ? {width: '320px'} : {width: '0px'}">
                <div class="drawer-header">
                  <div class="title">可视化配置</div>
                  <div class="icon">
                    <a-button @click="() => showDrawer = false">
                      <template #icon>
                        <icon-double-right/>
                      </template>
                    </a-button>
                  </div>
                </div>
                <a-collapse :default-active-key="['1']" expand-icon-position="right" class="customStyle" :bordered="false" @change="handleChangeCollapse">
                  <a-collapse-item key="1">
                    <template #header>
                      <icon-settings/>
                      通用配置
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示数值</span>
                          <a-tooltip content="仅对直方图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showlabel" size="small"/>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                </a-collapse>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {onMounted, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {hxTrend, zfTrend} from '@/views/dashboard/components/img';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {ChartType} from "@/api/enum";
import _ from "lodash";
import {ReportAnalyseModel} from "@/api/analyse/type";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import analysisIndex from "./components/analysisIndex.vue"
import globalFilter from "../components/globalFilter.vue"
import groupItem from "../components/groupItem.vue"
import eventTableVue from "./components/eventTable.vue";
import eventEcharsVue from "./components/eventEchars.vue";
import analyseHeader from "../components/analyseHeader.vue"
import dateSet from "../components/dateSet.vue"

const headerInfo = reactive({
  title: '间隔分析',
  img: '/icon/topMenu/interval.svg',
  tips: '计算用户产生起点事件到后续产生终点事件的时间间隔，分析时间间隔的分布情况',
  root: '#intervalRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true
})
const eventData = ref({
  y: [],
  groupsDesc: []
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// ------left start
const loading = ref(false)
// 传参数组
const queryParam = reactive({
  indicators: [], // 查询指标
  filter: [], // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: [], // 查询日期范围
  timeParticleSize: '', // 时间粒度
  firstDayDfWeek: null
})
const eventBus = useEventBus('eventList');
const timeChange = (v) => {
  queryParam.timeParticleSize = v.timeParticleSize
  queryParam.firstDayDfWeek = v.firstDayDfWeek
}
// 分析指标传参
const indicatorsChange = (v) => {
  queryParam.indicators = v
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
}
// 计算
const computedData = () => {
  loading.value = true
}
const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
onMounted(() => {
  eventBus.on((event: any) => {
    form.value.name = `${event[0]?.displayName}等${event.length}个`
  });
})

const handleSaveCancel = () => {
  saveVisible.value = false;
}
// 保存报表
const saveReport = async () => {
  if (!form.value.name) {
    Message.error('报表名称未填写')
    return
  }
  await saveAnalyseReportList(ReportAnalyseModel.EVENT, undefined, form.value.dashboard, form.value.name, form.value.description, queryParam, undefined, ChartType.TABLE).then(res => {
    Message.info(_.isEmpty(form.value.dashboard) ? "报表已保存" : "报表已保存并添加至看板")
  })
  saveVisible.value = false;
}

const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
const datePick = (date: any) => {
  boardDate.value = date
};

const showDrawer = ref<boolean>(false)
const eventtable = ref()
const eventechars = ref()
const isChartType = ref<string>('hxTrend')
const showlabel = ref<boolean>(false)
const yMax = ref<number | null>(null)
const yMin = ref<number | null>(null)


const handleChangeCollapse = (key: any) => {
  if (key.includes(2)) {
    eventechars.value.changeType('hxTrend')
  }
}

const onCollapse = () => {
  showDrawer.value = !showDrawer.value;
};
const selectChart = (type: string) => {
  isChartType.value = type
  eventechars.value.changeType(type)
}

// ---right end


const showBodyLeft = ref(true)
type RadioType = 'd' | 'h' | 'm' | 's';
// 间隔区间
const numberGroupPopup = ref(false)
const numberAvg = ref()
const customNumberAvg = ref(12)

const customRadioType = ref<RadioType>('d')
const dRangeList = ref<any>([{number: 1}])
const dRangeString = ref('')
const hRangeList = ref<any>([{number: 1}])
const hRangeString = ref('')
const mRangeList = ref<any>([{number: 1}])
const mRangeString = ref('')
const sRangeList = ref<any>([{number: 1}])
const sRangeString = ref('')
const addNumber = ref()
const batchType = ref('batch')
const activeKey = ref('1')
const showAddBatch = ref(false)
const textAreaValue = ref('')
const stepForm = reactive({
  stepSize: 50,
  stepNum: 1
})

const dNumberChange = (number, index) => {
  if (number) {
    dRangeList.value.sort((a, b) => a.number - b.number);
  } else {
    dRangeList.value.splice(index, 1)
  }
}
const hNumberChange = (number, index) => {
  if (number) {
    hRangeList.value.sort((a, b) => a.number - b.number);
  } else {
    hRangeList.value.splice(index, 1)
  }
}
const mNumberChange = (number, index) => {
  if (number) {
    mRangeList.value.sort((a, b) => a.number - b.number);
  } else {
    mRangeList.value.splice(index, 1)
  }
}
const sNumberChange = (number, index) => {
  if (number) {
    sRangeList.value.sort((a, b) => a.number - b.number);
  } else {
    sRangeList.value.splice(index, 1)
  }
}
const addNumberChange = () => {
  if (addNumber.value) {
    if (customRadioType.value === 'd') {
      dRangeList.value.push(
          {
            number: addNumber.value
          }
      )
    } else if (customRadioType.value === 'h') {
      hRangeList.value.push(
          {
            number: addNumber.value
          }
      )
    } else if (customRadioType.value === 'm') {
      mRangeList.value.push(
          {
            number: addNumber.value
          }
      )
    } else if (customRadioType.value === 's') {
      sRangeList.value.push(
          {
            number: addNumber.value
          }
      )
    }
  }
  addNumber.value = null
}
const batchAdd = () => {
  if (customRadioType.value === 'd') {
    textAreaValue.value = dRangeList.value.map(item => item.number).join("\n")
  } else if (customRadioType.value === 'h') {
    textAreaValue.value = hRangeList.value.map(item => item.number).join("\n")
  } else if (customRadioType.value === 'm') {
    textAreaValue.value = mRangeList.value.map(item => item.number).join("\n")
  } else if (customRadioType.value === 's') {
    textAreaValue.value = sRangeList.value.map(item => item.number).join("\n")
  }
  showAddBatch.value = true
}

const numberCustomBatch = () => {
  const lines = textAreaValue.value.split('\n').map((line) => {
    const number = parseFloat(line);
    return Number.isNaN(number) ? null : number;
  });
  const numbers = lines.filter((number) => {
    return number !== null;
  }).sort((a, b) => a - b)
  const rangeLists = {
    'd': dRangeList,
    'h': hRangeList,
    'm': mRangeList,
    's': sRangeList
  };
  if (batchType.value === 'batch') {
    if (numbers.length > 0) {
      rangeLists[customRadioType.value].value = numbers.map(item => {
        return {
          number: item
        }
      })
    } else {
      rangeLists[customRadioType.value].value = [{number: 1}]
    }
  } else {
    const length = rangeLists[customRadioType.value].value.length;
    for (let i = 0; i < stepForm.stepNum; i++) {
      const newNumber = rangeLists[customRadioType.value].value[length - 1].number + (stepForm.stepSize * (i + 1));
      rangeLists[customRadioType.value].value.push({number: newNumber});
    }
  }
  showAddBatch.value = false
}

watch(dRangeList, (newValue, oldValue) => {
  if (dRangeList.value.length) {
    dRangeList.value.forEach((item, index) => {
      if (index === 0) {
        dRangeString.value = `(0,${item.number})`
      }
      if (dRangeList.value[index + 1]?.number) {
        dRangeString.value += `[${item.number},${dRangeList.value[index + 1].number}]`
      }
      if (index === dRangeList.value.length - 1) {
        dRangeString.value += `[${item.number},+∞)`
      }
    })
  }
}, {immediate: true})
watch(hRangeList, (newValue, oldValue) => {
  if (hRangeList.value.length) {
    hRangeList.value.forEach((item, index) => {
      if (index === 0) {
        hRangeString.value = `(0,${item.number})`
      }
      if (hRangeList.value[index + 1]?.number) {
        hRangeString.value += `[${item.number},${hRangeList.value[index + 1].number}]`
      }
      if (index === hRangeList.value.length - 1 && index === 0) {
        hRangeString.value += `[${item.number},${item.number})`
      }
      if (index === hRangeList.value.length - 1 && index !== 0) {
        hRangeString.value += `[${item.number},+∞)`
      }
    })
  }
}, {immediate: true})
watch(mRangeList, (newValue, oldValue) => {
  if (mRangeList.value.length) {
    mRangeList.value.forEach((item, index) => {
      if (index === 0) {
        mRangeString.value = `(0,${item.number})`
      }
      if (mRangeList.value[index + 1]?.number) {
        mRangeString.value += `[${item.number},${mRangeList.value[index + 1].number}]`
      }
      if (index === mRangeList.value.length - 1 && item.number < 60) {
        mRangeString.value += `[${item.number},60)`
      }
      if (index === mRangeList.value.length - 1 && item.number > 60) {
        mRangeString.value += `[${item.number},+∞)`
      }
    })
  }
}, {immediate: true})
watch(sRangeList, (newValue, oldValue) => {
  if (mRangeList.value.length) {
    mRangeList.value.forEach((item, index) => {
      if (index === 0) {
        sRangeString.value = `(0,${item.number})`
      }
      if (sRangeList.value[index + 1]?.number) {
        sRangeString.value += `[${item.number},${sRangeList.value[index + 1].number}]`
      }
      if (index === sRangeList.value.length - 1 && item.number < 3600) {
        sRangeString.value += `[${item.number},3600)`
      }
      if (index === sRangeList.value.length - 1 && item.number > 3600) {
        sRangeString.value += `[${item.number},+∞)`
      }
    })
  }
}, {immediate: true})
const cancelRoot = () => {
  numberGroupPopup.value = false
  // rootRef.value.hide()
}
</script>

<style scoped lang="less">
#intervalRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}


.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;

}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  text-align: right;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  //padding-bottom: 18px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-content::before, .right-content::after {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-content::after {
  right: -24px;
  left: auto;
}

.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}

.number-group-set-interval-root {
  display: block;
  width: auto;
  width: 360px;
  margin: -16px;
  cursor: auto !important;
  position: relative;

  .number-custom-range-container {
    max-height: 225px;
    overflow-y: auto;
  }

  .number-group-set-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px 12px;
    border-top: 1px solid var(--tant-border-color-border1-1);
  }

  .number-group-set-read {
    padding: 0 16px;

    .interval-range-wrap {
      display: flex;
      align-items: center;
      padding-bottom: 16px;
    }
  }

  .number-custom-range-batch-add {
    margin: 0 0 4px;
    padding: 0 16px;
    text-align: right;
  }

  .number-custom-range-display {
    max-width: 360px;
    max-height: 60px;
    padding: 0 16px;
    padding-bottom: 16px;
    overflow: auto;
    color: var(--tant-text-gray-color-text1-3);
    font: var(--tant-description-font-description-regular);
  }

  .number-custom-range-batch {
    position: absolute;
    top: 0;
    z-index: 99999;
    width: 300px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-bottom);

    .number-custom-range-batch-header {
      padding: 12px 16px 0;
    }

    .number-custom-range-batch-content {
      padding: 12px 16px 16px;
    }

    .number-custom-range-batch-footer {
      padding: 8px 16px;
      text-align: right;
      border-top: 1px solid var(--tant-border-color-border1-1);
    }
  }
}

</style>