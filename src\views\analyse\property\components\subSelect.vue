

<template>
    <!-- 计算公式 -->
    <div class="event-quotas-filter">
        <a-trigger
            v-model:popup-visible="selectVisible"
            trigger="click"
            :unmount-on-close="false"
            position="bl"
            :update-at-scroll="true"
            @click="handleSelectVisible">
            <div class="filter-btn">
                <span class="filter-label">{{ selectObject.eventAttrName }}</span>
            </div>
            <template #content>
                <div class="virtual-container">
                    <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
                        <a-input placeholder="请输入搜索" style="border: none;height: 40px;">
                            <template #prefix>
                            <icon-search />
                            </template>
                        </a-input>
                    </div>
                    <div class="dropdown-list">
                        <div v-for="(item,index) in typeList" :key="index" class="li-box">
                            <div class="li-title">
                                <div class="li-class">
                                    <div class="item-label">{{ item.typeName }}</div>
                                </div>
                            </div>
                            <div v-for="el in item.itemData" :key="el.name" class="li-content">
                                <a-trigger v-if="el.sub" :trigger="['hover']" position="right" :update-at-scroll="true">
                                    <div class="li-class" :class="{'li-active': selectObject.eventAttrName == el.name}" @click="sub1Change(el)">
                                        <div class="item-label">{{ el.name }}</div>
                                    </div>
                                    <template #content>
                                        <div class="trigger-box">
                                            <div class="card-header">
                                                <div class="card-header-container">
                                                    <div class="header-icon"><icon-launch/></div>
                                                    <div class="header-title">
                                                        <div class="name">111</div>
                                                    </div>
                                                    <div class="header-type">事件指标</div>
                                                </div>
                                                <div class="title-sub">222</div>
                                            </div>
                                            <div class="card-desc">
                                                <div class="span-desc">333</div>
                                            </div>
                                            <div class="card-footer">
                                                <div></div>
                                                <div class="action">
                                                    <a-tooltip content="前往属性详情" position="top">
                                                        <div class="action-icon">
                                                            <icon-launch/>
                                                        </div>
                                                    </a-tooltip>
                                                    
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </a-trigger>
                                <div v-else class="li-class" :class="{'li-active': selectObject.eventAttrName == el.name}" @click="sub1Change(el)">
                                    <div class="item-label">{{ el.name }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </a-trigger>
        <span v-if="selectObject.eventAttrName !='用户数'" style="margin: 0 4px;">的</span>
        <a-dropdown v-if="selectObject.eventAttrName !='用户数'" @select="handleSelect">
            <div class="filter-btn">
                <span class="filter-label">{{ selectObject.statisticalName }}</span>
            </div>
            <template #content>
                <a-doption v-for="(item,index) in unitTypeList" :key="index" :value="item.value" :style="selectObject.statisticalType === item.value ? {backgroundColor: 'var(--tant-secondary-color-secondary-fill)'} : {}">{{ item.name }}</a-doption>
            </template>
        </a-dropdown>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {cloneDeep} from "lodash"

const selectVisible = ref(false)
const emits = defineEmits(['subChange']);

const handleSelectVisible = () => {
    selectVisible.value = !selectVisible.value;

}
const props = defineProps({
    panelData: {
      type: Object,
      default() {
        return {};
      },
    }
})
const selectObject = ref({
    eventAttrCode:props.panelData.eventAttrCode || '',
    eventAttrName:props.panelData.eventAttrName || '',
    statisticalType:props.panelData.statisticalType || '',
    statisticalName:props.panelData.statisticalName || '',
})
watch(() => props.panelData, (newVal) => {
    selectObject.value.eventAttrCode = newVal.eventAttrCode || '';
    selectObject.value.eventAttrName = newVal.eventAttrName || '';
    selectObject.value.statisticalType = newVal.statisticalType || '';
    selectObject.value.statisticalName = newVal.statisticalName || '';
}, { immediate: true });
const unitList = ref<any>([
    {
        value:'sum',
        name:'总和'
    },
    {
        value:'avg',
        name:'均值'
    },
    {
        value:'per_capita',
        name:'人均值'
    },
    {
        value:'median',
        name:'中位数'
    },
    {
        value:'max',
        name:'最大值'
    },
    {
        value:'min',
        name:'最小值'
    },
    {
        value:'distinct',
        name:'去重数'
    },
    {
        value:'variance',
        name:'方差'
    },
    {
        value:'std_dev',
        name:'标准差'
    },
    {
        value:'pct_99',
        name:'99分位数'
    },
    {
        value:'pct_95',
        name:'95分位数'
    },
    {
        value:'pct_90',
        name:'90分位数'
    },
    {
        value:'pct_80',
        name:'80分位数'
    },
    {
        value:'pct_75',
        name:'75分位数'
    },
    {
        value:'pct_70',
        name:'70分位数'
    },
    {
        value:'pct_60',
        name:'60分位数'
    },
    {
        value:'pct_40',
        name:'40分位数'
    },
    {
        value:'pct_30',
        name:'30分位数'
    },
    {
        value:'pct_25',
        name:'25分位数'
    },
    {
        value:'pct_20',
        name:'20分位数'
    },
    {
        value:'pct_10',
        name:'10分位数'
    },
    {
        value:'pct_5',
        name:'5分位数'
    }
])
const unitTypeList = ref<any>([])
const typeList = ref<any>([
    {
        typeName:'预置计算方法',
        itemData:[
            {
                name:'用户数'
            },
        ]
    },
    {
        typeName:'用户属性',
        itemData:[
            {
                name:'时区偏移',
                sub:'sdfs',
                type:'number',
                desc:'暂无备注',
            },
            {
                name:'区服ID',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'VIP等级',
                sub:'sdfs',
                type:'number',
                desc:'暂无备注',
            },
            {
                name:'角色等级',
                sub:'sdfs',
                type:'number',
                desc:'暂无备注',
            },
            {
                name:'在线时长',
                sub:'sdfs',
                type:'number',
                desc:'暂无备注',
            },
            {
                name:'生命周期天数',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'卡牌ID列表',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'卡牌品质列表',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'卡牌类型列表',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'事件名称',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'城市_账户ID',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'省份_账户ID',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
        ]
    },
    {
        typeName:'用户标签',
        itemData:[
            {
                name:'付费RMF标签',
                sub:'sdfs',
                type:'string',
                desc:'暂无备注',
            },
            {
                name:'用户的付费分层标签',
                type:'string',
                sub:'sdfs',
                desc:'暂无备注',
            }
        ]
    }
])
const handleSelect = (v) => {
    selectObject.value.statisticalType = v
    unitList.value.forEach(item => {
        if(item.value === v) {
            selectObject.value.statisticalName = item.name
        }
    })
    emits('subChange',selectObject.value)
}
const sub1Change = (v) => {
    selectObject.value.eventAttrName = v.name
    const arr = typeList.value[0].itemData.map(item => item.name)
    const list = cloneDeep(unitList.value)
    if(v?.type === 'number') {
        unitTypeList.value = [
            {
                value:'distinct',
                name:'去重数'
            }
        ]
    }else{
        unitTypeList.value = list
    }
    if(arr.includes(v.name)){
        selectObject.value.statisticalType = ''
        selectObject.value.statisticalName = ''
    }else{
        selectObject.value.statisticalType = v?.type === 'number' ? 'distinct' : 'sum'
        selectObject.value.statisticalName = v?.type === 'number' ? '去重数' : '总和'
    }
    selectVisible.value = false
    emits('subChange',selectObject.value)
}



</script>


<style scoped lang="less">
.filter-btn{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon{
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}
:deep(.week-icon):hover{
    color: var(--tant-primary-color-primary-active);
}
.event-quotas-filter{
    display: flex;
    align-items: center;
    .content{
        height: calc(100% - 96px);
        padding: 16px 0;
        background: #fff;
    }
}
.virtual-container{
    min-width: 100px;
    max-width: 240px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 16px 40px rgba(10, 16, 50, .05), 0 6px 20px rgba(10, 16, 50, .03), 0 0 5px rgba(10, 16, 50, .02);
    .dropdown-list{
        height: 256px;
        overflow: auto;
        .li-box{
            &:not(:last-of-type){
                border-bottom: 1px solid var(--tant-border-color-border1-1);
            }
            .li-title,.li-content{
                width: 100%;
                height: 32px;
                padding: 4px 4px 0;
                margin-bottom: 8px;
                cursor: default;
                .li-class{
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    padding: 8px 8px 0;
                    color: var(--tant-text-gray-color-text1-3);
                    font-weight: 500;
                    font-size: 12px;
                    border-radius: 4px;
                    &:hover{
                        background-color: var(--tant-secondary-color-secondary-fill);
                    }
                    .item-label{
                        flex: 1 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                
            }
            .li-content{
                height: 42px;
                cursor: pointer;
                .li-class{
                    padding: 8px;
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    border-radius: 4px;
                    &:hover{
                        background-color: var(--tant-secondary-color-secondary-fill);
                    }
                }
                
            }
            .li-active{
                background-color: var(--tant-secondary-color-secondary-fill);
            }
        }
    }
}
.trigger-box{
    display: flex;
    flex-direction: column;
    width: 280px;
    max-height: 360px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-small-shadow-small-overall);
    .card-header{
        padding: 12px;
        background-color: var(--tant-bg-gray-color-bg2-1);
        border-radius: 4px 4px 0 0;
        .card-header-container{
            display: flex;
            flex-direction: row;
            .header-icon{
                margin-right: 5px;
                color: var(--tant-text-gray-color-text1-1);
            }
            .header-title{
                flex-grow: 1;
                color: var(--tant-text-gray-color-text1-1);
                .name{
                    display: inline-block;
                    width: 100%;
                    padding: 0 4px;
                    word-break: break-all;
                    border-radius: 4px;
                    color: var(--tant-text-gray-color-text1-1);
                    font-weight: 500;
                    line-height: 22px;
                }
                
            }
            .header-type{
                flex-shrink: 0;
                width: 62px;
                margin-left: 6px;
                color: var(--tant-text-gray-color-text1-3);
                font-weight: 400;
                font-size: 12px;
                text-align: right;
            }
        }
        .title-sub{
            margin-left: 24px;
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            word-break: break-all;
        }
    }
    .card-desc{
        flex-grow: 1;
        height: 130px;
        padding: 12px 8px;
        overflow-y: auto;
        color: var(--tant-text-gray-color-text1-2);
        font-weight: 400;
        font-size: 14px;
        .span-desc{
            display: inline-block;
            width: 100%;
            padding: 0 4px;
            word-break: break-all;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    }
    .card-footer{
        display: flex;
        flex-shrink: 0;
        justify-content: space-between;
        height: 34px;
        padding-top: 6px;
        padding-right: 12px;
        padding-left: 12px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        border-radius: 0 0 4px 4px;
        box-shadow: inset 0 1px #e6e6e6;
        .action{
            .action-icon{
                display: inline-block;
                min-width: 24px;
                height: 24px;
                padding: 0 4px;
                line-height: 22px;
                text-align: center;
                background-color: transparent;
                border-radius: 2px;
                cursor: pointer;
                transition: background-color .3s;
            }
            .action-icon:hover {
                background-color: var(--tant-primary-color-primary-fill-hover);
                color: var(--tant-primary-color-primary-default);
            }
        }
    }
}
</style>