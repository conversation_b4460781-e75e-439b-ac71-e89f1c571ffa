<template>
  <div class="content">
    <img src="/sketchy/18.png" width="40%" alt="">
    <div class="operation-row">
      <a-button key="back" type="primary" size="large" @click="back"> 返回首页 </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {useRouter} from 'vue-router';
import {DEFAULT_ROUTE_NAME} from "@/router/routes";

const router = useRouter();

const back = () => {
  // warning： Go to the node that has the permission
  router.push({name: DEFAULT_ROUTE_NAME});
};

</script>

<style scoped lang="less">
.content {
  position: absolute;
  top: 20%;
  width: 100%;
  text-align: center;
}

.operation-row {
  padding-top: 60px;
}
</style>
