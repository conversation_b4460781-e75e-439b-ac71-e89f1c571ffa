<script setup lang="ts">
import {defineEmits, defineExpose, ref} from 'vue';
import {delAnalyseFavoriteList, getAnalyseFavoriteList} from "@/api/analyse/api";
import {Message} from '@arco-design/web-vue';


const emit = defineEmits<{
  (event: 'bookmark', data: any): void;
}>();

const data = ref([]);

const refreshData = async ()=>{
  await getAnalyseFavoriteList().then(res => {
    data.value = res.map((item,index) => {
      return {
        title:item.name,
        sql:item.queryParam.sql,
        favorite_code:item.favoriteCode,
        index
      }
    })
  })

}
defineExpose({
  refreshData
})

const bookmarkEmit = (value:any)=>{
  emit('bookmark',value)
}
const deleteOne = (value:string)=>{
  delAnalyseFavoriteList(value).then(()=>{
    Message.success('删除成功')
    refreshData()
  }).catch(()=>{
    Message.error('请检查网络')
  })
}


</script>

<template>
<div style="margin: 20px 25px 45px 25px;">
  <div style="display: flex;justify-content: space-between;">
    <div>
      共 {{data.length}} 条书签 <br><br>
    </div>
    <div>
      <a-input-search placeholder="请输入搜索" :style="{width:'180px'}">
      </a-input-search>
    </div>

  </div>

  <a-table  :data="data" >
    <template #columns>
      <a-table-column title="序号" data-index="rowIndex" min-width="100" align="center">
        <template #cell="{ record }">
          {{record.index +1}}
        </template>
      </a-table-column>
      <a-table-column title="书签名" data-index="title" min-width="200" align="left"></a-table-column>
      <a-table-column title="书签内容" data-index="sql" min-width="800" align="left"></a-table-column>
      <a-table-column title="操作" min-width="100">
        <template #cell="{ record }">
          <a-tooltip content="设置" position="top">
            <icon-settings  :style="{fontSize:'16px'}" style="cursor: pointer;" @click="bookmarkEmit(record.sql)"/>
          </a-tooltip>
          <a-tooltip content="删除" position="top">
            <icon-delete :style="{fontSize:'16px'}" style="margin-left: 10px;cursor: pointer;" @click="deleteOne(record.favorite_code)"/>
          </a-tooltip>
        </template>
      </a-table-column>
    </template>
  </a-table>
</div>
</template>

<style scoped lang="less">

</style>