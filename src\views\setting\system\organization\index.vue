<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchData">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="()=>editModalShow=true">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #apps="{ record }" style="display:flex">
          <div v-if="record.apps?.indexOf('all') >= 0">
            <a-tag>
              全部应用
            </a-tag>
          </div>
          <div v-else>
            <a-tag v-for="(app,index) in record.apps" :key="index" style="margin-right:5px;">
              {{ app }} - {{ appData.find(item => item.code === app)?.name }}
            </a-tag>
          </div>
        </template>
        <template #optional="{ record }">
          <a-button type="text" @click="editData(record)">编辑</a-button>
          <a-popconfirm v-if="record.code !='admin'" :content="`确认删除“${record.name}”？该操作不可恢复！`" type="warning" position="tr" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :visible="editModalShow"
        title-align="start"
        :title="formData.code?'编辑应用分组':'新增应用分组'"
        :ok-text="formData.code?'更新':'保存'"
        @cancel="editCancel"
        @ok="editConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-form-item v-show="formData.code" :required="formData.code"  field="code" label="应用分组编码">
          <a-input v-model="formData.code" disabled/>
        </a-form-item>
        <a-form-item field="name" :rules="[{required:true,message:'应用分组名称不能为空'}]" label="应用分组名称">
          <a-input v-model="formData.name" placeholder="请输入应用分组名称"/>
        </a-form-item>
        <a-form-item field="status" label="应用分组状态">
          <a-switch v-model="formData.status" :disabled="formData.code =='admin'" :checked-value="1" :unchecked-value="0"/>
        </a-form-item>
        <a-form-item field="status" :rules="[{required:true,message:'请选择上级组织'}]" label="上级组织">
          <a-tree-select
              v-model:model-value="formData.parentCode"
              allow-clear
              allow-search
              :filter-tree-node="(searchValue, nodeData) =>  nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1"
              dropdown-class-name="menu-tree-select-wrapper"
              placeholder="请选择上级组织"
              :data="memberGroupData"
          />
        </a-form-item>
        <a-form-item field="apps" label="应用权限" tooltip="输入关键词搜索">
          <a-select
              v-if="formData.code =='admin'"
              model-value="all"
              disabled
              allow-clear
              multiple
              :max-tag-count="3"
              tree-checkable
              placeholder="请选择应用权限"
          >
            <a-option value="all">所有应用</a-option>
          </a-select>
          <a-select
              v-else
              v-model:model-value="formData.apps"
              allow-clear
              :loading="selectedAppDataLoading"
              :allow-search="true"
              :filter-option="false"
              :options="selectedAppData"
              multiple
              :max-tag-count="3"
              tree-checkable
              placeholder="请选择应用权限"
              @search="(searchValue) => {
                selectedAppDataLoading = true
                if(_.isEmpty(searchValue)) {
                  selectedAppData = []
                  selectedAppDataLoading = false
                  return
                }
                selectedAppData = appData.filter(item => item.code.toLowerCase().indexOf(searchValue.toLowerCase()) > -1 ||  item.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1)
                ?.map(item => {
                  return {
                    value: item.code,
                    label: item.code+'-'+item.name
                  }
                });
                selectedAppDataLoading = false
              }"
          >
            <a-option value="all">所有应用</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="note" label="应用分组备注">
          <a-textarea v-model="formData.note" placeholder="请输入应用分组备注"/>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getSystemMemberGroup, getSystemMemberGroupTree, removeSystemMemberGroup, saveSystemMemberGroup} from "@/api/setting/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {getAppPageList} from "@/api/marketing/api";
import {useRoute} from "vue-router";

const route = useRoute();
const data = ref<any>([]);
const appData = ref<any>([]);
const selectedAppData = ref<any>([]);
const selectedAppDataLoading = ref<boolean>(false);
const memberGroupData = ref<any>([]);
const loading = ref<boolean>(true);
const searchCode = ref<string>();
const searchName = ref<string>();
const formData = ref<any>({});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const columns = ref<any>([
  {
    title: '名称',
    dataIndex: 'name',
    width: 160,
    fixed: 'left',
  },
  {
    title: '上级组织',
    dataIndex: 'parentCode',
    width: 160,
    render: (value) => {
      const {record} = value;
      return data.value.filter((item: any) => item.code == record.parentCode)?.[0]?.name
    }
  },
  {
    title: '备注',
    dataIndex: 'note',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    render: (value) => {
      const {record} = value;
      return record.status ? '启用' : '禁用'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '应用权限',
    slotName: 'apps',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    width: 160,
    fixed: 'right',
  },
]);

const getChildrenInfo = (group) => {
  if (group && group.children) {
    return group.children.map(child => {
      return {
        ...child,
        key: child.code,
        title: child.name,
        children: getChildrenInfo(child)
      }
    })
  }
  return []
}

const refreshMemberGroupTree = () => {
  getSystemMemberGroupTree().then(res => {
    memberGroupData.value = res?.map(item => {
      return {
        ...item,
        key: item.code,
        title: item.name,
        children: getChildrenInfo(item)
      }
    }) || []
  })
}

onMounted(() => {
  getAppPageList({
    pageSize: 10000,
    current: 1,
  }).then(res => {
    appData.value = res?.items || []
  })
  refreshMemberGroupTree()
  searchData()
})

const searchData = () => {
  const pageParams = {
    code: searchCode.value,
    name: searchName.value,
  };
  loading.value = true
  getSystemMemberGroup(pageParams).then(res => {
    data.value = res
  }).finally(() => {
    loading.value = false
  })
}

const editData = (record: any) => {
  formData.value = {...record};
  editModalShow.value = true;
}

const formSubmit = (value) => {
  if (_.isEmpty(value)) {
    Message.warning("应用分组数据不能为空！")
  }
  saveSystemMemberGroup(value).then(res => {
    Message.info("应用分组数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    searchData()
    refreshMemberGroupTree()
  }).catch(e => {
    Message.error("应用分组数据保存失败！", e)
  })
}

const editConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}

const deleteData = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除应用分组id不能为空！")
  }
  removeSystemMemberGroup(code).then(res => {
    Message.info("应用分组数据删除成功！")
    searchData()
    refreshMemberGroupTree()
  }).catch(e => {
    Message.error("应用分组数据删除失败！", e)
  })
}
</script>

<style scoped lang="less">

</style>