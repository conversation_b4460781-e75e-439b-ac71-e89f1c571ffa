<template>
  <a-select
    v-model:model-value="appIdList"
    multiple
    :style="{ width: props.width, height: props.height }"
    :max-tag-count="1"
    :loading="selectLoading"
    :show-extra-options="false"
    :filter-option="false"
    allow-search
    placeholder="请选择应用"
    @search="handleSearch"
    @dropdown-reach-bottom="handleReachBottom"
    @change="handleChange"
    @focusin="selectFocusin"
    @remove="removeChange"
    @popup-visible-change="popupVisibleChange"
  >
    <template #search-icon>
      <div class="select-suffix">
        <div class="select-suffix-item">
          <a-tag v-if="!selectAll" class="tag-btn" @click="selectAllClick"> 全选 </a-tag>
          <a-tag v-else class="tag-btn" bordered color="green" @click="selectAllClick"> 全选 </a-tag>
          <a-tag v-if="appIdList?.length > 0" class="tag-btn" color="orange" style="margin-left: 3px" @click="clearAllClick"> 清空 </a-tag>
        </div>
      </div>
    </template>
    <template #label="{ data }">
      <div class="game-select">
        <a-avatar shape="square" :size="20">
          <img :src="appDataList?.[0]?.icon || '/image/game-default.svg'" alt="" />
        </a-avatar>
        <span class="game-name">{{ data?.label }}</span>
      </div>
    </template>
    <div v-if="notMatchApp?.length > 0" class="select-content">
      <a-option v-for="item in notMatchApp" :key="item?.code" :value="item?.code" :tag-props="{ icon: item?.icon || '/image/game-default.svg' }">
        <div class="select-item">
          <a-avatar shape="square" :size="20">
            <img :src="item?.icon || '/image/game-default.svg'" alt="" />
          </a-avatar>
          <span class="game-name">{{ item?.code }}-{{ item?.name }}</span>
        </div>
      </a-option>
    </div>
    <a-option v-for="(item, index) in sortedGameAppList" :key="item.code" :value="item.code" :tag-props="{ icon: item?.icon || '/image/game-default.svg' }">
      <div class="select-item">
        <a-avatar shape="square" :size="20">
          <img :src="item?.icon || '/image/game-default.svg'" alt="" />
        </a-avatar>
        <span class="game-name">{{ item.code }}-{{ item.name }}</span>
      </div>
    </a-option>
    <div v-if="bottomLoading" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-bottom: 10px">
      <a-spin :size="8" style="margin-top: 4px" dot />
    </div>
  </a-select>
</template>

<script lang="ts" setup>
  import { computed, reactive, ref, watch } from 'vue';
  import { getAppPageList } from '@/api/marketing/api';
  import { useEventBus, useSessionStorage } from '@vueuse/core';
  import { LocalStorageEventBus } from '@/types/event-bus';
  import { sortSelectedFirst } from '@/utils/sortUtil';
  import _ from 'lodash';

  const props = defineProps({
    width: {
      type: String,
      default: '320px',
    },
    height: {
      type: String,
      default: '32px',
    },
  });

  const pageParams = reactive({
    current: 1,
    pageSize: 10,
    text: '',
    appStatus: undefined,
    osName: undefined,
  });

  const bottomLoading = ref(false);
  const selectLoading = ref(false);
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const emits = defineEmits(['change']);
  // app列表
  const gameAppList = ref<any>([]);

  // 选中app
  const appIdList = useSessionStorage('overview-app-id-list', []);
  const appDataList = useSessionStorage('overview-app-data-list', []);

  // 搜索相关状态
  const searchText = ref('');

  // 非列表的App
  const notMatchApp = computed(() => {
    const list = gameAppList.value.concat(appDataList.value);
    const gameAppIdList = gameAppList.value?.map((item) => item?.code) || [];
    return list?.filter((item) => gameAppIdList.indexOf(item?.code) < 0);
  });
  // 排序后的游戏应用列表
  const sortedGameAppList = ref<any>([]);
  // 获取过滤后的应用列表（根据搜索条件）
  const getFilteredApps = () => {
    if (!searchText.value || !gameAppList.value) return gameAppList.value || [];

    const searchValue = searchText.value.toLowerCase();
    return gameAppList.value.filter((item: any) => {
      const name = item.name?.toLowerCase() || '';
      const code = item.code?.toLowerCase() || '';
      return name.includes(searchValue) || code.includes(searchValue);
    });
  };

  // 计算是否全选（基于当前搜索结果和分页数据）
  const selectAll = computed(() => {
    const filteredApps = getFilteredApps();
    const notMatchApps = notMatchApp.value || [];
    // 合并当前可见的所有应用
    const allVisibleApps = [...filteredApps, ...notMatchApps];
    if (!allVisibleApps || allVisibleApps.length === 0) return false;
    const allVisibleValues = allVisibleApps.map((item: any) => item.code);
    return allVisibleValues.every((value: string) => appIdList.value.includes(value));
  });

  // 全选点击处理
  const selectAllClick = () => {
    const filteredApps = getFilteredApps();
    const notMatchApps = notMatchApp.value || [];
    // 合并当前可见的所有应用（搜索结果 + 不匹配的应用）
    const allVisibleApps = [...filteredApps, ...notMatchApps];
    const allVisibleValues = allVisibleApps.map((item: any) => item.code);
    if (selectAll.value) {
      // 取消选中当前可见的所有应用
      const newValue = appIdList.value.filter((x: string) => !allVisibleValues.includes(x));
      appIdList.value = newValue;
    } else {
      // 选中当前可见的所有应用
      const newValue = [...new Set([...appIdList.value, ...allVisibleValues])];
      appIdList.value = newValue;
    }
    // 更新appDataList
    const list = gameAppList.value.concat(appDataList.value).filter((item) => item?.code);
    const appData =
      appIdList.value
        ?.map((appId) => {
          return list?.find((item) => item.code === appId);
        })
        .filter(Boolean) || [];

    appDataList.value = appData;
    localStorage.setItem('overview-app-id-list', JSON.stringify(appData?.map((app) => app.code)));
    localStorage.setItem('overview-app-data-list', JSON.stringify(appData));
    localStorageEventBus.emit('overview-app-id-list', appIdList.value);
    localStorageEventBus.emit('overview-app-data-list', appData);
  };
  // 清空点击处理
  const clearAllClick = () => {
    appIdList.value = [];
    appDataList.value = [];
    localStorage.setItem('overview-app-id-list', JSON.stringify([]));
    localStorage.setItem('overview-app-id-list', JSON.stringify([]));
    localStorageEventBus.emit('overview-app-id-list', []);
    localStorageEventBus.emit('overview-app-id-list', []);
  };

  const getList = async (loading) => {
    loading.value = true;
    try {
      const res = await getAppPageList(pageParams);
      gameAppList.value = res?.items || [];
      return res;
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = (v) => {
    searchText.value = v;
    pageParams.text = v;
    getList(selectLoading);
    getList(selectLoading).then(() => {
      // 搜索完成后重新排序
      sortedGameAppList.value = sortSelectedFirst(gameAppList.value, appIdList.value);
    });
  };

  const handleReachBottom = () => {
    if (pageParams.pageSize <= gameAppList.value.length) {
      pageParams.pageSize += 10;
      getList(bottomLoading).then(() => {
        // 数据加载完成后重新排序
        sortedGameAppList.value = sortSelectedFirst(gameAppList.value, appIdList.value);
      });
    }
  };

  const selectFocusin = async () => {
    // gameAppList为空则初始化数据，减少初始化的请求次数
    if (_.isEmpty(gameAppList.value)) {
      await getList(selectLoading);
      sortedGameAppList.value = sortSelectedFirst(gameAppList.value, appIdList.value);
    }
    if (!_.isEmpty(pageParams.text)) {
      // 搜索值不为空时，重置搜索
      searchText.value = '';
      pageParams.text = '';
      pageParams.pageSize = 10;
      await getList(selectLoading);
      sortedGameAppList.value = sortSelectedFirst(gameAppList.value, appIdList.value);
    }
  };

  const popupVisibleChange = (v) => {
    if (!v) {
      emits('change', appIdList.value);
    } else {
      // 下拉打开时，对gameAppList进行排序，已选中的排在前面
      sortedGameAppList.value = sortSelectedFirst(gameAppList.value, appIdList.value);
    }
  };
  const removeChange = () => {
    emits('change', appIdList.value);
  };
  const handleChange = (v) => {
    appIdList.value = v || [];
    const list = gameAppList.value.concat(appDataList.value).filter((item) => item?.code);
    const appData =
      v
        ?.map((appId) => {
          return list?.find((item) => item.code === appId);
        })
        .filter(Boolean) || [];
    appDataList.value = appData;
    localStorage.setItem('overview-app-id-list', JSON.stringify(appData?.map((app) => app.code)));
    localStorage.setItem('overview-app-data-list', JSON.stringify(appData));
    localStorageEventBus.emit('overview-app-id-list', appIdList.value);
    localStorageEventBus.emit('overview-app-data-list', appData);
  };
  // 监听appIdList的变化，当它发生变化时重新初始化
  watch(
    () => appIdList.value,
    (newAppIdList, oldAppIdList) => {
      // 只有当新的appIdList不为空且与旧的不同时才重新初始化
      if (!_.isEmpty(newAppIdList) && !_.isEqual(newAppIdList, oldAppIdList)) {
        // 检查是否需要重新初始化（appIdList和appDataList是否匹配）
        const isMatch = newAppIdList.every((appId) => appDataList.value.some((appData) => appData.code === appId));
        // 如果不匹配，重新初始化
        if (!isMatch) {
          // init();
        }
      }
    },
    { deep: true } // 深度监听数组变化
  );
</script>

<style scoped lang="less">
  .game-name {
    padding-left: 8px;
    margin-bottom: 4px;
  }

  .select-content {
    padding-bottom: 8px;
    border-bottom: dashed 1px var(--color-neutral-4);
  }

  .game-select {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 全选功能样式
  .select-suffix {
    display: flex;

    .select-suffix-item {
      margin-left: 8px;
      height: 24px;

      .tag-btn {
        width: 30px;
        height: 16px;
        font-size: 10px;
        padding: 4px;
        cursor: pointer;
        user-select: none;
      }
    }
  }
</style>
