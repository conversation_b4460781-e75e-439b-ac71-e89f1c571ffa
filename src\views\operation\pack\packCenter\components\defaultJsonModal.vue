<template>
    <a-modal v-model:visible="modalVisible" :width="600" title-align="start" title="default.json" :footer="false" @cancel="closeModal">
        <a-spin :style="{display: 'block'}" :loading="loading">
            <div class="content">
                <a-textarea disabled :style="{height: '550px'}"></a-textarea>
            </div>
        </a-spin>
        <div class="footer">
            <a-button :style="{'margin-right': '10px'}" class="cancel" @click="closeModal">关闭</a-button>
            <a-button type="primary" @click="">
                去编辑
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const loading = ref(false)

const openModal = async () => {
    modalVisible.value = true
    loading.value = true

    setTimeout(function () {
        loading.value = false
    }, 2000)

}
const closeModal = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.modal-title{
    font-size: 20px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 16px;
}

.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>
