<template>
    <!-- 批量复制广告 -->
    <a-modal v-model:visible="modalVisible" :width="700" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="scroll-container">
            <a-alert>仅支持复制系统创建且使用系统素材库素材的广告，当前已选中20个广告，自动过滤掉0个不符要求的广告和415个非系统创建的广告，您希望将广告复制到</a-alert>
            <a-form ref="formRef" :rules="rules" :model="form" style="margin-top: 12px;min-height: 300px;">
                <a-form-item field="accountType" label="账户">
                    <a-radio-group v-model="form.accountType" type="button" @change="handleTypeChange">
                        <a-radio value="old">原账户</a-radio>
                        <a-radio value="new">新账户</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item v-if="form.accountType === 'new'" field="accountId" label="新账户">
                    <a-select
                        v-model:model-value="form.accountId"
                        placeholder="请选择"
                        allow-clear>
                    </a-select>
                </a-form-item>
                <a-form-item v-if="form.accountType === 'new'" field="fbList" label="FB个人号">
                    <a-table
                        :columns="columns"
                        :data="form.fbList"
                        :hoverable="true"
                        sticky-header
                        :pagination="false"
                    >
                        <template #fbId="{ rowIndex }">
                            <a-select v-model="form.fbList[rowIndex].fbId">
                            </a-select>
                        </template>
                    </a-table>
                </a-form-item>
                <a-form-item field="campaignType" label="广告系列">
                    <a-radio-group v-model="form.campaignType" type="button" @change="handleCampaignChange">
                        <a-radio value="old" :disabled="form.accountType === 'new'">原广告系列</a-radio>
                        <a-radio value="new">新广告系列</a-radio>
                        <a-tooltip content="请确保多个广告创意的广告系列目标一致且进阶赋能型广告开关一致">
                            <a-radio value="has" :disabled="form.accountType === 'new'">已有广告系列</a-radio>
                        </a-tooltip>
                    </a-radio-group>
                </a-form-item>
                <a-form-item v-if="form.campaignType === 'has'" field="campaignId">
                    <a-select
                        v-model:model-value="form.campaignId"
                        placeholder="请选择"
                        allow-clear>
                    </a-select>
                </a-form-item>
                <a-form-item field="adsetType" label="广告组">
                    <a-radio-group v-model="form.adsetType" type="button">
                        <a-tooltip :content="form.accountType === 'new'? '选择新账户时不支持选原广告组' :'包含进阶赋能型广告不能选择原广告系列'">
                            <a-radio value="old" :disabled="form.accountType === 'new' || form.campaignType != 'old'">原广告组</a-radio>
                        </a-tooltip>
                        <a-radio value="new">新广告组</a-radio>
                        <a-tooltip content="广告系列选择新广告系列不支持选已有广告组">
                            <a-radio value="has" :disabled="form.accountType === 'new' || form.campaignType === 'new'">已有广告组</a-radio>
                        </a-tooltip>
                    </a-radio-group>
                </a-form-item>
                <a-form-item v-if="form.adsetType === 'has'" field="adsetId">
                    <a-select
                        v-model:model-value="form.adsetId"
                        placeholder="请选择"
                        allow-clear>
                    </a-select>
                </a-form-item>
                <a-form-item v-if="form.campaignType === 'new'" field="newCampaignName" label="新广告系列名称">
                    <a-input v-model="form.newCampaignName"/>
                </a-form-item>
                <a-form-item v-if="form.adsetType === 'new'" field="newAdsetName" label="新广告组名称">
                    <a-input v-model="form.newAdsetName"/>
                </a-form-item>
                <a-form-item field="numbers" label="复制数量">
                    <a-input-number v-model="form.numbers" :min="1"/>
                </a-form-item>
            </a-form>
        </div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('复制广告')
const form = reactive({
    accountType:'old',
    campaignType:'old',
    numbers:1,
    accountId:'',
    campaignId:'',
    fbList:[
        {
            accountName:'',
            fbId:''
        }
    ],
    newCampaignName:'',
    adsetType:'old',
    adsetId:'',
    newAdsetName:''
})
const columns = [
    {
        title: '账户',
        dataIndex: 'accountName',
    },
    {
        title: 'FB个人号ID',
        dataIndex: 'fbId',
        slotName: 'fbId',
    },
]
const rules = {
    accountId: [
        {
            required: true,
            message:'请选择账户',
        }
    ],
    fbList: [
        {
            required: true,
            message:'请选择FB个人号',
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const handleTypeChange = (v) => {
    if(v === 'new'){
        form.campaignType = 'new'
        form.adsetType = 'new'
    }
}
const handleCampaignChange = (v) => {
    if(v === 'new'){
        form.adsetType = 'new'
    }
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.scroll-container{
    max-height: 500px;
    overflow-y: auto;
}
</style>