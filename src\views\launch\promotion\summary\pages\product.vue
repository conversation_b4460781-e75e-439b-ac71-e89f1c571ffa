<template>
  <div class="page-content">
    <div class="setting">
      <div class="label">筛选</div>
      <div class="setting-content">
        <a-select placeholder="请选择产品" allow-clear multiple :max-tag-count="1" style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>产品-{{ data?.label }}</span>
          </template>
        </a-select>
        <a-select placeholder="请选择产品类型" allow-clear multiple :max-tag-count="1" style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>产品类型-{{ data?.label }}</span>
          </template>
          <a-option value="1">iOS</a-option>
          <a-option value="2">Android</a-option>
          <a-option value="3">其他</a-option>
        </a-select>
        <!-- <a-select
                    placeholder="请选择渠道"
                    allow-clear
                    multiple
                    :max-tag-count="1"
                    style="width: 240px;margin-right: 24px;">
                    <template #label="{ data }">
                      <span>渠道-{{ data?.label }}</span>
                    </template>
                    <a-option value="1">Meta</a-option>
                    <a-option value="2">Mintegral</a-option>
                </a-select> -->
        <a-select placeholder="请选择地区" allow-clear multiple :max-tag-count="1" style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>地区-{{ data?.label }}</span>
          </template>
        </a-select>
      </div>
    </div>
    <a-divider />
    <div class="table-wrap">
      <div class="wrap-left">
        <div class="text">详细数据</div>
      </div>
      <div class="wrap-right">
        <a-select placeholder="请选择细分数据" allow-clear style="width: 240px">
          <template #label="{ data }">
            <span>细分数据-{{ data?.label }}</span>
          </template>
          <a-option value="1">日期</a-option>
          <a-option value="2">地区</a-option>
          <a-option value="3">渠道</a-option>
          <a-option value="4">版位</a-option>
        </a-select>
        <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
          <icon-question-circle style="color: var(--color-text-2); margin-left: 2px" />
        </a-tooltip>
        <a-button class="br4" style="margin-left: 12px" @click="getList">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>刷新</template>
        </a-button>
        <a-button class="br4" style="margin-left: 12px">
          <template #icon>
            <icon-download />
          </template>
          <template #default>导出</template>
        </a-button>
      </div>
    </div>
    <div class="table-area">
      <a-table :columns="columns" :loading="loading" :data="tableData" :bordered="false" :hoverable="true" sticky-header :table-layout-fixed="true" :filter-icon-align-left="true" :column-resizable="true" :scroll="scroll" :pagination="false" :summary="true">
        <template #productName="{ record }">
          <div class="cell-content">
            <div class="text-link" @click="linkAdset(record)">{{ record.productName }}</div>
            <div>
              <a-trigger trigger="hover" position="rt">
                <icon-down-circle style="cursor: pointer" />
                <template #content>
                  <div class="trigger-content">
                    <div class="tooltip-icon-list-header"> 数据下钻 </div>
                    <div class="dropdown-list-content">
                      <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                        <div class="dropdown-list-item" @click="openDrill(record, item.value)">{{ item.label }}</div>
                      </div>
                    </div>
                  </div>
                </template>
              </a-trigger>
            </div>
          </div>
        </template>
        <template #summary-cell="{ column, record }">
          <div v-if="column.dataIndex === 'productName'">
            <div class="text">汇总</div>
          </div>
          <div v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</div>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange" />
      </div>
    </div>
    <DrillDrawer ref="drillRef" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, inject } from 'vue';
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import { sumDrillList } from '@/views/launch/promotion/components/promotionData';
  import DrillDrawer from '../components/DrillDrawer.vue';

  // 设置数据
  const filterParams = reactive({
    viewType: '1',
    showChildrenMaterial: false,
    showLaunchData: false,
  });
  const dateTime = ref(inject('dateTime') as any[]);
  const emits = defineEmits(['changeTabs']);
  const total = ref(0);
  // 模拟table数据
  const loading = ref(false);
  const dropdownList = sumDrillList.product;
  const tableData = ref<any>([
    {
      id: '1234',
      productName: 'mockName1',
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
    {
      id: '123456',
      productName: 'mockName2',
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
  ]);
  const columns = ref<any>([
    { title: '产品', dataIndex: 'productName', slotName: 'productName', width: 250, fixed: 'left' },
    { title: '花费', dataIndex: 'cost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '展示数', dataIndex: 'impressions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '千次展示成本', dataIndex: 'cpm', minWidth: 180, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击数', dataIndex: 'clicks', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击成本', dataIndex: 'cpc', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击率', dataIndex: 'ctr', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化数', dataIndex: 'conversions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化成本', dataIndex: 'conversionCost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化率', dataIndex: 'conversionRate', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
  ]);

  // Table页面滑动
  const scroll = {
    y: 'calc(100% - 56px)',
  };

  const linkAdset = (record) => {
    emits('changeTabs', { id: record.id, tab: 'adset' });
  };
  const drillRef = ref();
  const openDrill = (record, val) => {
    drillRef.value.openModal({ tab: 'product', value: val, recordData: record });
  };
  const getList = () => {};
  const init = () => {};
  init();
  // 分页
  const pageChange = (v) => {
    getList();
  };
  defineExpose({
    init,
  });
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
  :deep(.arco-select-view-tag) {
    span:first-child {
      width: 90%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: bottom;
    }
  }
</style>
