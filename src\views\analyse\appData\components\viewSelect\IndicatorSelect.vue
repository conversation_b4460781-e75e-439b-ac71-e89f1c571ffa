<template>
  <a-modal v-model:visible="modalVisible" :width="900" title-align="start" style="z-index: 998" @cancel="handleCancel">
    <template #title>
      {{ modalTitle }}
    </template>
    <div style="display: flex; align-items: center; margin-bottom: 12px">
      视图名称：
      <a-input v-model:model-value="editName" placeholder="请输入视图名称" style="width: 360px"></a-input>
    </div>
    <div class="index-content">
      <div class="left">
        <div class="left-header">
          <a-input v-model:model-value="indexText" style="background-color: #fff" placeholder="指标搜索" allow-clear>
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
          <a-button :loading="addIndicatorLoading" type="primary" style="margin-left: 12px" @click="handleComputedIndicator">
            <template #icon>
              <icon-plus />
            </template>
            计算指标
          </a-button>
        </div>
        <div v-if="indexText && filteredLists.length" class="search-content">
          <div v-for="(item, index) in filteredLists" :key="index" class="search-item">
            <div v-if="!item.code" class="type-title">{{ item.type }}</div>
            <div v-else>
              <a-popover>
                <div class="list-box" :class="{ 'has-select': isAble(item) }" @click="addIndexItem(item)">
                  <span style="display: flex; align-items: center">
                    <span :style="{ display: 'inline-block', maxWidth: showType ? '220px' : '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }">
                      {{ item.displayName }}
                    </span>
                    <span v-if="showType" style="display: inline-block; font-size: 12px; color: #ccc; margin-left: 8px">({{ getIndicatorTypeName(item.type) }})</span>
                  </span>
                  <icon-arrow-right class="select-icon" />
                </div>
                <template #content>
                  <div class="indicator-content">
                    <component :is="CustomIndicatorDisplay.default" v-if="item?.eventList?.length" :indicator="item || {}" />
                    <div v-else style="display: flex; align-items: center">
                      聚合数据：
                      <div class="select-btn-disabled">
                        <span class="btn-label"> {{ item.name }}-{{ item.displayName }} </span>
                      </div>
                    </div>
                    <component :is="EventQueryFilter.default" v-if="item?.filter?.filters?.length > 0" :filter="item?.filter" :disabled="true" :show-detail-filter="true" :code-list="item?.eventList" />
                    <div v-if="item.description">描述：{{ item.description }}</div>
                  </div>
                </template>
              </a-popover>
            </div>
          </div>
        </div>
        <a-collapse v-if="!indexText && props.indicatorList.length" :default-active-key="[1]" :bordered="false" expand-icon-position="right">
          <a-collapse-item v-for="(item, index) in props.indicatorList" :key="index + 1" :header="item.type">
            <div v-for="el in item.items" :key="el.code">
              <a-popover>
                <div class="list-box" :class="{ 'has-select': isAble(el) }" @click="addIndexItem(el)">
                  <span style="display: flex; align-items: center">
                    <span :style="{ display: 'inline-block', maxWidth: showType ? '220px' : '100%', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }">
                      {{ el.displayName }}
                    </span>
                    <span v-if="showType" style="display: inline-block; font-size: 12px; color: #ccc; margin-left: 8px">({{ getIndicatorTypeName(el.type) }})</span>
                  </span>
                  <icon-arrow-right class="select-icon" />
                </div>
                <template #content>
                  <div class="indicator-content">
                    <component :is="CustomIndicatorDisplay.default" v-if="el?.eventList?.length" :indicator="el || {}" />
                    <div v-else style="display: flex; align-items: center">
                      聚合数据：
                      <div class="select-btn-disabled">
                        <span class="btn-label"> {{ el.name }}-{{ el.displayName }} </span>
                      </div>
                    </div>
                    <component :is="EventQueryFilter.default" v-if="el?.filter?.filters?.length > 0" :filter="el?.filter" :disabled="true" :show-detail-filter="true" :code-list="el?.eventList" />
                    <div v-if="el.description">描述：{{ el.description }}</div>
                  </div>
                </template>
              </a-popover>
            </div>
          </a-collapse-item>
        </a-collapse>
        <a-empty v-if="!filteredLists.length || !props.indicatorList.length" />
      </div>
      <div class="right">
        <div class="title">
          <div>已选择：支持排序</div>
          <a @click="checkList = []">清除</a>
        </div>
        <a-divider style="margin: 8px auto" />
        <a-list v-if="checkList.length > 0" :max-height="530">
          <draggable :list="checkList" handle=".drag-handle">
            <template #item="{ element, index }">
              <a-list-item style="padding: 8px">
                <a-list-item-meta :description="element.displayName">
                  <template #avatar>
                    <div style="display: flex; align-items: center">
                      <div class="drag-handle">
                        <icon-drag-dot-vertical size="16" />
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <icon-delete @click="deleteDetailItem(index)" />
                </template>
              </a-list-item>
            </template>
          </draggable>
        </a-list>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="left"> </div>
        <div class="right">
          <a-button style="margin-right: 8px" class="cancel" @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="saveLoading" style="margin-right: 8px" @click="handleOk"> 保存 </a-button>
        </div>
      </div>
    </template>
    <!-- 添加计算指标 -->
    <edit-custom-indicator-modal v-if="editCustomIndicatorVisible" v-model:visible="editCustomIndicatorVisible" title="添加计算指标" :indicator="defaultIndicator" :subject-code="props.filterParams.subjectCode" :no-fetch-attr="true" :no-sure="true" @save="indicatorSave" />
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, ref, inject } from 'vue';
  import '@/views/analyse/css/analyse.less';
  import draggable from 'vuedraggable';
  import { Message } from '@arco-design/web-vue';
  import { analyseStore, toolStore } from '@/store';
  import { getNumberDisplayConfig, NumberDisplayType, getIndicatorTypeName } from '@/api/enum';
  import { getAttributionViewDetail, saveAttributionView } from '@/api/analyse/api';
  import EditCustomIndicatorModal from '@/views/analyse/components/operation/EditCustomIndicatorModal.vue';
  import * as CustomIndicatorDisplay from '@/views/analyse/components/CustomIndicatorDisplay.vue';
  import * as EventQueryFilter from '@/views/analyse/components/AttrCategoryFilter.vue';

  interface Props {
    indicatorList: any; // 指标列表
    filterParams: any; // 过滤参数
    pageType: string;
    showType: boolean;
  }
  const props = defineProps<Props>();
  const dashboardId = inject('dashboardCode', ref(''));
  const toolData = toolStore();
  const analyseData = analyseStore();
  const emits = defineEmits(['updateView', 'updateIndicator']);
  const modalVisible = ref(false);
  const viewCode = ref('');
  const saveLoading = ref(false);
  const checkList = ref<any>([]);
  const editName = ref('');
  const modalTitle = ref('');
  // 添加计算指标编辑显示
  const editCustomIndicatorVisible = ref<boolean>(false);

  const handleCancel = () => {
    modalVisible.value = false;
  };

  const defaultIndicator = ref();
  const addIndicatorLoading = ref(false);
  const handleComputedIndicator = async () => {
    addIndicatorLoading.value = true;
    toolData.updateTemporaryList([]);
    await analyseData.fetchOperationFilterLists('application');
    const originList = (await toolData.fetchOperationModalList('application')).flatMap((category) => category.items || []);
    defaultIndicator.value = {
      name: originList?.[0]?.name,
      type: props.pageType === 'comprehensive' ? 'operation' : 'traffic',
      isBasic: true,
      code: originList?.[0]?.code,
      displayName: `${originList?.[0]?.displayName}`,
      displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
      unitName: '',
      filter: {},
      eventList: [
        {
          indicatorName: originList?.[0]?.name,
          indicatorDisplayName: originList?.[0]?.displayName,
          indicatorCode: originList?.[0]?.code,
          type: originList?.[0]?.objectType,
          eventType: originList?.[0]?.type,
          filter: {},
        },
      ],
      formula: '',
      ignoreCalc: false,
    };
    editCustomIndicatorVisible.value = true;
    addIndicatorLoading.value = false;
  };
  const indexText = ref('');

  const addIndexItem = (v) => {
    if (!checkList.value.some((item) => item.code === v.code)) {
      checkList.value.push(v);
    }
  };

  const isAble = (v) => {
    const flag = checkList.value.some((item) => item.code === v.code);
    return flag; // 返回右边是否存在
  };

  const deleteDetailItem = (index: number) => {
    checkList.value.splice(index, 1);
  };
  const filteredLists = computed(() => {
    const str = indexText.value.trim();
    const listDate = [];
    props.indicatorList.forEach((item, index) => {
      const filterData = item.items?.filter((item) => item?.name?.includes(str) || item?.displayName?.includes(str));
      if (filterData?.length > 0) {
        listDate.push({ type: item.type });
        listDate.push(...filterData);
      }
    });
    return listDate;
  });
  // 应用
  const handleOk = async () => {
    if (!checkList.value.length) {
      Message.error('请至少选择一条指标');
      return;
    }
    if (!editName.value) {
      Message.error('请填写视图名称');
      return;
    }
    saveLoading.value = true;
    if (viewCode.value) {
      // 更新
      const data = {
        code: viewCode.value,
        name: editName.value,
        indicator: checkList.value.map((item) => item.code),
        subjectCode: props.filterParams?.subjectCode,
      };
      try {
        await saveAttributionView(data).then((res) => {
          if (res) {
            emits('updateView', viewCode.value);
            Message.success('保存成功');
          }
        });
        modalVisible.value = false;
      } catch (error) {
        console.error(error);
      } finally {
        saveLoading.value = false;
      }
    } else {
      // 新增
      let data = {
        name: editName.value,
        indicator: checkList.value.map((item) => item.code),
        subjectCode: props.filterParams?.subjectCode,
      };
      const shareParams = {
        shareType: 'non_public',
        permissionType: 4,
      };
      // 综合分析的时候传share参数
      if (props.pageType === 'comprehensive') {
        data = { ...data, share: shareParams };
      }
      try {
        await saveAttributionView(data).then((res) => {
          if (res) {
            Message.success('新建成功');
            emits('updateView', res.code);
          }
        });
        modalVisible.value = false;
      } catch (error) {
        console.error(error);
      } finally {
        saveLoading.value = false;
      }
    }
  };
  // 获取详情，checkList
  const getViewDetail = async () => {
    await getAttributionViewDetail(viewCode.value, dashboardId.value).then((res) => {
      checkList.value = res;
    });
  };

  const openModal = async (record?: any) => {
    const { code, name } = record || {};
    viewCode.value = code || '';
    editName.value = name || '';
    checkList.value = [];
    if (viewCode.value) {
      modalTitle.value = '编辑视图指标组';
      await getViewDetail();
    } else {
      modalTitle.value = '新建视图指标组';
    }
    modalVisible.value = true;
  };
  const indicatorSave = () => {
    emits('updateIndicator');
  };
  defineExpose({
    openModal,
  });
</script>

<style scoped lang="less">
  .footer {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
    }
    .right {
      display: flex;
      justify-content: flex-end;
      button {
        border-radius: 4px;
      }
    }
  }
  .cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
      background-color: var(--color-secondary);
    }
  }
  .index-content {
    border: 1px solid var(--color-secondary);
    width: 100%;
    height: 100%;
    display: flex;
    .left {
      width: 50%;
      padding: 12px;
      height: 600px;
      border-right: 1px solid var(--color-secondary);
      overflow-y: scroll;
      .left-header {
        display: flex;
        button {
          border-radius: 4px;
        }
      }
      .search-content {
        width: 100%;
        padding: 8px;
        margin-top: 12px;
        background-color: var(--color-fill-1);
        .type-title {
          color: var(--color-text-1);
          font-size: 14px;
          line-height: 24px;
          padding-top: 8px;
          padding-bottom: 8px;
          overflow: hidden;
        }
      }
      .list-box {
        margin: 0 8px 8px;
        padding: 0 12px;
        border-radius: 4px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        line-height: 28px;
        height: 30px;
        background: #fff;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .select-icon {
          opacity: 0;
        }
        &:hover .select-icon {
          opacity: 1;
        }
      }
      .has-select {
        cursor: no-drop;
        background: var(--color-secondary);
        &:hover .select-icon {
          opacity: 0;
        }
      }
    }
    .right {
      flex: 1;
      max-height: 600px;
      overflow: hidden;
      width: 100%;
      padding: 12px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        a {
          color: #0b75ff;
          cursor: pointer;
        }
      }
    }
  }
  .drag-handle {
    cursor: grab;
    margin-right: 8px;
  }
  .button-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .header-underline {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
  .select-btn,
  .select-btn-disabled {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    max-width: 100%;
    height: 26px;
    padding: 0 8px;
    font-size: 14px;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.3s;
    box-sizing: border-box;
    .btn-icon {
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      margin-right: 5px;
      flex-shrink: 0;
    }
    .btn-label {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }
  }
</style>
