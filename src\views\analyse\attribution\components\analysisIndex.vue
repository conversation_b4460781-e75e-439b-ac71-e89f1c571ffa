

<template>
    <div class="guide">
        <div class="stickyBar">
            <div class="modal" style="width:100%">
                <img src="/icon/analysis/performAnalysis.svg" alt="">
                <span class="title">对</span>
                <analysisSubjectSelect :style="{width:'72px',margin:'0 8px'}" @subject-change="subjectChange"/>
                <span class="title">进行分析</span>
            </div>
        </div>
        <div class="event-filter-box">
            <div class="attributionBase">
                <div class="names">
                    <div class="name">归因方式</div>
                    <div class="name">
                        窗口期
                        <a-tooltip content="归因事件距离目标事件在此限定时间范围内才有效" position="top">
                            <icon-info-circle/>
                        </a-tooltip>
                    </div>
                </div>
                <div class="values">
                    <a-select style="width:100px;margin-bottom: 16px;" default-value="first">
                        <a-option value="first">首次归因</a-option>
                        <a-option value="last">末次归因</a-option>
                        <a-option value="linear">线性归因</a-option>
                    </a-select>
                    <div style="display: flex;align-items: center;">
                        <a-select style="width:100px;margin-bottom: 16px;" default-value="day">
                            <a-option value="day">当天</a-option>
                            <a-option value="custom">自定义</a-option>
                        </a-select>
                        <a-input-group style="margin-bottom: 16px;margin-left: 16px;">
                            <a-input-number :style="{width:'66px'}" :precision="0" :min="1"/>
                            <a-select style="width:68px;" default-value="d">
                                <a-option value="d">天</a-option>
                                <a-option value="h">小时</a-option>
                                <a-option value="m">分</a-option>
                            </a-select>
                        </a-input-group>
                    </div>
                </div>
            </div>
            <div class="subTitle">目标事件</div>
            <handleTargetEvent :show-sub="true"/>
            <div class="subLabel" style="margin-bottom: 24px;">
                <a-checkbox v-model:model-value="calculation" style="margin-right: 8px;">直接转化参与归因计算</a-checkbox>
                <a-tooltip content="若目标事件在窗口期内找不到归因事件，则此次转化被视作直接转化。例如，若将目标事件、归因事件分别设置为付费事件、领券事件，若某付费事件的窗口期内无领券事件，此次付费事件的金额会被纳入直接转化。" position="top">
                    <icon-info-circle/>
                </a-tooltip>
            </div>
            <div class="subTitle">归因事件</div>
            <handleattributionEvent :show-sub="true"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useEventBus} from '@vueuse/core';
import analysisSubjectSelect from "@/views/analyse/components/analysisSubjectSelect.vue"
import handleTargetEvent from "./handleTargetEvent.vue";
import handleattributionEvent from "./handleattributionEvent.vue";


const eventBus = useEventBus('eventList');

// 直接参与归因计算
const calculation = ref(true)
const targetData = ref<any>([
    {
        name:'用户登录',
        type:'event',
        displayType:{
            type:'default',
            decimalNum:2,
            thousandSep: 1
        },
        displayName:'',
        rename:false,
        unitName:'',
        eventList:[
            {
                eventName:'用户登录',
                type:'event',
                eventAttrCode:undefined,
                eventAttrName:'总次数',
                // statisticalType:undefined,
                // statisticalName:undefined,
                filter:{
                    logicalOperation:'and',
                    filters:[],
                    subFilters:[]
                }
            },
        ],
        filtersList:[],
    }
])
const attributionData = ref<any>([
    {
        name:'用户登录',
        type:'event',
        displayType:{
            type:'default',
            decimalNum:2,
            thousandSep: 1
        },
        displayName:'',
        rename:false,
        unitName:'',
        eventList:[
            {
                eventName:'用户登录',
                type:'event',
                eventAttrCode:'',
                eventAttrName:'总次数',
                statisticalType:'',
                statisticalName:'',
                filter:{
                    logicalOperation:'and',
                    filters:[],
                    subFilters:[]
                }
            },
        ],
        filtersList:[],
    }
])
const subject = ref('')
const subjectName = ref('')
const subjectChange = (v) => {
  subject.value = v.subject
  subjectName.value = v.subjectName
}
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .title{
                font-weight: 600;
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .attributionBase{
            border-bottom: 1px solid var(--tant-border-color-border1-1);
            margin-bottom: 16px;
            .names{
                float: left;
                margin-right: 10px;
                margin-left: 24px;
                color: var(--tant-text-gray-color-text1-2);
                font: var(--tant-body-font-body-regular);
                .name{
                    display: flex;
                    align-items: center;
                    height: 32px;
                    margin-bottom: 16px;
                    line-height: 32px;
                }
            }
            .values{

            }
        }
        .subTitle{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
        }
        .subLabel{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
            display: flex;
            align-items: center;
        }
        .relevance-box{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            line-height: 38px;
            &:hover{
                background-color: var(--tant-fill-color-fill1-2);
                :deep(.filter-btn){
                    background: #fff;
                }
            }
        }
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 4px 0;
                        overflow: hidden;
                        line-height: 32px;
                        white-space: normal;
                    }
                }
            }
            .action-right{
                position: absolute;
                top: 0;
                right: 24px;
                min-width: 40px;
                height: 36px;
                padding-top: 0 !important;
                display: flex;
                align-items: center;
                opacity: 0;
                transition: opacity .3s;
            }
            .filter-btn{
                display: inline-flex;
                align-items: center;
                min-width: 40px;
                max-width: 200px;
                height: 26px;
                padding: 0 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
                box-sizing: border-box;
                .btn-icon{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    margin-right: 5px;
                }
                .filter-label{
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    color: var(--tant-text-gray-color-text1-2);
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: top;
                }

                &:hover{
                    border-color: var(--tant-primary-color-primary-hover);
                }
            }
            .row-word{
                display: inline-block;
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
                vertical-align: top;
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .action-left .row-content :deep(.filter-btn){
                background: #fff;
            }
            .action-left .row-content :deep(.filter-icon){
                background: #fff;
            }
            .sub-action-left :deep(.filter-btn){
                background: #fff;
            }
            .action-right{
                opacity: 1;
            } 
        }
        }
        .row-foot{
            margin: 0;
            padding-left: 34px;
            transition: all .3s;
            .ta-filter-button{
                padding: 6px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                font-size: 14px;
                transition: all .3s;
                .action{
                    border-radius: 4px;
                    background-color: var(--tant-primary-color-primary-fill);
                    color: var(--tant-primary-color-primary-default);
                    margin-right: 8px;
                    padding: 3px;
                    font-size: 18px;
                }
                .label{
                    color: var(--tant-primary-color-primary-default);
                }
                &:hover .action{
                    background-color: var(--tant-primary-color-primary-fill-hover);
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}

</style>