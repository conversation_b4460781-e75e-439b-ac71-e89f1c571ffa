// @/router/dynamic.ts
import {getMenuList} from "@/api/authorize/api";
import {menuToRouter} from "@/utils/menuUtil";
import type {Router} from "vue-router";
import {NOT_FOUND_ROUTE} from "@/router/routes/base";
import {DEFAULT_ROUTE} from "@/router/routes";
import {getLatestReleaseVersion} from "@/api/system/api";

/**
 * 动态添加路由
 */
const addRoutes = (router: Router, routes: any[]): void => {
  routes.forEach((item) => {
    if (item.path) {
      router.addRoute(item);
    }
  })
};


/**
 * 注册路由
 * 用户切换账号需移除 sessionStorage 中的 routerMap 数据
 */
export const registerRoutes = (router: Router): Promise<boolean> => {
  const menuList: any[] = JSON.parse(sessionStorage.getItem("menu-list"));
  const webVersion = JSON.parse(sessionStorage.getItem("web-version"));
  return new Promise(async (resolve, reject) => {
    if (menuList?.length) {
      const toRouter = menuToRouter(menuList);
      addRoutes(router, toRouter);
      router.addRoute(NOT_FOUND_ROUTE);
      router.addRoute(
        {
          path: '/',
          redirect: JSON.parse(localStorage.getItem("last-visited-page")) || DEFAULT_ROUTE,
        })
      resolve(true);
    } else {
      if (!webVersion?.versionCode?.length) {
        const version = await getLatestReleaseVersion();
        sessionStorage.setItem("web-version", JSON.stringify(version));
      }
      // 后端请求数据
      getMenuList().then(menus => {
        const toRouter = menuToRouter(menus);
        sessionStorage.setItem("menu-list", JSON.stringify(menus));
        addRoutes(router, toRouter);
        router.addRoute(NOT_FOUND_ROUTE);
        router.addRoute(
          {
            path: '/',
            redirect: JSON.parse(localStorage.getItem("last-visited-page")) ||DEFAULT_ROUTE,
          })
        resolve(true);
      })
    }
  })
}


/**
 * 生成管理菜单
 */
export const getAuthMenu = () => {
  // 这里就根据路由生成后台左侧菜单
  const menuList = JSON.parse(sessionStorage.getItem("menu-list"));
}