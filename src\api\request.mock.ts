import {AxiosPromise, AxiosResponse} from "axios";
import {ResponseData} from "@/api/type";
import {convertKeysToSnakeCase} from "@/utils/strUtil";
import serviceMock from "@/api/axios.mock.config";

// 封装通用的GET请求
export function getRequestMock<T>(url: string, params: any = undefined): AxiosPromise<T> {
  return serviceMock.get<ResponseData<T>, AxiosResponse<T>>(url, {
    method: 'get',
    params: convertKeysToSnakeCase(params)
  });
}

// 封装通用的POST请求
export function postRequestMock<T>(url: string, data: any = undefined): AxiosPromise<T> {
  return serviceMock.post<T, AxiosResponse<T>>(url, convertKeysToSnakeCase(data), {
    method: 'post'
  });
}

// 封装上传文件的POST请求
export function uploadRequestMock<T>(url: string, data: any = undefined): AxiosPromise<T> {
  return serviceMock.post<T, AxiosResponse<T>>(url, data, {
    method: 'post',
  });
}
