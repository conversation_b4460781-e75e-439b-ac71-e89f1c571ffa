<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import { useEventBus, useSessionStorage } from '@vueuse/core';
  import { DateManageEventBus, SaveEvent } from '@/types/event-bus';
  import { Message } from '@arco-design/web-vue';
  import router from '@/router';
  import { useRoute } from 'vue-router';
  import handleFilterList from '@/views/analyse/components/HandleFilterList.vue';
  import globalFilter from '@/views/analyse/components/globalFilter.vue';
  import dateDropdown from '@/views/analyse/components/dateDropdown.vue';
  import analysisSubjectSelect from '@/views/analyse/components/analysisSubjectSelect.vue';
  import { getIndicatorDetail, saveIndicatorData } from '@/api/setting/api';
  import useSettingStore from '@/store/modules/setting';
  import OpAnalysisSubjectSelect from '@/views/analyse/components/operation/opAnalysisSubjectSelect.vue';
  import operationIndicatorFormula from '@/views/analyse/components/operationIndicatorItem.vue';
  import DisplayTypeSelect from '@/views/analyse/components/DisplayTypeSelect.vue';
  import { analyseStore, toolStore } from '@/store';
  import { ROUTE_NAME } from '@/router/constants';
  import handleIndicatorItem from '@/views/analyse/components/handleIndicatorItem/index.vue';
  import { validateFormula } from '@/utils/indicator-util';
  import { isEmpty, cloneDeep } from 'lodash';
  import { getRepCategoryList } from '@/api/marketing/api';

  const settingStore = useSettingStore();
  // 是否保存了
  const editStatus = ref(false);
  // 是否可编辑
  const editAble = ref(false);
  watch(
    () => editStatus.value,
    (newVal) => {
      settingStore.EditStatus = !newVal;
    },
    { immediate: true }
  );
  const eventBus = useEventBus('eventList');
  const eventListData = ref<any>([]);
  const beginData = ref<any>([]);
  const visitData = ref<any>([]);
  const indicatorListStore = ref<any>([]);
  const analyseData = analyseStore();
  const toolData = toolStore();

  const route = useRoute();
  const code = ref('');
  const handleType = ref('');
  const inApp = ref<number>(0);
  const pageType = ref(route.query.pageType);
  code.value = route.query.code || '';
  handleType.value = route.query.type || '';
  inApp.value = route.query.inApp === '1' ? 1 : 0;

  const initData = ref<any>([]);
  const AData = ref<any>([]);
  const BData = ref<any>([]);
  const loading = ref(true);
  const operationFormula = ref('A'); // 运营指标计算公式
  const timeSpan = ref({
    number: 7,
    unit: 'DAY',
    firstDayDfWeek: 1,
  });
  const retentionFilter = ref({});
  const valueFormat = ref({ type: 'default', decimalNum: 2, thousandSep: 1 });
  const valueRetentionFormat = ref({ type: 'percent', decimalNum: 2, thousandSep: 1 });
  const valueOperationFormat = ref({ type: 'default', decimalNum: 2, thousandSep: 1 });
  const valueGroupFormat = ref({ type: 'default', decimalNum: 2, thousandSep: 1 });
  const valueTrafficFormat = ref({ type: 'default', decimalNum: 2, thousandSep: 1 });
  const formRef = ref();
  const form = reactive({
    appId: useSessionStorage('app-id', ''),
    radio: 'event',
    showFormat: 'default2',
    name: '',
    displayName: '',
    description: '',
    retentionFormat: 'percent',
    retentionIndicatorType: 'retention_rate',
    subject: '', // 分析主体
    subjectName: '',
    categoryCode: '',
  });

  const repCategoryList = ref<any>([]);
  // 留存主体
  const retentionSubjectChange = (v) => {
    form.subject = v.subject;
    form.subjectName = v.subjectName;
  };
  // 运营主体
  const subjectChange = async (v) => {
    loading.value = true;
    form.subject = v.subject;
    form.subjectName = v.subjectName;
    await analyseData.fetchOperationFilterLists(form.subject);
    const indicatorList = (await toolData.fetchOperationModalList(v.subject)).flatMap((category) => category.items || []);
    indicatorListStore.value = indicatorList;

    loading.value = false;
  };
  const saveEventBus = useEventBus(DateManageEventBus);
  const submitStatus = ref();
  const isAddB = ref(false);
  // A->B
  const isAtoB = ref(true);
  const dropSymbol = ref('+');

  const calculationList = ref<any>([
    {
      value: 'retention_rate',
      label: '留存率',
    },
    {
      value: 'retention_num',
      label: '留存人数',
    },
    {
      value: 'churn_rate',
      label: '流失率',
    },
    {
      value: 'churn_num',
      label: '流失人数',
    },
    {
      value: 'other',
      label: '同时展示',
    },
  ]);

  const rules = {
    name: [
      {
        required: true,
        validator: (value, cb) => {
          return new Promise((resolve) => {
            if (!value) {
              cb('请输入指标名');
            }
            if (value && value.length > 80) {
              cb('指标名不能超过 80 个字符');
            }
            if (!/^[a-z][a-z0-9_]{0,79}$/.test(value)) {
              cb('小写字母开头，可含小写字母/数字/下划线，不超过 80 个字符');
            }
            resolve(value);
          });
        },
      },
    ],
    displayName: [
      {
        required: true,
        validator: (value, cb) => {
          return new Promise((resolve) => {
            if (!value) {
              cb('请输入显示名');
            }
            if (value && value.length > 80) {
              cb('显示名不能超过 80 个字符');
            }
            resolve(value);
          });
        },
      },
    ],
  };

  const typeRadioChange = async (v) => {
    toolData.updateTemporaryList([]);
    if (v === 'event' || v === 'retention') {
      loading.value = true;
      await toolData.fetchAllModalList();
      loading.value = false;
    }
    if (v === 'group') {
      loading.value = true;
      await toolData.fetchGroupModalList();
      loading.value = false;
    }
    if (v === 'traffic') {
      loading.value = true;
      await toolData.fetchTrafficModalList('application');
      loading.value = false;
    }
  };
  const paramsVerify = (params) => {
    const secondList = params.filter?.filters || [];
    const firstList = params.eventList.filter((ee) => ee.filter?.filters && ee.filter?.filters.length > 0).map((ss) => ss.filter?.filters) || [];
    let flag1 = false;
    let flag2 = false;
    if (firstList.length > 0) {
      flag1 = firstList.every((items) => {
        const itemArray = Array.isArray(items) ? items : [items];
        return itemArray.every((item) => {
          // 如果 calcuSymbol 不是 ex 或 nex，则必须有 thresholds 且长度大于 0
          if (item.calcuSymbol !== 'ex' && item.calcuSymbol !== 'nex' && item.calcuSymbol !== 'con' && item.calcuSymbol !== 'ncon') {
            if (!item.thresholds?.length) {
              return false;
            }
          }
          // 检查子筛选条件
          if (item.subFilters?.length > 0) {
            return item.subFilters.every((ss) => {
              if (ss.calcuSymbol !== 'ex' && ss.calcuSymbol !== 'nex' && ss.calcuSymbol !== 'con' && ss.calcuSymbol !== 'ncon') {
                return ss.thresholds?.length > 0;
              }
              return true;
            });
          }
          return true;
        });
      });
    } else {
      flag1 = true;
    }
    if (secondList.length > 0) {
      flag2 = secondList.every((items) => {
        const itemArray = Array.isArray(items) ? items : [items];
        return itemArray.every((item) => {
          if (item.calcuSymbol !== 'ex' && item.calcuSymbol !== 'nex' && item.calcuSymbol !== 'con' && item.calcuSymbol !== 'ncon') {
            if (!item.thresholds?.length) {
              return false;
            }
          }
          // 检查子筛选条件
          if (item.subFilters?.length > 0) {
            return item.subFilters.every((ss) => {
              if (ss.calcuSymbol !== 'ex' && ss.calcuSymbol !== 'nex' && ss.calcuSymbol !== 'con' && ss.calcuSymbol !== 'ncon') {
                return ss.thresholds?.length > 0;
              }
              return true;
            });
          }
          return true;
        });
      });
    } else {
      flag2 = true;
    }
    return flag1 && flag2;
  };

  // 处理保存参数
  const buildParams = () => {
    const baseParams = {
      code: handleType.value === 'edit' ? code.value : undefined,
      displayName: form.displayName,
      description: form.description,
      type: form.radio,
      categoryCode: form.categoryCode,
      inApp: inApp.value,
    };
    switch (form.radio) {
      case 'event':
      case 'group':
      case 'traffic':
        return {
          ...eventListData.value[0],
          displayType: form.radio === 'event' ? valueFormat.value : form.radio === 'group' ? valueGroupFormat.value : valueTrafficFormat.value,
          subjectCode: undefined,
          ...baseParams,
        };
      case 'retention':
        const eventList = [beginData.value?.[0]?.eventList?.[0], visitData.value?.[0]?.eventList?.[0]];
        // 只有在“同时展示”模式下才传递indicatorList
        const indicatorList = form.retentionIndicatorType === 'other' ? (AData.value?.length ? [AData.value[0]] : []).concat(BData.value.length > 0 ? [BData.value[0]] : []) : [];
        return {
          subjectCode: form.subject,
          timeSpan: timeSpan.value,
          retentionIndicatorType: form.retentionIndicatorType,
          formula: isAddB.value ? (isAtoB.value ? `\${0}${dropSymbol.value}\${1}` : `\${1}${dropSymbol.value}\${0}`) : '',
          eventList,
          ...(form.retentionIndicatorType === 'other' && { indicatorList }),
          filter: retentionFilter.value,
          ...baseParams,
        };
      case 'operation':
        const newFormula = operationFormula.value.replace(/\s+/g, '');
        if (!isEmpty(newFormula) && !validateFormula(eventListData.value.eventList.length, newFormula)) {
          Message.warning('计算公式不合法，请检查修正后再次提交！');
          return null;
        }
        return {
          subjectCode: form.subject,
          isBasic: !operationFormula.value || operationFormula.value === 'A',
          displayType: valueOperationFormat.value,
          eventList: eventListData.value.eventList,
          formula: operationFormula.value,
          filter: eventListData.value.filter,
          ...baseParams,
        };
      default:
        Message.error('创建方式错误！');
        return null;
    }
  };
  const save = () => {
    if (!formRef.value) return;
    formRef.value.validate(async (errors: any) => {
      if (errors) {
        submitStatus.value = 'error';
        return;
      }
      submitStatus.value = 'success';
      try {
        const params = buildParams();
        if (!params) return;
        if (!paramsVerify(params)) {
          Message.error('筛选条件参数错误');
          return;
        }
        const res = await saveIndicatorData(params);
        if (res) {
          editStatus.value = true;
          router.back();
          Message.success('保存成功！');
        } else {
          Message.error('保存失败');
        }
      } catch (error) {
        Message.error('保存失败');
      }
    });
  };

  saveEventBus.on((event, route) => {
    if (event === SaveEvent && route === ROUTE_NAME.SETTING_ANALYSE_INDICATOR_CREATE) {
      save();
    }
  });

  const globalFilterMetaEventVueRef = ref(null);
  const addFilter = () => {
    if (globalFilterMetaEventVueRef.value) {
      globalFilterMetaEventVueRef.value.add();
    }
  };

  const init = async () => {
    toolData.updateTemporaryList([]);
    try {
      if (handleType.value === 'edit' && code.value) {
        const res = await getIndicatorDetail(code.value);
        if (res) {
          form.displayName = res.displayName;
          form.description = res.description;
          form.categoryCode = res.categoryCode;
          form.radio = res.type;
          form.subject = res.subjectCode;
          editAble.value = res.isPredefined;
          if (res.type === 'operation') {
            valueOperationFormat.value = res.displayType;
            operationFormula.value = res.formula;
            initData.value = res;
          } else if (res.type === 'retention') {
            timeSpan.value = res?.timeSpan || timeSpan.value;
            form.retentionIndicatorType = res?.retentionIndicatorType || 'retention_rate';
            valueRetentionFormat.value = res.displayType;
            await toolData.fetchAllModalList();
            // 处理留存指标的回显
            // 处理初始事件和回访事件的回显
            if (res.eventList && res.eventList.length >= 2) {
              // 回显初始事件
              if (res.eventList[0]) {
                beginData.value = [
                  {
                    eventList: [res.eventList[0]],
                    displayName: res.eventList[0].eventDisplayName || res.eventList[0].indicatorDisplayName,
                    name: res.eventList[0].eventName || res.eventList[0].indicatorName,
                    type: 'retention',
                    isBasic: true,
                    filter: {},
                  },
                ];
              }
              // 回显回访事件
              if (res.eventList[1]) {
                visitData.value = [
                  {
                    eventList: [res.eventList[1]],
                    displayName: res.eventList[1].eventDisplayName || res.eventList[1].indicatorDisplayName,
                    name: res.eventList[1].eventName || res.eventList[1].indicatorName,
                    type: 'retention',
                    isBasic: true,
                    filter: {},
                  },
                ];
              }
            }
            if (res.indicatorList && res.indicatorList.length > 0) {
              // 回显回访用户指标 (A指标)
              if (res.indicatorList[0]) {
                AData.value = [res.indicatorList[0]];
              }

              // 回显初始日期指标 (B指标)
              if (res.indicatorList[1]) {
                BData.value = [res.indicatorList[1]];
                isAddB.value = true;
              }

              // 处理同时展示指标的公式
              if (res.formula && res.formula !== '') {
                const formulaPattern = /\$\{(\d+)\}([+\-×÷])\$\{(\d+)\}/;
                const match = res.formula.match(formulaPattern);
                if (match) {
                  const firstIndex = parseInt(match[1]);
                  const operator = match[2];
                  isAtoB.value = firstIndex === 0;
                  dropSymbol.value = operator;
                }
              }
            }
            // 处理全局筛选的回显
            if (res.filter) {
              retentionFilter.value = res.filter;
            }
          } else if (res.type === 'event') {
            valueRetentionFormat.value = res.displayType;
            await toolData.fetchAllModalList();
            initData.value = [res];
          } else if (res.type === 'group') {
            valueGroupFormat.value = res.displayType;
            await toolData.fetchGroupModalList();
            initData.value = [res];
          } else if (res.type === 'traffic') {
            valueTrafficFormat.value = res.displayType;
            await toolData.fetchTrafficModalList('application');
            initData.value = [res];
          }
        }
      } else {
        await toolData.fetchAllModalList();
      }
    } catch (error) {
      console.error('初始化失败:', error);
      Message.error('初始化失败');
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    eventBus.emit('DataIndex');
    getRepCategoryList().then((res) => {
      repCategoryList.value = res;
    });
    init();
  });

  const indicatorsChange = (v) => {
    eventListData.value = v;
  };
  const operaionIndexChange = (v) => {
    operationFormula.value += v;
  };
  const weekChange = (v) => {
    timeSpan.value = v;
  };

  // 留存分析——展示格式，禁用控制,默认开启禁用，retentionIndicatorType为同时展示是可选
  const retentionFormatDisabled = ref(true);
  // 指标计算
  const retentionIndicatorTypeChange = (v) => {
    if (v === 'retention_rate' || v === 'churn_rate') {
      valueRetentionFormat.value = { type: 'percent', decimalNum: 2, thousandSep: 1 };
    } else if(v === 'retention_num' || v === 'churn_num') {
      valueRetentionFormat.value = { type: 'default', decimalNum: 0, thousandSep: 1 };
    }
    retentionFormatDisabled.value = v !== 'other';

    // 当切换到"同时展示"模式时，清空A和B指标数据
    if (v === 'other') {
      AData.value = [];
      BData.value = [];
      isAddB.value = false;
      // 重置公式相关状态
      isAtoB.value = true;
      dropSymbol.value = '+';
    }
  };
  const beginChange = (v) => {
    beginData.value = v;
  };
  const visitChange = (v) => {
    visitData.value = v;
  };
  // 回访用户指标
  const AChange = (v) => {
    if (Array.isArray(v) && v.length > 0) {
      v.forEach((item) => {
        if (item && item.eventList && item.eventList.length > 0) {
          const firstEvent = item.eventList[0];
          if (!item.displayName && (firstEvent.eventDisplayName || firstEvent.indicatorDisplayName)) {
            item.displayName = firstEvent.eventDisplayName || firstEvent.indicatorDisplayName;
          }
          if (!item.name && (firstEvent.eventName || firstEvent.indicatorName)) {
            item.name = firstEvent.eventName || firstEvent.indicatorName;
          }
          if (!item.code && (firstEvent.eventCode || firstEvent.indicatorCode)) {
            item.code = firstEvent.eventCode || firstEvent.indicatorCode;
          }
        }
      });
    }
    AData.value = v;
  };
  // 初始日期指标
  const BChange = (v) => {
    if (Array.isArray(v) && v.length > 0) {
      v.forEach((item) => {
        if (item && item.eventList && item.eventList.length > 0) {
          const firstEvent = item.eventList[0];
          if (!item.displayName && (firstEvent.eventDisplayName || firstEvent.indicatorDisplayName)) {
            item.displayName = firstEvent.eventDisplayName || firstEvent.indicatorDisplayName;
          }
          if (!item.name && (firstEvent.eventName || firstEvent.indicatorName)) {
            item.name = firstEvent.eventName || firstEvent.indicatorName;
          }
          if (!item.code && (firstEvent.eventCode || firstEvent.indicatorCode)) {
            item.code = firstEvent.eventCode || firstEvent.indicatorCode;
          }
        }
      });
    }
    BData.value = v;
    isAddB.value = v.length > 0;
  };

  const addBIndex = () => {
    isAddB.value = !isAddB.value;
  };

  // 同时展示指标
  const symbolList = ref<any>([
    {
      value: 'add',
      symbol: '+',
    },
    {
      value: 'subtract',
      symbol: '-',
    },
    {
      value: 'multiply',
      symbol: '×',
    },
    {
      value: 'divide',
      symbol: '÷',
    },
  ]);

  const symbolSelect = (v) => {
    dropSymbol.value = v.symbol;
  };
  // 全局筛选传参
  const filtersChange = (v) => {
    retentionFilter.value = v;
  };
  defineExpose({});
</script>

<template>
  <div style="display: flex; flex-direction: row; height: 85vh">
    <div class="common-frame-content-left">
      <div>
        <a-anchor :change-hash="false" smooth>
          <a-anchor-link href="#Rule">逻辑配置</a-anchor-link>
          <a-anchor-link href="#Basic">基本信息</a-anchor-link>
        </a-anchor>
      </div>
    </div>
    <a-spin :loading="loading" class="common-frame-content-right">
      <div class="spinContainer">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" class="form">
          <div style="width: 100%">
            <div id="Rule" class="common-block">
              <div class="common-block-header">
                <div class="common-block-header-title">逻辑配置</div>
              </div>
              <a-row :gutter="20">
                <a-col :span="13">
                  <a-form-item field="radio" label="创建方式">
                    <a-radio-group v-model="form.radio" :disabled="code !== ''" @change="typeRadioChange">
                      <a-radio value="event">事件</a-radio>
                      <a-radio value="retention">留存</a-radio>
                      <a-radio value="operation">应用</a-radio>
                      <a-radio value="group">群组</a-radio>
                      <a-radio value="traffic">归因</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="form.radio === 'event'" :gutter="20">
                <a-col :span="13">
                  <a-form-item field="showFormat" label="展示格式">
                    <display-type-select v-model:displayType="valueFormat" />
                  </a-form-item>
                </a-col>
                <a-col :span="13">
                  <a-form-item label="计算方式">
                    <handleIndicatorItem v-if="!loading" :show-sub="true" :is-custom="true" :handle-list="initData" @indicators-change="indicatorsChange" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="form.radio === 'retention'" :gutter="24">
                <a-col :span="10">
                  <a-form-item label="分析主体">
                    <analysisSubjectSelect :style="{ width: '150px' }" @subject-change="retentionSubjectChange" />
                  </a-form-item>
                  <a-form-item field="section" label="指标计算">
                    <dateDropdown :timeSpanData="timeSpan" @time-span-change="weekChange" />
                    <span style="margin: 0 4px">的</span>
                    <a-select v-model:model-value="form.retentionIndicatorType" style="width: 150px" @change="retentionIndicatorTypeChange">
                      <div v-for="(item, index) in calculationList" :key="index">
                        <a-option :style="form.retentionIndicatorType === item.value ? 'backgroundColor:var(--tant-secondary-color-secondary-fill)' : ''" :value="item.value">{{ item.label }} </a-option>
                      </div>
                    </a-select>
                  </a-form-item>
                  <a-form-item field="section" label="展示格式">
                    <display-type-select v-model:displayType="valueRetentionFormat" :disabled="retentionFormatDisabled" />
                  </a-form-item>
                </a-col>
                <a-col :span="14" style="padding-left: 64px; padding-right: 24px; border-left: 1px solid var(--tant-border-color-border1-1)">
                  <a-form-item field="section" label="初始事件">
                    <handleFilterList v-if="!loading" :handle-list="beginData" @indicators-change="beginChange" />
                  </a-form-item>
                  <a-form-item field="section" label="回访事件">
                    <handleFilterList v-if="!loading" :handle-list="visitData" @indicators-change="visitChange" />
                  </a-form-item>
                  <div v-if="form.retentionIndicatorType === 'other'">
                    <a-form-item field="section">
                      <template #label>
                        <span class="roi-step">A</span>
                        回访用户指标
                      </template>
                      <!-- <handleFilterList :handle-list="AData" :show-sub="true" @indicators-change="AChange" /> -->
                      <handleIndicatorItem :show-sub="true" :is-custom="true" :handle-list="AData" @indicators-change="AChange" />
                    </a-form-item>
                    <a-form-item field="section">
                      <template #label>
                        <span class="roi-step">B</span>
                        初始日期指标
                      </template>
                      <div style="width: 100%">
                        <handleIndicatorItem v-if="isAddB" :show-sub="true" :is-custom="true" :show-del="true" :handle-list="BData" @indicators-change="BChange" />
                        <!-- <handleFilterList v-if="isAddB" :handle-list="BData" :show-sub="true" :show-del="true" @indicators-change="BChange" /> -->
                        <div v-if="!isAddB" class="ta-filter-button" @click="addBIndex">
                          <icon-plus class="action" />
                          <span class="label">添加初始日期指标-B</span>
                        </div>
                      </div>
                    </a-form-item>
                    <a-form-item v-if="isAddB" field="section" label="同时展示指标">
                      <div class="ab">
                        <span v-if="isAtoB" class="roi-step roi-step-pointer" @click="() => (isAtoB = false)">A</span>
                        <span v-else class="roi-step roi-step-pointer" @click="() => (isAtoB = true)">B</span>
                        <a-dropdown position="top" @select="symbolSelect">
                          <div class="roi-btn">{{ dropSymbol }}</div>
                          <template #content>
                            <a-doption v-for="(item, index) in symbolList" :key="index" :value="item" style="width: 60px">{{ item.symbol }}</a-doption>
                          </template>
                        </a-dropdown>
                        <span v-if="isAtoB" class="roi-step roi-step-pointer" @click="() => (isAtoB = false)">B</span>
                        <span v-else class="roi-step roi-step-pointer" @click="() => (isAtoB = true)">A</span>
                      </div>
                    </a-form-item>
                  </div>
                  <a-form-item field="section" label="全局筛选">
                    <div style="width: 100%">
                      <globalFilter ref="globalFilterMetaEventVueRef" :filter="retentionFilter" :apply-create-event="true" @filters-change="filtersChange" />
                      <div class="ta-filter-button" @click="addFilter">
                        <span role="img" class="anticon">
                          <icon-plus />
                        </span>
                        <span class="ta-filter-button-label">添加条件</span>
                      </div>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="form.radio === 'operation'" :gutter="20">
                <a-col :span="13">
                  <a-form-item label="分析主体">
                    <opAnalysisSubjectSelect :style="{ width: '150px' }" :disabled="editAble" @subject-change="subjectChange" @subject-init="subjectChange" />
                  </a-form-item>
                  <a-form-item field="showFormat" label="展示格式">
                    <display-type-select v-model:displayType="valueOperationFormat" />
                  </a-form-item>
                </a-col>
                <a-col :span="13">
                  <a-form-item label="计算方式">
                    <operationIndicatorFormula v-if="!loading" :indicator-list-store="indicatorListStore" :init-data="initData" :disabled="editAble" @index-change="operaionIndexChange" @indicators-change="indicatorsChange" />
                    <!-- <handleFilterList v-if="!formulaLoading" :handle-list="initData" @indicators-change="indicatorsChange"/> -->
                  </a-form-item>
                </a-col>
                <a-col :span="13">
                  <a-form-item label="计算公式">
                    <a-input v-model="operationFormula" :disabled="editAble" allow-clear class="form-input" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="form.radio === 'group'" :gutter="20">
                <a-col :span="13">
                  <a-form-item field="showFormat" label="展示格式">
                    <display-type-select v-model:displayType="valueGroupFormat" />
                  </a-form-item>
                </a-col>
                <a-col :span="13">
                  <a-form-item label="计算方式">
                    <handleIndicatorItem v-if="!loading" :is-group="true" :show-sub="true" :is-custom="true" :handle-list="initData" @indicators-change="indicatorsChange" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row v-if="form.radio === 'traffic'" :gutter="20">
                <a-col :span="13">
                  <a-form-item field="showFormat" label="展示格式">
                    <display-type-select v-model:displayType="valueTrafficFormat" />
                  </a-form-item>
                </a-col>
                <a-col :span="13">
                  <a-form-item label="计算方式">
                    <handleIndicatorItem v-if="!loading" :disabled="editAble" :show-sub="true" :is-custom="true" :handle-list="initData" @indicators-change="indicatorsChange" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <div id="Basic" class="common-block">
              <div class="common-block-header">
                <div class="common-block-header-title">基本信息</div>
              </div>
              <div>
                <a-row v-if="pageType === 'app'" :gutter="20">
                  <a-col :span="13">
                    <a-form-item field="appId" label="应用ID" label-col-flex="70px">
                      <a-input v-model="form.appId" class="form-input" disabled />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-form-item tooltip="不超过80个字符" field="displayName" label="显示名" label-col-flex="70px" validate-trigger="blur">
                      <a-input v-model="form.displayName" class="form-input" placeholder="请输入显示名" />
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-form-item field="categoryCode" label="关联分组" label-col-flex="70px">
                      <a-select v-model="form.categoryCode" allow-clear placeholder="请选择关联分组" style="width: 600px">
                        <a-option v-for="item in repCategoryList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <!-- <a-row :gutter="20">
                  <a-col :span="13">
                    <a-form-item
                        tooltip="不超过80个字符"
                        field="name"
                        label="指标名" label-col-flex="70px"
                        validate-trigger="blur">
                      <a-input
                          v-model="form.name"
                          :disabled="handleType === 'edit'"
                          class="form-input"
                          placeholder="小写字母开头，可含小写字母/数字/下划线，不超过80个字符"
                      />
                    </a-form-item>
                  </a-col>
                </a-row> -->
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-form-item field="description" label="备注" label-col-flex="70px">
                      <a-textarea v-model:model-value="form.description" placeholder="请输入备注" class="form-input" :max-length="200" allow-clear show-word-limit />
                      <template #label> <span>备注</span><span style="color: var(--tant-text-gray-color-text1-4)">(选填)</span> </template>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-form>
      </div>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  :deep(.arco-form-item-label) {
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 500;
  }

  .common-frame-content-left {
    min-width: 200px;
    padding: 24px;
    overflow: auto;
    border-right: 1px solid var(--tant-border-color-border1-1);
  }

  .common-frame-content-right {
    flex: 1 1;
    width: 0;
    padding: 24px 24px 48px;
    border-radius: 4px;

    .spinContainer {
      height: 100%;

      overflow-x: hidden;
      overflow-y: auto;

      .form {
        font-size: 14px;
        font-variant: tabular-nums;
        line-height: 1.5715;
        list-style: none;
        font-feature-settings: 'tnum', 'tnum';

        .common-block {
          //margin-bottom: 32px;

          .common-block-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;

            .common-block-header-title {
              flex: 1 1;
              width: 0;
              color: var(--tant-text-gray-color-text1-1);
              font: var(--tant-header-font-header4-medium);
            }
          }
        }
      }
    }
  }

  .form-input {
    width: 600px;
  }

  .form-input:focus-within {
    box-shadow: var(--tant-input-shadow-active-overall);
    border-color: var(--tant-primary-color-primary-active);
  }

  .ta-filter-button {
    display: flex;
    align-items: center;
    padding: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;

    .anticon {
      margin-right: 8px;
      padding: 0 3px;
      color: var(--tant-primary-color-primary-default);
      background-color: var(--tant-primary-color-primary-fill);
      border-radius: 4px;
    }

    .ta-filter-button-label {
      color: var(--tant-primary-color-primary-default);
    }
  }

  .ta-filter-button:hover .anticon {
    color: var(--tant-primary-color-primary-hover);
    background-color: var(--tant-primary-color-primary-fill-hover);
  }

  :deep(.guide) {
    margin-bottom: 0;
  }

  .roi-step {
    margin-right: 8px;
    display: inline-block;
    width: 24px;
    height: 24px;
    color: var(--tant-bg-white-color-bg1-1);
    font-size: 12px;
    line-height: 24px;
    text-align: center;
    background-color: var(--tant-secondary-color-secondary-default);
    border-radius: 4px;
  }

  .roi-step-pointer {
    cursor: pointer;

    &:hover {
      color: var(--tant-text-gray-color-text1-2);
      background-color: var(--tant-secondary-color-secondary-fill);
    }
  }

  .roi-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    font-size: 14px;
    background: var(--tant-bg-white-color-bg1-1);
    border: 1px solid #dbdee4;
    border-radius: 2px;
    cursor: pointer;
  }

  .ta-filter-button {
    padding: 6px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;

    .action {
      border-radius: 4px;
      background-color: var(--tant-primary-color-primary-fill);
      color: var(--tant-primary-color-primary-default);
      margin-right: 8px;
      padding: 3px;
      font-size: 18px;
    }

    .label {
      color: var(--tant-primary-color-primary-default);
    }

    &:hover .action {
      background-color: var(--tant-primary-color-primary-fill-hover);
      color: var(--tant-primary-color-primary-hover);
    }
  }

  .ab {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
</style>
