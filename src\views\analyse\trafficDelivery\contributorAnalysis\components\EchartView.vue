<template>
  <div class="chart-content">
    <Chart :option="chartOption" :style="{ height: totalItems > 5 ? 'calc(100% - 32px)' : '100%'}"/>
    <div v-if="totalItems > 5" style="display: flex; justify-content: flex-end;">
      <a-pagination :total="totalItems" show-total :page-size="pageSize" :current="currentPage" @change="handlePageChange"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import Chart from "@/components/chart/index.vue";
import { cloneDeep } from 'lodash';

interface Props {
  chartData?: any[];
}

const props = defineProps<Props>();

const chartOption = ref({});
const currentPage = ref(1);
const pageSize = ref(5);

const totalItems = computed(() => {
  if (!props.chartData?.length) return 0;
  return props.chartData.length;
  }
);

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// 定义颜色数组，确保柱状图和饼图使用相同的颜色
const defaultColors = [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#8d5a9f',
  '#d97a4a', '#4cb6c4', '#ff9f6b', '#2ec4b6', '#e67e22',
  '#e74c3c', '#3498db', '#9b59b6', '#1abc9c', '#f1c40f',
  '#2980b9', '#8e44ad', '#27ae60', '#d35400', '#1f618d',
  '#7d3c98', '#00b894', '#fdcb6e', '#6c5ce7', '#a29bfe',
  '#00cec9', '#fab1a0', '#e17055', '#0984e3', '#74b9ff',
  '#00b894', '#55efc4', '#ffeaa7', '#fab1a0', '#fd79a8'
];
// 处理数据转换为ECharts格式
const processChartData = () => {
  if (!props.chartData?.length) {
		chartOption.value = {
			series: [],
			legend: { data: [] },
			tooltip: {}
		};
		return;
	}
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  let processedData = cloneDeep(props.chartData);
  const paginatedData = cloneDeep(props.chartData).slice(start, end);
  
  // 收集所有唯一的source作为图例
  const allSources = new Set<string>();
  processedData.forEach((item: any) => {
    item.items?.forEach((subItem: any) => {
      allSources.add(subItem.source);
    });
  });
  
  const legendData = Array.from(allSources);
  // 创建source到颜色的映射
  const sourceColorMap: Record<string, string> = {};
  legendData.forEach((source, index) => {
    sourceColorMap[source] = defaultColors[index % defaultColors.length];
  });
  
  // y轴数据：显示用户数 + source名称
  const yAxisData = paginatedData.map((item: any) => {
    const totalUsers = item.absoluteUsers || 0;
    return `(用户数：${totalUsers.toLocaleString()}) ${item.source}`;
  });
  
  // 为每个图例创建系列数据
  const seriesData = legendData.map(source => {
    const data = paginatedData.map(item => {
      let filteredItems = item.items || [];
      const foundItem = filteredItems.find((subItem: any) => subItem.source === source);
      return foundItem ? {
        value: foundItem.relativeRate,
        users: foundItem.users,
        absoluteRate: foundItem.absoluteRate,
        relativeRate: foundItem.relativeRate
      } : { value: 0, users: 0, absoluteRate: 0, relativeRate: 0 };
    });
    
    return {
      name: source,
      type: 'bar',
      stack: 'total',
      barMaxWidth: 30,
      data: data,
      itemStyle: {
        color: sourceColorMap[source]
      },
      emphasis: {
        focus: 'series'
      },
      label: {
        show: true,
        position: 'inside',
        formatter: (params: any) => {
          return params.value > 0.05 ? `${(params.value * 100).toFixed(2)}%` : '';
        }
      }
    };
  });
  chartOption.value = {
    color: defaultColors,
    tooltip: {
      trigger: 'item',
      appendToBody: true,
      confine: true,
      enterable: true,
      extraCssText: 'max-height: 300px; overflow-y: auto; width: 280px;',
      formatter: (param: any) => {
        // 只显示单个项目的数据
        if (param.value <= 0) {
          return '';
        }
        const data = param.data;
        let result = ``;
        result += `${param.marker}<strong>${param.seriesName}</strong><br/>`;
        result += `&nbsp;&nbsp;用户数: ${data.users.toLocaleString()}<br/>`;
        result += `&nbsp;&nbsp;相对占比: ${(data.relativeRate * 100).toFixed(2)}%<br/>`;
        result += `&nbsp;&nbsp;绝对占比: ${(data.absoluteRate * 100).toFixed(2)}%<br/>`;
        return result;
      }
    },
    legend: {
      data: legendData,
      bottom: 15,
      type: 'scroll',
      itemWidth: 12,
      itemHeight: 8,
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '20%',
      top: '2%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      min: 0,
      max: 1,
      axisLabel: {
        formatter: (value: number) => {
          return `${(value * 100).toFixed(0)}%`;
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'category',
      data: yAxisData,
      inverse: true,
      axisLabel: {
        interval: 0,
        width: 180,
        overflow: 'truncate',
        formatter: (value: string) => {
          return value.length > 25 ? `${value.substring(0, 25)}...` : value;
        }
      },
    },
    series: [
      ...seriesData.filter(s => s.data.some(d => d.value > 0)),
    ]
  };
};

// 监听分页数据变化
watch([currentPage, () => props.chartData], () => {
  processChartData();
}, { immediate: true, deep: true });

</script>

<style lang="less" scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style>

