<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          事件类型：
          <a-select v-model:model-value="typeFilter" placeholder="全部" allow-clear style="width: 120px" @change="refresh">
            <a-option value="common">公共事件</a-option>
            <a-option value="app">应用事件</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="refresh">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-upload
              :custom-request="uploadFile"
              :show-file-list="false"
              :accept="'.xlsx,.xls'"
          >
            <template #upload-button>
              <a-button type="primary">
                <icon-upload/>
              </a-button>
            </template>
          </a-upload>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="addData">
            <icon-plus/>
          </a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="handleBatchPrefix">
            <template #icon>
              <icon-tags/>
            </template>
          </a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="batchEditGroup">
            分组批量编辑
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #sourceChannel="{record}">
          <a-tag v-for="item in record.sourceChannel" :key="item">
            {{ getUserAttributeSourceChannelName(item) }}
          </a-tag>
        </template>
        <template #eventNote="{record}">
          {{ handleDataAliasStr(record) }}
          {{ record.eventNote }}
        </template>
        <template #optional="{ record }">
          <a-button v-if="EventType.PREDEFINED!==record?.eventType && !_.isEmpty(record.appId)" type="text" @click="editData(record)">编辑</a-button>
          <a-popconfirm v-if="EventType.PREDEFINED!==record?.eventType && !_.isEmpty(record.appId)" :content="`确认删除“${record.eventName}”?`" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :visible="editModalShow"
        title-align="start"
        :title="formData.eventCode?'编辑事件':'新增事件'"
        :ok-text="formData.eventCode?'更新':'保存'"
        @cancel="editCancel"
        @ok="editConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-form-item v-show="false" field="eventCode" label="事件编码">
          <a-input v-model="formData.eventCode" disabled/>
        </a-form-item>
        <a-form-item field="eventName" :disabled="formData.eventCode" :rules="[{required:true,message:'属性名称不能为空'}]" label="事件名称">
          <a-input v-model="formData.eventName" placeholder="请输入事件名称"/>
        </a-form-item>
        <a-form-item field="eventDisplayName" :rules="[{required:true,message:'显示名称不能为空'}]" label="显示名称">
          <a-input v-model="formData.eventDisplayName" placeholder="请输入属性显示名称"/>
        </a-form-item>
        <a-form-item field="eventType" disabled :rules="[{required:true,message:'事件类型不能为空'}]" label="事件类型">
          <a-select v-model="formData.eventType" placeholder="请选择事件类型">
            <a-option v-for="item in Object.values(EventType).filter(value => typeof value === 'number')" :key="item" :value="item">{{ getEventTypeName(item) }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="eventRelatedAttribute" label="关联属性">
          <a-select v-model="formData.eventRelatedAttribute" multiple :max-tag-count="2" placeholder="请选择事件属性">
            <a-option v-for="item in sortedAttrData" :key="item.code" :disabled="item.isCommon === 1" :value="item.code">{{ item.displayName}} ({{item.name}})</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="categoryCode" label="关联分组">
          <a-select v-model="formData.categoryCode" allow-clear allow-search placeholder="请选择关联分组">
            <a-option v-for="item in repCategoryList" :key="item.code" :value="item.code">{{item.name}}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item style="margin-bottom: 0">
          <a-form-item field="eventVisibility" label="显示状态">
            <a-switch v-model="formData.eventVisibility"/>
          </a-form-item>
          <a-form-item field="eventStatus" label="可用状态">
            <a-switch v-model="formData.eventStatus" :checked-value="1" :unchecked-value="0"/>
          </a-form-item>
        </a-form-item>
        <a-form-item style="margin-bottom: 0" label="数据源映射">
          <a-form-item field="dataSource" label="数据源">
            <a-select v-model="formData.dataSource" default-value="appsflyer" placeholder="请选择数据源">
              <a-option value="appsflyer">appsflyer</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="alias" label="别名" style="margin-left: 12px;margin-right: 8px;">
            <a-input v-model="formData.alias" placeholder="请输入别名"/>
          </a-form-item>
        </a-form-item>
        <a-form-item field="eventNote" label="备注">
          <a-textarea v-model="formData.eventNote" placeholder="请输入备注"/>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:visible="editGroupShow" title-align="start" title="分组批量编辑" :footer="false" @cancel="closeModal">
      <a-form ref="groupFormRef" :model="groupForm" layout="vertical">
        <a-form-item field="code" label="关联分组" :rules="[{required:true,message:'请选择分组'}]">
          <a-select v-model="groupForm.code" allow-clear allow-search placeholder="请选择关联分组" @change="categoryCodeChange">
            <a-option v-for="item in repCategoryList" :key="item.code" :value="item.code">{{item.name}}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="relatedEventList" label="关联事件">
          <CustomSelectWithActions
            v-model="groupForm.relatedEventList"
            :options="eventList"
            :loading="relatedLoading"
            :max-tag-count="2"
            multiple
            allow-search
            placeholder="请选择关联事件"
          />
        </a-form-item>
      </a-form>
      <div class="footer" style="text-align: right;">
          <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
          <a-button type="primary" :loading="updateLoading" style="margin-right: 10px;" @click="batchChangeRepCategory('update')">
              更新
          </a-button>
          <a-button type="primary" :loading="updateLoading" @click="batchChangeRepCategory('apply')">
              应用
          </a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="batchAddVisible" title-align="start" title="批量添加事件" :footer="false" @cancel="batchClose">
      <a-form ref="batchFormRef" :model="batchForm" layout="vertical">
        <a-form-item field="prefix" label="事件前缀" :rules="[{required:true,message:'请输入事件前缀'}]">
          <a-input v-model="batchForm.prefix" placeholder="请输入事件前缀(完全按正则匹配)"/>
        </a-form-item>
      </a-form>
      <div class="footer" style="text-align: right;">
          <a-button style="margin-right: 10px;" class="cancel" @click="batchClose">取消</a-button>
          <a-button type="primary" :loading="bactchLoading" @click="batchAdd">
              确定
          </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {addCustomEvent, deleteEventList, getEventAttrList, getEventDetail, getEventList, saveEventList, uploadMetadata} from "@/api/setting/api";
import _ from "lodash";
import {Message, Modal, RequestOption} from "@arco-design/web-vue";
import {EventType, getEventTypeName, getUserAttributeSourceChannelName} from "@/api/enum";
import {useRoute} from "vue-router";
import selectApp from "@/components/selected-game-app/index.vue"
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {getRepCategoryList, getRepEventList, saveEventPrefixMatch, updateRepCategoryGroups} from "@/api/marketing/api";
import {sortSelectedFirst} from "@/utils/sortUtil";
import router from "@/router";
import CustomSelectWithActions from "@/components/custom-select-with-actions/index.vue";


const localStorageEventBus = useEventBus(LocalStorageEventBus);
const route = useRoute();
const data = ref<any>([]);
const attributeData = ref<any>([]);
const sortedAttrData = ref<any>([]);
const repCategoryList = ref<any>([]);
const loading = ref<boolean>(true);
const searchName = ref<string>(route.query.text as string || '');
const typeFilter = ref<string>('app'); // 新增：属性类型筛选
const formData = ref<any>({});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const columns = ref<any>([
  {
    title: '事件名',
    dataIndex: 'eventName',
    fixed: 'left',
    width: 160,
  },
  {
    title: '显示名',
    dataIndex: 'eventDisplayName',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '备注',
    dataIndex: 'eventNote',
    slotName: 'eventNote',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    render: (value) => {
      const {record} = value;
      return (_.isEmpty(record.appId) ? '公共' : '') + getEventTypeName(record.eventType)
    },
    width: 120,
    align: 'center',
  },
  {
    title: '显示状态',
    dataIndex: 'eventVisibility',
    render: (value) => {
      const {record} = value;
      return record.eventVisibility ? '可见' : '隐藏'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '可用状态',
    dataIndex: 'eventStatus',
    render: (value) => {
      const {record} = value;
      return record.eventStatus ? '启用' : '禁用'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '最后更新人',
    dataIndex: 'creator',
    slotName: 'creator',
    width: 120,
    align: 'center',
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 160
  },
]);;
const handleDataAliasStr = (record) => {
  if(!record?.dataAlias) return ''
  const list = record.dataAlias.filter(item => item.alias)
  return list.map(item => `数据源${item.dataSource}的别名为${item.alias}`).join('，');
}
const refresh = () => {
  const params = {
    name: searchName.value,
    inApp: 1,
  };
  loading.value = true
  getEventList(params).then(res => {
    const searchRegex = searchName.value ? new RegExp(searchName.value, 'i') : null;
    let attrData = _.isEmpty(searchName.value) ? res : res?.filter(item => {
      return searchRegex ? (searchRegex.test(item.eventName) || searchRegex.test(item.eventDisplayName)) : true;
    })
    if (typeFilter.value === 'app') {
      attrData = attrData?.filter((item: any) => !_.isEmpty(item.appId))
    }
    if(typeFilter.value === 'common') {
      attrData = attrData?.filter((item: any) => _.isEmpty(item.appId))
    }
    data.value = attrData
  }).finally(() => {
    loading.value = false
  })
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") refresh()
})

onMounted(() => {
  getEventAttrList({inApp: 1}).then(res => {
    attributeData.value = res
  })
  getRepCategoryList().then(res => {
    repCategoryList.value = res
  })
  refresh()
})

const searchData = () => {
  // 更新路由参数
  const query = { ...route.query };
  if (searchName.value) {
    query.text = searchName.value;
  } else {
    delete query.text;
  }
  router.replace({ query });
}
const addData = ()=>{
  formData.value.eventType = EventType.CUSTOM;
  formData.value.dataSource = 'appsflyer'
  sortedAttrData.value = sortSelectedFirst(attributeData.value,formData.value.eventRelatedAttribute)
  editModalShow.value=true
}
const editData = (record: any) => {
  getEventDetail(record.eventCode).then((res) => {
    const aliasData = res.dataAlias[0] || {};
    formData.value = {
      eventCode:record.eventCode,
      eventName:record.eventName,
      eventDisplayName:record.eventDisplayName,
      eventType:record.eventType,
      eventVisibility:res.visibility,
      eventStatus:res.status,
      eventNote:record.eventNote,
      eventRelatedAttribute: res.eventRelatedAttribute,
      dataSource: aliasData?.dataSource || 'appsflyer',
      alias: aliasData?.alias,
      categoryCode: res.categoryCode,
    };
    sortedAttrData.value = sortSelectedFirst(attributeData.value,formData.value.eventRelatedAttribute)
    editModalShow.value = true;
  })
}

const formSubmit = (value) => {
  if (_.isEmpty(value)) {
    Message.warning("事件数据不能为空！")
  }
  if (_.isEmpty(value.eventCode)) {
    addCustomEvent({
      name: value.eventName,
      displayName: value.eventDisplayName,
      note: value.eventNote,
      type: EventType.CUSTOM,
      relatedAttributes: value.eventRelatedAttribute,
      visibility: value.eventVisibility,
      status: value.eventStatus,
      inApp: 1,
      categoryCode: value.categoryCode,
      dataAlias: {
        dataSource: value.dataSource || 'appsflyer',
        alias: value.alias,
      }
    }).then(res => {
      Message.info("事件数据保存成功！")
      editModalShow.value = false;
      formRef.value.resetFields();
      refresh()
    }).catch(e => {
      Message.error("事件数据保存失败！", e)
    })
    return
  }

  saveEventList({
    code: value.eventCode,
    displayName: value.eventDisplayName,
    note: value.eventNote,
    relatedAttributes: value.eventRelatedAttribute,
    visibility: value.eventVisibility,
    status: value.eventStatus,
    inApp: 1,
    categoryCode: value.categoryCode,
    dataAlias: {
      dataSource: value.dataSource || 'appsflyer',
      alias: value.alias,
    }
  }).then(res => {
    Message.info("事件数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    refresh()
  }).catch(e => {
    Message.error("事件数据保存失败！", e)
  })
}

const editConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}

const uploadFile = (option: RequestOption) => {
  const {fileItem} = option
  uploadMetadata("app_event", fileItem).then((res) => {
    if (res?.error === 0) {
      Modal.success({
        titleAlign: "start",
        title: '数据导入成功',
        content: `共导入${res?.success || 0}条数据！`
      });
      return
    }
    if (res?.success === 0) {
      Modal.error({
        titleAlign: "start",
        title: '数据导入失败',
        content: `共失败${res?.error || 0}条数据！部分原因为：${res?.errorList?.[0]?.msg || '未知'}`
      });
      return;
    }
    Modal.warning({
      titleAlign: "start",
      title: '数据导入完成',
      content: `成功导入${res?.success || 0}条数据，失败${res?.error || 0}条！部分原因为：${res?.errorList?.[0]?.msg || '未知'} ${res?.errorList?.[1]?.msg || ''} ${res?.errorList?.[2]?.msg || ''}`
    });
  }).catch(e => {
    Modal.error({
      titleAlign: "start",
      title: '数据导入失败',
      content: e || "请联系管理员！"
    });
  })
}
const deleteData = (record:any) => {
  const {eventCode} = record
  deleteEventList(eventCode).then(res => {
    Message.info("事件数据删除成功！")
    refresh()
  }).catch(e => {
    Message.error("事件数据删除失败！", e)
  })
}

const groupForm = ref<any>({})
const editGroupShow = ref<boolean>(false);
const groupFormRef = ref()
const updateLoading = ref(false)
const eventList = ref<any>([])
const relatedLoading = ref(false)
const getEventOptions = async (code?:string) => {
    relatedLoading.value = true
    await getRepEventList({categoryCode:code,inApp:1}).then(res => {
        eventList.value = res
        groupForm.value.relatedEventList = eventList.value.filter(item => item.categoryCode).map(item => item.code)
    })
    relatedLoading.value = false
}
const categoryCodeChange = () => {
  getEventOptions(groupForm.value.code)
}
const batchEditGroup = async () => {
  groupFormRef.value.clearValidate()
  groupFormRef.value.resetFields()
  eventList.value = []
  editGroupShow.value = true;
}
const closeModal = () => {
  editGroupShow.value = false;
}
const batchChangeRepCategory =  (type:string) => { 
  groupFormRef.value.validate().then(async (valid) => {
    if (!valid) {
      try {
        updateLoading.value = true;
        await updateRepCategoryGroups(groupForm.value);
        Message.info("分组批量编辑成功！");
        if (type === 'update') {
          editGroupShow.value = false;
        }
      } catch (e) {
        Message.error("分组批量编辑失败！" + e);
      } finally {
        updateLoading.value = false;
      }
    }
  })
}
const batchAddVisible = ref(false)
const batchFormRef = ref()
const batchForm = ref<any>({})
const bactchLoading = ref(false)
const handleBatchPrefix = () => { 
  batchFormRef.value.resetFields()
  batchAddVisible.value = true
}
const batchAdd = () => { 
   batchFormRef.value.validate().then(async (valid) => {
    if (!valid) {
      try {
        bactchLoading.value = true;
        const res = await saveEventPrefixMatch(batchForm.value.prefix);
        if(res?.success > 0){
          Message.success(`成功导入${res.success}个事件`)
        }else{
          Message.info('没有匹配的事件')
        }
        refresh()
        batchAddVisible.value = false
      } catch (e) {
        console.log(e);
      } finally {
        bactchLoading.value = false;
      }
    }
  })
}
const batchClose = () => { 
  batchAddVisible.value = false
}
</script>

<style scoped lang="less">

</style>