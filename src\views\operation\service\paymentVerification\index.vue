<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange"/>
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="pickDate"/>
        </div>
        <div class="filter-item">
          <a-tooltip>
            <template #content>
              搜索条件：<br/>
              <div v-if="params.orderId">平台订单号:{{params.orderId}}</div>
              <div v-if="params.uuid">用户ID：{{params.uuid}}</div>
              <div v-if="params.payload">payload：{{params.payload}}</div>
              <div>环境：{{getEnvLabel(params.environment)}}</div>
              <div>订单状态：{{getStatusLabel(params.orderStatus)}}</div>
            </template>
            <a-button class="button" :class="hasFilter ? 'filter-active' : ''" style="margin-left: 16px;background: #fff;" @click="openSearchModal">
              <template #icon>
                <icon-filter />
              </template>
              精准搜索
            </a-button>
          </a-tooltip>
        </div>
        <div class="filter-item">
          <a-button class="button" type="primary" style="margin-left: 16px" @click="openModal">
            配置修改
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-tabs type="text">
        <a-tab-pane key="1" title="订单">
          <a-table
              :loading="loading"
              :columns="columns"
              :data="tableData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :pagination="false"
              :table-layout-fixed="true"
              :scrollbar="scrollbar"
              :scroll="scroll">
            <template #orderStatus="{ record }">
              <a-tag :color="getStatusColor(record.orderStatus)">
                {{ getStatusLabel(record.orderStatus) }}
              </a-tag>
            </template>
            <template #environment="{ record }">
              <span v-if="record.environment === 'production'">正式</span>
              <span v-else>沙盒测试</span>
            </template>
            <template #usd="{ record }">
              ${{ record.usd }}
            </template>
          </a-table>
          <div class="pagination">
            <a-pagination :total="orderPage.total" show-total @change="pageChange"/>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" title="统计">
          <a-table
              :loading="countLoading"
              :columns="countColumns"
              :data="countData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :pagination="true"
              :table-layout-fixed="true"
              :scrollbar="scrollbar"
              :scroll="scroll">
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="3" title="验证">
          <VerifyOrder/>
        </a-tab-pane>
      </a-tabs>

    </div>
    <a-modal v-model:visible="modalVisible" :width="520" title-align="start" title="应用支付配置" :footer="false" @cancel="closeModal">
      <a-spin :loading="formLoading" style="width: 100%;">
        <a-form ref="basicFormRef" class="attr-form" :rules="basicRules" :model="basicForm" layout="vertical">
          <a-form-item field="purchaseKey" label="应用密钥" validate-trigger="blur">
            <a-input v-model="basicForm.purchaseKey" placeholder="请输入支付密钥"/>
          </a-form-item>
          <a-form-item field="iosAppItemId" label="iosAppItemId" validate-trigger="blur">
            <a-input v-model="basicForm.iosAppItemId" placeholder="请输入iosAppItemId"/>
          </a-form-item>
          <a-form-item field="deliverUrl" label="回调地址" validate-trigger="blur">
            <a-input v-model="basicForm.deliverUrl" placeholder="请输入回调地址"/>
          </a-form-item>
          <a-form-item field="deliverUrlSalt" label="回调地址盐值" validate-trigger="blur">
            <a-input v-model="basicForm.deliverUrlSalt" placeholder="请输入回调地址盐值"/>
          </a-form-item>
        </a-form>
      </a-spin>
      <div class="footer">
        <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
        <a-button type="primary" :loading="saveLoading" @click="saveData">
          保存
        </a-button>
      </div>
    </a-modal>
    <SearchModal ref="searchRef" @init-data="initData"/>
  </div>

</template>

<script setup lang="ts">

import {reactive, ref,computed} from "vue";
import {getPurchaseDetail, getPurchaseOrders, savePurchaseInfo,getPurchaseStatistic} from "@/api/marketing/api";
import selectApp from "@/components/selected-game-app/index.vue"
import selectCountryList from "@/components/selected-country-list/index.vue"
import {Message} from '@arco-design/web-vue';
import DatePicker from "@/components/date-picker/index.vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {useRoute} from 'vue-router';
import SearchModal from "./components/SearchModal.vue";
import VerifyOrder from "./components/VerifyOrder.vue";

const route = useRoute();
const appId = ref(useSessionStorage('app-id', '')?.value)
const ORDER_STATUS_OPTIONS = [
  { value: 'all', label: '全部' },
  { value: 0, label: '预支付' },
  { value: 1, label: '支付中' },
  { value: 2, label: '支付失败' },
  { value: 3, label: '支付成功' },
  { value: 4, label: '收货确认' },
  { value: 5, label: '退款/退订' }
] as const;
const params = reactive({
  country: ['global'],
  date: {
    recentStartDate: 29,
    recentEndDate: 0,
    dateText: '最近30天'
  },
  orderStatus: 'all',
  environment: 'all',
  uuid:'',
  orderId:'',
  payload:''
})
const hasFilter = computed(() => {
  return params.uuid || params.orderId || params.payload || params.environment !== 'all' || params.orderStatus !== 'all';
});
const orderPage = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})
const tableData = ref<any>([])
const countData = ref<any>([])

const scrollbar = ref(true)
const scroll = {x: 1000, y: 'calc(100vh - 300px)'}
const columns = [
  // {
  //   title: '应用ID',
  //   dataIndex: 'appId',
  //   ellipsis: true,
  //   tooltip: true,
  //   slotName: 'appId',
  //   minWidth: 150,
  //   fixed: 'left'
  // },
  {
    title: '商户交易号',
    dataIndex: 'merchantTransactionId',
    ellipsis: true,
    tooltip: true,
    width: 240,
  },
  {
    title: '平台订单号',
    dataIndex: 'orderId',
    ellipsis: true,
    tooltip: true,
    width: 240,
  },
  {
    title: '用户ID',
    dataIndex: 'uuid',
    ellipsis: true,
    tooltip: true,
    slotName: 'uuid',
    width: 320,
  },
 
  {
    title: '日期',
    dataIndex: 'rpDate',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    ellipsis: "true",
    slotName: 'orderStatus',
    minWidth: 150,
  },
  {
    title: '国家',
    dataIndex: 'country',
    ellipsis: "true",
    minWidth: 150,
  },
  
  {
    title: '环境',
    dataIndex: 'environment',
    ellipsis: "true",
    slotName: 'environment',
    minWidth: 150,
  },
  
  {
    title: '商品名称',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '商品价格',
    dataIndex: 'usd',
    slotName: 'usd',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: 'payload',
    dataIndex: 'payload',
    ellipsis: true,
    tooltip: true,
    width: 320,
  },
]
const countColumns = [
  {
    title: '日期',
    dataIndex: 'rpDate',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '预支付',
    dataIndex: 'status0',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '支付中',
    dataIndex: 'status1',
    ellipsis: true,
    tooltip: true,
    slotName: 'uuid',
    minWidth: 150,
  },
  
  {
    title: '支付失败',
    dataIndex: 'status2',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '支付成功',
    dataIndex: 'status3',
    ellipsis: "true",
    minWidth: 150,
  },
  {
    title: '收货确认',
    dataIndex: 'status4',
    ellipsis: "true",
    minWidth: 150,
  },
  
  {
    title: '退款/退订',
    dataIndex: 'status5',
    ellipsis: "true",
    minWidth: 150,
  },
]
const getStatusColor = (status: string) => {
  const colorMap = {
    0: 'lime',    // 预支付
    1: 'blue', // 支付中
    2: 'red',      // 支付失败
    3: 'green',    // 支付成功
    4: 'green',    // 收货确认
    5: 'gray'     // 退款/退订
  };
  return colorMap[status] || 'gray';
};

const getStatusLabel = (status: string) => {
  return ORDER_STATUS_OPTIONS.find(option => option.value === status)?.label || '未知状态';
};
const getEnvLabel = (env: string) => {
  const envMap = {
    'all': '全部',
    'Production': '正式',
    'Sandbox': '沙盒测试'
  };
  return envMap[env] || '未知';
};
const pickDate = (date: any) => {
  params.date = date
};
const modalVisible = ref(false)
const basicFormRef = ref()
// 表单规则
const basicRules = {
  purchaseKey: [
    {
      required: true,
      message: '请输入支付密钥'
    }
  ],
  deliverUrl: [
    {
      required: false,
      message: '请输入回调地址'
    }
  ],
}

// 表单信息
const basicForm = reactive({
  purchaseKey: '',
  deliverUrl: '',
  deliverUrlSalt: '',
  iosAppItemId:''
});

const loading = ref(true)
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const countLoading = ref(true)
const formLoading = ref(false)
// 获取基本信息
const getInfo = async () => {
  formLoading.value = true
  try {
    await getPurchaseDetail(appId.value).then(res => {
      const {purchaseKey, deliverUrl, deliverUrlSalt,iosAppItemId} = res
      basicForm.purchaseKey = purchaseKey
      basicForm.deliverUrl = deliverUrl
      basicForm.deliverUrlSalt = deliverUrlSalt
      basicForm.iosAppItemId = iosAppItemId
    })
  }catch (error) {
    console.log(error)
  }finally {
    formLoading.value = false
  }
}
const openModal = async () => {
  modalVisible.value = true
}
const closeModal = () => {
  modalVisible.value = false
}
const saveLoading = ref(false)
const saveData = () => {
  basicFormRef.value.validate(async (valid: any) => {
    saveLoading.value = true
    if (!valid) {
      const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
      try {
        const data = {
          appId: appId.value,
          ...basicForm,
          osType: storedData?.osName,
          packageName: storedData?.package,
        }
        await savePurchaseInfo(data)
        Message.success('保存成功');
        getInfo()
        modalVisible.value = false
      } catch (error) {
        console.log(error);
      } finally {
        saveLoading.value = false;
      }
    } else {
      saveLoading.value = false;
    }
  })
}


// 获取订单列表
const getOrderData = async () => {
  loading.value = true
  try {
    const data = {
      appId: appId.value,
      country: params.country.join(','),
      environment: params.environment,
      orderStatus: params.orderStatus,
      uuid: params.uuid,
      orderId: params.orderId,
      payload: params.payload,
      current: orderPage.current,
      pageSize: orderPage.pageSize,
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
    }
    await getPurchaseOrders(data).then(res => {
      tableData.value = res.items
      orderPage.total = res.total
    })
  } catch (error) {
    console.log(error, 'pay-init');

  } finally {
    loading.value = false
  }


}
// 获取统计列表
const getCountData = async () => {
  countLoading.value = true
  try {
    const data = {
      appId: appId.value,
      country: params.country.join(','),
      environment: params.environment,
      orderStatus: params.orderStatus,
      uuid: params.uuid,
      orderId: params.orderId,
      payload: params.payload,
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
    }
    await getPurchaseStatistic(data).then(res => {
      countData.value = res
    })
  } catch (error) {
    console.log(error, 'pay-init');

  } finally {
    countLoading.value = false
  }
}

const countryChange = async (v) => {
  params.country = v
  Promise.all([
    getOrderData(),
    getCountData()
  ])
}
const searchRef = ref()
const openSearchModal = () => {
  searchRef.value?.openModal()
}
const initData = (data) => {
  orderPage.current = 1;
  const { uuid, orderId, payload,environment,orderStatus } = data;
  params.uuid = uuid;
  params.orderId = orderId;
  params.payload = payload;
  params.environment = environment;
  params.orderStatus = orderStatus;
  Promise.all([
    getOrderData(),
    getCountData()
  ])
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    appId.value = value
    // Promise.all([
    //   getInfo(),
    //   getOrderData(),
    //   getCountData()
    // ])
    getInfo()
    searchRef.value?.resetData()
  }
})

const pageChange = (v) => {
  orderPage.current = v
  getOrderData()
}
const init = async () => {
  try {
    Promise.all([
      getInfo(),
      getOrderData(),
      getCountData()
    ])
  } catch (error) {
    console.log(error, 'pay-init');
  }
}
init()
</script>

<style scoped lang="less">
.filter-active{
  color: rgb(var(--primary-6));
  border:1px solid rgb(var(--primary-6)) !important;
}
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
</style>