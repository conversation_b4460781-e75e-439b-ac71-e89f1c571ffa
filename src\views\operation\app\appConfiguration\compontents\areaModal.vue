<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" title="编辑国家地区" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="地区">
                <a-select v-model:model-value="form.name" allow-create :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="handleChange">
                    <a-option v-for="(item,index) in props.areaList" :key="index" :value="item.name">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="members" label="国家">
                <a-select v-model:model-value="form.members" multiple :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="(item,index) in countryList" :key="index" :value="item.code">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {areaAdd, areaUpdate, getCountryList} from "@/api/marketing/api";

const props = defineProps({
    areaList: {
      type: Array,
      default: () => [],
    }
})
const modalVisible = ref(false)
const countryList = ref<any>([])
const form = reactive({
    name: '',
    code: '',
    members:[]
})
const rules = {
    name: [
        {
            required: true,
            message:'请选择地区',
        }
    ],
    members: [
        {
            required: true,
            message:'请选择国家',
        }
    ],
}
const formRef = ref()

const getData = async () => {
    await getCountryList().then(res => {
        countryList.value = res
    })
}
const openModal = async (value:string) => {
    await getData()
    form.code = value
    props.areaList.forEach(item => {
        if(item.code === value) {
            form.name = item.name
            form.members = item.members
        }
    })
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
    formRef.value.resetFields()
    formRef.value.clearValidate()
}
const handleChange = (v) => {
    const hasNameEqualToV = props.areaList.some(item => item.name === v);
    if(hasNameEqualToV){
        form.name = v
        props.areaList.forEach(item => {
            if(item.name === v) {
                form.code = item.code
                form.members = item.members
            }
        })
    }else{
        form.code = ''
        form.name = v
        form.members = []
    }
    
}
const emits = defineEmits(['updateData']);
const saveData = () => {
    formRef.value.validate((valid:any) => {
        if (!valid) {
            // 
            console.log(form,'form');
            if(form.code){
                // 更新
                try {
                    areaUpdate(form).then(res => {
                        Message.success('更新成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('更新失败:', error);
                }
            }else{
                // 添加
                try {
                    areaAdd(form).then(res => {
                        Message.success('创建成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('创建失败:', error);
                }
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>