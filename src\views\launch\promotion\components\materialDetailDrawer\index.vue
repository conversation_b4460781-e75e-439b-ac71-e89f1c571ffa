<template>
    <a-drawer width="90%" :visible="modalVisible" unmountOnClose :footer="false" class="drill-drawer" @cancel="() => modalVisible = false">
        <template #title>
            {{ modalTitle }}
        </template>
        <a-spin :loading="loading" style="width: 100%;height: 100%;display: flex;flex-direction: column;">
            <a-tabs>
                <template #extra>
                    <DateRangePicker v-model="dateTime" />
                </template>
                <a-tab-pane v-if="showDetail" key="1" title="详情">
                    <DetailTab/>
                </a-tab-pane>
                <a-tab-pane key="2" title="趋势">
                    <TrendTab/>
                </a-tab-pane>
                <a-tab-pane v-if="!showDetail" key="3" title="素材">
                    <TrendTab/>
                </a-tab-pane>
                <a-tab-pane v-if="showDetail" key="4" title="创意">
                    <IdeaTab/>
                </a-tab-pane>
                <a-tab-pane v-if="showDetail && showChannel" key="5" title="渠道">
                    <ChannelTab/>
                </a-tab-pane>
                <a-tab-pane key="6" title="地区">
                    <AreaTab/>
                </a-tab-pane>
            </a-tabs>
        </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref,inject} from "vue";
import dayjs from 'dayjs';
import DateRangePicker from "@/components/date-quick-picker/index.vue";
import DetailTab from "./DetailTab.vue";
import TrendTab from "./TrendTab.vue";
import IdeaTab from "./IdeaTab.vue";
import ChannelTab from "./ChannelTab.vue";
import AreaTab from "./AreaTab.vue";

const props = defineProps({
    showDetail: {
        type: Boolean,
        default: true
    },
    showChannel:{
        type: Boolean,
        default: true
    }
})
const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref('素材详情')

const today = dayjs();
const dateTime = ref(inject('dateTime') as any[])
const openModal = async (obj?:any) => {
    modalTitle.value = obj?.materialName
    // loading.value = true
    modalVisible.value = true
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
:global(.drill-drawer .arco-drawer-title) {
    width: 100%!important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.br4{
    border-radius: 4px;
}
</style>