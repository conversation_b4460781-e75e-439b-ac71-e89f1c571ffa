<template>
  <a-drawer
      :width="680"
      :unmount-on-close=true
      :visible="boardPushvisible"
      :mask-closable="false"
      @ok="handleOk"
      @cancel="handleCancel">
    <template #title>
      <div>看板推送</div>
    </template>
    <div class="drawer-body">
      <div class="boardPush-setting">
        <div class="push-setting">
          <span class="push-set">推送设置</span>
          <div>
            <a-tooltip
                content="看板推送将按看板默认全局筛选条件和你的数据权限，在你选定的时间开始计算，并于计算完成后立即推送"
                position="right">
              <icon-exclamation-circle/>
            </a-tooltip>
          </div>
        </div>
        <div class="boardPush-datesCss">
          <span class="boardPush-span">基础数据</span>
          <time-zone-select @selected="onChange" class="nav-right-picktime"/>
        </div>
        <div style="display:grid;margin-bottom: 20px">
          <div class="boardPush-datesCss-followtime">
            <span class="boardPush-span">定时推送</span>
            <a-switch v-model="switchValueTime" size="small"/>
          </div>
          <span v-if="!switchValueTime" style="color: #6b6c77;opacity: .8"
                class="boardPush-span">暂无定时推送内容</span>
          <div v-else>
          <span style="display: flex ;gap:10px">
          <a-select
              :model-value="selectValues"
              :style="{width:'360px'}"
              multiple :max-tag-count='2'
              @update:model-value="WeekHandleChange">
                <a-option :value="0">周一</a-option>
                <a-option :value="1">周二</a-option>
                <a-option :value="2">周三</a-option>
                <a-option :value="3">周四</a-option>
                <a-option :value="4">周五</a-option>
                <a-option :value="5">周六</a-option>
                <a-option :value="6">周日</a-option>
          <template #footer>
           <div style="padding: 6px 12px;">
             <a-checkbox
                 :indeterminate="isIndeterminate"
                 :default-checked="isAllSelected"
                 @change="handleClickAllSelect">全选</a-checkbox>
           </div>
          </template>
         </a-select>
        <a-select
            :model-value="nextPushTime"
            placeholder="请输入搜索 "
            :style="{width:'90px'}"
            @update:model-value="HourhandleChange">
          <a-option
              v-for="timezone in timeZones"
              :key="timezone"
              :value="timezone"
              :v-model="timezone">
            {{ timezone }}
          </a-option>
        </a-select>
        <span style="color: #6b6c77;opacity: .8 ;align-content: center" class="boardPush-span">UTC+08</span>
          </span>
            <div v-if="selectValues">
               <span
                   style="color: #6b6c77;opacity: .8 ;align-content: center"
                   class="boardPush-span">

            <div v-if="diffWeek>0">
            <span v-if="diffWeek==1&&diffMin>=0">下次推送预计在{{ diffWeek }}天{{ diffMin }}小时后开始计算</span>
            <span v-if="diffWeek==1&&diffMin<0">下次推送预计在{{ 24 + diffMin }}小时后开始计算</span>
            <span v-if="diffWeek>1&&diffMin>=0">下次推送预计在{{ diffWeek }}天{{ diffMin }}小时后开始计算</span>
            <span v-if="diffWeek>1&&diffMin<0">下次推送预计在{{ diffWeek - 1 }}天{{ 24 + diffMin }}小时后开始计算</span>
            </div>
            <div v-if="diffWeek<0">
            <span v-if="diffWeek==-6&&diffMin>=0">下次推送预计在{{ 7 + diffWeek }}天{{ diffMin }}小时后开始计算</span>
            <span v-if="diffWeek==-6&&diffMin<0">下次推送预计在{{ 24 + diffMin }}小时后开始计算</span>
            <span v-if="diffWeek>-6&&diffMin>=0">下次推送预计在{{ 7 + diffWeek }}天{{ diffMin }}小时后开始计算</span>
            <span v-if="diffWeek>-6&&diffMin<0">下次推送预计在{{ 6 + diffWeek }}天{{
                24 + diffMin
              }}小时后开始计算</span>
            </div>
            <div v-if="diffWeek==0">
            <span v-if="diffMin>=0">下次推送预计在{{ diffMin }}小时后开始计算</span>
            <span v-if="diffMin<0">下次推送预计在6天{{ 24 + diffMin }}小时后开始计算</span>
            </div>

          </span>
            </div>


          </div>
        </div>
        <!--        推送渠道-->
        <push-channels/>
      </div>


      <a-divider class="half-divider"/>

      <div class="boardPush-setting">
        <div class="push-setting">
          <div class="push-content">
            <div class="push-content-head">
              <span class="push-set">推送内容</span>
            </div>
            <div class="push-checkbox">
              <a-checkbox :default-checked="true" disabled> 推送看板图片</a-checkbox>
              <a-checkbox> 推送看板数据</a-checkbox>
              <div>
                <a-tooltip
                    content="看板数据为CSV文件或下载链接，下载链接将于30日后失效。机器人将推送下载链接；邮箱推送时小于30M的CSV文件直接推送
                            其他情况推送下载链接；无SMTP的邮件推送仅支持推送下载链接。"
                    position="right">
                  <icon-exclamation-circle/>
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>

        <div class="boardPush-datesCss">
          <div class="push-content">
            <div class="push-content-head">
              <span class="boardPush-span">图片样式</span>
              <a-tooltip
                  content="标准宽度为1920px，宽屏宽度为2880px。宽屏可横向展示更多信息，适合宽表看板"
                  position="right">
                <icon-exclamation-circle/>
              </a-tooltip>
            </div>
            <div class="push-radio">
              <a-radio-group>
                <a-radio value="a">标准</a-radio>
                <a-radio value="b">宽屏</a-radio>
              </a-radio-group>
            </div>
          </div>
        </div>

        <div class="boardPush-datesCss">
          <div class="push-content">
            <div class="push-content-head">
              <span class="boardPush-span">推送标题</span>
              <a-tooltip
                  content="可以使用替换符，$[看板名]$，$[日期]$，$[昨日日期]$，$[看板计算时间]$"
                  position="right">
                <icon-exclamation-circle/>
              </a-tooltip>
            </div>
            <div class="input-margin">
              <input class="a-input" value="$[看板名]$ $[日期]$" placeholder="$[看板名]$ $[日期]$"/>
            </div>

          </div>
        </div>


        <div class="boardPush-datesCss">
          <div class="push-content">
            <div class="push-content-head">
              <span class="boardPush-span">推送正文</span>
            </div>
            <div class="input-margin">
              <a-textarea class="push-textarea" default-value="你好，
                             以下是$[看板名]$看板截至 $[看板计算时间]$ 的数据快报，请查收。" placeholder="你好，
                             以下是$[看板名]$看板截至 $[看板计算时间]$ 的数据快报，请查收。" :max-length="200" allow-clear
                          show-word-limit/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import dayjs from "dayjs";
import timeZoneSelect from "@/components/time-zone-select/index.vue";
import pushChannels from "@/components/navbar-board/pushchannels.vue"


const nowTimeWeek = dayjs().format('dddd')
const nowTimeMin = dayjs().format('HH')
const timeList = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday',
]
const nowTimeIndex = (timeList.findIndex((v) => {
  return v === nowTimeWeek
}))
const timeZones = [
  '00:00', '01:00', '02:00', '03:00', '04:00',
  '05:00', '06:00', '07:00', '08:00', '09:00', '10:00',
  '11:00', '12:00', '13:00', '14:00', '15:00', '16:00',
  '17:00', '18:00', '19:00', '20:00', '21:00', '22:00',
  '23:00'
];
const nextPushTime = ref<string>(timeZones[9])
const allOptions = [0, 1, 2, 3, 4, 5, 6];
const selectValues = ref<number[]>(allOptions)
const isAllSelected = computed(() => {
  return selectValues.value.length === allOptions.length;
});
const isIndeterminate = computed(() => {
  return selectValues.value.length > 0 && !isAllSelected.value;
})
const handleClickAllSelect = (e: Event) => {
  if (e) {
    selectValues.value = allOptions
  } else {
    selectValues.value = []
  }
}
const diffWeek = ref(selectValues.value.sort()[0] - nowTimeIndex)
const WeekHandleChange = (values: number[]) => {
  selectValues.value = values;
  if (selectValues.value.sort()[0] !== nowTimeIndex) {
    diffWeek.value = selectValues.value.sort()[0] - nowTimeIndex
  }
}
const diffMin = ref((parseFloat(nextPushTime.value)) - parseFloat(nowTimeMin))
const HourhandleChange = (values: string) => {
  nextPushTime.value = values
  if (parseFloat(nextPushTime.value)) {
    diffMin.value = ((parseFloat(nextPushTime.value)) - parseFloat(nowTimeMin))
  }


}
// 时间选择器组件
const lastDate = ref('0')
const timeTriggerVisible = ref<boolean>(false);
const onChange = (value) => {
  lastDate.value = value;
  timeTriggerVisible.value = false
}
const switchValueTime = ref<boolean>(false)
const boardPushvisible = ref<boolean>(false)
const handleClick = () => {
  boardPushvisible.value = true;
};
const handleOk = () => {
  boardPushvisible.value = false;
};
const handleCancel = () => {
  boardPushvisible.value = false;
}


defineExpose({
  handleClick
})


</script>

<style lang="less" scoped>
.boardPush-setting {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
  color: var(--tant-text-gray-color-text1-2);

  .boardPush-datesCss {
    display: flex;
    align-items: center;

    margin-bottom: 24px;
  }
}

.boardPush-span {
  color: #333;
  font-style: normal;
  margin-right: 10px;

}

.push-setting {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 5px;

  .push-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }


  .push-content-head {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .push-set {
    font-size: 16px;
  }

  .push-checkbox {
    display: flex;
    gap: 10px;


  }
}

.boardPush-datesCss-followtime {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.nav-right-picktime-body {
  display: flex;
  gap: 2px;
}


.push-radio {
  display: flex;
  margin-top: 10px;
}

.a-input {
  height: 32px;
  padding: 0 8px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  line-height: 30px;
  background-color: transparent;
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
  width: 638px;
  outline: none;
  box-shadow: none;
}

.a-input:hover {
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-primary-color-primary-hover);
}

.push-textarea {
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
  width: 638px;
}

.drawer-body {
  padding: 20px 16px;
}

.drawer-footer {
  padding: 20px 16px;

}

.input-margin {
  margin-top: 12px;
}

.nav-right-picktime {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
}
.nav-right-picktime:hover{
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-primary-color-primary-hover);
}
</style>