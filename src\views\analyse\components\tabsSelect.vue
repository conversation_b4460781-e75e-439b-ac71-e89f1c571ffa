<template>
  <div>
    <!-- 全部 事件属性 用户属性 用户分群 用户标签下拉选 -->
    <div class="screen-body">
      <a-trigger
          v-model:popup-visible="triggerVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          :update-at-scroll="true"
          style="z-index: 1002;"
          @click="handleTriggerVisible">
        <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
          <a-button v-if="!props.disabled" class="filter-btn">
            <img src="/icon/eventAttr.svg" class="btn-icon">
            <span v-if="filterData.objectName" class="filter-label">{{ filterData.objectDisplayName }}</span>
            <span v-else class="filter-label un-filter-label">选择属性</span>
            <span v-if="filterData.type == '4'" class="virtual-sign">*</span>
          </a-button>
          <template #content>
            <div class="trigger-box">
              <div class="card-header">
                <div class="card-header-container">
                  <div class="header-icon">
                    <icon-launch/>
                  </div>
                  <div class="header-title">
                    <div class="name">{{ filterData.objectDisplayName }}</div>
                  </div>
                  <div class="header-type">{{ getAttrName(filterData) }}</div>
                </div>
                <div class="title-sub">{{ filterData.objectName }}</div>
              </div>
              <div v-if="filterData.note" class="card-desc">
                <div class="span-desc">{{ filterData.note }}</div>
              </div>
              <div v-else class="prop-bubble-card-desc">
                <a-button v-if="!showTextArea" style="background-color: transparent;" @click="() => showTextArea = true">
                  <template #icon>
                    <icon-plus/>
                  </template>
                  <template #default>添加备注</template>
                </a-button>
                <a-textarea v-if="showTextArea" :max-length="200" allow-clear show-word-limit/>
              </div>
              <div v-if="filterData.filterType === 'cluster' || filterData.filterType === 'tag'" class="card-detail">
                <div v-if="filterData.filterType === 'cluster'" class="detail-item">分群人数：{{ filterData.userNumber }}</div>
                <div v-if="filterData.filterType === 'tag'" class="detail-item">标签人数：{{ filterData.userNumber }}</div>
                <div class="detail-item">分析主体: {{ filterData.entityName }}</div>
                <div class="detail-item">时区：{{ filterData.timeZone }}</div>
                <div class="detail-item">数据更新时间：{{ dayjs(filterData.lastUpdateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
                <div class="detail-item">由 {{ filterData.creatorName }} 在 创建</div>
              </div>
              <div class="card-footer">
                <div v-if="filterData.filterType !== 'cluster'">
                  {{ getLabel(filterData.objectType) }}
                </div>
                <div v-else></div>
                <div class="action">
                  <a-tooltip content="前往属性详情" position="top" style="z-index: 1003;">
                    <div class="action-icon">
                      <icon-launch/>
                    </div>
                  </a-tooltip>
                </div>
              </div>
            </div>
          </template>
        </a-trigger>
        <template #content>
          <div class="screen-content">
            <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
              <a-input v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                <template #prefix>
                  <icon-search/>
                </template>
              </a-input>
            </div>
            <div>
              <a-tabs
                  :destroy-on-hide="true"
                  default-active-key="1" size="mini" class="screen-content-body">
                <a-tab-pane key="1" title="全部">
                  <a-list :virtual-list-props="{ height: 350 }" :data="filteredAllList">
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <div v-if="index == 0" style="border-bottom: 1px solid var(--tant-border-color-border1-1);padding-bottom: 8px;">
                          <div class="affix-eventName">
                            <img src="/icon/collect.svg">
                            收藏
                          </div>
                          <div v-for="(event,eventIndex) in collectLists" :key="eventIndex" ref="collect">
                            <a-doption :disabled='ChooseLists.includes(event.code)' :style="filterData.objectDisplayName ===event.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                              <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                                <div class="screen-option">
                                  <div class="screen-option" @click="handleEvent(event)">
                                    <div class="screen-option-name">
                                      {{ event.objectDisplayName }}
                                    </div>
                                    <div class="screen-option-attr">
                                      {{ getAttrName(event) }}
                                    </div>
                                    <div class="screen-option-type">
                                      <div v-if="filterData.filterType !== 'cluster'" class="type-text">
                                        <img :src="getIcon(event.objectType)" class="typeIcon"/>
                                        {{ getLabel(event.objectType) }}
                                      </div>
                                      <div v-else class="type-text"></div>
                                    </div>
                                  </div>
                                  <div class="screen-option-collect">
                                    <a-tooltip content="取消收藏" position="top" mini>
                                      <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(event)">
                                    </a-tooltip>
                                  </div>
                                </div>
                                <template #content>
                                  <div class="trigger-box">
                                    <div class="card-header">
                                      <div class="card-header-container">
                                        <div class="header-icon">
                                          <icon-launch/>
                                        </div>
                                        <div class="header-title">
                                          <div class="name">{{ event.objectDisplayName }}</div>
                                        </div>
                                        <div class="header-type">{{ getAttrName(event) }}</div>
                                      </div>
                                      <div class="title-sub">{{ event.name }}</div>
                                    </div>
                                    <div class="card-desc">
                                      <div v-if="event.note" class="span-desc">{{ event.note }}</div>
                                      <div v-else class="span-desc">暂无备注</div>
                                    </div>
                                    <div v-if="event.filterType === 'cluster' || filterData.filterType === 'tag'" class="card-detail">
                                      <div v-if="event.filterType === 'cluster'" class="detail-item">分群人数：{{ event.userNumber }}</div>
                                      <div v-if="event.filterType === 'tag'" class="detail-item">标签人数：{{ event.userNumber }}</div>
                                      <div class="detail-item">分析主体: {{ event.entityName }}</div>
                                      <div class="detail-item">时区：{{ event.timeZone }}</div>
                                      <div class="detail-item">数据更新时间：{{ dayjs(event.lastUpdateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
                                      <div class="detail-item">由 {{ event.creatorName }} 在 创建</div>
                                    </div>
                                    <div class="card-footer">
                                      <div v-if="filterData.filterType !== 'cluster'">
                                        {{ getLabel(event.objectType) }}
                                      </div>
                                      <div class="action">
                                        <a-tooltip content="前往属性详情" position="top">
                                          <div class="action-icon">
                                            <icon-launch/>
                                          </div>
                                        </a-tooltip>
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </a-doption>
                          </div>
                        </div>
                        <div v-if="item.typeName" class="affix-eventName">
                          <img v-if="item.typeName == '事件属性'" src="/icon/eventAttr.svg">
                          <img v-if="item.typeName == '用户属性'" src="/icon/userAttr.svg">
                          <img v-if="item.typeName == '用户分群'" src="/icon/usergroup.svg">
                          <img v-if="item.typeName == '用户标签'" src="/icon/usergroup.svg">
                          {{ item.typeName }}
                        </div>
                        <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                            <div v-if="item.objectDisplayName" class="screen-option">
                              <div class="screen-option" @click="handleEvent(item)">
                                <div class="screen-option-name">
                                  {{ item.objectDisplayName }}
                                </div>
                                <div class="screen-option-attr">
                                  {{ getAttrName(item) }}
                                </div>
                                <div class="screen-option-type">
                                  <div v-if="filterData.filterType !== 'cluster'" class="type-text">
                                    <img :src="getIcon(item.objectType)" class="typeIcon"/>
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                  <div v-else class="type-text"></div>
                                </div>
                              </div>
                              <div class="screen-option-collect">
                                <div v-if="!item.collectType">
                                  <a-tooltip content="点击收藏" position="top" mini>
                                    <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                  </a-tooltip>
                                </div>
                                <div v-else>
                                  <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <icon-launch/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.objectDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getAttrName(item) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.name }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div v-if="item.filterType === 'cluster' || filterData.filterType === 'tag'" class="card-detail">
                                  <div v-if="item.filterType === 'cluster'" class="detail-item">分群人数：{{ item.userNumber }}</div>
                                  <div v-if="filterData.filterType === 'tag'" class="detail-item">标签人数：{{ item.userNumber }}</div>
                                  <div class="detail-item">分析主体: {{ item.entityName }}</div>
                                  <div class="detail-item">时区：{{ item.timeZone }}</div>
                                  <div class="detail-item">数据更新时间：{{ dayjs(item.lastUpdateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
                                  <div class="detail-item">由 {{ item.creatorName }} 在 创建</div>
                                </div>
                                <div class="card-footer">
                                  <div v-if="filterData.filterType !== 'cluster'">
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                  <div v-else></div>
                                  <div class="action">
                                    <a-tooltip content="前往属性详情" position="top">
                                      <div class="action-icon">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-doption>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
                <a-tab-pane v-show="!props.excludeEvent" v-if="showPane2" key="2" title="事件属性">
                  <a-list :virtual-list-props="{ height: 300 }" :data="filteredEventLists">
                    <template #header>
                      <div class="affix-eventName">
                        <img src="/icon/eventAttr.svg">
                        事件属性
                      </div>
                    </template>
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                            <div class="screen-option">
                              <div class="screen-option" @click="handleEvent(item)">
                                <div class="screen-option-name">
                                  {{ item.objectDisplayName }}
                                </div>
                                <div class="screen-option-attr">
                                  {{ getAttrName(item) }}
                                </div>
                                <div class="screen-option-type">
                                  <div class="type-text">
                                    <img :src="getIcon(item.objectType)" class="typeIcon"/>
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                </div>
                              </div>
                              <div class="screen-option-collect">
                                <div v-if="!item.collectType">
                                  <a-tooltip content="点击收藏" position="top" mini>
                                    <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                  </a-tooltip>
                                </div>
                                <div v-else>
                                  <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <icon-launch/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.objectDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getAttrName(item) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.name }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-footer">
                                  <div>{{ getLabel(item.objectType) }}</div>
                                  <div class="action">
                                    <a-tooltip content="前往属性详情" position="top">
                                      <div class="action-icon">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-doption>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
                <a-tab-pane v-if="showPane3" key="3" title="用户属性">
                  <a-list :virtual-list-props="{ height: 300 }" :data="filteredUserLists">
                    <template #header>
                      <div class="affix-eventName">
                        <img src="/icon/userAttr.svg">
                        用户属性
                      </div>
                    </template>
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                            <div class="screen-option">
                              <div class="screen-option" @click="handleEvent(item)">
                                <div class="screen-option-name">
                                  {{ item.objectDisplayName }}
                                </div>
                                <div class="screen-option-attr">
                                  {{ getAttrName(item) }}
                                </div>
                                <div class="screen-option-type">
                                  <div v-if="item.objectType" class="type-text">
                                    <img :src="getIcon(item.objectType)" class="typeIcon"/>
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                  <div v-else class="type-text"></div>
                                </div>
                              </div>
                              <div class="screen-option-collect">
                                <div v-if="!item.collectType">
                                  <a-tooltip content="点击收藏" position="top" mini>
                                    <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                  </a-tooltip>
                                </div>
                                <div v-else>
                                  <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <icon-launch/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.objectDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getAttrName(item) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.name }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-footer">
                                  <div>{{ getLabel(item.objectType) }}</div>
                                  <div class="action">
                                    <a-tooltip content="前往属性详情" position="top">
                                      <div class="action-icon">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-doption>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
                <a-tab-pane v-if="showPane4" key="4" title="用户分群">
                  <a-list :virtual-list-props="{ height: 300 }" :data="filteredUserGroup">
                    <template #header>
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户分群
                      </div>
                    </template>
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                            <div class="screen-option">
                              <div class="screen-option" @click="handleEvent(item)">
                                <div class="screen-option-name">
                                  {{ item.objectDisplayName }}
                                </div>
                                <div class="screen-option-attr">{{ getAttrName(item) }}</div>
                                <div class="screen-option-type">
                                  <div v-if="item.objectType" class="type-text">
                                    <img :src="getIcon(item.objectType)" class="typeIcon"/>
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                  <div v-else class="type-text"></div>
                                </div>
                              </div>
                              <div class="screen-option-collect">
                                <div v-if="!item.collectType">
                                  <a-tooltip content="点击收藏" position="top" mini>
                                    <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                  </a-tooltip>
                                </div>
                                <div v-else>
                                  <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <icon-launch/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.objectDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getAttrName(item) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.name }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-detail">
                                  <div class="detail-item">分群人数：{{ item.userNumber }}</div>
                                  <div class="detail-item">分析主体: {{ item.entityName }}</div>
                                  <div class="detail-item">时区：{{ item.timeZone }}</div>
                                  <div class="detail-item">数据更新时间：{{ dayjs(item.lastUpdateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
                                  <div class="detail-item">由 {{ item.creatorName }} 在 创建</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <a-tooltip content="前往属性详情" position="top">
                                      <div class="action-icon">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-doption>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
                <a-tab-pane v-if="showPane5" key="5" title="用户标签">
                  <a-list :virtual-list-props="{ height: 300 }" :data="filteredUserTag">
                    <template #header>
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户标签
                      </div>
                    </template>
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.objectDisplayName?'backgroundColor: var(--color-fill-2);' : ''">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                            <div class="screen-option">
                              <div class="screen-option" @click="handleEvent(item)">
                                <div class="screen-option-name">
                                  {{ item.objectDisplayName }}
                                </div>
                                <div class="screen-option-attr">
                                  {{ getAttrName(item) }}
                                </div>
                                <div class="screen-option-type">
                                  <div v-if="item.objectType" class="type-text">
                                    <img :src="getIcon(item.objectType)" class="typeIcon"/>
                                    {{ getLabel(item.objectType) }}
                                  </div>
                                  <div v-else class="type-text"></div>
                                </div>
                              </div>
                              <div class="screen-option-collect">
                                <div v-if="!item.collectType">
                                  <a-tooltip content="点击收藏" position="top" mini>
                                    <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                  </a-tooltip>
                                </div>
                                <div v-else>
                                  <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <icon-launch/>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.objectDisplayName }}</div>
                                    </div>
                                    <div class="header-type">{{ getAttrName(item) }}</div>
                                  </div>
                                  <div class="title-sub">{{ item.name }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-detail">
                                  <div class="detail-item">标签人数：{{ item.userNumber }}</div>
                                  <div class="detail-item">分析主体: {{ item.entityName }}</div>
                                  <div class="detail-item">时区：{{ item.timeZone }}</div>
                                  <div class="detail-item">数据更新时间：{{ dayjs(item.lastUpdateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
                                  <div class="detail-item">由 {{ item.creatorName }} 在 创建</div>
                                </div>
                                <div class="card-footer">
                                  <div>{{ getLabel(item.objectType) }}</div>
                                  <div class="action">
                                    <a-tooltip content="前往属性详情" position="top">
                                      <div class="action-icon">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-doption>
                      </a-list-item>
                    </template>
                  </a-list>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </template>
      </a-trigger>
      <div v-if="props.disabled" class="filter-btn-disabled">
        <img src="/icon/eventAttr.svg" class="btn-icon">
        <span v-if="filterData.objectName" class="filter-label">{{ filterData.objectDisplayName }}</span>
        <span v-else class="filter-label un-filter-label">选择属性</span>
        <span v-if="filterData.type == '4'" class="virtual-sign">*</span>
      </div>
      <a-trigger
          v-if="props.showDetailFilter"
          v-model:popup-visible="subVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          :update-at-scroll="true"
          @click="handleSubVisible">
        <a-button v-if="!props.disabled" class="filter-btn" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
            <span class="filter-label" style="white-space:wrap;text-align: left;">
              {{ filterData.calcuSymbolName }}
              <span v-if="showSelectData && !filterData.selectArr.length">{{ nameString }}</span>
              <span v-if="showSelectArr && (filterData.objectType === 'string' || filterData.type === 4)">{{ nameString }}</span>
              <span v-if="showInputData">{{ filterData.inputData }}</span>
              <span v-if="showNumber">{{ filterData.number1 }}至{{ filterData.number2 }}之间</span>
              <span v-if="showDateArr">{{ filterData.dateArr[0] }}至{{ filterData.dateArr[1] }}之间</span>
              <span v-if="showDateValue">{{ filterData.dateValue }}</span>
              <span v-if="(filterData.objectType == 'date' || filterData.objectType == 'datetime') && filterData.calcuSymbolName == '相对当前日期'">
                <span v-if="filterData.dateType == 'scope'">
                  在{{ filterData.dateNumber1 }}到{{ filterData.dateNumber2 }}
                  <span v-if="filterData.dateTypeUnit == 'd'">天</span>
                  <span v-if="filterData.dateTypeUnit == 'h'">小时</span>
                  <span v-if="filterData.dateTypeUnit == 'm'">分钟</span>
                  之间
                </span>
                <span v-if="filterData.dateType == 'past'">
                  在过去{{ filterData.dateNumber1 }}天前
                </span>
                <span v-if="filterData.dateType == 'cd'">在当天</span>
                <span v-if="filterData.dateType == 'cw'">在当周</span>
                <span v-if="filterData.dateType == 'cm'">在当月</span>
              </span>
            </span>
        </a-button>
        <template #content>
          <div class="builder-content">
            <div class="builder-panel-container">
              <span class="quot-name">{{ filterData.objectDisplayName }}</span>
              <!-- 第一级筛选条件 -->
              <div v-if="filterData.objectType == 'string'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('textRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in textRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'int' || filterData.objectType == 'float'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('valueRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in valueRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'array'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('valueRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in itemRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType && ['date', 'datetime'].includes(filterData.objectType)" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('dateRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in dateRangeList" :key="index" style="width: 200px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'boolean'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('booleanRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in booleanRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="!filterData.objectType" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('groupingList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in groupingList" :key="index" style="width: 200px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="filterData.objectType == 'boolean' && filterData.calcuSymbolName == '等于'" class="operator-select">
                  <a-select style="width:60px" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="booleanChange">
                    <template #arrow-icon>
                    </template>
                    <a-option :value="true">真</a-option>
                    <a-option :value="false">假</a-option>
                  </a-select>
                </div>
                <!-- 不同选择项，第二级筛选条件 -->
                <div v-if="filterData.objectType && ['string', 'int','float', 'array'].includes(filterData.objectType)" class="equal-search">
                  <!-- 多选时 -->
                  <a-select
                      v-if="['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName) && (filterData.objectType === 'string' || filterData.type === 4)"
                      v-model="filterData.selectArr"
                      multiple
                      allow-search
                      allow-create
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      :show-extra-options="false"
                      @change="selectArrChange"
                      @popup-visible-change="selectArrPopup">
                    <template #arrow-icon>
                    </template>
                    <template #search-icon>
                    </template>
                    <template #loading-icon>
                    </template>
                    <a-option v-for="(item,index) in selectEnumList" :key="index" :value="item.code">{{ item.name }}</a-option>
                  </a-select>
                  <!-- 单选时 -->
                  <a-select
                      v-if="(filterData.calcuSymbolName == '包括' || filterData.calcuSymbolName == '不包括') && (filterData.objectType === 'string' || filterData.type === 4)"
                      v-model="filterData.selectData"
                      style="width: 82px;"
                      allow-search
                      allow-create
                      :show-extra-options="false"
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      @change="selectDataChange"
                      @popup-visible-change="selectArrPopup">
                    <template #arrow-icon>
                    </template>
                    <template #search-icon>
                    </template>
                    <template #loading-icon>
                    </template>
                    <a-option v-for="(item,index) in selectEnumList" :key="index" :value="item.code">{{ item.name }}</a-option>
                  </a-select>
                  <a-input
                      v-if="['等于', '不等于', '存在元素', '不存在元素','包括','不包括'].includes(filterData.calcuSymbolName)
                    && filterData.objectType !== 'string' 
                    && filterData.type !== 4"
                      v-model="filterData.selectData"
                      style="width: 80px;"
                      @input="selectDataChange"/>
                  <div
                      v-if="['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)
                  && filterData.objectType === 'string'"
                      class="option-edit"
                      @click="handleAddClick">
                    <icon-edit/>
                  </div>
                  <a-input
                      v-if="['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)" v-model="filterData.inputData" style="width: 80px;"
                      @change="inputDataChange"/>
                  <div v-if="filterData.calcuSymbolName == '区间'" class="next-input">
                    <div class="next-input-number">
                      <a-input-number v-model="filterData.number1" @blur="value1Change"/>
                    </div>
                    <span class="word">与</span>
                    <div class="next-input-number">
                      <a-input-number v-model="filterData.number2" @blur="value2Change"/>
                    </div>
                    <span class="word">之间</span>
                  </div>
                </div>
              </div>
              <div
                  v-if="(filterData.objectType && ['date', 'datetime'].includes(filterData.objectType))
                && (filterData.calcuSymbolName == '小于等于' || filterData.calcuSymbolName == '大于等于' || filterData.calcuSymbolName == '位于区间')"
                  class="absolute-date">
                <a-date-picker
                    v-if="filterData.calcuSymbolName == '小于等于' || filterData.calcuSymbolName == '大于等于'"
                    v-model="filterData.dateValue"
                    style="width: 150px;"
                    show-time
                    :time-picker-props="{ defaultValue: '00:00:00' }"
                    format="YYYY-MM-DD HH:mm:ss"
                    @change="onChange"
                    @select="onSelect"
                    @ok="onOk"
                />
                <a-range-picker
                    v-if="filterData.calcuSymbolName == '位于区间'"
                    v-model="filterData.dateArr"
                    style="width: 360px;"
                    show-time
                    :time-picker-props="{ defaultValue: ['00:00:00', '00:00:00'] }"
                    format="YYYY-MM-DD HH:mm"
                    @change="onRangeChange"
                    @select="onSelect"
                    @ok="onOk"
                />
              </div>
              <div v-if="filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && filterData.calcuSymbolName == '相对当前日期'" class="relative-date">
                <span v-if="filterData.dateType == 'scope'" class="word">在</span>
                <a-select v-model:model-value="filterData.dateType" :trigger-props="{ updateAtScroll: true }" style="width:60px" @change="dateTypeChange">
                  <template #arrow-icon>
                  </template>
                  <a-option value="scope">区间</a-option>
                  <a-option value="past">过去</a-option>
                  <a-option value="cd">当天</a-option>
                  <a-option value="cw">当周</a-option>
                  <a-option value="cm">当月</a-option>
                </a-select>
                <div v-if="filterData.dateType == 'past'" class="next-input">
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber1" :precision="0" :min="0"/>
                  </div>
                  <span class="word">天前</span>
                </div>
                <div v-if="filterData.dateType == 'scope'" class="next-input">
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber1" :precision="0" @blur="number1Change"/>
                  </div>
                  <span class="word">到</span>
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber2" :precision="0" @blur="number2Change"/>
                  </div>
                  <a-select v-model:model-value="filterData.dateTypeUnit" :trigger-props="{ updateAtScroll: true }" style="width:60px" @change="dateUnitChange">
                    <template #arrow-icon>
                    </template>
                    <a-option value="d">天</a-option>
                    <a-option value="h">小时</a-option>
                    <a-option value="m">分钟</a-option>
                  </a-select>
                </div>

              </div>
            </div>
            <div class="desc-foot">
              <div class="desc-tip">
                <icon-bulb class="bulb"/>
                <span>{{ filterData.calcuSymbolName }}：{{ getCalTips(filterData.calcuSymbolName) }}</span>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <div v-if="props.disabled && props.showDetailFilter" class="filter-btn-disabled" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
        <span class="filter-label" style="white-space:wrap;text-align: left;">
          {{ filterData.calcuSymbolName }}
          <!-- <span v-if="filterData.selectData && filterData.objectType === 'string'">{{ filterData.selectData }}</span> -->
          <span v-if="showSelectData && !filterData.selectArr.length">{{ nameString }}</span>
          <span v-if="showSelectArr && (filterData.objectType === 'string' || filterData.type === 4)">{{ nameString }}</span>
          <span v-if="showNumber">{{ filterData.number1 }}至{{ filterData.number2 }}之间</span>
          <span v-if="showDateArr">{{ filterData.dateArr[0] }}至{{ filterData.dateArr[1] }}之间</span>
          <span v-if="showDateValue">{{ filterData.dateValue }}</span>
          <span v-if="(filterData.objectType == 'date' || filterData.objectType == 'datetime') && filterData.calcuSymbolName == '相对当前日期'">
            <span v-if="filterData.dateType == 'scope'">
              在{{ filterData.dateNumber1 }}到{{ filterData.dateNumber2 }}
              <span v-if="filterData.dateTypeUnit == 'd'">天</span>
              <span v-if="filterData.dateTypeUnit == 'h'">小时</span>
              <span v-if="filterData.dateTypeUnit == 'm'">分钟</span>
              之间
            </span>
            <span v-if="filterData.dateType == 'past'">
              在过去{{ filterData.dateNumber1 }}天前
            </span>
            <span v-if="filterData.dateType == 'cd'">在当天</span>
            <span v-if="filterData.dateType == 'cw'">在当周</span>
            <span v-if="filterData.dateType == 'cm'">在当月</span>
          </span>
        </span>
      </div>
      <!-- 批量添加 -->
      <a-modal v-model:visible="addVisible" :closable="false" @ok="handleAddOk" @cancel="handleAddCancel">
        <div class="batch-option-edit">
          <div class="boe-title">
            <span>批量添加</span>
            <span class="boe-subtitle">使用回车换行分割</span>
          </div>
          <div class="boe-main">
            <a-textarea v-model="batchText" :auto-size="{ minRows: 8, maxRows: 15 }" placeholder="请输入内容，每行一个"/>
          </div>
        </div>
        <template #footer>
          <div class="boe-foot">
            <a-button type="primary" style="float: left;" @click="handleCopyAll">全选复制</a-button>
            <a-button style="margin-right: 8px;" @click.stop="handleAddCancel">取消</a-button>
            <a-button type="primary" @click.stop="handleAddOk">确定</a-button>
          </div>
        </template>
      </a-modal>
    </div>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, inject, reactive, ref, watch} from "vue";
import {analyseStore} from '@/store';
import {getAttributeEnum, getEventAttributeBatch} from "@/api/analyse/api";
import dayjs from 'dayjs';
import {cloneDeep} from "lodash";
import {useEventBus} from '@vueuse/core';
import {LocalStorageEventBus} from "@/types/event-bus";
import {Message} from '@arco-design/web-vue';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";

const localStorageEventBus = useEventBus(LocalStorageEventBus);
const analyseData = analyseStore()
const showTextArea = ref(false)

interface screenConfig {
  name: string,
  type?: string,
  note?: string,
  objectDisplayName?: string,
  filterType?: string, // tab分类
  objectType?: string,
  code: string
  collectType: boolean,
}

const props = defineProps({
  // 禁用选择
  disabled: {
    type: Boolean,
    default: false
  },
  // 不可选数据
  disabledList: {
    type: Array,
    default: () => []
  },
  // 只有事件列表
  onlyEvent: {
    type: Boolean,
    default: false
  },
  // 只有用户属性列表
  onlyUser: {
    type: Boolean,
    default: false
  },
  // 不包括事件列表
  excludeEvent: {
    type: Boolean,
    default: false
  },
  // 是否提供选择范围
  showDetailFilter: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object,
    default() {
      return {};
    },
  },
  // 事件数组，用以筛选eventName请求事件对应属性
  codeList: {
    type: Array,
    default: () => []
  },
  // 事件属性数组
  eventLists: {
    type: Array,
    default: () => []
  },
})
const emits = defineEmits(['tabsChange']);

// 事件属性
const showPane2 = computed(() => {
  if (props.excludeEvent) {
    return false
  }
  if (props.onlyEvent) {
    return true
  }
  return !props.onlyUser
})
// 用户属性
const showPane3 = computed(() => {
  if (props.onlyEvent) {
    return false
  }
  if (props.onlyUser) {
    return true
  }
  return true
})
// 用户分群
const showPane4 = computed(() => {
  if (props.onlyEvent) {
    return false
  }
  return !props.onlyUser
})
// 用户标签
const showPane5 = computed(() => {
  if (props.onlyEvent) {
    return false
  }
  return !props.onlyUser
})
// 模拟数据
const EventLists = ref<screenConfig[]>([])
const UserLists = ref<screenConfig[]>([])
const UserGroup = ref<screenConfig[]>([])
const UserTag = ref<screenConfig[]>([])
const getCalTips = (name: string) => {
  const labels: { [key: string]: string } = {
    '等于': '属性值等于任一设定值',
    '不等于': '属性值不等于任一设定值，且不为空',
    '包括': '属性值包括与设定值完全一致的部分',
    '不包括': '属性值没有与设定值完全一致的部分',
    '有值': '属性值不为空',
    '无值': '属性值为空',
    '正则匹配': '属性值满足正则匹配规则',
    '正则不匹配': '属性值不满足正则匹配规则',
    '小于': '属性值小于设定值',
    '小于等于': '属性值小于或者等于设定值',
    '大于': '属性值大于设定值',
    '大于等于': '属性值大于或者等于设定值',
    '区间': '属性值在设定的数值区间范围内(左闭右闭)',
    '为真': '属性值为 True',
    '为假': '属性值为 False',
    '位于区间': '属性值在设置的时间区间范围内(左闭右闭)',
    '相对当前日期': '将属性值与“今天”进行比较',
    '相对事件发生时刻': '将属性值与事件时间进行比较',
    '属于分群': '用户在选择的分群内',
    '不属于分群': '用户不在选择的分群内',
  };
  return labels[name] || '';
}

// 方法获取图标
const getIcon = (objectType: any) => {
  const icons: { [key: string]: string } = {
    string: '/icon/text.svg',
    int: '/icon/number.svg',
    float: '/icon/number.svg',
    boolean: '/icon/boolean.svg',
    date: '/icon/time.svg',
    array: '/icon/list.svg',
    datetime: '/icon/time.svg',
    variant: '/icon/list.svg'
  };
  return icons[objectType] || ''; // 默认返回空字符串
};

// 方法获取标签
const getLabel = (objectType: any) => {
  const labels: { [key: string]: string } = {
    string: '文本',
    int: '整数',
    float: '小数',
    boolean: '布尔',
    date: '日期',
    array: '列表',
    datetime: '日期时间',
    variant: '对象组'
  };
  return labels[objectType] || '';
};
const ChooseLists = ref<any>([])
const collectLists = ref<any>([])
const allList = ref<any>([])
const searchInput = ref('')

interface FilterData {
  objectName: string;
  objectDisplayName: string;
  calcuSymbolName: string;
  calcuSymbol: string;
  thresholds?: (string | number)[];
  objectType: string | undefined;
  objectId: string;
  filterType: string;
  type: string | number; // 判断维度表等属性
  note: string;
  rangeSymbol: string;
  rangeName: string;
  number1: number;
  number2: number;
  dateType: string;
  dateNumber1: number;
  dateNumber2: number;
  dateTypeUnit: string;
  dateValue: string;
  dateArr: any[];
  selectData: string;
  selectArr: any[];
  inputData: string;
  itemIndex: number;
  userNumber: string,
  entityName: string,
  timeZone: string,
  lastUpdateTime: string,
  creatorName: string,
  enumList:any[]
}

const filterData: FilterData = reactive({
  objectName: '',
  objectDisplayName: '',
  calcuSymbolName: '等于',
  calcuSymbol: 'eq',
  thresholds: [],
  objectType: '',
  objectId: '',
  filterType: '',
  type: '',
  note: '',
  rangeSymbol: 'eq',
  rangeName: '等于',
  number1: 0,
  number2: 1,
  dateType: 'scope',
  dateNumber1: -1,
  dateNumber2: 1,
  dateTypeUnit: 'd',
  dateValue: '',
  dateArr: [],
  selectData: '',
  selectArr: [],
  inputData: '',
  itemIndex: 1,
  userNumber: '',
  entityName: '',
  timeZone: '',
  lastUpdateTime: '',
  creatorName: '',
  enumList:[]
})
// 方法获取属性
const getAttrName = (value: any) => {
  const clusterTypeLabel: { [key: number]: string } = {
    1: '条件分群',
    2: 'ID分群',
    3: 'SQL分群',
    4: '结果分群',
  }
  const tagLabel: { [key: number]: string } = {
    1: '条件标签',
    2: 'ID标签',
    3: 'SQL标签',
    4: '首末次标签',
    5: '指标标签',
  }
  const labels: { [key: number]: string } = {
    1: '预置属性',
    2: '自定属性',
    3: '虚拟属性',
    4: '维度表属性',
  };
  if (value.filterType && value.filterType === 'cluster') {
    return clusterTypeLabel[value.type] || '';
  }
  if (value.filterType && value.filterType === 'tag') {
    return tagLabel[value.type] || '';
  }
  return labels[value.type] || '';
};
// 文本选项
const textRangeList = ref([
  {value: 'eq', label: '等于'},
  {value: 'neq', label: '不等于'},
  {value: 'con', label: '包括'},
  {value: 'ncon', label: '不包括'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'},
  {value: 'rm', label: '正则匹配'},
  {value: 'nrm', label: '正则不匹配',}
])
// 数值选项
const valueRangeList = ref([
  {value: 'eq', label: '等于'},
  {value: 'neq', label: '不等于'},
  {value: 'lt', label: '小于'},
  {value: 'lteq', label: '小于等于'},
  {value: 'gt', label: '大于'},
  {value: 'gteq', label: '大于等于'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'},
  {value: 'scope', label: '区间'}
])

// 列表选项
const itemRangeList = ref([
  {value: 'con', label: '存在元素'},
  {value: 'ncon', label: '不存在元素'},
  // {value:'scope',label:'元素位置'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 时间选项
const dateRangeList = ref([
  {value: 'scope', label: '位于区间'},
  {value: 'lteq', label: '小于等于'},
  {value: 'gteq', label: '大于等于'},
  {value: 'rct', label: '相对当前日期'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 布尔选项
const booleanRangeList = ref([
  // {value:'true',label:'为真'},
  // {value:'false',label:'为假'},
  {value: 'eq', label: '等于'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 分群
const groupingList = ref([
  {value: 'con', label: '属于分群'},
  {value: 'ncon', label: '不属于分群'},
])
const getRangeType = (data?: string) => {
  // data是传入calcuSymbo值，用以筛选calcuSymbolName显示值
  const type = filterData.objectType || EventLists.value[0]?.objectType
  const rangeLists = {
    'string': textRangeList.value,
    'int': valueRangeList.value,
    'float': valueRangeList.value,
    'array': itemRangeList.value,
    'date': dateRangeList.value,
    'datetime': dateRangeList.value,
    'boolean': booleanRangeList.value,
    'variant': itemRangeList.value,
  };
  const matchedItem = rangeLists[type]?.find(item => item.value === data) || rangeLists[type]?.[0];
  return matchedItem || groupingList.value[0];
};
// 获取用户list
const getUserList = async () => {
  const data = analyseData.userLists.length > 0 ? analyseData.userLists : await analyseData.fetchUserInfo()
  if (data && data.length) {
    UserLists.value = data.map((item: any) => {
      return {
        ...item,
        filterType: 'user',
        objectType: item.dataType,
        objectDisplayName: item.displayName,
        collectType: false
      }
    })
  }
}

// 获取用户分群cluster
const getClusterList = async () => {
  const data = analyseData.userGroups.length > 0 ? analyseData.userGroups : await analyseData.fetchGroupInfo()
  if (data && data.length) {
    UserGroup.value = data.map((item: any) => {
      return {
        ...item,
        filterType: 'cluster',
        // objectType: item.dataType,
        objectDisplayName: item.displayName,
        collectType: false
      }
    })
  }
}

// 获取用户标签
const getTagList = async () => {
  const data = analyseData.userTags.length > 0 ? analyseData.userTags : await analyseData.fetchTagInfo()
  if (data && data.length) {
    UserTag.value = data.map((item: any) => {
      return {
        ...item,
        filterType: 'tag',
        objectType: item.dataType,
        collectType: false
      }
    })
  }
}

watch(
  () => [props.onlyEvent, props.onlyUser],
  ([isOnlyEvent, isOnlyUser]) => {
    // 如果是仅事件属性，不需要加载任何列表
    if (isOnlyEvent) {
      return;
    }
    // 如果是仅用户属性，加载用户列表
    if (isOnlyUser) {
      getUserList();
    }
    // 如果不是仅单分类模式，需要加载分类列表
    if (!isOnlyEvent && !isOnlyUser) {
      getUserList();
      getClusterList();
      getTagList();
    }
  },
  { immediate: true }
);
// 获取事件属性list;
// 如果传入name-list 则请求接口获取，否则从props.eventLists种获取
const separateNumberAndUnit = (str: string) => {
  const number = parseInt(str);
  const unit = str.replace(/^-?\d+/, '');
  return {number, unit};
};
const nameString = ref('')
const selectLoading = ref(false)
const selectEnumList = ref<any>([])

const handleNameString = () => {
  const list = [] as any
  selectEnumList.value = props.info?.enumList?.length > 0 ? cloneDeep(props.info.enumList) : selectEnumList.value
  if(filterData.thresholds?.length && selectEnumList.value.length){
    selectEnumList.value.forEach(element => {
      filterData.thresholds.forEach(item => {
        if(element.code === item){
          list.push(element.name)
        }
      })
    });
  }
  if(filterData.thresholds?.length && !selectEnumList.value.length){
    filterData.thresholds.forEach(item => {
      list.push(item)
    })
  }

  nameString.value =  list.join(',')
}

const getEventList = async () => {
  // 没有传入eventLists代表需要使用已有事件列表code动态请求事件属性 | 传入了事件属性列表则直接使用
  if (props?.eventLists?.length === 0 && props?.codeList?.length > 0) {
    const list = props.codeList.map(item => item.eventCode)
    const data = {
      event:list,
      inApp:1
    }
    await getEventAttributeBatch(data).then((res) => {
      EventLists.value = res.map((item: any) => {
        return {
          name: item.name,
          objectDisplayName: item.displayName,
          code: item.code,
          note: item.note,
          type: item.type,
          filterType: 'event',
          objectType: item?.dataType || item?.objectType,
          collectType: false
        }
      })
    })
  } else if (props.eventLists?.length) {
    EventLists.value = props.eventLists.map((item: any, index) => {
      return {
        name: item?.name,
        objectDisplayName: item?.displayName,
        code: item?.code,
        note: item?.note,
        type: item?.type,
        filterType: 'event',
        objectType: item?.dataType || item?.objectType,
        collectType: false
      }
    })
  }
  // 处理默认显示，首先使用传入props.info中属性，其次使用各分类的第一个属性，暂时只处理了事件属性分类，用户属性分类
  const v = EventLists.value[0] || UserLists.value[0]
  filterData.objectName = props.info?.objectName || props.info?.name || v.name
  filterData.objectDisplayName = props.info.objectDisplayName ||  props.info.displayName || v.objectDisplayName
  filterData.filterType = props.info?.filterType || props.info?.aggregateType || v.filterType
  filterData.objectType = filterData.filterType !== 'cluster' ? props.info?.objectType || v?.objectType : ''
  filterData.objectId = props.info?.code || props.info?.objectId || v.code
  filterData.calcuSymbolName = props.info.calcuSymbol ? getRangeType(props.info.calcuSymbol).label : getRangeType().label
  filterData.calcuSymbol = props.info.calcuSymbol ? props.info.calcuSymbol : getRangeType().value
  filterData.type = props.info?.type || v?.type
  filterData.note = props.info?.note || v?.note
  filterData.thresholds = props.info?.thresholds || []
  filterData.enumList = props.info?.enumList || []
  if (filterData.thresholds && filterData.thresholds.length > 0) {
    // filterData.selectArr = props.info?.thresholds || []
    const arr = cloneDeep(props.info?.thresholds || [])
    if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && ['等于', '不等于','包括', '不包括'].includes(filterData.calcuSymbolName)) {
      filterData.selectData = arr[0] || ''
      handleNameString()
    }
    // if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && ['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)) {
    //   filterData.selectArr = arr
    //   handleNameString()
    // }
    if (filterData.objectType && ['string'].includes(filterData.objectType) && ['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)) {
      filterData.selectArr = arr
      handleNameString()
    }
    if (filterData.objectType && ['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)) {
      filterData.inputData = arr[0] || ''
    }
    if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && filterData.calcuSymbolName === '区间') {
      filterData.number1 = arr[0] || 0
      filterData.number2 = arr[1] || 1
    }
    if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && filterData.calcuSymbolName === '位于区间') {
      filterData.dateArr = arr
    }
    if ((filterData.objectType === 'date' || filterData.objectType === 'datetime') && filterData.calcuSymbolName === '相对当前日期') {
      if (arr.length === 2) {
        const result = arr.map(separateNumberAndUnit);
        filterData.dateNumber1 = result[0].number || -1
        filterData.dateNumber2 = result[1].number || 1
        filterData.dateTypeUnit = result[0].unit
        filterData.dateType = 'scope'
      }
      if (arr.length === 1) {
        const result = arr.map(separateNumberAndUnit);
        if (result[0].number) {
          filterData.dateNumber1 = result[0]?.number
          filterData.dateType = 'past'
        } else {
          filterData.dateType = result[0]?.unit
        }
      }
    }
    if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && (filterData.calcuSymbolName === '小于等于' || filterData.calcuSymbolName === '大于等于')) {
      filterData.dateValue = arr[0] || ''
    }
  }
  if (filterData.filterType === 'cluster') {
    if (UserGroup.value.length < 1) await getClusterList()
    UserGroup.value.forEach((item: any) => {
      if (item.code === filterData.objectId) {
        filterData.userNumber = item?.userNumber
        filterData.entityName = item?.entityName
        filterData.timeZone = item?.timeZone
        filterData.lastUpdateTime = item?.lastUpdateTime
        filterData.creatorName = item?.creatorName
        filterData.note = item?.note
      }
    })
  }
  if (filterData.filterType === 'tag') {
    if (UserTag.value.length < 1) await getTagList()
    UserTag.value.forEach((item: any) => {
      if (item.code === filterData.objectId) {
        filterData.userNumber = item?.userNumber
        filterData.entityName = item?.entityName
        filterData.timeZone = item?.timeZone
        filterData.lastUpdateTime = item?.lastUpdateTime
        filterData.creatorName = item?.creatorName
        filterData.note = item?.note
      }
    })
  }

  emits('tabsChange', filterData)
}
getEventList()


// concat全部
watch([EventLists, UserLists, UserGroup, UserTag], ([newEventLists, newUserLists, newUserGroup, newUserTag]) => {
  if (props.onlyEvent) {
    allList.value = [
      {typeName: '事件属性'},
      ...newEventLists,
    ];
  } else if (props.excludeEvent) {
    allList.value = [
      {typeName: '用户属性'},
      ...newUserLists,
      {typeName: '用户分群'},
      ...newUserGroup,
      {typeName: '用户标签'},
      ...newUserTag,
    ];
  } else if (props.onlyUser) {
    allList.value = [
      {typeName: '用户属性'},
      ...newUserLists,
    ];
  } else {
    allList.value = [
      {typeName: '事件属性'},
      ...newEventLists,
      {typeName: '用户属性'},
      ...newUserLists,
      {typeName: '用户分群'},
      ...newUserGroup,
      {typeName: '用户标签'},
      ...newUserTag,
    ];
  }

}, {immediate: true});

const flattenData = (array) => {
  const data = array.reduce((acc, item) => {
    const key = item.filterType; // 使用 filterType 作为分组的键
    if (!acc[key]) {
      acc[key] = []; // 如果没有该键，初始化为一个空数组
    }
    acc[key].push(item); // 将当前项添加到对应的分组中
    return acc;
  }, {});
  const flattenedArray = [];
  // 处理 event 数组
  if (data.event) {
    flattenedArray.push({typeName: '事件属性'}); // 添加事件属性类型
    flattenedArray.push(...data.event); // 添加事件数据
  }
  // 处理 user 数组
  if (data.user) {
    flattenedArray.push({typeName: '用户属性'}); // 添加用户属性类型
    flattenedArray.push(...data.user);
  }
  // 处理 user 数组
  if (data.cluster) {
    flattenedArray.push({typeName: '用户分群'}); // 添加用户分群类型
    flattenedArray.push(...data.cluster);
  }
  // 处理 user 数组
  if (data.tag) {
    flattenedArray.push({typeName: '用户标签'}); // 添加用户标签类型
    flattenedArray.push(...data.tag);
  }

  return flattenedArray;
};
// 搜索全部
const filteredAllList = computed(() => {
  const str = searchInput.value.trim();
  if (str) {
    const list = allList.value.filter(item => item?.name?.includes(str) || item?.objectDisplayName?.includes(str));
    return flattenData(list)
  }

  return allList.value
});
// 搜索事件属性
const filteredEventLists = computed(() => {
  const str = searchInput.value.trim();
  return EventLists.value.filter(item => item?.name.includes(str) || item?.objectDisplayName?.includes(str));
});
// 搜索用户属性
const filteredUserLists = computed(() => {
  const str = searchInput.value.trim();
  return UserLists.value.filter(item => item?.name.includes(str) || item?.objectDisplayName?.includes(str));
});
// 搜索用户分群
const filteredUserGroup = computed(() => {
  const str = searchInput.value.trim();
  return UserGroup.value.filter(item => item?.name.includes(str) || item?.objectDisplayName?.includes(str));
});
// 搜索用户标签
const filteredUserTag = computed(() => {
  const str = searchInput.value.trim();
  return UserTag.value.filter(item => item?.name.includes(str) || item?.objectDisplayName?.includes(str));
});


const triggerVisible = ref<boolean>(false)
const subVisible = ref<boolean>(false)

watch(() => props.eventLists, (newVal) => {
  if (props.eventLists.length > 0) {
    EventLists.value = props.eventLists.map((item: any, index) => {
      return {
        name: item.name,
        objectDisplayName: item.objectDisplayName,
        code: item.code,
        note: item.note,
        type: item.type,
        filterType: 'event',
        objectType: item.dataType || item.objectType,
        collectType: false
      }
    })
  }
}, {deep: true});


const handleTriggerVisible = () => {
  // ChooseLists.value = props.disabledList.map(item => item.code)
  triggerVisible.value = !triggerVisible.value;
}
watch(() => props.disabledList, () => {
  if (props.disabledList.length > 0) {
    ChooseLists.value = props.disabledList.map(item => item.objectId)
  }
}, {deep: true, immediate: true})
const handleSubVisible = () => {
  subVisible.value = !subVisible.value;
}


// 点击
const handleEvent = (v: any) => {

  filterData.objectName = v.name
  filterData.objectDisplayName = v.objectDisplayName
  filterData.filterType = v.filterType
  filterData.objectType = v.filterType !== 'cluster' ? v?.objectType || '' : ''
  filterData.objectId = v.code
  filterData.calcuSymbolName = getRangeType().label
  filterData.calcuSymbol = getRangeType().value
  filterData.type = v?.type || ''
  filterData.note = v.note
  filterData.userNumber = v?.userNumber
  filterData.entityName = v?.entityName
  filterData.timeZone = v?.timeZone
  filterData.lastUpdateTime = v?.lastUpdateTime
  filterData.creatorName = v?.creatorName
  triggerVisible.value = false
  filterData.selectArr = []
  filterData.selectData = ''
  filterData.thresholds = []
  filterData.enumList = []
  subVisible.value = true

  emits('tabsChange', filterData)
}
// 收藏
const collectEvent = (e) => {
  collectLists.value.push(e)
  e.collectType = true
}
// 取消收藏
const uncollectEvent = (e) => {
  e.collectType = false
  const a = collectLists.value.indexOf(e)
  collectLists.value.splice(a, 1)
}
const rangeTypeChange = (name, v) => {
  const list = {
    'textRangeList': textRangeList.value,
    'valueRangeList': valueRangeList.value,
    'itemRangeList': itemRangeList.value,
    'dateRangeList': dateRangeList.value,
    'booleanRangeList': booleanRangeList.value
  };
  const arr = list[name]
  filterData.calcuSymbol = v

  arr.forEach(item => {
    if (item.value === v) {
      filterData.calcuSymbolName = item.label
    }
  })
  filterData.dateType = 'scope'
  filterData.inputData = ''
  filterData.selectData = ''
  filterData.selectArr = []
  filterData.dateValue = ''
  filterData.dateArr = []
  if (filterData.calcuSymbolName === '区间') {
    filterData.thresholds = [filterData.number1, filterData.number2]
  } else if (filterData.calcuSymbolName === '相对当前日期') {
    filterData.thresholds = ['-1d', '1d']
  } else {
    filterData.thresholds = []
  }
  emits('tabsChange', filterData)
}

// 多选筛选下拉列表
const selectArrChange = (v) => {
  filterData.thresholds = []
  if(v){
    filterData.thresholds = v
  }
  handleNameString()
  emits('tabsChange', filterData)
}

// 日期
const boardDate = inject('boardDate');
// 事件列表
// const eventParamsList = inject('eventParamsList');

const selectArrPopup = async (v) => {
  if (filterData?.enumList?.length > 0) {
    selectEnumList.value = cloneDeep(filterData.enumList)
  }
  // 使用props.nameList中的code请求枚举
  const codeList = [...new Set(props.codeList.map(item => item?.eventCode || item?.indicatorCode))]
  if (!filterData.enumList.length && v) {
    selectLoading.value = true
    const data = {
      type: filterData.type,
      attributeType: filterData.objectId.split('_')[0],
      event: codeList,
      attribute: filterData.objectId,
    }
    // 只有当 boardDate 存在且有相应的值时才添加日期字段
    if (boardDate && boardDate?.value) {
      data.startDate = getDateRangeStartDate(boardDate.value)
      data.endDate = getDateRangeEndDate(boardDate.value)
    }
    await getAttributeEnum(data).then(res => {
      if (res) {
        selectEnumList.value = res
        filterData.enumList = cloneDeep(selectEnumList.value)
      }
      selectLoading.value = false
    }).catch(error => {
      selectLoading.value = false;
    });
  }
}
// 单选筛选下拉列表
const selectDataChange = (v) => {
  filterData.thresholds = []
  if(v){
    filterData.thresholds.push(v)
  }
  handleNameString()
  emits('tabsChange', filterData)
}
const inputDataChange = () => {
  filterData.thresholds = []
  if(filterData.inputData){
    filterData.thresholds = [filterData.inputData]
  }
  emits('tabsChange', filterData)
}
const value1Change = () => {
  const num1 = filterData.number1
  const num2 = filterData.number2
  if (num1 >= 0 && num1 > num2) {
    filterData.number2 = num1
  }
  filterData.thresholds = [filterData.number1, filterData.number2]
  emits('tabsChange', filterData)
}

const value2Change = () => {
  const num1 = filterData.number1
  const num2 = filterData.number2
  if (num2 < num1) {
    filterData.number1 = num2
  }
  filterData.thresholds = [filterData.number1, filterData.number2]
  emits('tabsChange', filterData)
}
const number1Change = () => {
  const num1 = filterData.dateNumber1
  const num2 = filterData.dateNumber2
  if (num1 >= 0 && num1 > num2) {
    filterData.dateNumber2 = num1
  }
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('tabsChange', filterData)
}
const number2Change = () => {
  const num1 = filterData.dateNumber1
  const num2 = filterData.dateNumber2
  if (num2 < num1) {
    filterData.dateNumber1 = num2
  }
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('tabsChange', filterData)
}
const booleanChange = (v) => {
  filterData.thresholds = [v]
  emits('tabsChange', filterData)
}
const dateTypeChange = (v) => {
  if (v === 'past') {
    filterData.dateNumber1 = 0
    filterData.dateTypeUnit = 'd'
    const value = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
    filterData.thresholds = [value]
    emits('tabsChange', filterData)
  }
  if (v === 'cd' || v === 'cw' || v === 'cm') {
    filterData.thresholds = [v]
    emits('tabsChange', filterData)
  }
}
const dateUnitChange = (v) => {
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('tabsChange', filterData)
}
const onChange = (v) => {
  filterData.thresholds = [v]
  emits('tabsChange', filterData)
}
const onRangeChange = (v) => {
  filterData.thresholds = v
  emits('tabsChange', filterData)
}
const onSelect = () => {

}
const onOk = () => {

}
// 批量添加
const addVisible = ref(false)
const batchText = ref('')
const copyText = ref('')
// 打开批量添加弹窗
const handleAddClick = () => {
  addVisible.value = true
  // 如果已有选中值，将其转换为文本
  if (filterData.selectArr?.length) {
    batchText.value = filterData.selectArr.join('\n')
    copyText.value = filterData.selectArr.join('\n')
  }
}

// 确认批量添加
const handleAddOk = () => {
  if (!batchText.value) {
    addVisible.value = false
    return
  }
  // 将文本按换行符分割并过滤空值
  const values = batchText.value.split('\n').filter(item => item.trim())
  // 更新选中值
  filterData.selectArr = values
  filterData.thresholds = values
  // 处理显示文本
  const list: string[] = []
  if (selectEnumList.value?.length) {
    selectEnumList.value.forEach(element => {
      values.forEach(item => {
        if (element.code === item) {
          list.push(element.name)
        }
      })
    })
  } else {
    list.push(...values)
  }
  nameString.value = list.join(',')
  addVisible.value = false
  emits('tabsChange', filterData)
}

// 取消批量添加
const handleAddCancel = () => {
  batchText.value = ''
  addVisible.value = false
}

// 全选复制
const handleCopyAll = () => {
  if (!batchText.value) return
  navigator.clipboard.writeText(batchText.value)
    .then(() => {
      Message.success('复制成功')
    })
    .catch(() => {
      Message.error('复制失败')
    })
}

// 判断数据类型是否在指定范围内
const isValidDataType = (type: any, validTypes: string[]) => {
  return type && validTypes.includes(type);
};

// 判断计算符号是否在指定范围内
const isValidSymbol = (symbol: string, validSymbols: string[]) => {
  return validSymbols.includes(symbol);
};

const showSelectArr = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array'];
  const validSymbols = ['等于', '不等于', '存在元素', '不存在元素'];

  return isValidDataType(filterData.objectType, validTypes) &&
         isValidSymbol(filterData.calcuSymbolName, validSymbols) &&
         filterData.selectArr.length > 0;
});

const showSelectData = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array'];
  const validSymbols = ['等于', '不等于', '存在元素', '不存在元素', '包括', '不包括'];

  return isValidDataType(filterData.objectType, validTypes) &&
         isValidSymbol(filterData.calcuSymbolName, validSymbols) &&
         filterData.selectData;
});

const showInputData = computed(() => {
  const validSymbols = ['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'];
  return isValidSymbol(filterData.calcuSymbolName, validSymbols);
});

const showNumber = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array'];
  return isValidDataType(filterData.objectType, validTypes) &&
         filterData.calcuSymbolName === '区间';
});

const showDateArr = computed(() => {
  const validTypes = ['date', 'datetime'];
  return isValidDataType(filterData.objectType, validTypes) &&
         filterData.calcuSymbolName === '位于区间' &&
         filterData.dateArr[0] &&
         filterData.dateArr[1];
});

const showDateValue = computed(() => {
  const validTypes = ['date', 'datetime'];
  const validSymbols = ['小于等于', '大于等于'];

  return isValidDataType(filterData.objectType, validTypes) &&
         isValidSymbol(filterData.calcuSymbolName, validSymbols) &&
         filterData.dateValue;
});

watch(() => props.info, (newVal) => {
  const defaultObj = props.onlyUser ? UserLists.value[0] : EventLists.value[0]
  if (newVal) {
    filterData.objectName = props.info?.objectName || defaultObj?.name;
    filterData.objectDisplayName = props.info?.objectDisplayName || defaultObj?.objectDisplayName;
    filterData.filterType = props.info?.filterType || props.info?.aggregateType || defaultObj?.filterType
    filterData.objectType = filterData.filterType !== 'cluster' ? props.info?.objectType || defaultObj?.objectType : ''
    filterData.objectId = props.info?.objectId || defaultObj?.code
    filterData.calcuSymbolName = props.info.calcuSymbol ? getRangeType(props.info.calcuSymbol).label : getRangeType().label
    filterData.calcuSymbol = props.info.calcuSymbol ? props.info.calcuSymbol : getRangeType().value
    filterData.type = props.info?.type || defaultObj?.type
    filterData.note = props.info?.note || defaultObj?.note
    filterData.thresholds = props.info?.thresholds || []
    filterData.enumList = props.info?.enumList || []
    if (filterData.thresholds && filterData.thresholds.length > 0) {
      // filterData.selectArr = props.info?.thresholds || []
      const arr = cloneDeep(props.info?.thresholds || [])
      if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && ['等于', '不等于','包括', '不包括'].includes(filterData.calcuSymbolName)) {
        filterData.selectData = arr[0] || ''
        handleNameString()
      }
      // if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && ['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)) {
      //   filterData.selectArr = arr
      //   handleNameString()
      // }
      if (filterData.objectType && ['string'].includes(filterData.objectType) && ['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)) {
        filterData.selectArr = arr
        handleNameString()
      }
      if (['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)) {
        filterData.inputData = arr[0] || ''
      }
      if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && filterData.calcuSymbolName === '区间') {
        filterData.number1 = arr[0] || 0
        filterData.number2 = arr[1] || 1
      }
      if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && filterData.calcuSymbolName === '位于区间') {
        filterData.dateArr = arr
      }
      if ((filterData.objectType === 'date' || filterData.objectType === 'datetime') && filterData.calcuSymbolName === '相对当前日期') {
        if (arr.length === 2) {
          const result = arr.map(separateNumberAndUnit);
          filterData.dateNumber1 = result[0].number || -1
          filterData.dateNumber2 = result[1].number || 1
          filterData.dateTypeUnit = result[0].unit
          filterData.dateType = 'scope'
        }
        if (arr.length === 1) {
          const result = arr.map(separateNumberAndUnit);
          if (result[0].number) {
            filterData.dateNumber1 = result[0]?.number
            filterData.dateType = 'past'
          } else {
            filterData.dateType = result[0]?.unit
          }
        }
      }
      if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && (filterData.calcuSymbolName === '小于等于' || filterData.calcuSymbolName === '大于等于')) {
        filterData.dateValue = arr[0] || ''
      }
    }

    if (filterData.filterType === 'cluster' && UserGroup.value.length > 0) {
      UserGroup.value.forEach((item: any) => {
        if (item.code === filterData.objectId) {
          filterData.userNumber = item?.userNumber
          filterData.entityName = item?.entityName
          filterData.timeZone = item?.timeZone
          filterData.lastUpdateTime = item?.lastUpdateTime
          filterData.creatorName = item?.creatorName
          filterData.note = item?.note
        }
      })
    }
    if (filterData.filterType === 'tag' && UserTag.value.length > 0) {
      UserTag.value.forEach((item: any) => {
        if (item.code === filterData.objectId) {
          filterData.userNumber = item?.userNumber
          filterData.entityName = item?.entityName
          filterData.timeZone = item?.timeZone
          filterData.lastUpdateTime = item?.lastUpdateTime
          filterData.creatorName = item?.creatorName
          filterData.note = item?.note
        }
      })
    }
  }
  // emits('tabsChange',filterData)
}, {deep: true});

localStorageEventBus.on((name, value) => {
  if (name === "data-source") {
    // selectEnumList.value = []
    filterData.enumList = []
  }
})
</script>

<style scoped lang="less">
:deep(.arco-select-view-tag){
  white-space: nowrap!important;;
}
.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.filter-btn, .filter-btn-disabled {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;
  margin: 2px 0;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  .un-filter-label {
    color: var(--tant-text-gray-color-text1-4);
  }

  .virtual-sign {
    color: var(--tant-primary-color-primary-default);
  }
}

.filter-btn {
  cursor: pointer;

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

:deep(.arco-dropdown-option) {
  border-radius: 4px;
}

.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);

  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;

      .header-icon {
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }

      }

      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }

  .card-detail {
    padding: 6px 12px 4px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    box-shadow: inset 0 1px 0 var(--tant-border-color-border1-1);

    .detail-item {
      padding-bottom: 4px;
    }
  }

  .prop-bubble-card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    :deep(.arco-textarea-wrapper) {
      height: 100px;
      padding: 5px 8px;
      font-size: 14px;
      line-height: 22px;
      background-color: transparent;
      border-radius: var(--tant-border-radius-medium);
      resize: none;
      color: var(--tant-text-gray-color-text1-2);
      border: 1px solid var(--tant-border-color-border1-1);
    }

    :deep(.arco-textarea) {
      border: none;
      padding: none;
    }
  }

  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;

    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color .3s;
      }

      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}

.builder-content {
  display: flex;
  flex-direction: column;
  width: 400px;
  min-width: 400px;
  max-height: 420px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: var(--tant-small-shadow-small-overall);

  .builder-panel-container {
    flex-grow: 1;
    padding: 24px 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    line-height: 40px;

    .quot-name {
      margin-right: 6px;
      font-weight: 500;
      line-height: 20px;
      vertical-align: text-top;
    }

    .operator-select {
      display: inline-block;
      margin-right: 10px;
    }

    .item-index-number {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 100px;
      margin-right: 8px;
    }

    .absolute-date {
      display: inline-block;

      :deep(.arco-picker) {
        border-radius: 4px;
      }
    }

    .relative-date {
      display: inline-flex;
      align-items: center;
      vertical-align: top;
    }

    .word {
      margin: 0 6px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 14px;
    }

    .next-input {
      display: inline-flex;
      align-items: center;
      vertical-align: top;
      margin-left: 8px;
    }

    .next-input-number {
      width: 60px;
      margin-right: 8px;
    }
  }

  .equal-search {
    display: inline-block;
    max-width: 360px;
    min-width: 80px;
    position: relative;

    .option-edit {
      position: absolute;
      top: 0px;
      right: 4px;
      z-index: 9;
      // padding: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      opacity: 0;
      cursor: pointer;

      &:hover {
        color: var(--tant-primary-color-primary-hover);
      }
    }

    &:hover .option-edit {
      opacity: 1;
    }
  }

  .desc-foot {
    display: flex;
    align-items: center;
    padding: 12px 14px;
    font-size: 13px;
    line-height: 20px;
    background: var(--tant-bg-gray-color-bg2-1);
    border-top: 1px solid var(--tant-border-color-border1-1);

    .desc-tip {
      display: flex;
      align-items: center;
      color: var(--tant-text-gray-color-text1-3);

      .bulb {
        margin-right: 10px;
        color: var(--tant-decorative-yellow-color-decorative6-1);
        font-size: 18px;
        vertical-align: top;
      }
    }
  }
}

:deep(.arco-modal-footer) {
  border: none !important;
}

.batch-option-edit {
  min-height: 200px;
  overflow: hidden;

  .boe-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--tant-text-gray-color-text1-1);
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;

    .boe-subtitle {
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
      font-size: 12px;
    }
  }

  .boe-main {
    width: 100%;
    margin-top: 8px;

    :deep(.arco-textarea-wrapper) {
      height: 164px;
      padding: 5px 8px;
      font-size: 14px;
      line-height: 22px;
      background-color: transparent;
      border-radius: var(--tant-border-radius-medium);
      resize: none;
      color: var(--tant-text-gray-color-text1-2);
      border: 1px solid var(--tant-border-color-border1-1);
    }

    :deep(.arco-textarea) {
      border: none;
      padding: none;
    }
  }

}

.boe-foot {
  width: 100%;
  text-align: right;

  :deep(.arco-btn) {
    height: 24px;
    padding: 1px 8px;
    font: var(--tant-body-font-body-regular);
    border-radius: var(--tant-border-radius-medium);
    border: none;
  }
}

.screen-bt {
  color: var(--tant-text-gray-color-text1-2);
  text-shadow: none;
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
  box-shadow: none;
  font-size: 14px;
  padding: 5px 16px;
}

.screen-bt:hover {
  cursor: pointer;
  background-color: var(--tant-bg-gray-color-bg2-1);
  transition: .3s;
}


.screen-content {
  width: 360px;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  .screen-content-body {
    margin: 0 8px;

    :deep(.arco-tabs-content) {
      padding-top: 0;
    }

    :deep(.arco-list-item) {
      padding: 0;
      border: none;
    }

    :deep(.arco-list-bordered) {
      border: none;
    }

    :deep(.arco-list-content-wrapper .arco-list-content.arco-list-virtual .arco-list-item) {
      padding: 0;
      border: none;
    }

    :deep(.arco-list-header) {
      padding: 0;
    }

    .screen-tabHeight {
      height: 350px;
      overflow: auto;
      position: relative;

      .affix {
        width: 100%;
        position: sticky;
        top: 0;
        z-index: 99;
      }
    }

    .affix-eventName {
      display: flex;
      align-self: center;
      background-color: var(--tant-bg-white-color-bg1-1);
      color: var(--tant-text-black-color-text1-1);
      padding: 12px;
      width: 100%;
      font-size: 14px;
    }

    .screen-option {
      display: flex;

      .screen-option-group {
        display: inline-block;
        padding-left: 60px;
        width: 130px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 10px;

      }

      .screen-option-name {
        display: inline-block;
        width: 150px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .screen-option-attr {
        display: inline-block;
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
      }

      .screen-option-type {
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;

        .type-text {
          display: flex;
          align-items: center
        }

        .typeIcon {
          width: 12px;
          height: 12px;
          margin-right: 4px;
        }
      }

      .screen-option-collect {
        display: flex;
        align-items: center;
        width: 30px;
        padding: 0;

        .uncollect-button {
          display: none;

        }

        .collect-button {
          display: flex;
          align-self: center;
          border: none;
          margin-right: 5px;
          color: var(--tant-text-gray-color-text1-3);
        }
      }
    }

    .screen-option:hover {
      .uncollect-button {
        display: flex;
        align-self: center;
        border: none;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-3);

      }
    }
  }
}

.affix-eventName {
  display: flex;
  align-self: center;
  background-color: var(--tant-bg-white-color-bg1-1);
  color: var(--tant-text-black-color-text1-1);
  padding: 12px;
  width: 100%;
}
</style>