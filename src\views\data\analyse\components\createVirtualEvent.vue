<script setup lang="ts">
import {onMounted, reactive, ref, watch} from "vue";
import {getEventAttributeBatch} from "@/api/analyse/api";
import {useEventBus} from "@vueuse/core";
import panelCheckBox from "@/views/analyse/components/panelCheckBox.vue"
import {DateManageEventBus, SaveEvent} from "@/types/event-bus";
import router from "@/router";
import {Message} from "@arco-design/web-vue";
import {saveEventList} from "@/api/setting/api";
import EventFilter from "@/views/data/analyse/components/eventFilter.vue";
import useSettingStore from "@/store/modules/setting";
import {ROUTE_NAME} from "@/router/constants";

const settingStore=useSettingStore()
const form = reactive({
  name: '',
  displayName: '',
  note: '',
})
const rules = {
  name: [
    {
      required: true,
      maxLength: 60,
      message: '事件名不能为空，且不能超过60个字符',
    },
  ],
  displayName: [
    {
      required: true,
      maxLength: 60,
      message: '显示名不能为空，且不能超过60个字符',
    },
  ]
}
const tooltipText = [
  '虚拟事件名称要以字母开头，可含字母、数字、下划线，最多60个字符',
  '不能超过60个字符'
]
const eventBus = useEventBus('eventList');
const attrBus = useEventBus('attrList');
const saveEventBus = useEventBus(DateManageEventBus)
const formRef = ref(null)
const submitStatus = ref()
const formValue = ref()
const addModalVisible = ref(false)
const globalFilterMetaEventVueRef = ref(null)
const panelCheckBoxRef = ref(null)
const eventFilterRef=ref(null)
const EditStatus = ref(false)
watch(EditStatus,(newVal)=>{
  settingStore.EditStatus=newVal
})
watch(form,()=>{
  if(EditStatus.value!==true)
  EditStatus.value=true
})
// 多选添加事件
const addMoreEvent = () => {
  addModalVisible.value = true
}
// 使用转换函数
const addMultiAnalysisIndex = () => {

  if(EditStatus.value!==true){
    EditStatus.value=true
  }
  if (panelCheckBoxRef.value?.oldCheckData) {
    const filterData = panelCheckBoxRef.value?.oldCheckData.map(item => {
      return {
        name: '',
        type: 'event',
        displayType: {},
        displayName: '',
        eventList: [
          {
            eventName: item.eventName,
            eventDisplayName: item.eventDisplayName,
            type: 'event',
            eventAttrCode: '',
            eventAttrName: '总次数',
            filter: {}
          }
        ],
        filtersList: [],
        description: '',
        note: ''
      }
    })
    eventFilterRef.value?.eventListData.push(...filterData)
    panelCheckBoxRef.value.checkDataNumber=0
    panelCheckBoxRef.value.checkData=[]
  }

}
const cancel=()=>{
  if( panelCheckBoxRef.value){
    panelCheckBoxRef.value.checkDataNumber=0
    panelCheckBoxRef.value.checkData=[]
  }
}
const addEvent=()=>{
  if(EditStatus.value!==true){
    EditStatus.value=true
  }
  if(eventFilterRef.value){
    eventFilterRef.value.addEvent()
  }
}
const addFilter = () => {
  if(EditStatus.value!==true){
    EditStatus.value=true
  }
  if (globalFilterMetaEventVueRef.value) {
    globalFilterMetaEventVueRef.value.add()
  }
}


const save = () => {
  if (formRef.value) {
    const letterStartRegex = /^[A-Za-z]/;
    formRef.value.validate((errors: any) => {
      if (!errors && letterStartRegex.test(form.name)) {
        submitStatus.value = 'success'; // 提交成功，更新状态
        formValue.value = form
        const params =null
            saveEventList(params).then(res => {
              if (res && res.code) {
                router.push({name:ROUTE_NAME.SETTING_ANALYSE_EVENT})
                Message.success('创建成功！')
              } else {
                Message.error('创建失败')
              }
            })
      }
      if (!errors && !letterStartRegex.test(form.name)) {
        Message.error(
            {
              content: '虚拟事件名称要以字母开头，可含字母、数字、下划线，最多60个字符',
              resetOnHover: true
            })
        submitStatus.value = 'error'; // 更新状态
      } else {
        submitStatus.value = 'error'; // 提交失败，更新状态
      }
    });
  }

}

saveEventBus.on((event, route) => {
  if (event === SaveEvent && route === 'data.analyse.event.create.virtual') {
    save()
  }
})
onMounted(() => {

  // 获取筛选分组事件属性公共部分
  // if(analysisIndexRef.value){
  //   analysisIndexRef.value.applyDataIndex=false
  //   analysisIndexRef.value.applyCreateEvent=true
  // }
  // if(globalFilterMetaEventVueRef.value){
  //   globalFilterMetaEventVueRef.value.applyCreateEvent=true
  // }
  eventBus.on((event, payload) => {
    if (event === 'virtualEvent') {
      const list = payload.map((item: any) => item.code);
      if (list.length > 0) {
        const data = {
          event:list,
          inApp:1
        }
        getEventAttributeBatch(data).then((res) => {
          attrBus.emit(res);  // 发射结果
        });
      }
    }
  });
})
</script>

<template>
  <div style="display: flex;flex-direction: row; height: 85vh;">
    <div class="common-frame-content-left">
      <div>
        <a-anchor :change-hash="false" smooth>
          <a-anchor-link href="#Rule">规则定义</a-anchor-link>
          <a-anchor-link href="#Basic">基本信息</a-anchor-link>
        </a-anchor>
      </div>
    </div>
    <div class="common-frame-content-right">
      <div class="spinContainer">
        <a-form ref="formRef" :rules="rules" :model="form" class="form" >
          <div style="width: 100%">
            <div id="Rule" class="common-block">
              <div class="common-block-header">
                <div class="common-block-header-title">规则定义</div>
              </div>
              <div id="virtual-event-basic" class="basicSection">
                <p class="tip">以下任一事件被触发，即该虚拟事件被触发</p>
                <div class="editor">
                  <div id="virtual-event">
                    <p class="editItem">事件</p>
                    <!--                    <div>-->
                    <!--                      <PanelSelect :panel-data="attrList"/>-->
                    <!--                      -->
                    <!--                    </div>-->
                    <EventFilter ref="eventFilterRef" />
                    <a-dropdown>
                      <div class="ta-filter-button">
                      <span role="img" class="anticon">
                        <icon-plus/>
                      </span>
                        <span class="ta-filter-button-label">添加事件</span>
                      </div>
                      <template #content>
                        <a-doption @click="addEvent">单个事件</a-doption>
                        <a-doption @click="addMoreEvent">多个事件</a-doption>
                      </template>
                    </a-dropdown>
                    <a-modal
                        v-model:visible="addModalVisible"
                        title="添加多个事件"
                        title-align="start"
                        width="320px"
                        :align-center="false"
                        :mask-closable="false"
                        :top="200"
                        :esc-to-close="false"
                        @ok="addMultiAnalysisIndex"
                        @cancel="cancel">
                      <div class="multi-modal">
                        <div style="margin-right: auto;">
                          批量添加需要的事件，最多选择20个
                        </div>
                        <div style="margin-right: auto">
                          <!--                          <panel-multi-select ref="panelMultiSelectRef" @update-select-object="handleUpdateSelectObject"/>-->
                          <panelCheckBox ref="panelCheckBoxRef" :click-all="false"/>
                        </div>
                      </div>
                    </a-modal>
                  </div>
                  <div style="margin-top: 16px;">
                    <p class="editItem">全局筛选</p>
<!--                    <globalFilter ref="globalFilterMetaEventVueRef" :apply-create-event="true"/>-->
                    <div class="ta-filter-button" @click="addFilter">
                      <span role="img" class="anticon">
                        <icon-plus/>
                      </span>
                      <span class="ta-filter-button-label">添加筛选条件</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div id="Basic" class="common-block">
              <div class="common-block-header">
                <div class="common-block-header-title">基本信息</div>
              </div>
              <div style="padding: 0 32px">
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-tooltip :content="tooltipText[0]" trigger="click">
                      <a-form-item label="事件名" field="name" label-col-flex="70px" :hide-asterisk="true" validate-trigger="blur">
                        <a-button class="name-button"><span>ta@</span></a-button>
                        <a-input
                            v-model="form.name"
                            class="form-input"
                            style="border-radius: 0 var(--tant-border-radius-medium)  var(--tant-border-radius-medium) 0;"
                            placeholder="请输入"
                        />
                      </a-form-item>
                    </a-tooltip>
                  </a-col>
                </a-row>
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-tooltip trigger="click" :content="tooltipText[1]">
                      <a-form-item label="显示名" field="displayName" label-col-flex="70px" :hide-asterisk="true" validate-trigger="blur">
                        <a-input
                            v-model="form.displayName"
                            class="form-input"
                            placeholder="请输入"
                        />
                      </a-form-item>
                    </a-tooltip>

                  </a-col>
                </a-row>
                <a-row :gutter="20">
                  <a-col :span="13">
                    <a-form-item label="备注" field="note" label-col-flex="70px">
                      <a-textarea
                          v-model:model-value="form.note"
                          placeholder="请输入"
                          class="form-input"
                          :max-length="200"
                          allow-clear show-word-limit/>
                      <template #label>
                        <span>备注</span><span style="color: var(--tant-text-gray-color-text1-4);">(选填)</span>
                      </template>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>

          </div>
        </a-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

.common-frame-content-left {
  min-width: 200px;
  padding: 24px;
  overflow: auto;
  border-right: 1px solid var(--tant-border-color-border1-1);
}

.common-frame-content-right {
  flex: 1 1;
  width: 0;
  padding: 24px 0 48px;
  border-radius: 4px;

  .spinContainer {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;

    .form {
      font-size: 14px;
      font-variant: tabular-nums;
      line-height: 1.5715;
      list-style: none;
      font-feature-settings: "tnum", "tnum";

      .common-block {
        margin-bottom: 32px;

        .common-block-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 24px;
          padding: 0 32px;

          .common-block-header-title {
            flex: 1 1;
            width: 0;
            color: var(--tant-text-gray-color-text1-1);
            font: var(--tant-header-font-header4-medium);
          }
        }

        .basicSection {
          padding: 0 32px;

          .tip {
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-regular);
          }

          .editor {
            box-sizing: border-box;
            min-width: 800px;
            margin-top: 8px;
            padding: 20px;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 8px;

            .editItem {
              margin-bottom: 12px;
              color: var(--tant-text-gray-color-text1-1);
              font: var(--tant-body-font-body-medium);
            }

            .ta-filter-button {
              display: inline-flex;
              align-items: center;
              padding: 6px;
              font-size: 14px;
              cursor: pointer;
              transition: all .3s;

              .anticon {

                margin-right: 8px;
                padding: 0 3px;
                color: var(--tant-primary-color-primary-default);
                background-color: var(--tant-primary-color-primary-fill);
                border-radius: 4px;
              }

              .ta-filter-button-label {
                color: var(--tant-primary-color-primary-default);
              }
            }

            .ta-filter-button:hover .anticon {
              color: var(--tant-primary-color-primary-hover);
              background-color: var(--tant-primary-color-primary-fill-hover);
            }

          }
        }

      }
    }

  }
}

.name-button {
  height: 32px;
  padding: 0 8px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  line-height: 30px;
  background-color: transparent;
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium) 0 0 var(--tant-border-radius-medium);
  border-right: none;
  cursor: text;
}

.name-button:hover {
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-primary-color-primary-hover);
}

.form-input:focus-within {
  box-shadow: var(--tant-input-shadow-active-overall);
  border-color: var(--tant-primary-color-primary-active);
}

.multi-modal {
  display: flex;
  gap: 10px;
  flex-direction: column;
  align-items: center;
  justify-items: start;
  padding: 10px;
}

</style>