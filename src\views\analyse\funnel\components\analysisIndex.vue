<template>
  <div class="guide">
    <div class="stickyBar">
      <div class="modal" style="width: calc(100% - 42px);">
        <img src="/icon/analysis/performAnalysis.svg" alt="">
        <span class="title">对</span>
        <analysisSubjectSelect :style="{width:'120px',margin:'0 8px'}" @subject-change="subjectChange"/>
        <span class="title">进行分析</span>
        <div class="model-btn">
          <a-button @click="addAnalysisIndex">
            <template #icon>
              <icon-plus class="nav-icon"/>
            </template>
          </a-button>
        </div>
      </div>
      <a-tooltip content="重置" position="top">
        <a-button style="width: 36px;height: 48px;" class="btn-bg" @click="resetParams">
          <template #icon>
            <icon-loop style="font-size: 16px;"/>
          </template>
        </a-button>
      </a-tooltip>
    </div>
    <div class="event-filter-box">
      <draggable :list="eventListData" handle=".hover-drag">
        <template #item="{ element,index }">
          <div class="action-row">
            <div class="filter-row-eventRow">
              <div class="action-left">
                <i class="drag-index">{{ index + 1 }}</i>
                <icon-drag-dot-vertical class="hover-drag"/>
                <div class="row-content">
                  <div v-if="element.rename" class="rename">
                    <a-input v-model="element.eventDisplayName" placeholder="请输入" @blur="reNameBlur(index)"/>
                  </div>
                  <div style="margin-top: 4px;">
                    <EventIndicatorSelect :ref="el => evtIndRefs[index] = el" :panel-data="element" @analysis-index-change="panelSelectChange(index,$event)"/>
                  </div>
                </div>
              </div>
              <div class="action-right">
                <a-space align="center">
                  <a-tooltip content="添加筛选" position="top">
                    <a-button class="btn-bg btn-26" @click="add(index)">
                      <template #icon>
                        <icon-filter/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="复制" position="top">
                    <a-button class="btn-bg btn-26" @click="copyItem(index)">
                      <template #icon>
                        <icon-copy/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="重命名" position="top">
                    <a-button class="btn-bg btn-26" @click="() => element.rename = true">
                      <template #icon>
                        <icon-pen/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-dropdown trigger="hover" @select="handleSelect(index,$event)">
                    <a-button class="btn-bg btn-26">
                      <template #icon>
                        <icon-more-vertical/>
                      </template>
                    </a-button>
                    <template #content>
                      <a-doption value="create">
                        <template #icon>
                          <icon-plus/>
                        </template>
                        <template #default>插入漏斗步骤</template>
                      </a-doption>
                      <a-doption v-if="eventListData.length>2" value="delete">
                        <template #icon>
                          <icon-close-circle/>
                        </template>
                        <template #default>删除</template>
                      </a-doption>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </div>
            <eventQueryFilter
                :ref="el => queryFilterRefs[index] = el"
                :only-event="true"
                :filter="element.filter"
                :show-detail-filter="true"
                :code-list="[element]"
                @query-filters-change="queryFiltersChange(index,$event)"/>
            <div v-if="element.filter&&element.filter.filters&&element.filter.filters.length" class="row-foot">
              <div class="ta-filter-button" @click="add(index)">
                <icon-plus class="action"/>
                <span class="label">筛选条件</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>

      <div class="subTitle" style="margin-top: 16px;">使用关联属性</div>
      <div style="padding: 6px 4px 0 24px;margin-bottom: 16px;">
        <a-switch v-model:model-value="relevance" @change="relevanceChange"/>
      </div>
      <div v-if="relevance" style="color: var(--tant-text-gray-color-text1-3);line-height: 38px;">
        <div class="action-row" style="padding: 8px 24px;">
          <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
            <attrEnumSelect
                :only-event="true"
                :info="associateInfo"
                :code-list="eventListData"
                @tabs-change="subChange"/>
          </div>
          为关联属性
          <a-tooltip content="漏斗各步骤的关联属性值必须保持一致，且不为空" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {nextTick, onMounted, reactive, ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import {toolStore} from '@/store';
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import analysisSubjectSelect from "@/views/analyse/components/analysisSubjectSelect.vue"
import {cloneDeep} from "lodash";
import draggable from 'vuedraggable'
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";
import {getDefaultObj} from "@/views/analyse/components/util/verify";


const eventBus = useEventBus('eventList');
const subject = ref('')
const props = defineProps({
  analysisIndexData: {
    type: Array,
    default: () => []
  },
  timeSpan: {
    type: Object
  },
  associateAttribute: {
    type: Object,
    default: () => {
    }
  },
  eventLists: {
    type: Array,
    default: () => []
  },
})
const toolData = toolStore();
onMounted(() => {
// attrBus.on((event: any) => {
//   dataLists.value = event.map((item) => {
//     return {
//       ...item,
//       filterType:'event',
//       objectType:item.dataType,
//     }
//   })
// });
})

const emits = defineEmits(['indicatorsChange', 'subjectChange', 'associateChange', 'resetParams'])
const subjectChange = (v) => {
  subject.value = v.subject
  emits('subjectChange', v)
}

const eventListData = ref<any>([])
const evtLists = ref<any>([])

// 监听传入事件列表
watch(() => props.eventLists, () => {
  evtLists.value = cloneDeep(props.eventLists)
}, {immediate: true, deep: true})
watch(() => props.analysisIndexData, () => {
  eventListData.value = cloneDeep(props.analysisIndexData)
}, {immediate: true, deep: true})
const relevance = ref(false)

const associateInfo = reactive({
  code: '',
  name: '',
  displayName: ''
})
watch(() => props.associateAttribute, () => {
  relevance.value = Boolean(props.associateAttribute?.code)
  associateInfo.code = props.associateAttribute.code
  associateInfo.name = props.associateAttribute.name
  associateInfo.displayName = props.associateAttribute.displayName
}, {immediate: true, deep: true})
const reNameBlur = (index: number) => {
  if (eventListData.value[index].eventDisplayName === '') {
    // 通过eventCode从原始事件列表中查找对应的原始显示名称
    const originalEvent = props.eventLists.find(item => item.code === eventListData.value[index].eventCode)
    eventListData.value[index].eventDisplayName = originalEvent?.displayName || eventListData.value[index].eventName || ''
    eventListData.value[index].rename = false
  }
}


const panelSelectChange = (index: number, e: any) => {
  const {eventName, eventCode, eventDisplayName, type, eventType} = e
  const filter = {...eventListData.value[index]};
  filter.eventName = eventName;
  filter.eventDisplayName = eventDisplayName;
  filter.type = type;
  filter.eventCode = eventCode;
  filter.eventType = eventType;
  eventListData.value[index] = filter;
}

const queryFiltersChange = (index: number, v) => {
  eventListData.value[index].filter = v;
}

// 关联属性change
const subChange = (v) => {

  const {objectId, objectName, displayName} = v
  associateInfo.code = objectId
  associateInfo.name = objectName
  associateInfo.displayName = displayName
  emits('associateChange', associateInfo)
}

const relevanceChange = async (v) => {
  if (!v) {
    emits('associateChange', {})
  } else {
    const flatList = handleAttrFlatData(toolData.allAttrList, eventListData.value);
    const attrList = flatList.filter(category => category.categoryName === '事件属性').flatMap(category => category.itemData || []);
    const defalutData = attrList[0]
    associateInfo.code = associateInfo.code || defalutData.code
    associateInfo.name = associateInfo.name || defalutData.name
    associateInfo.displayName = associateInfo.displayName || defalutData.displayName
    emits('associateChange', associateInfo)
  }
}
const evtIndRefs = ref<any>([])
// 添加事件指标
const addAnalysisIndex = () => {
  const appOpenData = getDefaultObj(evtLists.value)
  eventListData.value.push({
    eventName: appOpenData.name,
    eventDisplayName: appOpenData.displayName,
    eventType: appOpenData.type,
    eventCode: appOpenData.code,
    eventAttrCode: '',
    eventAttrName: appOpenData.objectType === 'event' ? '总次数' : '',
    type: appOpenData.objectType,
    filter: {}
  })
  nextTick(() => {
    if (evtIndRefs.value[eventListData.value.length - 1]) {
      evtIndRefs.value[eventListData.value.length - 1].handleTriggerVisible();
    }
  });
  // eventBus.emit(eventListData.value);
}
const handleSelect = (index: number, v) => {
  if (v === 'delete') {
    eventListData.value.splice(index, 1)
  } else if (v === 'create') {
    addAnalysisIndex()
  }
  eventBus.emit(eventListData.value);
};
const queryFilterRefs = ref<any>([])
// 添加并列条件
const add = (index1: number) => {
  queryFilterRefs.value[index1].add()
}
// 复制
const copyItem = (index: number) => {
  const newItem = cloneDeep(eventListData.value[index]);
  eventListData.value.splice(index + 1, 0, newItem);
}

watch(eventListData, (newValue, oldValue) => {
  if (newValue) {
    emits('indicatorsChange', eventListData.value)
  }
}, {immediate: true, deep: true})

const resetParams = () => {
  emits('resetParams')
}
</script>

<style scoped lang="less">
.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        font-weight: 600;
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .subTitle {
      padding-left: 24px;
      color: var(--tant-text-gray-color-text1-3);
      font: var(--tant-description-font-description-medium);
      line-height: 20px;
    }

    .action-row {
      position: relative;
      height: auto;
      padding-right: 24px;
      padding-left: 24px;
      width: 100%;
      min-height: 24px;
      line-height: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 9px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 9px;
          margin-left: 31px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
          cursor: grab;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            // font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }

            :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
              font-weight: 600 !important;
            }

            :deep(.arco-input-wrapper) {
              border: none;
              background-color: transparent;
              font-weight: 600;

              &:hover {
                border: none;
                background-color: transparent;
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        :deep(.filter-btn) {
          background: #fff;
        }

        :deep(.filter-icon) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }
      }
    }

    .row-foot {
      margin: 0;
      margin-top: -8px;
      padding-left: 28px;
      transition: all .3s;

      .ta-filter-button {
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-hover);
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

</style>