import {AxiosPromise} from "axios";
import {SpaceDto, SpaceSaveDto} from "@/api/space/type";
import {getRequest, postRequest} from "@/api/request";
import _ from "lodash";


/**
 * 看板列表接口
 * @param containDetail 是否包含详情
 */
// eslint-disable-next-line import/prefer-default-export
export function listSpace(containDetail: boolean): AxiosPromise<Array<SpaceDto>> {
  return getRequest<SpaceDto[]>('/api/dashboard/space/list', {'contain_detail': containDetail});
}

// 节流后的 saveSpace 函数
export const saveSpace = _.throttle(
    (space: SpaceSaveDto): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/space/save', {
        ...space,
        spaceName: space.name,
      });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

// 节流后的 moveSpace 函数
export const moveSpace = _.throttle(
    (space: SpaceSaveDto): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/space/move', {
        ...space,
        spaceName: space.name,
      });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

// 节流后的 removeSpace 函数
export const removeSpace = _.throttle(
    (spaceId: string): AxiosPromise<any> => {
      return getRequest<any>('/api/dashboard/space/remove', { spaceId });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

