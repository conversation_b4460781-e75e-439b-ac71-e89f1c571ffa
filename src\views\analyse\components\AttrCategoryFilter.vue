<template>
  <!-- 属性分类筛选 -->
  <div class="guide">
    <div v-if="filtersList.length" class="event-filter-box">
      <div class="action-row">
        <div class="row-filters">
          <div class="relation-editor">
            <div v-if="filtersList.length" class="relation-relation">
              <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
              <div v-if="filtersList.length>1" :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="logicalChange">
                <span v-if="isAnd">且</span>
                <span v-else>或</span>
              </div>
            </div>
            <div class="relation-main">
              <div v-for="(item,index) in filtersList" :key="item.objectId" class="relation-row">
                <div class="multi-filter-condition">
                  <div v-if="item.subFilters && item.subFilters.length>1" class="relation-editor">
                    <div class="relation-relation">
                      <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
                      <div :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="() => item.isAnd = !item.isAnd">
                        <span v-if="item.isAnd">且</span>
                        <span v-else>或</span>
                      </div>
                    </div>
                    <div class="relation-main">
                      <div class="relation-row">
                        <div class="multi-filter-condition">
                          <div v-for="(val,num) in item.subFilters" :key="num" class="sub-action-row">
                            <div class="sub-action-left">
                              <span v-if="props.onlyUser" style="line-height: 26px;padding-right: 12px;">用户属性</span>
                              <span v-if="props.onlyGroup" style="line-height: 26px;padding-right: 12px;">用户分群</span>
                              <attrEnumSelect
                                  :disabled="props.disabled"
                                  :only-event="props.onlyEvent"
                                  :only-user="props.onlyUser"
                                  :only-group="props.onlyGroup"
                                  :exclude-event="props.excludeEvent"
                                  :code-list="props.codeList"
                                  :show-detail-filter="props.showDetailFilter"
                                  :info="val"
                                  @tabs-change="subChange(index,num,$event)"/>
                            </div>
                            <div class="sub-action-right">
                              <a-space v-if="!props.disabled" align="center">
                                <a-tooltip content="添加并列条件" position="top">
                                  <a-button
                                      class="btn-bg btn-26" style="margin-left: 8px;"
                                      @click="addItemFilters(index)">
                                    <template #icon>
                                      <icon-filter/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                                <a-tooltip content="删除" position="top">
                                  <a-button class="btn-bg-delete btn-26" @click="removeMultipleListItem(index,num)">
                                    <template #icon>
                                      <icon-close-circle/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                              </a-space>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="sub-action-row">
                    <div class="sub-action-left">
                      <span v-if="props.onlyUser" style="line-height: 26px;padding-right: 12px;">用户属性</span>
                      <span v-if="props.onlyGroup" style="line-height: 26px;padding-right: 12px;">用户分群</span>
                      <attrEnumSelect
                          :disabled="props.disabled"
                          :only-event="props.onlyEvent"
                          :only-user="props.onlyUser"
                          :only-group="props.onlyGroup"
                          :exclude-event="props.excludeEvent"
                          :code-list="props.codeList"
                          :show-detail-filter="props.showDetailFilter"
                          :info="item"
                          @tabs-change="filChange(index,$event)"/>
                    </div>
                    <div class="sub-action-right">
                      <a-space v-if="!props.disabled" align="center">
                        <a-tooltip content="添加并列条件" position="top">
                          <a-button class="btn-bg btn-26" style="margin-left: 8px;" @click="addFilters(item,index)">
                            <template #icon>
                              <icon-filter/>
                            </template>
                          </a-button>
                        </a-tooltip>
                        <a-tooltip content="删除" position="top">
                          <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index)">
                            <template #icon>
                              <icon-close-circle/>
                            </template>
                          </a-button>
                        </a-tooltip>
                      </a-space>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import {FilterExpression} from "@/api/analyse/type";
import {toolStore} from '@/store';
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";


const toolData = toolStore();
const emits = defineEmits(['queryFiltersChange'])
const isAnd = ref(true)
const props = defineProps({
  // 筛选项回显
  filter: {
    type: Object,
    default: () => {
    }
  },
  // 是否排除事件属性
  excludeEvent: {
    type: Boolean,
    default: false
  },
  // 用于属性下拉枚举传值，事件code
  codeList: {
    type: Array,
    default: () => []
  },
  eventLists: {
    type: Array,
    default: () => []
  },
  showDetailFilter: {
    type: Boolean,
    default: false
  },
  // 禁用/只读
  disabled: {
    type: Boolean,
    default: false
  },
  // 只有事件属性列表
  onlyEvent: {
    type: Boolean,
    default: false
  },
  // 只有用户属性列表
  onlyUser: {
    type: Boolean,
    default: false
  },
  // 只有分群属性列表
  onlyGroup: {
    type: Boolean,
    default: false
  },
})
const filtersList = ref<any>([])

// isAnd.value = props.filter?.logicalOperation ? props.filter?.logicalOperation === 'and' : true
// if(props.filter?.filters && props.filter.filters.length > 0) {
//   filtersList.value = props.filter.filters.map(item => {
//       return {
//           objectName: item.objectName,
//           objectDisplayName: item.objectDisplayName,
//           type:item.type,
//           isAnd: item.logicalOperation ? item.logicalOperation === 'and' : true,
//           objectType: item.objectType,
//           objectId: item.objectId,
//           filterType: item.filterType,
//           calcuSymbol: item.calcuSymbol,
//           thresholds: item.thresholds,
//           enumList:item.enumList,
//           subFilters: item.subFilters || []
//       }
//   })

// }

const eventQueryfilter = reactive(
    {
      logicalOperation: 'and',
      filters: [] as FilterExpression[],
    }
)
const logicalChange = () => {
  if (!props.disabled) {
    isAnd.value = !isAnd.value
    eventQueryfilter.logicalOperation = isAnd.value ? 'and' : 'or'
  }

}
//
const dataList = ref<any>([])
// 赋值默认计算公式
const defaultCalcuSymbol = (objectType?: string) => {
  const rangeLists = {
    'string': 'eq',
    'int': 'eq',
    'float': 'eq',
    'array': 'con',
    'date': 'scope',
    'datetime': 'scope',
    'boolean': 'eq',
    'variant': 'con',
  };
  return rangeLists[objectType || 'array'] || ''
}
const add = async () => {
  // let dataList = []
  if (toolData.allAttrList.length === 0) {
    await toolData.fetchAllAttrList()
  }
  const flatList = handleAttrFlatData(toolData.allAttrList, props.codeList);
  let attrList = []
  if (props.onlyUser) {
    attrList = flatList.filter(category => category.categoryName === '用户属性')
  } else if (props.onlyGroup) {
    attrList = flatList.filter(category => category.categoryName === '用户分群')
  } else if (props.onlyEvent) {
    attrList = flatList.filter(category => category.categoryName === '事件属性')
  } else if (props.excludeEvent) {
    attrList = flatList.filter(category => category.categoryName !== '事件属性')
  } else {
    attrList = flatList
  }
  dataList.value = attrList.filter(category => category.itemData?.length > 0).flatMap(category => category.itemData || []);
  const defaultItem = dataList.value[0] || {};
  filtersList.value.push({
    objectName: defaultItem.name || '',
    objectDisplayName: defaultItem.displayName || '',
    isAnd: true,
    objectType: defaultItem.dataType || '',
    objectId: defaultItem.code || '',
    filterType: defaultItem.attributeType || 'event',
    type: defaultItem.type,
    calcuSymbol: defaultCalcuSymbol(defaultItem.dataType),
    thresholds: [],
    subFilters: []
  } as never)
}
const deleteAll = () => {
  filtersList.value = []
}
// 添加并列条件
const addFilters = (item: any, index: number) => {
  if (!filtersList.value[index].subFilters.length) {
    filtersList.value[index].subFilters.push({
      objectName: item.objectName,
      objectDisplayName: item.objectDisplayName,
      objectType: item.objectType,
      objectId: item.objectId,
      filterType: item.filterType,
      type: item.type,
      calcuSymbol: item.calcuSymbol,
      thresholds: item.thresholds,
      enumList: item.enumList
    })
  }
  filtersList.value[index].subFilters.push({
    objectName: dataList.value[0]?.name || '',
    objectDisplayName: dataList.value[0]?.displayName || '',
    objectType: dataList.value[0]?.dataType || '',
    objectId: dataList.value[0]?.code || '',
    filterType: dataList.value[0]?.attributeType || 'event',
    type: dataList.value[0]?.type,
    calcuSymbol: '',
    thresholds: [],
  })
}
const addItemFilters = (index: number) => {
  filtersList.value[index].subFilters.push({
    objectName: dataList.value[0]?.name || '',
    objectDisplayName: dataList.value[0]?.displayName || '',
    objectType: dataList.value[0]?.dataType || '',
    objectId: dataList.value[0]?.code || '',
    filterType: dataList.value[0]?.attributeType || 'event',
    type: dataList.value[0]?.type,
    calcuSymbol: '',
    thresholds: [],
  } as never)
}
const deleteFilters = (index: number) => {
  filtersList.value.splice(index, 1)
}
const removeMultipleListItem = (index: number, num: number) => {
  filtersList.value[index].subFilters.splice(num, 1)
  if (filtersList.value[index].subFilters.length === 1) {
    // 删的剩最后两个的时候，外层变为剩下的一个
    // const data = cloneDeep(filtersList.value[index].subFilters[0])
    const filter = {...filtersList.value[index].subFilters[0]};
    filter.isAnd = true;
    filter.subFilters = [];
    filtersList.value[index] = filter
  }
}
// tabselect组件传参改变值
const subChange = (index: number, num: number, e) => {
  const {objectName, type, objectDisplayName, objectType, calcuSymbol, thresholds, filterType, objectId, enumList} = e
  const filter = {...filtersList.value[index].subFilters[num]};
  filter.objectName = objectName;
  filter.objectDisplayName = objectDisplayName;
  filter.type = type;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  filter.enumList = enumList
  filtersList.value[index].subFilters[num] = filter;
}
const filChange = (index: number, e) => {
  const {objectName, type, objectDisplayName, objectType, calcuSymbol, thresholds, filterType, objectId, enumList} = e
  const filter = {...filtersList.value[index]};
  filter.objectName = objectName;
  filter.objectDisplayName = objectDisplayName;
  filter.type = type;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  filter.enumList = enumList
  filtersList.value[index] = filter;
}

const listChangeFromProps = ref<boolean>(false);

watch(() => props.filter, () => {
  if (!props.filter) return;

  isAnd.value = props.filter?.logicalOperation ? props.filter?.logicalOperation === 'and' : true;
  listChangeFromProps.value = true;
  filtersList.value = props.filter?.filters?.length > 0
      ? props.filter.filters.map(item => ({
        objectName: item.objectName,
        objectDisplayName: item.objectDisplayName,
        type: item.type,
        isAnd: item.logicalOperation ? item.logicalOperation === 'and' : true,
        objectType: item.objectType,
        objectId: item.objectId,
        filterType: item.filterType,
        calcuSymbol: item.calcuSymbol,
        thresholds: item.thresholds,
        enumList: item.enumList,
        subFilters: item.subFilters || []
      }))
      : [];
}, {immediate: true});

//
const updateEventQueryFilter = () => {
  if (!filtersList.value) return;
  if (listChangeFromProps.value){
    listChangeFromProps.value = false;
    return;
  }
  eventQueryfilter.logicalOperation = isAnd.value ? 'and' : 'or';
  eventQueryfilter.filters = filtersList.value.map(item => ({
    objectName: item.objectName,
    objectDisplayName: item.objectDisplayName,
    type: item.type,
    logicalOperation: item.isAnd ? 'and' : 'or',
    objectType: item.objectType,
    objectId: item.objectId,
    filterType: item.filterType,
    calcuSymbol: item.calcuSymbol,
    thresholds: item.thresholds,
    enumList: item.enumList,
    subFilters: item.subFilters
  }));

  emits('queryFiltersChange', eventQueryfilter);
};
watch(filtersList, updateEventQueryFilter, {immediate: true,deep: true});
watch(isAnd, updateEventQueryFilter);  // watch(isAnd, updateEventQueryFilter);
defineExpose({
  add,
  deleteAll
})
</script>

<style scoped lang="less">
.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all .3s;
  box-sizing: border-box;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.guide {
  width: 100%;
  // margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn),
        .action-left .row-content :deep(.select-btn) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn),
        .sub-action-left :deep(.select-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }
      }
    }

    .row-filters {
      // padding-left: 38px;
      .relation-editor {
        box-sizing: border-box;
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;

        .relation-relation {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          width: 24px;
          margin-right: 8px;
          flex-shrink: 0;

          .relation-relation-line, .relation-relation-disabled-line {
            position: absolute;
            left: 12px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
            top: 6px;
            height: calc(100% - 12px);
          }

          .relation-relation-value, .relation-relation-disabled-value {
            position: absolute;
            text-transform: uppercase;
            top: 50%;
            left: 50%;
            transform: translate(-50%);
            height: 24px;
            padding: 0 5px;
            margin-top: -12px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            line-height: 22px;
            text-align: center;
            background-color: #fff;
            border: 1px solid var(--tant-border-color-border1-2);
            border-radius: 4px;
            word-break: keep-all;
            transition: all .3s;
          }

          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }

          .relation-relation-value {
            cursor: pointer;
          }

          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }
        }

        .relation-main {
          flex: 1 1;

          .relation-row {
            box-sizing: border-box;

            .multi-filter-condition {
              .sub-action-row {
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;

                .sub-action-left {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  flex-wrap: wrap;
                }

                .sub-action-right {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  display: flex;
                  align-items: center;
                  opacity: 0;
                  transition: opacity .3s;
                }

                &:hover .sub-action-right {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>