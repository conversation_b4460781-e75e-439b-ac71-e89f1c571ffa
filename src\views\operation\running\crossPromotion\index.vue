<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <a-select v-model="params.category" style="width: 240px;" @change="categoryChange">
          <a-option v-for="item in categoryList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
        </a-select>
      </div>
    </div>
    <div class="page-body">
      <div class="cross-content">
        <a-spin :loading="loading" class="group-box">
          <div v-for="(item,index) in groupList" :key="index" class="list-content" @click="handleGroup(item,index)">
            <div class="content" :class="groupIndex === index ? 'active' : ''">
              <div class="list-title">{{ item.name }}</div>
              <div class="list-body">
                <div class="count">{{ item?.apps.length || 0 }}</div>
                <span title="配置" class="glyphicon" @click.stop="handleConfigure(item.name)"></span>
                <div v-if="index === 0" title="添加新组" class="btn-icon" @click.stop="addGroup">
                  <icon-plus/>
                </div>
                <a-popconfirm v-else content="确定删除？" type="error" @ok="deleteGroupItem(item)">
                  <div class="btn-icon">
                    <icon-close/>
                  </div>
                </a-popconfirm>
              </div>
            </div>
            <div v-if="index === 0 && addInputList.length>0" class="add-box" style="padding: 12px;">
              <div v-for="(value,valueIndex) in addInputList" :key="valueIndex" class="input-list">
                <a-input v-model:model-value="value.appId" @press-enter="inputEnter(value,valueIndex)"/>
                <div class="delete-icon" @click="deleteInputItem(valueIndex)">
                  <icon-close-circle/>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
        <div class="group-detail">
          <div class="remark">
            <span>备注：成员列表</span>
            <div title="添加" class="btn-icon" @click="addDetailList">
              <icon-plus/>
            </div>
          </div>
          <div v-if="addDetailInputList.length>0" class="add-box">
            <div v-for="(value,valueIndex) in addDetailInputList" :key="valueIndex" class="input-list">
              <a-input v-model:model-value="value.appId" @press-enter="detailInputEnter(value,valueIndex)"/>
              <div class="delete-icon" @click="deleteDetailInputItem(valueIndex)">
                <icon-close-circle/>
              </div>
            </div>
          </div>
          <a-list v-if="!loading">
            <a-list-item v-for="(element,num) in detailList" :key="num">
              <a-list-item-meta
                  :description="`${element.appId}_${element.name}`"
              >
                <template #avatar>
                  <a-avatar shape="square">
                    <img
                        alt="avatar"
                        :src="element.icon"
                    />
                  </a-avatar>
                </template>
              </a-list-item-meta>
              <template #actions>
                <icon-delete @click="deleteDetailItem(element,num)"/>
              </template>
            </a-list-item>
          </a-list>
        </div>
      </div>
    </div>
    <ruleConfigure ref="ruleRef"/>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {addCrossGroup, addCrossGroupMember, deleteCrossGroup, deleteCrossGroupMember, getCrossCategoryList, getCrossGroupList} from "@/api/marketing/api";
import {Message} from '@arco-design/web-vue';
import {useRoute} from 'vue-router';
import ruleConfigure from "./components/ruleConfigure.vue";


const route = useRoute();
const params = reactive(
    {
      category: '',
      name: ''
    }
)
const loading = ref(false)
const ruleRef = ref()

interface CategoryItem {
  code: string,
  name: string,
}

interface InputData {
  appId: string;
}

const categoryList = ref<CategoryItem[]>([])
const groupIndex = ref(0)
const groupList = ref<any>([])
const detailList = ref<any>([])
const addInputList = ref<InputData[]>([])
const addDetailInputList = ref<InputData[]>([])
const getGroupList = async () => {
  loading.value = true
  try {
    const groupRes = await getCrossGroupList(params.category)
    groupList.value = groupRes
    detailList.value = groupList.value[0]?.appsInfo || []
    params.name = groupList.value[0]?.name
  } catch (error) {
    console.error('初始化数据失败:', error)

  } finally {
    loading.value = false
  }
}
const init = async () => {
  try {
    // 获取分类列表
    const categoryRes = await getCrossCategoryList()
    categoryList.value = (categoryRes as unknown) as CategoryItem[]
    // 获取默认分类的 code
    const defaultCategory = categoryList.value[0]?.code
    if (!defaultCategory) {
      return
    }
    // 获取分组列表
    params.category = defaultCategory
    getGroupList()

  } catch (error) {
    console.error('初始化数据失败:', error)

  }
}
init()
const categoryChange = async (v) => {
  params.category = v
  groupIndex.value = 0
  addInputList.value = []
  getGroupList()
}


const handleGroup = (item, index) => {
  groupIndex.value = index
  params.name = item.name
  addDetailInputList.value = []
  detailList.value = item.appsInfo
}
const deleteGroupItem = async (item) => {
  await deleteCrossGroup(params.category, item.name).then(res => {
    if (res) {
      getGroupList()
      Message.success('删除成功')
    }
  })
}
// 配置
const handleConfigure = (value: string) => {
  ruleRef.value.openModal({name: value, category: params.category})
}
// 添加新组


const addGroup = () => {
  addInputList.value.push({
    appId: ''
  })
}
// 删除input
const deleteInputItem = (index: number) => {
  addInputList.value.splice(index, 1)
}
// input回车操作
const inputEnter = (value, index) => {
  if (value.appId) {
    addCrossGroup(params.category, value.appId).then(res => {
      if (res) {
        getGroupList()
        Message.success('创建成功')
      }
    })
    addInputList.value.splice(index, 1)
  }
}
// 添加详情列表

const addDetailList = () => {
  addDetailInputList.value.push({
    appId: ''
  })
}
// 删除detail-input
const deleteDetailInputItem = (value) => {
  addDetailInputList.value.splice(index, 1)

}
// detail-input回车操作
const detailInputEnter = (value, index) => {
  if (value.appId) {
    const paramsData = {
      category: params.category,
      name: params.name,
      appId: value.appId
    }
    try {
      addCrossGroupMember(paramsData).then(res => {
        if (res) {
          // groupList.value[groupIndex.value].appsInfo.push(res)
          detailList.value.push(res)
          Message.success('创建成功')
        } else {
          Message.warning('创建失败')
        }
      })
    } catch (error) {
      Message.warning('创建失败')
    }
    addDetailInputList.value.splice(index, 1)
  }
}
const deleteDetailItem = (value, index) => {
  if (value.appId) {
    const paramsData = {
      category: params.category,
      name: params.name,
      appId: value.appId
    }
    try {
      deleteCrossGroupMember(paramsData).then(res => {
        detailList.value.splice(index, 1)
        Message.success('删除成功')
      })
    } catch (error) {
      Message.error('删除失败')
    }

  }
}
</script>

<style scoped lang="less">
.page {
  .page-body {
    .cross-content {
      border: 1px solid var(--color-secondary);
      width: 100%;
      height: 100%;
      display: flex;

      .group-box {
        width: 33%;
        height: 100%;
        border-right: 1px solid var(--color-secondary);
        overflow-y: auto;

        .list-content {
          .content {
            padding: 8px 16px;
            background-color: var(--tant-fill-color-fill1-2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            cursor: pointer;

            &:hover {
              background: #fff;
            }

            .list-body {
              display: flex;
              align-items: center;

              .glyphicon {
                font-size: 24px;
                padding-left: 16px;
                padding-right: 16px;
                margin-top: 6px;
                line-height: 1;

                &::before {
                  content: "\2a";
                }
              }
            }

          }
        }
      }

      .active {
        background-color: rgb(143, 184, 243) !important;
        color: #fff !important;
      }

      .group-detail {
        padding: 0 16px;
        overflow-y: auto;
        width: 100%;

        .remark {
          border: 1px solid var(--tant-border-color-border1-1);
          border-radius: var(--tant-border-radius-medium);
          padding: 10px 15px;
          position: relative;
          margin-top: 15px;
          margin-bottom: 8px;
          width: 100%;
          background-color: var(--tant-fill-color-fill1-2);

          .btn-icon {
            position: absolute;
            right: 12px;
            top: 8px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.add-box {
  background: #fff;

  .input-list {
    margin-bottom: 12px;
    position: relative;
  }

  .delete-icon {
    position: absolute;
    right: 12px;
    top: 4px;
    cursor: pointer;
  }
}
</style>