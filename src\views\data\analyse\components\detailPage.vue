<script setup lang="ts">
import {useRoute} from "vue-router";
import {computed, ref} from "vue";
import {FieldType} from "@/api/type";
import {EventAttributesType} from "@/api/enum";
import router from "@/router";
import correlationEvent from "./correlationEvent.vue"

const gotoItem = (item: string, param: any = {}) => {
  router.push({
    name: item,
    query: {data: JSON.stringify(param)},
  });
};
const route = useRoute()
const queryData = JSON.parse(route.query.data as string);
const data = ref({
  code: queryData.code || queryData.eventCode,
  name: queryData.name || queryData.eventName,
  displayName: queryData.displayName || queryData.eventDisplayName,
  note: queryData.note || queryData.eventNote,
  status: queryData.status || queryData.eventStatus,
  type: queryData.type || queryData.eventType,
  dataType: queryData.dataType
})
const selectOptions = [
  {label: '自定义属性', value: EventAttributesType.CUSTOM},
  {label: '预置属性', value: EventAttributesType.PREDEFINED},
  {label: '维度表属性', value: EventAttributesType.DIMENSION},
  {label: '虚拟属性', value: EventAttributesType.VIRTUAL}
]
const dataOptions = [
  {label: '整数', value: FieldType.INT},
  {label: '小数', value: FieldType.FLOAT},
  {label: '文本', value: FieldType.STRING},
  {label: '布尔', value: FieldType.BOOLEAN},
  {label: '日期', value: FieldType.DATE},
  {label: '日期时间', value: FieldType.DATETIME},
  {label: '对象', value: FieldType.VARIANT},
];
const DIMENSION = ref(false)
const DataTypeTag = computed(() => {
  return dataOptions.find(item => item.value === data.value.dataType)?.label
})
const TypeTag = computed(() => {
  return selectOptions.find(item => item.value === data.value.type)?.label
})
const parent = (value: any) => {
  if (value.code) {
    if (value.code.startsWith('user')) {
      return {label: '用户属性', value: 'userAttr'}
    }
    return {label: '事件属性', value: 'eventAttr'}
  }
  if (value.eventCode) {
    return {label: '元事件', value: 'metaEvent'}
  }
  return false
}
</script>

<template>
  <a-layout style="height: 100%;width: 100%;">
    <a-layout-header style="height: 100%;">
      <div class="layout-title">
        <a-breadcrumb>
          <a-breadcrumb-item style="cursor:pointer;" @click="gotoItem(parent(queryData).value)">
            {{ parent(queryData).label }}
          </a-breadcrumb-item>
          <a-breadcrumb-item style="cursor:pointer;">详情页</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </a-layout-header>
    <a-layout>
      <div class="container">
        <section class="basicSection">
          <div class="basicRow">
            <div class="name">{{ data.name }}</div>
            <div class="desc-edit-text">
              <div class="desc">
                <a-tooltip :content="data.displayName">
                  <p class="columnDesc">{{ data.displayName }}</p>
                </a-tooltip>
                <a-button class="editable-edit">
                  <template #icon>
                    <icon-edit/>
                  </template>
                </a-button>
              </div>
            </div>
          </div>
          <div class="tags">
            <div v-if="DataTypeTag" class="tag-item">{{ DataTypeTag }}</div>
            <div class="tag-item" :style="DataTypeTag?'margin-left: 8px':''">{{ TypeTag }}</div>
          </div>
          <div class="info">
            <div class="left">
              <div class="labels">
                <div class="labelItem">创建时间</div>
                <div v-if="!DIMENSION" class="labelItem" style="margin-top: 12px;">备注</div>
                <div v-if="DIMENSION" class="labelItem" style="margin-top: 12px;">实时的使用状态</div>
              </div>
              <div class="vals">
                <div class="labelItem">
                  <span class="timeLabel">2020-03-23 15：34：54</span>
                </div>
                <div v-if="!DIMENSION" class="labelItem" style="margin-top: 12px;">
                  <div class="remark">
                    <div class="editable-text-edit">
                      <p v-if="data.note" class="remarkLabel">{{ data.note }}</p>
                      <p v-else class="remarkLabel">-</p>
                      <a-button class="editable-edit">
                        <template #icon>
                          <icon-edit/>
                        </template>
                      </a-button>
                    </div>
                  </div>
                </div>
                <div v-if="DIMENSION" class="labelItem" style="margin-top: 12px;">
                  <span style="display: flex;align-items: center">
                     <img src="/icon/usePoint.svg" alt="可用">
                  <span class="margin" style="color: var(--tant-blue-blue-60)">可用</span>
                  </span>

                </div>
              </div>
            </div>
            <div class="right">
              <div class="labels">
                <div v-if="!DIMENSION" class="labelItem">实时的使用状态</div>
                <div v-if="DIMENSION" class="labelItem">创建人</div>
                <div v-if="DIMENSION" class="labelItem" style="margin-top: 12px;">备注</div>
              </div>
              <div class="vals">
                <div v-if="!DIMENSION" class="labelItem">
                  <span style="display: flex;align-items: center">
                     <img src="/icon/usePoint.svg" alt="可用">
                  <span class="margin" style="color: var(--tant-blue-blue-60)">可用</span>
                  </span>

                </div>
                <div v-if="DIMENSION" class="labelItem">
                  <span>zz</span>
                </div>
                <div v-if="DIMENSION" class="labelItem" style="margin-top: 12px;">
                  <div class="remark">
                    <div class="editable-text-edit">
                      <p v-if="data.note" class="remarkLabel">{{ data.note }}</p>
                      <p v-else class="remarkLabel">-</p>
                      <a-button class="editable-edit">
                        <template #icon>
                          <icon-edit/>
                        </template>
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section class="tabsSection">
          <div class="next-tabs-div">
            <a-tabs type="line">
              <a-tab-pane v-if="DIMENSION" key="0" title="属性规则">
                <div>
                </div>
              </a-tab-pane>
              <a-tab-pane v-if="parent(queryData).label==='事件属性'" key="1" title="关联事件">
                <correlation-event :data-code="data.code"/>
              </a-tab-pane>
              <a-tab-pane v-if="parent(queryData).label==='元事件'" key="2" title="关联事件属性">
                <correlation-event  />
              </a-tab-pane>
              <a-tab-pane v-if="DIMENSION" key="3" title="维度表">
                {{data.code}}
              </a-tab-pane>
              <a-tab-pane key="4" title="影响范围">
                {{data.code}}
              </a-tab-pane>
              <a-tab-pane key="5" title="变更日志">
                Content of Tab Panel 3
              </a-tab-pane>
            </a-tabs>
          </div>
        </section>
      </div>
    </a-layout>
  </a-layout>
</template>

<style scoped lang="less">
:deep(.arco-breadcrumb-item):hover {
  color: var(--color-text-1);
  font-weight: 500;
}

.layout-title {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  height: 32px;

}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 780px;
  margin-right: -24px;
  overflow-x: hidden;
  overflow-y: auto;

  .basicSection {
    position: relative;
    flex: 0 0 auto;
    padding: 24px;
    margin-right: 16px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;

    .basicRow {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 32px;

      .name {
        color: var(--tant-text-gray-color-text1-1);
        font: var(--tant-header-font-header4-medium);
      }

      .desc-edit-text {
        .desc {
          margin-left: 8px;
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-header-font-header5-regular);
          display: flex;
          align-items: center;

          .columnDesc {
            max-width: 240px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          .editable-edit {
            padding: 4px;
            margin-left: 8px;
            height: 24px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
            color: var(--tant-text-gray-color-text1-2);
            background-color: transparent;
            border: none;
            visibility: hidden;

            &:hover {
              background-color: var(--tant-secondary-color-secondary-transp-hover);
            }
          }

          &:hover .editable-edit {
            visibility: visible;
            transition: .3s;
          }
        }
      }
    }

    .tags {
      margin-top: 16px;

      .tag-item {
        display: inline-block;
        height: 22px;
        padding: 0 8px;
        color: var(--tant-secondary-color-secondary-default);
        font: var(--tant-body-font-body-regular);
        vertical-align: middle;
        background-color: var(--tant-secondary-color-secondary-fill);
        border-radius: 2px;
      }
    }

    .info {
      display: flex;
      flex-direction: row;
      margin-top: 24px;

      .left, .right {
        display: flex;
        flex: 1 1 auto;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;
      }

      .labels {
        flex: 0 0 auto;
        color: var(--tant-text-gray-color-text1-3);
        font: var(--tant-body-font-body-regular);

        .labelItem {
          position: relative;
          height: 22px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .vals {
        flex: 1 1;
        margin-left: 12px;
        color: var(--tant-text-gray-color-text1-2);
        font: var(--tant-body-font-body-regular);

        .labelItem {
          position: relative;
          height: 22px;
          white-space: nowrap;
          text-overflow: ellipsis;

          .timeLabel {
            box-sizing: border-box;
          }

          .remark {
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);

            .editable-text-edit {
              display: flex;
              align-items: center;

              .remarkLabel {
                font-family: var(--init-font-family);
                width: max-content;
                min-width: 30px;
                max-width: 200px;
                overflow: hidden;
                color: var(--tant-text-gray-color-text1-2);
                font: var(--tant-body-font-body-regular);
                white-space: nowrap;
                text-overflow: ellipsis;
              }

              .editable-edit {
                padding: 4px;
                margin-left: 8px;
                height: 24px;
                font: var(--tant-body-font-body-regular);
                border-radius: var(--tant-border-radius-medium);
                color: var(--tant-text-gray-color-text1-2);
                background-color: transparent;
                border: none;
                visibility: hidden;

                &:hover {
                  background-color: var(--tant-secondary-color-secondary-transp-hover);
                }
              }

              &:hover .editable-edit {
                visibility: visible;
                transition: .3s;
              }
            }
          }

          .margin {
            margin-right: 2px;
            margin-left: 2px;
          }
        }
      }


    }
  }

  .tabsSection {
    flex: 1 1;
    min-height: 100%;
    max-height: 100%;
    margin-top: 16px;
    padding-top: 12px;
    box-sizing: border-box;
    margin-right: 16px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;

    .next-tabs-div {
      height: 100%;
      overflow: hidden;
      margin-left: 20px;

    }
  }
}

</style>