<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {getDays} from "@/utils/dateUtil";
import {onMounted, ref, watch} from "vue";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 利润
   */
  total: number[]

  /**
   * 渠道名称
   */
  detailNames: string[]

  /**
   * 渠道数据
   */
  detail: number[][]

}

const props = defineProps<Props>()

const option = ref<any>();

const refresh = () => {
  const detailLength = props.detailNames?.length;
  const detailSeries = [];
  if (detailLength && detailLength > 0) {
    for (let i = 0; i < detailLength; i++) {
      detailSeries.push({
        name: props.detailNames[i],
        type: 'bar',
        stack: 'total',
        data: props.detail[i],
      })
    }
  }
  const {chartOption} = useChartOption(() => {
    return {
      grid: {
        left: '36',
        right: '0',
        top: '10',
        // bottom: '24',
        bottom: '48'
      },
      legend: {
        data: ['总投放', ...(props.detailNames || [])],
        bottom: '0',
        type: 'scroll'
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        enterable: true,
        confine: true,
        className: 'echarts-tooltip-diy',
        extraCssText: 'max-height: 400px; overflow-y: auto;',
      },
      xAxis: {
        type: 'category',
        data: getDays(props.date[0], props.date[1]),
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter(value: number) {
            if (value >= 1000000) {
              return `${value / 1000000}M`; // 转换为 k 单位
            }
            if (value >= 1000) {
              return `${value / 1000}k`; // 转换为 k 单位
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '总投放',
          type: 'line',
          data: props.total,
          label: {
            show: true,
            position: 'top',
            formatter(params) {
              if (!params.value || typeof params.value !== 'number') {
                return params.value;
              }
              if (params.value >= 1000000) {
                return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
              }
              if (params.value >= 1000) {
                return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
              }
              return Math.round(params.value); // 保留整数
            }
          },
          markLine: {
            symbol: 'none',
            data: [{type: 'average', name: 'Avg'}],
            label: {
              show: true,
              position: 'end'
            }
          }
        },
        ...detailSeries
      ]
    };
  });
  option.value = chartOption.value
}

watch(props, () => {
  refresh()
})

onMounted(() => {
  refresh()
})

</script>

<template>
  <Chart :option="option"/>
</template>

<style scoped lang="less">

</style>