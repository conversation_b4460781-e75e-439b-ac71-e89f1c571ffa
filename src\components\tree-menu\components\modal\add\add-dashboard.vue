<script setup lang="ts">
import {computed, h, ref} from "vue";
import {SpaceDto} from "@/api/space/type";
import {Message, TreeNodeData} from "@arco-design/web-vue";
import cubeImg from '/public/icon/cube-open.svg';
import folderImg from '/public/icon/folder.svg';
import _ from "lodash";
import {RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {saveDashboard} from "@/api/dashboard/api";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {useDashboardStore} from "@/store";


interface Props {

  /**
   * 指定空间新建看板
   */
  addInSpace: string

  /**
   * 指定文件夹新建看板
   */
  addInFolder: string

  /**
   * 我的看板
   */
  mySpace: SpaceDto

  /**
   * 项目空间
   */
  projectSpaces: SpaceDto[]
}

const props = defineProps<Props>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const projectPosition = computed(() => {
  return props.projectSpaces.map(space => {
    return {
      'key': `space-${space.spaceId}`,
      'title': space.name,
      'icon': () => h(cubeImg),
      'children': space.folders?.map(folder => {
        return {
          'key': `folder-${folder.folderId}`,
          'title': folder.name,
          'icon': () => h(folderImg)
        }
      })
    } as TreeNodeData
  }) || []
})

const myPosition = computed(() => {
  return props.mySpace?.folders?.filter(folder => folder?.allowAdd !== false || folder.isDeletable).map(folder => {
    return {
      'key': folder.folderId,
      'title': folder.name,
      'icon': () => h(folderImg)
    }
  }) || []
})
const fieldNames = {value: 'key', label: 'title'}
const visible = defineModel<boolean>("visible", {default: false});
const dashboardName = ref<string>();
const dashboardPosition = ref<string>('my');
const projectPositionSelected = ref<string>();
const myPositionSelected = ref<string>('');
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})
const unfoldFolders = useSessionStorage("unfoldFolders", new Set<string>());
const dashboardStore = useDashboardStore()


const handleOk = () => {

  let spaceId = props.addInSpace;
  let folderId = props.addInFolder;
  if (_.isEmpty(spaceId)) {
    if (dashboardPosition.value === 'my') {
      spaceId = props.mySpace.spaceId;
      if (_.isEmpty(myPositionSelected.value)) {
        Message.warning("请选择看板位置");
        return;
      }
      folderId = myPositionSelected.value;
    } else if (dashboardPosition.value === 'other') {
      const selectedType = projectPositionSelected.value?.split('-')[0];
      if (_.isEmpty(projectPositionSelected.value)) {
        Message.warning("请选择看板位置");
        return;
      }
      if (selectedType === 'space') {
        spaceId = projectPositionSelected.value?.split('-')[1];
        folderId = undefined;
      } else if (selectedType === 'folder') {
        folderId = projectPositionSelected.value?.split('-')[1];
      }
    }
  }

  if (!dashboardName.value?.trim()) {
    Message.warning('看板名称不能为空');
  } else {
    saveDashboard({name: dashboardName.value, spaceId: spaceId, folderId: folderId, appId: sessionStorage.getItem('app-id')})
        .then((resp) => {
          dashboardSelected.value = {spaceId: spaceId, folderId: folderId, dashboardId: resp.id};
          dashboardStore.dashboardSelected = {spaceId: spaceId, folderId: folderId, dashboardId: resp.id};
          unfoldFolders.value.add(folderId);
          treeMenuEventBus.emit(RefreshEvent);
          dashboardName.value = undefined;
          visible.value = false;
          dashboardPosition.value = 'my'
          projectPositionSelected.value = ''
        })
        .catch((e) => {
          Message.error("新建看板失败！", e);
        });
  }
}
const handleCancel = () => {
  visible.value = false;
  dashboardName.value = undefined
}
</script>

<template>
  <a-modal
      :visible="visible"
      :mask-closable="false"
      :width="450"
      title-align="start"
      ok-text="新建"
      title="新建看板"
      @ok="handleOk"
      @cancel="handleCancel">
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          看板名称
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-input v-model:model-value="dashboardName" placeholder="请输入看板名称"/>
      </a-col>
    </a-row>
    <a-row v-if="_.isEmpty(addInSpace)" class="item" align="center">
      <a-col :span="5">
        <div class="label">
          看板位置
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-radio-group type="button" :model-value="dashboardPosition" @change="value => {dashboardPosition=value}">
          <a-radio value="my">我的看板</a-radio>
          <a-radio value="other">项目空间</a-radio>
        </a-radio-group>
      </a-col>
    </a-row>
    <a-row v-if="_.isEmpty(addInSpace) && dashboardPosition==='my'" class="item" align="center">
      <a-col :span="5"></a-col>
      <a-col :span="18" :offset="1">
        <a-select
            v-model="myPositionSelected"
            :options="myPosition"
            :field-names="fieldNames"
            placeholder="请选择添加位置">
          <template #option="{ data }">
            <div class="space-select-option">
              <img class="icon" src="/icon/folder.svg" alt=""/>
              <div class="label">
                {{ data.title }}
              </div>
            </div>
          </template>
        </a-select>
      </a-col>
    </a-row>
    <a-row v-if="_.isEmpty(addInSpace) && dashboardPosition==='other'" class="item" align="center">
      <a-col :span="5">
      </a-col>
      <a-col :span="18" :offset="1">
        <a-tree-select
            v-model="projectPositionSelected"
            :data="projectPosition"
            placeholder="请选择添加位置">
          <template #label="{ data }">
            <div class="space-select-option">
              <img v-if="projectPositionSelected?.split('-')[0]==='space'" class="icon" src="/icon/cube-open.svg" alt=""/>
              <img v-else class="icon" src="/icon/folder.svg" alt=""/>
              <div class="label">
                {{ data.label }}
              </div>
            </div>
          </template>
        </a-tree-select>
      </a-col>
    </a-row>
  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}

.space-select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

</style>