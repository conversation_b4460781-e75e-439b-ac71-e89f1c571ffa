/**
 * 将已选中的项目排在前面
 * @param allList 全部数据列表
 * @param selectedValues 已选中的值数组
 * @param valueKey 用于比较的字段名，默认为 'code'
 * @returns 排序后的数组，已选中的在前面
 */
export function sortSelectedFirst<T>(
  allList: T[], 
  selectedValues: any[], 
  valueKey: keyof T = 'code' as keyof T
): T[] {
  if (!allList?.length || !selectedValues?.length) {
    return allList || [];
  }
  
  const selected = allList.filter(item => selectedValues.includes(item[valueKey]));
  const unselected = allList.filter(item => !selectedValues.includes(item[valueKey]));
  
  return [...selected, ...unselected];
}