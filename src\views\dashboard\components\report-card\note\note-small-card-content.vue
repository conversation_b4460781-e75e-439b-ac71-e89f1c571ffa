<script setup lang="ts">
import {reactive, ref, watch} from 'vue';
import {Alignment, BalloonEditor, Bold, Essentials, Font, FontBackgroundColor, Heading, ImageBlock, ImageInline, ImageUpload, Italic, Link, List, Undo,} from 'ckeditor5';


interface Props {
  /**
   * 报表分析数据
   */
  noteAnalysisData: any;
  textColor: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['changeNote']);

const editor = BalloonEditor

const editorData = ref<string>(props.noteAnalysisData || '');

const editorConfig = reactive({
  language: 'zh-cn',
  toolbar: {
    items: ['undo', 'redo', '|', 'heading', 'bold', 'italic', 'fontColor', 'fontBackgroundColor', '|', 'alignment','bulletedList','imageUpload','numberedList', 'link',],
    shouldNotGroupWhenFull: true
  },
  plugins: [Bold, Essentials, Italic, Undo, Font, Heading, Link,List,Alignment,ImageBlock,ImageInline,ImageUpload,FontBackgroundColor],
  placeholder: '输入文本内容'
})

const onEditorBlur = () => {
  if (editorData.value!== props.noteAnalysisData.content){
    emit('changeNote', editorData.value)
  }
}


watch(() => props.noteAnalysisData, (newData, oldData) => {
  if (newData === undefined) {
    return
  }
  editorData.value = newData.content;

})
</script>

<template>
  <div class="card-content" :style="{ 'color': props.textColor }">
    <a-scrollbar style="overflow: auto;">
      <ckeditor
          ref="editorRef"
          v-model="editorData"
          :editor="editor"
          :config="editorConfig"
          @blur="onEditorBlur"
      ></ckeditor>
    </a-scrollbar>
  </div>
</template>

<style scoped lang="less">
.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
:deep(.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused))  {
  border-color: transparent;
  background-color: transparent;
  border-top: none;
}

:deep(.ck.ck-reset_all){
  display: none;
}
:deep(.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable)){
  border:none;
  box-shadow: none;
  background-color: transparent;
}

</style>
