<template>
    <a-modal v-model:visible="modalVisible" :width="520" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%;">
            <a-form-item field="name" label="名称">
                <a-input v-model="form.name" max-length="100" show-word-limit/>
            </a-form-item>
            <a-form-item field="userCodes" label="用户名单">
                <a-textarea
                v-model="form.userCodes"
                placeholder="多个用户请用【,】隔开"
                style="height: 100px;"
                allow-clear/>
            </a-form-item>
            <a-form-item field="status" label="启用">
                <a-switch v-model="form.status" :checked-value="1" :unchecked-value="0"/>
            </a-form-item>
            <a-form-item field="description" label="描述">
                <a-textarea
                v-model="form.description"
                placeholder="请输入"
                style="height: 100px;"
                :max-length="{length:200,errorOnly:true}"
                allow-clear
                show-word-limit/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {saveUserWhiteList} from "@/api/ab/api";


const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    code:undefined,
    status:1,
    name: '',
    description:'',
    userCodes:''
})


const rules = {
    name: [
        {
            required: true,
            message:'请填写白名单名称',
        }
    ],
    userCodes: [
        {
            required: true,
            message:'请填写用户名单',
        }
    ]
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const loading = ref(false)

const openModal = async (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    if(obj){
        form.code = obj.code;
        form.name = obj.name;
        form.status = obj.status;
        form.description = obj.description;
        form.userCodes = obj.userCodes.join(',');
        modalTitle.value = '编辑白名单'
    }else{
        form.code = undefined
        modalTitle.value = '新建白名单'
    }
    modalVisible.value = true
}



const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true;
            try {
                const userCodes =  form.userCodes.split(',').map(item => item.trim()).filter(Boolean);
                const params = {
                    userCodes,
                    description: form.description,
                    code: form.code,
                    name: form.name,
                    status: form.status
                }
                await saveUserWhiteList(params);
                if(form.code){
                    Message.success('保存成功')
                }else{
                    Message.success('创建成功');
                }
                modalVisible.value = false;
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            } finally {
                loading.value = false;
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>