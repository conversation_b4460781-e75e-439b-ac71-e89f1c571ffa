// 统一样式
.arco-picker,
.arco-input-wrapper,
.arco-textarea,
.arco-select-view-multiple,
.arco-select-view-single {
  border-radius: var(--tant-border-radius-medium);
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);

  &:hover {
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid var(--tant-primary-color-primary-hover);
  }
}

// 统一字体
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-input, .arco-select-view-single.arco-select-view-size-medium .arco-select-view-value,
.arco-select-dropdown .arco-select-option,
.arco-tabs-tab,
.arco-table .arco-table-th,
.arco-table .arco-table-td,
.select-btn,
.arco-list,
.arco-pagination-jumper > span,
.arco-form-item-label-col > .arco-form-item-label,
.arco-select-view-single.arco-select-view-size-medium .arco-select-view-value,
.arco-modal-body,
.arco-icon,
.select-btn-disabled,
.arco-table-size-small .arco-table-td,
.arco-table-size-small .arco-table-th,
.arco-modal-title,
.arco-message-content,
.arco-pagination-total,
.arco-pagination-item,
.arco-menu, //菜单
.arco-tree-node-title, // 树节点
.arco-checkbox, // checkbox
.arco-btn-size-medium, // 按钮
.arco-select-view-single // 选择框
{
  font-size: var(--font-size-body-default);
}

// 菜单
.arco-menu-horizontal .arco-menu-inner {
  padding: 8px 0;
}

.arco-menu-horizontal .arco-menu-item, .arco-menu-horizontal .arco-menu-group-title, .arco-menu-horizontal .arco-menu-pop-header, .arco-menu-horizontal .arco-menu-inline-header {
  line-height: 26px;
}

.arco-menu-selected-label {
  height: 2px;
  bottom: -4px;
}

.arco-btn-size-medium.arco-btn-shape-circle {
  width: 26px;
  height: 26px;
}

.arco-menu-horizontal .arco-menu-item:not(:first-child), .arco-menu-horizontal .arco-menu-pop:not(:first-child) {
  margin-left: 6px;
}

// 文本框
.arco-textarea-wrapper {
  background-color: transparent;
  border: none;
}

.arco-input-wrapper .arco-input.arco-input-size-medium {
  font-size: var(--font-size-body-default);
}

// 遮罩
.arco-modal-mask {
  background-color: rgba(0, 0, 0, .25);
}

.arco-drawer-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.01);
}

// 选择框下拉
.arco-dropdown-option {
  font-size: var(--font-size-body-default);
  line-height: 28px;
}

.arco-dropdown-option-content {
  width: 100%;
}

.arco-select-option-content {
  display: flex;
  align-items: center;
  gap: 5px;
}

// 多选框选择条目
.arco-select-view-multiple .arco-select-view-inner .arco-select-view-tag {
  border-radius: var(--tant-border-radius-medium) !important;;
  background-color: var(--color-fill-2) !important;;
  border: 0 !important;;
  max-width: 78% !important;;
}

// 表格
th.arco-table-th {
  background-color: #fff !important;
  border-bottom: 1px solid var(--color-neutral-3);
}

.arco-table-cell {
  .circle {
    display: inline-block;
    margin-right: 4px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: rgb(var(--blue-6));

    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

.arco-table-tr {
  height: 32px;
}

.arco-pagination-item{
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

.arco-message-icon {
  margin-right: 4px;
  margin-bottom: 4px;
}
.arco-table-filters-align-left svg{
  font-size: 15px;
  font-weight: 500;
}