import type {Router, RouteRecordNormalized} from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import {useAppStore} from '@/store';
import {NOT_FOUND, WHITE_LIST} from '../constants';

export default function setupPermissionGuard(router: Router) {

  router.beforeEach(async (to, from, next) => {
    const appStore = useAppStore();
    // 根据需要自行完善来源于服务端的菜单配置的permission逻辑
    // Refine the permission logic from the server's menu configuration as needed
    if (!appStore.appAsyncMenus.length && !WHITE_LIST.find((el) => el.name === to.name)) {
      await appStore.fetchServerMenuConfig();
    }
    const serverMenuConfig = [...appStore.appAsyncMenus, ...WHITE_LIST];
    let exist = false;
    while (serverMenuConfig.length && !exist) {
      const element = serverMenuConfig.shift();
      if (element?.name === to.name) exist = true;
      if (element?.children) {
        serverMenuConfig.push(
          ...(element.children as unknown as RouteRecordNormalized[])
        );
      }
    }
    if (exist) {
      next();
    } else {
      next(NOT_FOUND);
    }

    NProgress.done();
  });
}
