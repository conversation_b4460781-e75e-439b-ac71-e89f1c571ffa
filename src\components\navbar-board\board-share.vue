<script setup lang="ts">

import {reactive, ref} from "vue";
import {getDashboardShareDetail, getGroupUsers, shareDashboard} from "@/api/dashboard/api";
import {useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue'

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const dashboardId = ref('')

export interface UserGroup {
  // 组名
  name: string
  // ID
  id: string
  isDefault: boolean
  userNum: number
  // 看板身份权限
  boardAuthority?: number
}

export interface User {
  // 用户名
  name: string;
  // 用户ID
  userId: string;
  // 所属分组ID
  groupId: string;
  // 看板身份权限
  boardAuthority?: number
}

const shareVisible = ref(false)

const shareHandleCancel = () => {
  shareVisible.value = false;
}
const permissionType = ref(4)

const loading = ref(false)
// 记录选中的节点的键
const selectedKeys = ref<string[]>([]);
// 输入框值
const saveSelectedKeys = ref<string[]>([]);
// 按钮禁用状态
const selectButton = ref<boolean>(true)
const shareList = ref<any>([])
const shareLoading = ref(false)

// 应用
const handleOk = async () => {
  loading.value = true
  try {
    const data = {
      dashboardId: dashboardId.value,
      list: shareList.value.map(item => ({
        memberCode: item?.memberCode,
        memberGroupCode: item?.memberGroupCode,
        permissionType: item.boardAuthority
      })),
    }
    await shareDashboard(data)
    Message.success('分享成功')
    shareVisible.value = false
  } catch (error) {
    console.error('分享失败:', error)
  } finally {
    loading.value = false
  }
}

const shareSelectChange = (v: number) => {
  permissionType.value = v
}

const creatorInfo = reactive({name: '张三', memberCode: 'a', memberGroupCode: '1'})

const groupTreeData = ref<any>([]);

// 更新树选择禁用状态
const refreshTreeDisableStatus = ()=> {
  const disableItems = shareList.value.map(item => item.memberCode || item.memberGroupCode) || [];
  selectedKeys.value?.forEach(key => {
    disableItems.push(key);
  })
  groupTreeData.value.forEach(group => {
    group.disabled = false;
    group.children.forEach(user => {
      user.disabled = false;
    });
  });

  disableItems?.forEach(key => {
    groupTreeData.value.forEach(group => {
      if (!saveSelectedKeys.value.includes(group.key) && group.key === key) {
        group.disabled = true;
      }
      group.children.forEach(user => {
        if (!saveSelectedKeys.value.includes(user.key) && user.key.split('_')?.[1] === key) {
          user.disabled = true;
        }
      });
    });
  })
}

// 修改添加按钮事件
const disableSelectedOptions = () => {
  saveSelectedKeys.value.forEach(key => {
    // 查找选中的组或用户
    const selectedGroup = groupTreeData.value.find(group => group.key === key);
    if (selectedGroup) {
      // 分享到组
      shareList.value.push({
        name: selectedGroup.title,
        memberGroupCode: selectedGroup.key,
        boardAuthority: permissionType.value
      });
    } else {
      // 分享到用户
      groupTreeData.value.forEach(group => {
        const selectedUser = group.children.find(user => user.key === key);
        if (selectedUser) {
          shareList.value.push({
            name: selectedUser.title,
            memberCode: selectedUser.key.split('_')?.[1],
            boardAuthority: permissionType.value
          });
        }
      });
    }
  });
  saveSelectedKeys.value = [];
  selectedKeys.value = [];
  selectButton.value = true;
  // 更新禁用状态
  refreshTreeDisableStatus()
};

// 删除共享者
const deleteShareUser = (memberGroupCode, memberCode, index) => {
  shareList.value.splice(index, 1)
  refreshTreeDisableStatus()
}

// 添加选择的功能
const onTreeSelect = (value: string | string[]) => {
  if (Array.isArray(value)) {
    selectedKeys.value = value.map(v => v.split('_').length > 1 ? v.split('_')?.[1] : v)
  } else {
    selectedKeys.value = value.split('_').length > 1 ? value.split('_')?.[1] : value
  }
  if (saveSelectedKeys.value.length !== 0) {
    selectButton.value = false
  } else {
    selectButton.value = true
  }
  refreshTreeDisableStatus()
};

const getInitials = (fullName: string) => {
  if (!fullName) {
    return '';
  }
  const names = fullName?.trim().split(' ');
  return names.map(name => name.charAt(0).toUpperCase()).join('');

}
// 更新权限
const updateAuthority = (value: any, index: number) => {
  shareList.value[index].boardAuthority = value;
};
const treeLoading = ref(false)
const getTreeData = async () => {
  treeLoading.value = true;
  try {
    const res = await getGroupUsers();
    // 直接处理返回的树结构数据
    groupTreeData.value = res.map(group => ({
      title: group.name,
      value: group.code,
      key: group.code,
      disabled: !group.code,
      children: group.users.map(user => ({
        title: user.name,
        value: user.code,
        key: group.code + '_' + user.code,
        disabled: !user.code
      }))
    }));
    // 更新树结构的禁用状态
    refreshTreeDisableStatus()
  } catch (error) {
    console.error('获取应用分组数据失败:', error);
    Message.error('获取应用分组数据失败');
  } finally {
    treeLoading.value = false;
  }
};

const getShareData = async () => {
  shareLoading.value = true;
  try {
    const res = await getDashboardShareDetail(dashboardId.value);
    shareList.value = res?.share.map(item => {
      return {
        ...item,
        boardAuthority: item.permissionType
      }
    })
    creatorInfo.name = res?.creator?.name || ''
    // 更新树结构的禁用状态
    refreshTreeDisableStatus()
  } catch (error) {
    console.error('获取分享数据失败:', error);
  } finally {
    shareLoading.value = false;
  }
};

// 初始化
const openModal = (id?: string) => {
  dashboardId.value = id || dashboardSelected.value.dashboardId
  permissionType.value = 4
  selectedKeys.value = []
  saveSelectedKeys.value = []
  selectButton.value = true
  shareVisible.value = true;
  getTreeData()
  getShareData()
};

const filterTreeNode = (searchValue: string, nodeData: any) => {
  const targetText = nodeData?.title?.toLowerCase();
  const searchText = searchValue.toLowerCase();
  return targetText?.includes(searchText);
};
defineExpose(
    {
      openModal
    }
)
</script>

<template>
  <a-modal
      v-model:visible="shareVisible"
      title-align="start"
      :mask-closable="false"
      width="474px">
    <template #title>
      看板分享设置
    </template>
    <div class="share-board">
      <div class="share-setting">
        <div class="share-user">
          <!--          选择成员-->
          <a-tree-select
              v-model:model-value="saveSelectedKeys"
              tree-checked-strategy="all"
              :loading="treeLoading"
              :tree-check-strictly="true"
              :allow-search="true"
              :allow-clear="true"
              :tree-checkable="true"
              :data="groupTreeData"
              :filter-tree-node="filterTreeNode"
              placeholder="搜索成员组、成员"
              style="width: 245px;"
              :border="false"
              @change="onTreeSelect"
          >
          </a-tree-select>
        </div>
        <div class="share-userId">
          <a-select
              v-model="permissionType"
              :style="{width:'90px'}"
              placeholder="Select"
              :bordered="false"
              :trigger-props="{ autoFitPopupMinWidth: true }"
              @input-value-change="shareSelectChange">
            <div class="option">
              <a-option :value="4" label="查看者">
                <div class="label">
                  查看者
                </div>
              </a-option>
            </div>
            <div class="option">
              <a-option :value="3" label="协作者">
                <div>
                  <div class="label">
                    协作者
                  </div>
                  <div class="tag">
                    添加或移除看板报表、修改看板
                    设置、编辑看板中的全部报表
                  </div>
                </div>
              </a-option>
            </div>
          </a-select>
        </div>
        <div class="share-button">
          <a-button :disabled="selectButton" @click="disableSelectedOptions">添加</a-button>
        </div>
      </div>
      <div class="share-creater">
        <div class="share-creater-name">
          看板创建者
        </div>
        <div>
          <div>
            <div class="share-avatar">
              <div class="share-avatar-avatar">
                <a-avatar
                    :size="27"
                    :style="{backgroundColor:'var(--tant-decorative-orange-color-decorative2-5)'}">
                  {{ getInitials(creatorInfo.name) }}
                </a-avatar>
                <div class="share-avatar-name">{{ creatorInfo.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="share-creater">
        <div class="share-creater-name">
          分享者
        </div>
        <a-spin :loading="shareLoading" class="head">
          <div v-for="(avatar,index) in shareList" :key="index">
            <div v-if="avatar.boardAuthority===3||avatar.boardAuthority===4" class="share-avatar">
              <div class="share-avatar-avatar">
                <a-avatar
                    :size="27"
                    :style="{backgroundColor:'var(--tant-decorative-orange-color-decorative2-1)'}">
                  <div>{{ avatar.name || avatar.groupName }}</div>
                </a-avatar>
                <div class="share-avatar-name">{{ avatar.name || avatar.groupName }}
                  <span style="color: #ccc;">{{ avatar.memberCode ? '(人员)' : '(组织)' }}</span>
                </div>
              </div>
              <div class="share-avatar-selece">
                <a-select
                    :default-value="avatar.boardAuthority"
                    :style="{width:'100px'}"
                    :trigger-props="{ autoFitPopupMinWidth: true }"
                    :bordered="false"
                    @change="(value) => updateAuthority(value, index)"
                >
                  <div class="option">
                    <a-option :value="4" label="查看者">
                      <div class="label">
                        查看者
                      </div>
                    </a-option>
                  </div>
                  <div class="option">
                    <a-option :value="3" label="协作者">
                      <div>
                        <div class="label">
                          协作者
                        </div>
                        <div class="tag">
                          添加或移除看板报表、修改看板
                          设置、编辑看板中的全部报表
                        </div>
                      </div>
                    </a-option>
                  </div>
                </a-select>
              </div>
              <button class="share-avatar-delete" @click="deleteShareUser(avatar.memberGroupCode,avatar.memberCode,index)">
                <icon-close-circle/>
              </button>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <a-button style="margin-right: 10px;" @click="shareHandleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleOk">
          应用
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="less">

.share-board {
  padding: 0 12px;

  .share-setting {
    display: flex;
    padding-bottom: 20px;

    .share-user {
      display: flex;
      border: solid 1px var(--tant-border-color-border1-1);
      border-radius: 4px;
      border-right: none;
    }


    .share-userId {
      display: flex;
      border: solid 1px var(--tant-border-color-border1-1);
      border-radius: 4px;

    }

    .share-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      padding-left: 30px;
    }
  }

  .share-creater {
    padding-bottom: 20px;
    overflow: auto;

    .head {
      height: 30vh;
      overflow-y: auto;
      width: 100%;
    }

    .share-creater-name {
      margin-bottom: 8px;
      color: var(--tant-text-gray-color-text1-3);
      font: var(--tant-description-font-description-regular);
    }

    .share-avatar {
      display: flex;
      align-items: center;
      height: 40px;

      .share-avatar-avatar {
        display: flex;
        align-items: center;
        width: 245px;
        overflow: hidden;
        white-space: nowrap;
        gap: 10px;
      }

      .share-avatar-name {
        display: flex;
        max-width: calc(100% - 40px);
        margin-right: auto;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        font: var(--tant-body-font-body-regular);
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .share-avatar-selece {
        display: flex;
        align-items: center;
      }

      .share-avatar-delete {
        display: none;
      }

    }

    .share-avatar:hover {
      .share-avatar-delete {
        display: flex;
        border: none;
        align-items: center;
        background-color: var(--tant-bg-white-color-bg1-1);
        cursor: pointer;
        height: 24px;
        border-radius: 5px;
        margin-left: 20px;
      }

      .share-avatar-delete:hover {
        background-color: var(--tant-red-red-30);
        transition: .3s;
      }

    }
  }
}

.label {
  display: flex;
  color: var(--tant-text-gray-color-text1-2);
  align-items: center;
  font: var(--tant-body-font-body-regular);
  height: 38px;
}

.tag {
  width: 180px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 12px;
  white-space: normal;
  line-height: 18px;
}

.option {
  padding: 0 8px;
  border-radius: 4px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}
</style>