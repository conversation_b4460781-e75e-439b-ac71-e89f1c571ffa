<template>
  <div class="data-configure">
    <div class="condition-list">
      <div class="label-text">数据版本</div>
      <div class="edit-select">
        <a-select
          v-model:model-value="form.dataVersion"
          allow-search
          :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
          placeholder="数据版本"
          @change="versionChange">
          <a-option v-for="(item,index) in versionList" :key="index" :value="item.name">
            {{ item.name }}
          </a-option>
        </a-select>
        <a-tooltip content="新增">
          <icon-plus-circle size="16" class="edit" @click="addData('新增数据版本')"/>
        </a-tooltip>
      </div>
      <div class="label-text">条件列表</div>
      <div class="handle-box">
        <a-input v-model:model-value="conditionText" style="background-color: #FFF" placeholder="搜索">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
        <a-tooltip content="添加条件">
          <a-button type="primary" style="margin-left: 12px;flex-shrink: 0;" @click="addCondition">
            <template #icon>
              <icon-plus/>
            </template>
          </a-button>
        </a-tooltip>
      </div>
      <div :style="{ maxHeight: 'calc(100% - 104px)', overflowY: 'auto' }">
        <div v-if="addInputList.length>0" class="add-box">
          <div v-for="(value,valueIndex) in addInputList" :key="valueIndex" class="input-list">
            <a-input v-model:model-value="value.name" placeholder="输入条件名称" @press-enter="inputEnter(value,valueIndex)"/>
            <div class="delete-icon" @click="deleteInputItem(valueIndex)">
              <icon-close-circle/>
            </div>
          </div>
        </div>
        <a-list :bordered="false">
            <a-list-item v-for="(item,index) in filteredList" :key="index" :style="conditionIndex === index ?{backgroundColor: '#fdfbfb'} : ''" @click="selectItem(item,index)">
                <a-list-item-meta>
                  <template #description>
                    <a-switch v-model="item.status" :disabled="item.code === 'default'" :checked-value="1" :unchecked-value="0"  size="small" style="margin-right: 8px;" @change="statusChange($event,item)"/>
                    <span :style="conditionIndex === index ?{color: 'rgb(var(--primary-6))'} : ''">{{ item.name }}</span>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <a-tooltip content="删除">
                    <icon-delete v-if="item.code !== 'default'" @click.stop="deleteItem(item,index)"/>
                  </a-tooltip>
                </template>
            </a-list-item>
        </a-list>
      </div>
    </div>
    <a-spin :loading="configLoading" class="configure-body">
      <div class="top">
        <div class="tips">使用条件，以便在满足条件时提供不同的参数值</div>
        <div class="handle-btn">
          <a-button class="cancel" @click="resetData">重置</a-button>
          <a-button type="primary" :loading="saveLoading" :disabled="!form.code" @click="saveData">
            保存
          </a-button>
        </div>
      </div>
      <div class="detail">
        <div v-if="form.code && form.code !== 'default'" class="form-content">
          <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="height:100%;">
            <div class="detail-content">
              <div class="form-content">
                <a-form-item field="country" label="国家地区" :hide-asterisk="true">
                  <a-select
                    v-model:model-value="form.country"
                    multiple
                    :scrollbar="true"
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                    :filter-option="false"
                    @search="searchCountry"
                    @change="countryChange">
                      <a-option v-for="item in countryList" :key="item.code" :value="item.code">
                          {{ item.name }}
                      </a-option>
                  </a-select>
                </a-form-item>
                <a-form-item field="appVersion" label="最小应用版本" :hide-asterisk="true">
                  <a-input-number v-model:model-value="form.appVersion" :min="0"/>
                </a-form-item>
                <a-form-item field="sysVersion" label="最小系统版本" :hide-asterisk="true">
                  <a-input-number v-model:model-value="form.sysVersion" :min="0"/>
                </a-form-item>
                <div class="men-content">
                  <a-form-item field="minMem" label="设备内存" :hide-asterisk="true">
                    <a-input-number v-model="form.minMem" :min="0" @blur="value1Change">
                    </a-input-number>
                  </a-form-item>
                  <span class="to">to</span>
                  <a-form-item field="maxMem" label="" :hide-asterisk="true" style="margin-top: 22px;">
                    <a-input-number v-model="form.maxMem" :min="0" @blur="value2Change">
                    </a-input-number>
                  </a-form-item>
                </div>
                <a-form-item field="adSource" label="广告源" :hide-asterisk="true">
                  <div class="edit-select">
                    <a-select v-model:model-value="form.adSource" allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                      <a-option v-for="(item,index) in sourceList" :key="index" :value="item.name">
                        {{ item.name }}
                      </a-option>
                    </a-select>
                    <a-tooltip content="新增">
                      <icon-plus-circle size="16" class="edit" @click="addData('新增广告源')"/>
                    </a-tooltip>
                  </div>
                </a-form-item>
                <a-form-item field="network" label="网络类型" :hide-asterisk="true">
                  <a-input v-model:model-value="form.network"/>
                </a-form-item>
                <a-form-item field="opt" label="设备类型" :hide-asterisk="true">
                  <a-input v-model:model-value="form.opt"/>
                </a-form-item>
                <!-- <a-form-item field="conditionNote" label="描述">
                  <a-textarea v-model:model-value="form.conditionNote" style="height: 120px;"/>
                </a-form-item> -->
              </div>
            </div>
          </a-form>
        </div>
        <div class="json-content">
          <div class="action-content">
            <div class="label">DATA</div>
            <div class="btns">
              <a-button type="primary" status="warning" size="small" @click="formatConfig">格式化</a-button>
              <a-button type="outline" status="warning" size="small" @click="compressConfig">压缩</a-button>
              <a-button type="outline" size="small" @click="copyConfig">复制</a-button>
              <a-button type="primary" size="small" status="danger" @click="clearConfig">清空</a-button>
            </div>
          </div>
          <json-editor-vue v-model="config" class="editor" @update:modelValue="updataModel" @validationError="editError"/>
        </div>
      </div>

    </a-spin>
    <addModal ref="addRef" @update-data="updateListData"/>
  </div>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import JsonEditorVue from 'json-editor-vue3'
import {Message} from '@arco-design/web-vue';
import {countrySearch, deleteConditionItem, getAdSourceList, getAppVersionList, getConditionList, getCountryList, saveCondition} from "@/api/marketing/api";
import addModal from "./addModal.vue";

const appId = ref('')
const conditionText = ref('')
const conditionIndex = ref(0)
const addInputList = ref<any>([])
const conditionList = ref<any>([])
const form = reactive({
  code:'',
  name: '',
  status:0,
  dataVersion: '',
  sysVersion:undefined,
  appVersion:undefined,
  country:[],
  adSource: '',
  minMem:undefined,
  maxMem:undefined,
  // conditionNote: ''
  network:'',
  opt:''
})
const countryList = ref<any>([])
const config = ref<any>({})
const rules = {}
const configLoading = ref(false)

const versionList = ref<any>([])
const sourceList = ref<any>([])

// 获取应用下发数据可用买量来源渠道列表
const getSourceList = async () => {
  await getAdSourceList().then(res => {
    sourceList.value = res
  })
}
// 获取应用下发数据可用数据版本列表
const getVersionList = async () => {
  await getAppVersionList({appId: sessionStorage.getItem('app-id') || ''}).then(res => {
    versionList.value = res
  })
}
// 获取条件列表
const getTabList = async () => {
  const data = {
    appId:appId.value,
    paramName:'default_json',
    conditionScope:'app_param_specific',
    dataVersion:form.dataVersion
  }
  await getConditionList(data).then(res => {
    conditionList.value = res.items || []
    conditionList.value.forEach(item => {
      item.value = item.value ? JSON.parse(item.value) : null
    })
  })
}
const filteredList = computed(() => {
  const str = conditionText.value.trim();
  return conditionList.value.filter(item => item?.name.includes(str));
});
const getCountry = async () => {
    await getCountryList().then(res => {
        countryList.value = res?.filter(item => {return item.code !== 'global'})
    })
}

const addCondition = () => {
  addInputList.value.push({
    name: ''
  })
}// 删除input
const deleteInputItem = (index: number) => {
  addInputList.value.splice(index, 1)
}

// 获取基本信息
const getInfo = async () => {
  configLoading.value = true
  appId.value = sessionStorage.getItem('app-id') || ''
  await getTabList()
  config.value = conditionList.value[0]?.value
  form.code = conditionList.value[0]?.code
  form.status = conditionList.value[0]?.status
  configLoading.value = false
}

const init = async () => {
  try {
    await Promise.all([
      getSourceList(),
      getVersionList(),
      getCountry()
    ]);
    form.dataVersion = versionList.value[0].name
  } catch (error) {
    console.error('初始化列表失败:', error);
  }
}
init()


// 新增modal
const addRef = ref()
const addData = (value: string) => {
  addRef.value.openModal(value)
}
const updateListData = async (v) => {
  const operationMap = {
    '新增广告源': getSourceList,
    '新增数据版本': getVersionList
  };
  const addFunction = operationMap[v];
  try {
    await addFunction();
  } catch (error) {
    console.error('更新失败:', error);
  }
}
// 国家搜索
const searchCountry = (v) => {
  const data = {
    selectCountryCode:form.country,
    content:v
  }
  countrySearch(data).then(res => {
    countryList.value = []
    countryList.value = res?.filter(item => {return item.code !== 'global'});
  })
}
// 国家切换
const countryChange = async () => {
  if(!form.country.length){
    searchCountry('')
  }
}
// 点击条件列表
const selectItem = (item,index) => {
  conditionIndex.value = index
  const {code,value,status,rules,note} = item
  form.code = code
  config.value = value
  form.status = status
  if(item.code !== 'default'){
    form.country = rules?.country?.thresholds
    form.appVersion = rules?.appVersion?.thresholds[0]
    console.log(form.appVersion,'form.appVersion');
    form.sysVersion = rules?.osVersion?.thresholds[0]
    form.minMem = rules?.osRams?.thresholds[0]
    form.maxMem = rules?.osRams?.thresholds[1]
    form.adSource = rules?.mediaSources?.thresholds.join(',')
    // form.conditionNote = note
  }
}
// 数据版本change
const versionChange = async () => {
  configLoading.value = true
  await getTabList()
  const obj = conditionList.value[0]
  conditionIndex.value = 0
  selectItem(obj,conditionIndex.value)
  configLoading.value = false
}
const statusChange = async (e,obj) => {
  console.log(obj,'ee');
  const params = {
    appId: appId.value,
    paramName:'default_json',
    conditionCode:obj.code,
    conditionStatus:obj.status,
  }
  try {
    await saveCondition(params).then(res => {
      Message.success('更改成功')
      getTabList()
    })
  } catch (error) {
    console.log(error);
  }
}
// input回车操作
const inputEnter = (obj, index) => {
  if (obj.name) {
    // conditionList.value.push(obj)
    const data = {
      appId:appId.value,
      paramName:'default_json',
      paramScope:'app_specific',
      conditionName:obj.name,
      dataVersion:form.dataVersion,
      rules:{}
    }
    saveCondition(data).then(async res => {
      if (res) {
        configLoading.value = true
        await getTabList()
        const itemData = conditionList.value.find(item => item.code === res.code)
        const itemIndex = conditionList.value.findIndex(item => item.code === res.code)
        selectItem(itemData,itemIndex)
        configLoading.value = false
        Message.success('创建成功')
      }
    })
    addInputList.value.splice(index, 1)
  }
}

const deleteItem = (item,index) => {
  const data = {
    appId:appId.value,
    paramName:'default_json',
    conditionCode:item.code,
    dataVersion:form.dataVersion
  }
  deleteConditionItem(data).then(res => {
    if(res){
      Message.success('删除成功')
      configLoading.value = true
      getTabList()
      conditionIndex.value = index-1
      const info = conditionList.value[index-1]
      const {code,value,status,rules,note} = info
      form.code = code
      config.value = value
      form.status = status
      if(item.code !== 'default'){
        form.country = rules?.countries?.thresholds
        form.appVersion = rules?.appVersion?.thresholds[0]
        form.sysVersion = rules?.osVersion?.thresholds[0]
        form.minMem = rules?.osRams?.thresholds[0]
        form.maxMem = rules?.osRams?.thresholds[1]
        form.adSource = rules?.mediaSources?.thresholds.join(',')
        form.network = rules?.network?.thresholds[0]
        form.opt = rules?.opt?.thresholds[0]
        // form.conditionNote = note
      }
      configLoading.value = false
    }
  })
}
// 是否存在错误
const errors = ref(0);
// 错误行数
const line = ref();
// 数据更新时触发
const updataModel = (val: any) => {
  config.value = val;
};
// 数据错误时触发
const editError = (a: any, error: any) => {
  errors.value = error.length;
  console.log(errors.value, 'errors.value');
  if (error[0]) {
    line.value = error[0].line;
  }
}


// 格式化
const formatConfig = () => {
  if (typeof config.value === 'string') {
    config.value = JSON.parse(config.value);
  }
}
// 压缩
const compressConfig = () => {
  if (typeof config.value === 'object') {
    const compressed = JSON.stringify(config.value);
    config.value = compressed;
  }
}

// 复制
const copyConfig = () => {
  navigator.clipboard.writeText(JSON.stringify(config.value)).then(() => {
    Message.success('复制成功')
  }).catch(err => {
    console.error('复制失败:', err);
  });
}
// 清空
const clearConfig = () => {
  config.value = {}
}
const value1Change = () => {
  const num1 = form.minMem
  const num2 = form.maxMem
  if (num1 !== undefined && num2 !== undefined &&num1 >= 0 && num1 > num2) {
    form.maxMem = num1
  }
}

const value2Change = () => {
  const num1 = form.minMem
  const num2 = form.maxMem
  if (num1 !== undefined && num2 !== undefined && num2 < num1) {
    form.minMem = num2
  }
}
// 保存
const formRef = ref()
const saveLoading = ref(false)
const saveData = async() => {
  if(!config.value){
    Message.error('请输入json')
    return
  }
  saveLoading.value = true
  const ruleObj = {
      country: {
          name: "country",
          symbol: "eq",
          thresholds: form.country ? form.country : []
      },
      appVersion: {
          name: "app_version",
          symbol: "gt",
          thresholds: form.appVersion !== undefined ? [form.appVersion] : []
      },
      OSVersion: {
          name: "os_version",
          symbol: "gt",
          thresholds: form.sysVersion !== undefined ? [form.sysVersion] : []
      },
      OSRams: {
          name: "os_rams",
          symbol: "scope",
          thresholds: (form.minMem !== undefined && form.maxMem !== undefined) ? [form.minMem, form.maxMem] : []
      },
      mediaSources: {
          name: "media_sources",
          symbol: "eq",
          thresholds: form.adSource ? [form.adSource] : []
      },
      network: {
          name: "network",
          symbol: "eq",
          thresholds: form.network ? [form.network] : []
      },
      opt: {
          name: "opt",
          symbol: "eq",
          thresholds: form.opt ? [form.opt] : []
      },
  }
  const filteredRules = Object.fromEntries(
    Object.entries(ruleObj).filter(([key, value]) => value.thresholds.length > 0)
  );
  const params = {
      appId: appId.value,
      value: JSON.stringify(config.value),
      paramName: 'default_json',
      paramScope:'app_specific',
      dataVersion:form.dataVersion,
      conditionCode: form.code,
      conditionStatus: form.status,
      conditionScope: 'app_param_specific',
      valueType: 'json',
      // conditionNote: form.conditionNote,
      rules: formRef.value ? filteredRules : {}
  };
  try {
    await saveCondition(params).then(async res => {
      Message.success('保存成功')
      configLoading.value = true
      await getTabList()
      const obj = conditionList.value[conditionIndex.value]
      selectItem(obj,conditionIndex.value)
      configLoading.value = false
    })
  } catch (error) {
    console.log(error);

  } finally {
    saveLoading.value = false
  }

}
// 重置
const resetData = () => {
  formRef.value.resetFields();
  config.value = {}
}

defineExpose({getInfo})
</script>

<style scoped lang="less">

// :deep(.arco-form-item:last-of-type) {
//   margin-bottom: 0;
// }

.detail-content {
  display: flex;
  align-items: stretch;

  .line {
    border-left: 2px dashed #ccc;
    margin-left: 24px;
    margin-right: 24px;
    width: 2px;
  }

  .form-content {
    flex: 1;
  }
}

.data-configure {
  width: 100%;
  height: 100%;
  display: flex;
  .condition-list{
    width: 33%;
    height: 100%;
    padding-right: 12px;
    .handle-box{
      display: flex;
      margin-bottom: 12px;
    }
  }
  .configure-body {
    width: 100%;
    height: 100%;
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    padding: 12px;
    overflow-y: auto;
    .top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      .handle-btn{
        button {
            margin-left: 8px;
            border-radius: 4px;
          }
      }
    }
    .detail{
      display: flex;
      width: 100%;
      height: calc(100% - 44px);
    }

    .form-content {
      width: 33%;
      height: 100%;
      overflow-y: auto;
    }

    .json-content {
      flex: 1;
      padding-left: 8px;
      height: 100%;

      .action-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--tant-fill-color-fill1-2);

        .label {
          font-weight: 700;
        }

        .btns {
          button {
            margin-left: 8px;
            border-radius: 4px;
          }
        }
      }

      .editor {
        height: calc(100% - 56px);
        // height: 100%;
      }
    }
  }

}

.configure-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  height: 50px;
  margin-top: 16px;
  padding: 0 24px;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

:deep(.full-screen) {
  display: none !important;
}

:deep(.jsoneditor-poweredBy) {
  display: none !important;
}

:deep(.jsoneditor-modes) {
  display: none !important;
}

.edit-select {
  width: 100%;
  position: relative;

  .edit {
    position: absolute;
    right: 30px;
    top: 8px;
    opacity: 0;
    cursor: pointer;
  }

  &:hover {
    .edit {
      opacity: 1;
    }
  }
}
.men-content{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .to{
    margin: 0 12px;
  }
}
:deep(.arco-list-item){
  padding: 8px;
  cursor: pointer;
}
:deep(.arco-list-item:hover){
  background-color: #fdfbfb
}
.add-box {
  background: #fff;

  .input-list {
    margin-bottom: 12px;
    position: relative;
  }

  .delete-icon {
    position: absolute;
    right: 12px;
    top: 4px;
    cursor: pointer;
  }
}
.label-text{
  color: var(--color-text-2);
  font-size: 14px;
  white-space: normal;
  line-height: 28px;
}
</style>
