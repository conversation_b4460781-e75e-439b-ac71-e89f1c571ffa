<template>
  <div class="selected-content">
    <a-tooltip content="分析数据源" placement="top">
      <a-select
          v-model:model-value="dataSourceModel"
          :style="{width:props.width,backgroundColor:'#fff'}"
          :show-extra-options="false"
          :disabled="disabled"
          @change="handleChange">
        <a-option value="event">IVY</a-option>
        <a-option value="appsflyer">AF</a-option>
      </a-select>
    </a-tooltip>
  </div>
</template>

<script lang="ts" setup>
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";

const props = defineProps({
  width: {
    type: String,
    default: '80px'
  },
  // 禁用缓存更新
  disableStorage: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const localStorageEventBus = useEventBus(LocalStorageEventBus)
const dataSourceModel = defineModel<string>("dataSource", {default: undefined});
// 绑定方法，传给外部选择值
const emits = defineEmits(['selected']);
// 数据源初始化
const initDataSource = () => {
  if (dataSourceModel.value && props.disableStorage) {
    // 禁用缓存且已有值，无需初始化
    return;
  }

  // 有绑定值以绑定值优先
  if (dataSourceModel.value && !props.disableStorage) {
    sessionStorage.setItem('data-source', dataSourceModel.value)
    localStorageEventBus.emit("data-source", 'event')
    return;
  }

  // 无绑定值以缓存优先
  // 1. sessionStorage 缓存
  const sessionDataSource = sessionStorage.getItem('data-source');
  if (sessionDataSource) {
    dataSourceModel.value = sessionDataSource
    emits('selected', sessionDataSource)
    return;
  }

  // 2. localStorage 缓存
  const localDataSource = localStorage.getItem('data-source');
  if (localDataSource) {
    dataSourceModel.value = localDataSource
    sessionStorage.setItem('data-source', localDataSource)
    localStorageEventBus.emit("data-source", localDataSource)
    emits('selected', localDataSource)
    return;
  }

  // 3. sessionStorage appData 缓存
  const sessionAppData = JSON.parse(sessionStorage.getItem('app-data'));
  if (sessionAppData?.eventDataSource) {
    const defaultDataSource = sessionAppData?.eventDataSource === 'IVY' ? 'event' : 'appsflyer';
    dataSourceModel.value = defaultDataSource
    sessionStorage.setItem('data-source', defaultDataSource)
    localStorageEventBus.emit("data-source", defaultDataSource)
    emits('selected', defaultDataSource)
    return;
  }

  // 4. localStorage appData 缓存
  const localAppData = JSON.parse(localStorage.getItem('app-data'));
  if (localAppData?.eventDataSource) {
    const defaultDataSource = sessionAppData?.eventDataSource === 'IVY' ? 'event' : 'appsflyer';
    dataSourceModel.value = defaultDataSource
    sessionStorage.setItem('data-source', defaultDataSource)
    localStorageEventBus.emit("data-source", defaultDataSource)
    emits('selected', defaultDataSource)
    return;
  }

  // 默认值 af
  dataSourceModel.value = 'appsflyer'
  sessionStorage.setItem('data-source', 'appsflyer')
  localStorageEventBus.emit("data-source", 'appsflyer')
  emits('selected', 'appsflyer')
}

const handleChange = (value) => {
  dataSourceModel.value = value;
  if (!props.disableStorage) {
    sessionStorage.setItem('data-source', value)
    localStorage.setItem('data-source', value)
    localStorageEventBus.emit("data-source", value)
  }
  emits('selected', value);
}
localStorageEventBus.on((name, value) => {
  if (name === 'app-data') {
    if (value?.eventDataSource) {
      const defaultDataSource = value?.eventDataSource === 'IVY' ? 'event' : 'appsflyer';
      dataSourceModel.value = defaultDataSource
      sessionStorage.setItem('data-source', defaultDataSource)
      localStorageEventBus.emit("data-source", defaultDataSource)
      emits('selected', defaultDataSource)
    }
  }
})
initDataSource();
</script>

<style scoped lang="less">
.game-name {
  padding-left: 8px;
  margin-bottom: 4px;
}

.select-content {
  padding-bottom: 8px;
  border-bottom: dashed 1px var(--color-neutral-4);
}
</style>
  