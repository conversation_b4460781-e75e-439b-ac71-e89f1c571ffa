<!--单事件选择器-->
<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {getAnalyseEventList} from "@/api/analyse/api";
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";

const props = defineProps({
  width: {
    type: String,
    default: '280px'
  },
  eventCode: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['eventChange']);
const localStorageEventBus = useEventBus(LocalStorageEventBus)

const selectLoading = ref(true)
const eventSelected = ref<string>()
const eventList = ref([])
const fullEventList = ref([])

/**
 * 事件搜索
 */
const handleSearch = (v) => {
  eventList.value = fullEventList.value.filter(item => item.name.indexOf(v) > -1 || item.displayName.indexOf(v) > -1)
}

const handleChange = (v: string) => {
  emits('eventChange', JSON.parse(v))
}

watch(() => props.eventCode, () => {
  if (props.eventCode?.length > 0 && fullEventList.value?.length > 0) {
    eventSelected.value = JSON.stringify(fullEventList.value.filter(item => item.code === props.eventCode)?.[0] || {})
  }
})

const init =()=>{
  getAnalyseEventList({inApp: 1, types: 0}).then(res => {
    const initEventList = []
    let hasTrackEvent = false;
    res?.forEach(item => {
      // eventSelected.value 有值则不执行初始化
      if (item.eventName === 'start_track_event' && !eventSelected.value) {
        eventSelected.value = JSON.stringify({
          code: item.eventCode,
          name: item.eventName,
          displayName: item.eventDisplayName,
        })
        hasTrackEvent = true
      }
      if (!hasTrackEvent && item.eventName === 'sdk_app_open' && !eventSelected.value) {
        eventSelected.value = JSON.stringify({
          code: item.eventCode,
          name: item.eventName,
          displayName: item.eventDisplayName,
        })
      }

      initEventList.push({
        code: item.eventCode,
        name: item.eventName,
        displayName: item.eventDisplayName,
      })
    })

    fullEventList.value = initEventList;
    eventList.value = initEventList;
    selectLoading.value = false;
  })
}

onMounted(() => {
  init()
})

/**
 * app变化时刷新列表
 */
localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    init()
  }
})

</script>

<template>
  <a-select
      v-model:model-value="eventSelected"
      :loading="selectLoading"
      :style="{width:props.width}"
      :show-extra-options="false"
      :filter-option="false"
      allow-search
      @search="handleSearch"
      @change="handleChange">
    <a-option v-for="item in eventList" :key="item.code" :value="JSON.stringify(item)">{{ item.name }} - {{ item.displayName }}</a-option>
  </a-select>
</template>

<style scoped lang="less">


</style>