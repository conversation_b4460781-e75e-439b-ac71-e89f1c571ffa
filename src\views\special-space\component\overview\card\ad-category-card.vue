<script setup lang="ts">
import PureTotalDetail from "@/views/special-space/component/overview/chart/pure-total-detail.vue";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 数据
   */
  dataIndex: number

  /**
   * 标题
   */
  title: string | undefined

  /**
   * 数据名
   */
  dataName: string

  /**
   * 总数据
   */
  total: number[]

  /**
   * 总数据
   */
  diff: number[]

  /**
   * 总数据
   */
  diffRate: number[]

  /**
   * 详细数据
   */
  detail: number[][]

  /**
   * 详细数据名
   */
  detailNames: string[]

  /**
   * 加载中
   */
  loading: boolean
}

const props = defineProps<Props>();
const emits = defineEmits(['showFullScreen']);
</script>

<template>
  <div class="report-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ props.title }}
      </div>
      <div class="report-card-indicator">
        <div class="report-card-indicator-item">
          <div class="report-card-indicator-title">
          </div>
          <div v-if="diff[diff?.length - 1] ===0" class="report-card-indicator-data">
            -- ( -- %)
          </div>
          <div v-if="diff[diff?.length - 1] && diff[diff?.length - 1] >=0" class="report-card-indicator-data up">
            ↑ +{{ diff[diff?.length - 1] }} (+{{ diffRate[diffRate?.length - 1] }}%)
          </div>
          <div v-if="diff[diff?.length - 1]  && diff[diff?.length - 1]  < 0" class="report-card-indicator-data down">
            ↓ {{ diff[diff?.length - 1] }} ({{ diffRate[diffRate?.length - 1] }}%)
          </div>
        </div>
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="全屏">
          <div class="operation-icon" @click="()=>{emits('showFullScreen','ad-category-card',{
            dataIndex: props.dataIndex
          })}">
            <icon-fullscreen class="icon"/>
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-content">
      <a-spin :loading="props.loading" style="width: 100%; height: 100%">
        <pure-total-detail :date="props.date" :total="props.total" :detail-names="props.detailNames" :detail="detail" :total-name="dataName"/>
      </a-spin>
    </div>
  </div>

</template>

<style scoped lang="less">
@import './style.less';

</style>