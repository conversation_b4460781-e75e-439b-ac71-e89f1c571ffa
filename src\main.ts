import {createApp} from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import globalComponents from '@/components';
import {CkeditorPlugin} from "@ckeditor/ckeditor5-vue";
import IconFont from '@/components/icon-font/index.vue'
import router from './router';
import store from './store';
import i18n from './locale';
import directive from './directive';
import './mock';
import App from './App.vue';


// Styles are imported via arco-plugin. See config/plugin/arcoStyleImport.ts in the directory for details
// 样式通过 arco-plugin 插件导入。详见目录文件 config/plugin/arcoStyleImport.ts
// https://arco.design/docs/designlab/use-theme-package
import '@arco-design/web-vue/dist/arco.css';
import '@/assets/style/global.less';
import '@/assets/style/variables.less';
import '@/assets/style/acroDesign.less';
import '@/assets/style/defaultLayout.less';
import 'ckeditor5/ckeditor5.css';

const app = createApp(App);

app.use(ArcoVue, {});
app.use(ArcoVueIcon);
app.use(CkeditorPlugin)
app.use(router);
app.use(store);
app.use(i18n);
app.use(globalComponents);
app.use(directive);

// 注册图标全局组件
app.component('IconFont', IconFont)

app.mount('#app');


