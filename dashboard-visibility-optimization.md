# Dashboard 可视逻辑处理优化

## 概述
参考 `appData/index.vue` 中的可视逻辑处理，为 `dashboard/index.vue` 添加了只刷新可见组件的功能，以优化性能和用户体验。

## 主要修改

### 1. 添加可视性跟踪
- 添加 `visibleMap` 用于跟踪组件是否可见
- 添加 `paramsChanged` 用于跟踪参数变化状态
- 添加 `lastQueryParam` 用于存储上一次的查询参数

### 2. 模板修改
- 在 `grid-item` 上添加 `v-viewport` 指令来监听组件可见性
- 在 `ReportCard` 和 `NoteCard` 组件上添加 `visibleMap[item.i]` 条件渲染

### 3. 刷新逻辑优化
- 修改 `refreshAllReports` 函数，只刷新可见的组件
- 添加 `refreshVisibleComponents` 函数专门处理可见组件的刷新
- 添加 `getComponentRef` 辅助函数获取组件引用

### 4. 看板切换优化
- 在 `fetchDashboard` 函数中重置 `visibleMap`，确保切换看板时清空可见性状态

### 5. 参数变化监听
- 添加 `watch` 监听 `visibleMap` 变化
- 当组件变为可见时，检查参数是否已变化，如有变化则刷新该组件

### 6. 时间变化处理
- 修改 `dateRangeChange` 函数，时间变化时只刷新可见组件

## 核心逻辑

### 可见性检测
使用 `v-viewport` 指令（基于 IntersectionObserver API）检测组件是否进入视口。

### 逐级刷新策略
1. **看板切换**: 获取卡片分布数据 → 重置可见性映射
2. **组件可见**: 检查参数变化 → 按需刷新单个组件
3. **参数变化**: 只刷新当前可见的组件

### 性能优化
- 避免不可见组件的无效渲染和数据请求
- 减少看板切换时的并发请求数量
- 提升大型看板的加载性能

## 与 appData 的差异
- **appData**: 面板切换 → 组件初始化 → 数据获取
- **dashboard**: 看板切换 → 卡片配置 → 逐个卡片数据请求

两者都实现了只刷新可见组件的逻辑，但数据获取的层级和时机不同。
