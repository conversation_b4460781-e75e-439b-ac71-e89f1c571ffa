import {getRequest, postRequest, uploadRequest} from '@/api/request';
import {AxiosPromise} from 'axios';

// 反馈保存响应数据结构
export interface FeedbackSaveResponseData {
  code: string; // 反馈编号
}

// 用户信息结构
export interface UserInfo {
  code: string;
  name: string;
  avatar: string;
  dingdingUnionId: string;
  email: string | null;
  mobile: string;
}

// 处理人信息结构
export interface HandlerInfo {
  code: string;
  name: string;
}

// 反馈回复结构
export interface FeedbackReply {
  id: number;
  code: string;
  feedbackCode: string;
  userCode: string;
  content: string;
  parentReplyCode: string | null;
  creator: UserInfo | null;
  updateUser: UserInfo | null;
  createTime: number;
  updateTime: number | null;
}

// 反馈列表项结构
export interface FeedbackListItem {
  id: number;
  code: string;           // 反馈编号
  objectCode: string;    // 对象编号
  versionCode: string;   // 版本编号
  typeCode: string;      // 类型编号
  statusCode: string;    // 状态编号
  submitterCode: string; // 提交人编号
  handlerCode: string;   // 处理人编号
  title: string;          // 标题
  description: string;    // 描述
  feedbackTime: number;  // 反馈时间
  lastProcessedTime: number; // 最后处理时间
  creator: UserInfo | null;    // 创建者信息
  handler: UserInfo | null; // 处理人信息
  updateUser: UserInfo | null; // 更新者信息
  createTime: number;    // 创建时间
  updateTime: number;    // 更新时间
  replies: FeedbackReply[]; // 回复列表
}

// 反馈详情响应数据结构
export interface FeedbackDetailResponseData extends FeedbackListItem {
  // 可以扩展更多详情字段
}

// 反馈列表响应数据结构
export interface FeedbackListResponseData {
  items: FeedbackListItem[];
  total: number;
  pages: number;
  current: number;
  pageSize: number;
}

// 反馈保存参数接口
export interface FeedbackSaveParams {
  code?: string;           // 反馈编号
  objectCode?: string;    // 对象编号
  versionCode?: string;   // 版本编号
  typeCode?: string;      // 类型编号
  statusCode?: string;    // 状态编号
  submitterCode?: string; // 提交人编号
  handlerCode?: string;   // 处理人编号
  title?: string;          // 标题
  description?: string;    // 描述
}

// 反馈列表请求参数接口
export interface FeedbackListParams {
  current?: number; // 当前页码
  pageSize?: number; // 每页数量
  statusCode?: string; // 状态编号
  startDate?: string; // 开始日期
  endDate?: string; // 结束日期
  objectCode?: string; // 对象编号
  versionCode?: string; // 版本编号
}

// 版本信息结构
export interface VersionInfo {
  versionCode: string;
  versionName: string;
}

// 反馈回复保存响应数据结构
export interface FeedbackReplySaveResponseData {
  code: string; // 回复编号
}

// 反馈回复保存参数接口
export interface FeedbackReplySaveParams {
  code?: string;              // 回复编号
  feedbackCode?: string;     // 反馈编号
  content?: string;           // 回复内容
  parentReplyCode?: string;  // 父回复编号
}

// 图片上传响应数据结构
export interface ImageUploadResponseData {
  url: string; // 图片URL
}

// 保存反馈
export function saveFeedback(data: FeedbackSaveParams): AxiosPromise<FeedbackSaveResponseData> {
  return postRequest<FeedbackSaveResponseData>('/api/op/feedbacks/save', data);
}

// 获取反馈列表
export function getFeedbackList(params: FeedbackListParams): AxiosPromise<FeedbackListResponseData> {
  return getRequest<FeedbackListResponseData>('/api/op/feedbacks/list', params);
}

// 获取反馈详情
export function getFeedbackDetail(code: string): AxiosPromise<FeedbackDetailResponseData> {
  return getRequest<FeedbackDetailResponseData>(`/api/op/feedbacks/info/${code}`);
}

// 保存反馈回复
export function saveFeedbackReply(data: FeedbackReplySaveParams): AxiosPromise<FeedbackReplySaveResponseData> {
  return postRequest<FeedbackReplySaveResponseData>('/api/op/feedbacks/reply/save', data);
}

// 上传图片
export function uploadImage(file: File): AxiosPromise<ImageUploadResponseData> {
  const formData = new FormData();
  formData.append('file', file);
  return uploadRequest<ImageUploadResponseData>('/api/op/feedbacks/images/upload', formData);
}

// 获取处理人列表
export function getHandlerList(): AxiosPromise<HandlerInfo[]> {
  return getRequest<HandlerInfo[]>('/api/op/feedbacks/handler/list', {});
}

// 获取版本列表
export function getVersionList(objectCode: string): AxiosPromise<VersionInfo[]> {
  return getRequest<VersionInfo[]>('/api/op/feedbacks/version/list', { objectCode });
}