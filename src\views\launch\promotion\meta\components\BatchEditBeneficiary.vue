<template>
    <!-- 批量编辑受益方或赞助方 -->
    <a-modal v-model:visible="modalVisible" :width="520" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert>对于任何定位欧盟地区受众的广告组，你需要指明广告组的受益人或组织，以及广告组的赞助方</a-alert>
        <a-form ref="formRef" :model="form" style="margin-top: 12px;">
            <a-form-item field="beneficiary" label="受益方">
                <a-select v-model="form.beneficiary" placeholder="请选择">
                    <a-option value="1">受益人</a-option>
                    <a-option value="2">赞助方</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="sponsor" label="赞助方">
                <a-select v-model="form.sponsor" placeholder="请选择">
                    <a-option value="1">受益人</a-option>
                    <a-option value="2">赞助方</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('编辑受益方或赞助方')
const form = reactive({
    beneficiary:'',
    sponsor:''
})
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>