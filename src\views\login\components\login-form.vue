<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">欢迎来到GrowthX</div>
    <a-tabs v-model:active-key="tabType" @change="tabChange">
      <a-tab-pane v-for="item in tabList" :key="item.type" :title="item.name">
      </a-tab-pane>
    </a-tabs>
    <!-- 钉钉登录 -->
    <a-spin v-if="tabType === 'code'" :loading="codeLoading" style="width: 100%;height: 100%;position: relative;">
      <!-- <div class="login-form-sub-title">{{ $t('login.form.title') }}</div> -->
      <div id="self_defined_element" class="self-defined-classname"></div>
      <div class="dd-tip">
        使用
        <span>钉钉</span>
        扫描二维码登录授权
      </div>
    </a-spin>
    <div v-if="errorMessage && tabType !== 'code'" class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form v-if="tabType !== 'code'" ref="loginForm" :model="userInfo" class="login-form" layout="vertical" @submit="handleSubmit">
      <!-- 密码登录 -->
      <a-form-item
          v-if="tabType === 'email'"
          field="email"
          :rules="[
            { required: true, message: $t('login.form.userName.errMsg') },
            {
              validator: (value, callback) => {
                if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
                  callback('请输入正确的邮箱格式');
                } else {
                  callback();
                }
              },
            }
          ]"
          :validate-trigger="['change', 'blur']"
          hide-label
      >
        <a-input v-model="userInfo.email" :placeholder="$t('login.form.userName.placeholder')" class="h40">
          <template #prefix>
            <icon-email/>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
          v-if="tabType === 'email'"
          field="password"
          :rules="[{ required: true, message: $t('login.form.password.errMsg') }]"
          :validate-trigger="['change', 'blur']"
          hide-label
      >
        <a-input-password v-model="userInfo.password" :placeholder="$t('login.form.password.placeholder')" allow-clear class="h40">
          <template #prefix>
            <icon-lock/>
          </template>
        </a-input-password>
      </a-form-item>
      <!-- 手机号登录 -->
      <a-form-item
          v-if="tabType === 'msg'"
          field="phoneNumber"
          :rules="[
            { required: true, message:'请输入手机号' },
            {
              validator: (value, callback) => {
                if (!/^1[3-9]\d{9}$/.test(value)) {
                  callback('请输入正确的手机号');
                } else {
                  callback();
                }
              },
            }]"
          :validate-trigger="['change', 'blur']"
          hide-label
      >
        <a-input v-model="userInfo.phoneNumber" placeholder="请输入手机号"  class="h40">
          <template #prefix>
            <icon-phone />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
          v-if="tabType === 'msg'"
          field="messageAuthCode"
          :rules="[{ required: true, message:'请输入验证码' }]"
          :validate-trigger="['change', 'blur']"
          hide-label
      >
        <a-input v-model="userInfo.messageAuthCode" placeholder="请输入验证码" :max-length="6"  class="h40" @input="(value) => userInfo.messageAuthCode = value.replace(/\D/g, '')">
          <template #prefix>
            <icon-message />
          </template>
        </a-input>
        <a-button type="primary" :disabled="isSendingCode || countDown>0" style="margin-left: 6px;" class="h40" @click="sendCode">
          {{ countDown > 0 ? `重新发送(${countDown})` : '发送验证码' }}
        </a-button>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div v-if="tabType === 'email'" class="login-form-password-actions">
          <a-checkbox
              checked="rememberPassword"
              :model-value="loginConfig.rememberPassword"
              @change="setRememberPassword as any">
            {{ $t('login.form.rememberPassword') }}
          </a-checkbox>
        </div>
        <div class="protocol">
          登录视为您已阅读并同意GrowthX
          <a href="http://" target="_blank" rel="noopener noreferrer">服务条款</a>
          和
          <a href="http://" target="_blank" rel="noopener noreferrer">隐私政策</a>
        </div>
        <a-button type="primary" html-type="submit" long :loading="loading" class="login-btn">
          {{ $t('login.form.login') }}
        </a-button>
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import {computed, nextTick, onBeforeUnmount, onMounted, reactive, ref} from 'vue';
import {useRouter} from 'vue-router';
import {Message} from '@arco-design/web-vue';
import {ValidatedError} from '@arco-design/web-vue/es/form/interface';
import {useI18n} from 'vue-i18n';
import {useStorage} from '@vueuse/core';
import {useUserStore} from '@/store';
import useLoading from '@/hooks/loading';
import type {LoginData} from '@/api/authorize/api';
import {dingtalkLogin, getMsgCode} from '@/api/authorize/api';
import {getAppPageList} from "@/api/marketing/api";

const router = useRouter();
const {t} = useI18n();
const errorMessage = ref('');
const {loading, setLoading} = useLoading();
const userStore = useUserStore();

const loginConfig = useStorage('login-config', {
  rememberPassword: true,
  email: '', // 演示默认值
  password: '', // demo default value
});
const userInfo = reactive({
  email: loginConfig.value.email,
  password: loginConfig.value.password,
  phoneNumber:'',
  messageAuthCode:''
});

const isParfka = ref(false)
const codeLoading = ref(false)

const tabType = ref('email')
const baseTabList = [
  {
    type: 'email',
    name: '邮箱登录'
  },
  {
    type: 'msg',
    name: '手机号登录'
  }
]

const tabList = computed(() => {
  if (!isParfka.value) return baseTabList
  return [
    {
      type: 'code',
      name: '钉钉登录'
    },
    ...baseTabList
  ]
})
const isSendingCode = ref(false)
const countDown = ref(0)
const loginForm = ref()
const getDeaultAppList = async () => {
  const pageParams = {
    current: 1,
    pageSize: 10,
    text: '',
    appStatus: undefined,
    osName: undefined
  }
  try {
    const res = await getAppPageList(pageParams);
    const defaultItem = res?.items?.[0];
    localStorage.setItem('app-id', defaultItem.code);
    localStorage.setItem('app-data', JSON.stringify(defaultItem));
    // localStorage.setItem('app-id-list', JSON.stringify([defaultItem.code]));
    // localStorage.setItem('app-data-list', JSON.stringify([defaultItem]));
  } catch (error) {
    console.log(error);
  }
}
const sendCode = () => {
  loginForm.value.validateField('phoneNumber',(async (valid: any) => {
      if (!valid) {
        // 
        await getMsgCode(userInfo.phoneNumber).then(res => {
          // userInfo.messageAuthCode = res
          Message.success('发送成功，注意查收')
        })
        isSendingCode.value = true
        countDown.value = 60
        const countdownInterval = setInterval(() => {
          countDown.value -= 1;
          if (countDown.value <= 0) {
            clearInterval(countdownInterval);
            isSendingCode.value = false;
          }
        }, 1000);
      }
    })
  )
}
const handleSubmit = async ({ errors, values,}: {
  errors: Record<string, ValidatedError> | undefined;
  values: Record<string, any>;
}) => {
  if (loading.value) return;
  if (!errors) {
    setLoading(true);
    try {
      if(tabType.value === 'email'){
        await userStore.login(values as LoginData);
        const {rememberPassword} = loginConfig.value;
        const {email, password} = values;
        // 实际生产环境需要进行加密存储。
        // The actual production environment requires encrypted storage.
        loginConfig.value.email = rememberPassword ? email : '';
        loginConfig.value.password = rememberPassword ? password : '';
      }else{
        const data = {
          loginMethod:'phone',
          phoneNumber:values.phoneNumber,
          messageAuthCode:values.messageAuthCode
        }
        await userStore.phoneLogin(data);
      }
      await getDeaultAppList()
      // 初始化数据
      await userStore.refreshAppData()
      userStore.initDataSource();
      const {redirect, ...othersQuery} = router.currentRoute.value.query;
      const routePath = redirect && redirect !== "/" ? (redirect as string) : "/"; // 默认跳转到根路径
      router.push({
        path: routePath,
        query: {
          ...othersQuery,
        },
      });
      Message.success(t('login.form.login.success'));
    } catch (err) {
      errorMessage.value = (err as Error).message;
    } finally {
      setLoading(false);
    }
  }
};

const setRememberPassword = (value: boolean) => {
  loginConfig.value.rememberPassword = value;
};

// 钉钉二维码登录

// 防止二次登录重复请求接口
let isLoggingIn = false; // 添加登录状态标记
let debouncedDingtalkLogin: ((authCode: any) => Promise<void>) | null = async (authCode) => {
  if (isLoggingIn) return; // 如果正在登录中，直接返回
  isLoggingIn = true;
  codeLoading.value = true;
  try {
    const res = await dingtalkLogin(authCode);
    if (res) {
      userStore.ddLogin(res);
      await getDeaultAppList()
      // 初始化数据
      await userStore.refreshAppData()
      userStore.initDataSource();
      const {redirect, ...othersQuery} = router.currentRoute.value.query;
      const routePath = redirect && redirect !== "/" ? (redirect as string) : "/";
      router.push({
        path: routePath,
        query: {
          ...othersQuery,
        },
      });
      debouncedDingtalkLogin = null;
      Message.success(t('login.form.login.success'));
    }
  } catch (err) {
    errorMessage.value = (err as Error).message;
    debouncedDingtalkLogin = null; // 发生错误时也需要清空函数引用
  } finally {
    codeLoading.value = false;
    isLoggingIn = false;
  }
}
const initCode = () => {
  tabType.value = 'code'
  nextTick(() => {
    if (isParfka.value) {
      (window as any).DTFrameLogin(
          {
            id: 'self_defined_element',
            width: '100%',
            height: 300,
          },
          {
            redirect_uri: encodeURIComponent('https://data.ivymobile.cn'), // 重定向地址
            client_id: 'dingjfkviy9rfqjwzly3', // client_id在创建的应用基本信息->凭证与基础信息中可以找到
            scope: 'openid', // 固定值
            response_type: 'code', // 固定值
            // state: 'xxxxxxxxx', // 这里的state可以不传，如果传了会在loginResult中拿到一样的值
            prompt: 'consent', // 固定值
          },
          (loginResult: any) => {
            console.log('登录成功--->', loginResult)
            const {redirectUrl, authCode, state} = loginResult
            if (debouncedDingtalkLogin) {
              debouncedDingtalkLogin(authCode);
            }
          },
          (errorMsg: any) => {
            console.error(`errorMsg of errorCbk: ${errorMsg}`)
          },
      )
    }
  })
}

const tabChange = (type) => {
  tabType.value = type
  errorMessage.value = ''
  if(type === 'code'){
    initCode()
  }
}
onMounted(() => {
  const currentUrl = window.location.href;
  // isParfka.value = currentUrl.includes('http://localhost:5173/');
  isParfka.value = currentUrl.includes('data.ivymobile.cn');
  if(isParfka.value){
    initCode()
  }
})

onBeforeUnmount(() => {
  debouncedDingtalkLogin = null;
  isLoggingIn = false; // 组件卸载时重置状态
});
</script>

<style lang="less" scoped>
.login-form {
 width: 100%;
 height: 100%;
  &-wrapper {
    width: 100%;
  }

  &-title {
    color: #0c0d0e;
    font-size: 24px;
    font-weight: 500;
    letter-spacing: .003em;
    line-height: 32px;
    margin-bottom: 32px;
  }

  &-sub-title {
    color: var(--color-text-3);
    font-size: 16px;
    line-height: 24px;
  }

  &-error-msg {
    height: 32px;
    color: rgb(var(--red-6));
    line-height: 32px;
  }

  &-password-actions {
    display: flex;
    justify-content: space-between;
  }

  &-register-btn {
    color: var(--color-text-3) !important;
  }
}
.login-btn{
  height: 40px;
  border-radius: 4px;
}
.h40{
  height: 40px;
}
:deep(.arco-tabs-nav::before){
  height: 0;
}
:deep(.arco-tabs-tab-title) {
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
}
:deep(.arco-tabs-tab:hover .arco-tabs-tab-title::before){
  background: transparent;
}
:deep(.arco-tabs-tab:first-child) {
  margin-left: 0;
}
.self-defined-classname {
  width: 100%;
  height: 300px;
}
.center{
  text-align: center;
}
.login-box-tabs{
  width: 100%;
  .login-box-tabs-items{
    font-weight: 600;
    color: #999aaa;
    height: 32px;
    box-sizing: content-box;
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
    position: relative;
    span{
      width: 120px;
      display: inline-block;
      font-size: 16px;
      height: 22px;
      line-height: 16px;
      position: relative;
      cursor: pointer;
      user-select: none;
      text-align: center;
    }
  }
  .active-tab{
    color: #222226;
    &::after{
      content: "";
      display: block;
      width: 24px;
      height: 2px;
      background: #222226;
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.protocol {
    color: #41464f;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 4px;
    a{
      color: rgb(var(--primary-6));
      cursor: pointer !important;
      text-decoration: none;
    }
}
.dd-tip{
    position: absolute;
    color: #41464f;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 4px;
    left: 50%;
    bottom: 10px;
    transform: translateX(-50%);
    text-align: center;
    span{
      color: rgb(var(--primary-6));
      padding: 0 2px;
    }
}
</style>
