<template>
  <div class="page-container">
    <a-page-header class="header" :title="'用户详情 - ' + userCode" :show-back="false">
      <template #extra>
        <a-space>
          <selectEventList :event-list="params.events" @events-change="eventsChange" />
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick" />
          <a-button @click="back">返回</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="content">
      <DetailInfo ref="infoRef" :query-params="params" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { onMounted, ref, reactive } from 'vue';
  import DatePicker from '@/components/date-picker/index.vue';
  import selectEventList from '@/components/selected-event-list/index.vue';
  import DetailInfo from '@/views/data/user/userReview/DetailInfo.vue';
  import { isEqual } from 'lodash';

  const router = useRouter();
  const route = useRoute();

  const params = reactive({
    date: {
      recentStartDate: 14,
      recentEndDate: 1,
      dateText: '过去14天',
    },
    events: [],
  });
  // 添加用于存储上一次复选参数值的响应式变量
  const lastParams = ref<{
    events: string[];
  }>({
    events: [],
  });
  /**
   * 用户编码
   */
  const userCode = ref<string>(route.query.userCode);

  function back() {
    router.back();
  }
  const infoRef = ref();
  const datePick = (date: any) => {
    params.date = date;
    infoRef.value.refreshEventList();
  };
  const eventsChange = (events: any) => {
    params.events = events;
    const hasParamsChanged = !isEqual({ events: params.events }, { events: lastParams.value.events });
    if (hasParamsChanged) {
      lastParams.value = {
        events: [...params.events],
      };
      infoRef.value.refreshEventList();
    }
  };
  onMounted(() => {
    infoRef.value?.init(userCode.value);
  });
</script>

<style scoped lang="less">
  @import '../table.less';

  .page-container {
    height: 100vh;
    width: 100%;

    .header {
      background: var(--color-bg-2);
      margin-bottom: 24px;
    }

    .content {
      padding: 24px;
      background: var(--color-bg-2);
      height: calc(100% - 88px);
    }
  }
</style>
