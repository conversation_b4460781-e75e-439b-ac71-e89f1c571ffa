<template>
  <div class="chart-content">
    <Chart :option="chartOption"/>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import Chart from "@/components/chart/index.vue";
import { cloneDeep } from 'lodash';

interface Props {
  chartData?: any[];
}

const props = defineProps<Props>();

const chartOption = ref({});


// 定义颜色数组，并提供索引扩展函数
const defaultColors = [
	'#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
	'#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf',
	'#4E79A7', '#F28E2B', '#E15759', '#76B7B2', '#59A14F',
	'#EDC948', '#B07AA1', '#FF9DA7', '#9C755F', '#BAB0AC',
	'#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3',
	'#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd',
	'#ccebc5', '#ffed6f', '#a6cee3', '#1f78b4', '#b2df8a',
	'#33a02c', '#fb9a99', '#e31a1c', '#fdbf6f', '#ff7f00',
	'#cab2d6', '#6a3d9a', '#ffff99', '#b15928'
];
const getColor = (i: number) => {
	if (i < defaultColors.length) return defaultColors[i];
	const hue = (i * 137.508) % 360;
	return `hsl(${hue}, 70%, 55%)`;
};

// 转为 10 个饼图（2x5 小 multiples）
const processChartData = () => {
	if (!props.chartData?.length) {
		chartOption.value = {
			series: [],
			legend: { data: [] },
			tooltip: {}
		};
		return;
	}
	const processedData = cloneDeep(props.chartData).slice(0, 10);
	// 图例与颜色映射（按所有子 source 统一配色）
	const allSources = new Set<string>();
	processedData.forEach((item: any) => {
		// 获取前10条数据，并添加"其他"分类
		const top10Items = (item.items || []).slice(0, 10);
		top10Items.forEach((sub: any) => allSources.add(sub.source));
		allSources.add('其他'); // 添加"其他"分类到图例
	});
	const legendData = Array.from(allSources);
	const sourceColorMap: Record<string, string> = {};
	legendData.forEach((source, idx) => {
		sourceColorMap[source] = getColor(idx);
	});
	// 自适应布局：≤5 个单行，多于 5 个两行（最多 10 个）
	const count = processedData.length;
	const maxCols = 5;
	const cols = Math.min(count, maxCols);
	const rows = Math.ceil(count / cols);
	const isSingleRow = rows === 1;
	// 行数不同，半径与标题偏移自适应
	const radius = isSingleRow ? ['26%', '36%'] : ['20%', '30%'];
	const pieSeries = processedData.map((item: any, i: number) => {
		const r = Math.floor(i / cols);
		const c = i % cols;
		const cx = ((c + 0.5) * 100) / cols;
		const cy = ((r + 0.5) * 100) / rows;
		// 调整Y轴位置，为标题留出空间
		const centerY = `${(isSingleRow ? (r + 0.5) * 100 / rows + 3 : (r + 0.5) * 100 / rows)}%`;
		const centerX = `${cx}%`;
		// 处理数据：前10条保持原样，剩余数据合并为"其他"
		const allItems = item.items || [];
		const top10Items = allItems.slice(0, 10);
		const remainingItems = allItems.slice(10);
		// 计算剩余数据的总和
		const remainingTotal = remainingItems.reduce((sum: number, sub: any) => sum + (sub.users || 0), 0);
		// 构建饼图数据
		const pieData = [
			...top10Items.map((sub: any) => ({
				name: sub.source,
				value: sub.users || 0,
				itemStyle: { color: sourceColorMap[sub.source] },
				// 添加图例名称，确保与 legend.data 匹配
				legendName: sub.source
			}))
		];
		// 如果有剩余数据，添加"其他"分类
		if (remainingTotal > 0) {
			pieData.push({
				name: '其他',
				value: remainingTotal,
				itemStyle: { color: '#666' },
				legendName: '其他'
			});
		}
		return [
			{
				name: item.source,
				type: 'pie',
				radius,
				center: [centerX, centerY],
				data: pieData,
				label: { show: false },
				labelLine: { show: false },
				tooltip: {
					trigger: 'item',
					formatter: (params: any) => {
						// 计算总用户数（包括"其他"分类）
						const total = pieData.reduce((s: number, x: any) => s + (x.value || 0), 0);
						const pct = total ? ((params.value / total) * 100).toFixed(2) : '0.00';
						const absoluteRate = total ? ((params.value / item.absoluteUsers) * 100).toFixed(2) : '0.00';
						// 正常数据项的处理
						return `${params.marker}${params.name}<br/>用户数：${(params.value || 0).toLocaleString()}<br/>相对占比：${pct}%<br/>绝对占比：${absoluteRate}%`;
					}
				}
			},
			// 添加标题文本
			{
				type: 'pie',
				silent: true,
				radius: [0, 0],
				center: [centerX, `${parseFloat(centerY) - (isSingleRow ? 24 : 20)}%`],
				label: {
					show: true,
					position: 'center',
					fontSize: isSingleRow ? 14 : 12,
					fontWeight: 'bold',
					align: 'center',
					formatter: () => {
						// 标题包含用户数
						const title = item.source;
						const users = item.absoluteUsers || 0;
						const displayTitle = title.length > 15 ? `${title.substring(0, 15)}...` : title;
						return `{title|${displayTitle}}\n{users|用户数: ${users.toLocaleString()}}`;
					},
					rich: {
						title: {
							fontSize: isSingleRow ? 14 : 12,
							fontWeight: 'bold',
							lineHeight: 14
						},
						users: {
							fontSize: isSingleRow ? 10 : 8,
							color: '#666',
							lineHeight: 12
						}
					}
				},
				data: [{ value: 1, itemStyle: { color: 'transparent' } }],
				tooltip: { show: false }
			}
		];
	});

	chartOption.value = {
		color: legendData.map((source, idx) => sourceColorMap[source]), // 确保颜色顺序与图例一致
		tooltip: { trigger: 'item', appendToBody: true, confine: true },
		legend: { 
			data: legendData, 
			bottom: 0, 
			type: 'scroll', 
			itemWidth: 12, 
			itemHeight: 8,
			textStyle: {
				color: '#333'
			}
		},
		series: pieSeries.flat()
	};
};

// 监听分页数据变化
watch([ () => props.chartData], () => {
  processChartData();
}, { immediate: true, deep: true });

</script>

<style lang="less" scoped>
.chart-content {
  width: 100%;
  height: 100%;
}
</style>

