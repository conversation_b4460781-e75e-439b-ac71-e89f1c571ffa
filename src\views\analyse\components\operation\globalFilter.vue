<template>
  <!-- 全局筛选 -->
  <div class="guide">
    <div v-if="!props.applyCreateEvent" class="stickyBar" :style="!eventQueryfilter.filters.length ? { color: 'var(--tant-text-gray-color-text1-4)' } : {}">
      <div  class="modal">
        <svg style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
          <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 13V3l6 6h-4v4h-2z"></path>
            <path d="M9 3v18l-6-6h4V3h2z"></path>
            <path d="M21 17v-2h-7v2h7z"></path>
            <path d="M21 21v-2h-7v2h7z"></path>
          </svg>
        </svg>
        <span class="title">{{ props.componentName }}</span>
        <div class="model-btn">
          <a-button @click="add">
            <template #icon>
              <icon-plus class="nav-icon"/>
            </template>
          </a-button>
        </div>
      </div>
    </div>
    <eventQueryFilter ref="queryFilterRef" :filter="props.filter" :show-detail-filter="true" :exclude-event="props.excludeEvent" @query-filters-change="queryFiltersChange"/>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useEventBus} from '@vueuse/core';
import eventQueryFilter from "./eventQueryFilter.vue"

const eventBus = useEventBus('eventList');
const attrBus = useEventBus('attrList');
const emits = defineEmits(['filtersChange'])
const props = defineProps({
  componentName: {
    type: String,
    default: '全局筛选'
  },
  excludeEvent: {
    type: Boolean,
    default: false
  },
  applyCreateEvent:{
    type: Boolean,
    default: false
  },
  // 回显传参
  filter:{
    type:Object,
    default:() => {}
  }
})
const eventQueryfilter = ref(
  {
    logicalOperation: '',
    filters: [],
  }
)

onMounted(() => {
})
// 
const queryFilterRef = ref()
const add = () => {
  queryFilterRef.value.add()
}
const queryFiltersChange = (v) => {
  eventQueryfilter.value = v
  emits('filtersChange', eventQueryfilter.value)
}
defineExpose({
  add,
})
</script>

<style scoped lang="less">
.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all .3s;
  box-sizing: border-box;
  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }
  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}
.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);
  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);
    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;
      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }
      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {
    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }
        .action-right {
          opacity: 1;
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>