<script setup lang="ts">
import router from "@/router";
import {ref, watchEffect} from "vue";
import {useRoute} from "vue-router";
import {useI18n} from "vue-i18n";
import useMenuTree from "@/components/top-menu/use-menu-tree";

const props = defineProps({
    collapsed: Boolean
})
const emit = defineEmits(['updateCollapsed'])
const selectedKeys = ref([])
const openKeys = ref([])
const route = useRoute();
const {menuTree} = useMenuTree();
const {t} = useI18n();
const menuData = ref<any>([]);

const updateCollapsed = () => {
    emit('updateCollapsed', !props.collapsed)
}

const gotoItem = (item: string) => {
    router.push({
        name: item,
    });
};

watchEffect(() => {
    const menu = menuTree.value?.find(topMenu => topMenu.name === route.matched[0].name)?.children?.filter(item => !item?.meta?.hideInMenu).map(item => {
        return {
            name: t(item.meta?.locale || ''),
            route: item.name,
            icon: item.meta?.icon,
            children: item.children?.filter(child => !child?.meta?.hideInMenu)?.map(child => {
                return {
                    name: t(child.meta?.locale || ''),
                    route: child.name,
                }
            })
        }
    });
    menuData.value = menu;
    openKeys.value = menu?.filter(item => item.children.map(child => child.route).includes(route.name)).map(item => item.route)
    selectedKeys.value = [route.name];
});

</script>


<template>
    <div class="sider">
        <div :class="props.collapsed?'header header-collapsed':'header'">
            <span v-if="!props.collapsed">{{ t(route.matched[0].meta.locale || '') }}</span>
            <button type="button" class="header-button" @click="updateCollapsed">
                <img v-if="props.collapsed" class="img" src="/icon/double-right.svg"/>
                <img v-else class="img" src="/icon/double-left.svg"/>
            </button>
        </div>
        <div v-show="!props.collapsed" class="full-menu">
            <a-menu
                v-model:selected-keys="selectedKeys"
                v-model:open-keys="openKeys"
                accordion
                :collapse="true"
                @menu-item-click="gotoItem"
            >
                <a-menu-item v-for="menuItem in menuData" :key="menuItem.route" style="padding-bottom: 10px;">
                    <template #icon>
                        <img style="width: 22px;height: 22px;" :src="menuItem.icon" alt="">
                    </template>
                    <span class="menu-title">
                          {{ menuItem.name }}
                      </span>
                </a-menu-item>
            </a-menu>
        </div>
        <div v-show="props.collapsed" class="collapsed-menu">
            <a-tooltip v-for="menuItem in menuData" :key="menuItem.route" :content="menuItem.name" position="right">
                <div class="open-icon" @click="()=>{ updateCollapsed();  gotoItem(menuItem.route); }">
                    <img style="width: 22px;height: 22px;" :src="menuItem.icon" alt="">
                </div>
            </a-tooltip>
        </div>
    </div>
</template>

<style scoped lang="less">
:deep(.arco-menu-vertical .arco-menu-inner) {
    padding: 0;
    max-width: 210px;
}

:deep(.arco-menu-light .arco-menu-inline-header:hover) {
    background-color: var(--tant-bg-white-color-bg1-1);
}

.sider {
    height: calc(100% - 64px);

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 18px 24px 14px;

        .header-button {
            border: none;
            color: var(--tant-text-gray-color-text1-2);
            background-color: transparent;
            cursor: pointer;
            transition: .3s;
            padding: 7px;

            .img {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .header-button:hover {
            background-color: var(--tant-secondary-color-secondary-transp-hover);
            text-decoration: none;
            border-radius: 4px;
        }
    }

    .header-collapsed {
        justify-content: center;
        margin: 18px 0 22px;
    }


    .header > span:first-child {
        overflow: hidden;
        font: var(--tant-header-font-header4-medium);
        font-size: var(--font-size-title-default);
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .full-menu {
        flex: 1 1;
        height: 100%;
        margin-left: 12px;
        overflow-y: auto;

        .menu-title {
            color: var(--tant-text-gray-color-text1-2);
            font: var(--tant-body-font-body-medium);
            font-size: var(--font-size-body-default);
        }

        .menu-item {
            padding-left: 17px;
            color: var(--tant-text-gray-color-text1-1);
        }
    }


    .collapsed-menu {
        overflow-y: auto;
        overflow-x: hidden;
        padding: 0 !important;
        background-color: var(--tant-bg-white-color-bg1-1);
        transition-property: width;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .open-button {
            border: none;
            background-color: transparent;
            width: 34px;
            height: 34px;
            margin-bottom: 24px;
            padding: 8px;
            color: var(--tant-text-gray-color-text1-3);
            line-height: 18px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
        }

        .open-button:hover {
            background-color: var(--tant-secondary-color-secondary-transp-hover);
            text-decoration: none;
            border-radius: 4px;
        }

        .open-icon {
            border: none;
            background-color: transparent;
            width: 34px;
            height: 34px;
            margin-bottom: 24px;
            padding: 8px;
            color: var(--tant-text-gray-color-text1-3);
            line-height: 18px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
        }
    }
}
</style>
