<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <DateRangePicker v-model="dateTime" />
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createAd">创建广告</a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-tabs v-model:active-key="activeName" @change="handleTabChange">
        <a-tab-pane key="account" title="广告账户">
          <Account ref="accountRef" @change-tabs="changeTabs" />
        </a-tab-pane>
        <a-tab-pane key="campaign" title="广告系列">
          <Campaign ref="campaignRef" @change-tabs="changeTabs" />
        </a-tab-pane>
        <a-tab-pane key="adset" title="广告组">
          <Adset ref="adsetRef" @change-tabs="changeTabs" />
        </a-tab-pane>
        <a-tab-pane key="ad" title="广告">
          <Ad ref="adRef" />
        </a-tab-pane>
        <a-tab-pane key="material" title="素材">
          <Material />
        </a-tab-pane>
      </a-tabs>
    </div>
    <CreateAd ref="createAdRef" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, provide } from 'vue';
  import dayjs from 'dayjs';
  import DateRangePicker from '@/components/date-quick-picker/index.vue';
  import CreateAd from '@/views/launch/promotion/components/CreateAd.vue';
  import { useRoute } from 'vue-router';
  import Account from './pages/account.vue';
  import Campaign from './pages/campaign.vue';
  import Adset from './pages/adset.vue';
  import Ad from './pages/ad.vue';
  import Material from './pages/material/index.vue';

  const route = useRoute();
  const today = dayjs();
  const dateTime = ref<Date[]>([today.toDate(), today.toDate()]);
  const activeName = ref('account');
  provide('dateTime', dateTime);

  const adsetRef = ref();
  const accountRef = ref();
  const campaignRef = ref();
  const materialRef = ref();
  const adRef = ref();

  // 创建refs映射对象
  const refsMap = {
    account: accountRef,
    campaign: campaignRef,
    adset: adsetRef,
    material: materialRef,
    ad: adRef,
  };
  // 处理tab切换
  const handleTabChange = (key) => {
    const targetRef = refsMap[key];
    targetRef.value?.init();
  };

  const changeTabs = (val) => {
    activeName.value = val.tab;
    // 获取对应的ref
    const targetRef = refsMap[val.tab];
    // 如果ref存在且有refresh方法，则调用
    if (targetRef?.value?.init) {
      targetRef.value.init(val);
    }
  };
  const createAdRef = ref();
  const createAd = () => {
    createAdRef.value.openModal();
  };
</script>

<style scoped lang="less"></style>
