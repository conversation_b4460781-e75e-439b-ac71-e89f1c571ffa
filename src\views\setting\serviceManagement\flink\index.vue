

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
            <a-select placeholder="地域">
                <!-- <a-option>Beijing</a-option>
                <a-option>Shanghai</a-option>
                <a-option>Guangzhou</a-option> -->
            </a-select>
        </div>
        <div class="filter-item">
            <a-select placeholder="可用区域">
                <!-- <a-option>Beijing</a-option>
                <a-option>Shanghai</a-option>
                <a-option>Guangzhou</a-option> -->
            </a-select>
        </div>
        <div class="filter-item">
          <a-input v-model="searchValue" class="title-input" placeholder="搜索作业">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-button class="button-background" @click="refresh">
            <template #icon>
              <icon-refresh class="nav-icon"/>
            </template>
          </a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            <template #icon>
              <icon-plus/>
            </template>
            创建作业
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :loading="loading"
          :data="filteredTableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :scrollbar="scrollbar"
          :pagination="pagination"
          @page-change="pageChange"
          @page-size-change="pageSizeChange"
      >
      <template #jobType="{record}">
        <span>{{ formatJobType(record.jobType) }}</span>
      </template>
      <template #status="{record}">
        <a-tag :color="getStatusColor(record.status)">{{ formatStatus(record.status) }}</a-tag>
      </template>
        <template #action="{record}">
          <div style="display:flex;align-items: center;gap:10px">
            <a-tooltip content="编辑">
              <div class="setting" @click="editIndex(record)">
                <icon-edit size="16px"/>
              </div>
            </a-tooltip>
            <a-tooltip content="删除">
                <a-popconfirm :content="`确认删除【${record?.name }】？ 该操作不可恢复。`" type="error">
                    <div class="delete">
                        <icon-delete size="16px"/>
                    </div>
                </a-popconfirm>
            </a-tooltip>
          </div>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ filteredTableData.length }}条记录
          </div>
        </template>

      </a-table>
    </div>
  </div>

</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import {useRoute} from 'vue-router';
import {getOceanusList} from "@/api/setting/api"

const route = useRoute();
// 模拟table数据
const loading = ref(false)

const tableData = ref<any>([])

const init = () => {
  loading.value = true
  getOceanusList().then(res => {
    if (res) {
      tableData.value = res
    }
    loading.value = false
  })
}
init()
const refresh = () => {
  init()
}
const pageOptions = [
  10, 20, 50, 100
]
const createIndex = () => {
//   handleRefs.value.openModal()
}
const editIndex = (record) => {
//   handleRefs.value.openModal(record)
}
// input搜索
const searchValue = ref('');

// 图标改变
const scrollbar = ref(true);

const filteredTableData = computed(() => {
  const str = searchValue.value.trim();
  return tableData.value.filter(item => item?.name.includes(str));
});
const columns = ref<any>([
  {
    title: '作业名称',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '关联的项目组',
    dataIndex: 'teamCode',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '作业类型',
    dataIndex: 'jobType',
    ellipsis: true,
    slotName:'jobType',
    minWidth: 100,
  },
  {
    title: '作业状态',
    dataIndex: 'status',
    ellipsis: true,
    slotName:'status',
    minWidth: 100,
  },
  {
    title: '作业创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    minWidth: 180,
  },
  {
    title: '作业启动时间',
    dataIndex: 'startTime',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    minWidth: 180,
  },
  {
    title: '作业更新时间',
    dataIndex: 'updateTime',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    minWidth: 180,
  },
  {
    title: '备注信息',
    dataIndex: 'remark',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '最新配置版本号',
    dataIndex: 'latestJobConfigVersion',
    ellipsis: true,
    minWidth: 180,
  },
  {
    title: '运行的CPU数量',
    dataIndex: 'runningCpu',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    minWidth: 120,
  },
  {
    title: '作业内存规格',
    dataIndex: 'runningMem',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    minWidth: 120,
  },
  {
    title: '作业管理WEB UI入口',
    dataIndex: 'webUiUrl',
    ellipsis: true,
    tooltip: true,
    minWidth: 300,
  },
  {
    title: '作业运行的Flink版本',
    dataIndex: 'flinkVersion',
    ellipsis: true,
    minWidth: 300,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    slotName: 'action',
  }
])
const formatJobType = (type: number | null) => {
  if (!type) return '-';
  const typeMap = {
    1: 'SQL作业',
    2: 'JAR作业'
  };
  return typeMap[type] || '-';
};
// 格式化状态
const formatStatus = (status: number | null) => {
  if (!status) return '-';
  const statusMap = {
    1: '未初始化',
    2: '未发布',
    3: '操作中',
    4: '运行中',
    5: '停止',
    6: '暂停',
    '-1': '故障'
  };
  return statusMap[status] || '-';
};

// 获取状态对应的颜色
const getStatusColor = (status: number | null) => {
  if (!status) return 'gray';
  const colorMap = {
    1: 'blue',    // 未初始化
    2: 'orange',  // 未发布
    3: 'purple',  // 操作中
    4: 'green',   // 运行中
    5: 'gray',    // 停止
    6: 'gold',  // 暂停
    '-1': 'red'   // 故障
  };
  return colorMap[status] || 'gray';
};
// Table页面滑动
const scroll = {
  y: 'calc(100vh - 186px)'
};
const totalPages = computed(() => {
  return filteredTableData.value.length;
});
const pageNumber = ref(1)

const pagination = reactive({
  total: totalPages.value,  // 使用过滤后的数据长度
  PageSize: pageOptions[0],
  showPageSize: true,
  pageSizeOptions: pageOptions,
  showJumper: true,
  hideOnSinglePage: false,
  autoAdjust: true,
});
const pageChange = (v: number) => {
  pageNumber.value = v
}
const pageSizeChange = (v: number) => {
  pagination.PageSize = v
}
watch(totalPages, (newTotal) => {
  pagination.total = newTotal;
});

</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}



:deep(.arco-table-tr) {
  height: 66px;
}

.pagination-left {
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}


:deep(.arco-pagination-jumper > span) {
  color: var(--tant-text-gray-color-text1-1);
}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}
.button-background {
  background-color: #ffffff;
  padding: 8px;
  border-radius: 4px;
  color: var(--tant-text-gray-color-text1-2);
}

.button-background:hover {
  color: #BDBEBF;
  background-color: #fff;
}
</style>