
<template>
    <!-- Excel批量屏蔽子渠道 -->
    <a-modal v-model:visible="modalVisible" :width="625" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="form-content">
            <div class="form-item">
                <div class="index-title">
                    <span class="index">1</span>
                    <span>下载文件，支持导出前1万行</span>
                </div>
                <div class="word">
                    1.空白文件只包含表头信息，需要手动填入待编辑的广告ID <br>
                    2.数据文件会导出当前列表的数据，目前仅支持导出前50,000行，如果超出限制，请增加筛选条件后再下载
                </div>
                <div class="btn">
                    <a-button style="margin-right: 12px;">空白文件</a-button>
                    <a-button v-if="handleType === 'subChannel'">数据文件</a-button>
                </div>
                <div class="word">请注意，单次上传的数据条数不能超过50000条，模版说明如下：</div>
                <a-table
                    :columns="columns"
                    :data="tableData"
                    :hoverable="true"
                    :pagination="false"
                    size="small"
                    style="margin-left: 25px;"
                >
                </a-table>
            </div>
            <div class="form-item">
                <div class="index-title">
                    <span class="index">2</span>
                    <span> 请对第一步下载的文件进行编辑后再上传</span>
                </div>
                <div class="btn">
                    <a-upload draggable action="/" accept=".xlsx,.xls" :limit="1"/>
                </div>
            </div>
            <div class="tip">提示：点击确定后，可以在任务列表中查看执行结果</div>
        </div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('Excel屏蔽子渠道')


const emits = defineEmits(['updateData']);
const handleType = ref('')

const columns = reactive([
    {
        title: '模板列',
        dataIndex: 'name',
        minWidth: 180,
    },
    {
        title: '列值说明',
        dataIndex: 'describe',
    },
])
const tableData = reactive([
    {
        name:'Offer Name',
        describe:'用于录入需要更新出价的广告单元名称',
    },
    {
        name:'operation',
        describe:'1、可选操作包括：exclude, include2、exclude，表示屏蔽子渠道3、include，表示将子渠道从广告单元的流量黑名单中移除',
    },
    {
        name:'sub source',
        describe:'1、目标子渠道的ID，eg.mtg12345678902、需要将当次针对某广告单元屏蔽的应用ID写在同一行，多个应用ID用英文逗号分隔',
    },
])
const openModal = async (type:string) => {
    handleType.value = type
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.form-content{
    max-height: 740px;
    overflow-y: auto;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.form-item{
    position: relative;
    line-height: 32px;
    font-size: 12px;
    margin-bottom: 24px;
    .index{
        margin-right: 5px;
        border-radius: 50%;
        color: #0b75ff;
        background: #e6f1ff;
        font-size: 12px;
        width: 18px;
        height: 18px;
        display: inline-block;
        text-align: center;
        line-height: 18px;
    }
    .btn{
        margin-left: 25px;
        display: flex;
        align-items: center;
    }
}
.word{
    line-height: 20px;
    margin-left: 25px;
    margin-bottom: 10px;
    word-break: break-word;
}
.tip{
    color: #999;
    margin: 0 25px 0;
    font-size: 12px;
}
</style>