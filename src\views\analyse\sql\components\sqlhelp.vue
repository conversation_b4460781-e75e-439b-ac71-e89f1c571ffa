<script setup lang="ts">
import {ref} from "vue";

const visible = ref<boolean>(false);

const show = () => {
  visible.value = true
}
const cancel = () => {
  visible.value = false
}

defineExpose({
  show,
  cancel
});


</script>

<template>
  <a-modal v-model:visible="visible" :footer="false" title="帮助" @cancel="cancel">
    <div>
      <span style="font-weight: 600">动态参数</span><br><br>
      可以在语句中使用表达式 ${variable} 添加动态参数
      SELECT ${variable} FROM ${table} where ${PartDate:date}
    </div>
  </a-modal>
</template>

<style scoped lang="less">

</style>