

<template>
    <div class="guide">
        <div class="stickyBar">
            <div class="modal">
                <img src="/icon/analysis/performAnalysis.svg" alt="">
                <span class="title">分析指标</span>
            </div>
        </div>
        <div class="event-filter-box">
            <div v-for="(item,index) in eventListData" :key="index" class="action-row">
                <div class="filter-row-eventRow">
                    <div class="action-left">
                        <div class="row-content">
                            <div v-if="item.rename" class="rename">
                                <a-input v-model="item.displayName" placeholder="请输入" @blur="reNameBlur(index)"/>
                            </div>
                            <div>
                                <div v-for="(event,eventIndex) in  item.eventList" :key="eventIndex" class="event-item">
                                    <subSelect v-if="event.type == 'event'" :panel-data="event" @sub-change="subChange(index,eventIndex,$event)"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="action-right">
                        <a-space align="center">
                            <a-tooltip content="重命名" position="top">
                                <a-button class="btn-bg btn-26" @click="() => item.rename = true">
                                    <template #icon>
                                        <icon-pen />
                                    </template>
                                </a-button>
                            </a-tooltip>
                        </a-space>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import {Indicator} from "@/api/analyse/type";
import subSelect from "./subSelect.vue"

const eventBus = useEventBus('eventList');
const emits = defineEmits(['indicatorsChange'])

const indicator = ref<Indicator[]>([])

const eventListData = ref<any>([
    {
        name:'用户登录',
        type:'event',
        displayType:{
            type:'default',
            decimalNum:2,
            thousandSep: 1
        },
        displayName:'',
        unitName:'',
        rename:false,
        isAnd:true,
        eventList:[
            {
                eventName:'用户登录',
                type:'event',
                eventAttrCode:'',
                eventAttrName:'用户数',
                statisticalType:'',
                statisticalName:'',
                filter:{}
            },
        ],
        filtersList:[]
    },
])

const reNameBlur = (index:number) => {
    if(eventListData.value[index].displayName === ''){
        eventListData.value[index].rename = false
    }
}
const subChange = (index:number,eventIndex:number,e:any) => {
    const {eventAttrName,eventAttrDisplayName,statisticalName,statisticalType} = e
    const filter = { ...eventListData.value[index].eventList[eventIndex] };
    filter.eventAttrName = eventAttrName;
    filter.statisticalName = statisticalName;
    filter.statisticalType = statisticalType;
    filter.statisticalType = eventAttrDisplayName
    eventListData.value[index].eventList[eventIndex] = filter;
}

watch(eventListData.value, (newValue, oldValue) => {
    indicator.value = newValue.map(item => {
        const filters = item.filtersList.reduce((acc, el) => {
            if (el.multipleList.length <= 1) {
                acc.filters.push({ 
                    objectName: el.name,
                    objectType: el.objectType,
                    objectId:el.objectId,
                    filterType: el.filterType,
                    calcuSymbol:el.calcuSymbol,
                    thresholds:el.thresholds
                })
            } else {
                acc.subFilters.push({
                    logicalOperation: el.isAnd ? 'and' : 'or',
                    filters: el.multipleList.map(item => ({ 
                        objectName: item.name,
                        objectType: item.objectType,
                        objectId:item.objectId,
                        filterType: item.filterType,
                        calcuSymbol: item.calcuSymbol,
                        thresholds:item.thresholds
                    }))
                })
            }
            return acc
        }, { filters: [], subFilters: [] })

        return {
            displayName: item.displayName,
            displayType:item.displayType,
            name: item.name,
            type: item.type,
            eventList: item.eventList,
            filter: {
                logicalOperation: item.isAnd ? 'and' : 'or',
                filters: filters.filters,
                subFilters: filters.subFilters
            }
        }
    })
    emits('indicatorsChange',indicator.value)
    eventBus.emit(newValue); 

},{immediate:true})
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .title{
                font-weight: 600;
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .action-row{
            position: relative;
            height: auto;
            padding-right: 24px;
            padding-left: 24px;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .drag-index{
                    margin-top: 9px;
                    margin-right: 12px;
                    flex-shrink: 0;
                    width: 24px;
                    height: 24px;
                    color: var(--tant-bg-white-color-bg1-1);
                    font-size: 12px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    background-color: var(--tant-secondary-color-secondary-default);
                    border-radius: 4px;
                    transition: all .3s;
                    opacity: 1;
                }
                .hover-drag{
                    position: absolute;
                    left: 0;
                    margin-top: 9px;
                    margin-left: 31px;
                    flex-shrink: 0;
                    // width: 24px;
                    // height: 24px;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    opacity: 0;
                    transition: all .3s;
                }
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 4px 0;
                        overflow: hidden;
                        line-height: 32px;
                        white-space: normal;
                        
                        
                    }
                }
            }
            .action-right{
                position: absolute;
                top: 0;
                right: 4px;
                min-width: 40px;
                height: 36px;
                padding-top: 0 !important;
                display: flex;
                align-items: center;
                opacity: 0;
                transition: opacity .3s;
            }
            .filter-btn{
                display: inline-flex;
                align-items: center;
                min-width: 40px;
                max-width: 200px;
                height: 26px;
                padding: 0 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
                box-sizing: border-box;
                .btn-icon{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    margin-right: 5px;
                }
                .filter-label{
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    color: var(--tant-text-gray-color-text1-2);
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: top;
                }

                &:hover{
                    border-color: var(--tant-primary-color-primary-hover);
                }
            }
            .row-word{
                display: inline-block;
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
                vertical-align: top;
            }
            &:hover{
                background-color: var(--tant-fill-color-fill1-2);
                .action-left .drag-index{
                    opacity: 0;
                    transition: all .3s;
                }
                .action-left .hover-drag{
                    opacity: 1;
                    transition: all .3s;
                }
                :deep(.filter-btn){
                    background: #fff;
                }
                :deep(.filter-icon){
                    background: #fff;
                }
                .sub-action-left :deep(.filter-btn){
                    background: #fff;
                }
                .action-right{
                    opacity: 1;
                } 
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}

</style>