<script setup lang="ts">

import {ref} from "vue";
import TabBar from "@/components/tab-bar/index.vue";
import PageLayout from "@/layout/page-layout.vue";
import {useAppStore} from "@/store";
import router from "@/router";
import {useRoute} from "vue-router";
import {useEventBus} from "@vueuse/core";
import {DateManageEventBus, SaveEvent} from "@/types/event-bus";
import {useI18n} from "vue-i18n";

const {t} = useI18n();

const appStore = useAppStore();
const gotoItem = (item: string) => {
  router.push({
    name: item,
  });
};
const QuitModelVisible = ref(false)
const eventBus = useEventBus('eventList');
const saveEventBus = useEventBus(DateManageEventBus)
const targetRoute = ref();
const route = useRoute();
const translation = ref(route.name);
const bntActive = ref(false)
const goBack = () => {
  router.back()
}
eventBus.on((event, payload) => {
  if (event === 'virtualEvent') {
    bntActive.value = payload.length > 0
  }
  if(event === 'customEvent') {
    bntActive.value = payload>0

  }
  if (event === 'DataIndex') {
    bntActive.value = true
  }
  if(event === 'customAttr' || event === 'eventAttr') {
    bntActive.value = true
  }
})
const deleteConfirm=()=>{
  gotoItem(targetRoute.value)
}
const handleSava = () => {
  saveEventBus.emit(SaveEvent, translation.value)
}
const getPageTitle = () => {
  // 特殊处理指标创建/编辑页面
  if (route.path.includes('/data/analyse/indicator/create')) {
    return route.query.type === 'edit' ? '编辑指标' : '创建指标';
  }
  
  // 其他页面使用默认的国际化标题
  return t(route.meta.locale);
}
</script>

<template>
  <a-layout class="layout">
    <a-layout-header class="layout__header">
      <div class="header-title">
        <button class="title-button" @click="goBack">
          <span role="img">
            <icon-left size="13"/>
          </span>
        </button>
        <span style="align-items: center">{{ getPageTitle() }}</span>
      </div>
      <div class="header-oper">
        <a-button class="title-button" @click="goBack">
          <span>取消</span>
        </a-button>
        <a-button class="title-button-next" :disabled="!bntActive" @click="handleSava()">
          <span>保存</span>
        </a-button>
      </div>
    </a-layout-header>

    <a-layout-content class="layout__content">
      <TabBar v-if="appStore.tabBar"/>
      <a-layout-content>
        <PageLayout/>
      </a-layout-content>
    </a-layout-content>
    <a-modal
        v-model:visible="QuitModelVisible"
        :ok-button-props="{status:'normal'}"
        :align-center="false"
        :cancel-button-props="{type:'text' }"
        width="320px"
        top="120px"
        cancel-text="继续编辑"
        ok-text="退出" title-align="start"
        @ok="deleteConfirm" >
      <template #title>
        退出编辑
      </template>
      <div>
        信息尚未保存。确认退出编辑吗？
      </div>
    </a-modal>
  </a-layout>
</template>

<style scoped lang="less">
.layout {
  background-color: var(--tant-bg-gray-color-bg2-1);
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;

  .layout__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 12px 24px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .header-title {
      flex: 1 1;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header4-medium);
      align-items: center;

      .title-button {
        color: var(--tant-text-gray-color-text1-2);
        background-color: transparent;
        border: none;
        margin-right: 4px;
        padding: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;
        transition: .5s;

      }

      .title-button > span {
        display: flex;
        align-items: center;
        line-height: normal;
      }

    }

    .header-oper {
      display: flex;

      .title-button {
        background-color: transparent;
        border: none;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        color: var(--tant-secondary-color-secondary-default);
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;

      }

      .title-button-next {
        background-color: var(--tant-primary-color-primary-hover);
        border-color: var(--tant-primary-color-primary-hover);
        color: var(--tant-white-white-100);
        border: none;
        margin-left: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button-next:disabled {
        background-color: var(--tant-primary-color-primary-disable);
        border-color: var(--tant-primary-color-primary-disable);
        cursor: not-allowed;
      }
    }
  }

  .layout__content {
    display: flex;
    flex: 1 1;
    height: calc(100vh - 100px);
    margin: 24px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
  }
}

</style>