.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .card-toolbar {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;

    .card-filter {
      flex-wrap: wrap;
      display: flex;
      align-items: center;
      padding: 0;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      background-color: transparent;
      border: none;
      box-shadow: none !important;

      .particle-select {
        min-width: 20px;
        height: 24px;
      }

      .card-ranger-picker {
        min-width: 35px;
        height: 27px;
        padding: 0;
        line-height: 24px;
        cursor: pointer;
      }

      .date-compare {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin: -3px 6px 0 8px;
        color: var(--tant-secondary-color-secondary-default);
        cursor: pointer;
      }

      .comparing {
        border-radius: 4px;
        background-color: var(--tant-secondary-color-secondary-fill-active);

        &:hover {
          color: var(--tant-primary-color-primary-default);
        }
      }

      .compare-content {
        z-index: 1000;
        position: absolute;
        top: 70px;
        left: 125px;
        width: 410px;
        background-color: var(--tant-bg-white-color-bg1-1);
        box-shadow: var(--tant-large-shadow-large-overall);

        .content {
          padding: 8px 12px;

          .content-line {
            display: flex;
            align-items: center;
            padding: 6px 16px;

            .range-pick {
              flex: 1 1;
              margin: 0 16px;
              display: inline-block;
              min-width: 60px;
              height: 32px;
              padding: 0 10px;
              color: var(--tant-text-gray-color-text1-1);
              font-size: 14px;
              line-height: 32px;
              white-space: nowrap;
              background-color: var(--tant-bg-white-color-bg1-1);
              border: 1px solid var(--tant-border-color-border1-1);
              border-radius: 4px;
              transition: all .3s;

              &:hover {
                border-color: var(--tant-primary-color-primary-hover);
              }

              .container {
                display: flex;
                align-items: center;
                width: 100%;

                .range-icon {
                  margin-left: auto;
                  color: var(--tant-text-gray-color-text1-3);
                  font-size: 14px;
                }
              }
            }

            .delete-icon {
              background-color: transparent;
              padding: 4px;
              color: var(--tant-text-gray-color-text1-3);
              font: var(--tant-body-font-body-regular);
              border-radius: var(--tant-border-radius-medium);
              border: none;

              &:hover {
                color: var(--tant-status-danger-color-danger-default);
                background-color: var(--tant-status-danger-color-danger-fill-hover);
              }
            }

            &:hover {
              background-color: var(--tant-fill-color-fill1-2);
              border-radius: 4px;
            }
          }

          .content-empty {
            height: 60px;
            padding: 19px 0;
            color: var(--tant-text-gray-color-text1-3);
            text-align: center;
          }
        }

        .compare-footer {
          display: flex;
          align-items: center;
          height: 48px;
          padding: 8px 16px;
          border-top: 1px solid var(--tant-border-color-border1-1);

          .filter-button {
            display: inline-flex;
            align-items: center;
            padding: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all .3s;

            .action {
              margin-right: 8px;
              padding: 3px;
              color: var(--tant-primary-color-primary-default);
              background-color: var(--tant-primary-color-primary-fill);
              border-radius: 4px;
            }

            &:hover .action {
              color: var(--tant-primary-color-primary-hover);
              background-color: var(--tant-primary-color-primary-fill-hover);
            }
          }

          .button-left {
            margin-right: 8px;
            margin-left: auto;
            color: var(--tant-text-gray-color-text1-2);
            background-color: transparent;
            border: none;
            height: 32px;
            padding: 5px 16px;
            border-radius: var(--tant-border-radius-medium);
            box-shadow: unset;

            &:hover {
              background-color: var(--tant-secondary-color-secondary-transp-hover) !important;
            }
          }

          .button-right {
            color: var(--tant-white-white-100);
            background-color: var(--tant-primary-color-primary-default);
            border: none;
            height: 32px;
            padding: 5px 16px;
            border-radius: var(--tant-border-radius-medium);
            box-shadow: unset;

            &:hover {
              background-color: var(--tant-primary-color-primary-hover);
            }
          }
        }
      }
    }

    .card-config {
      display: flex;
      cursor: initial;
      height: 23px;
      flex: 1 0;
      align-items: center;
      justify-content: flex-end;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      transition: opacity, 0.3s;

      .show-chart-label {
        margin-right: 8px;
        color: var(--tant-disabled-color-disabled-text);
        font-size: 12px;
        line-height: 0;
      }

      .arco-select-view-suffix {
        padding-left: 6px;
      }

      .chart-type-select-label {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: end;

        .select-icon {
          padding-right: 4px;
        }
      }

      .option-icon {
        margin-right: -4px;
      }
    }
  }
  .data-chart,
  .data-chart-middle,
  .data-chart-large {
    width: 100%;
    height: calc(100% - 32px);
    .chart-box{
      width: 100%;
      height: 100%;
    }
  }
}

:root {
  .card:hover .card-config {
    display: flex;
  }
}
