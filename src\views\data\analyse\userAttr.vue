<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="refresh">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-upload
              :custom-request="uploadFile"
              :show-file-list="false"
              :accept="'.xlsx,.xls'"
          >
            <template #upload-button>
              <a-button type="primary">
                <icon-upload/>
              </a-button>
            </template>
          </a-upload>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="()=>{
            formData.type = UserAttributeType.CUSTOM
            formData.aggregateType = UserAttributeAggregateType.REPLACE
            formData.dataType = FieldType.STRING
            formData.status = 1
            formData.visibility = true
            formData.sourceChannel= [UserAttributeSourceChannel.EVENT]
            formData.dataSource = 'appsflyer'
            editModalShow=true
          }">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #sourceChannel="{record}">
          <a-tag v-for="item in record.sourceChannel" :key="item">
            {{ getUserAttributeSourceChannelName(item) }}
          </a-tag>
        </template>
        <template #note="{record}">
          {{ handleDataAliasStr(record) }}
          {{ record.note }}
        </template>
        <template #optional="{ record }">
          <a-button type="text" @click="editData(record)">编辑</a-button>
          <a-popconfirm v-if="UserAttributeType.PRESET !== record.type" :content="`确认删除“${record.name}”?`" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :visible="editModalShow"
        title-align="start"
        :title="formData.code?'编辑用户属性':'新增用户属性'"
        :ok-text="formData.code?'更新':'保存'"
        @cancel="editCancel"
        @ok="editConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical" style="max-height: 680px;overflow: auto;">
        <a-form-item v-show="false" field="code" label="属性编码">
          <a-input v-model="formData.code" disabled/>
        </a-form-item>
        <a-form-item field="name" :disabled="formData.code" :rules="[{required:true,message:'属性名称不能为空'}]" label="属性名称">
          <a-input v-model="formData.name" placeholder="请输入属性名称"/>
        </a-form-item>
        <a-form-item field="displayName" :rules="[{required:true,message:'显示名称不能为空'}]" label="显示名称">
          <a-input v-model="formData.displayName" placeholder="请输入属性显示名称"/>
        </a-form-item>
        <a-form-item field="type" :rules="[{required:true,message:'属性类型不能为空'}]" label="属性类型">
          <a-select v-model="formData.type" disabled placeholder="请选择属性类型">
            <a-option v-for="item in Object.values(UserAttributeType).filter(value => typeof value === 'number')" :key="item" :value="item">{{
                getUserAttributeTypeName(item)
              }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="dataType" :rules="[{required:true,message:'数据类型不能为空'}]" label="数据类型">
          <a-select v-model="formData.dataType" :disabled="formData.code"  placeholder="请选择属性数据类型">
            <a-option v-for="item in Object.values(FieldType)" :key="item" :value="item">{{ getFieldTypeName(item) }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="aggregateType" :rules="[{required:true,message:'数据更新方式不能为空'}]" label="更新方式">
          <a-select v-model="formData.aggregateType" placeholder="请选择数据更新方式">
            <a-option v-for="item in Object.values(UserAttributeAggregateType)" :key="item" :value="item">{{ getUserAttributeAggregateType(item) }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="dimTable" label="关联维表属性">
          <a-select v-model="formData.dimTable" placeholder="请选择关联维表属性" allow-clear>
            <a-option v-for="item in dimensionOptions" :key="item.tableCode" :value="item.tableCode">{{ item.tableName }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item v-if="UserAttributeType.PRESET !== formData.type" style="margin-bottom: 0">
          <a-form-item field="visibility" label="显示状态">
            <a-switch v-model="formData.visibility"/>
          </a-form-item>
          <a-form-item field="status" label="可用状态">
            <a-switch v-model="formData.status" :checked-value="1" :unchecked-value="0"/>
          </a-form-item>
        </a-form-item>
        <a-form-item field="sourceChannel" label="数据来源">
          <a-checkbox-group v-model="formData.sourceChannel">
            <a-checkbox v-for="item in Object.values(UserAttributeSourceChannel)" :key="item" :value="item">{{ getUserAttributeSourceChannelName(item) }}</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item style="margin-bottom: 0" label="数据源映射">
          <a-form-item field="dataSource" label="数据源">
            <a-select v-model="formData.dataSource" default-value="appsflyer" placeholder="请选择数据源">
              <a-option value="appsflyer">appsflyer</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="alias" label="别名" style="margin-left: 12px;margin-right: 8px;">
            <a-input v-model="formData.alias" placeholder="请输入别名"/>
          </a-form-item>
        </a-form-item>
        <a-form-item field="note" label="属性备注">
          <a-textarea v-model="formData.note" placeholder="请输入属性备注"/>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {deleteUserAttrList, getDimensionList, getUserAttrDetail, getUserAttrList, saveUserAttrList, uploadMetadata, userAttrAdd} from "@/api/setting/api";
import _ from "lodash";
import {Message, Modal, RequestOption} from "@arco-design/web-vue";
import {getUserAttributeAggregateType, getUserAttributeSourceChannelName, getUserAttributeTypeName, UserAttributeAggregateType, UserAttributeSourceChannel, UserAttributeType} from "@/api/enum";
import {FieldType, getFieldTypeName} from "@/api/type";
import {useRoute} from "vue-router";
import router from "@/router";


const route = useRoute();
const data = ref<any>([]);
const loading = ref<boolean>(true);
const searchName = ref<string>(route.query.text as string || '');
const formData = ref<any>({});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const dimensionOptions = ref<any>([])
const columns = ref<any>([
  {
    title: '属性名',
    dataIndex: 'name',
    fixed: 'left',
    width: 160,
  },
  {
    title: '显示名',
    dataIndex: 'displayName',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '备注',
    dataIndex: 'note',
    slotName: 'note',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '属性类型',
    dataIndex: 'type',
    render: (value) => {
      const {record} = value;
      return getUserAttributeTypeName(record.type)
    },
    width: 120,
    align: 'center',
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    width: 200,
    render: (value) => {
      const {record} = value;
      return getFieldTypeName(record.dataType)
    },
    align: 'center',
  },
  {
    title: '更新方式',
    dataIndex: 'aggregateType',
    width: 200,
    render: (value) => {
      const {record} = value;
      return getUserAttributeAggregateType(record.aggregateType)
    },
    align: 'center',
  },
  {
    title: '显示状态',
    dataIndex: 'visibility',
    render: (value) => {
      const {record} = value;
      return record.visibility ? '可见' : '隐藏'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '可用状态',
    dataIndex: 'status',
    render: (value) => {
      const {record} = value;
      return record.status ? '启用' : '禁用'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '数据来源',
    dataIndex: 'sourceChannel',
    slotName: 'sourceChannel',
    width: 120,
    align: 'center',
  },
  {
    title: '最后更新人',
    dataIndex: 'creator',
    slotName: 'creator',
    width: 120,
    align: 'center',
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 160
  },
]);
const handleDataAliasStr = (record) => {
  if(!record?.dataAlias) return ''
  const list = record.dataAlias.filter(item => item.alias)
  return list.map(item => `数据源${item.dataSource}的别名为${item.alias}`).join('，');
}
const refresh = () => {
  const params = {
    name: searchName.value,
  };
  loading.value = true
  getUserAttrList(params).then(res => {
    const searchRegex = searchName.value ? new RegExp(searchName.value, 'i') : null;
    data.value = _.isEmpty(searchName.value) ? res : res?.filter(item => {
      return searchRegex ? (searchRegex.test(item.name) || searchRegex.test(item.displayName)) : true;
    })
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  getDimensionList().then(res => {
    dimensionOptions.value = res;
  })
  refresh()
})

const searchData = () => {
  // 更新路由参数
  const query = { ...route.query };
  if (searchName.value) {
    query.text = searchName.value;
  } else {
    delete query.text;
  }
  router.replace({ query });
}


const editData = (record: any) => {
  getUserAttrDetail(record.code).then((res) => {
    const aliasData = res.dataAlias[0] || {};
    formData.value = {
      code:record.code,
      name:record.name,
      displayName:record.displayName,
      type:record.type,
      dataType:record.dataType,
      aggregateType:record.aggregateType,
      visibility:record.visibility,
      status:record.status,
      sourceChannel:record.sourceChannel,
      note:record.note,
      dimTable: res.dimTable,
      dataSource: aliasData?.dataSource || 'appsflyer',
      alias: aliasData?.alias,
    };
    editModalShow.value = true;
  })
}

const formSubmit = (value) => {
  if (_.isEmpty(value)) {
    Message.warning("用户属性数据不能为空！")
  }
  const { dataSource, alias, ...restParams } = value;
  const params = {
    ...restParams,
    dataAlias: {
      dataSource: dataSource || 'appsflyer',
      alias,
    }
  };
  if (_.isEmpty(value.code)) {
    userAttrAdd(params).then(res => {
      Message.info("用户属性数据保存成功！")
      editModalShow.value = false;
      formRef.value.resetFields();
      refresh()
    }).catch(e => {
      Message.error("用户属性数据保存失败！", e)
    })
    return
  }
  saveUserAttrList(params).then(res => {
    Message.info("用户属性数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    refresh()
  }).catch(e => {
    Message.error("用户属性数据保存失败！", e)
  })
}

const editConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}
const uploadFile = (option: RequestOption) => {
  const {fileItem} = option
  uploadMetadata("sdk_user_attribute", fileItem).then((res) => {
    if (res?.error === 0) {
      Modal.success({
        titleAlign: "start",
        title: '数据导入成功',
        content: `共导入${res?.success || 0}条数据！`
      });
      return
    }
    if (res?.success === 0) {
      Modal.error({
        titleAlign: "start",
        title: '数据导入失败',
        content: `共失败${res?.error || 0}条数据！部分原因为：${res?.errorList?.[0]?.msg || '未知'}`
      });
      return;
    }
    Modal.warning({
      titleAlign: "start",
      title: '数据导入完成',
      content: `成功导入${res?.success || 0}条数据，失败${res?.error || 0}条！部分原因为：${res?.errorList?.[0]?.msg || '未知'} ${res?.errorList?.[1]?.msg || ''} ${res?.errorList?.[2]?.msg || ''}`
    });
  }).catch(e => {
    Modal.error({
      titleAlign: "start",
      title: '数据导入失败',
      content: e || "请联系管理员！"
    });
  })
}

const deleteData = (record:any) => {
  const {code} = record
  deleteUserAttrList(code).then(res => {
    Message.info("用户属性数据删除成功！")
    refresh()
  }).catch(e => {
    Message.error("用户属性数据删除失败！", e)
  })
}
</script>

<style scoped lang="less">

</style>