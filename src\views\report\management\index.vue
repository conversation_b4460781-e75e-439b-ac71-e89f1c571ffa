<script setup lang="ts">
import {ref} from "vue";
import shareTable from "./components/shareReport.vue"
import myTable from "./components/myReport.vue"

const selectedTab = ref('我的报表')
const choose = (v: string) => {
  selectedTab.value = v
}
const reportInputValue=ref('')

</script>

<template>
  <div id="taRoot" style="display: block">
    <main class="ant-layout">
      <div class="head">
        <span class="title">报表管理</span>
        <a-button class="ant-bnt">
          <span class="ant-bnt-icon"><icon-plus/></span>
          <span class="ant-bnt-text">创建报表</span>
        </a-button>
      </div>
      <div class="content">
        <div class="toolbar">
          <a-button-group>
            <button
                class="tab__"
                :class="{ active: selectedTab === '我的报表' }"
                @click="choose('我的报表')"><span class="text">我的报表</span></button>
            <button
                class="tab__"
                :class="{ active: selectedTab === '共享给我' }"
                @click="choose('共享给我')"><span class="text">共享给我</span></button>
          </a-button-group>
          <a-input v-model:model-value="reportInputValue" style="width: 216px" placeholder="搜索报表">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="tab-content">
          <div v-if="selectedTab === '我的报表'">
            <my-table :report-value="reportInputValue"/>
          </div>
          <div v-if="selectedTab === '共享给我'">
            <share-table/>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="less">
body #taRoot {
  width: 100%;
  min-width: 1200px;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
  background: var(--tant-bg-gray-color-bg2-1);

}

.ant-layout {
  position: relative;
  width: 100%;
  padding: 24px;
  overflow-y: auto;


  .head {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    height: 38px;
    margin-bottom: 16px;

    .title {
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header3-medium);
    }

    .ant-bnt {
      color: var(--tant-white-white-100);
      background-color: var(--tant-primary-color-primary-default);
      border: none;
      border-radius: var(--tant-border-radius-medium);
      box-shadow: unset;
      padding: 5px 16px;

      .ant-bnt-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .ant-bnt-text {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        margin-left: 4px;


      }
    }
  }

  .content {
    padding: 24px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;

    .toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      margin-bottom: 16px;


      .tab__ {
        background-color: var(--tant-bg-white-color-bg1-1);
        border: none;
        outline: none;
        height: 32px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;
        font-weight: 500;
        padding: 5px 12px;
        margin-right: 4px;
        transition: background-color 0.3s ease, color 0.3s ease;

        .text {
          font-size: 14px;
        }
      }

      .tab__:hover {
        background-color: var(--tant-secondary-color-secondary-fill);
        color: var(--tant-text-gray-color-text1-1);
        cursor: pointer;
      }

      .tab__.active {
        background-color: var(--tant-secondary-color-secondary-fill);
        color: var(--tant-text-gray-color-text1-1);
        -webkit-text-stroke: .2px currentColor;
        border-radius: 4px;
      }

      /* 去掉 active 状态下的 hover 效果 */

      .tab__.active:hover {
        background-color: var(--tant-secondary-color-secondary-fill);
        cursor: default; /* 让光标保持为默认状态 */
      }
    }
  }
}

</style>