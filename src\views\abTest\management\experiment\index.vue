<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <a-select
              v-model:model-value="queryParams.tag"
              style="width: 180px;"
              :loading="tagLoading"
              placeholder="请选择实验标签"
              allow-search
              multiple
              :tag-nowrap="true"
              @change="init">
            <a-option v-for="item of options" :key="item" :value="item">{{ item }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-select v-model:model-value="queryParams.status" placeholder="实验状态" style="width: 120px;" allow-clear @change="statusChange">
            <a-option :value="0">草稿</a-option>
            <a-option :value="1">进行中</a-option>
            <a-option :value="2">已终止</a-option>
            <a-option :value="3">已结束</a-option>
            <a-option :value="4">调试中</a-option>
          </a-select>
        </div>
        <!-- <div class="filter-item">
          <a-select v-model="queryParams.timeType">
            <a-option value="1">运行时间</a-option>
            <a-option value="2">开始时间</a-option>
            <a-option value="3">停止时间</a-option>
          </a-select>
        </div> -->
        <!-- <div class="filter-item">
          <a-range-picker style="width: 240px;" />
        </div> -->
        <div class="filter-item">
          <a-input v-model:model-value="queryParams.name" placeholder="请输入实验名称回车搜索" style="width: 240px;" @change="init"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            <template #icon>
              <icon-plus/>
            </template>
            创建实验
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
      >
        <template #name="{ record }">
          <span class="link" @click="viewDetail(record)">{{ record.name }}</span>
        </template>
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusLabel(record.status) }}
          </a-tag>
        </template>
        <template #runningTime="{ record }">
          <div class="diff">{{ getTimeDiffText(record.startTime, record.endTime) }}</div>
          <div class="time-detail">
            {{
              record.startTime && record.endTime
                  ? dayjs(record.startTime).format('YYYY-MM-DD HH:MM:ss') + ' 至 ' + dayjs(record.endTime).format('YYYY-MM-DD HH:MM:ss')
                  : record.startTime ? '开始于 ' + dayjs(record.startTime).format('YYYY-MM-DD HH:MM:ss') : '-'
            }}
          </div>
        </template>

        <template #action="{ record }">
          <a-dropdown position="bl" :hide-on-select="false">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption v-if="record.status===0" value="edit" @click="editItem(record)">
                <icon-edit class="mr8"/>
                编辑
              </a-doption>
              <a-doption v-if="record.status === 4" value="start" @click="startItem(record.code)">
                <icon-record-stop class="mr8"/>
                启动
              </a-doption>
              <a-doption v-if="record.status === 1" value="stop" @click="stopItem(record.code)">
                <icon-record-stop class="mr8"/>
                停止
              </a-doption>
              <a-doption value="copy" @click="copyItem(record.code)">
                <icon-copy class="mr8"/>
                复制
              </a-doption>
              <a-doption v-if="record.status>0" value="view" @click="viewReport(record.code)">
                <icon-file class="mr8"/>
                查看报告
              </a-doption>
              <a-popconfirm type="error" :content="`确认删除“${record.name}”?`" @ok="deleteItem(record)">
                <a-doption value="delete">
                  <icon-delete class="mr8"/>
                  删除
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange"/>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import {copyExperiment, deleteExperimentItem, getExperimentList, getExperimentTagList, startExperiment, stopExperiment} from "@/api/ab/api";
import selectApp from "@/components/selected-game-app/index.vue"
import {useEventBus} from '@vueuse/core';
import {LocalStorageEventBus} from "@/types/event-bus";
import dayjs from 'dayjs';
import {useRoute} from "vue-router";
import {Message} from '@arco-design/web-vue';

const localStorageEventBus = useEventBus(LocalStorageEventBus)
const route = useRoute();
// 查询数据
const queryParams = reactive({
  name: route.query.text as string || '',
  status: undefined,
  tag: [],
  current: 1,
  pageSize: 10,
})
const total = ref(0)
const options = ref<any>([]);
const tagLoading = ref(false);

// const handleSearch = async (value) => {
//   if (value) {
//     tagLoading.value = true;
//     try {
//       const res = await getExperimentTagList(value);
//       options.value = res || [];
//     } catch (e) {
//       options.value = [];
//     } finally {
//       tagLoading.value = false;
//     }
//   } else {
//     options.value = [];
//   }
// };
const getTags = async () => {
  tagLoading.value = true;
  try {
    const res = await getExperimentTagList();
    options.value = res || [];
  } catch (e) {
    options.value = [];
  } finally {
    tagLoading.value = false;
  }
};
getTags()
// 模拟table数据
const loading = ref(false)

const tableData = ref<any>([])
const columns = ref<any>([
  {
    title: '实验',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    slotName: 'name',
    minWidth: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    ellipsis: true,
    tooltip: true,
    slotName: 'status',
    minWidth: 120,
  },
  {
    title: '标签',
    dataIndex: 'tags',
    ellipsis: true,
    tooltip: true,
    slotName: 'tags',
    minWidth: 120,
    render: (value) => {
      const {record} = value;
      return record.tags?.join(',') || '-'
    },
  },
  {
    title: '设定流量',
    dataIndex: 'flowPercentage',
    ellipsis: true,
    slotName: 'flowPercentage',
    minWidth: 100,
    render: (value) => {
      const {record} = value;
      return record.flowPercentage ? `${record.flowPercentage}%` : '-'
    },
  },
  {
    title: '进组用户数',
    dataIndex: 'userNumber',
    ellipsis: true,
    minWidth: 100,
  },
  {
    title: '激活用户数',
    dataIndex: 'activateUser',
    ellipsis: true,
    minWidth: 100,
  },
  {
    title: '运行时间',
    dataIndex: 'runningTime',
    slotName: 'runningTime',
    ellipsis: true,
    tooltip: true,
    width: 340,
  },
  {
    title: '最后更新人',
    dataIndex: 'principal',
    slotName: 'principal',
    width: 120,
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },

  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    slotName: 'action',
  }
])

// Table页面滑动
const scroll = {
  y: 'calc(100vh - 256px)'
};

const init = () => {
  loading.value = true
  try {
    const params: any = {...queryParams};
    if (!params.name) {
      delete params.name;
    }
    if (params.status === undefined || params.status === null || params.status === '') {
      delete params.status;
    }
    getExperimentList(params).then(res => {
      if (res) {
        tableData.value = res.items
        total.value = res.total
      }
    }).catch((e) => {
      console.error('获取实验列表失败', e)
    }).finally(() => {
      loading.value = false
    })
  } catch (e) {
    loading.value = false
    console.error('获取实验列表异常', e)
  }
}
init()
// 分页
const pageChange = (v) => {
  queryParams.current = v
  init()
}
// 状态切换
const statusChange = () => {
  init();
}
// 获取时间差
const getTimeDiffText = (start, end) => {
  if (!start) return '';
  const startTime = dayjs(start);
  const endTime = end ? dayjs(end) : dayjs();
  const diff = endTime.diff(startTime, 'minute');
  if (diff < 0) return '';
  const days = Math.floor(diff / (60 * 24));
  const hours = Math.floor((diff % (60 * 24)) / 60);
  const minutes = diff % 60;
  return `${days}天${hours}时${minutes}分`;
};
// 新建
const createIndex = () => {
//   handleRefs.value.openModal()
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT_CREATE,
  })
}
const editItem = (record: any) => {
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT_CREATE,
    query: {
      code: record.code,
      remainEditStep:record.remainEditStep
    }
  })
}
const STATUS_OPTIONS = [
  {value: 0, label: '草稿'},
  {value: 1, label: '进行中'},
  {value: 2, label: '已终止'},
  {value: 3, label: '已结束'},
  {value: 4, label: '调试中'},
] as const;
const getStatusColor = (status: string) => {
  const colorMap = {
    0: 'lime',    // 草稿
    1: 'blue', // 进行中
    2: 'red',      // 已终止
    3: 'gray',     // 已结束
    4: 'orange',   // 调试中
  };
  return colorMap[status] || 'gray';
};

const getStatusLabel = (status: string) => {
  return STATUS_OPTIONS.find(option => option.value === status)?.label || '未知状态';
};


// 复制
const copyItem = async (code: string) => {
  try {
    const res = await copyExperiment(code)
    editItem(res)
    Message.success('复制成功')
    init()
  } catch (e) {
    Message.error('复制失败')
  }
}
// 停止
const stopItem = async (code: string) => {
  try {
    await stopExperiment(code)
    Message.success('停止成功')
    init()
  } catch (e) {
    Message.error('停止失败')
  }
}
// 启动
const startItem = async (code: string) => {
  try {
    await startExperiment(code)
    Message.success('启动成功')
    init()
  } catch (e) {
    Message.error('启动失败')
  }
}
// 查看报告
const viewReport = (code: string) => {
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT_DETAIL,
    query: {
      code,
      tab: '2'
    }
  })
}
// 查看详情
const viewDetail = (record:any) => {
  if(record.status===0){
    editItem(record)
    return
  }
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT_DETAIL,
    query: {
      code: record.code,
      tab: '1'
    }
  })
}
// 删除
const deleteItem = async (record: any) => {
  try {
    await deleteExperimentItem(record.code);
    Message.success('删除成功');
    init();
  } catch (e) {
    // Message.error(e.detail || '删除失败');
  }
}
localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    init()
  }
})
</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}

.time-detail {
  color: #14141473;
  font-size: 12px;
  line-height: 18px;
}

.link {
  cursor: pointer;

  &:hover {
    color: rgb(var(--primary-6));
    text-decoration: underline;
  }
}
</style>