<script setup lang="ts">
import {computed, h, onMounted, ref} from "vue";
import {SpaceDto} from "@/api/space/type";
import {Message, TreeNodeData} from "@arco-design/web-vue";
import cubeImg from '/public/icon/cube-open.svg';
import folderImg from '/public/icon/folder.svg';
import _ from "lodash";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {ObjectPermissionType} from "@/api/enum";
import {listSpace} from "@/api/space/api";
import {moveDashboard} from "@/api/dashboard/api";
import {DashboardDto} from "@/api/dashboard/type";
import {useDashboardStore} from "@/store";


interface Props {


  /**
   * 看板
   */
  dashboard?: DashboardDto
}


const props = defineProps<Props>()
/*
 *我的看板
 */

const mySpace = ref<SpaceDto>();
/*
 *项目空间
 */
const dashboardStore = useDashboardStore()
const projectSpaces = ref<SpaceDto[]>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})
const visible = defineModel<boolean>("visible", {default: false});
const dashboardPosition = ref<string>('other');
const projectPositionSelected = ref<string | undefined>();
const myPositionSelected = ref<string>();
const myPositionSelectedSpace = ref<string>()
const cacheEventBus = useEventBus(CacheEventBus)
const projectPosition = computed(() => {
  return projectSpaces.value?.map(space => {
    // 对authority为4（GUEST查看者）的空间添加disabled属性
    const isDisabled = space.authority === ObjectPermissionType.GUEST;
    return {
      'key': `${space.spaceId}`,
      'title': space.name,
      'icon': () => h(cubeImg),
      'disabled': isDisabled,
      'children': space.folders?.map(folder => {
        return {
          'key': `${folder.folderId}`,
          'title': folder.name,
          'icon': () => h(folderImg),
          'disabled': isDisabled
        }
      })
    } as TreeNodeData
  }) || []
})

const myPosition = computed(() => {
  return mySpace.value?.folders?.filter(folder => folder?.allowAdd !== false || folder.isDeletable).map(folder => {
    return {
      'key': folder.folderId,
      'title': folder.name,
      'icon': () => h(folderImg)
    }
  })
})


const handleOk = () => {
  if (props.dashboard?.space.name !== '我的看板') {
    const myList = [{
      folderId: myPositionSelectedSpace.value,
      spaceId: mySpace.value?.spaceId,
      dashboardId: props.dashboard?.dashboardId
    }]
    if (dashboardPosition.value === 'my' && myPositionSelectedSpace.value) {
      moveDashboard(myList)
          .then((resp) => {
            if (props.dashboard?.dashboardId === dashboardStore.dashboardSelected?.dashboardId) {
              treeMenuEventBus.emit(RefreshEvent);
            } else {
              cacheEventBus.emit('move-event', {
                type: 'dashboard',
                dashboardId: props.dashboard?.dashboardId,
                folderId: myPositionSelectedSpace.value,
                spaceId: mySpace.value?.spaceId
              });
            }
            visible.value = false;
            if(props.dashboard?.dashboardId===dashboardStore.dashboardSelected?.dashboardId) {
              dashboardSelected.value={
                spaceId: mySpace.value?.spaceId,
                folderId: myPositionSelectedSpace.value,
                dashboardId: props.dashboard?.dashboardId
              }
            dashboardStore.dashboardSelected={
              spaceId: mySpace.value?.spaceId,
              folderId: myPositionSelectedSpace.value,
              dashboardId: props.dashboard?.dashboardId
            }

            }
            Message.success('移动成功')
          })
          .catch((e) => {
            Message.error("移动失败！", e);
          });

      return;
    }
    if (dashboardPosition.value === 'other' && projectPositionSelected.value) {
      const spaceID = projectPositionSelected.value?.startsWith('space') ? projectPositionSelected.value : projectSpaces.value?.filter(space =>
          space.folders?.some(folder => folder.folderId === projectPositionSelected.value)
      ).map(space => space.spaceId)[0];
      const folderID = projectPositionSelected.value?.startsWith('folder') ? projectPositionSelected.value : null
      const spaceList = [{folderId: folderID, spaceId: spaceID, dashboardId: props.dashboard?.dashboardId}]
      moveDashboard(spaceList)
          .then((resp) => {
            if (props.dashboard?.dashboardId === dashboardStore.dashboardSelected?.dashboardId) {
              treeMenuEventBus.emit(RefreshEvent);
            } else {
              cacheEventBus.emit('move-event', {
                type: 'dashboard',
                dashboardId: props.dashboard?.dashboardId,
                folderId: folderID,
                spaceId: spaceID
              });
            }
            visible.value = false;
            if(props.dashboard?.dashboardId===dashboardStore.dashboardSelected?.dashboardId) {
              dashboardSelected.value={
                spaceId: spaceID,
                folderId:folderID,
                dashboardId: props.dashboard?.dashboardId
              }
              dashboardStore.dashboardSelected={
                spaceId: spaceID,
                folderId:folderID,
                dashboardId: props.dashboard?.dashboardId
              }
            }
            Message.success('移动成功')
          })
          .catch((e) => {
            Message.error("重命名失败！", e);
          });
    }else{
      Message.warning('请选择移动至位置')
    }
  } else {
    const myList = [{
      folderId: myPositionSelected.value,
      spaceId: mySpace.value?.spaceId,
      dashboardId: props.dashboard?.dashboardId
    }]
    if (myPositionSelected.value) {
      moveDashboard(myList)
          .then((resp) => {
            if (props.dashboard?.dashboardId === dashboardStore.dashboardSelected?.dashboardId) {
              treeMenuEventBus.emit(RefreshEvent);
            } else {
              cacheEventBus.emit('move-event', {
                type: 'dashboard',
                dashboardId: props.dashboard?.dashboardId,
                folderId: myPositionSelected.value,
                spaceId: mySpace.value?.spaceId
              });
            }
            visible.value = false;
            if(props.dashboard?.dashboardId===dashboardStore.dashboardSelected?.dashboardId) {
              dashboardSelected.value={
                folderId: myPositionSelected.value,
                spaceId: mySpace.value?.spaceId,
                dashboardId: props.dashboard?.dashboardId
              }
              dashboardStore.dashboardSelected={
                folderId: myPositionSelected.value,
                spaceId: mySpace.value?.spaceId,
                dashboardId: props.dashboard?.dashboardId
              }
            }
            Message.success('移动成功')
          })
    } else {
      Message.warning('请选择移动至位置')
    }
  }
}
const handleCancel = () => {
  visible.value = false;
}
/**
 * 刷新菜单
 */
const refreshMenu = () => {
  listSpace(true).then((resp: SpaceDto[]) => {
    const spaces = resp || []
    const mySpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority === ObjectPermissionType.OWNER
    })
    const projectSpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority !== ObjectPermissionType.OWNER
    })
    if (!_.isEmpty(mySpaceList) && mySpaceList.length === 1) {
      // eslint-disable-next-line prefer-destructuring
      mySpace.value = mySpaceList[0];
    }

    if (!_.isEmpty(projectSpaceList) && projectSpaceList.length > 0) {
      projectSpaces.value = projectSpaceList;
    }
  })
}

treeMenuEventBus.on((event) => {
  if (event === RefreshEvent) {
    refreshMenu()
  }
})

/**
 * 初始化
 */
onMounted(() => {
  refreshMenu()
  if (props.dashboard?.space.name === '我的看板') {
    myPositionSelected.value = ""
  }
  if (props.dashboard?.folder?.folderId) {
    projectPositionSelected.value = props.dashboard?.folder?.folderId
  }
  if (!props.dashboard?.folder?.folderId && props.dashboard?.space.spaceId) {
    projectPositionSelected.value = props.dashboard?.space.spaceId
  }

})

</script>

<template>
  <a-modal
      :visible="visible"
      :mask-closable="false"
      :width="392"
      title-align="start"
      ok-text="移动"
      title="移动至"
      @ok="handleOk"
      @cancel="handleCancel">
    <div v-if="props.dashboard?.space.name!=='我的看板'">
      <a-row class="item" align="center">
        <a-col :span="24">
          <a-radio-group type="button" :model-value="dashboardPosition" @change="value => {dashboardPosition=value}">
            <a-radio value="my">我的看板</a-radio>
            <a-radio value="other">项目空间</a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row v-if="dashboardPosition==='my'" class="item" align="center">
        <a-col :span="24">
          <a-select
              v-model="myPositionSelectedSpace"
              placeholder="请选择添加位置">
            <div v-for="item in myPosition" :key="item.key" class="select-options">
              <a-option :value="item.key">
                <div class="option-item">
                  <img src="/icon/folder.svg" alt="看板空间"/>{{ item.title }}
                </div>
              </a-option>
            </div>
          </a-select>
        </a-col>
      </a-row>
      <a-row v-if=" dashboardPosition==='other'" class="item" align="center">
        <a-col :span="24">
          <a-tree-select
              v-model="projectPositionSelected"
              :data="projectPosition"
              checked-strategy="all"
              placeholder="请选择添加位置">
          </a-tree-select>
        </a-col>
      </a-row>
    </div>
    <div v-else>
      <a-select
          v-model="myPositionSelected"
          placeholder="请选择添加位置">
        <div v-for="item in myPosition" :key="item.key" class="select-options">
          <a-option :value="item.key">
            <div class="option-item">
              <img src="/icon/folder.svg" alt="看板空间"/>{{ item.title }}
            </div>
          </a-option>
        </div>
      </a-select>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}

.space-select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

.select-options {
  width: 100%;
  padding: 5px;

  .option-item {
    display: flex;
    gap: 5px;
    align-items: center;
  }
}
</style>