<template>
  <div class="card-content">
    <div class="card-toolbar">
      <div class="card-filter">
        <a-dropdown v-if="showTable" :update-at-scroll="true" @select="handleSelect">
          <span style="line-height: 24px;cursor: pointer;">{{ tableName }}</span>
          <template #content>
            <a-doption value="transform">转化</a-doption>
            <a-doption value="loss">流失</a-doption>
          </template>
        </a-dropdown>
        <a-divider v-if="showTable" :margin="8" direction="vertical"/>
        <date-picker :date-range="boardDate" position="tl" @date-pick="pickDate"/>
      </div>
      <div class="card-config">
        <a-select
            id="select"
            v-model="selectValue"
            :bordered="false"
            :style="{width:'92px',padding:'0 0 0 13px',position:'relative'}"
            default-value="trend-chart"
            @change="handleChange">
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/'+data?.value+'-chart.svg'" alt=""/>
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon>
          </template>
          <a-popover title="转化图" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option :value="ChartType.DISTRIBUTION">
              <template #icon>
                <img class="option-icon" src="/icon/distribution-chart.svg" alt=""/>
              </template>
              <template #default>转化图</template>
            </a-option>
            <template #content>
              展示每个步骤间的转化情况 <br>
              <img :src="trend" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="表格" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option :value="ChartType.TABLE">
              <template #icon>
                <img class="option-icon" src="/icon/table-chart.svg" alt=""/>
              </template>
              <template #default>表格</template>
            </a-option>
            <template #content>
              <div>
                <p>展示详细数据</p>
                <img :src="table" alt="" style="width: 90%;height: 90%;"/>
              </div>
            </template>
          </a-popover>
        </a-select>
      </div>
    </div>
    <a-alert v-if="reportData?.resultsExceedsLimit" style="margin-top: 5px;">
      因数据条数过多，优先展示前1000条数据
    </a-alert>
    <div class="data-chart" :style="{height: chartHeight}">
      <div class="chart-box">
        <TrendChart v-show="showDistribution" ref="trendChartRef" :report-analysis-data="reportData"/>
        <TableChart v-show="showTable" ref="tableRef" :size="254" :table-type="tableType" :report-analysis-data="reportData"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TrendChart from "@/views/dashboard/components/table-chart/funnel/FunnelChart.vue";
import TableChart from "@/views/dashboard/components/table-chart/funnel/FunnelTable.vue";
import {computed, ref, watch} from "vue";
import {table, trend} from "@/views/dashboard/components/img"
import {ReportQueryResponse} from "@/api/type";
import {ChartType} from "@/api/enum";
import DatePicker from "@/components/date-picker/index.vue";
import useReportDataStore from "@/store/modules/report";
import {DateRange} from "@/api/analyse/type";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  report: any;
  dataRangeData: any
}

const emits = defineEmits(['changeParams', 'update:modelValue', 'dateChange']);
const props = defineProps<Props>();
const reportDataStore = useReportDataStore()
// eslint-disable-next-line vue/no-dupe-keys
const trendChartRef = ref()
const reportData = ref<any>();
// 报表状态
const showTable = ref<boolean>(false);
const showDistribution = ref<boolean>(true);
const selectValue = ref<ChartType>(ChartType.DISTRIBUTION);
const boardDate = ref<DateRange>({...props.dataRangeData});
const tableName = ref('转化')
const tableType = ref('transform')
const tableRef = ref()

const handleSelect = (v) => {
  tableType.value = v
  tableName.value = v === 'transform' ? '转化' : '流失'
}
const handleChange = (value: ChartType, frash?: boolean) => {
  switch (value) {
    case ChartType.TABLE:
      showTable.value = true;
      showDistribution.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TABLE})
      }
      break;
    case ChartType.DISTRIBUTION:
      showTable.value = false;
      showDistribution.value = true
      if (!frash) {
        emits('changeParams', {chartType: ChartType.DISTRIBUTION})
      }
      break;
    default:
      break;
  }
};
const transformData = (data: ChartType) => {
  switch (data) {
    case ChartType.TABLE:
      selectValue.value = ChartType.TABLE
      handleChange(ChartType.TABLE, true)
      break
    case ChartType.DISTRIBUTION:
      selectValue.value = ChartType.DISTRIBUTION
      handleChange(ChartType.DISTRIBUTION, true)
      break
    default:
      break
  }
}
const pickDate = (date: any) => {
  boardDate.value = date
  reportDataStore.setDateRanges(props.report.objectCode, date);
  emits('dateChange', boardDate.value)
};
const exportXlsx = (date: any, name?: string) => {
  tableRef.value?.exportXlsx(date, name)
}
const init = () => {
  transformData(props.report.configParams?.chartType || ChartType.TREND)
}
init()

// 添加对 dataRangeData 的监听
watch(() => props.dataRangeData, (newVal) => {
  boardDate.value = newVal
}, {deep: true, immediate: true})
watch(() => props.reportAnalysisData, (newData) => {
  if (newData === undefined) {
    return
  }
  reportData.value = newData?.result;
})

// 图标高度
const chartHeight = computed(() => reportData.value?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)')
defineExpose({
  exportXlsx
})
</script>

<style scoped lang="less">
@import "@/views/dashboard/style/card.less";
</style>
