<template>
    <!-- 批量替换素材 -->
    <a-modal v-model:visible="modalVisible" :width="800" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="filter-box">
            <a-select
                v-model:model-value="filterParams.materialId"
                placeholder="请选择素材"
                allow-clear
                style="margin-right: 24px;width: 240px;">
            </a-select>
            <a-select
                v-model:model-value="filterParams.materialType"
                placeholder="请选择素材类型"
                multiple
                :max-tag-count="1"
                allow-clear
                style="width: 240px;">
                <a-option value="1">图片</a-option>
                <a-option value="2">视频</a-option>
                <a-option value="3">试玩素材</a-option>
            </a-select>
        </div>
        <a-divider />
        <a-alert>共选中 20 个广告，只能对其中 0 个系统创建的广告进行素材替换。如果存在相同的素材，它们将被合并展示。</a-alert>
        <div class="table-box">
            <a-button style="margin:12px 0;" :disabled="!selectedKeys.length">批量设置</a-button>
            <a-table
                v-model:selectedKeys="selectedKeys"
                :columns="columns"
                :data="tableData"
                :hoverable="true"
                sticky-header
                :table-layout-fixed="true"
                :column-resizable="true"
                :pagination="true"
                row-key="id"
                :row-selection="rowSelection"
            >
            </a-table>
        </div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('批量替换素材')
const filterParams = reactive({
    materialId:'',
    materialType:''
})
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const columns = [
    {
        title: '素材预览',
        dataIndex: 'thumbnail',
    },
    {
        title: '素材名称',
        dataIndex: 'materialName',
    },
    {
        title: '替换素材',
        dataIndex: 'replaceMaterial',
    },
]
const tableData = ref<any>([])
const emits = defineEmits(['updateData']);
const openModal = async () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.filter-box{
    display: flex;
    align-items: center;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>