<template>
  <div :val="computedValue">
    <div class="mb-8">
      <a-radio v-model="type" value="1" size="small">每周</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="5" size="small">不指定</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="2" size="small">周期</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">从星期</span>
      <a-input-number v-model="cycle.start" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">至星期</span>
      <a-input-number v-model="cycle.end" :min="2" :max="7" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="3" size="small">循环</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">从星期</span>
      <a-input-number v-model="loop.start" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '3'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">开始，每</span>
      <a-input-number v-model="loop.end" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '3'"></a-input-number>
      天执行一次
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="7" size="small">指定周</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">本月第</span>
      <a-input-number v-model="week.start" :min="1" :max="4" size="small" style="width: 100px;" @change="type = '7'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">周，星期</span>
      <a-input-number v-model="week.end" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '7'"></a-input-number>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="6" size="small">最后一周</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">星期</span>
      <a-input-number v-model="last" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '6'"></a-input-number>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="4" size="small">指定</a-radio>
      <a-checkbox-group v-model="appoint" style="margin-left: 0px; line-height: 25px;">
        <a-checkbox 
          v-for="option in weekOptions" 
          :key="option.value"
          :value="option.value"
          @change="type = '4'"
        >
          {{option.label}}
        </a-checkbox>
      </a-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '*'
  }
})

const emit = defineEmits(['update:modelValue'])

const type = ref('1') // 类型
const cycle = ref({ // 周期
  start: 1,
  end: 2
})
const loop = ref({ // 循环
  start: 1,
  end: 1
})
const week = ref({ // 指定周
  start: 1,
  end: 1
})
const last = ref(1) // 最后一周
const appoint = ref<string[]>([]) // 指定

const computedValue = computed(() => {
  let result = ''
  switch (type.value) {
    case '1': // 每周
      result = '*'
      break
    case '2': // 周期
      result = `${cycle.value.start}-${cycle.value.end}`
      break
    case '3': // 循环
      result = `${loop.value.start}/${loop.value.end}`
      break
    case '4': // 指定
      result = appoint.value.join(',')
      break
    case '5': // 不指定
      result = '?'
      break
    case '6': // 最后一周
      result = `${last.value}L`
      break
    case '7': // 指定周
      result = `${week.value.start}#${week.value.end}`
      break
    default:
      result = '*'
      break
  }
  emit('update:modelValue', result)
  return result
})
const weekOptions = computed(() => [
  { value: '1', label: '星期一' },
  { value: '2', label: '星期二' },
  { value: '3', label: '星期三' },
  { value: '4', label: '星期四' },
  { value: '5', label: '星期五' },
  { value: '6', label: '星期六' },
  { value: '7', label: '星期日' }
]);
watch(() => props.modelValue, (val) => {
  if (!val) {
    val = '*'
  }
  if (val === '*') {
    type.value = '1'
  } else if (val === '?') {
    type.value = '5'
  } else if (val.indexOf('-') !== -1) { // 2周期
    if (val.split('-').length === 2) {
      type.value = '2'
      cycle.value.start = parseInt(val.split('-')[0])
      cycle.value.end = parseInt(val.split('-')[1])
    }
  } else if (val.indexOf('/') !== -1) { // 3循环
    if (val.split('/').length === 2) {
      type.value = '3'
      loop.value.start = parseInt(val.split('/')[0])
      loop.value.end = parseInt(val.split('/')[1])
    }
  } else if (val.indexOf('#') !== -1) { // 7指定周
    if (val.split('#').length === 2) {
      type.value = '7'
      week.value.start = parseInt(val.split('#')[0])
      week.value.end = parseInt(val.split('#')[1])
    }
  } else if (val.indexOf('L') !== -1) { // 6最后一周
    type.value = '6'
    last.value = parseInt(val.replace('L', ''))
  } else if (val.indexOf(',') !== -1) { // 4指定
    type.value = '4'
    appoint.value = val.split(',')
  } else {
    type.value = '1'
  }
}, { immediate: true })
</script>

<style lang="less" scoped>
.mb-8 {
  margin-bottom: 8px;
}
</style>