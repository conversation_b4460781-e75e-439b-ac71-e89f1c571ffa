
<template>
    <a-modal v-model:visible="modalVisible" :width="560" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="account" label="授权账号" validate-trigger="blur">
                <a-input v-model="form.account" placeholder="请输入授权账号"/>
            </a-form-item>
            <a-form-item field="accessToken" label="access-token" validate-trigger="blur">
                <a-textarea v-model:model-value="form.accessToken" style="height: 120px;"/>
            </a-form-item>
            <a-form-item field="isEnabled" label="开启状态" validate-trigger="change">
                <a-select v-model:model-value="form.isEnabled" placeholder="请选择开启状态" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option :value="1">开启</a-option>
                    <a-option :value="0">关闭</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addAppsflyer, getAppsflyerInfo, updateAppsflyer} from "@/api/setting/api"

const modalVisible = ref(false)
const modalTitle = ref('添加Appsflyer授权')
const form = reactive({
    account:'',
    isEnabled:1,
    accessToken:'',
})
const rules = {
    account: [
        {
            required: true,
            message:'请输入授权账号'
        }
    ],
    isEnabled: [
        {
            required: true,
            message:'请选择开启状态'
        }
    ],
    accessToken: [
        {
            required: true,
            message:'请输入accessToken'
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);

const openModal = async (account?:string) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalTitle.value = account ? '编辑Appsflyer授权' :'添加Appsflyer授权'
    if(account){
        await getAppsflyerInfo(account).then(res => {
            const {account,accessToken,isEnabled} = res
            form.accessToken = accessToken
            form.account = account
            form.isEnabled = isEnabled
        })
    }
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            if(modalTitle.value === '编辑Appsflyer授权'){
                // 更新
                try {
                    await updateAppsflyer(form).then(res => {
                        Message.success('更新成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('更新失败:', error);
                }
            }else{
                // 添加
                try {
                    await addAppsflyer(form).then(res => {
                        Message.success('创建成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('创建失败:', error);
                }
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    // button{
    //     border-radius: 4px;
    // }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>