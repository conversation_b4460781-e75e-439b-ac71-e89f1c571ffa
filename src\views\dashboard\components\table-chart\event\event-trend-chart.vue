<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import useLoading from "@/hooks/loading";
import {WsEventAnalysisResultData} from "@/api/report/type";

interface Props {
  /**
   * 展示label
   */
  showLabel: boolean

  /**
   * 报表数据
   */
  reportAnalysisData: WsEventAnalysisResultData;
}

const props = defineProps<Props>()
const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([]);
const {loading, setLoading} = useLoading(true);

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}
const formatNumber = (num: string | number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const formatValue = (
    num: number | string | null | undefined,
    displayType?: { type: string; decimalNum: number }
): string => {
    if (num === null || num === undefined || num === '' || Number.isNaN(Number(num))) {
        return '';
    }
    const n = Number(num);
    if (Number.isNaN(n)) return '';
    let formattedValue = '';
    if (displayType?.type) {
        switch (displayType.type) {
            case 'default':
                formattedValue = n.toFixed(displayType.decimalNum);
                break;
            case 'percent':
                formattedValue = (n * 100).toFixed(displayType.decimalNum);
                break;
            default:
                formattedValue = n.toFixed(2);
        }
    } else {
        formattedValue = n.toFixed(2);
    }
    return formattedValue;
};
function renderChart(wsResultData: WsEventAnalysisResultData) {
  if (!wsResultData) {
    return
  }
  const xData = wsResultData?.x || []
  xAxis.value = [...xData]
  const data = [];
  const yLength = props.reportAnalysisData?.y.length

  props.reportAnalysisData?.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if ( yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {
        
        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        displayType:item.displayType,
        values:el.values,
        valuesCompared: el.valuesCompared
      });
    });
  });
  
  legendData.value = data.map(item => item.name);
  ySeries.value = data.map(item => {
    return {
      name: item.name,
      data: item.values,
      displayType: item.displayType,
      type: 'line',
      label: {show: false},
    }
  });
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData) => {
  renderChart(newData);
})

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '1%',
      top: '20',
      bottom: '50',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      type: 'scroll',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      offset: 2,
      data: xAxis.value,
      boundaryGap: false,
      axisLabel: {
        color: '#4E5969',
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        interval: (idx: number) => {
          if (idx === 0) return false;
          return true;
        },
        lineStyle: {
          color: '#E5E8EF',
        },
      },
      axisPointer: {
        show: true,
        lineStyle: {
          color: '#23ADFF',
          width: 2,
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        formatter(value: any, idx: number) {
          if (idx === 0) return value;
          if (value > 1000 && value < 10000) return `${value}`
          if (value >= 10000)  {
            const formattedValue= new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4
            }).format(value / 10000);
            return formattedValue.replace('.', ',');
          }
          return `${value}`;
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E8EF',
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine:true,
      axisPointer: {
        type: 'line'
      },
      formatter: (params) => {
          // 线图
          const seriesInfo = params.map((param, index) => {
            const matchingY = ySeries.value.find(y => y.name === param.seriesName);
            if (matchingY) {
                return `
                    <div style="display: flex; align-items: center; margin-bottom: 5px;">
                      <span style="flex-shrink: 0;">${param.marker}</span>
                      <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                      <span style="flex-shrink: 0;">${formatNumber(formatValue(param.value, matchingY.displayType))}${matchingY.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
                    </div>
                `;
            }
            return '';
          }).join('');
          const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${params[0].axisValue}</div>
              ${seriesInfo}
            </div>
          `;
          return tooltipHtml;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

const showLabels = ()=>{
  ySeries.value.forEach((item: any, index: number) => {
    item.label.show = !props.showLabel
  })
}

defineExpose({
  showLabels
})

</script>

<style scoped lang="less">
.chart-content {
  width: 100%;
  height: 100%;
}
</style>
