<script setup lang="ts">
import {ref, watch} from 'vue';
import {useRoute} from 'vue-router';
import draggable from 'vuedraggable';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import router from '@/router';
import _ from 'lodash';
import {Message} from '@arco-design/web-vue';
import {useStorage} from '@vueuse/core';
import {ROUTE_NAME} from '@/router/constants';

const route = useRoute();
  const queryParamsData = useStorage('resultValue', {});

  // const sqlQuery = ref(JSON.parse(route.query.sql));
  // const sqlQuery = ref(queryParamsData.value?.result);
  const sqlQuery = useStorage('sql-chart-query', queryParamsData.value?.result);
  const sqlChartValue = useStorage('sql-chart-value', []);

  const showHeader = route.query.showHeader === ''; // 判断显示页头
  const selectChart = ref();
  const visibleDel = ref<boolean>(false);

  const packet = ref([]);

  // echarts
  const xAxis = ref<string[]>([]);
  const ySeries = ref<any>([]);
  const ySeriesScatter = ref([]);
  const legendData = ref([]);

  const columns = ref<any>([]);
  const data = ref<any>([]);
  const columnsResult = ref([]);
  const dataResult = ref([]);

  const filterOptions = ref([]);
  const filterValues = ref([]);
  const filterSelectAll = ref([]);
  const filterValuesNumber = ref([]);
  const visibleSelect = ref([]);
  const searchValue = ref('');
  const isIconVisible = ref<Array<boolean>>(Array(0));
  const isNameVisible = ref<Array<boolean>>(Array(0));
  const itemsX = ref([]);
  const itemsY = ref([]);
  const itemssubY = ref([]);
  const itemsZ = ref([]);
  const itemsB = ref([]);
  const itemsScatterColor = ref([]);
  const itemsScatterSize = ref([]);
  const originalItemsB = ref([]);
  const itemsT = ref([]);
  const isChartype = ref({ value: 'trend-chart', label: '折线图' });
  const visible = ref(false);
  const visibleEditName = ref<Array<boolean>>(Array(0));
  const visibleEditNameY = ref<Array<boolean>>(Array(0));
  const visibleNumberY = ref<Array<boolean>>(Array(0));
  const visiblefilter = ref(false);
  const editNameValue = ref([]);
  const editNameValueY = ref([]);
  const isXY = ref(false);
  const YshowLable = ref(true);
  const XshowLable = ref(true);
  const XgroupNumber = ref(20000);
  const ZgroupNumber = ref(50);
  const axisLabelSpacing = ref('');
  const axisLabelSymbolSelect = ref('头');
  const axisLabelSymbolValue = ref('');
  const YaxisLabelMin = ref('');
  const YaxisLabelMax = ref('');
  const YaxisLabelAdaptive = ref(false);
  const showSqlresult = ref(false);
  const showSort = ref(false);
  const showsubYitems = ref(false);
  const showNumberCustom = ref(false);
  const NumberCustomType = ref('auto');
  const isconversionTable = ref(false); // 是否转换成表格
  const selectySeries = ref();
  const selectSort = ref();
  const sortValue = ref('none');
  const triggerValue = ref('axis');
  const triggerType = ref('category');
  const showTableHeader = ref(true);

  const showTitle = (index: number, value: boolean) => {
    isIconVisible.value[index] = value;
  };
  const showName = (index: number, value: boolean) => {
    isNameVisible.value[index] = value;
  };

  isIconVisible.value = Array(8).fill(false);

  isNameVisible.value = Array(itemsB.value.length).fill(false);

  visibleEditName.value = Array(itemsB.value.length).fill(false);
  visibleEditNameY.value = Array(itemsB.value.length).fill(false);
  visibleNumberY.value = Array(itemsB.value.length).fill(false);

  const back = () => {
    sqlChartValue.value = null;
    router.push({ name: ROUTE_NAME.ANALYSE_CUSTOM_SQL });
  };
  function graphicFactory(side: AnyObject) {
    return {
      type: 'text',
      bottom: '8',
      ...side,
      style: {
        text: '',
        textAlign: 'center',
        fill: '#4E5969',
        fontSize: 12,
      },
    };
  }

  const graphicElements = ref([graphicFactory({ left: '2.6%' }), graphicFactory({ right: 0 })]);

  const { chartOption } = useChartOption(() => {
    return {
      grid: {
        left: '4.6%',
        right: '4.6%',
        top: '60',
        bottom: '120',
      },
      legend: {
        data: legendData.value,
        bottom: '69',
        type: 'scroll',
      },
      xAxis: {
        type: 'category',
        offset: 2,
        data: xAxis.value,
        show: XshowLable.value,
        axisLabel: {
          color: '#4E5969',
          interval: axisLabelSpacing.value === '' ? Math.ceil(xAxis.value.length / 20) : axisLabelSpacing.value,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisPointer: {
          show: true,
          lineStyle: {
            color: '#23ADFF',
            width: 2,
          },
        },
      },
      yAxis: [
        {
          type: 'value',
          show: YshowLable.value,
          min: YaxisLabelMin.value === '' ? null : YaxisLabelMin.value,
          max: YaxisLabelMax.value === '' ? null : YaxisLabelMax.value,
        },
        {
          type: 'value',
          position: 'right',
        },
      ],
      tooltip: {
        trigger: triggerValue.value,
        axisPointer: {
          type: triggerType.value,
        },
      },
      graphic: {
        elements: graphicElements.value,
      },
      series: ySeries.value,
    };
  });

  // 判断值类型
  function checkType(value: any) {
    if (typeof value === 'number') {
      return 'number';
    }
    if (typeof value !== 'string') {
      return 'object';
    }
    const cleanedValue = value.replace(/:/g, '');

    if (!isNaN(cleanedValue) && cleanedValue !== '') {
      return 'number';
    }
    return 'string';
  }

  const refresh = () => {
    itemsB.value = sqlQuery.value.headers.map((item: any, index: number) => {
      return {
        name: item,
        id: index,
        type: checkType(sqlQuery.value.rows[0][index]),
      };
    });
    originalItemsB.value = [...itemsB.value];
  };

  const showResultTable = () => {
    showSqlresult.value = true;

    columnsResult.value = [];
    dataResult.value = [];

    // const keys = Object.keys(value[0])
    const keys = sqlQuery?.value.headers;
    keys.forEach((item, index) => {
      if (index === 0) {
        columnsResult.value.push({
          fixed: 'left',
          title: keys[index],
          dataIndex: keys[index],
        });
      } else {
        columnsResult.value.push({
          title: keys[index],
          dataIndex: keys[index],
          ellipsis: true,
          tooltip: true,
        });
      }
    });
    columnsResult.value.unshift({
      fixed: 'left',
      title: '序号',
      dataIndex: 'index',
    });

    dataResult.value = sqlQuery?.value.rows.map((subArray: any, index: number) => {
      const rowObject = subArray.reduce((accumulator: any, currentValue: any, index: number) => {
        accumulator[keys[index]] = currentValue;
        return accumulator;
      }, {});
      rowObject.index = index + 1;
      return rowObject;
    });
  };
  // const switchChart = (index:number) => {
  //   switch (index){
  //     case 0:
  //       isChartype.value = {value:'trend-chart',label:'折线图'}
  //       break
  //     case 1:
  //       isChartype.value = {value:'distribution-chart',label:'柱状图'}
  //       break
  //     case 2:
  //       isChartype.value = {value:'table-chart',label:'一维表'}
  //       break
  //     case 3:
  //       isChartype.value = {value:'pie-chart',label:'环图'}
  //       break
  //     case 4:
  //       isChartype.value = {value:'radar-chart',label:'组合图'}
  //       break
  //     case 5:
  //       isChartype.value = {value:'distribution-chart',label:'堆积柱状图'}
  //       break
  //     case 6:
  //       isChartype.value = {value:'table-chart',label:'交叉表'}
  //       break
  //     case 7:
  //       isChartype.value = {value:'gauge-chart',label:'散点图'}
  //       break
  //   }
  // }
  const CHART_TYPES = {
    0: { value: 'trend-chart', label: '折线图' },
    1: { value: 'distribution-chart', label: '柱状图' },
    2: { value: 'table-chart', label: '一维表' },
    3: { value: 'pie-chart', label: '环图' },
    4: { value: 'radar-chart', label: '组合图' },
    5: { value: 'distribution-chart', label: '堆积柱状图' },
    6: { value: 'table-chart', label: '交叉表' },
    7: { value: 'gauge-chart', label: '散点图' },
  } as const;

  // 切换图表类型
  const switchChart = (index: number) => {
    isChartype.value = CHART_TYPES[index] || CHART_TYPES[0];
  };

  const delsubY = () => {
    itemssubY.value.forEach((item: any, index: number) => {
      itemsY.value.push(item);
    });
    itemssubY.value.splice(0, itemssubY.value.length);
    showsubYitems.value = false;
    // ySeries.value = ySeries.value.filter((item:any,index:number)=>{
    //   return item.yAxisIndex!==1
    // })
  };

  const transTable = () => {
    delsubY();
    showTableHeader.value = trues;
    itemsZ.value.splice(0, itemsZ.value.length);
    columns.value = [];
    data.value = [];
    showsubYitems.value = false;
    itemsX.value.forEach((item: any, index: number) => {
      columns.value.push({ title: item.name, dataIndex: item.name, key: item.name, sortable: { sortDirections: ['ascend', 'descend'] } });
    });
    ySeries.value.forEach((series) => {
      columns.value.push({ title: series.name, dataIndex: series.name, key: series.name, sortable: { sortDirections: ['ascend', 'descend'] } });
    });

    xAxis.value.forEach((xValue, index) => {
      const rowData = {};
      rowData[itemsX.value[0].name] = xValue;
      ySeries.value.forEach((series) => {
        rowData[series.name] = series.data[index];
      });
      data.value.push(rowData);
    });
  };

  const transCrossTable = () => {
    delsubY();
    showTableHeader.value = true;
    itemsZ.value.splice(0, itemsZ.value.length);
    columns.value = [];
    data.value = [];
    showsubYitems.value = false;
    itemsX.value.forEach((item: any, index: number) => {
      columns.value.push({ title: item.name, dataIndex: item.name, key: item.name, sortable: { sortDirections: ['ascend', 'descend'] } });
    });
    ySeries.value.forEach((series) => {
      columns.value.push({ title: series.name, dataIndex: series.name, key: series.name, align: 'right', sortable: { sortDirections: ['ascend', 'descend'] } });
    });

    xAxis.value.forEach((xValue, index) => {
      const rowData = {};
      rowData[itemsX.value[0].name] = xValue;
      ySeries.value.forEach((series) => {
        rowData[series.name] = series.data[index];
      });
      data.value.push(rowData);
    });
  };

  const conversionTable = () => {
    if (!columns.value.length || !data.value.length) return;

    if (!isconversionTable.value) {
      showTableHeader.value = false;

      const transposedColumns = [
        { title: 'Field', dataIndex: 'field', key: 'field' },
        ...data.value.map((item, index) => ({
          title: `Row ${index + 1}`,
          dataIndex: `row${index + 1}`,
          key: `row${index + 1}`,
        })),
      ];

      const transposedData = columns.value.map((col) => {
        const newRow = { key: col.key, field: col.title };
        data.value.forEach((item, index) => {
          newRow[`row${index + 1}`] = item[col.dataIndex];
        });
        return newRow;
      });

      columns.value = transposedColumns;
      data.value = transposedData;

      isconversionTable.value = true;
    } else {
      isconversionTable.value = false;
      if (selectChart.value === 2) {
        transTable();
      } else {
        transCrossTable();
      }
    }
  };

  const transBar = () => {
    XshowLable.value = true;
    triggerType.value = 'shadow';
    triggerValue.value = 'axis';
    ySeries.value.forEach((series) => {
      series.type = 'bar';
      series.stack = '';
      series.label = {
        show: false,
      };
    });
  };
  const transLine = () => {
    triggerType.value = 'line';
    XshowLable.value = true;
    triggerValue.value = 'axis';
    ySeries.value.forEach((series) => {
      series.type = 'line';
      series.stack = '';
      series.label = {
        show: false,
      };
    });
  };

  const transPie = () => {
    delsubY();
    itemsZ.value.splice(0, itemsZ.value.length);
    if (itemsY.value.length > 1) {
      itemsY.value.splice(1);
    }
    showsubYitems.value = false;
    XshowLable.value = false;
    triggerValue.value = 'item';
    triggerType.value = 'none';
    chartOption.value.xAxis.axisPointer.show = false;
    // 修改tooltip格式为简洁样式
    chartOption.value.tooltip = {
      trigger: 'item',
      formatter: (params) => {
        // 使用seriesName作为标题
        const title = params.seriesName;
        return `
          <div style="padding: 3px 6px;">
            <div style="font-size: 14px; color: #666;">${title}</div>
            <div style="display: flex; align-items: center; margin-top: 4px;">
              <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${params.color}; margin-right: 6px;"></span>
              <span style="font-size: 14px;">${xAxis.value[params.dataIndex]}</span>
              <span style="margin-left: 10px; font-weight: bold;">${params.value}</span>
            </div>
          </div>
        `;
      }
    };
    // 处理饼图数据，确保包含正确的name属性
    ySeries.value.forEach((series, index) => {
      series.type = 'pie';
      series.radius = ['40%', '70%'];
      series.stack = '';
      series.label = {
        show: false,
      };
      if (series.data && Array.isArray(series.data)) {
        // 创建新的数据数组，包含正确的name属性
        const newData = [];
        for (let i = 0; i < series.data.length; i++) {
          const value = typeof series.data[i] === 'object' ? series.data[i].value : series.data[i];
          newData.push({
            name: xAxis.value[i],
            value: value
          });
        }
        series.data = newData;
      }
    });
  };
  const transLineBar = () => {
    itemsZ.value.splice(0, itemsZ.value.length);
    triggerType.value = 'shadow';
    triggerValue.value = 'axis';
    XshowLable.value = true;
    ySeries.value.forEach((series, index) => {
      if (series.yAxisIndex === 0) {
        series.type = 'bar';
        series.stack = '';
      } else {
        series.type = 'line';
        series.stack = '';
      }
    });
    showsubYitems.value = true;
  };
  const transStackBar = () => {
    XshowLable.value = true;
    ySeries.value.forEach((series, index) => {
      series.type = 'bar';
      series.stack = 'Ad';
    });
  };
  const transScatter = () => {
    delsubY();
    itemsZ.value.splice(0, itemsZ.value.length);
    triggerType.value = 'line';
    XshowLable.value = true;
    triggerValue.value = 'axis';
    showsubYitems.value = false;

    ySeries.value.forEach((series) => {
      series.type = 'scatter';
      series.stack = '';
      series.label = {
        show: false,
      };
    });
  };

  const changeSelectChart = (index: number) => {
    selectChart.value = index;
    switch (index) {
      case 0:
        transLine();
        break;
      case 1:
        transBar();
        break;
      case 2:
        transTable();
        break;
      case 3:
        transPie();
        break;
      case 4:
        transLineBar();
        break;
      case 5:
        transStackBar();
        break;
      case 6:
        transCrossTable();
        break;
      case 7:
        transScatter();
        break;
      default:
        break;
    }

    switchChart(index);
    visible.value = false;
  };

  // const removeAt =  (type:string,idx:number)=> {
  //   switch (type){
  //     case 'x':
  //       itemsX.value.splice(idx, 1);
  //       break
  //     case 'y':
  //       itemsY.value.splice(idx, 1);
  //       break
  //     case 'z':
  //       itemsZ.value.splice(idx, 1);
  //       break
  //     case 'b':
  //       itemsB.value.splice(idx, 1);
  //       break
  //     case 't':
  //       itemsT.value.splice(idx, 1);
  //       break
  //     case 'subY':
  //       itemssubY.value.splice(idx, 1);
  //       break
  //     case 'Color':
  //       itemsScatterColor.value.splice(idx, 1);
  //       break
  //     case 'Size':
  //       itemsScatterSize.value.splice(idx, 1);
  //       break
  //   }
  // }
  const ITEM_TYPES = {
    x: itemsX,
    y: itemsY,
    z: itemsZ,
    b: itemsB,
    t: itemsT,
    subY: itemssubY,
    Color: itemsScatterColor,
    Size: itemsScatterSize,
  } as const;

  const removeAt = (type: keyof typeof ITEM_TYPES, idx: number) => {
    ITEM_TYPES[type].value.splice(idx, 1);
  };
  const log = (value: any) => {};

  const radioXY = (value: string) => {
    isXY.value = value !== 'X';
  };

  function mergeArrays(arr1: any, arr2: any) {
    return arr1.map((el: any, index: number) => `${el}${arr2[index]}`);
  }

  const pushValue = () => {
    if (selectChart.value === 2 || selectChart.value === 6) {
      sqlChartValue.value = ['table', columns.value, data.value, !isconversionTable.value, isChartype.value];
      router.push({ name: ROUTE_NAME.ANALYSE_CUSTOM_SQL });
    } else {
      sqlChartValue.value = ['chart', xAxis.value, ySeries.value, legendData.value, isChartype.value];
      router.push({ name: ROUTE_NAME.ANALYSE_CUSTOM_SQL });
    }
  };

  const Xgroup = (value: any) => {
    if (value < xAxis.value.length) {
      xAxis.value = xAxis.value.slice(0, value);
    } else {
      itemsX.value.push({});
      itemsX.value.pop();
    }
  };

  const Zgroup = (value: any) => {
    if (value < ySeries.value.length) {
      ySeries.value = ySeries.value.slice(0, value);
      legendData.value = legendData.value.slice(0, value);
    } else {
      itemsY.value.push({});
      itemsY.value.pop();
    }
  };

  const axisLabelSymbol = (value: any) => {
    if (value === '') {
      itemsX.value.push({});
      itemsX.value.pop();
    }

    switch (axisLabelSymbolSelect.value) {
      case '头':
        xAxis.value.forEach((item: any, index: number) => {
          xAxis.value[index] = value + item;
        });
        break;
      case '尾':
        xAxis.value.forEach((item: any, index: number) => {
          xAxis.value[index] = item + value;
        });
        break;
      case '头尾':
        xAxis.value.forEach((item: any, index: number) => {
          xAxis.value[index] = value + item + value;
        });
        break;
      default:
        break;
    }
  };

  const axisLabelSymbolChange = (value: any) => {
    axisLabelSymbol(axisLabelSymbolValue.value);
  };

  const YaxisLabelAdaptiveChange = (value: any) => {
    if (value) {
      YaxisLabelMin.value = '';
      YaxisLabelMax.value = '';
    }
  };

  const sortItem = () => {
    showSort.value = true;
  };

  const handleChangeSort = (value: string | number | boolean) => {};

  const addsubY = () => {
    showsubYitems.value = true;
  };

  const submitSort = () => {
    showSort.value = false;
    switch (sortValue.value) {
      case 'ascending':
        ySeries.value.forEach((series, index) => {
          if (series.name === selectSort.value) {
            series.data.sort((a: any, b: any) => a - b);
          }
        });
        break;
      case 'descending':
        ySeries.value.forEach((series, index) => {
          if (series.name === selectSort.value) {
            series.data.sort((a: any, b: any) => b - a);
          }
        });
        break;
      case 'none':
        refresh();
        itemsY.value.push({});
        itemsY.value.pop();
        break;
      default:
        break;
    }
  };

  // const typetransName = (type:number)=>{
  //   switch (type) {
  //     case 0:
  //       return '折线图'
  //     case 1:
  //       return '柱状图'
  //     case 2:
  //       return '一维表'
  //     case 3:
  //       return '环图'
  //     case 4:
  //       return '组合图'
  //     case 5:
  //       return '堆积柱状图'
  //     case 6:
  //       return '交叉表'
  //     case 7:
  //       return '散点图'
  //     default:
  //       return '未知类型'
  //   }
  // }
  const typetransName = (type: number) => {
    return CHART_TYPES[type]?.label || '未知类型';
  };
  const searchItemsB = (value: string) => {
    if (value.length > 0) {
      itemsB.value = originalItemsB.value.filter((item: any) => {
        return item.name.includes(value);
      });
    } else {
      itemsB.value = originalItemsB.value;
    }
  };

  const editName = (value: any, index: number) => {
    visibleEditName.value[index] = false;
    itemsB.value[index].name = editNameValue.value[index] ? editNameValue.value[index] : itemsB.value[index].name;
  };
  const editNameY = (value: any, index: number) => {
    visibleEditNameY.value[index] = false;
    itemsB.value.forEach((item: any, index2: number) => {
      if (value.id === item.id) {
        item.name = editNameValueY.value[index] ? editNameValueY.value[index] : item.name;
      }
    });
    itemsY.value[index].name = editNameValueY.value[index] ? editNameValueY.value[index] : itemsY.value[index].name;
  };

  const filterItems = (index: number) => {
    visibleSelect.value[index] = false;
    if (filterValues.value[index].length > 0) {
      ySeries.value.forEach((series, index2) => {
        if (series.name === itemsT.value[index].name) {
          series.data = filterValues.value[index];
        }
      });
    } else {
      ySeries.value.forEach((series, index2) => {
        if (series.name === itemsT.value[index].name) {
          series.data = filterOptions.value[index];
        }
      });
    }
  };

  const filterItemsNumber = (index: number) => {
    switch (filterValues.value[index]) {
      case '>':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item > filterValuesNumber.value[index];
            });
          }
        });
        break;
      case '>=':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item >= filterValuesNumber.value[index];
            });
          }
        });
        break;
      case '<':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item < filterValuesNumber.value[index];
            });
          }
        });
        break;
      case '<=':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item <= filterValuesNumber.value[index];
            });
          }
        });
        break;
      case '===':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item === filterValuesNumber.value[index];
            });
          }
        });
        break;
      case '!==':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return item !== filterValuesNumber.value[index];
            });
          }
        });
        break;
      case 'in':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return _.isEmpty(item);
            });
          }
        });
        break;
      case 'not':
        ySeries.value.forEach((series, index2) => {
          if (series.name === itemsT.value[index].name) {
            series.data = series.data.filter((item: any) => {
              return _.isEmpty(item);
            });
          }
        });
        break;
      default:
        break;
    }
  };

  const filterValuesSelect = (index: number) => {
    if (!_.isEmpty(filterValuesNumber.value[index])) {
      filterItemsNumber(index);
    }
  };

  const selectAll = (index: number) => {
    if (filterSelectAll.value[index]) {
      filterValues.value[index] = filterOptions.value[index];
    } else {
      filterValues.value[index] = [];
    }
  };

  const selectFilterAll = (index: number) => {
    filterValues.value[index].length === filterOptions.value[index].length ? (filterSelectAll.value[index] = true) : (filterSelectAll.value[index] = false);
  };

  // 数组去重
  function removeDuplicates(arr: any) {
    return [...new Set(arr)];
  }

  const addFilterItemT = (index: number) => {
    itemsT.value.push(itemsY.value[index]);
  };

  const NumberPolymerization = (index: number, value?: any) => {
    visibleNumberY.value[index] = false;
    // if (value) {
    //   ySeries.value.forEach((series, index2) => {
    //     if (series.name === itemsT.value[index].name) {
    //       series.data = series.data.map((item: any) => {
    //         return item + value
    //       })
    //     }
    //   })
    // }
  };
  const NumberFormat = (index: number, type: number, value?: any) => {
    visibleNumberY.value[index] = false;

    switch (type) {
      case 0:
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === itemsY.value[index].name && itemsY.value[index].type === 'number') {
            series.data = series.data.map((item: any) => {
              return Math.floor(item);
            });
          }
        });
        break;
      case 1:
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === itemsY.value[index].name && itemsY.value[index].type === 'number') {
            series.data = series.data.map((item: any) => {
              return item.toFixed(1);
            });
          }
        });
        break;
      case 2:
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === itemsY.value[index].name && itemsY.value[index].type === 'number') {
            series.data = series.data.map((item: any) => {
              return item.toFixed(2);
            });
          }
        });
        break;
      case 3:
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === itemsY.value[index].name && itemsY.value[index].type === 'number') {
            series.data = series.data.map((item: any) => {
              return item.toFixed(3);
            });
          }
        });
        break;
      case 4:
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === itemsY.value[index].name && itemsY.value[index].type === 'number') {
            series.data = series.data.map((item: any) => {
              return item.toLocaleString();
            });
          }
        });
        break;
      default:
        break;
    }
  };

  refresh();
  changeSelectChart(0); // 初始默认折线图

  watch(itemsX.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 0) {
      xAxis.value = [];
      newValue.forEach((item: any, index: number) => {
        if (index === 0) {
          sqlQuery.value.rows.forEach((item2: any, index2: number) => {
            xAxis.value.push(sqlQuery.value.rows[index2][newValue[index].id]);
          });
        } else {
          const newArr = sqlQuery.value.rows.map((item2: any, index2: number) => {
            return sqlQuery.value.rows[index2][newValue[index].id];
          });
          xAxis.value = mergeArrays(xAxis.value, newArr);
        }
      });
      switch (selectChart.value) {
        case 0:
          transLine();
          break;
        case 1:
          transBar();
          break;
        case 2:
          transTable();
          break;
        case 3:
          transPie();
          break;
        case 4:
          transLineBar();
          break;
        case 5:
          transStackBar();
          break;
        case 6:
          transCrossTable();
          break;
        case 7:
          transScatter();
          break;
        default:
          break;
      }
    } else {
      xAxis.value = [];
    }
  });

  watch(itemsY.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 0) {
      // 过滤次Y轴数据
      ySeries.value = ySeries.value.filter((item: any, index: number) => {
        return item.yAxisIndex !== 0;
      });

      newValue.forEach((item: any, index: number) => {
        ySeries.value.push({
          data: sqlQuery.value.rows.map((item2: any, index2: number) => {
            return sqlQuery.value.rows[index2][newValue[index].id];
          }),
          name: item.name,
          type: 'line',
          yAxisIndex: 0,
          barMaxWidth: 40,
          stack: '',
          markPoint: { data: [] },
          label: {
            show: false,
          },
        });
        legendData.value.push(item.name);
      });

      // 子分组项
      if (packet.value.length > 0) {
        legendData.value = [];

        let legendDataArr = [];
        sqlQuery.value.headers.forEach((item: any, index: number) => {
          if (item === packet.value[0].name) {
            sqlQuery.value.rows.forEach((item2: any, index2: number) => {
              legendDataArr.push(item2[index].toString());
            });
          }
        });
        legendData.value = removeDuplicates(legendDataArr);

        ySeries.value = [];
        let ySeriesArr = new Array(legendData.value.length);
        packet.value.forEach((item: any, index: number) => {
          if (index === 0) {
            legendData.value.forEach((item2: any, index2: number) => {
              ySeriesArr = [];
              ySeriesArr[index2] = item2;
              ySeries.value.push({
                data: ySeriesArr,
                name: item2.toString(),
                type: 'line',
                yAxisIndex: 0,
                barMaxWidth: 40,
                stack: '',
                markPoint: { data: [] },
                label: {
                  show: false,
                },
              });
            });
          } else {
            sqlQuery.value.headers.forEach((item: any, index: number) => {
              if (item === packet.value[0].name) {
                sqlQuery.value.rows.forEach((item2: any, index2: number) => {
                  legendDataArr[index2] = legendDataArr[index2] + ',' + item2[index].toString();
                  ySeries.value[index2].name = legendDataArr[index2];
                });
              }
            });
            legendData.value = removeDuplicates(legendDataArr);
          }
        });
      }

      switch (selectChart.value) {
        case 0:
          transLine();
          break;
        case 1:
          transBar();
          break;
        case 2:
          transTable();
          break;
        case 3:
          transPie();
          break;
        case 4:
          transLineBar();
          break;
        case 5:
          transStackBar();
          break;
        case 6:
          transCrossTable();
          break;
        case 7:
          transScatter();
          break;
        default:
          break;
      }
    } else {
      if (itemssubY.value.length === 0) {
        ySeries.value = [];
        legendData.value = [];
      }
      ySeries.value = ySeries.value.filter((item: any, index: number) => {
        return item.yAxisIndex !== 0;
      });
    }
  });

  watch(itemssubY.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 0) {
      ySeries.value = ySeries.value.filter((item: any, index: number) => {
        return item.yAxisIndex !== 1;
      });

      newValue.forEach((item: any, index: number) => {
        ySeries.value.push({
          data: sqlQuery.value.rows.map((item2: any, index2: number) => {
            return sqlQuery.value.rows[index2][newValue[index].id];
          }),
          name: item.name,
          type: 'line',
          yAxisIndex: 1,
          barMaxWidth: 40,
          stack: '',
          markPoint: { data: [] },
          label: {
            show: false,
          },
        });
        legendData.value.push(item.name);
      });
      switch (selectChart.value) {
        case 0:
          transLine();
          break;
        case 1:
          transBar();
          break;
        case 2:
          transTable();
          break;
        case 3:
          transPie();
          break;
        case 4:
          transLineBar();
          break;
        case 5:
          transStackBar();
          break;
        case 6:
          transCrossTable();
          break;
        case 7:
          transScatter();
          break;
        default:
          break;
      }
    } else {
      if (itemsY.value.length === 0) {
        ySeries.value = [];
        legendData.value = [];
      }
      ySeries.value = ySeries.value.filter((item: any, index: number) => {
        return item.yAxisIndex !== 1;
      });
    }
  });

  watch(itemsT.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 0) {
      const seen = new Set();

      let IsNewValue = true;
      // 遍历新值并检查重复
      for (let i = 0; i < newValue.length; i++) {
        const item = newValue[i];
        if (seen.has(item)) {
          itemsT.value.splice(i, 1);
          IsNewValue = false;
          i--; // 调整索引，避免跳过下一个元素
        } else {
          seen.add(item);
        }
      }
      // 如果有重复，则不执行数据添加
      if (!IsNewValue) {
        return;
      }

      filterOptions.value = [];
      filterValues.value = [];
      filterValuesNumber.value = [];

      newValue.forEach((item: any, index: number) => {
        if (item.type === 'number') {
          filterOptions.value.push(
            sqlQuery.value.rows.map((item2: any, index2: number) => {
              return sqlQuery.value.rows[index2][item.id];
            })
          );
        } else if (item.type === 'string') {
          const options = Array.from(
            new Set(
              sqlQuery.value.rows.map((item2: any, index2: number) => {
                return sqlQuery.value.rows[index2][item.id];
              })
            )
          );
          filterOptions.value.push(options);
        }
      });
    } else {
      filterOptions.value = [];
      filterValues.value = [];
      filterValuesNumber.value = [];
      filterSelectAll.value = [];
      itemsY.value.push({});
      itemsY.value.pop();
    }
  });

  watch(itemsZ.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 0) {
      if ((itemsY.value.length > 1 && selectChart.value === 1) || (selectChart.value === 5 && itemsY.value.length > 1)) {
        Message.info('柱状图多指标场景下不可添加子分组');
        itemsZ.value.pop();
        return;
      }
      packet.value = newValue;
      itemsY.value.push({});
      itemsY.value.pop();
    } else {
      packet.value = [];
      itemsY.value.push({});
      itemsY.value.pop();
    }
  });

  watch(visiblefilter, (newValue: any, oldValue: any) => {
    if (!newValue) {
      visibleSelect.value.forEach((item: any, index: number) => {
        visibleSelect.value[index] = false;
      });
    }
  });

  watch(itemsScatterColor.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 1) {
      itemsY.value.push({});
      itemsY.value.pop();
      let newArr = itemsScatterColor.value.slice(-1);
      itemsScatterColor.value.splice(0, itemsScatterColor.value.length);
      itemsScatterColor.value.push(newArr[0]);
    } else if (newValue.length === 1) {
      newValue.forEach((item: any, index: number) => {
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === item.name) {
            series.itemStyle = {
              color: '#fba861',
            };
          }
        });
      });
    } else {
      itemsY.value.push({});
      itemsY.value.pop();
    }
  });

  watch(itemsScatterSize.value, (newValue: any, oldValue: any) => {
    if (newValue.length > 1) {
      itemsY.value.push({});
      itemsY.value.pop();
      let newArr = itemsScatterColor.value.slice(-1);
      itemsScatterColor.value.splice(0, itemsScatterColor.value.length);
      itemsScatterColor.value.push(newArr[0]);
    } else if (newValue.length === 1) {
      newValue.forEach((item: any, index: number) => {
        ySeries.value.forEach((series: any, index2: number) => {
          if (series.name === item.name) {
            series.symbolSize = 30;
          }
        });
      });
    } else {
      itemsY.value.push({});
      itemsY.value.pop();
    }
  });
</script>

<template>
  <div>
    <a-page-header v-if="showHeader" :style="{ background: 'var(--color-bg-2)' }" title="SQL图表" @back="back">
      <template #extra>
        <a-space>
          <a-button @click="visibleDel = true">删除</a-button>
          <a-button type="primary" :disabled="itemsX.length === 0 || itemsY.length === 0" @click="pushValue">保存</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div style="margin: 20px; background-color: #fff; display: flex">
      <div style="flex: 3; border-right: 1px solid #e3e4e5; width: calc(100% - 414px); height: 90vh">
        <div v-show="true">
          <div style="display: flex; justify-content: space-between; margin: 15px">
            <div>
              <span style="color: #212121; font-size: 16px; font-weight: 500">图表展示区</span>
            </div>
            <div>
              <a-space>
                <a-trigger v-model:popup-visible="visiblefilter" trigger="click" :unmount-on-close="false">
                  <div v-show="itemsT.length !== 0" style="margin-right: 10px" class="btn">
                    <a-space>
                      <icon-filter /><span> 图内筛选器 ( {{ filterValues.length }}/{{ itemsT.length }} )</span>
                    </a-space>
                  </div>
                  <template #content>
                    <div v-for="(items, index) in itemsT" :key="index" style="box-shadow: var(--tant-small-shadow-small-overall); border: 3px; background-color: #fff">
                      <a-space style="padding: 10px; display: flex; justify-content: space-between">
                        <span style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 8px">{{ items.name }}</span>
                        <div v-if="items.type === 'number'">
                          <a-select v-model="filterValues[index]" @change="filterValuesSelect(index)" :style="{ width: '180px' }" placeholder="不限">
                            <a-option value="===">等于</a-option>
                            <a-option value="!==">不等于</a-option>
                            <a-option value="<">小于</a-option>
                            <a-option value="<=">小于等于</a-option>
                            <a-option value=">">大于</a-option>
                            <a-option value=">=">大于等于</a-option>
                            <a-option value="in">有值</a-option>
                            <a-option value="not">无值</a-option>
                          </a-select>
                          <a-input-number v-show="filterValues[index] !== 'in' && filterValues[index] !== 'not'" v-model="filterValuesNumber[index]" @blur="filterItemsNumber(index)" style="width: 120px; margin-left: 10px" placeholder="请输入数字" />
                        </div>

                        <div v-if="items.type === 'string'">
                          <a-select
                            v-model="filterValues[index]"
                            :popup-visible="visibleSelect[index]"
                            @click="visibleSelect[index] = !visibleSelect[index]"
                            @change="selectFilterAll(index)"
                            style="border: 1px solid var(--tant-border-color-border1-1)"
                            :style="{ width: '310px', backgroundColor: '#fff' }"
                            placeholder="不限"
                            multiple
                            :max-tag-count="2"
                          >
                            <a-option v-for="(option, index2) in filterOptions[index]" :key="index2" :value="option">{{ option }}</a-option>
                            <template #footer>
                              <div style="display: flex; justify-content: space-between; align-items: center">
                                <div style="padding: 6px 12px">
                                  <a-checkbox v-model="filterSelectAll[index]" @change="selectAll(index)">全选</a-checkbox>
                                </div>
                                <div>
                                  <a-button @click="visibleSelect[index] = false" size="mini">取消</a-button>
                                  <a-button type="primary" @click="filterItems(index)" size="mini" style="margin-left: 10px; margin-right: 5px">确定</a-button>
                                </div>
                              </div>
                            </template>
                          </a-select>
                        </div>

                        <div v-if="items.type === 'object'">
                          <a-select v-model="filterValues[index]" @change="selectFilterAll(index)" style="border: 1px solid var(--tant-border-color-border1-1)" :style="{ width: '310px', backgroundColor: '#fff' }" placeholder="不限" multiple :max-tag-count="2">
                            <a-option v-for="(option, index2) in filterOptions[index]" :key="index2" :value="option">{{ option }}</a-option>
                            <template #footer>
                              <div style="display: flex; justify-content: space-between; align-items: center">
                                <div style="padding: 6px 12px">
                                  <a-checkbox v-model="filterSelectAll[index]" @change="selectAll(index)">全选</a-checkbox>
                                </div>
                                <div>
                                  <a-button @click="visiblefilter = false" size="mini">取消</a-button>
                                  <a-button type="primary" @click="filterItems(index)" size="mini" style="margin-left: 10px">确定</a-button>
                                </div>
                              </div>
                            </template>
                          </a-select>
                        </div>
                      </a-space>
                    </div>
                  </template>
                </a-trigger>
                <a-trigger v-model:popup-visible="visible" trigger="click" :unmount-on-close="false" update-at-scroll>
                  <a-space>
                    <div style="margin-right: 20px" class="btn">
                      <a-space>
                        <img class="option-icon" :src="'/icon/' + isChartype.value + '.svg'" alt="" /><span> {{ isChartype.label }}</span>
                      </a-space>
                    </div>
                  </a-space>
                  <template #content>
                    <div style="box-shadow: var(--tant-small-shadow-small-overall); border-right: 3px; background-color: #fff">
                      <div style="display: flex; padding: 24px; justify-content: space-between; flex-wrap: wrap; width: 320px">
                        <div style="flex: 1">
                          <div
                            class="divItem"
                            :class="selectChart === 0 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/foldline.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(0, true)"
                            @mouseleave="showTitle(0, false)"
                            @click="changeSelectChart(0)"
                          >
                            <span v-show="isIconVisible[0] || selectChart === 0" class="Chartspan">折线图</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 1 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/columnar.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(1, true)"
                            @mouseleave="showTitle(1, false)"
                            @click="changeSelectChart(1)"
                          >
                            <span v-show="isIconVisible[1] || selectChart === 1" class="Chartspan">柱状图</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 2 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/table1.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(2, true)"
                            @mouseleave="showTitle(2, false)"
                            @click="changeSelectChart(2)"
                          >
                            <span v-show="isIconVisible[2] || selectChart === 2" class="Chartspan">一维表</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 3 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/loop.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(3, true)"
                            @mouseleave="showTitle(3, false)"
                            @click="changeSelectChart(3)"
                          >
                            <span v-show="isIconVisible[3] || selectChart === 3" class="Chartspan">环图</span>
                          </div>
                        </div>

                        <div style="flex: 1">
                          <div
                            class="divItem"
                            :class="selectChart === 4 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/combination.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(4, true)"
                            @mouseleave="showTitle(4, false)"
                            @click="changeSelectChart(4)"
                          >
                            <span v-show="isIconVisible[4] || selectChart === 4" class="Chartspan">组合图</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 5 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/piledcolumn.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(5, true)"
                            @mouseleave="showTitle(5, false)"
                            @click="changeSelectChart(5)"
                          >
                            <span v-show="isIconVisible[5] || selectChart === 5" class="Chartspan">堆积柱状图</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 6 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/cross.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(6, true)"
                            @mouseleave="showTitle(6, false)"
                            @click="changeSelectChart(6)"
                          >
                            <span v-show="isIconVisible[6] || selectChart === 6" class="Chartspan">交叉表</span>
                          </div>
                          <div
                            class="divItem"
                            :class="selectChart === 7 ? 'itemActive' : ''"
                            style="background-image: url('/image/sql/scatter.svg'); background-size: 70%; background-position: center; background-repeat: no-repeat"
                            @mouseover="showTitle(7, true)"
                            @mouseleave="showTitle(7, false)"
                            @click="changeSelectChart(7)"
                          >
                            <span v-show="isIconVisible[7] || selectChart === 7" class="Chartspan">散点图</span>
                          </div>
                        </div>
                      </div>

                      <div v-show="isIconVisible[0] ? true : isIconVisible.every((item) => item === false) ? selectChart === 0 : false" class="described">
                        <span style="color: #333333; font-weight: 500">折线图</span><br /><br />
                        <span style="color: #7e7f80">
                          展示数据随时间间隔或有序类别的变化 <br />
                          推荐X轴上至少1个分组，Y轴上至少1个指标
                        </span>
                      </div>
                      <div v-show="isIconVisible[1] ? true : isIconVisible.every((item) => item === false) ? selectChart === 1 : false" class="described">
                        <span style="color: #333333; font-weight: 500">柱状图</span><br /><br />
                        <span style="color: #7e7f80">
                          进行多个分类的对比，或同类别各变量之间的对比 <br />
                          推荐X轴上至少1个分组，Y轴上至少1个指标
                        </span>
                      </div>
                      <div v-show="isIconVisible[2] ? true : isIconVisible.every((item) => item === false) ? selectChart === 2 : false" class="described">
                        <span style="color: #333333; font-weight: 500">一维表</span><br /><br />
                        <span style="color: #7e7f80">
                          以表格形式展示分组聚合结果 <br />
                          推荐至少 1 个分组，至少 1 个指标
                        </span>
                      </div>
                      <div v-show="isIconVisible[3] ? true : isIconVisible.every((item) => item === false) ? selectChart === 3 : false" class="described">
                        <span style="color: #333333; font-weight: 500">环图</span><br /><br />
                        <span style="color: #7e7f80">
                          比较各类别在整体中的占比情况 <br />
                          推荐1个指标，和至少1个分组
                        </span>
                      </div>
                      <div v-show="isIconVisible[4] ? true : isIconVisible.every((item) => item === false) ? selectChart === 4 : false" class="described">
                        <span style="color: #333333; font-weight: 500">组合图</span><br /><br />
                        <span style="color: #7e7f80">
                          在一个图表中应用多种图表类型的元素来同时展示多组数据 <br />
                          推荐X轴上至少1个分组，Y轴上至少2个指标
                        </span>
                      </div>
                      <div v-show="isIconVisible[5] ? true : isIconVisible.every((item) => item === false) ? selectChart === 5 : false" class="described">
                        <span style="color: #333333; font-weight: 500">堆积柱状图</span><br /><br />
                        <span style="color: #7e7f80">
                          展示总体与细分指标的变动趋势 <br />
                          推荐X轴上至少1个分组，1个子分组，Y轴上至少一个指标
                        </span>
                      </div>
                      <div v-show="isIconVisible[6] ? true : isIconVisible.every((item) => item === false) ? selectChart === 6 : false" class="described">
                        <span style="color: #333333; font-weight: 500">交叉表</span><br /><br />
                        <span style="color: #7e7f80"> 按照行分组项和列分组项对指标数据汇总统计 </span>
                      </div>
                      <div v-show="isIconVisible[7] ? true : isIconVisible.every((item) => item === false) ? selectChart === 7 : false" class="described">
                        <span style="color: #333333; font-weight: 500">散点图</span><br /><br />
                        <span style="color: #7e7f80">
                          描述变量之间是否存在相关性 <br />
                          X轴限制1个数值，Y轴限制1个数值
                        </span>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </a-space>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 15px">
            <div>
              <span style="color: #212121; font-size: 16px; font-weight: 500"></span>
            </div>
            <div>
              <p v-show="selectChart === 0 || selectChart === 1 || selectChart === 3 || selectChart === 4 || selectChart === 5 || selectChart === 7" style="margin-right: 18px; color: #7e7f80">结果共{{ xAxis.length }}个分组项</p>
            </div>
          </div>
          <div v-show="itemsY.length == 0 && itemssubY.length == 0" style="width: 100%; text-align: center; margin-top: 25vh">
            <br />
            <img src="/sql/tiki.png" style="width: 400px" /><br /><br />
            <span v-show="itemsX.length == 0 && itemsY.length == 0 && itemssubY.length == 0" style="color: #212121; font-size: 15px; font-weight: 500">拖拽右侧字段构建图表</span>
            <span v-show="itemsX.length !== 0 && itemsY.length == 0 && itemssubY.length == 0" style="color: #212121; font-size: 15px; font-weight: 500"> {{ typetransName(selectChart) }}： 至少需要一个指标字段</span>
          </div>
          <div v-show="itemsY.length !== 0 || itemssubY.length !== 0" class="chart-content">
            <a-spin style="width: 100%; height: 86vh">
              <Chart v-if="selectChart === 0 || selectChart === 1 || selectChart === 3 || selectChart === 4 || selectChart === 5 || selectChart === 7" :option="chartOption" />
              <!--          <Chart v-if="selectChart===7" :option="chartOption"/>-->
              <a-table v-if="selectChart === 2 || selectChart === 6" :show-header="showTableHeader" :columns="columns" style="padding: 0px 16px" :data="data" column-resizable :bordered="{ cell: true }" :scroll="columns.length > 7 ? { x: 120 * columns.length } : false" />
            </a-spin>
          </div>
        </div>
      </div>

      <div style="width: 414px">
        <a-tabs default-active-key="1" justify>
          <a-tab-pane key="1">
            <template #title> 数据 </template>
            <div style="display: flex">
              <div style="flex: 1; width: 207px; border-right: 1px solid var(--color-neutral-3); height: 84vh">
                <div style="padding: 0 16px">
                  <span style="font-weight: 500">x轴/分组</span>
                  <br />
                  <div v-show="itemsX.length == 0" class="emptyItems">
                    <p>拖入字段</p>
                  </div>
                  <draggable class="list-group" :list="itemsX" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                    <template #item="{ element, index }">
                      <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                        <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                        <template #content>
                          <div class="menuItem">
                            <div class="itemOffspring" style="justify-content: center" @click="sortItem">
                              <a-space> <span>排序</span> <icon-settings /></a-space>
                            </div>
                            <div class="itemOffspringDel" @click="removeAt('x', index)">
                              <a-space> <span>移出字段</span> </a-space>
                            </div>
                          </div>
                        </template>
                      </a-trigger>
                    </template>
                  </draggable>

                  <br />
                  <div v-show="selectChart === 0 || selectChart === 1 || selectChart === 5">
                    <span style="font-weight: 500">子分组</span>
                    <br />
                    <div v-show="itemsZ.length == 0" class="emptyItems">
                      <p>拖入字段</p>
                    </div>
                    <draggable class="list-group" :list="itemsZ" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                      <template #item="{ element, index }">
                        <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                          <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                          <template #content>
                            <div class="menuItem">
                              <div class="itemOffspring" style="justify-content: center" @click="sortItem">
                                <a-space> <span>排序</span> <icon-settings /></a-space>
                              </div>
                              <div class="itemOffspringDel" @click="removeAt('z', index)">
                                <a-space> <span>移出字段</span> </a-space>
                              </div>
                            </div>
                          </template>
                        </a-trigger>
                      </template>
                    </draggable>
                    <br />
                  </div>
                  <a-divider v-if="selectChart === 2 || selectChart === 6" :margin="20">
                    <img src="/icon/conversion.svg" alt="" style="cursor: pointer; margin-bottom: -4px" @click="conversionTable" />
                  </a-divider>

                  <div style="display: flex; justify-content: space-between">
                    <span style="font-weight: 500">Y轴/指标</span>
                    <a-tooltip position="bottom" content="添加次轴">
                      <div v-if="!showsubYitems" v-show="selectChart !== 6 && selectChart !== 2 && selectChart !== 3 && selectChart !== 7" class="resultItem" style="padding: 3px" @click="addsubY">
                        <icon-plus size="14" />
                      </div>
                    </a-tooltip>
                  </div>

                  <div v-show="itemsY.length == 0" class="emptyItems">
                    <p>拖入字段</p>
                  </div>
                  <draggable class="list-group" :list="itemsY" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                    <template #item="{ element, index }">
                      <a-trigger trigger="click" :popup-visible="visibleNumberY[index]" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                        <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                        <template #content>
                          <div class="menuItem" style="width: 134px">
                            <a-trigger position="right" auto-fit-position :unmount-on-close="false" :popup-translate="[0, 100]">
                              <div class="itemOffspring">
                                <a-space style="display: flex; justify-content: space-between"> <span>聚合方式</span> <icon-right style="margin-left: 35px" /> </a-space>
                              </div>

                              <template #content>
                                <div style="width: 124px; background-color: #fff; border: 1px solid var(--color-neutral-3); border-radius: 4px">
                                  <div v-show="element.type == 'number'" class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>求和</span> </a-space>
                                  </div>
                                  <div v-show="element.type == 'number'" class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>均值</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>计数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>去重计数</span> </a-space>
                                  </div>
                                  <div v-show="element.type == 'number'" class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>最大值</span> </a-space>
                                  </div>
                                  <div v-show="element.type == 'number'" class="itemOffspring" @click="NumberPolymerization(index, element.id)">
                                    <a-space> <span>最小值</span> </a-space>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>

                            <div class="itemOffspring" @click="sortItem">
                              <a-space> <span>排序</span> </a-space>
                            </div>

                            <a-trigger position="right" auto-fit-position :unmount-on-close="false" :popup-translate="[0, 72]">
                              <div class="itemOffspring">
                                <a-space style="display: flex; justify-content: space-between"> <span>数字展示格式</span> <icon-right /> </a-space>
                              </div>

                              <template #content>
                                <div style="width: 124px; background-color: #fff; border: 1px solid var(--color-neutral-3); border-radius: 4px">
                                  <div class="itemOffspring" @click="NumberFormat(index, 0, element.id)">
                                    <a-space> <span>取整</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 1, element.id)">
                                    <a-space> <span>1位小数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 2, element.id)">
                                    <a-space> <span>2位小数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 3, element.id)">
                                    <a-space> <span>3为小数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 4, element.id)">
                                    <a-space> <span>百分数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 5, element.id)">
                                    <a-space> <span>1位百分数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="NumberFormat(index, 6, element.id)">
                                    <a-space> <span>2位百分数</span> </a-space>
                                  </div>
                                  <div class="itemOffspring" @click="showNumberCustom = true">
                                    <a-space> <span>自定义设置</span> </a-space>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>

                            <a-trigger v-model:popup-visible="visibleEditNameY[index]" trigger="click" :unmount-on-close="false">
                              <div class="itemOffspring">
                                <a-space> <span>重命名</span> </a-space>
                              </div>
                              <template #content>
                                <div class="EditNameItem">
                                  <div class="">
                                    <a-space style="background-color: #fff">
                                      <a-input v-model="editNameValueY[index]" :style="{ width: '180px' }" :placeholder="element.name" allow-clear></a-input>
                                      <a-button @click="visibleEditNameY[index] = false">取消</a-button>
                                      <a-button type="primary" @click="editNameY(element, index)">确定</a-button>
                                    </a-space>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>

                            <div class="itemOffspring" @click="addFilterItemT(index)">
                              <a-space> <span>添加至筛选器</span> </a-space>
                            </div>
                            <div class="itemOffspringDel" style="width: 127px; justify-content: left; padding: 0 6px" @click="removeAt('y', index)">
                              <a-space> <span>移出字段</span> </a-space>
                            </div>
                          </div>
                        </template>
                      </a-trigger>
                    </template>
                  </draggable>
                  <br />

                  <div v-if="showsubYitems">
                    <div style="display: flex; justify-content: space-between">
                      <span style="font-weight: 500">次Y轴/指标</span>
                      <a-tooltip position="top" content="删除次轴">
                        <div class="delresultItem" style="padding: 3px" @click="delsubY">
                          <icon-delete size="14" />
                        </div>
                      </a-tooltip>
                    </div>

                    <div v-show="itemssubY.length == 0" class="emptyItems">
                      <p>拖入字段</p>
                    </div>
                    <draggable class="list-group" :list="itemssubY" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                      <template #item="{ element, index }">
                        <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                          <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                          <template #content>
                            <div class="menuItem">
                              <div class="itemOffspring" style="justify-content: center" @click="sortItem">
                                <a-space> <span>排序</span> <icon-settings /></a-space>
                              </div>
                              <div class="itemOffspringDel" @click="removeAt('subY', index)">
                                <a-space> <span>移出字段</span> </a-space>
                              </div>
                            </div>
                          </template>
                        </a-trigger>
                      </template>
                    </draggable>
                    <br />
                  </div>

                  <div v-if="selectChart === 7">
                    <div>
                      <span style="font-weight: 500">颜色</span>
                    </div>

                    <div v-show="itemsScatterColor.length == 0" class="emptyItems">
                      <p>拖入字段</p>
                    </div>
                    <draggable class="list-group" :list="itemsScatterColor" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                      <template #item="{ element, index }">
                        <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                          <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                          <template #content>
                            <div class="menuItem">
                              <div class="itemOffspring" style="justify-content: center" @click="sortItem">
                                <a-space> <span>排序</span> <icon-settings /></a-space>
                              </div>
                              <div class="itemOffspringDel" @click="removeAt('Color', index)">
                                <a-space> <span>移出字段</span> </a-space>
                              </div>
                            </div>
                          </template>
                        </a-trigger>
                      </template>
                    </draggable>
                    <br />
                  </div>

                  <div v-if="selectChart === 7">
                    <div>
                      <span style="font-weight: 500">尺寸</span>
                    </div>

                    <div v-show="itemsScatterSize.length == 0" class="emptyItems">
                      <p>拖入字段</p>
                    </div>
                    <draggable class="list-group" :list="itemsScatterSize" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                      <template #item="{ element, index }">
                        <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                          <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                          <template #content>
                            <div class="menuItem">
                              <div class="itemOffspring" style="justify-content: center" @click="sortItem">
                                <a-space> <span>排序</span> <icon-settings /></a-space>
                              </div>
                              <div class="itemOffspringDel" @click="removeAt('Size', index)">
                                <a-space> <span>移出字段</span> </a-space>
                              </div>
                            </div>
                          </template>
                        </a-trigger>
                      </template>
                    </draggable>
                    <br />
                  </div>

                  <span style="font-weight: 500">图内筛选器</span>
                  <a-tooltip position="top">
                    <template #content>
                      设置后，在看板中可对选择的字段值 <br />
                      进行筛选查看
                    </template>
                    <icon-info-circle style="cursor: pointer; margin-left: 5px" />
                  </a-tooltip>
                  <br />
                  <div v-show="itemsT.length == 0" class="emptyItems">
                    <p>拖入字段</p>
                  </div>
                  <draggable class="list-group" :list="itemsT" :group="{ name: 'people', pull: true, put: true }" item-key="name" @change="log">
                    <template #item="{ element, index }">
                      <a-trigger trigger="click" :unmount-on-close="false" update-at-scroll :popup-translate="[86, 0]">
                        <div class="listItem"> &nbsp;&nbsp;{{ element.name }} </div>
                        <template #content>
                          <div class="menuItem">
                            <div class="itemOffspringDel" @click="removeAt('t', index)">
                              <a-space> <span>移出字段</span> </a-space>
                            </div>
                          </div>
                        </template>
                      </a-trigger>
                    </template>
                  </draggable>
                  <br />
                </div>
              </div>
              <div style="flex: 1; width: 207px; display: flex">
                <div style="padding: 0 16px; width: 100%">
                  <div style="display: flex; justify-content: space-between">
                    <div>
                      <span style="font-weight: 500"
                        >字段
                        <a-tooltip position="bottom">
                          <template #content>
                            支持快捷键多选字段进行拖拽 <br />
                            <img src="/image/sql/sqlchartInfo.png" style="width: 218px" />
                          </template>
                          <icon-info-circle style="cursor: pointer" />
                        </a-tooltip>
                      </span>
                    </div>
                    <div>
                      <a-tooltip position="left">
                        <template #content> 查看原始数据 </template>
                        <!--                    <icon-code-square style="cursor: pointer;" @click="showResultTable" />-->
                        <div class="resultItem">
                          <img src="/icon/result.svg" @click="showResultTable" />
                        </div>
                      </a-tooltip>
                    </div>
                  </div>

                  <div style="width: 100%; margin: 10px 5px 13px 5px">
                    <a-space>
                      <icon-search />
                      <a-input v-model="searchValue" style="all: unset; margin-left: 3px" placeholder="搜索字段" @input="searchItemsB"> </a-input>
                    </a-space>
                  </div>

                  <draggable class="list-group" :list="itemsB" :group="{ name: 'people', pull: 'clone', put: false }" item-key="name" @change="log">
                    <template #item="{ element, index }">
                      <div v-if="element.type === 'number'" class="move" style="display: flex; justify-content: space-between; width: 90%" @mouseover="showName(index, true)" @mouseleave="showName(index, false)">
                        <span style="text-align: left"><img src="/icon/number.svg" style="width: 14px; height: 14px" /> {{ element.name }}</span>
                        <a-tooltip position="top" content="设置显示名">
                          <a-trigger v-model:popup-visible="visibleEditName[index]" trigger="click" :unmount-on-close="false">
                            <icon-edit v-if="isNameVisible[index]" />
                            <template #content>
                              <div class="EditNameItem">
                                <div class="">
                                  <a-space style="background-color: #fff">
                                    <a-input v-model="editNameValue[index]" :style="{ width: '180px' }" :placeholder="element.name" allow-clear></a-input>
                                    <a-button @click="visibleEditName[index] = false">取消</a-button>
                                    <a-button type="primary" @click="editName(element, index)">确定</a-button>
                                  </a-space>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-tooltip>
                      </div>
                      <div v-else class="move" style="display: flex; justify-content: space-between; width: 90%" @mouseover="showName(index, true)" @mouseleave="showName(index, false)">
                        <span style="text-align: left"><icon-strikethrough /> {{ element.name }}</span>
                        <a-tooltip position="top" content="设置显示名">
                          <a-trigger v-model:popup-visible="visibleEditName[index]" trigger="click" :unmount-on-close="false">
                            <icon-edit v-if="isNameVisible[index]" />
                            <template #content>
                              <div class="EditNameItem">
                                <div class="">
                                  <a-space style="background-color: #fff">
                                    <a-input v-model="editNameValue[index]" :style="{ width: '180px' }" :placeholder="element.name" allow-clear></a-input>
                                    <a-button @click="visibleEditName[index] = false">取消</a-button>
                                    <a-button type="primary" @click="editName(element, index)">确定</a-button>
                                  </a-space>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </a-tooltip>
                      </div>
                    </template>
                  </draggable>
                </div>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2">
            <template #title> 样式 </template>
            <div style="padding: 0 16px">
              <span style="font-weight: 550">显示条数</span>
              <div style="border-bottom: 1px solid var(--color-neutral-3)">
                <br />
                <a-space class="spaceItem">
                  <a-row>
                    <a-col :span="10">
                      <span class="spanItem">X轴分组数</span>
                    </a-col>
                    <a-col :span="14">
                      <a-input v-model="XgroupNumber" :style="{ width: '220px' }" placeholder="20000" allow-clear @change="Xgroup" />
                    </a-col>
                  </a-row>
                </a-space>
                <a-space v-show="selectChart === 0 || selectChart === 1 || selectChart === 5" class="spaceItem">
                  <a-row>
                    <a-col :span="10">
                      <span class="spanItem">子分组数&nbsp;&nbsp;</span>
                    </a-col>
                    <a-col :span="14">
                      <a-input v-model="ZgroupNumber" :style="{ width: '220px' }" placeholder="50" allow-clear @change="Zgroup" />
                    </a-col>
                  </a-row>
                </a-space>
              </div>

              <div v-show="selectChart === 0 || selectChart === 1 || selectChart === 4 || selectChart === 5 || selectChart === 7" style="margin-top: 10px">
                <span style="font-weight: 550">坐标轴</span><br />
                <a-radio-group type="button" default-value="X" @change="radioXY">
                  <a-radio value="X">X轴</a-radio>
                  <a-radio value="Y">Y轴</a-radio>
                </a-radio-group>
                <div style="border-bottom: 1px solid var(--color-neutral-3)">
                  <br />

                  <a-row v-show="isXY" class="spaceItem">
                    <a-col :span="8">
                      <div>
                        <span class="spanItem" style="line-height: 28px">显示轴标签</span>
                      </div>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch v-model="YshowLable" />
                      </div>
                    </a-col>
                  </a-row>

                  <a-row v-show="!isXY" class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">显示轴标签</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch v-model="XshowLable" />
                      </div>
                    </a-col>
                  </a-row>

                  <a-row v-show="isXY" class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">自适应</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch v-model="YaxisLabelAdaptive" @change="YaxisLabelAdaptiveChange" />
                      </div>
                    </a-col>
                  </a-row>

                  <a-space v-show="!isXY" class="spaceItem">
                    <a-row>
                      <a-col :span="10">
                        <span class="spanItem">轴标签间隔</span>
                      </a-col>
                      <a-col :span="14">
                        <a-input v-model="axisLabelSpacing" :style="{ width: '220px' }" allow-clear placeholder="默认" />
                      </a-col>
                    </a-row>
                  </a-space>

                  <a-space v-show="!isXY" class="spaceItem">
                    <a-row>
                      <a-col :span="10">
                        <span class="spanItem"
                          >标签文字
                          <a-tooltip>
                            <template #content>
                              设置轴标签文字的截取规则，如：<br />
                              “abc”设置头尾各1，展示为“a…c”
                            </template>
                            <icon-info-circle />
                          </a-tooltip>
                        </span>
                      </a-col>
                      <a-col :span="14">
                        <a-input-group>
                          <a-select v-model="axisLabelSymbolSelect" :options="['头', '尾', '头尾']" :style="{ width: '100px' }" @change="axisLabelSymbolChange" />
                          <a-input v-model="axisLabelSymbolValue" :style="{ width: '120px' }" placeholder="默认" @change="axisLabelSymbol" />
                        </a-input-group>
                      </a-col>
                    </a-row>
                  </a-space>

                  <a-space v-show="isXY" class="spaceItem">
                    <a-row>
                      <a-col :span="10">
                        <span class="spanItem">轴最大值</span>
                      </a-col>
                      <a-col :span="14">
                        <a-input v-model="YaxisLabelMax" :disabled="YaxisLabelAdaptive" :style="{ width: '220px' }" allow-clear placeholder="默认" />
                      </a-col>
                    </a-row>
                  </a-space>
                  <a-space v-show="isXY" class="spaceItem">
                    <a-row>
                      <a-col :span="10">
                        <span class="spanItem">轴最小值</span>
                      </a-col>
                      <a-col :span="14">
                        <a-input v-model="YaxisLabelMin" :disabled="YaxisLabelAdaptive" :style="{ width: '220px' }" allow-clear placeholder="默认" />
                      </a-col>
                    </a-row>
                  </a-space>
                </div>
              </div>
              <div style="margin-top: 10px">
                <span v-show="selectChart !== 3 && selectChart !== 7" style="font-weight: 550">指标</span>
                <div v-show="selectChart !== 3 && selectChart !== 7" style="border-bottom: 1px solid var(--color-neutral-3); text-align: center">
                  <br />
                  <a-space v-show="selectChart === 0 || selectChart === 1 || selectChart === 5 || selectChart === 4" class="spaceItem">
                    <a-row>
                      <a-col :span="10">
                        <span class="spanItem">指标选择</span>
                      </a-col>
                      <a-col :span="14">
                        <a-select v-model="selectySeries" :style="{ width: '220px' }">
                          <a-option v-for="(item, index) in ySeries" :key="index" :label="item.name" :value="item.name"></a-option>
                        </a-select>
                      </a-col>
                    </a-row>
                  </a-space>

                  <a-row v-show="selectChart === 0 || selectChart === 1 || selectChart === 5 || selectChart === 4" class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">显示数值</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch @change="(value) => (value ? ySeries.forEach((item) => (item.name === selectySeries ? (item.label.show = true) : '')) : ySeries.forEach((item) => (item.name === selectySeries ? (item.label.show = false) : '')))" />
                      </div>
                    </a-col>
                  </a-row>

                  <a-row v-show="selectChart === 2 || selectChart === 6" class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">显示色阶</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch />
                      </div>
                    </a-col>
                  </a-row>

                  <a-row v-show="selectChart === 0 || selectChart === 1 || selectChart === 5 || selectChart === 4" class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">显示最值</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left">
                        <a-switch
                          @change="(value) => (value ? ySeries.forEach((item) => (item.name === selectySeries ? item.markPoint.data.push({ type: 'max', name: 'Max' }, { type: 'min', name: 'Min' }) : '')) : ySeries.forEach((item) => (item.name === selectySeries ? (item.markPoint.data = []) : '')))"
                        />
                      </div>
                    </a-col>
                  </a-row>
                </div>
              </div>
              <div style="margin-top: 10px">
                <span style="font-weight: 550">分组</span>
                <div>
                  <br />

                  <a-row class="spaceItem">
                    <a-col :span="8">
                      <span class="spanItem" style="line-height: 28px">显示星期</span>
                    </a-col>
                    <a-col :span="16">
                      <div style="text-align: left"> <a-switch /> <span style="color: #7e7f80">*仅对日期字段生效</span> </div>
                    </a-col>
                  </a-row>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <a-modal v-model:visible="visibleDel" width="auto" title-align="start" @cancel="visibleDel = false">
        <template #title> 删除图表 </template>
        <div>确认删除图表吗？该操作不可恢复</div>
        <template #footer>
          <a-button @click="showSqlresult = false">关闭</a-button>
          <a-button type="primary" status="danger" @click="back">删除</a-button>
        </template>
      </a-modal>
      <a-modal v-model:visible="showSqlresult" :footer="false" width="75%" @cancel="showSqlresult = false">
        <template #title> SQL查询结果 </template>
        <div>
          <a-table :columns="columnsResult" :data="dataResult" column-resizable :bordered="{ cell: true }" :scroll="columnsResult.length > 7 ? { x: 120 * columnsResult.length } : false" />
        </div>
      </a-modal>
      <a-modal v-model:visible="showSort" width="auto" @cancel="showSort = false" @ok="submitSort">
        <template #title> 排序 </template>
        <div style="display: flex; flex-direction: column; justify-content: center">
          <div style="margin: 0 auto">
            <span style="font-weight: 500">排名指标：</span>
            <a-select v-model="selectSort" :style="{ width: '220px' }">
              <a-option v-for="(item, index) in ySeries" :key="index" :label="item.name" :value="item.name"></a-option>
            </a-select>
          </div>
          <div style="margin: 0 auto; padding-top: 15px">
            <span style="font-weight: 500">顺序：</span>
            <a-radio-group v-model="sortValue" type="button" @change="handleChangeSort">
              <a-radio value="ascending">升序</a-radio>
              <a-radio value="descending">降序</a-radio>
              <a-radio value="none">不排序</a-radio>
            </a-radio-group>
          </div>
        </div>
      </a-modal>
      <a-modal v-model:visible="showNumberCustom" title-align="start" width="380px" @cancel="showNumberCustom = false" @ok="showNumberCustom = false">
        <template #title> 自定义数字展示格式 </template>
        <div style="display: flex; flex-direction: column; justify-content: center">
          <div style="margin: 0 auto; padding-bottom: 16px">
            <a-radio-group v-model="NumberCustomType" type="button">
              <a-radio value="auto">自动</a-radio>
              <a-radio value="number">数字</a-radio>
              <a-radio value="percentage">百分比</a-radio>
            </a-radio-group>
            <br /><br />
            <div v-show="NumberCustomType === 'auto'" style="text-align: left; width: 100%"> <span style="font-weight: 500; margin-right: 6px">示例 </span> <span>1,000,000</span> </div>
            <div v-show="NumberCustomType === 'number'" style="text-align: left; width: 100%"> <span style="font-weight: 500; margin-right: 6px">示例 </span> <span>1000000</span> </div>
            <div v-show="NumberCustomType === 'percentage'" style="text-align: left; width: 100%"> <span style="font-weight: 500; margin-right: 6px">示例 </span> <span>1000000</span> </div>
          </div>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<style scoped lang="less">
  :deep(.arco-tabs-nav-tab) {
    display: flex;
    flex: 1;
    justify-content: center;
    overflow: hidden;
    font-size: 15px;
  }
  :deep(.arco-tabs-nav-tab-list) {
    display: flex;
    justify-content: space-around;
    white-space: nowrap;
    width: 100%;
  }
  .btn {
    //width: 80px;
    height: 30px;
    padding-left: 5px;
    padding-right: 5px;
    border-radius: 4px;
    border: 1px solid #e3e4e5;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .btn:hover {
    border: 1px solid #165dff;
  }
  .Chartspan {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: #f2f3f8;
    width: 100%;
    height: 20px;
  }
  .divItem {
    cursor: pointer;
    height: 80px;
    width: 126px;
    margin-bottom: 15px;
    position: relative;
    text-align: center;
    background-color: #f8fafe;
    border-radius: 4px;
    border: 1px solid #e3e4e5;
  }
  .divItem:hover {
    background-color: #fff;
  }
  .itemActive {
    border: 1px solid #1b6ef3;
    border-radius: 3px;
  }
  .described {
    width: 262px;
    padding: 0 24px 24px 24px;
  }
  .move {
    cursor: pointer;
    margin: 8px 0;
    width: calc(100% - 32px);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    color: var(--tant-text-gray-color-text1-2);
    font: var(--tant-body-font-body-regular);
  }
  .move:hover {
    background-color: #f6f6f9;
  }
  .listItem {
    cursor: pointer;
    border: 1px solid var(--color-neutral-3);
    margin-bottom: 5px;
    padding: 5px 0;
    border-radius: 3px;
    margin-top: 15px;
  }
  .listItem:hover {
    border: 1px solid #3583ef;
  }
  .itemOffspring {
    cursor: pointer;
    width: 100%;
    height: 38px;
    display: flex;
    //justify-content: center;
    justify-items: center;
    padding: 0 6px;
  }
  .itemOffspring:hover {
    background-color: #f6f6f9;
    border-radius: 5px;
  }

  .itemOffspringDel {
    cursor: pointer;
    width: 88px;
    height: 38px;
    display: flex;
    justify-content: center;
    justify-items: center;
  }
  .itemOffspringDel:hover {
    background-color: #fbebea;
    color: #e84d3d;
    border-radius: 5px;
  }
  .menuItem {
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    background-color: #fff;
    padding: 3px;
    width: 100%;
  }
  .chart-content {
    width: 100%;
    height: 100%;
  }
  .resultItem {
    cursor: pointer;
    color: rgb(126, 127, 128);
    display: flex;
    justify-content: center;
    justify-items: center;
    margin-right: 10px;
  }
  .resultItem:hover {
    background-color: #f1f2f5;
    border-radius: 6px;
  }
  .delresultItem {
    cursor: pointer;
    color: rgb(126, 127, 128);
    display: flex;
    justify-content: center;
    justify-items: center;
    margin-right: 10px;
  }
  .delresultItem:hover {
    background-color: #fdf5f4;
    border-radius: 6px;
  }

  .list-group {
    max-height: 68vh;
    width: 100%;
    //min-height: 30px;
    overflow-y: auto;
    text-overflow: ellipsis;
    div {
      span {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .EditNameItem {
    padding: 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  .emptyItems {
    background-color: #f9f9fb;
    cursor: default;
    height: 30px;
    line-height: 30px;
    p {
      color: #ccbec3;
      margin: 10px;
    }
  }
  .spanItem {
    font-weight: 500;
    line-height: 32px;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .spaceItem {
    margin-bottom: 20px;
    text-align: right;
    width: 100%;
  }
</style>
