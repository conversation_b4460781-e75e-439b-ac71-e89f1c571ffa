<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <a-input v-model:model-value="queryParams.name" placeholder="请输入白名单名称搜索" style="width: 240px;" @change="init"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            <template #icon>
              <icon-plus/>
            </template>
            创建白名单
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
        :columns="columns"
        :loading="loading"
        :data="filterData"
        :bordered="false"
        :hoverable="true"
        sticky-header
        :table-layout-fixed="true"
        :filter-icon-align-left="true"
        :column-resizable="true"
        :scroll="scroll"
        :pagination="true"
      >
        <template #relatedExperiment="{ record }"> 
          <a-tooltip>
            <template #content>
              <div v-for="item in record.experimentList" :key="item.code" class="listItem">
                {{ item.name }}-{{ getStatusLabel(item.status) }}
              </div>
            </template>
            <span v-if="record.experimentList?.length" class="link">{{record.experimentList.length}}个</span>
          </a-tooltip>
        </template>
        <template #action="{ record }">
          <a-dropdown position="bl" :hide-on-select="false">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption value="edit" @click="editItem(record)">
                <icon-edit class="mr8"/>
                编辑
              </a-doption>
              <a-popconfirm type="error" :content="`确认删除“${record.name}”?`" @ok="deleteItem(record)">
                <a-doption value="delete">
                  <icon-delete class="mr8"/>
                  删除
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
    </div>
    <handleModal ref="handleRefs" @update-data="init"/>
  </div>

</template>

<script setup lang="ts">
import {reactive, ref,computed} from "vue";
import {deleteUserWhiteList,getUserWhiteList} from "@/api/ab/api";
import selectApp from "@/components/selected-game-app/index.vue"
import {useEventBus} from '@vueuse/core';
import {LocalStorageEventBus} from "@/types/event-bus";
import dayjs from 'dayjs';
import {useRoute} from "vue-router";
import {Message} from '@arco-design/web-vue';
import handleModal from "./components/handleModal.vue";

const localStorageEventBus = useEventBus(LocalStorageEventBus)
const route = useRoute();
// 查询数据
const queryParams = reactive({
  name: route.query.text as string || '',
})
const total = ref(0)
// 模拟table数据
const loading = ref(false)

const tableData = ref<any>([])
const columns = ref<any>([
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    slotName: 'name',
    minWidth: 120,
  },
  
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    tooltip: true,
    slotName: 'description',
    minWidth: 120,
  },
  {
    title: '关联实验',
    dataIndex: 'relatedExperiment',
    slotName: 'relatedExperiment',
    ellipsis: true,
    minWidth: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    minWidth: 120,
    render: (value) => { 
      const {record} = value;
      return record.status === 1 ? '启用' : '禁用'
    },
  },
  {
    title: '最后更新人',
    dataIndex: 'principal',
    slotName: 'principal',
    width: 120,
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    slotName: 'action',
  }
])

// Table页面滑动
const scroll = {
  y: 'calc(100vh - 256px)'
};
const filterData = computed(() => {
  if (!queryParams.name.trim()) {
    return tableData.value
  }
  return tableData.value.filter(item => 
    item.name?.includes(queryParams.name)
  )
})
const init = () => {
  loading.value = true
  try {
    getUserWhiteList().then(res => {
      if (res) {
        tableData.value = res
      }
    }).catch((e) => {
      console.error('获取白名单列表失败', e)
    }).finally(() => {
      loading.value = false
    })
  } catch (e) {
    loading.value = false
    console.error('获取白名单列表异常', e)
  } finally {
    loading.value = false
  }
}
init()
const handleRefs = ref()
// 新建
const createIndex = () => {
  handleRefs.value.openModal()
}
const editItem = (record: any) => {
  handleRefs.value.openModal(record)
}
const STATUS_OPTIONS = [
  {value: 0, label: '草稿'},
  {value: 1, label: '进行中'},
  {value: 2, label: '已终止'},
  {value: 3, label: '已结束'},
  {value: 4, label: '调试中'},
] as const;
const getStatusLabel = (status: string) => {
  return STATUS_OPTIONS.find(option => option.value === status)?.label || '未知状态';
};
// 删除
const deleteItem = async (record: any) => {
  try {
    await deleteUserWhiteList(record.code);
    Message.success('删除成功');
    init();
  } catch (e) {
    // Message.error(e.detail || '删除失败');
  }
}
localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    init()
  }
})
</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
.link {
  color: rgb(var(--primary-6));
  text-decoration: underline;
}
</style>