<template>
  <div class="single-page">
    <a-page-header
        class="header"
        :title="_.isEmpty(groupCode)?'创建条件分群':'编辑条件分群'"
        @back="cancelConfirmVisible = true">
      <template #extra>
        <a-space>
          <a-button @click="cancelConfirmVisible = true">取消</a-button>
          <a-button type="primary" @click="pushSave">保存</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="container">
      <div class="block rule-block">
        <div class="block-title">
          分群规则
          <div class="description">
            筛选特定行为条件或用户属性的用户生成分群
          </div>
        </div>
        <div class="rule-item">
          <div class="title">
            数据来源
          </div>
          <select-data-source v-model:data-source="dataSource" disable-storage/>
        </div>
        <div class="user-condition">
          <div class="user-condition-tree">
            <cluster-index ref="groupConditionRef" :only-evt="true" :user-filter="initGroupCondition" @change="filtersChange"/>
          </div>
          <a-dropdown trigger="hover" :popup-translate="[18, 0]">
            <div class="ta-filter-button">
              <div class="ta-filter-button-icon">
                <icon-plus/>
              </div>
              添加分群条件
            </div>
            <template #content>
              <a-doption @click="addEvent('sequentially')">做过/没做过的事件</a-doption>
              <a-doption @click="addEvent('inOrder')">依次/没依次做过的事件</a-doption>
              <a-doption @click="addEvent('userProperty')">用户属性满足</a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
      <div class="block info-block">
        <div class="block-title">
          基本信息
        </div>
        <div class="group-info">
          <a-form ref="formRef" :model="form" :rules="rules" class="form">
            <a-form-item
                field="appId"
                validate-trigger="blur"
                label="应用ID"
                label-col-flex="90px"
            >
              <a-input v-model="appId" disabled :style="{ width: '384px' }"></a-input>
            </a-form-item>
            <a-form-item
                field="displayName"
                validate-trigger="blur"
                label="分群名称"
                label-col-flex="90px"
            >
              <a-input v-model="form.displayName" placeholder="请输入分群名称" :style="{ width: '384px' }"></a-input>
            </a-form-item>
            <a-form-item field="status" label="启用状态" label-col-flex="90px">
              <a-switch v-model="form.status" :checked-value="1" :unchecked-value="0"/>
            </a-form-item>
<!--            <a-form-item-->
<!--                field="timeZone"-->
<!--                validate-trigger="blur"-->
<!--                label="时区"-->
<!--                label-col-flex="90px"-->
<!--            >-->
<!--              <time-zone-select v-model:time-zone="form.timeZone" disable-storage/>-->
<!--            </a-form-item>-->
<!--            <a-form-item-->
<!--                field="calculateCron"-->
<!--                validate-trigger="blur"-->
<!--                label="定时计算"-->
<!--                label-col-flex="90px"-->
<!--            >-->
<!--              <a-switch v-model="showCalculateCron"/>-->
<!--              <a-input v-if="showCalculateCron" v-model="form.calculateCron" class="cron-input" placeholder="请输入计算周期"/>-->
<!--            </a-form-item>-->
            <a-form-item
                label-col-flex="90px"
            >
              <template #label>备注 <span class="description">(选填)</span>
              </template>
              <a-textarea v-model="form.remark" placeholder="请输入备注" show-word-limit :rows="3" :max-length="{length:200}" :style="{ width: '384px' }"></a-textarea>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    <a-modal v-model:visible="cancelConfirmVisible" :align-center="false" title-align="start" :width="300" :top="140" @cancel="cancelConfirmVisible = false">
      <template #title>
        退出编辑
      </template>
      <div>用户分群编辑尚未保存。确认退出编辑吗？</div>
      <template #footer>
        <div style="display: flex;justify-content: flex-end;">
          <a-button style="margin-right: 8px;" @click="cancelConfirmVisible = false">继续编辑</a-button>
          <a-button type="primary" @click="router.back()">退出</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue"
import {getUserGroupDetail, updateUserGroupItem, userGroupAdd} from "@/api/setting/api";
import selectDataSource from "@/components/selected-data-source/index.vue"
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {Message} from "@arco-design/web-vue";
import {toolStore} from '@/store';
import {ROUTE_NAME} from "@/router/constants";
import _ from "lodash";
import {useSessionStorage} from "@vueuse/core";

const appId = useSessionStorage('app-id', '')?.value;
/**
 * 事件属性选择器使用
 * todo 优化
 */
const toolData = toolStore();
const modelLists = ref<any>([])

const route = useRoute()
const router = useRouter()

/**
 * 数据源
 */
const dataSource = ref<string>('event');

/**
 * 用户分群编码
 */
const groupCode = ref<string>()
/**
 * 展示计算周期选择器
 */
const showCalculateCron = ref<boolean>(false)
/**
 * 群组筛选条件
 */
const groupConditionRef = ref()
/**
 * 退出确认框
 */
const cancelConfirmVisible = ref(false)
/**
 * 分群条件
 */
const groupCondition = ref()
/**
 * 初始化分群条件
 */
const initGroupCondition = ref()

/**
 * 创建分群名
 */
function formatCurrentTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `分群${year}${month}${day}_${hours}${minutes}${seconds}`;
}

const form = ref({
  displayName: formatCurrentTimestamp(),
  timeZone: '+08:00',
  calculateCron: '0 0 1 * * ?',
  remark: '',
  status: 1,
})
const rules = {
  displayName: [
    {
      required: true,
      message: '分群名不能为空',
    },
  ],
  timeZone: [
    {
      required: true,
      message: '时区不能为空',
    },
  ]
}

/**
 * 用户属性条件检查
 */
const paramsVerify = (params) => {
  const filters = params?.filters || [];
  for (const filter of filters) {
    // 如果 calcuSymbol 不是 ex 或 nex，则必须有 thresholds 且长度大于 0
    if (filter.calcuSymbol === 'ex' || filter.calcuSymbol === 'nex' || filter.thresholds?.length) {
      continue;
    }
    return false
  }
  return true;
}

/**
 * 保存分群
 */
async function pushSave() {
  const sequenceConditionLength = groupCondition.value?.eventCondition?.sequenceConditionExpressions?.length;
  const singleConditionLength = groupCondition.value?.eventCondition?.singleConditionExpressions?.length;
  const userConditionLength = groupCondition.value?.userCondition?.filters?.length;
  if (sequenceConditionLength + singleConditionLength + userConditionLength === 0) {
    Message.warning('分群条件不能为空，请点击按钮添加！')
    return
  }
  //  用户属性条件校验
  if (userConditionLength && !paramsVerify(groupCondition.value?.userCondition)) {
    Message.warning('用户属性筛选参数错误！')
    return
  }

  const data = {
    display_name: form.value.displayName,
    type: 1,
    timeZone: form.value.timeZone,
    calculateCron: showCalculateCron.value ? form.value.calculateCron : '',
    condition: groupCondition.value,
    dataSource: dataSource.value,
    note: form.value.remark,
    status: form.value.status
  }

  if (_.isEmpty(groupCode.value)) {
    // 新增
    await userGroupAdd(data).then(res => {
      if (Object.keys(res).length > 0) {
        Message.success('保存成功')
        router.push({
          name: ROUTE_NAME.USER_GROUP
        })
      }
    }).catch(error => {
      console.error(error)
    })
  } else {
    // 编辑
    data.code = groupCode.value
    await updateUserGroupItem(data).then(res => {
      if (Object.keys(res).length > 0) {
        Message.success('保存成功')
        router.push({
          name: ROUTE_NAME.USER_GROUP
        })
      }
    }).catch(error => {
      console.error(error)
    })
  }
}

/**
 * 添加分群条件
 * @param type 条件类型
 */
const addEvent = async (type: string) => {
  switch (type) {
    case 'sequentially':
      groupConditionRef.value.add()
      break;
    case 'inOrder':
      groupConditionRef.value.addDone()
      break;
    case 'userProperty':
      groupConditionRef.value.addUser()
      break;
    default:
      break;
  }
}

/**
 * 分群条件变更
 */
function filtersChange(filters: any) {
  groupCondition.value = filters
}

/**
 * 初始化
 */
onMounted(async () => {
  const queryCode = route?.query.code;
  if (queryCode?.length > 0) {
    getUserGroupDetail(queryCode).then(res => {
      groupCode.value = res.code
      form.value.displayName = res.displayName
      form.value.remark = res.note
      form.value.timeZone = res.timeZone
      form.value.status = res.status
      groupCondition.value = res.condition
      initGroupCondition.value = res.condition
      dataSource.value = res.dataSource || 'event'
      const {calculateCron} = res;
      if (!_.isEmpty(calculateCron)) {
        showCalculateCron.value = true
        form.value.calculateCron = calculateCron
      }
    }).catch(e => console.error(e))
  }
  toolData.updateTemporaryList([])
  modelLists.value = (await toolData.fetchAllModalList()).flatMap(category => category.items || []);
})
</script>

<style scoped lang="less">
.single-page {
  width: 100%;
  height: 100%;

  .header {
    background: var(--color-bg-2);
    margin-bottom: 24px;
  }

  .container {
    margin: 24px;
    padding: 24px;
    height: calc(100vh - 120px);
    overflow: auto;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;

    .block {
      margin-bottom: 32px;

      .block-title {
        display: flex;
        align-items: flex-end;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font: var(--tant-header-font-header4-medium);

        .description {
          margin-left: 8px;
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }
      }

      .rule-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .title {
          padding-right: 16px;
        }
      }

      .user-condition {
        padding: 20px;
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: 8px;

        .ta-filter-button {
          display: inline-flex;
          color: var(--tant-primary-color-primary-default);
          align-items: center;
          padding: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all .3s;

          .ta-filter-button-icon {
            background-color: #eaeefd;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            margin-right: 6px;
          }

          &:hover {
            background-color: var(--tant-bg-white-color-bg1-1);
            color: var(--tant-primary-color-primary-hover);
          }
        }
      }

      .group-info {
        .description {
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }

        .cron-input {
          width: 328px;
          margin-left: 16px;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    //.rule-block{
    //  max-height: calc(100% - 308px);
    //  .user-condition{
    //    max-height: calc(100% - 46px);
    //    .user-condition-tree{
    //      max-height: calc(100% - 34px);
    //      overflow-y: auto;
    //    }
    //  }
    //}
  }
}
</style>