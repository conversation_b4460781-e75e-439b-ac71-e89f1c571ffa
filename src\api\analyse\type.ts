import {WsReportDataRequestQueryParam} from "@/api/type";
import {CalculateSymbol, ChartType, IndicatorType, LogicalOperationType, NumberSummaryType, QueryAggregateType, QueryFilterType, StatisticalType, TimeParticleSize} from "@/api/enum";

/**
 * 报表分析类型
 */
export enum ReportAnalyseModel {

    /**
     * 事件分析
     */
    EVENT = 'event',

    /**
     * 留存分析
     */
    RETENTION = 'retention',

    /**
     * 漏斗分析
     */
    FUNNEL = 'funnel',

    /**
     * 间隔分析
     */
    INTERVAL = 'interval',

    /**
     * 分布分析
     */
    SCATTER = 'scatter',

    /**
     * 路径分析
     */
    TRACE = 'trace',

    /**
     * 属性分析
     */
    PROPERTY = 'property',

    /**
     * 归因分析
     */
    ATTRIBUTION = 'attribution',

    /**
     * 自定义SQL分析
     */
    CUSTOM = 'custom',

    /**
     * 排行榜
     */
    RANKLIST = 'ranklist',

    /**
     * 热力图
     */
    HEATMAP = 'heatmap',
    /**
     * 运营分析
     */
    APPLICATION = 'application',
    /**
     * 变现分析
     */
    REVENUE = 'revenue',
    /**
     *买量分析
     */
    SPEND = 'spend',
    /**
     *群组分析
     */
    GROUP = 'group'
}
/**
 * 报表创建方式
 */
export enum ReportCreateWayParam {
    /**
     * 手动创建
     */
    MANUAL= 1,
    /**
     * 看板复制
     */
    COPY= 2,
    /**
     * 配置导入
     */
    IMPORT= 3,
    /**
     * 模版创建
     */
    TEMPLATE= 4,
}
/**
 * 自定义SQL分析查询参数中SQL语句涉及的变量类型
 */
export enum CustomQuerySqlParamType {

    /**
     * 变量
     */
    VARIABLE = 'Variable',

    /**
     * 事件时间
     */
    PART_DATE = 'PartDate',

    /**
     * 数值表达式
     */
    NUMBER = 'Number',

    /**
     * 文本
     */
    TEXT = 'Text',

    /**
     * 时间表达式
     */
    TIME = 'Time',

    /**
     * 时间表达式
     */
    SELECTOR = 'Selector',
}

/**
 * 事件分析查询参数
 */
export interface DateRange {
    /**
     * 开始日期
     */
    startDate?: string
    /**
     * 结束日期
     */
    endDate?: string
    /**
     * 动态开始日期
     */
    recentStartDate?: number
    /**
     * 动态结束日期
     */
    recentEndDate?: number
    /**
     * 动态日期范围文本
     */
    dateText?: string
}

export interface NumberDisplayConfig {
    /**
     * 展示方式，default - 数值，percent - 百分比
     */
    type: string
    /**
     * 小数位数
     */
    decimalNum?: number
    /**
     * 千分位，0 - 不显示千分位；1 - 显示千分位
     */
    thousandSep?: number
}

export interface EventQueryAggregate {
    /**
     * 聚合类型
     */
    aggregateType: QueryAggregateType
    /**
     * 聚合对象（属性/分群/标签）编号
     */
    objectId: string
    /**
     * 聚合对象（属性/分群/标签）名称
     */
    objectName: string
    /**
     * 聚合对象（属性/分群/标签）类型
     */
    objectType: string
    /**
     * 数值汇总方式
     */
    numberSummaryType?: NumberSummaryType
    /**
     * 数值自定义汇总区间
     */
    numberSummaryScope?: number[]
    /**
     * 时间分组方式
     */
    timeSummaryType?: TimeParticleSize
}

export interface FilterExpression {
    /**
     * 筛选类型
     */
    filterType: QueryFilterType
    /**
     * 筛选对象（属性/分群/标签）编号
     */
    objectId: string
    /**
     * 筛选对象（属性/分群/标签）名称
     */
    objectName: string
    /**
     * 筛选对象（属性/分群/标签）类型
     */
    objectType: string
    /**
     * 计算符号
     */
    calcuSymbol: CalculateSymbol
    /**
     * 阈值数组
     */
    thresholds?: number[] | string[]
    logicalOperation?:string
    /**
     * sub_filters虚拟列表数据
     */
    subFilters?: FilterExpression
}

export interface EventQueryFilter {
    /**
     * 逻辑运算关系
     */
    logicalOperationType: LogicalOperationType
    /**
     * 筛选表达式
     */
    filters?: FilterExpression[]
}

// 指标包含事件
export interface IndicatorEvent {
    // 类型，event - 事件；indicator - 指标
    type:string
    /**
     * 事件编码
     */
    eventCode?: string
    /**
     * 事件名称
     */
    eventName?: string
    eventDisplayName: string
    /**
     * 事件属性编码
     */
    eventAttrCode?: string
    /**
     * 事件属性名称
     */
    eventAttrName?: string
    /**
     * 统计类型
     */
    statisticalType?: StatisticalType
    /**
     * 筛选条件
     */
    filter: EventQueryFilter
}
export interface EventAttitude{
     /**
     * 指标编码
     */
     code?: string
     /**
      * 指标名称
      */
     name?: string
     /**
      * 显示名称
      */
     displayName?: string
}
export interface Indicator {
    /**
     * 指标编码
     */
    code?: string
    /**
     * 指标名称
     */
    name?: string
    /**
     * 显示名称
     */
    displayName?: string
    /**
     * 是否为基础指标
     */
    isBasic: boolean
    /**
     * 指标类型
     */
    type: IndicatorType
    /**
     * 展示格式
     */
    displayType: NumberDisplayConfig
    /**
     * 事件列表
     */
    eventList: IndicatorEvent[]
    /**
     * 计算公式
     */
    formula: string
    /**
     * 筛选条件
     */
    filter: EventQueryFilter
    // 描述
    description?: string
}

export interface ReportEventQueryParam {
    /**
     * 查询指标
     */
    indicators: Indicator[]
    /**
     * 查询筛选条件
     */
    filter: EventQueryFilter
    /**
     * 聚合分组
     */
    aggregates: EventQueryAggregate[]
    /**
     * 查询日期范围
     */
    dateRange: DateRange
    /**
     * 时间粒度
     */
    timeParticleSize: TimeParticleSize
    /**
     * 对比周期
     */
    comparedDateList?: DateRange[]
    /**
     * 周几作为一周开始
     */
    firstDayDfWeek?: number
    /**
     * 图表类型
     */
    chartType?: ChartType
}

/**
 * 自定义SQL分析查询参数中SQL语句涉及的变量
 * 选择器（Selector）类型对应的选项
 */
export interface CustomQuerySqlParamSelectorItem {

    /**
     * 选项名称
     */
    selectorName: string

    /**
     * 选项值
     */
    selectorValue: string
}

/**
 * 自定义SQL分析查询参数中SQL语句涉及的变量
 */
export interface CustomQuerySqlParam {

    /**
     * 变量名称
     */
    paramName: string

    /**
     * 变量类型
     */
    paramType: CustomQuerySqlParamType

    /**
     * 展示名称
     */
    paramDisplay?: string

    /**
     * 备注
     */
    paramRemark?: string

    /**
     * 计算符号
     */
    calcuSymbol?: CalculateSymbol

    /**
     * 阈值数组
     */
    thresholds: number[] | string[]

    /**
     * 时区，0-不受时区影响；1-受时区影响
     */
    useTimezone?: number

    /**
     * 开始日期，PartDate类型变量
     */
    startDate?: string

    /**
     * 结束日期，PartDate类型变量
     */
    endDate?: string

    /**
     * 动态开始日期，PartDate类型变量
     */
    recentStartDate?: number

    /**
     * 动态结束日期，PartDate类型变量
     */
    recentEndDate?: number

    /**
     * 动态日期，PartDate类型变量
     */
    formattedDate?: string

    /**
     * 选项，Selector类型变量
     */
    selectorItems: CustomQuerySqlParamSelectorItem[]

    /**
     * 选中选项，Selector类型变量
     */
    selectedItem: string
}

/**
 * 自定义SQL分析查询参数
 */
export interface WsReportCustomQueryParam extends WsReportDataRequestQueryParam {

    /**
     * 查询SQL表达式
     */
    sql: string

    /**
     * 查询变量
     */
    sqlParams: CustomQuerySqlParam[]
}

