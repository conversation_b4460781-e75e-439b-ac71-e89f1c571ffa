<!-- 分析主体下拉选组件 -->
 <template>
    <a-select v-model="params.subject" :disabled="disabled" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
        <a-trigger v-for="item in barList" :key="item.code" :trigger="['hover']" position="right" :update-at-scroll="true">
        <a-option style="width: 140px;" :style="optionStyle" :value="item.code">{{ item.name }}</a-option>
        <template #content>
            <div class="trigger-box">
            <div class="card-header">
                <div class="card-header-container">
                  <div class="header-icon">
                      <IconFont type="icon-crosshair-2-fill" :size="14"/>
                  </div>
                  <div class="header-title">
                      <div class="name">{{ item.name }}</div>
                  </div>
                  <div class="header-type">自定义分析主体</div>
                </div>
            </div>
            <div class="card-desc">
                <div class="span-desc">{{ item.description==''?'暂无描述' : item.description }}</div>
            </div>
            <div class="card-footer">
                <div>来源属性</div>
                <div>
                  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
                      <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                          <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                          <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                      </svg>
                  </svg>
                {{item.source}}
                </div>
            </div>
            </div>
        </template>
        </a-trigger>
    </a-select>
 </template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch} from "vue";
import {analyseStore} from '@/store';

const analyseData = analyseStore()
const props = defineProps({
  subjectData:{
    type:Object,
    default: () => ({
      subject:'',
      subjectName:''
    })
  },
  disabled:{
    type:Boolean,
    default: () => false
  },
  optionStyle:{
    type:Object,
    required:false
  }
})
const params = reactive({
    subject:'',
    subjectName:''
})
const barList = ref<any>([])
const emits = defineEmits(['subjectChange'])

onMounted(async () => {
  const data = analyseData.$state.subjectLists.length > 0 ? analyseData.$state.subjectLists : await analyseData.fetchSubjectLists()
  const filteredData = data?.filter(item => ['user'].includes(item.source));
  barList.value = filteredData
  const {code,name} = filteredData[0]
  params.subject = props.subjectData.subject ? props.subjectData.subject : code
  params.subjectName = props.subjectData.subjectName ? props.subjectData.subjectName : name
})
watch(() => params.subject,() => {
    emits('subjectChange',params)
},{deep:true})
</script>

<style lang="less" scoped>
.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);
  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;
    .card-header-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      .header-icon {
        // margin-right: 5px;
        margin-bottom: 1px;
        color: var(--tant-text-gray-color-text1-1);
      }
      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);
        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          // line-height: 22px;
        }
      }
      .header-type {
        flex-shrink: 0;
        // min-width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }
    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }
  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;
    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }
  .card-footer {
    // display: flex;
    // flex-shrink: 0;
    // justify-content: space-between;
    height: auto;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: rgba(0, 0, 0, .85);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;
    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}
</style>