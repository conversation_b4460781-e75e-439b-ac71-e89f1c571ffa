<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {getDays} from "@/utils/dateUtil";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 分组
   */
  group: string[]

  /**
   * 数据
   */
  data: number[][]

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '0',
      top: '10',
      // bottom: '24',
      bottom: '48'
    },
    legend: {
      data: props.group,
      bottom: '0',
      type: 'scroll'
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      className: 'echarts-tooltip-diy',
      extraCssText: 'max-height: 400px; overflow-y: auto;',
      formatter: (params) => {
        // 计算当前时间点的总值
        const totalValue = params.reduce((sum, item) => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          return sum + value;
        }, 0);
        
        const seriesData = params.map(item => ({
          seriesName: item.seriesName,
          value: item.value,
          color: item.color,
          dataIndex: item.dataIndex
        }));
        // 按值从大到小排序
        seriesData.sort((a, b) => {
          const valueA = typeof a.value === 'number' ? a.value : parseFloat(a.value) || 0;
          const valueB = typeof b.value === 'number' ? b.value : parseFloat(b.value) || 0;
          return valueB - valueA;
        });
        let tooltipContent = params[0].name;
        seriesData.forEach(item => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          const percentage = totalValue > 0 ? ((value / totalValue) * 100).toFixed(2) : 0;
          const formattedValue = item.value;
          tooltipContent += `<br><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span> ${item.seriesName}: ${formattedValue} <span style="padding-left:8px;color:${item.color}">(${percentage}%)</span>`;
        });
        return tooltipContent;
      }
    },
    xAxis: {
      type: 'category',
      data: getDays(props.date[0], props.date[1]),
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`; // 转换为 k 单位
          }
          if (value >= 1000) {
            return `${value / 1000}k`; // 转换为 k 单位
          }
          return value;
        }
      }
    },
    series: props.group.map((item: any, index: number) => {
      return {
        name: item,
        type: 'line',
        data: props.data[index],
        label: {
          show: true,
          position: 'top',
          formatter(params) {
            if (!params.value || typeof params.value !== 'number') {
              return params.value;
            }
            if (params.value >= 1000000) {
              return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
            }
            if (params.value >= 1000) {
              return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
            }
            return Math.round(params.value); // 保留整数
          }
        }
      }
    })
  };
});


</script>

<template>
  <Chart :option="chartOption"/>
</template>

<style scoped lang="less">

</style>