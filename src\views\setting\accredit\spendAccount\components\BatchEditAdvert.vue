
<template>
    <!-- meta编辑广告账户 -->
    <a-modal v-model:visible="modalVisible" :width="600" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="info-content">
            <h3>广告账户</h3>
            <div class="account-info">
                <div class="accountList">
                    <img src="/icon/meta.svg" alt="" class="icon">
                    <div class="detail">
                        <div class="title">Madhouse-Leyoo-GAME-2749-4-Merge Neverland-Android</div>
                        <div class="subTitle">***************</div>
                    </div>
                </div>
                <div class="accountList">
                    <img src="/icon/meta.svg" alt="" class="icon">
                    <div class="detail">
                        <div class="title">Madhouse-Leyoo-GAME-2749-3-Merge Neverland-Android</div>
                        <div class="subTitle">***************</div>
                    </div>
                </div>
                <div>等2个广告账户</div>
            </div>
        </div>
        <a-divider/>
        <a-form ref="formRef" :model="form" :rules="rules">
            <h3>统一设置</h3>
            <a-form-item field="setBelong" label="" validate-trigger="blur">
                <a-checkbox v-model:model-value="form.setBelong">统一设置所属人员</a-checkbox>
            </a-form-item>
            <a-form-item v-if="form.setBelong" field="belongPerson" label="" validate-trigger="change" :hide-asterisk="true">
                <a-cascader v-model:model-value="form.belongPerson" placeholder="请选择" :options="belongOptions" />
            </a-form-item>
            <a-form-item field="setAssist" label="" validate-trigger="blur">
                <a-checkbox v-model:model-value="form.setAssist">统一设置协助人员</a-checkbox>
            </a-form-item>
            <a-form-item v-if="form.setAssist" field="setAssistType" label="" validate-trigger="blur">
                <a-radio-group v-model:model-value="form.setAssistType">
                    <a-radio value="1">添加协助人员</a-radio>
                    <a-radio value="2">
                        修改协助人员
                        <a-tooltip placement="top" content="全量覆盖已选广告账户的广告人员">
                            <icon-question-circle />
                        </a-tooltip>
                    </a-radio>
                    <a-radio value="3">清空协助人员</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item v-if="form.setAssist && form.setAssistType != '3'" field="assistPerson" label="" validate-trigger="change" :hide-asterisk="true">
                <a-cascader v-model:model-value="form.assistPerson" placeholder="请选择" multiple :options="belongOptions" />
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('编辑广告账户')

const formRef = ref()
const form = reactive({
    setBelong:false,
    belongPerson:'',
    setAssist:false,
    setAssistType:'1',
    assistPerson:''
})
const rules = {
    belongPerson: [
        {
            required: true,
            message:'请选择所属人员',
        }
    ],
    assistPerson: [
        {
            required: true,
            message:'请选择协助人员',
        }
    ]
}
const belongOptions = [
    {
        value: '1',
        label: 'admin',
        children: [
            {
                value: '1-1',
                label: '李总',
            },
            {
                value: '1-2',
                label: '李哥',
            },
        ],
    },
    {
        value: '2',
        label: 'manager',
        children: [
            {
                value: '2-1',
                label: '张总',
            },
        ],
    },
];
const emits = defineEmits(['updateData']);
const openModal = async (channel?:string) => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.info-content{
    display: flex;
}
h3{
    width: 80px;
    line-height: 16px;
    word-wrap: normal;
    word-break: break-word;
    font-size: 14px;
    color: #000;
}
.accountList{
    display: flex;
    margin-top: 5px;
    align-items: center;
    .icon{
        height: 18px;
        display: inline-flex;
        width: 18px;
        flex-shrink: 0;
    }
    .detail{
        overflow: hidden;
        margin-left: 10px;
        box-sizing: border-box;
        .title,.subTitle{
            height: 20px;
            line-height: 20px;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .title{
            color: #000;
        }
       .subTitle{
            color: #818181;
       }
    }
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    // button{
    //     border-radius: 4px;
    // }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>