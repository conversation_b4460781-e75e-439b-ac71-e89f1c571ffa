<template>
  <a-modal
    v-model:visible="visible"
    title-align="start"
    :title="handleType === 'edit' ? '编辑维度表' : '新增维度表'"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item field="tableCode" label="数据表名" :rules="[{ required: true, message: '请输入数据表名' }]">
        <a-input v-model="formData.tableCode" placeholder="请输入数据表名" :disabled="handleType === 'edit'"/>
      </a-form-item>
      <a-form-item field="tableName" label="维度名称" :rules="[{ required: true, message: '请输入维度名称' }]">
        <a-input v-model="formData.tableName" placeholder="请输入维度名称" />
      </a-form-item>
      <a-form-item field="majorKey" label="主键属性" :rules="[{ required: true, message: '请输入主键字段' }]">
        <a-input v-model="formData.majorKey" placeholder="请输入主键属性"  :disabled="handleType === 'edit'"/>
      </a-form-item>
      <a-form-item field="dataType" label="主键数据类型" :rules="[{ required: true, message: '请选择主键数据类型' }]">
        <a-select v-model="formData.dataType" placeholder="请选择主键数据类型" :disabled="handleType === 'edit'">
          <a-option value="string">文本</a-option>
          <a-option value="boolean">布尔</a-option>
          <a-option value="date">日期</a-option>
          <a-option value="datetime">时间</a-option>
          <a-option value="array">列表</a-option>
          <a-option value="variant">对象</a-option>
          <a-option value="int">整数</a-option>
          <a-option value="float">小数</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="relatedAttribute" label="关联属性">
        <a-input v-model="relatedAttributeString" disabled />
      </a-form-item>
      <a-form-item field="sourceEvent" label="更新事件" :rules="[{ required: true, message: '请输入更新事件' }]">
        <a-input v-model="formData.sourceEvent" placeholder="请输入更新事件" />
      </a-form-item>
      <a-form-item field="tableDesc" label="描述说明" :rules="[{ required: true, message: '请输入描述说明' }]">
        <a-textarea
          v-model="formData.tableDesc"
          placeholder="请输入描述说明"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleOk">保存</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from 'vue';
import {Message} from '@arco-design/web-vue';
import {saveDimension} from "@/api/setting/api";

const visible = ref(false);
const formRef = ref();

const formData = reactive({
  tableCode: '',
  tableName: '',
  majorKey: '',
  dataType: '',
  relatedAttribute: [],
  sourceEvent: '',
  tableDesc: ''
});

const relatedAttributeString = computed(() => {
  return formData.relatedAttribute.map(item => item?.name).join(', ');
});
const rules = {
  // tableCode: [{ required: true, message: '请输入数据表名' }],
  // tableName: [{ required: true, message: '请输入维度名称' }],
  // majorKey: [{ required: true, message: '请输入主键字段' }],
  // dataType: [{ required: true, message: '请选择主键数据类型' }]
};

const emit = defineEmits(['success']);

const handleType = ref('create')
const openModal = (record?: any) => {
  visible.value = true;
  if (record) {
    handleType.value = 'edit';
    Object.assign(formData, record);
  } else {
    handleType.value = 'create';
    Object.keys(formData).forEach(key => {
      formData[key] = '';
    });
  }
};

const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

const loading = ref(false);
const handleOk = async () => {
  formRef.value.validate(async (valid:any) => {
    if (!valid) {
      loading.value = false;
      try {
        await saveDimension(formData);
        Message.success('保存成功');
        emit('success');
        visible.value = false;
      } catch (error) {
        Message.error('保存失败');
      } finally {
        loading.value = false;
      }
    }
  })
  
};
defineExpose({
  openModal
});
</script>

<style scoped lang="less">
</style>