/**
 * 校验指标公式的合法性
 * 包含 + - * / ( ) 运算
 *
 * @param eventNum 事件个数
 * @param formula 计算公式
 */
export const validateFormula = (eventNum: number, formula: string): boolean => {
  if (eventNum < 1 || !formula) return false;

  // 生成合法的大写字母范围（如 eventNum=3，则 validLetters=['A', 'B', 'C']）
  const validLetters = Array.from({ length: eventNum }, (_, i) => String.fromCharCode(65 + i));
  const validChars = new RegExp(`^[${validLetters.join('')}0-9+.\\-*/() ]+$`);

  // 检查是否包含非法字符
  if (!validChars.test(formula)) return false;

  // 确保字母之间没有直接相连
  if (/[A-Z]{2,}/.test(formula)) return false;

  // 确保运算符不会连续出现（例如 **, //, ++ 等非法格式）
  if (/[*\\/+\\-]{2,}/.test(formula)) return false;

  try {
    // 替换字母为合法数字（如 A=1, B=2, ...），保证 eval 计算不出错
    const replacedFormula = formula.replace(/[A-Z]/g, '1');

    // 解析计算式是否合理（eval 会检查括号匹配及符号规则）
    // eslint-disable-next-line no-eval
    eval(replacedFormula);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * 分割公式内容
 * @param formula 公式字符串
 */
export const splitFormula = (formula: string): string[] => {
  if (!formula) return [];
  return formula.match(/\d*\.?\d+|[A-Z]+|[+\-*/()]/g) || [];
};