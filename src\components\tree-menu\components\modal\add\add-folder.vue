<script setup lang="ts">
import {ref} from "vue";
import {SpaceDto} from "@/api/space/type";
import _ from "lodash"
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {Message} from "@arco-design/web-vue";
import {saveFolder} from "@/api/folder/api";
import {useEventBus} from "@vueuse/core";

interface Props {

  /**
   * 指定空间新建文件夹
   */
  addInSpace?: string

  /**
   * 我的看板
   */
  mySpace: SpaceDto

  /**
   * 项目空间
   */
  projectSpaces: SpaceDto[]
}

const props = defineProps<Props>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const cacheEventBus = useEventBus(CacheEventBus)


const visible = defineModel<boolean>("visible", {default: false});
const folderName = ref<string>();
const folderPosition = ref<string>('my');
const selectedPosition = ref<string>();

const handleOk = () => {
  const spaceId = _.isEmpty(props?.addInSpace) ? (folderPosition.value === 'my' ? props.mySpace.spaceId : selectedPosition.value) : props.addInSpace;
  if (!folderName.value?.trim()) {
    Message.warning("文件夹名称不能为空");
    return
  }
  if (!spaceId) {
    Message.warning("文件夹位置不能为空");
    return
  }
  saveFolder({name: folderName.value, spaceId}).then((resp) => {
    const newFolder=_.cloneDeep(resp)
    cacheEventBus.emit('add-event', {
      type: 'folder',
      folder: newFolder,
      spaceId,
    })
    folderName.value = undefined
    visible.value = false;
  }).catch((e) => {
    Message.error("新建文件夹失败！", e)
  })
}
const handleCancel = () => {
  visible.value = false;
  folderName.value = undefined
}
</script>

<template>
  <a-modal
      :visible="visible"
      :mask-closable="false"
      :width="450"
      title-align="start"
      ok-text="新建"
      title="新建文件夹"
      @ok="handleOk"
      @cancel="handleCancel">
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          文件夹名称
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-input v-model:model-value="folderName" placeholder="请输入文件夹名称"/>
      </a-col>
    </a-row>
    <a-row v-if="_.isEmpty(addInSpace)" class="item" align="center">
      <a-col :span="5">
        <div class="label">
          添加至
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-radio-group type="button" :model-value="folderPosition" @change="value => {folderPosition=value}">
          <a-radio value="my">我的看板</a-radio>
          <a-radio value="other">项目空间</a-radio>
        </a-radio-group>
      </a-col>
    </a-row>
    <a-row v-if="_.isEmpty(addInSpace) && folderPosition==='other'" class="item" align="center">
      <a-col :span="5">
      </a-col>
      <a-col :span="18" :offset="1">
        <a-select placeholder="请选择添加位置" v-model="selectedPosition">
          <template #label="{ data }">
            <div class="space-select-option">
              <img class="icon" src="/icon/cube-open.svg" alt=""/>
              <div class="label">
                {{ data.label }}
              </div>
            </div>
          </template>
          <a-option v-for="item in projectSpaces" :key="item.spaceId" :value="item.spaceId">
            <div class="space-select-option">
              <img class="icon" src="/icon/cube-open.svg" alt=""/>
              <div class="label">
                {{ item.name }}
              </div>
            </div>
          </a-option>
        </a-select>
      </a-col>
    </a-row>
  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}

.space-select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

</style>