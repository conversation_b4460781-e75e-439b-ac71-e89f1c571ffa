import {defineStore} from "pinia";
import {DashboardState} from "./type"

const useDashboardStore = defineStore("dashboardData", {
    state: (): DashboardState =>
        ({
            spaceList: [],
            appSelected: undefined,
            dashboardSelected: undefined,
            loading: undefined,
            dashboardSearchStore: undefined,
        }),
    actions: {},

});
export default useDashboardStore;