<template>
    <!-- 默认区间 离散区间 自定义区间选择组件 -->
    <a-popover v-model:popup-visible="numberGroupPopup" trigger="click" position="bottom">
        <div class="set-icon" @click="() => numberGroupPopup = true">
            <icon-settings />
        </div>
        <template #content>
            <div class="number-group-set-interval-root">
            <a-tabs v-model:active-key="activeKey" default-active-key="st12" @change="activeKeyChange">
                <a-tab-pane key="st12" title="默认区间">
                    <div class="number-group-set-read" style="margin-bottom: 16px;">
                        <div class="number-group-set-tip-content">
                            <p>区间数根据最大值与最小值的差值而定：</p>
                            <p>当差值 ≤ 12 时，自动转化为离散数字；</p>
                            <p>当差值 > 12 时，将动态调整区间数。
                                <a href="http://" target="_blank" rel="noopener noreferrer" style="color: var(--tant-primary-color-primary-default);margin-left: 8px;">了解详情</a>
                            </p>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="discrete" title="离散数字">
                    <div class="number-group-set-read" style="margin-bottom: 16px;">
                        <div class="number-group-set-tip-content">
                            例：1, 2, 3, 4, 5, 6, 7, 8, ..., 100, ...
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="custom" title="自定义区间">
                    <div class="number-custom-range-container">
                        <div class="number-group-set-read">
                            <div v-for="(item,index) in rangeList" :key="index" class="interval-range-wrap">
                                <a-input-number v-model:model-value="item.number" :precision="4" :min="0" @blur="numberChange(item.number,index)"/> 
                            </div>
                            <div class="interval-range-wrap">
                                <a-input-number v-model:model-value="addNumber" :precision="4" :min="0" @blur="addNumberChange"/> 
                            </div>
                        </div>
                    </div>
                    <div class="number-custom-range-batch-add">
                        <a-button type="text" @click="batchAdd">
                            <template #icon>
                                <icon-plus />
                            </template>
                            批量添加
                        </a-button>
                    </div>
                    <div class="number-custom-range-display">{{rangeString}}</div>
                </a-tab-pane>
            </a-tabs>
            <div v-if="activeKey === 'custom' && showAddBatch" class="number-custom-range-batch" style="right: -301px;">
                <div class="number-custom-range-batch-header">
                    <a-radio-group v-model:model-value="batchType">
                        <a-radio value="batch">
                        批量替换
                        <a-tooltip content="使用回车换行分隔" position="top">
                            <icon-info-circle/>
                        </a-tooltip>
                        </a-radio>
                        <a-radio value="fast">快速添加</a-radio>
                    </a-radio-group>
                </div>
                <div class="number-custom-range-batch-content">
                    <a-textarea v-if="batchType === 'batch'" v-model:model-value="textAreaValue" placeholder="使用回车换行分割" style="height: 150px;"/>
                    <a-form v-else :model="stepForm">
                        <a-form-item field="stepSize" label="步数">
                            <a-input-number v-model:model-value="stepForm.stepNum" :precision="0" :min="1"/>
                        </a-form-item>
                        <a-form-item field="stepNum" label="步长">
                            <a-input-number v-model:model-value="stepForm.stepSize" :precision="4" :min="0"/>
                        </a-form-item>
                    </a-form>
                </div>
                <div class="number-custom-range-batch-footer">
                    <a-button class="cancel" @click="() => showAddBatch = false">取消</a-button>
                    <a-button type="primary" :disabled="stepForm.stepNum<1" @click="numberCustomBatch">批量添加</a-button>
                </div>

            </div>
            <div class="number-group-set-footer">
                <span></span>
                <div class="number-group-set-footer-actions">
                    <a-button class="cancel" @click="cancelRoot">取消</a-button>
                    <a-button type="primary" @click="submit">应用</a-button>
                </div>
            </div>
            </div>
        </template>
    </a-popover>
</template>

<script setup lang="ts">
import {computed, nextTick, reactive, ref, watch} from "vue";

const props = defineProps({
    setData: {
        type:Object,
        default:() => {}
    }
})
const activeKey = ref('st12')
const numberGroupPopup = ref(false)
const rangeList = ref<any>([{number:0}])
const addNumber = ref()
const showAddBatch = ref(false)
const batchType = ref('batch')
const textAreaValue = ref('')
// const rangeString = ref('')
const stepForm = reactive({
    stepNum:1,
    stepSize:50
})
const setInfo = reactive({
    numberSummaryType:'',
    numberSummaryScope:[]
})


// const init = () => {
//     activeKey.value = props.setData?.numberSummaryType ? props.setData.numberSummaryType : 'st12'
//     setInfo.numberSummaryType = props.setData?.numberSummaryType ? props.setData.numberSummaryType : ''
//     setInfo.numberSummaryScope = props.setData?.numberSummaryScope ? props.setData.numberSummaryScope : []
//     if (setInfo?.numberSummaryScope?.length) {
//         rangeList.value = setInfo.numberSummaryScope.map(number => ({ number }))
//     }
// }
// init()

const emits = defineEmits(['numberSet']);
const numberChange = (number,index) => {
  if(number){
    rangeList.value.sort((a, b) => a.number - b.number);
  }else{
    rangeList.value.splice(index,1)
  }
}
const addNumberChange = () => {
    if(addNumber.value){
        if (rangeList.value.map(el => el.number).indexOf(addNumber.value) === -1) {
            rangeList.value.push({ number:addNumber.value })
        }
    }
    nextTick(() => {
        addNumber.value = null
    })
  
}
const batchAdd = () => {
    textAreaValue.value = rangeList.value.map(item => item.number).join("\n")
    showAddBatch.value = true
}
const cancelRoot = () => {
  numberGroupPopup.value = false
}
const numberCustomBatch = () => {
  const lines = textAreaValue.value.split('\n').map((line) => {
      const number = parseFloat(line);
      return Number.isNaN(number) ? null : number;
  });
  const numbers = lines.filter((number) => {
      return number !== null;
  }).sort((a, b) => a - b)
  if(batchType.value === 'batch'){
    if(numbers.length>0){
        rangeList.value = numbers.map(item => {
            return {
                number:item
            }
        })
    }else{
        rangeList.value =[{number:0}]
    }
  }else{
    const length = rangeList.value.length;
    for (let i = 0; i < stepForm.stepNum; i++) {
        const newNumber = rangeList.value[length - 1].number + (stepForm.stepSize * (i + 1));
        rangeList.value.push({ number: newNumber });
    }
  }
  showAddBatch.value = false
}

// 应用
const submit = () => {
    const scopeList = rangeList.value.map(item => item.number)
    setInfo.numberSummaryScope = setInfo.numberSummaryType === 'custom' ? scopeList : []
    emits('numberSet',setInfo)
    numberGroupPopup.value = false
}
const activeKeyChange = (v) => {
    setInfo.numberSummaryType = activeKey.value
}
// watch(rangeList, (newValue, oldValue) => {
//   if(rangeList.value.length){
//     rangeList.value.forEach((item,index) => {
//       if(index === 0) {
//         rangeString.value = `(-∞,${item.number})`
//       }
//       if(rangeList.value[index+1]?.number){
//         rangeString.value += `[${item.number},${rangeList.value[index+1].number}]`
//       }
//       if(index === rangeList.value.length -1){
//         rangeString.value += `[${item.number},+∞)`
//       }
//     })
//     setInfo.numberSummaryScope = rangeList.value.map(item => item.number)
//   }
// },{immediate:true,deep:true})
const rangeString = computed(() => {
  if (!rangeList.value.length) return '';
  let result = '';
  rangeList.value.forEach((item, index) => {
    if (index === 0) {
      result = `(-∞,${item.number})`;
    }
    if (rangeList.value[index + 1]?.number) {
      result += `[${item.number},${rangeList.value[index + 1].number}]`;
    }
    if (index === rangeList.value.length - 1) {
      result += `[${item.number},+∞)`;
    }
  });
  
  return result;
});
watch(() => props.setData, (newVal) => {
    activeKey.value = newVal?.numberSummaryType ? newVal.numberSummaryType : 'st12'
    setInfo.numberSummaryType = newVal?.numberSummaryType ? newVal.numberSummaryType : ''
    setInfo.numberSummaryScope = newVal?.numberSummaryScope ? newVal.numberSummaryScope : []
    if (newVal?.numberSummaryScope?.length) {
        rangeList.value = newVal.numberSummaryScope.map(number => ({ number }))
    }
    if(activeKey.value !== 'custom'){
        rangeList.value = [{number:0}]
    }
}, { immediate: true,deep:true})
</script>

<style lang="less" scoped>
.set-icon{
    font-size: 16px;
    height: 26px;
    width: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--tant-text-gray-color-text1-3);
    transition: all .3s;
    border: 1px solid transparent;
    background-color: var(--tant-secondary-color-secondary-fill);
    margin: 0 8px;
    &:hover{
        border: 1px solid var(--tant-primary-color-primary-default)
    }
}
.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}
.number-group-set-interval-root {
    display: block;
    width: auto;
    width: 360px;
    margin: -16px;
    cursor: auto !important;
    position: relative;

    .number-custom-range-container{
      max-height: 225px;
      overflow-y: auto;
    }
    .number-group-set-tip-content{
        color: var(--tant-text-gray-color-text1-3);
        font: var(--tant-description-font-description-regular);
    }
    .number-group-set-footer{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 16px 12px;
      border-top: 1px solid var(--tant-border-color-border1-1);
    }
    .number-group-set-read{
      padding: 0 16px;
      .interval-range-wrap {
          display: flex;
          align-items: center;
          padding-bottom: 16px;
      }
    }
    .number-custom-range-batch-add {
        margin: 0 0 4px;
        padding: 0 16px;
        text-align: right;
    }
    .number-custom-range-display{
      max-width: 360px;
      max-height: 60px;
      padding: 0 16px;
      padding-bottom: 16px;
      overflow: auto;
      color: var(--tant-text-gray-color-text1-3);
      font: var(--tant-description-font-description-regular);
    }
    .number-custom-range-batch{
      position: absolute;
      top: 0;
      z-index: 99999;
      width: 300px;
      background-color: var(--tant-bg-white-color-bg1-1);
      border-radius: 4px;
      box-shadow: var(--tant-small-shadow-small-bottom);
      .number-custom-range-batch-header{
        padding: 12px 16px 0;
      }
      .number-custom-range-batch-content{
        padding: 12px 16px 16px;
      }
      .number-custom-range-batch-footer{
        padding: 8px 16px;
        text-align: right;
        border-top: 1px solid var(--tant-border-color-border1-1);
      }
  }
}
</style>