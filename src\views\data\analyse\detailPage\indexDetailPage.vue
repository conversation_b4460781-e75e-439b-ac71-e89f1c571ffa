<template>
    <div class="main">
        <a-breadcrumb>
            <a-breadcrumb-item style="cursor:pointer;" @click="gotoItem('dataIndex')">指标</a-breadcrumb-item>
            <a-breadcrumb-item style="cursor:pointer;">详情页</a-breadcrumb-item>
        </a-breadcrumb>
        <a-spin :loading="loading" class="contentContainer">
            <div class="content" style="max-height: 20vh;">
                <div class="contentHead">
                    <div class="title">{{ formData.name }}</div>
                    <div class="subTitle">{{ formData.displayName }}</div>
                    <a-button v-if="userName === formData.creator" type="text" style="margin-left: auto;" @click="editIndex">前往编辑</a-button>
                </div>
                <div class="basicInfoContainer">
                    <a-form label-align="left">
                        <a-row :gutter="24">
                            <a-col :span="12">
                                <a-form-item label="创建方式">
                                    <div v-if="formData.type === 'retention'">留存分析</div>
                                    <div v-else>事件分析</div>
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="展示格式">
                                    <div>{{ FormatDisplayType(formData.displayType) }}</div>
                                </a-form-item>
                            </a-col>
                            <a-col v-if="formData.type === 'retention'" :span="12">
                                <a-form-item label="指标计算">
                                    <div>第1日的留存率</div>
                                </a-form-item>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item label="备注">
                                    <div>{{ formData.description }}</div>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>
            </div>
            <div class="calcContentContainer">
                <div class="content">
                    <div v-if="formData.type === 'retention'" class="tabTitle">指标逻辑</div>
                    <div v-else class="tabTitle">计算方式</div>
                    <div v-if="formData.type === 'retention'" class="metricContent">
                        <div class="model-filter">
                            <div class="retention-readonly-div ">
                                <span class="retention-readonly-h1">分析主体:</span>
                                <span class="retention-readonly-p">
                                    设备号主体
                                </span>
                            </div>
                            <div class="retention-readonly-div ">
                                <span class="retention-readonly-h1">初始事件:</span>
                                <span style="line-height: 24px;">
                                    <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;">
                                    <span class="retention-readonly-p">
                                        用户登录
                                    </span>
                                </span>
                                
                            </div>
                            <div class="retention-readonly-div ">
                                <span class="retention-readonly-h1">回访事件:</span>
                                <span style="line-height: 24px;">
                                    <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;">
                                    <span class="retention-readonly-p">
                                        角色升级
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div v-else class="eventMetricMetric">
                        <div v-for="(item,index) in eventList" :key="index" class="event-item">
                            <span v-if="item.startFormula">{{ item.startFormula }}</span>
                            <span style="line-height: 24px;">
                                <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;margin-right: 4px;">
                                <span class="event-label">{{ item.eventDisplayName }}</span>
                            </span>
                            <eventScreenPopup :read-only="true" :info="item" :code-list="eventList"/>
                            <span style="margin: 0 4px;">的</span>
                            <span class="event-label">{{ item.eventAttrName }}</span>
                            <span v-if="item.endFormula">{{ item.endFormula }}</span>
                        </div>
                        <eventQueryFilter v-if="formData.filter && formData.filter?.filters?.length" :filter="formData.filter" :disabled="true" :show-detail-filter="true" :code-list="formData.eventList"/>
                    </div>
                </div>
            </div>
        </a-spin>
    </div>
</template>

<script setup lang="js">
import {computed, ref} from "vue";
import router from "@/router";
import {useRoute} from 'vue-router';
import {getIndicatorDetail} from "@/api/setting/api";
import {cloneDeep} from 'lodash';
import {useUserStore} from '@/store';
import eventScreenPopup from "@/views/analyse/components/EventScreenPopup.vue"
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import {ROUTE_NAME} from "@/router/constants";

const userStore = useUserStore();
const userName = computed(() => {
  return userStore.code;
});

const route = useRoute();
const code = ref(route.query.code)
const formData = ref({
})
const gotoItem = (item) => {
  router.push({
    name: item
  });
};
const editIndex=()=>{
  router.push({
    name: ROUTE_NAME.REPORT_EDIT,
    query: {code: code.value,type:'edit'}
  });
}
const loading = ref(false)
const FormatDisplayType = (v) => {
    if(v){
        const formatData = [
            {label: '2位小数', value: 'default2'},
            {label: '3位小数', value: 'default3'},
            {label: '4位小数', value: 'default4'},
            {label: '百分比', value: 'percent'},
            {label: '取整', value: 'default0'},
        ]
        const labels = {
            'default2': {type:'default',decimalNum:2,thousandSep: 1},
            'default3': {type:'default',decimalNum:3,thousandSep: 1},
            'default4': {type:'default',decimalNum:4,thousandSep: 1},
            'percent': {type:'percent',decimalNum:2,thousandSep: 1},
            'default0': {type:'default',decimalNum:0,thousandSep: 1},
        };
        const matchingValue = Object.keys(labels).find(key => {
            const label = labels[key];
            return label.type === v.type && label.decimalNum === v.decimalNum && label.thousandSep === v.thousandSep;
        });
        if (matchingValue) {
            const format = formatData.find(item => item.value === matchingValue);
            return format ? format.label : null;
        }
        return '2位小数';
    }
    
}
const eventList = ref([])
const init = async () => {
    loading.value = true
    await getIndicatorDetail(code.value).then(res => {
        if(res){
            console.log(res,'222');
            formData.value = res;
            // formData.value.formula = '42*${0}+'
            eventList.value = cloneDeep(formData.value.eventList)
            if(formData.value.formula && formData.value.eventList.length > 0){
                eventList.value.forEach((item, index) => {
                    if (index === 0) {
                        item.startFormula = formData.value.formula.split('${')[0];
                    }
                    const endFormulaParts = formData.value.formula.split(/\$\{\d+\}/);
                    if (index < endFormulaParts.length - 1) {
                        item.endFormula = endFormulaParts[index + 1].trim();
                    } else {
                        item.endFormula = '';
                    }
                });
            }
        }
    })
    loading.value = false
}
init()
</script>

<style scoped lang="less">
.main{
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 90vh;
}
:deep(.arco-btn-text):hover{
    background: transparent;
}
.contentContainer {
    display: flex;
    flex: 1 1;
    flex-direction: column;
    width: 100%;
    margin-top: 16px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
    .content{
        // max-height: 20vh;
        padding: 24px;
        overflow-y: scroll;
        &:not(:last-of-type) {
            border-bottom: 1px solid var(--tant-border-color-border1-1);
        }
        .tabTitle{
            margin-bottom: 12px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-body-font-body-regular);
        }
        .contentHead{
            display: flex;
            align-items: center;
            .title{
                margin-right: 16px;
                color: var(--tant-text-gray-color-text1-1);
                font: var(--tant-header-font-header4-medium);
            }
            .subTitle{
                color: var(--tant-text-gray-color-text1-3);
                font: var(--tant-header-font-header5-regular);
            }
        }
        .basicInfoContainer{
            margin-top: 16px;
            :deep(.arco-form-item ){
                margin-bottom: 0px!important;
            }
        }
        .metricContent{
            padding: 16px 24px;
            background-color: var(--tant-fill-color-fill1-2);
            .retention-readonly-div {
                line-height: 32px;
                .retention-readonly-h1{
                    margin-right: 12px;
                    color: var(--tant-text-gray-color-text1-2);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 32px;
                }
                .retention-readonly-p{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 12px;
                }
            }
        }
        .eventMetricMetric {
            padding: 16px 24px;
            background-color: var(--tant-fill-color-fill1-2);
            white-space: pre-wrap;
            overflow-wrap: break-word;
        }
        .event-item{
            display: inline-flex;
            align-items: center;
            margin-right: 16px; 
        }
        .event-label {
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
        }
    }
    .calcContentContainer{
        flex: 1 1;
        overflow-y: auto;
    }
}
</style>