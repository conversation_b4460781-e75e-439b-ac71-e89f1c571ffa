/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AAffix: typeof import('@arco-design/web-vue')['Affix']
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AAnchor: typeof import('@arco-design/web-vue')['Anchor']
    AAnchorLink: typeof import('@arco-design/web-vue')['AnchorLink']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    AButtonGroup: typeof import('@arco-design/web-vue')['ButtonGroup']
    ACarousel: typeof import('@arco-design/web-vue')['Carousel']
    ACarouselItem: typeof import('@arco-design/web-vue')['CarouselItem']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    AColorPicker: typeof import('@arco-design/web-vue')['ColorPicker']
    AConfigProvider: typeof import('@arco-design/web-vue')['ConfigProvider']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    ADsubmenu: typeof import('@arco-design/web-vue')['Dsubmenu']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutHeader: typeof import('@arco-design/web-vue')['LayoutHeader']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AListItemMeta: typeof import('@arco-design/web-vue')['ListItemMeta']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOptgroup: typeof import('@arco-design/web-vue')['Optgroup']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    AResult: typeof import('@arco-design/web-vue')['Result']
    ARow: typeof import('@arco-design/web-vue')['Row']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    ASplit: typeof import('@arco-design/web-vue')['Split']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATimeline: typeof import('@arco-design/web-vue')['Timeline']
    ATimelineItem: typeof import('@arco-design/web-vue')['TimelineItem']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATreeSelect: typeof import('@arco-design/web-vue')['TreeSelect']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    ATypographyText: typeof import('@arco-design/web-vue')['TypographyText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
