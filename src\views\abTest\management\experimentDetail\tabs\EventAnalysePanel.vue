<template>
  <div class="analyse-body">
    <a-split v-model:size="splitSize" class="full-container" min="400px" max="0.9" :disabled="splitSize === '0px'">
      <template #first>
        <div class="body-left">
          <a-spin :loading="dataLoading" class="full-container">
            <div v-if="!dataLoading" class="query-condition">
              <!-- 分析指标 -->
              <AnalysisIndex
                  :analysis-index-data="analysisIndexData"
                  :full-event-lists="fullEventList"
                  @indicators-change="indicatorsChange"
                  @reset-params="reset"/>
              <!-- 全局筛选 -->
              <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
              <a-divider :margin="5" type="dashed" style="width: 100%;padding: 0 24px;"/>
              <!-- 用户筛选 -->
              <userFilter :user-filter="userFilterData" @filters-change="userFiltersChange"/>
            </div>
            <div class="left-footer">
              <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
            </div>
          </a-spin>
          <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
            <icon-double-left/>
          </div>
        </div>
      </template>
      <template #second>
        <div id="body-right" class="body-right">
          <div id="wrap" class="wrap" :style="{width:'calc(100% - 24px)'}">
            <a-spin :loading="loading" :size="100" class="body-right-content">
              <div class="right-header">
                <a-space direction="horizontal" size="large">
                  <div style="display: flex;align-items: center;">
                    筛选周期：
                    <DatePicker v-if="!dataLoading" :date-range="boardDate" @date-pick="datePick"/>
                  </div>
                  <div style="display: flex;align-items: center;">
                    颗粒度：
                    <dateSet v-if="!dataLoading" :time-particle="timeParticle" @time-change="timeChange"/>
                  </div>
                </a-space>
              </div>
              <div v-if="totalNum > 0" class="right-content">
                <a-table :style="{width: '100%'}" :loading="loading" :data="tableData" :scrollbar="true" :scroll="{ x: '100%', y: '100%' }" :pagination="false" :bordered="false" :hoverable="true" sticky-header :table-layout-fixed="true" :filter-icon-align-left="true" :column-resizable="true">
                  <template #columns>
                    <a-table-column title="实验分组" data-index="name" fixed="left" width="180">
                      <template #cell="{ record }">
                        <div class="variant-name">
                          <a-tag v-if="record?.type === 0" class="mini-tag" color="gray" bordered>对照组</a-tag>
                          <a-tag v-if="record?.type === 1" class="mini-tag" color="green" bordered>实验组</a-tag>
                          <div>{{ record?.name }}</div>
                        </div>
                        <div class="variant-id">
                          <div class="code">
                            ID: <a-tooltip :content="record?.code" position="lt"> <span>{{ record?.code }}</span> </a-tooltip>
                          </div>
                          <icon-copy :style="{cursor:'pointer',fontSize:'16px'}" @click="copyVariantId(record?.code)"/>
                        </div>
                      </template>
                    </a-table-column>
                    <a-table-column title="流量权重" data-index="flowPercentage" fixed="left" width="150" align="right">
                      <template #cell="{ record }">
                        {{ record.flowPercentage && record.flowPercentage > 0 ? record.flowPercentage + '%' : (record.flowPercentage || '--') }}
                      </template>
                    </a-table-column>
                    <a-table-column title="激活人数" data-index="userNumber" width="150" align="right">
                      <template #cell="{ record }">
                        {{ record.userNumber && record.userNumber > 0 ? formatIntegerValue(record.userNumber) : record.userNumber }}
                      </template>
                    </a-table-column>
                    <a-table-column title="人数占比" data-index="userNumberPercentage" width="150" align="right">
                      <template #cell="{ record }">
                        {{ record.userNumberPercentage && record.userNumberPercentage > 0 ? record.userNumberPercentage + '%' : (record.userNumberPercentage || '--') }}
                      </template>
                    </a-table-column>
                    <a-table-column v-for="(analysisIndexItem, index) in analysisIndexDataCopy" :key="index" :data-index="'indicator'+index" width="240" align="right">
                      <template #title>
                        <div class="table-title">
                          <a-tag v-if="analysisIndexItem?.eventList?.length === 1 && analysisIndexItem?.eventList[0].type === 'indicator' && analysisIndexItem?.eventList[0].indicatorCode === props.experimentDetail.coreIndicator.code" class="mini-tag" color="orange" bordered>核心</a-tag>
                          <div>{{ analysisIndexItem?.displayName }}</div>
                          <a-popover>
                            <icon-question-circle style="margin-left: 4px; cursor: pointer;"/>
                            <template #content>
                              <div class="indicator-content">
                                <div class="tip-title">{{ analysisIndexItem?.displayName }}</div>
                                <div class="tip-description">{{ analysisIndexItem?.description }}</div>
                                <a-divider :margin="6"/>
                                <CustomIndicatorDisplay :indicator="analysisIndexItem || {}"/>
                                <EventQueryFilter
                                    v-if="analysisIndexItem?.filter?.filters?.length > 0"
                                    :filter="analysisIndexItem?.filter"
                                    :disabled="true"
                                    :show-detail-filter="true"
                                    :code-list="analysisIndexItem?.eventList"
                                />
                              </div>
                            </template>
                          </a-popover>
                        </div>
                      </template>
                      <template #cell="{ record }">
                        <div v-if="record.type == 0">
                          {{ record.indicatorData[index]?.value }}
                        </div>
                        <div v-else-if="record.indicatorData[index]?.diffRate > 0"
                             :class="record.indicatorData[index]?.pValue == undefined || !props.experimentDetail?.confidenceLevel || record.indicatorData[index]?.pValue > 1 - props.experimentDetail?.confidenceLevel ? 'diff-rate' : 'diff-rate positive-background-color'">
                          <a-popover>
                            <div class="positive-color"> +{{ formatPercentageValue(record.indicatorData[index]?.diffRate) }}
                            </div>
                            <template #content>
                              <div class="tip-title">置信区间</div>
                              <div class="tip-description">由样本统计量构成的总体参数的估计区间，用于描述实验组指标<br/>相比于对照组在设定置信度水平上的涨跌幅度取值范围</div>
                              <a-divider :margin="6"/>
                              <div class="confidence-interval">
                                <div class="header-value">
                                  {{ formatPercentageValue(record.indicatorData[index]?.diffRate) }}
                                </div>
                                <div class="axis">
                                  <div class="interval-line" style="background-color: rgb(7, 163, 90)">
                                    <div class="interval-left"></div>
                                    <div class="interval-dot"></div>
                                    <div class="interval-right"></div>
                                  </div>
                                </div>
                                <div class="footer-value">
                                  <div>
                                    {{ formatPercentageValue(record.indicatorData[index]?.confidenceInterval?.[0]) }}
                                  </div>
                                  <div>
                                    {{ formatPercentageValue(record.indicatorData[index]?.confidenceInterval?.[1]) }}
                                  </div>
                                </div>
                              </div>
                              <div class="indicator-value-line">
                                <div class="label">指标值:</div>
                                <div class="value"> {{ record.indicatorData[index]?.value }}</div>
                                <div class="divide">|</div>
                                <div class="label">差异绝对值:</div>
                                <div class="value"> +{{ record.indicatorData[index]?.diffValue }}</div>
                              </div>
                            </template>
                          </a-popover>
                          <div style="margin-top: -4px">
                            <a-tooltip>
                              <span class="mde"> MDE:&nbsp;{{ formatPercentageValue(record.indicatorData[index]?.mde) }} </span>
                              <template #content>
                                最小可检测单位（检验灵敏度），指实验在当前条件下可检出置信度的最小指标变化幅度。若实验指标暂不显著，但MDE>预期提升值，则指标还有置信可能
                              </template>
                            </a-tooltip>
                            <a-tooltip v-if="record.indicatorData[index]?.pValue == undefined || !props.experimentDetail?.confidenceLevel || record.indicatorData[index]?.pValue > 1 - props.experimentDetail?.confidenceLevel" position="right">
                              <a-tag class="significant-tag"> 不显著</a-tag>
                              <template #content>
                                p-value: {{ record.indicatorData[index]?.pValue }}
                              </template>
                            </a-tooltip>
                            <a-tooltip v-else position="right">
                              <a-tag color="green" class="significant-tag">
                                正向显著
                              </a-tag>
                              <template #content>
                                p-value: {{ record.indicatorData[index]?.pValue }}
                              </template>
                            </a-tooltip>
                          </div>
                          <a-tooltip position="tr">
                            <div>{{ record.indicatorData[index]?.value }}</div>
                            <template #content>
                              差异绝对值 : {{ record.indicatorData[index]?.diffValue }}
                            </template>
                          </a-tooltip>
                        </div>
                        <div v-else-if="record.indicatorData[index]?.diffRate < 0"
                             :class="record.indicatorData[index]?.pValue == undefined || !props.experimentDetail?.confidenceLevel || record.indicatorData[index]?.pValue > 1 - props.experimentDetail?.confidenceLevel ? 'diff-rate' : 'diff-rate negative-background-color'">
                          <a-popover>
                            <div class="negative-color">
                              {{ formatPercentageValue(record.indicatorData[index]?.diffRate) }}
                            </div>
                            <template #content>
                              <div class="tip-title">置信区间</div>
                              <div class="tip-description">由样本统计量构成的总体参数的估计区间，用于描述实验组指标<br/>相比于对照组在设定置信度水平上的涨跌幅度取值范围</div>
                              <a-divider :margin="6"/>
                              <div class="confidence-interval">
                                <div class="header-value">
                                  {{ formatPercentageValue(record.indicatorData[index]?.diffRate) }}
                                </div>
                                <div class="axis">
                                  <div class="interval-line" style="background-color: rgb(7, 163, 90)">
                                    <div class="interval-left"></div>
                                    <div class="interval-dot"></div>
                                    <div class="interval-right"></div>
                                  </div>
                                </div>
                                <div class="footer-value">
                                  <div>
                                    {{ formatPercentageValue(record.indicatorData[index]?.confidenceInterval?.[0]) }}
                                  </div>
                                  <div>
                                    {{ formatPercentageValue(record.indicatorData[index]?.confidenceInterval?.[1]) }}
                                  </div>
                                </div>
                              </div>
                              <div class="indicator-value-line">
                                <div class="label">指标值:</div>
                                <div class="value"> {{ record.indicatorData[index]?.value }}</div>
                                <div class="divide">|</div>
                                <div class="label">差异绝对值:</div>
                                <div class="value"> +{{ record.indicatorData[index]?.diffValue }}</div>
                              </div>
                            </template>
                          </a-popover>
                          <div style="margin-top: -4px">
                            <a-tooltip>
                              <span class="mde"> MDE:&nbsp;{{ formatPercentageValue(record.indicatorData[index]?.mde) }} </span>
                              <template #content>
                                最小可检测单位（检验灵敏度），指实验在当前条件下可检出置信度的最小指标变化幅度。若实验指标暂不显著，但MDE>预期提升值，则指标还有置信可能
                              </template>
                            </a-tooltip>
                            <a-tooltip v-if="record.indicatorData[index]?.pValue == undefined || !props.experimentDetail?.confidenceLevel || record.indicatorData[index]?.pValue > 1 - props.experimentDetail?.confidenceLevel" position="right">
                              <a-tag class="significant-tag"> 不显著</a-tag>
                              <template #content>
                                p-value: {{ record.indicatorData[index]?.pValue }}
                              </template>
                            </a-tooltip>
                            <a-tooltip v-else position="right">
                              <a-tag color="green" class="significant-tag">
                                负向显著
                              </a-tag>
                              <template #content>
                                p-value: {{ record.indicatorData[index]?.pValue }}
                              </template>
                            </a-tooltip>
                          </div>
                          <a-tooltip position="tr">
                            <div>{{ record.indicatorData[index]?.value }}</div>
                            <template #content>
                              差异绝对值 : {{ record.indicatorData[index]?.diffValue }}
                            </template>
                          </a-tooltip>
                        </div>
                      </template>
                    </a-table-column>
                  </template>
                </a-table>
                <a-tabs v-model:active-key="chartType" type="card-gutter" class="chart-tabs">
                  <template #extra>
                    <a-select v-model="selectedIndicatorName" style="margin-bottom: 8px;width: 240px;" @change="handleIndicatorChange">
                      <a-option v-for="(indicator, index) in indicatorOptions" :key="index" :value="indicator.name">
                        {{ indicator.name }}
                      </a-option>
                    </a-select>
                  </template>
                  <a-tab-pane key="trend" class="chart-tab" title="趋势图">
                    <div class="trend-chart-option">
                      <div class="option-title">展示数据</div>
                      <a-radio-group v-model:model-value="trendChartType" style="height: 22px" @change="trendChartTypeChange">
                        <a-radio value="indicator">统计值</a-radio>
                        <a-radio value="diffRate">差异相对值</a-radio>
                        <a-radio value="pValue"> p-value</a-radio>
                      </a-radio-group>
                      <div class="option-title">范围展示</div>
                      <a-switch v-model:model-value="trendScopeDisplay" :disabled="trendScopeDisplayDisable" @change="renderTrendChart">
                        <template #checked> 开</template>
                        <template #unchecked> 关</template>
                      </a-switch>
                      <div class="option-title" style="margin-left: 12px">显著性水平展示</div>
                      <a-switch v-model:model-value="significanceLevelDisplay" :disabled="significanceLevelDisplayDisable" @change="renderTrendChart">
                        <template #checked> 开</template>
                        <template #unchecked> 关</template>
                      </a-switch>
                    </div>
                    <a-spin class="trend-chart">
                      <Chart :option="trendChartOption" @legendselectchanged="onTrendChartLegendSelectChanged"/>
                    </a-spin>
                  </a-tab-pane>
                  <a-tab-pane key="probability" class="chart-tab" title="概率图">
                    <a-spin class="trend-chart">
                      <Chart :option="probabilityChartOption" @legendselectchanged="onProbabilityChartLegendSelectChanged"/>
                    </a-spin>
                  </a-tab-pane>
                </a-tabs>
                <div v-if="chartType==='trend'" class="chart-tips">
                  <div class="tip-content">
                    <div class="tip-item">1. 可查看当日指标的统计值和p-value，以及当日统计值的范围。日均定义口径为已经进组的当日活跃用户的指标表现，并非当日新进组用户</div>
                    <div class="tip-item">2. 当打开「范围展示」，出现范围，会产出重叠效果，并查看各个版本的核心指标范围随时间变化的情况</div>
                    <div class="sub-tips">
                      <div class="sub-tip-item">a. 理想情况下，组内每日活跃用户会随着时间趋于稳定，从而波动变小，即范围变小并趋于稳定，并且在图中反映出来</div>
                      <div class="sub-tip-item">b. 如果范围忽大忽小，则表明日活指标数据波动大</div>
                      <div class="sub-tip-item">c. 如果范围重叠，则表示不确定哪个版本效果更好</div>
                    </div>
                  </div>
                </div>
                <div v-if="chartType==='probability'" class="chart-tips">
                  <div class="tip-content">
                    <div class="tip-item">1. 概率分布，展示的是指标的取值及其出现的概率分布，横轴是指标值，纵轴是指标值出现的概率密度，通过均值和方差反映指标的分布情况。实验组和对照组的概率分布对比，可辅助判断实验组和对照组的差异情况</div>
                    <div class="sub-tips">
                      <div class="sub-tip-item">a. 默认对照版本采用灰色系，其他版本采用彩色系</div>
                      <div class="sub-tip-item">b. 在不同实验版本的正态分布曲线上，鼠标悬浮会显示各个版本的“激活人数、p-value、指标方差、MDE、置信区间”信息</div>
                    </div>
                    <div class="tip-item">2. 适用范围：支持人均类、ctr类等支持计算置信度的指标</div>
                  </div>
                </div>
              </div>
              <div v-if="!loading && totalNum == 0" class="empty">
                <div class="empty-body">
                  <div style="box-sizing: border-box">
                    <div class="empty-img">
                      <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                    </div>
                    <div class="empty-description">
                      <div class="title">当前查询无数据</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-spin>
          </div>
          <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
            <icon-double-left/>
          </div>
        </div>
      </template>
    </a-split>
  </div>
</template>

<script setup lang="ts">
import {computed, provide, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {getNumberDisplayConfig, NumberDisplayType, TimeParticleSize} from "@/api/enum";
import {cloneDeep, debounce} from "lodash";
import {toolStore} from '@/store';
import DatePicker from "@/components/date-picker/index.vue";
import {LocalStorageEventBus} from "@/types/event-bus";
import userFilter from "@/views/analyse/components/UserFilter.vue"
import {compressDateRangeToEndDate} from "@/utils/dateUtil";
import {updateDisplayNames} from '@/views/analyse/components/util/mapNameChange';
import AnalysisIndex from "@/views/analyse/event/components/AnalysisIndex.vue"
import globalFilter from "@/views/analyse/components/globalFilter.vue"
import dateSet from "@/views/analyse/components/dateSet.vue"
import {postExperimentAnalyticsQuery} from "@/api/ab/api";
import {get16UUID} from "@/utils/strUtil";
import {useRoute} from "vue-router";
import {formatIntegerValue, formatPercentageValue} from "@/utils/number-util";
import {generateNormalDistribution, getZValue} from "@/utils/chartUtil";
import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import EventQueryFilter from "@/views/analyse/components/operation/eventQueryFilter.vue";

const props = defineProps({
  experimentDetail: {
    type: Object,
    default: () => ({}),
  },
});
// 2. 响应式数据/变量声明区
// 查询参数对象，包含指标、筛选、分组、时间等
const queryParam = reactive<any>({
  indicators: [],
  filter: {},
  aggregates: [],
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  },
  timeParticleSize: TimeParticleSize.DAY1,
  comparedDateList: [],
  firstDayDfWeek: null,
  userFilter: {},
  tableArrangeType: 'event'
});
const route = useRoute(); // 路由对象
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const toolData = toolStore(); // 工具数据 store
const experimentCode = (route.query.code as string) || '';
const paramsDataStorage = useSessionStorage(`${experimentCode}-event-params-data`, {}); // 会话存储参数
const eventData = ref<{ y: any[]; groupsDesc: any[] }>({y: [], groupsDesc: []}); // 事件数据
const splitSize = useLocalStorage("analyse-view-split-size", '488px'); // 左右分栏宽度
const loading = ref(true); // 右侧 loading 状态
const totalNum = ref(0); // 查询结果总数
const isReset = ref(false); // 是否重置标志
const dataLoading = ref(false); // 数据加载状态
const analysisIndexData = ref<any[]>([]); // 分析指标数据
const analysisIndexDataCopy = ref<any[]>([]);
const globalFilterData = ref<any>({}); // 全局筛选数据
const groupData = ref<any[]>([]); // 分组项数据
const userFilterData = ref<any>({}); // 用户筛选数据
const fullEventList = ref<any[]>([]); // 全部事件列表
const eventBus = useEventBus('eventList'); // 事件总线
const configData = ref<any[]>([]); // 图表配置数据
const reportSaveForm = ref<any>({name: '', dashboard: '', description: ''}); // 报表保存表单
const timeOut = ref<any>(); // 查询超时定时器
const boardDate = ref<any>({recentStartDate: 7, recentEndDate: 1, dateText: '过去7天'}); // 日期区间对象
const timeParticle = ref<any>({timeParticleSize: '', firstDayDfWeek: null}); // 时间粒度对象
const tableData = ref([]);  // 表格数据
const chartType = ref<string>('trend');  // 趋势图展示数据类型
const trendChartType = ref<string>('indicator');  // 趋势图展示数据类型
const trendChartOption = ref({});  // 趋势图配置
const trendScopeDisplay = ref(false);  // 趋势图范围展示
const trendScopeDisplayDisable = ref(false);  // 趋势图范围展示是否可用
const significanceLevelDisplay = ref(false);  // 显著性水平展示
const significanceLevelDisplayDisable = ref(true);  // 显著性水平展示是否可见
const probabilityChartOption = ref({});  // 概率图配置
const selectedIndicatorName = ref('');
provide('boardDate', boardDate);

// 3. 工具函数/通用函数区
// 删除对象中的 enumList 属性（递归）
const removeEnumList = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map((item: any) => removeEnumList(item));
  }
  if (typeof obj === 'object' && obj !== null) {
    const newObj = {...obj};
    delete newObj.enumList;
    Object.keys(newObj).forEach((key: string) => {
      newObj[key] = removeEnumList(newObj[key]);
    });
    return newObj;
  }
  return obj;
};
// 创建事件项的工具函数
const createIndicatorItem = (data: any): any => ({
  type: 'indicator',
  indicatorCode: data?.code,
  indicatorName: data.displayName,
  isBasic: data.isBasic,
  filter: {}
});

// 4. 业务方法区
// 参数校验函数，校验筛选条件是否合法
const paramsVerify = (params: any): boolean => {
  // 校验单个筛选项
  const verifyFilter = (item: any): boolean => {
    if (
        item.calcuSymbol !== 'ex' &&
        item.calcuSymbol !== 'nex' &&
        !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
        !item.thresholds?.length
    ) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every((subFilter: any) =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 校验筛选项列表
  const verifyFilterList = (filterList: any[] = []): boolean => {
    return filterList.length === 0 || filterList.every((items: any) => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取需要验证的列表
  const firstList = params.indicators
      .filter((item: any) => item.filter?.filters?.length > 0)
      .map((item: any) => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.indicators
      .flatMap((item: any) =>
          item.eventList
              .filter((event: any) => event.filter?.filters?.length > 0)
              .map((event: any) => event.filter.filters)
      );
  // 添加对 userFilter 的校验
  const userFilterList = params?.userFilter?.eventCondition?.singleConditionExpressions
      ?.flatMap((item: any) => item.filter?.filters || []) || [];
  const userSequenceList = params?.userFilter?.eventCondition?.sequenceConditionExpressions
      ?.flatMap((item: any) => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap((event: any) => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userFilter?.userCondition?.filters || [];
  // 验证所有列表
  return (
      verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList) &&
      verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList)
  );
};
// 按分钟粒度时校验时间范围
const minutesVerification = (): void => {
  const timeType = queryParam.timeParticleSize;
  if (timeType === 'm1' || timeType === 'm5' || timeType === 'm10') {
    Message.warning('按分钟查看，时间范围一次最多展示1天');
    const compressDateRange = compressDateRangeToEndDate(boardDate.value);
    boardDate.value = compressDateRange;
    queryParam.dateRange = compressDateRange;
  }
};

/**
 * 统计值趋势图
 */
function renderIndicatorTrendChart() {
  // 图例，实验组名称
  const legend = eventData.value?.groups?.map((item) => item?.[0]) || [];
  // x轴
  const x = eventData.value?.x || [];
  // 核心指标数据
  const selectedIndicatorIndex = indicatorOptions.value.findIndex(item => item.name === selectedIndicatorName.value) || 0
  const coreIndicatorData = eventData.value?.y?.[selectedIndicatorIndex]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];

  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      // 实验组
      seriesData.push({
        name: item.group[0],
        type: 'line',
        data: item.values.map((v, i) => ({
          value: v,
          diffRate: item.diffRates ? item.diffRates[i] : undefined,
        })),
        itemStyle: {
          color: '#333',
        },
        showSymbol: false,
      });
      return;
    }
    // 对照组
    seriesData.push({
      name: item.group[0],
      type: 'line',
      data: item.values.map((v, i) => ({
        value: v,
        diffRate: item.diffRates ? item.diffRates[i] : undefined,
      })),
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    });
    // 范围展示
    if (trendScopeDisplay.value) {
      seriesData.push({
        name: item.group[0] + '-L',
        type: 'line',
        data: item.confidenceInterval.map((confidenceInterval) => confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
      seriesData.push({
        name: item.group[0] + '-U',
        type: 'line',
        data: item.confidenceInterval.map((confidenceInterval) => confidenceInterval[1] - confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        areaStyle: {
          color: colorList[index % colorList.length].area,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => {
                  const diffRate = item.data.diffRate;
                  const diffRateStr = !diffRate ? '' : diffRate > 0 ? formatPercentageValue(diffRate) + '↑' : formatPercentageValue(diffRate) + '↓';
                  let color = !diffRate ? '#222' : diffRate > 0 ? '#07a35a' : '#e33232';
                  return item.marker + ' ' + item.seriesName + ' <b>' + item.value + '</b> ' + (diffRate !== undefined ? `<b style="color:${color}">${diffRateStr}</b>` : '');
                })
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
    },
    series: seriesData,
  };
}

/**
 * 差异绝对值趋势图
 */
function renderDiffRateTrendChart() {
  // 图例，实验组名称
  const legend = eventData.value?.groups?.filter((item, index) => index !== 0).map((item) => item?.[0]) || [];
  // x轴
  const x = eventData.value?.x || [];
  // 核心指标数据
  const selectedIndicatorIndex = indicatorOptions.value.findIndex(item => item.name === selectedIndicatorName.value) || 0
  const coreIndicatorData = eventData.value?.y?.[selectedIndicatorIndex]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];

  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      return;
    }
    // 对照组
    seriesData.push({
      name: item.group[0],
      type: 'line',
      data: item.diffRates.map((v, i) => ({
        value: v,
        confidenceInterval: item.diffRateConfidenceInterval?.length > 0 ? item.diffRateConfidenceInterval[i] : undefined,
      })),
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    });
    // 范围展示
    if (trendScopeDisplay.value) {
      seriesData.push({
        name: item.group[0] + '-L',
        type: 'line',
        data: item.diffRateConfidenceInterval.map((confidenceInterval) => confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
      seriesData.push({
        name: item.group[0] + '-U',
        type: 'line',
        data: item.diffRateConfidenceInterval.map((confidenceInterval) => confidenceInterval[1] - confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        areaStyle: {
          color: colorList[index % colorList.length].area,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
          formatter: function (params) {
            return formatPercentageValue(params.value);
          },
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => {
                  const confidenceInterval = item.data.confidenceInterval;
                  const confidenceIntervalStr = confidenceInterval?.length === 2 ? `[${formatPercentageValue(confidenceInterval[0])},${formatPercentageValue(confidenceInterval[1])}]` : '';
                  return item.marker + ' ' + item.seriesName + ' <b>' + formatPercentageValue(item.value) + '</b> ' + confidenceIntervalStr;
                })
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
      axisLabel: {
        formatter: function (value) {
          return formatPercentageValue(value);
        },
      },
    },
    series: seriesData,
  };
}

/**
 * pValue趋势图
 */
function renderPValueTrendChart() {
  // 图例，实验组名称
  const legend = eventData.value?.groups?.filter((item, index) => index !== 0).map((item) => item?.[0]) || [];
  // x轴
  const x = eventData.value?.x || [];
  // 核心指标数据
  const selectedIndicatorIndex = indicatorOptions.value.findIndex(item => item.name === selectedIndicatorName.value) || 0
  const coreIndicatorData = eventData.value?.y?.[selectedIndicatorIndex]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];
  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      return;
    }
    // 对照组
    const seriesItem = {
      name: item.group[0],
      type: 'line',
      data: item.pValues,
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    };
    // 显著性水平
    if (significanceLevelDisplay.value && props.experimentDetail?.confidenceLevel) {
      seriesItem.markLine = {
        symbol: 'none',
        data: [
          {
            yAxis: 1 - props.experimentDetail?.confidenceLevel,
            lineStyle: {
              color: 'rgba(7,163,90,0.8)',
              type: 'dashed',
              width: 1,
            },
            label: {
              show: true,
              position: 'middle',
              formatter: function () {
                return `显著性水平 ${(1 - props.experimentDetail?.confidenceLevel).toFixed(2)}`;
              },
              color: '#07a35a',
              fontSize: 12,
              fontWeight: 'bold',
              padding: [0, 8, 0, 0],
            },
          },
        ],
      };
    }
    seriesData.push(seriesItem);
    if (significanceLevelDisplay.value && props.experimentDetail?.confidenceLevel) {
      seriesData.push({
        name: '显著性水平参考线',
        type: 'line',
        data: x.map(() => 1 - props.experimentDetail?.confidenceLevel),
        lineStyle: {
          width: 0,
        },
        showSymbol: false,
        tooltip: {show: false},
        z: 0,
        areaStyle: {
          color: 'rgba(7,163,90,0.12)',
          origin: 'start',
        },
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => item.marker + ' ' + item.seriesName + ' <b>' + item.value + '</b> ')
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
    },
    series: seriesData,
  };
}

const indicatorOptions = computed(() => {
  return eventData.value?.y?.map((item, index) => ({
    name: item.displayName,
    index
  })) || [];
});
// 指标切换处理
const handleIndicatorChange = (v) => {
  // 重新渲染图表
  renderTrendChart();
};
/**
 * 趋势图渲染
 */
const renderTrendChart = () => {
  switch (trendChartType.value) {
    case 'indicator':
      renderIndicatorTrendChart();
      break;
    case 'diffRate':
      renderDiffRateTrendChart();
      break;
    case 'pValue':
      renderPValueTrendChart();
      break;
    default:
  }
};
/**
 * 概率图渲染
 */
const renderProbabilityChart = () => {
  // 图例，实验组名称
  const legend = eventData.value?.groups?.map((item: any) => item?.[0]) || [];
  // 核心指标数据
  const coreIndicatorData = eventData.value?.y?.[0]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData: any[] = [];
  let globalMinX = Infinity;
  let globalMaxX = -Infinity;
  coreIndicatorData.forEach((item: any, index: number) => {
    const fullData = generateNormalDistribution(item.summaryValue.mean, item.summaryValue.standardDeviation);
    const zValue = getZValue(props.experimentDetail.confidenceLevel);
    const minX = item.summaryValue.mean - zValue * item.summaryValue.standardDeviation;
    const maxX = item.summaryValue.mean + zValue * item.summaryValue.standardDeviation;
    const middle = fullData.filter(([x]) => x >= minX && x <= maxX);
    globalMinX = globalMinX > minX ? minX : globalMinX;
    globalMaxX = globalMaxX < maxX ? maxX : globalMaxX;
    // 主曲线
    seriesData.push({
      name: item.group[0],
      type: 'line',
      smooth: true,
      showSymbol: false,
      data: fullData.map(([x, y]) => ({value: [x, y]})), // 适配tooltip
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      markPoint: {
        symbolSize: [120, 60],
        label: {
          formatter: (params: Object | Array) => {
            return params.data.coord[0].toFixed(3)
          }
        },
        data: [
          {type: 'max', name: 'Max'}
        ]
      },
      markLine: {
        data: [[
          {xAxis: item.summaryValue.mean, yAxis: '0', symbol: 'none'},
          {
            xAxis: item.summaryValue.mean, yAxis: 'max', symbol: 'none',
            lineStyle: {
              color: colorList[index % colorList.length].line,
              type: 'dashed',
              width: 3
            }
          }
        ]]
      }
    });
    // 中间阴影
    seriesData.push({
      name: item.group[0] + '-L',
      type: 'line',
      smooth: true,
      showSymbol: false,
      data: middle.map(([x, y]) => ({value: [x, y]})),
      lineStyle: {width: 0},
      areaStyle: {
        color: colorList[index % colorList.length].area
      },
    });
  });
  const range = globalMaxX - globalMinX;
  const xMin = Math.round((globalMinX - 0.8 * range) * 1000) / 1000;
  const xMax = Math.round((globalMaxX + 0.8 * range) * 1000) / 1000;
  probabilityChartOption.value = {
    legend: {
      bottom: 0,
      data: legend,
      selected: legend.reduce((acc: any, name: string) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
      show: false,
    },
    xAxis: {
      type: 'value',
      boundaryGap: false,
      splitLine: {show: false},
      min: xMin,
      max: xMax,
      axisLabel: {
        margin: 12,
      },
    },
    yAxis: {
      name: '概率密度',
      splitNumber: 3,
      axisLine: {
        show: false,
        onZero: false
      },
      splitLine: {show: true, lineStyle: {type: 'dashed', color: '#d9d9d9'}},
    },
    series: seriesData,
  };
};
/**
 * 趋势图类型修改
 */
const trendChartTypeChange = (value) => {
  renderTrendChart();
  if (value === 'pValue') {
    trendScopeDisplayDisable.value = true;
    trendScopeDisplay.value = false;
    significanceLevelDisplayDisable.value = false;
  } else {
    trendScopeDisplayDisable.value = false;
    significanceLevelDisplayDisable.value = true;
    significanceLevelDisplay.value = false;
  }
};
// 初始化函数，加载初始数据
const init = async (): Promise<void> => {
  if (dataLoading.value) {
    // 正在初始化中
    return;
  }
  dataLoading.value = true;
  toolData.updateTemporaryList([]);
  fullEventList.value = (await toolData.fetchAllModalList()).flatMap((category: any) => category.items || []);
  // 切换应用时重新请求全部属性
  if (toolData.totalEvtIndCodes.length) {
    await toolData.fetchAllAttrList();
  }
  let initialQueryParam: any = paramsDataStorage.value;
  // 初始化分析指标
  if (initialQueryParam?.indicators && initialQueryParam.indicators?.length > 0) {
    analysisIndexData.value = initialQueryParam.indicators.map((item: any) => {
      return {
        ...item,
        unitName: item.unitName,
        eventList: item.eventList,
      };
    });
  }
  // 更新可能被修改的displayName
  updateDisplayNames(analysisIndexData.value);
  // 若初始化后指标值为空，则赋予默认值
  if (!analysisIndexData.value.length) {
    [props.experimentDetail.coreIndicator, ...props.experimentDetail.concernIndicator]?.forEach((indicator: any) => {
      const indicatorData = fullEventList.value.find((item: any) => item.code === indicator.code);
      const eventItem = createIndicatorItem(indicatorData);
      analysisIndexData.value.push({
        name: indicatorData.name,
        type: 'event',
        isBasic: true,
        displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
        displayName: indicatorData.displayName,
        unitName: '',
        eventList: [eventItem],
        filter: {}
      });
    })
  }
  // 初始化查询参数
  queryParam.indicators = analysisIndexData.value.map((item: any) => {
    return {
      displayName: item.displayName,
      displayType: item.displayType,
      name: item.name,
      type: item.type,
      isBasic: item.isBasic,
      eventList: item.eventList,
      filter: item.filter,
      formula: item?.formula
    };
  });
  // 处理报表保存弹窗
  reportSaveForm.value.name = analysisIndexData.value.length > 1
      ? `${analysisIndexData.value[0]?.displayName}等${analysisIndexData.value.length}个`
      : analysisIndexData.value[0]?.displayName;
  // 处理事件筛选
  if (initialQueryParam.filter) {
    globalFilterData.value = initialQueryParam.filter;
    queryParam.filter = initialQueryParam.filter;
  }
  // 处理分组项
  if (initialQueryParam.aggregates && initialQueryParam.aggregates.length > 0) {
    groupData.value = cloneDeep(initialQueryParam.aggregates);
    queryParam.aggregates = cloneDeep(initialQueryParam.aggregates);
  }
  // 处理用户筛选条件
  if (initialQueryParam.userFilter) {
    userFilterData.value = initialQueryParam.userFilter;
    queryParam.userFilter = initialQueryParam.userFilter;
  }
  // 处理时间
  if (initialQueryParam.dateRange) {
    boardDate.value = initialQueryParam.dateRange;
    queryParam.dateRange = initialQueryParam.dateRange;
  } else {
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    };
    boardDate.value = date;
    queryParam.dateRange = date;
  }
  // 处理dateSet
  timeParticle.value = {
    timeParticleSize: initialQueryParam.timeParticleSize ? initialQueryParam.timeParticleSize : 'D1',
    firstDayDfWeek: initialQueryParam.firstDayDfWeek ? initialQueryParam.firstDayDfWeek : null
  };
  queryParam.timeParticleSize = timeParticle.value.timeParticleSize;
  queryParam.firstDayDfWeek = timeParticle.value.firstDayDfWeek;
  dataLoading.value = false;
  selectedIndicatorName.value = indicatorOptions.value[0]?.name || '';
  setTimeout(() => {
    computedData();
  }, 1000);
};

// 计算查询数据
const computedData = (): void => {
  loading.value = true;
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false;
      Message.warning('事件查询超时，请重试！');
    }
  }, 60 * 1000);
  // 校验指标 displayName
  const hasInvalidDisplayName = queryParam.indicators.some((indicator: any) => !indicator.displayName);
  if (hasInvalidDisplayName) {
    Message.error('请输入指标名');
    loading.value = false;
    return;
  }
  if (paramsVerify(queryParam)) {
    postExperimentAnalyticsQuery({
      requestId: `request_${get16UUID()}`,
      experimentId: (route.query.code as string) || '',
      model: 'event',
      queryParam,
    }).then((res: any) => {
      if (res === null) {
        if (timeOut.value) clearTimeout(timeOut.value);
        loading.value = false;
        return;
      }
      if (timeOut.value) clearTimeout(timeOut.value);
      eventData.value = res;
      if (props.experimentDetail?.variants?.length > 0) {
        let data = [];
        props.experimentDetail.variants?.forEach((variant, variantIndex) => {
          const record = {
            name: variant.name,
            code: variant.code,
            type: variant.type,
            flowPercentage: variant.flowPercentage,
            userNumber: variant.userNumber,
            userNumberPercentage: variant?.userNumberPercentage ? Number((variant?.userNumberPercentage * 100).toFixed(1)) : undefined,
          }
          const indicatorData = []
          const tempYDataList = []
          res.y.forEach((item) => tempYDataList.push(item));
          analysisIndexDataCopy.value = cloneDeep(queryParam.indicators).map((item: any) => {
            return {
              ...item,
              unitName: item.unitName,
              eventList: item.eventList,
            };
          });
          analysisIndexDataCopy.value.forEach((analysisIndexItem: any) => {
            const displayName = analysisIndexItem.displayName;
            // 处理重命指标前缀逻辑
            const yDataList = tempYDataList?.filter(y => new RegExp(`(^\\d+_${displayName}$|^${displayName}$)`).test(y.displayName))
                .sort((a, b) => {
                  if (a.displayName === displayName) return -1; // 关键字本身最前
                  if (b.displayName === displayName) return 1;
                  // 提取前缀数字
                  const numA = parseInt(a.displayName.match(/^(\d+)_/)[1], 10);
                  const numB = parseInt(b.displayName.match(/^(\d+)_/)[1], 10);
                  return numA - numB;
                });
            const yData = yDataList[0]
            if (tempYDataList.indexOf(yData) > -1) {
              tempYDataList.splice(tempYDataList.indexOf(yData), 1)
            }
            const vIndex = yData?.yData.map((item: any) => item.group?.[0]).indexOf(variant.name)
            indicatorData.push({
              value: yData?.yData?.[vIndex]?.summaryValue?.mean,
              diffValue: yData?.yData?.[vIndex]?.summaryValue?.diffValue,
              diffRate: yData?.yData?.[vIndex]?.summaryValue?.diffRate,
              confidenceInterval: yData?.yData?.[vIndex]?.summaryValue?.confidenceInterval,
              mde: yData?.yData?.[vIndex]?.summaryValue?.mde,
              pValue: yData?.yData?.[vIndex]?.summaryValue?.pValue,
            })

          })
          record.indicatorData = indicatorData
          data.push(record);
        });
        tableData.value = data?.sort((a, b) => a.type - b.type);
      }
      paramsDataStorage.value = queryParam;
      totalNum.value = res.totalNum;
      const data: any[] = [];
      eventData.value.y.forEach((item: any) => {
        data.push({name: item.displayName});
      });
      configData.value = data.map((item: any) => {
        return {
          name: item.name,
          type: 'line',
          showSub: false
        };
      });
      const foundIndex = indicatorOptions.value.findIndex(item => item.name === selectedIndicatorName.value);
      selectedIndicatorName.value = foundIndex >= 0 ? selectedIndicatorName.value : indicatorOptions.value[0]?.name;
      renderTrendChart();
      renderProbabilityChart();
      loading.value = false;
    });
  } else {
    Message.error('筛选条件参数错误');
    loading.value = false;
  }
};

// 重置所有筛选和参数
const reset = (): void => {
  isReset.value = true;
  analysisIndexData.value = [];
  globalFilterData.value = {};
  groupData.value = [];
  userFilterData.value = {};
  queryParam.filter = {};
  queryParam.userFilter = {};
  paramsDataStorage.value = {};
  init();
};

// 5. 事件/回调函数区前置：获取属性并更新事件总线和报表名（防抖）
const getattrBus = debounce(async () => {
  const eventLists = queryParam.indicators.flatMap((item: any) => item.eventList);
  eventBus.emit(eventLists);
  reportSaveForm.value.name = queryParam.indicators.length > 1
      ? `${queryParam.indicators[0]?.displayName}等${queryParam.indicators.length}个`
      : `${queryParam.indicators[0]?.displayName}`;
}, 300);
// 5. 事件/回调函数区
// 分析指标变更回调
const indicatorsChange = (v: any): void => {
  queryParam.indicators = cloneDeep(v);
  getattrBus();
  paramsDataStorage.value = cloneDeep(queryParam);
  analysisIndexDataCopy
};
// 全局筛选变更回调
const filtersChange = (v: any): void => {
  queryParam.filter = v;
  paramsDataStorage.value = queryParam;
};
// 用户筛选变更回调
const userFiltersChange = (v: any): void => {
  queryParam.userFilter = v;
  paramsDataStorage.value = queryParam;
};
// 时间粒度变更回调
const timeChange = (v: any): void => {
  queryParam.timeParticleSize = v.timeParticleSize;
  queryParam.firstDayDfWeek = v.firstDayDfWeek;
  timeParticle.value = v;
  minutesVerification();
  computedData();
};
// 日期选择回调
const datePick = (date: any): void => {
  boardDate.value = date;
  queryParam.dateRange = date;
  minutesVerification();
  computedData();
};
// 置性区间图例联动
const onTrendChartLegendSelectChanged = (params) => {
  // params.name 是主曲线名
  // params.selected 是所有图例的显示状态
  const selected = params.selected;
  // 遍历所有主曲线
  Object.keys(selected).forEach((key) => {
    // key 是主曲线名
    // 控制对应的 L/U 曲线
    trendChartOption.value.legend.selected[key] = selected[key];
    trendChartOption.value.legend.selected[key + '-L'] = selected[key];
    trendChartOption.value.legend.selected[key + '-U'] = selected[key];
  });
};
// 置性区间图例联动
const onProbabilityChartLegendSelectChanged = (params) => {
  // params.name 是主曲线名
  // params.selected 是所有图例的显示状态
  const selected = params.selected;
  // 遍历所有主曲线
  Object.keys(selected).forEach((key) => {
    // key 是主曲线名
    // 控制对应的 L/U 曲线
    probabilityChartOption.value.legend.selected[key] = selected[key];
    probabilityChartOption.value.legend.selected[key + '-L'] = selected[key];
  });
};
// 颜色数组，10组，超过则循环
const colorList = [
  {line: '#333333', area: 'rgba(51,51,51,0.15)'},
  {line: '#5470C6', area: 'rgba(84,112,198,0.15)'},
  {line: '#91CC75', area: 'rgba(145,204,117,0.15)'},
  {line: '#EE6666', area: 'rgba(238,102,102,0.15)'},
  {line: '#FAC858', area: 'rgba(250,200,88,0.15)'},
  {line: '#73C0DE', area: 'rgba(115,192,222,0.15)'},
  {line: '#3BA272', area: 'rgba(59,162,114,0.15)'},
  {line: '#FC8452', area: 'rgba(252,132,82,0.15)'},
  {line: '#9A60B4', area: 'rgba(154,96,180,0.15)'},
  {line: '#EA7CCC', area: 'rgba(234,124,204,0.15)'},
  {line: '#2A99C9', area: 'rgba(42,153,201,0.15)'},
];


// 6. 生命周期/初始化逻辑区
init();
// 监听本地存储事件，切换应用或数据源时重新初始化
localStorageEventBus.on((name: any, value: any) => {
  if (name === "app-id" || name === "data-source") {
    init();
  }
});
watch(() => props.experimentDetail, (res) => {
  init()
}, {deep: true});

/**
 * 复制变体ID
 * @param code 变体ID
 */
const copyVariantId = async (code) => {
  await navigator.clipboard.writeText(code);
  Message.success('复制成功');
}
</script>

<style scoped lang="less">
@import '../style/analyse.less';

.positive-color {
  color: #07a35a;
}

.positive-background-color {
  background-color: #f1fdef;
}

.negative-color {
  color: #e33232;
}

.negative-background-color {
  background-color: #fdefed;
}

.conclusion {
  box-sizing: border-box;
  padding: 24px;
  background: url(/experiment/summary-bg.jpg) center center / cover no-repeat rgb(255, 255, 255);
  box-shadow: none;
  border-radius: 6px;
  margin-bottom: 20px;
  position: relative;
  color: #10389f;
  flex: 1 1;
  z-index: 1;

  .conclusion-content {
    margin-bottom: 12px;

    .title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      margin-bottom: 6px;
      display: flex;
      align-items: center;

      .data-compare {
        display: flex;
        position: relative;
        align-items: center;

        .icon {
          width: 12px;
          margin-bottom: 1px;
        }
      }
    }

    .sub-title {
      font-size: 12px;
    }
  }

  .footer-wrapper {
    align-items: center;
    color: #606773;
    display: flex;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;

    .note-item {
      margin-right: 24px;
    }
  }
}

// ---- 表格样式
.indicator-value-line {
  margin-top: 8px;
  display: flex;
  align-items: center;

  .label {
    font-size: 10px;
  }

  .value {
    margin-left: 10px;
    font-size: 14px;
  }

  .divide {
    padding: 0 8px;
  }
}

:deep(.table-title) {
  display: flex;
  align-items: center;

  .mini-tag {
    height: 14px;
    font-size: 8px;
    line-height: 12px;
    margin-right: 8px;
    padding: 0 4px;
    display: flex;
    justify-content: center;
  }
}

.mini-tag {
  height: 14px;
  font-size: 8px;
  line-height: 12px;
  margin-right: 8px;
  padding: 0 4px;
  display: flex;
  justify-content: center;
}

.diff-value {
  color: #14141473;
  font-size: 12px;
  font-weight: 400;
  margin-top: -4px;
}

.diff-rate {
  margin: -6px -12px;
  padding: 6px 12px;

  .mde {
    display: inline-block;
    border-bottom: 1px dashed rgba(20, 20, 20, 0.45);
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    margin-right: 8px;
    color: #14141473;
    font-size: 10px;
  }

  .significant-tag {
    height: 14px;
    font-size: 8px;
    line-height: 12px;
    padding: 0 4px;
    justify-content: center;
  }
}

.confidence-interval {
  width: 100%;
  padding: 12px 4px;
  justify-self: self-end;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #666686;
  font-size: 12px;
  line-height: 12px;

  .axis {
    align-items: center;
    background-color: #d9d9e9;
    display: flex;
    height: 4px;
    margin: 4px 0;
    position: relative;
    width: 100%;

    .interval-line {
      height: 100%;
      margin: auto;
      position: relative;
      width: 66.66%;

      .interval-left {
        background-color: inherit;
        height: 8px;
        position: absolute;
        top: 0;
        width: 1px;
        left: 0;
      }

      .interval-dot {
        background-color: #2f2f3f;
        bottom: 0;
        height: 100%;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
      }

      .interval-right {
        background-color: inherit;
        height: 8px;
        position: absolute;
        top: 0;
        width: 1px;
        right: 0;
      }
    }
  }

  .footer-value {
    display: flex;
    justify-content: space-between;
    width: 75%;
  }
}

.variant-name {
  display: flex;
  align-items: center;
}

.variant-id {
  display: flex;
  font-size: 10px;
  color: #606773;
  align-items: center;

  .code {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// ---- 表格样式

// ---- 图表样式
.chart-tabs {
  margin: 20px 0 12px 0;
  width: 100%;
  height: 410px;

  .chart-tab {
    width: 100%;
    padding: 0 16px;

    .trend-chart-option {
      display: flex;
      align-items: center;

      .option-title {
        margin-right: 12px;
      }
    }

    .trend-chart {
      width: 100%;
      height: 325px;
    }
  }

  :deep(.arco-tabs-content) {
    height: calc(100% - 36px);
    overflow: auto;
  }
}

.chart-tips {
  background: linear-gradient(0deg, #dff7ff33, #dff7ff33), linear-gradient(90deg, #e6f8f2, #edf9f8 50%, #ddf0e9);
  border-radius: 8px;
  display: flex;
  padding: 24px;
  color: #037849;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 16px;
  width: 100%;

  .tip-content {
    .tip-item {
    }

    .sub-tips {
      margin-left: 12px;

      .sub-tip-item {
      }
    }
  }
}

.tip-title {
  font-weight: bolder;
}

.tip-description {
  font-size: 12px;
  color: var(--tant-text-gray-color-text1-4);
}

// ---- 图表样式
</style>