// 工具函数：递归地将对象的键从驼峰命名转换为下划线命名
import _ from "lodash";
import {v4 as uuidv4} from 'uuid';

function toSnakeCase(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1_$2')        // fooBar -> foo_Bar
    .replace(/([A-Z])([A-Z][a-z])/g, '$1_$2')    // ABCDef -> ABC_Def
    .toLowerCase();
}

export function convertKeysToSnakeCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToSnakeCase(item));
  } else if (!_.isEmpty(obj) && obj.constructor === Object) {
    const newObj: any = {};
    Object.keys(obj).forEach(key => {
      const newKey = toSnakeCase(key);
      newObj[newKey] = convertKeysToSnakeCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

// 工具函数：递归地将对象的键从下划线命名转换为驼峰命名
// export function convertKeysToCamelCase(obj: any): any {
//   if (Array.isArray(obj)) {
//     return obj.map(item => convertKeysToCamelCase(item));
//   } else if (!_.isEmpty(obj) && obj.constructor === Object) {
//     const newObj: any = {};
//     Object.keys(obj).forEach(key => {
//       const newKey = _.camelCase(key);
//       newObj[newKey] = convertKeysToCamelCase(obj[key]);
//     });
//     return newObj;
//   }
//   return obj;
// }
export function convertKeysToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(item => convertKeysToCamelCase(item));
  } else if (!_.isEmpty(obj) && obj.constructor === Object) {
    const newObj: any = {};
    Object.keys(obj).forEach(key => {
      // 只处理下划线分隔的情况
      const newKey = key.replace(/_([a-z0-9])/gi, (_, letter) => letter.toUpperCase());
      newObj[newKey] = convertKeysToCamelCase(obj[key]);
    });
    return newObj;
  }
  return obj;
}

/**
 * 获取32位UUID
 */
export function get32UUID() {
  return uuidv4().replaceAll('-', ''); // 返回32位的UUID字符串
}

/**
 * 获取32位UUID
 */
export function get16UUID() {
  return uuidv4().replaceAll('-', '').slice(0, 16); // 返回32位的UUID字符串
}

/**
 * 计算字符串的自定义总长度
 * - 英文字母、数字、符号长度为1
 * - 中文字符长度为2
 * - 总长度再乘以字号（fontSize），返回最终长度
 *
 * @param str 输入字符串
 * @param fontSize 字号，默认14
 * @returns 总长度（自定义长度 * 字号）
 */
export function getCustomStringLength(str: string, fontSize: number = 14): number {
  let lowerCaseCount = 0;
  let upperCaseCount = 0;
  let chineseCount = 0;
  let digitCount = 0;
  let symbolCount = 0;

  for (const char of str) {
    if (/^[a-z]$/.test(char)) {
      lowerCaseCount++;
    } else if (/^[A-Z]$/.test(char)) {
      upperCaseCount++;
    } else if (/^[\u4e00-\u9fa5]$/.test(char)) {
      chineseCount++;
    } else if (/^\d$/.test(char)) {
      digitCount++;
    } else {
      symbolCount++;
    }
  }

  // 计算自定义总长度，大写字母使用更大的权重
  const totalLength = 
    lowerCaseCount * 0.56 + 
    upperCaseCount * 0.68 +  // 大写字母通常更宽
    chineseCount * 1 + 
    digitCount * 0.5686 + 
    symbolCount * 0.48;
    
  // 返回总长度乘以字号
  return totalLength * fontSize;
}

/**
 * 创建分析列表分组项自定义排序函数，根据数据格式类型进行排序
 * @param format 数据格式类型 ('string'|'number'|'range')
 * @returns 排序函数
 */
export function createCustomSorter(format: 'string' | 'number' | 'range') {
  return (a: any, b: any, direction: 'ascend' | 'descend') => {
    // 处理空值
    if (a === null && b === null) return 0;
    if (a === null) return direction === 'ascend' ? -1 : 1;
    if (b === null) return direction === 'ascend' ? 1 : -1;
    
    // 根据不同格式类型处理排序
    if (format === 'number') {
      // 数字类型排序
      const numA = typeof a === 'number' ? a : parseFloat(a);
      const numB = typeof b === 'number' ? b : parseFloat(b);
      
      if (isNaN(numA) && isNaN(numB)) return 0;
      if (isNaN(numA)) return direction === 'ascend' ? -1 : 1;
      if (isNaN(numB)) return direction === 'ascend' ? 1 : -1;
      
      return direction === 'ascend' ? numA - numB : numB - numA;
    } 
    else if (format === 'range') {
      // 范围类型排序
      const extractFirstNumber = (str: string) => {
        const match = str.match(/^\[?(\d+(?:\.\d+)?)/);
        return match ? parseFloat(match[1]) : NaN;
      };
      
      const numA = extractFirstNumber(String(a));
      const numB = extractFirstNumber(String(b));
      
      if (isNaN(numA) && isNaN(numB)) return 0;
      if (isNaN(numA)) return direction === 'ascend' ? -1 : 1;
      if (isNaN(numB)) return direction === 'ascend' ? 1 : -1;
      
      return direction === 'ascend' ? numA - numB : numB - numA;
    } 
    else {
      // 字符串类型排序
      const strA = String(a).toLowerCase();
      const strB = String(b).toLowerCase();
      
      return direction === 'ascend' 
        ? strA.localeCompare(strB) 
        : strB.localeCompare(strA);
    }
  };
}