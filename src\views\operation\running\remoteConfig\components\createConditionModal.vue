<template>
    <a-modal v-model:visible="modalVisible" :width="844" title-align="start" :title="modalTitle" :footer="false">
        <p class="desc">使用条件，以便在满足条件时提供不同的参数值。对该条件进行的任何更改都会随时应用于使用该条件的<b>所有参数</b></p>
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <div class="types">
                <a-form-item field="name" label="名称" :hide-asterisk="true" style="width: 300px;">
                    <a-input v-model="form.name"/>
                </a-form-item>
                <a-form-item field="color" label="颜色" style="width: 180px;margin-left: 12px;" class="non-shrink-item">
                    <a-select v-model:model-value="form.color" :style="{background: form.color,borderRadius:'4px'}" @change="colorChange">
                        <div style="padding: 0 4px">
                            <a-option v-for="(item,index) in colorList" :key="index" :value="item.value" :style="{background: item.value,marginBottom:'4px',borderRadius:'4px'}">
                            {{ item.label }}
                        </a-option>
                        </div>

                    </a-select>
                </a-form-item>
            </div>
            <div class="form-label">适用条件...</div>
            <div class="form-content">
                <div v-for="(pre,num) in predicateList" :key="num" class="predicate">
                    <a-dropdown @select="conditionSelect($event,num)">
                        <div class="trigger-content" style="width: 250px;">
                            <span v-if="pre.name" class="name-text">{{pre.name}}</span>
                            <span v-else>选择...</span>
                            <icon-caret-down />
                        </div>
                        <template #content>
                            <template v-for="item in selectList" :key="item.code">
                                <!-- 有子选项的情况 -->
                                <a-dsubmenu v-if="item.children?.length" :value="item.code"  trigger="hover" >
                                    <template #default>
                                        <div class="option-content">
                                            <div class="option-title">{{ item.name }}</div>
                                            <div v-if="item.desc" class="option-desc">{{ item.desc }}</div>
                                        </div>
                                    </template>
                                    <template #content>
                                        <div style="position:sticky;top:0;z-index:99;background:#fff;padding: 4px 8px 4px 8px;width: calc(100% - 8px)">
                                            <a-input
                                                v-model="item.childSearch"
                                                placeholder="搜索子选项"
                                                size="small"
                                                style="margin-bottom: 8px;"
                                            >
                                            <template #prefix>
                                                <icon-search />
                                            </template>
                                            </a-input>
                                        </div>
                                        <a-doption
                                            v-for="child in item.children.filter(child => !item.childSearch || child.name.includes(item.childSearch))"
                                            :key="child.code"
                                            :value="child.code"
                                            :disabled="disableItem(child.code)"
                                            style="width: 260px;"
                                        >
                                            {{ child.name }}
                                        </a-doption>
                                    </template>
                                </a-dsubmenu>
                                <!-- 没有子选项的情况 -->
                                <a-doption v-else :value="item.code" :disabled="disableItem(item.code)" style="width: 260px;" >
                                    <div class="option-content">
                                        <div class="option-title">{{ item.name }}</div>
                                        <div v-if="item.desc" class="option-desc">{{ item.desc }}</div>
                                    </div>
                                </a-doption>
                            </template>
                        </template>
                    </a-dropdown>
                    <div v-if="pre.code" class="input-content line-after">
                        <a-select
                            v-model:model-value="pre.symbol"
                            style="height: 100%;"
                            :bordered="false"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            @change="symbolChange($event,num)">
                            <a-option v-for="sym in getSymbol(pre.valueType)" :key="sym.value" :value="sym.value">{{ sym.label }}</a-option>
                        </a-select>
                    </div>
                    <!-- 非int,正则, select -->
                    <a-input
                        v-if="
                        pre.valueType !=='int' &&
                        ['rm','nrm'].includes(pre.symbol)"
                        v-model:model-value="pre.inputValue"
                        placeholder="值"
                        class="input-content"
                        @input="inputChange($event,num)"
                    />
                    <!--包括,不包括,小于，小于等于，大于，大于等于 单选 select-->
                    <div v-if="['con','ncon','lt','lteq','gt','gteq'].includes(pre.symbol) && pre.valueType === 'string'" class="input-content">
                        <a-select
                            v-model:model-value="pre.inputValue"
                            :loading="pre.attrLoading"
                            allow-search
                            :placeholder="`选择${pre.name}`"
                            style="height: 100%;"
                            :bordered="false"
                            :allow-create="pre.obtainMethod === 'manual_input'"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            @popup-visible-change="selectPopup($event,pre.code, pre)"
                            @change="singleSelectChange($event,num)">
                            <a-option v-for="li in pre.attrList" :key="li.code" :value="li.code">
                                {{ li.name }}
                            </a-option>
                        </a-select>
                    </div>
                    <!--等于 不等于 多选 select-->
                    <div v-if="['eq','neq'].includes(pre.symbol) && pre.valueType === 'string'" class="input-content">
                        <a-select
                            v-model:model-value="pre.multipleValue"
                            allow-search
                            :loading="pre.attrLoading"
                            style="height: 100%;"
                            multiple
                            :bordered="false"
                            :max-tag-count="1"
                            :scrollbar="true"
                            :allow-create="pre.obtainMethod === 'manual_input'"
                            :placeholder="`选择${pre.name}`"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            @popup-visible-change="selectPopup($event,pre.code, pre)"
                            @change="multipleSelectChange($event,num)">
                            <a-option v-for="li in pre.attrList" :key="li.code" :value="li.code">
                                {{ li.name }}
                            </a-option>
                        </a-select>
                    </div>
                    <!-- int 非有值，非无值，input -->
                    <div v-if="pre.valueType ==='int' && !['ex','nex'].includes(pre.symbol)" class="next-input">
                        <div class="next-input-number">
                            <a-input-number v-model="pre.number1" @blur="value1Change($event,num)"/>
                        </div>
                        <span v-if="pre.symbol === 'scope'" class="word">与</span>
                        <div v-if="pre.symbol === 'scope'" class="next-input-number">
                            <a-input-number v-model="pre.number2" @blur="value2Change($event,num)"/>
                        </div>
                        <span v-if="pre.symbol === 'scope'" class="word">之间</span>
                    </div>
                    <!-- <div v-if="['percent'].includes(pre.code)" class="input-content">
                        <a-slider :default-value="[0,100]" range show-input style="height: 100%;display: inline-flex;padding: 0 12px;"/>
                    </div>
                    <div v-if="['time'].includes(pre.code)" class="input-content">
                        <a-date-picker
                            style="height: 40px;width: 100%;border: none;margin-top: 1px;"
                            show-time
                            format="YYYY-MM-DD hh:mm"
                            :bordered="false"
                        />
                    </div> -->
                    <div v-if="!pre.code" class="input-content">
                    </div>
                    <a-button type="text" class="text-btn" @click="addItem">和</a-button>
                    <div class="delete-btn" @click="deleteItem(num)">
                        <icon-close-circle size="24"/>
                    </div>
                </div>
                <!-- <div class="predicate"></div> -->
            </div>
            <div class="tip-content">所选条件类型无法进行定位估测</div>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :disabled="!isValid || !validList" :loading="createLoading" @click="saveData">
                <span v-if="modalType === 'create'">创建条件</span>
                <span v-else>保存</span>
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, inject, reactive, ref, watch} from "vue";
import {getRemoteConfigAttrList, getRemoteConfigAttrValue, saveCondition} from "@/api/marketing/api";
import {Message} from '@arco-design/web-vue';
import {booleanRangeList, colorList, dateRangeList, itemRangeList, textRangeList, valueRangeList} from './listJson'
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {getAttributeEnum} from "@/api/analyse/api";
import {cloneDeep} from "lodash";

const props = defineProps({
    conditionList:{
        type:Array,
        default:() => []
    },
    conditionScope: {  // 新增
      type: String,
      default: 'app_specific'  // 默认值
    },
    conditionParam:{
      type:String,
      default:undefined
    }
})
const modalVisible = ref(false)
const modalTitle = ref('定义新的条件')
const modalType = ref('')
const form = reactive({
    name: '',
    color: 'rgba(251, 140, 0, .24)',
})
const copyAbleValid = (name) => {
    if(modalType.value !== 'edit'){
        return props.conditionList.findIndex(item => item.name === name) === -1
    }
    return true
}
const rules = {
    name: [
        {
            required: true,
            message:'必须填写条件名称',
        },
        {
          validator: (value: string, cb: (error?: string) => void) => {
            // 更新正则表达式以允许数字、英文字符、下划线、连字符、感叹号、百分号、点和中文字符
            const regex = /^[A-Za-z0-9_!%-. \u4e00-\u9fa5]+$/;
            if (!regex.test(value)) {
              cb('条件标题包含无效字符');
            } else if(!copyAbleValid(value)) {
              cb('条件名称必须是独一无二的')
            } else {
              cb();
            }
          },
            trigger: ['change', 'blur'],
        }
    ],
}

const getSymbol = (type:string) => {
  // data是传入calcuSymbo值，用以筛选calcuSymbolName显示值
  const rangeLists = {
    'string': textRangeList,
    'int': valueRangeList,
    'float': valueRangeList,
    'array': itemRangeList,
    'date': dateRangeList,
    'datetime': dateRangeList,
    'boolean': booleanRangeList,
    'variant': itemRangeList,
  };
  return rangeLists[type];
};
const selectList = ref<any>([])

const predicateList = ref([
    {
        code:'',
        name:'',
        valueType:'',
        obtainMethod:'',
        inputValue:'',
        multipleValue:[],
        symbol:'',
        number1:undefined,
        number2:undefined,
        thresholds:[],
        attrList:[],
        attrLoading:false,
        attributeCode: ''
    },
])

const isValid = ref(false);

// const saveAble = computed(() => isValid.value);
const formRef = ref()
// 在需要验证的地方
const validateForm = () => {
  formRef.value.validate((errors: any) => {
    isValid.value = !errors && form.name !== '';
    console.log(isValid.value,'isValid.value');
  });
};

// 监听表单变化时触发验证
watch(() => form.name, () => {
    if(formRef.value){
        validateForm();
    }
}, {immediate:true, deep: true });
const validList = computed (() => {
    return predicateList.value.every(item => {
        // 检查 code 和 symbol 是否有值
        if (!item.code || !item.symbol) {
            return false; // 如果没有值，返回 false
        }
        // 检查 thresholds 的长度
        if (item.symbol !== 'rm' && item.symbol !== 'nrm' && (!item.thresholds || item.thresholds.length === 0)) {
            return false; // 如果 thresholds 长度不大于 0，返回 false
        }
        return true; // 通过当前项的检查
    });
})

const emits = defineEmits(['conditionChange']);
const colorChange = () => {
}
// 校验code是否已选择，已选则禁用
const disableItem = (code) => {
    return predicateList.value.some(item => item.code === code)
}
const symbolChange = (e,index) => {
    predicateList.value[index].thresholds = []
}
const inputChange = (e,index) => {
    predicateList.value[index].thresholds = [e]
}
const value1Change = (e,index) => {
  const num1 = predicateList.value[index].number1
  const num2 = predicateList.value[index].number2
  predicateList.value[index].thresholds = [num1]
  if (num1 !== undefined && num2 !== undefined &&num1 >= 0) {
    if(num1 > num2){
        predicateList.value[index].number2 = num1
    }
    predicateList.value[index].thresholds = [num1,num2]
  }
}

const value2Change = (e,index) => {
  const num1 = predicateList.value[index].number1
  const num2 = predicateList.value[index].number2
  if (num1 !== undefined && num2 !== undefined) {
    if(num2 < num1){
        predicateList.value[index].number1 = num2
    }
    predicateList.value[index].thresholds = [num1,num2]
  }
  console.log(predicateList.value,'predicateList.value');

}
const singleSelectChange = (e,index) => {
    predicateList.value[index].thresholds = [e]
}
const multipleSelectChange = (e,index) => {
    predicateList.value[index].thresholds = e
}
interface ConditionItem {
    code?:string;
    name?: string;
    color?: string;
}
const appCode = ref(inject<string>('appId'))

const getList = async () => {
    await getRemoteConfigAttrList({appId:appCode.value}).then(res => {
        selectList.value = res || []
    })
}
const conditionCode = ref('')
const formatInputValue = (pre) => {
    if(pre.valueType !=='int' &&['rm','nrm'].includes(pre.symbol) &&pre.obtainMethod === 'dropdown_selection'){
        return pre.thresholds[0]
    }
    if(pre.valueType !=='int' &&!['ex', 'nex'].includes(pre.symbol) &&pre.obtainMethod === 'manual_input'){
        return pre.thresholds[0]
    }
    if(['con','ncon','lt','lteq','gt','gteq'].includes(pre.symbol) &&pre.obtainMethod === 'dropdown_selection'){
        return pre.thresholds[0]
    }
    return ''
}
const formatMultipleValue = (pre) => {
    if(['eq','neq'].includes(pre.symbol) &&pre.valueType === 'string'){
        return pre.thresholds
    }
    return []
}
const formatNumber1 = (pre) => {
    if(pre.valueType ==='int' && !['ex','nex'].includes(pre.symbol) && pre.obtainMethod === 'manual_input'){
        return pre.thresholds[0]
    }
    return undefined
}
const formatNumber2 = (pre) => {
    if(pre.valueType ==='int' && ['scope'].includes(pre.symbol) && pre.obtainMethod === 'manual_input'){
        return pre.thresholds[1]
    }
    return undefined
}
const formatAttrList = async (pre, attributeCode) => {
    let list = [] as any
    if (pre.thresholds.length && pre.valueType === 'string' && attributeCode.startsWith('user_attribute')) {
        const data = {
            type: 1,
            attributeType: 'user',
            event: [],
            attribute: attributeCode,
        }
        pre.thresholds.forEach(v => {
            let item = {code: v, name: v}
            if (!list.includes(item)) {
                list.push(item)
            }
        })
        return list
    } else {
        const data = {
            appId:appCode.value,
            attribute:pre.name
        }
        if(pre.obtainMethod === 'dropdown_selection'){
            await getRemoteConfigAttrValue(data).then(res => {
                list = res || []
            })
        }
        return list
    }
}
const findItemByCode = (list, code) => {
    const foundItem = list.find(item => item.code === code);
    if (foundItem) {
        return foundItem;
    }
    const foundInChildren = list.flatMap(item => item.children || []).find(child => child.code === code) || null;
    return foundInChildren;
};
const formatRules = async (arr:any) => {
    const list = await Promise.all(arr.map(async (item) => {
        const attributeCode = findItemByCode(selectList.value, item.name)?.attributeCode;
        return {
            name: findItemByCode(selectList.value, item.name)?.name,
            code: item.name,
            valueType: item.valueType,
            obtainMethod: item.obtainMethod,
            thresholds: item.thresholds,
            symbol: item.symbol,
            attrLoading: false,
            attrList: await formatAttrList(item, attributeCode), // 使用 await 获取 attrList
            inputValue: formatInputValue(item),
            multipleValue: formatMultipleValue(item),
            number1: formatNumber1(item),
            number2: formatNumber2(item),
            attributeCode: attributeCode
        }
    }));
    return list;
}
const openModal = async (val?:ConditionItem,type?:string) => {
    console.log(val,'val');
    conditionCode.value = val?.code || ''
    modalTitle.value = type ? '修改条件' : '定义新的条件'
    modalType.value = type || 'create'
    formRef.value.resetFields()
    formRef.value.clearValidate()
    // isValid.value = false
    predicateList.value = [
        {
            code:'',
            name:'',
            valueType:'',
            obtainMethod:'',
            inputValue:'',
            multipleValue:[],
            symbol:'',
            number1:undefined,
            number2:undefined,
            thresholds:[],
            attrList:[],
            attrLoading:false,
            attributeCode: ''
        }
    ]
    await getList()
    modalVisible.value = true
    if(val){
        const arr = Object.entries(val?.rules).map(([key, value]) => ({
            ...value
        }));
        predicateList.value = await formatRules(arr)
    }
    form.name = val?.name || ''
    form.color = val?.color || 'rgba(251, 140, 0, .24)'
    if(modalType.value === 'copy'){
        formRef.value.validateField('name')
    }
}
const closeModal = () => {
    modalVisible.value = false
}
const createLoading = ref(false)
const saveData = async () => {
    createLoading.value = true
    const ruleList = predicateList.value.map(item => {
        return {
            name:item.code,
            symbol:item.symbol,
            thresholds:item.thresholds,
            valueType:item.valueType,
            obtainMethod:item.obtainMethod
        }
    })
    const ruleObject = ruleList.reduce((acc, item) => {
        acc[item.name] = item;
        return acc;
    }, {});
    console.log(props);
    const data = {
      appId:appCode.value,
      paramScope:'app_specific',
      conditionName:form.name,
      conditionColor:form.color,
      conditionScope:props.conditionScope,
      paramName:props.conditionParam,
      rules:ruleObject,
      conditionCode: conditionCode.value || undefined
    }
    try{
        await saveCondition(data).then(async res => {
            if (res) {
                const params = {
                    code:res.code,
                    name:form.name,
                    color:form.color,
                    rules:ruleObject
                }
                emits('conditionChange',params)
                Message.success('创建成功')
                modalVisible.value = false
            }
        })
    }catch(error){
        console.log(error);

    }finally{
        createLoading.value = false
    }

}
// 添加条件
const addItem = () => {
    predicateList.value.push({
        code:'',
        name:'',
        valueType:'',
        obtainMethod:'',
        inputValue:'',
        symbol:'',
        multipleValue:[],
        number1:undefined,
        number2:undefined,
        thresholds:[],
        attrList:[],
        attrLoading:false,
        attributeCode: ''
    })
}
// 删除条件
const deleteItem = (index:number) => {
    if(predicateList.value.length>1){
        predicateList.value.splice(index,1)
    }else{
        predicateList.value[index] = {
            code:'',
            name:'',
            valueType:'',
            obtainMethod:'',
            inputValue:'',
            multipleValue:[],
            symbol:'',
            number1:undefined,
            number2:undefined,
            thresholds:[],
            attrList:[],
            attrLoading:false,
            attributeCode: ''
        }
    }
}

// const attrList = ref<any>([])
// const attrLoading = ref(false)
const selectPopup = async (v,code, attribute) => {
    if (v) {
        const index = predicateList.value.findIndex(item => item.code === code)
        if (attribute.attributeCode.startsWith('user_attribute')) {
            predicateList.value[index].attrLoading = true
            const data = {
                type: 1,
                attributeType: 'user',
                event: [],
                attribute: attribute.attributeCode,
            }
            await getAttributeEnum(data).then(res => {
                if (res) {
                    predicateList.value[index].attrList = res || []
                }

                attribute.thresholds.forEach(v => {
                    let item = {code: v, name: v}
                    if (!predicateList.value[index].attrList.includes(item)) {
                        predicateList.value[index].attrList.push(item)
                    }
                })

                predicateList.value[index].attrLoading = false;
            }).catch(error => {
                predicateList.value[index].attrLoading = false;
            });
        } else {
            if(attribute.obtainMethod === 'dropdown_selection'){
                predicateList.value[index].attrLoading = true
                const data = {
                    appId:appCode.value,
                    attribute:code
                }
                await getRemoteConfigAttrValue(data).then(res => {
                    predicateList.value[index].attrList = res || []
                })
                predicateList.value[index].attrLoading = false
            }
        }
    }
}


const conditionSelect = (v,index) => {
    console.log(v,'13')
    const obj = findItemByCode(selectList.value,v)
    // console.log(obj,'obj')
    predicateList.value[index].code = obj?.code
    predicateList.value[index].name = obj?.name
    predicateList.value[index].valueType = obj?.valueType
    predicateList.value[index].obtainMethod = obj?.obtainMethod
    if(['country'].includes(v)){
        predicateList.value[index].symbol = 'con'
    }else{
        predicateList.value[index].symbol = 'eq'
    }
    predicateList.value[index].inputValue = ''
    predicateList.value[index].multipleValue = []
    predicateList.value[index].number1 = undefined
    predicateList.value[index].number2 = undefined
    predicateList.value[index].thresholds = []
    predicateList.value[index].attrList = []
    predicateList.value[index].attributeCode = obj?.attributeCode
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.desc{
    color: var(--color-text-2);
    margin-bottom: 16px;
}
.types{
    display: flex;
}
.non-shrink-item {
  flex: 0 0 auto;
}
.option-item{
    width: 100%;
    background: rgba(109, 76, 65, .24);
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.form-label{
    color: var(--color-text-2);
    font-size: 13px;
    padding-bottom: 8px;
}
.form-content{
    border-radius: 4px;
    box-shadow: 2px 0 0 -1px var(--color-secondary) inset, -2px 0 0 -1px var(--color-secondary) inset, 0 2px 0 -1px var(--color-secondary) inset;
    display: block;
    min-height: 44px;
    flex-grow: 1;
    max-width: 100%;
    .predicate{
        border-radius: 4px;
        border-bottom: 1px solid var(--color-secondary);
        display: flex;
        height: 44px;
        // position: relative;
        .text-btn{
            background: transparent;
            height: 100%;
            flex-shrink: 0;
            // position: absolute;
            // top: 0;
            // right: 42px;
        }
        .delete-btn{
            width: 42px;
            height: 42px;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 100%;
            cursor: pointer;
            opacity: 0;
            &:hover{
                background: var(--color-secondary);
            }
        }
        &:hover .delete-btn{
            opacity: 1;
        }
        .trigger-content{
            padding: 0 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            position: relative;
            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 6px;
                bottom: 6px;
                width: 1px;
                background-color: var(--color-secondary);
                display: block;
                pointer-events: none;
            }
        }
        .no-border{
            &::after {
                display: none;
            }
        }
        .input-content{
            // width: 230px;
            flex: 1;
            background: none;
            border: 0;
            height: 100%;
            outline: none;
            :global(.arco-select-view-input){
                height: 100%!important;
            }
        }
    }

}

.option-content{
    padding-bottom: 4px;
    .option-title{
        list-style: 16px;
    }
    .option-desc{
        font-size: 12px;
        color: var(--color-text-2);
        line-height: 16px;
    }
}
.tip-content{
    border: 1px solid var(--color-secondary);
    border-radius: 8px;
    color: rgb(var(--primary-5));
    margin-top: 8px;
    padding: 16px;
}
.line-after{
    position: relative;
    &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 6px;
        bottom: 6px;
        width: 1px;
        background-color: var(--color-secondary);
        display: block;
        pointer-events: none;
    }
}
.word {
    margin-right: 6px;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;
    flex-shrink: 0;
}

.next-input {
    display: inline-flex;
    align-items: center;
    vertical-align: top;
    margin-left: 8px;
    flex: 1;
}

.next-input-number {
    // width: 60px;
    margin-right: 8px;
}
.name-text{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
