<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" title="KPI指标选择" :footer="false" @cancel="closeModal">
        <div class="content">
            <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
                <a-radio-group v-model:model-value="kpiType">
                    <a-radio value="event" disabled>事件</a-radio>
                    <a-radio value="indicator">指标</a-radio>
                </a-radio-group>
                <a-form-item v-if="kpiType === 'event'" field="event" validate-trigger="change" :hide-asterisk="true">
                    <a-select v-model:model-value="form.event" :style="{marginRight:'8px'}" placeholder="选择事件" allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <template #label="{ data }">
                            <span>事件-{{data?.label}}</span>
                        </template>
                        <a-option v-for="item in eventData" :key="item.eventCode" :value="item.eventCode" style="max-width: 400px;">{{item.eventDisplayName}}</a-option>
                    </a-select>
                </a-form-item>
                <a-form-item v-else field="indicator" validate-trigger="change" :hide-asterisk="true">
                    <a-select v-model:model-value="form.indicator" :style="{borderRadius:'4px'}" placeholder="选择指标" allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <template #label="{ data }">
                            <span>指标-{{data?.label}}</span>
                        </template>
                        <a-optgroup v-for="(item,index) in indicatorData" :key="index" :label="item.type">
                            <a-option v-for="el in item.items" :key="el.code" :value="el.code">{{ el.displayName }}</a-option>
                        </a-optgroup>
                    </a-select>
                </a-form-item>
            </a-form>
        </div>
        
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {inject, reactive, ref} from "vue";

const indicatorData = inject('indicatorList');
const eventData = inject('evtLists')

const kpiType = ref('indicator')
const modalVisible = ref(false)
const form = reactive({
    event: '',
    indicator:''
})
const rules = {
    event: [
        {
            required: true,
            message:'请选择事件',
        }
    ],
    indicator: [
        {
            required: true,
            message:'请选择指标',
        }
    ],
}
const formRef = ref()

const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}

const emits = defineEmits(['transferData']);
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            emits('transferData',form)
            modalVisible.value = false
        }
    })
}
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>