<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
            </div>
        </div>
        <div class="page-body ">
            <div class="cross-content">
                <a-table :loading="loading" :columns="columns" :data="historyList" :pagination="false">
                    <template #packResult="{record}">
                        <a-space>
                            <a-tag v-if="record.packResult === 2" color="orangered">失败</a-tag>
                            <a-tag v-else-if="record.packResult === 1" color="green">成功</a-tag>
                            <a-tag v-else color="gray">未完成</a-tag>
                        </a-space>
                    </template>
                    <template #operation="{record}">
                        <a-space>
                            <a-tooltip content="查看日志" v-if="record.packResult > 0">
                                <a-button class="border-mini" type="primary" size="mini" @click="(ev) => getCompleteLog(ev, record)">LOG</a-button>
                            </a-tooltip>
                            <a-tooltip content="查看日志" v-else>
                                <a-button class="border-mini" disabled type="primary" size="mini">LOG</a-button>
                            </a-tooltip>
                            <a-tooltip content="下载文件" v-if="osName === 'android' && record.packResult === 1">
                                <a-button class="border-mini" type="primary" size="mini" @click="(ev) => downloadPackage(ev, record)">{{record.appType.toUpperCase()}}</a-button>
                            </a-tooltip>
                            <a-tooltip content="下载文件" v-else-if="osName === 'android' && record.packResult !== 1">
                                <a-button class="border-mini" disabled type="primary" size="mini">{{record.appType.toUpperCase()}}</a-button>
                            </a-tooltip>
                        </a-space>
                    </template>
                </a-table>
                <div class="pagination">
                    <a-pagination :total="total" show-total @change="pageChange"/>
                </div>
            </div>
        </div>
        <complete-log-modal ref="completeLogRef" />
    </div>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import {onMounted, reactive, ref} from "vue";
import {useEventBus, useSessionStorage, useWebSocket} from "@vueuse/core";
import selectApp from "@/components/selected-game-app/index.vue"
import {getPackHistoryList} from "@/api/marketing/api";
import {LocalStorageEventBus} from "@/types/event-bus";
import {formatTimestamp} from "@/utils/dateUtil";
import {Message} from "@arco-design/web-vue";
import CompleteLogModal from "@/views/operation/pack/packCenter/components/completeLogModal.vue";

const route = useRoute();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const baseWsURL = process.env?.BASE_PACK_URL
const token = localStorage.getItem('token');
const retriesNum = process.env.NODE_ENV === 'development' ? 1 : -1
const {status, data, send, open, close} = useWebSocket(`${baseWsURL}/api/pack/ws?token=${token}&clb_type=log&server_type=`, {
    autoReconnect: {
        retries: retriesNum,
        delay: 2000,
        onFailed() {
            console.error('重试后无法连接 WebSocket！')
        },
    },
    onConnected(ws) {
        console.debug('websocket 连接成功！')
    },
    onDisconnected(ws, event) {
        console.warn('websocket 连接断开连接！', ws)
    },
    onError(ws, event) {
        console.error('websocket 连接错误！', ws)
    },
    onMessage(ws, event) {
        if (event.type === 'message') {
            if (event.data instanceof Blob) {
                // downloadFile(event.data)
            } else {
                const message = JSON.parse(event.data)
                switch (message.method) {
                    case 'getCompleteLog':
                        let logStr = 'error log of this build task maybe have been delete!'
                        if (message.payload) {
                            if (message.payload.notify) {
                                Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                                logStr = message.payload.notify.message ? message.payload.notify.message : logStr
                            } else {
                                if (osName.value === 'ios') {
                                    logStr = message.payload.log ? message.payload.log : logStr
                                } else {
                                    logStr = message.payload.error ? message.payload.error : logStr
                                }
                            }
                        }
                        completeLogRef.value.setLogContent(logStr)
                        break;
                }
            }
        }
    }
})
const columns = [
    {
        title: '创建时间',
        dataIndex: 'createTime',
        render: (value) => {
            const {record} = value;
            return formatTimestamp(record.createTime)
        }
    },
    // {
    //     title: '应用编号',
    //     dataIndex: 'appId',
    // },
    // {
    //     title: '应用包名',
    //     dataIndex: 'packageName',
    //     render: (value) => {
    //         const {record} = value;
    //         return record.appOpts.packageName
    //     }
    // },
    {
        title: '应用版本',
        dataIndex: 'versionName',
        render: (value) => {
            const {record} = value;
            return record.appOpts.versionName
        }
    },
    {
        title: '版本CODE',
        dataIndex: 'versionCode',
        render: (value) => {
            const {record} = value;
            return record.appOpts.versionCode
        }
    },
    {
        title: 'SDK版本',
        dataIndex: 'sdkVer',
        render: (value) => {
            const {record} = value;
            if (osName.value === 'android') {
                return record.buildOpts.sdkVer
            } else {
                return record.buildOpts.sdkTag
            }
        }
    },
    {
        title: '数据版本',
        dataIndex: 'dataVersion'
    },
    {
        title: '打包类型',
        dataIndex: 'packType'
    },
    {
        title: '打包模式',
        dataIndex: 'packMode',
        render: (value) => {
            const {record} = value;
            return record.packMode === 'pad' ? 'PAD打包' : '普通打包'
        }
    },
    {
        title: '包体类型',
        dataIndex: 'appType',
        render: (value) => {
            const {record} = value;
            return record.appType
        }
    },
    {
        title: '打包人员',
        dataIndex: 'creator',
        render: (value) => {
            const {record} = value;
            return record.creator
        }
    },
    {
        title: '状态',
        slotName: 'packResult'
    },
    {
        title: '操作',
        slotName: 'operation'
    }
]

const completeLogRef = ref()
const loading = ref()
const total = ref(0)
const osName = ref('')
const historyList = ref([])
const reqParams = reactive({
    appId: '',
    current: 1,
    pageSize: 10,
})

localStorageEventBus.on((name, value) => {
    if (name === 'app-data') {
        osName.value = value?.osName
        reqParams.appId = value?.code
        getHistoryLog()
    }
})

const getHistoryLog = () => {
    loading.value = true
    getPackHistoryList(reqParams).then(res => {
        total.value = res.total
        historyList.value = res.items
    }).catch(e => {
        console.log(e)
    }).finally(() => {
        loading.value = false
    })
}

const pageChange = (current: number) => {
    reqParams.current = current
    getHistoryLog()
}

const getCompleteLog = (event: MouseEvent, packRecord: object) => {
    completeLogRef.value.openModal()
    send(JSON.stringify({
        method: "getCompleteLog",
        osName: osName.value,
        payload: {
            buildType: packRecord.packType,
            appId: packRecord.appId,
            packageName: packRecord.appOpts.packageName,
            logName: `${packRecord.id}`
        }
    }));
}

const downloadPackage = (event: MouseEvent, packRecord: object) => {
    window.open(`https://**********:1443/${packRecord.appId}_${packRecord.appOpts.packageName}/${packRecord.appType}/${packRecord.appOpts.packageName}-${packRecord.appOpts.versionName}.${packRecord.appType}`)
}

onMounted(() => {
    let storageData = JSON.parse(useSessionStorage('app-data').value)
    console.log(storageData)
    reqParams.appId = storageData.code
    osName.value = storageData.osName
    getHistoryLog()
})
</script>

<style scoped lang="less">
.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
}
.page {
    .page-body {
        .cross-content {
            border: 1px solid var(--color-secondary);
            border-left: none;
            border-right: none;
            width: 100%;
            height: 100%;
        }
    }

    .border-mini{
        border-radius: 2px;
    }
}
</style>
