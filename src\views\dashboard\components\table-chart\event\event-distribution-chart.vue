<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import useLoading from '@/hooks/loading';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import {WsEventAnalysisResultData} from "@/api/report/type";

interface Props {
  /**
   * 展示label
   */
  showLabel: boolean

  /**
   * 报表数据
   */
  reportAnalysisData: WsEventAnalysisResultData;
}

const props = defineProps<Props>()

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const {loading, setLoading} = useLoading(true);
const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([]);
const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
const formatValue = (
    num: number | string | null | undefined,
    displayType?: { type: string; decimalNum: number }
): string => {
    if (num === null || num === undefined || num === '' || Number.isNaN(Number(num))) {
        return '';
    }
    const n = Number(num);
    if (Number.isNaN(n)) return '';
    let formattedValue = '';
    if (displayType?.type) {
        switch (displayType.type) {
            case 'default':
                formattedValue = n.toFixed(displayType.decimalNum);
                break;
            case 'percent':
                formattedValue = (n * 100).toFixed(displayType.decimalNum);
                break;
            default:
                formattedValue = n.toFixed(2);
        }
    } else {
        formattedValue = n.toFixed(2);
    }
    return formattedValue;
};
// 数组相加
const calculateSum = (array:any) => {
  return Number(array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0));
};
const percentNameList = ref<any>([])
function renderChart(wsResultData: WsEventAnalysisResultData) {
  if (!wsResultData) {
    return
  }
  xAxis.value = wsResultData?.y.map(item => item.displayName)
  const groups = wsResultData?.groups?.map(item => {
      return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  legendData.value = groups.map(item => item);
  const data = [];
  wsResultData.y.forEach(item => {
    item.yData.forEach(el => {
      data.push({
        displayName:item.displayName,
        displayType:item.displayType,
        groupName:el.group.map(value => value === null ? 'null' : value).join(','),
        values:formatValue(calculateSum(el.values),item.displayType),
        valuesCompared: el.valuesCompared ? el.valuesCompared.map(val => calculateSum(val)) : []
      });
    });
  });

  const uniqueGroups = [...new Set(data.map(item => item.groupName))];
  // 为每个 groupName 创建一个 series 对象
  ySeries.value = uniqueGroups.map(groupName => {
      const groupData = data.filter(item => item.groupName === groupName);
      return {
          name: groupName,
          data: groupData.map(item => item.values),
          type: 'bar',
          barWidth: uniqueGroups.length > 10 ? 'auto' : 40,
          label: {
            show: props.showLabel
          },
          large: true
      };
  });
  percentNameList.value = Array.from(
      new Map(
          data.filter(item => item.displayType.type === 'percent') // 筛选出 type 为 percent 的项
              .map(item => [item.displayName, { displayName: item.displayName, displayType: item.displayType }]) // 创建以 displayName 为键的 Map
      ).values() // 获取 Map 的值
  );
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData, oldData) => {
  renderChart(newData);
})

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '1%',
      top: '20',
      bottom: '5',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      type: 'scroll',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        formatter(value: any, idx: number) {
          if (idx === 0) return value;
          if (value > 1000 && value < 10000) return `${value}`
          if (value >= 10000) {
            // 直接格式化为千分位，并确保有 4 位数
            const formattedValue = new Intl.NumberFormat('en-US', {
              minimumFractionDigits: 4,
              maximumFractionDigits: 4
            }).format(value / 10000);
            return formattedValue.replace('.', ',');
          }
          return `${value}`;

        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E8EF',
        },
      },
    },
    tooltip: {
      trigger: 'item',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
      enterable: true,
      confine: true,
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        // 柱状图
        const paramsArray = Array.isArray(params) ? params : [params];
        const seriesInfo = paramsArray.map((param, index) => {
          const matchingY = percentNameList.value.find(y => y.displayName === param.axisValueLabel);
          // 使用 formatValue 统一格式化
          const value = formatValue(
            typeof param.value === 'number' ? param.value : Number(param.value),
            matchingY?.displayType || { type: 'default', decimalNum: 2 }
          );
          return `
            <div style="display: flex; align-items: center; margin-bottom: 5px;">
              <span style="flex-shrink: 0;">${param.marker}</span>
              <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
              <span style="flex-shrink: 0;">${formatNumber(value)}${matchingY?.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
            </div>
          `;
        }).join('');
        const tooltipHtml = `
          <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
            <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${paramsArray[0].name}">${paramsArray[0].name}</div>
            ${seriesInfo}
          </div>
        `;
        return tooltipHtml;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value
  };
});


onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

const showLabels = () => {
  ySeries.value.forEach((item: any, index: number) => {
    item.label.show = !props.showLabel
  })
}

defineExpose({
  showLabels
})

</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
