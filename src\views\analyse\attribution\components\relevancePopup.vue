<template>
  <div>
    <!-- 事件筛选弹窗 -->
    <a-popover trigger="click" position="tl" :popup-visible="popupVisible" style="z-index: 1000;">
      <a-tooltip content="设置关联属性" position="top">
        <div class="filter-icon" @click="openPopup">
          <icon-attachment/>
        </div>
      </a-tooltip>
      <template #content>
        <div class="ta-formula-filter-popup">
          <div class="ffp-head">设置关联属性</div>
          <div class="ffp-body">
            <div class="ffp-item">
              <span class="name">启用关联属性</span>
              <a-switch v-model:model-value="relevance" size="small"/>
            </div>
            <div class="ffp-item">
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor">
                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M1 3a2 2 0 012-2h10a2 2 0 012 2v1h-1V3a1 1 0 00-1-1H3a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1v-2h1v2a2 2 0 01-2 2H3a2 2 0 01-2-2V3zm5 1.5l3.6 8 1.35-4L15 6.722 6 4.5z"></path>
                </svg>
              </svg>
              <span class="event-name">游戏启动</span>
              的
              <div style="display: inline-block;line-height: 26px;padding: 0 8px;">
                <attrEnumSelect :info="relevanceInfo" :only-event="true" :disabled="!relevance" @tabs-change="relevanceChange"/>
              </div>
              与
              <span class="event-name">目标事件</span>
              的
              <div style="display: inline-block;line-height: 26px;padding: 0 8px;">
                <attrEnumSelect :info="targetInfo" :only-event="true" :disabled="!relevance" @tabs-change="targetChange"/>
              </div>
              的值相等
            </div>
          </div>
          <div class="ffp-foot">
            <a-button class="cancel" @click.stop="() => popupVisible = false">取消</a-button>
            <a-button type="primary" @click="filterDone">完成</a-button>
          </div>
        </div>
      </template>
    </a-popover>
    <div class="fixed-modal" :style="{display: popupVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue"
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"

const props = defineProps({
    info: {
      type: Array,
      default: () => {}
    }
})
const popupVisible = ref(false)
const relevance = ref(false)
const openPopup = () => {
    popupVisible.value = true
}
const relevanceInfo = reactive({
    name:'app版本',
    objectType:'string'
})
const targetInfo = reactive({
    name:'app版本',
    objectType:'string'
})
const emits = defineEmits(['eventScreen'])
// 筛选条件完成
const filterDone = () => {
    popupVisible.value = false
}
const targetChange = () => {

}
const relevanceChange = () => {

}
</script>

<style scoped lang="less">
.fixed-modal{
  position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background-color: transparent;
    visibility: hidden;
}
.ta-formula-filter-popup{
    width: 520px;
    // min-height: 156px;
    font-size: 14px;
    .ffp-head {
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        line-height: 20px;
    }
    .ffp-body{
       padding: 16px 0;
        border-bottom: 1px solid var(--tant-border-color-border1-1);
        .ffp-item{
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            flex-wrap: wrap;
            gap: 8px;
            .name{
                color: var(--tant-text-gray-color-text1-2);
                font: var(--tant-body-font-body-regular);
                margin-right: 8px;
            }
            .event-name{
                margin: 0 4px 0 8px;
                color: var(--tant-text-gray-color-text1-2);
                font: var(--tant-description-font-description-regular);
            }
        }
    }
    .ffp-foot{
        padding: 12px 0 0;
        text-align: right;
        :deep(.arco-btn){
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
        }
        .cancel {
            background: transparent;
            margin-right: 8px;

            &:hover {
            background-color: var(--color-secondary);
            }
        }
    }
}
.filter-icon{
    font-size: 16px;
    height: 26px;
    width: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--tant-text-gray-color-text1-3);
    transition: all .3s;
    border: 1px solid transparent;
    background-color: var(--tant-secondary-color-secondary-fill);
    margin-left: 8px;
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}

.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>