<script setup lang="ts">
import DisplayTypeSelect from "@/views/analyse/components/DisplayTypeSelect.vue";
import {ref} from "vue";
import {Indicator, IndicatorEvent, NumberDisplayConfig} from "@/api/analyse/type";
import EventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import SubSelect from "@/views/analyse/components/SubSelect.vue";
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import {numberToUpperLetter} from "@/utils/number-util";
import _, {cloneDeep} from "lodash";
import {Message} from "@arco-design/web-vue";
import {validateFormula} from "@/utils/indicator-util";
import {toolStore} from '@/store';
import CreateEventIndicatorModal from "@/views/analyse/components/CreateEventIndicatorModal.vue";

const toolData = toolStore();
const visible = defineModel<boolean>("visible");
const props = defineProps({
  indicator: {
    type: Object
  },
  fullEventList: {
    type: Object
  },
})
const emits = defineEmits(['confirm', 'cancel'])

// 自定义指标编辑表单值
const eventList = ref<IndicatorEvent[]>(cloneDeep(props.indicator.eventList))
const formula = ref<string>(cloneDeep(props.indicator.formula || 'A'))
const displayType = ref<NumberDisplayConfig>(cloneDeep(props.indicator.displayType || {}))

// 指标筛选条件引用
const queryFilterRefs = ref<any>([])

// 指标创建弹窗
const createIndicatorModalVisible = ref(false)
// 指标保存数据
const indicatorSaved = ref<Indicator>([])

/**
 * 指标选择
 */
const indicatorEventChange = (index: number, e: any) => {
  const filter = {
    ...eventList.value[index],
    ...e
  }
  const fieldsToRemove = e.type === 'indicator'
    ? ['eventCode', 'eventName', 'eventDisplayName']
    : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

  const cleanedFilter = _.omit(filter, fieldsToRemove);
  eventList.value[index] = cleanedFilter;
}

/**
 * 添加指标事件
 */
const addEvent = () => {
  const [firstEventValue] = toolData.toolModelList.flatMap(category => category.items || [])
  const firstEvent = firstEventValue
  const eventItem = {
    eventName: firstEvent.name,
    eventCode: firstEvent.code,
    eventDisplayName: firstEvent.displayName,
    type: firstEvent.objectType,
    eventType: firstEvent.type,
    eventAttrCode: '',
    eventAttrName: firstEvent.objectType === 'event' ? '总次数' : '',
    filter: {}
  };
  eventList.value.push(eventItem);
}

/**
 * 复制指标事件
 */
const copyIndicatorEvent = (index: number) => {
  const newItem = cloneDeep(eventList.value[index]);
  eventList.value.push(newItem);
}

/**
 * 复制指标事件
 */
const deleteIndicatorEvent = (index: number) => {
  eventList.value.splice(index, 1)
  queryFilterRefs.value[index].deleteAll()
}

/**
 * 指标统计变化
 */
const statisticsChange = (index: number, e: any) => {
  const {eventAttrName, eventAttrCode, eventAttrDisplayName, statisticalName, statisticalType, subjectName, subjectCode, objectType} = e
  const event = {...eventList.value[index]};
  event.eventAttrName = eventAttrName;
  event.eventAttrCode = eventAttrCode;
  event.eventAttrDisplayName = eventAttrDisplayName
  if (statisticalName) {
    event.statisticalName = statisticalName;
  } else {
    delete event?.statisticalName;
  }
  if (statisticalType) {
    event.statisticalType = statisticalType;
  } else {
    delete event?.statisticalType;
  }
  event.objectType = objectType;
  if (subjectName) {
    event.subjectName = subjectName;
  } else {
    delete event?.subjectName;
  }
  if (subjectCode) {
    event.subjectCode = subjectCode;
  } else {
    delete event?.subjectCode;
  }
  eventList.value[index] = event;
}

/**
 * 添加指标筛选条件
 */
const addQueryFilter = (index: number) => {
  queryFilterRefs.value[index].add()
}

/**
 * 指标筛选条件变化
 */
const queryFiltersChange = (index: number, v) => {
  eventList.value[index].filter = v;
}

/**
 * 参数校验
 */
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    // 如果 calcuSymbol 不是 ex 或 nex，如果是用户分群，则忽略con,ncon，否则必须有 thresholds 且长度大于 0
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    // 子筛选校验
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };

  // 遍历校验
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };

  // 验证所有事件
  return verifyFilterList(params.filter(event => event.filter?.filters?.length > 0).map(event => event.filter.filters) || []);
};

/**
 * 取消
 */
const editCustomIndicatorCancel = () => {
  emits('cancel', {chartType: 'default'})
  visible.value = false
}

/**
 * 确认
 */
const editCustomIndicatorConfirm = () => {
  if (!paramsVerify(eventList.value)) {
    Message.warning("指标事件筛选条件错误，请检查修正后再次提交！")
    return
  }
  const newFormula = formula.value.replace(/\s+/g, "");
  if (!_.isEmpty(newFormula) && !validateFormula(eventList.value.length, newFormula)) {
    Message.warning("计算公式不合法，请检查修正后再次提交！")
    return
  }
  emits('confirm', {
    eventList: eventList.value,
    formula: _.isEmpty(newFormula) ? "A" : newFormula,
    displayType: displayType.value
  })
  visible.value = false
}

/**
 * 创建
 */
const editCustomIndicatorSave = () => {
  if (!paramsVerify(eventList.value)) {
    Message.warning("指标事件筛选条件错误，请检查修正后再次提交！")
    return
  }
  const newFormula = formula.value.replace(/\s+/g, "");
  if (!_.isEmpty(newFormula) && !validateFormula(eventList.value.length, newFormula)) {
    Message.warning("计算公式不合法，请检查修正后再次提交！")
    return
  }
  indicatorSaved.value = {
    ...props.indicator,
    eventList: eventList.value,
    formula: _.isEmpty(newFormula) ? "A" : newFormula,
    displayType: displayType.value
  }
  createIndicatorModalVisible.value = true
}
const appendToFormula = (index) => {
  const valueToAdd = numberToUpperLetter(index);
  formula.value += valueToAdd;
};
</script>

<template>
  <a-modal v-model:visible="visible" width="620px" :mask-closable="false" style="z-index:999">
    <template #title>
      <div class="modal-title">自定义指标编辑</div>
    </template>
    <a-form layout="vertical">
      <a-form-item label="指标事件" required>
        <div class="event-list">
          <div v-for="(event,index) in eventList" :key="index" class="event-element">
            <div class="left-index" @click="appendToFormula(index)">
              <div class="index-text">{{ numberToUpperLetter(index) }}</div>
            </div>
            <div class="right-content">
              <div class="top-indicator">
                <EventIndicatorSelect :panel-data="event" @analysis-index-change="indicatorEventChange(index,$event)"/>
                <span v-if="event.type == 'event'" class="row-word">的</span>
                <sub-select v-if="event.type == 'event'" :panel-data="event" @sub-change="statisticsChange(index,$event)"/>
              </div>
              <event-query-filter
                  :ref="el => queryFilterRefs[index] = el"
                  :filter="event.filter"
                  :show-detail-filter="true"
                  :code-list="[event]"
                  @query-filters-change="queryFiltersChange(index, $event)"/>
              <div v-if="event?.filter?.filters?.length > 0" class="ta-filter-button" @click="addQueryFilter(index)">
                <icon-plus class="action"/>
                <div class="label">筛选条件</div>
              </div>
              <div class="action-right">
                <a-space align="center">
                  <a-tooltip content="添加筛选" position="top">
                    <a-button class="btn-bg btn-26" @click="addQueryFilter(index)">
                      <template #icon>
                        <icon-filter/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="复制" position="top">
                    <a-button class="btn-bg btn-26" @click="copyIndicatorEvent(index)">
                      <template #icon>
                        <icon-copy/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip v-if="eventList.length > 1" content="删除" position="top">
                    <a-button class="btn-bg btn-26 btn-bg-delete" @click="deleteIndicatorEvent(index)">
                      <template #icon>
                        <icon-close-circle/>
                      </template>
                    </a-button>
                  </a-tooltip>
                </a-space>
              </div>
            </div>
          </div>
          <a-button class="ta-filter-button event-add" @click="addEvent()">
            <icon-plus class="action"/>
            <div class="label">指标事件</div>
          </a-button>
        </div>
      </a-form-item>
      <a-form-item label="计算公式" tooltip="支持指标 + - * / ( ) 运算符，例如2*(A+B)/C" help="清空计算公式，可切换回基础指标">
        <a-input v-model="formula" allow-clear/>
      </a-form-item>
      <a-form-item label="展示格式">
        <display-type-select v-model:displayType="displayType"/>
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="boe-foot">
        <a-button class="cancel" @click.stop="editCustomIndicatorCancel">取消</a-button>
        <a-button type="primary" style="margin-right: 8px" @click="editCustomIndicatorSave">保存</a-button>
        <a-button type="primary" @click="editCustomIndicatorConfirm">确定</a-button>
      </div>
    </template>
    <create-event-indicator-modal
        v-if="createIndicatorModalVisible"
        v-model:visible="createIndicatorModalVisible"
        :calculate-formula-hidden="true"
        :indicator="indicatorSaved"/>
  </a-modal>
</template>

<style scoped lang="less">
.event-list {
  width: 100%;

  .event-element {
    display: flex;
    width: 100%;

    .left-index {
      display: flex;
      width: 40px;
      align-items: flex-start;
      margin: 5px;

      .index-text {
        display: inline-flex;
        align-items: center;
        height: 26px;
        padding: 0 8px;
        font-size: 14px;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        box-sizing: border-box;
      }
    }

    .right-content {
      width: calc(100% - 40px);
      overflow: hidden;
      position: relative;

      .top-indicator {
        display: flex;
        align-items: center;
        height: 36px;

        .row-word {
          display: inline-block;
          margin: 0 4px;
          color: var(--tant-text-gray-color-text1-2);
          vertical-align: top;
        }
      }

      .ta-filter-button {
        padding: 2px 0;
        display: flex;
        width: 100px;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-active);
        }

        &:hover .label {
          color: var(--tant-primary-color-primary-active);
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;

        .btn-bg {
          background-color: transparent;

          &:hover {
            background-color: var(--tant-secondary-color-secondary-transp-hover);
          }
        }

        .btn-26 {
          width: 26px;
          height: 26px;
          border-radius: 4px;
        }

        .btn-bg-delete {
          background-color: transparent;

          &:hover {
            color: var(--tant-status-danger-color-danger-default);
            background-color: var(--tant-status-danger-color-danger-fill-hover);
          }
        }
      }
    }

    &:hover {
      background-color: var(--tant-fill-color-fill1-2);

      .action-right {
        opacity: 1;
      }
    }
  }

  .event-add {
    background: transparent;
    margin-top: 18px;
    padding: 2px 0;
    display: flex;
    width: 100px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all .3s;
    border-radius: 4px;
    border: 1px solid var(--tant-secondary-color-secondary-fill);

    .action {
      border-radius: 4px;
      margin-right: 8px;
      padding: 3px;
      font-size: 18px;
    }


    &:hover {
      background-color: var(--tant-secondary-color-secondary-fill);
    }
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}
</style>