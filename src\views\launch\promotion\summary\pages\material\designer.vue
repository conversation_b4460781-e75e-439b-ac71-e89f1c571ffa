<template>
  <div class="material-content">
    <div class="setting">
      <div class="label">筛选</div>
      <div class="setting-content">
        <a-select placeholder="请选择汇总方式" allow-clear style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>汇总方式-{{ data?.label }}</span>
          </template>
          <a-option value="1">素材ID</a-option>
          <a-option value="2">素材名称</a-option>
        </a-select>
        <a-select placeholder="请选择渠道" allow-clear multiple :max-tag-count="1" style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>渠道-{{ data?.label }}</span>
          </template>
          <a-option value="1">Meta</a-option>
          <a-option value="2">Mintegral</a-option>
        </a-select>
      </div>
    </div>
    <a-divider />
    <div class="table-wrap">
      <div class="wrap-left"> </div>
      <div class="wrap-right">
        <a-button class="br4" style="margin-left: 12px">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>刷新</template>
        </a-button>
        <a-button class="br4" style="margin-left: 12px">
          <template #icon>
            <icon-download />
          </template>
          <template #default>导出</template>
        </a-button>
      </div>
    </div>
    <div class="table-area">
      <a-table :columns="columns" :loading="loading" :data="tableData" :bordered="false" :hoverable="true" sticky-header :table-layout-fixed="true" :filter-icon-align-left="true" :column-resizable="true" :scroll="scroll" :pagination="false" :summary="true">
        <template #name="{ record }">
          <div class="cell-content">
            <div class="text">{{ record.name }}</div>
            <div @click="toDetail(record)">
              <icon-right-circle style="cursor: pointer" />
            </div>
          </div>
        </template>
        <template #score="{ record }">
          <a-rate :default-value="record.score" readonly />
        </template>
        <template #summary-cell="{ column, record }">
          <div v-if="column.dataIndex === 'name'">
            <div class="text">汇总</div>
          </div>
          <div v-else-if="['ideas'].includes(column.dataIndex)">
            <div class="text">-</div>
          </div>
          <div v-else>{{ record[column.dataIndex] }}</div>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange" />
      </div>
    </div>
    <DetailDrawer ref="detailRef" :show-detail="false" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import DetailDrawer from '@/views/launch/promotion/components/materialDetailDrawer/index.vue';

  // 设置数据
  const filterParams = reactive({
    viewType: '1',
    showChildrenMaterial: false,
    showLaunchData: false,
  });
  const total = ref(0);
  // 模拟table数据
  const loading = ref(false);

  const tableData = ref<any>([
    {
      materialId: 1,
      name: 'mockName1',
      score: 4,
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
    {
      materialId: 2,
      name: 'mockName2',
      score: 3,
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
  ]);
  const columns = ref<any>([
    { title: '设计师', dataIndex: 'name', slotName: 'name', width: 150, fixed: 'left' },
    { title: '关联创意数', dataIndex: 'ideas', minWidth: 150 },
    { title: '有消耗素材数', dataIndex: 'useNum', minWidth: 150 },
    { title: '有消耗新素材数', dataIndex: 'newUseNum', minWidth: 150 },
    { title: '新素材使用率', dataIndex: 'usageRate', minWidth: 150 },
    { title: '花费', dataIndex: 'cost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '展示数', dataIndex: 'impressions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '千次展示成本', dataIndex: 'cpm', minWidth: 180, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击数', dataIndex: 'clicks', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击成本', dataIndex: 'cpc', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击率', dataIndex: 'ctr', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化数', dataIndex: 'conversions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化成本', dataIndex: 'conversionCost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化率', dataIndex: 'conversionRate', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
  ]);

  // Table页面滑动
  const scroll = {
    y: 'calc(100% - 56px)',
  };

  const detailRef = ref();
  const toDetail = (record: any) => {
    detailRef.value.openModal(record);
  };
  const init = () => {};
  init();
  // 分页
  const pageChange = (v) => {
    init();
  };
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
  :deep(.arco-select-view-tag) {
    span:first-child {
      width: 90%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: bottom;
    }
  }
</style>
