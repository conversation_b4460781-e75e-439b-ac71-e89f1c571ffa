<script setup lang="ts">
import {computed, ref} from "vue";
import NavBar from "@/components/navbar/index.vue";
import {useAppStore} from "@/store";
import PageLayout from "@/layout/page-layout.vue";
import {useRoute} from "vue-router";
import router from "@/router";
import TabBar from "@/components/tab-bar/index.vue";
import SideMenu from "@/layout/menu/side-menu.vue";

const route = useRoute();
const collapsed = ref(false)
const appStore = useAppStore();
const navbar = computed(() => appStore.navbar);

const menuWidth = computed(() => {
    return collapsed.value ? 48 : 240;
});
const layoutBodyMargin = computed(() => {
    return collapsed.value ? '48px' : `240px`;
});

// 菜单收起展开
const updateCollapsed = (newVal: boolean) => {
    collapsed.value = newVal;
}

const gotoItem = (item: string) => {
    router.push({
        name: item,
    });
};
</script>

<template>
    <a-layout class="layout" :class="{ mobile: appStore.hideMenu }">
        <div v-if="navbar" class="layout-navbar">
            <NavBar/>
        </div>
        <a-layout>
            <a-layout-sider
                v-if="route.meta.hasSideMenu"
                class="layout-sider"
                :collapsed="collapsed"
                :collapsible="true"
                :hide-trigger="true"
                :width="menuWidth"
                :style="{ paddingTop: navbar ? '60px' : '' }"
                @collapse="updateCollapsed"
            >
                <side-menu :collapsed="collapsed" @update-collapsed="updateCollapsed"/>
            </a-layout-sider>
            <a-layout class="layout-body">
                <div :style="route?.meta?.hasSideMenu?{marginLeft: layoutBodyMargin, marginTop: '60px'}:{marginTop: '60px'}">
                    <TabBar v-if="appStore.tabBar"/>
                    <a-layout-content>
                        <PageLayout/>
                    </a-layout-content>
                </div>
            </a-layout>
        </a-layout>
    </a-layout>
</template>

<style scoped lang="less">

.layout {

    width: 100%;
    height: 100%;
}

.layout-navbar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    width: 100%;
    height: var(--nav-height);
}

.layout-sider {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

    &::after {
        position: absolute;
        top: 0;
        right: -1px;
        display: block;
        width: 1px;
        height: 100%;
        background-color: var(--color-border);
        content: '';
    }

    > :deep(.arco-layout-sider-children) {
        overflow-y: hidden;
    }
}

.trigger {
    max-width: 244px;
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-overall);
}

.demo-basic {
    padding: 12px 8px 16px;
    background-color: var(--tant-bg-white-color-bg1-1);
    box-shadow: var(--tant-small-shadow-small-overall);;

    .trigger-title {
        margin-bottom: 6px;
        padding-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font: var(--tant-description-font-description-medium);
    }

    .trigger-item {
        height: 32px;
        margin-bottom: 10px;
        padding: 4px 4px 16px;
        color: var(--tant-text-gray-color-text1-2);
        font: var(--tant-header-font-header5-medium);
        border-radius: 4px;
        cursor: pointer;

        .trigger-options {
            padding: 4px;
            border-radius: 4px;
        }

        .trigger-options:hover {
            background-color: var(--tant-secondary-color-secondary-transp-hover);
        }
    }
}

.layout-body {
    display: flex;
    min-width: 1100px;
    min-height: 100vh;
    overflow-y: hidden;
    background-color: var(--color-fill-2);
    transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}

</style>
