<template>
    <div class="single-page">
      <a-page-header
          class="header"
          :title="_.isEmpty(tagCode)?'创建指标值标签':'编辑指标值标签'"
          @back="cancelConfirmVisible = true">
        <template #extra>
          <a-space>
            <a-button @click="cancelConfirmVisible = true">取消</a-button>
            <a-button type="primary" @click="pushSave">保存</a-button>
          </a-space>
        </template>
      </a-page-header>
      <div class="container">
        <div class="block">
          <div class="block-title">
            标签规则
            <div class="description">
            指定时段内，用户(分析主体)完成事件的聚合指标，作为标签值
            </div>
          </div>
          <div class="rule-item">
            <div class="title">
              分析时段
            </div>
            <date-picker :date-range="userIndicatorStage.dateRange" @date-pick="datePick"/>
          </div>
          <div class="rule-item">
            <div class="title">
              数据来源
            </div>
            <select-data-source v-model:data-source="dataSource" disable-storage/>
          </div>
          <div class="user-condition">
            <TagIndicatorValues ref="tagValueRef" :analysis-index-data="tagDisplayIndicators" @indicators-change="indicatorsChange"/>
            <div v-if="userIndicatorStage.indicator?.length < 1" class="ta-filter-button" @click="addRules">
              <div class="ta-filter-button-icon">
                <icon-plus/>
              </div>
              添加标签规则
            </div>
          </div>
        </div>
        <div class="block info-block">
          <div class="block-title">
            基本信息
          </div>
          <div class="tag-info">
            <a-form ref="formRef" :model="form" :rules="rules" class="form">
              <a-form-item
                  field="appId"
                  validate-trigger="blur"
                  label="应用ID"
                  label-col-flex="90px"
              >
                <a-input v-model="appId" disabled :style="{ width: '384px' }"></a-input>
              </a-form-item>
              <a-form-item
                  field="displayName"
                  validate-trigger="blur"
                  label="标签名称"
                  label-col-flex="90px"
              >
                <a-input v-model="form.displayName" placeholder="请输入标签名称" :style="{ width: '384px' }"></a-input>
              </a-form-item>
              <a-form-item field="status" label="启用状态" label-col-flex="90px">
                <a-switch v-model="form.status" :checked-value="1" :unchecked-value="0"/>
              </a-form-item>
<!--              <a-form-item-->
<!--                  field="timeZone"-->
<!--                  validate-trigger="blur"-->
<!--                  label="时区"-->
<!--                  label-col-flex="90px"-->
<!--              >-->
<!--                <time-zone-select v-model:time-zone="form.timeZone" disable-storage/>-->
<!--              </a-form-item>-->
<!--              <a-form-item-->
<!--                  field="calculateCron"-->
<!--                  validate-trigger="blur"-->
<!--                  label="定时计算"-->
<!--                  label-col-flex="90px"-->
<!--              >-->
<!--                <a-switch v-model="showCalculateCron"/>-->
<!--                <a-input v-if="showCalculateCron" v-model="form.calculateCron" class="cron-input" placeholder="请输入计算周期"/>-->
<!--              </a-form-item>-->
              <a-form-item
                  label-col-flex="90px"
              >
                <template #label>备注 <span class="description">(选填)</span>
                </template>
                <a-textarea v-model="form.remark" placeholder="请输入备注" show-word-limit :rows="3" :max-length="{length:200}" :style="{ width: '384px' }"></a-textarea>
              </a-form-item>
            </a-form>
          </div>
        </div>
      </div>
      <a-modal v-model:visible="cancelConfirmVisible" :align-center="false" title-align="start" :width="300" :top="140" @cancel="cancelConfirmVisible = false">
        <template #title>
          退出编辑
        </template>
        <div>用户标签编辑尚未保存。确认退出编辑吗？</div>
        <template #footer>
          <div style="display: flex;justify-content: flex-end;">
            <a-button style="margin-right: 8px;" @click="cancelConfirmVisible = false">继续编辑</a-button>
            <a-button type="primary" @click="router.back()">退出</a-button>
          </div>
        </template>
      </a-modal>
    </div>
  </template>

  <script setup lang="ts">
  import {getUserTagDetail, updateUserTagItem, userTagAdd} from "@/api/setting/api";
  import selectDataSource from "@/components/selected-data-source/index.vue"
  import {onMounted, reactive, ref} from 'vue'
  import {useRoute, useRouter} from 'vue-router'
  import {Message} from "@arco-design/web-vue";
  import {toolStore} from '@/store';
  import {ROUTE_NAME} from "@/router/constants";
  import _ from "lodash";
  import DatePicker from "@/components/date-picker/index.vue";
  import TagIndicatorValues from "@/views/data/user/components/tagIndicatorValues/index.vue";
  import {useSessionStorage} from "@vueuse/core";

  const appId = useSessionStorage('app-id', '')?.value;
  /**
   * 事件属性选择器使用
   * todo 优化
   */
  const toolData = toolStore();

  const route = useRoute()
  const router = useRouter()

  /**
   * 数据源
   */
  const dataSource = ref<string>('event');
  /**
   * 用户标签编码
   */
  const tagCode = ref<string>()
  /**
   * 展示计算周期选择器
   */
  const showCalculateCron = ref<boolean>(false)
  /**
   * 退出确认框
   */
  const cancelConfirmVisible = ref(false)

  /**
   * 创建标签名
   */
  function formatCurrentTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `标签${year}${month}${day}_${hours}${minutes}${seconds}`;
  }

  const form = ref({
    displayName: formatCurrentTimestamp(),
    timeZone: '+08:00',
    calculateCron: '0 0 1 * * ?',
    status:1,
    remark: '',
  })
  const userIndicatorStage = reactive({
    dateRange:{
        recentStartDate: 7,
        recentEndDate: 1,
        dateText: '过去7天'
    },
    indicator:[]
  })
  const rules = {
    displayName: [
      {
        required: true,
        message: '标签名不能为空',
      },
    ],
    timeZone: [
      {
        required: true,
        message: '时区不能为空',
      },
    ]
  }
  const datePick = (date: any) => {
    userIndicatorStage.dateRange = date
  };
  /**
   * 用户属性条件检查
   */
  const paramsVerify = (params) => {
    const verifyFilter = (item) => {
        if (
          item.calcuSymbol !== 'ex' &&
          item.calcuSymbol !== 'nex' &&
          !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
          !item.thresholds?.length) {
          return false;
        }
        if (item.subFilters?.length > 0) {
          return item.subFilters.every(subFilter =>
              subFilter.calcuSymbol === 'ex' ||
              subFilter.calcuSymbol === 'nex' ||
              ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
              subFilter.thresholds?.length > 0
          );
        }
        return true;
    };

    const verifyFilterList = (filterList = []) => {
        return filterList.length === 0 || filterList.every(items => {
        const itemArray = Array.isArray(items) ? items : [items];
        return itemArray.every(verifyFilter);
        });
    };

    // 获取需要验证的列表
    const firstList = params.filter(item => item.filter?.filters?.length > 0).map(item => item.filter.filters);
    const thirdList = params.flatMap(item =>
            item.eventList
                .filter(event => event.filter?.filters?.length > 0)
                .map(event => event.filter.filters)
        );

    // 验证所有列表
    return verifyFilterList(firstList) &&
        verifyFilterList(thirdList)
  };
  /**
   * 保存标签
   */
  async function pushSave() {
    if (!userIndicatorStage.indicator?.length) {
      Message.warning('标签条件不能为空，请点击按钮添加！')
      return
    }
    //  条件校验
    if (!paramsVerify(userIndicatorStage.indicator)) {
      Message.warning('筛选条件参数错误！')
      return
    }

    const data = {
      display_name: form.value.displayName,
      type: 4,
      timeZone: form.value.timeZone,
      calculateCron: showCalculateCron.value ? form.value.calculateCron : '',
      indicatorStage: {
        dateRange: userIndicatorStage.dateRange,
        indicator: userIndicatorStage.indicator[0]
      },
      dataType: 'float',
      dataSource: dataSource.value,
      note: form.value.remark,
      status: form.value.status,
    }
    if (_.isEmpty(tagCode.value)) {
      // 新增
      await userTagAdd(data).then(res => {
        if (Object.keys(res).length > 0) {
          Message.success('保存成功')
          router.push({
            name: ROUTE_NAME.USER_TAG
          })
        }
      }).catch(error => {
        console.error(error)
      })
    } else {
      // 编辑
      data.code = tagCode.value
      await updateUserTagItem(data).then(res => {
        if (Object.keys(res).length > 0) {
          Message.success('保存成功')
          router.push({
            name: ROUTE_NAME.USER_TAG
          })
        }
      }).catch(error => {
        console.error(error)
      })
    }
  }
  const tagDisplayIndicators = ref([])
  /**
   * 初始化
   */
  onMounted(async () => {
    const queryCode = route?.query.code;
    if (queryCode?.length > 0) {
      getUserTagDetail(queryCode).then(res => {
        tagCode.value = res.code
        form.value.displayName = res.displayName
        form.value.remark = res.note
        form.value.timeZone = res.timeZone
        form.value.status = res.status
        userIndicatorStage.dateRange = res.indicatorStage.dateRange
        userIndicatorStage.indicator = [res.indicatorStage?.indicator]
        tagDisplayIndicators.value = [res.indicatorStage?.indicator]
        dataSource.value = res.dataSource || 'event'
        const {calculateCron} = res;
        if (!_.isEmpty(calculateCron)) {
          showCalculateCron.value = true
          form.value.calculateCron = calculateCron
        }
      }).catch(e => console.error(e))
    }
    toolData.updateTemporaryList([])
    await toolData.fetchAllModalList()
    
  })
  const tagValueRef = ref()
  const addRules = () => {
    tagValueRef.value?.addItem()
  }
  const indicatorsChange = (params) => {
    userIndicatorStage.indicator = params
  }
  </script>

  <style scoped lang="less">
  .single-page {
    width: 100%;
    height: 100%;

    .header {
      background: var(--color-bg-2);
      margin-bottom: 24px;
    }

    .container {
      margin: 24px;
      padding: 24px;
      height: calc(100vh - 120px);
      overflow: auto;
      background-color: var(--tant-bg-white-color-bg1-1);
      border-radius: 4px;

      .block {
        margin-bottom: 32px;

        .block-title {
          display: flex;
          align-items: flex-end;
          margin-bottom: 16px;
          color: var(--tant-text-gray-color-text1-1);
          font: var(--tant-header-font-header4-medium);

          .description {
            margin-left: 8px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-regular);
          }
        }

        .rule-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          .title {
            padding-right: 16px;
          }
        }
        .user-condition {
          padding: 20px;
          border: 1px solid var(--tant-border-color-border1-1);
          border-radius: 8px;
          .ta-filter-button {
            display: inline-flex;
            color: var(--tant-primary-color-primary-default);
            align-items: center;
            padding: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all .3s;

            .ta-filter-button-icon {
              background-color: #eaeefd;
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 3px;
              margin-right: 6px;
            }

            &:hover {
              background-color: var(--tant-bg-white-color-bg1-1);
              color: var(--tant-primary-color-primary-hover);
            }
          }
        }

        .tag-info {
          .description {
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-regular);
          }

          .cron-input {
            width: 328px;
            margin-left: 16px;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  </style>