<template>
  <div id="sqlRoot">
    <div class="flex-container">
       <analyse-header
        :report-data="reportData"
        :header-info="headerInfo"
        @update-report-form="updateReportForm"/>
    </div>
    
    <div style="margin: 0 24px;background-color: #fff;">
      <sql-edit-vue ref="sqlEditRef" :query-params="queryParams" @result="handleResult" @report="handleReport"/>
    </div>
    <div style="margin: 0 24px 45px 24px;background-color: #fff;min-height: 73vh;">
      <a-tabs :active-key="activeKey" style="margin: 10px 0" @tab-click="handleChange">
        <template #extra>
          <a-button
            v-show="activeKey=='result'&&showStatementsBtn"
            style="margin-right: 25px;"
            class="btn"
            @click="pushChart">
            添加图表
          </a-button>
          <a-button
            v-show="activeKey=='result'&&showStatementsBtn"
            style="margin-right: 25px;"
            class="btn"
            @click="toSave">
            {{reportId? '更新报表' : '保存报表'}}
          </a-button>
          <a-button
            v-show="activeKey=='result'&&showStatementsBtn"
            style="margin-right: 25px;"
            @click="exportXlsx">
            导出
          </a-button>
          <div style="height: 56px"></div>
        </template>
        <a-tab-pane key="1">
          <template #title>&nbsp;&nbsp; 表结构&nbsp;&nbsp;</template>
          <structure-table @column="handleColumn"/>
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #title>&nbsp;&nbsp; 查询历史&nbsp;&nbsp;</template>
          <history-table-vue ref="historyTableRef" @history="historysqlParams"/>
        </a-tab-pane>
        <a-tab-pane key="result">
          <template #title>&nbsp;&nbsp; 查询结果&nbsp;&nbsp;</template>
          <result-table ref="resultTableRef"/>
        </a-tab-pane>
        <a-tab-pane key="4">
          <template #title>&nbsp;&nbsp; 语句书签&nbsp;&nbsp;</template>
          <bookmark-table-vue ref="bookmarkTableRef" @bookmark="editAddAll"/>
        </a-tab-pane>
      </a-tabs>
    </div>

    <a-modal
      v-model:visible="saveVisible"
      title-align="start"
      :mask-closable="false"
      :on-before-ok="validateForm"
      :ok-text="reportId? '更新':'保存'">
      <template #title>
        保存报表
      </template>
      <div>
        <a-form ref="saveFormRef" :model="form" :style="{width:'480px'}">
          <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
            <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
          </a-form-item>
          <a-form-item field="dashboard" label="保存至看板">
            <dashboard-select v-model:selected="form.dashboard" type="dashboard" :is-disabled="'分享给我的'"/>
          </a-form-item>
          <a-form-item field="description" label="备注">
            <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click.stop="saveVisible = false">取消</a-button>
        <a-button type="primary" :loading="saveLoading" @click="handleOk">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {h, onMounted, reactive, ref} from "vue";
import {saveAnalyseReportList} from "@/api/analyse/api";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import {ReportAnalyseModel} from "@/api/analyse/type";
import _ from "lodash";
import {ChartType} from "@/api/enum";
import {Message} from "@arco-design/web-vue";
import router from "@/router";
import {useRoute} from 'vue-router';
import analyseHeader from "@/views/analyse/components/analyseHeader.vue"
import {detailReport} from "@/api/report/api";
import {useStorage} from "@vueuse/core";
import sqlEditVue from "./components/sqlEdit.vue";
import historyTableVue from "./components/historyTable.vue";
import bookmarkTableVue from "./components/bookmarkTable.vue";
import structureTable from "./components/structureTable.vue"
import resultTable from "./components/resultTable.vue"
import {ROUTE_NAME} from "@/router/constants";

const route = useRoute();
const reportId = ref(route.query.code);

const headerInfo = reactive({
  title: 'SQL查询',
  img: '/icon/topMenu/custom.svg',
  tips: '使用SQL语句进行自定义查询，并通过动态参数灵活调整查询语句',
  root: '#sqlSearch',
  showUpdateTime: false,
  showDownloadData: false,
  showMoreValue: false,
  showAppSelect:false
})
const reportData = ref()
const saveFormRef = ref();
const sqlEditRef = ref()
const resultTableRef = ref()
const historyTableRef = ref()
const bookmarkTableRef = ref()
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const activeKey = ref<string>('1')
const saveVisible = ref<boolean>(false);
const showStatementsBtn = ref<boolean>(false);
const showChartsBtn = ref(true)

const sqlReportParams = ref()

const sqlChartQuery = useStorage("sql-chart-query", {});
const sqlChartValue = useStorage("sql-chart-value", []);
// const sqlQuery = ref(route.query.sqlValues);
// const resultValue = ref(route.query.result)
// sqlReportParams.value= route.query.report
// 接收表结构数据
const handleColumn = (value: any) => {
  sqlEditRef.value.addText(value)
}
// 接收书签数据
const editAddAll = (value: any) => {
  sqlEditRef.value.addTextAll(value)
}
// 接收历史sql
const historysqlParams = (value:any,sqlParams:any)=>{
  sqlEditRef.value.addsqlParams(sqlParams)
  sqlEditRef.value.addTextAll(value)
}
// 接收结果数据
const handleResult = (value: any) => {
  activeKey.value = 'result'
  // 暂存结果数据
  localStorage.setItem('resultValue', JSON.stringify(value))
  resultTableRef.value.sendResult(value?.result)
  // resultTableRef.value.refreshTheChartData([])
  resultTableRef.value.chartsDel()
  showChartsBtn.value = true
  showStatementsBtn.value = value?.result.headers.length !== 0;
}
// 接收sql语句
const handleReport = (value: any) => {
  sqlReportParams.value = value
  sessionStorage.setItem('sql-query-params', JSON.stringify(value))
}

// 切换tab
const handleChange = (value: any) => {
  activeKey.value = value
  if (value === '2') {
    historyTableRef.value.refreshData()
  } else if (value === '4') {
    bookmarkTableRef.value.refreshData()
  }
}


const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
// 编辑报表数据
const editReportData = reactive({
  name:'',
  description:''
})

const saveLoading = ref(false)
// 保存报表
const handleOk = _.debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    await validateForm()
    return;
  }
  saveLoading.value = true;
  const chartParams = [...
    [{graphStyle: sqlChartValue.value && sqlChartValue.value.length ? JSON.stringify(sqlChartValue.value): ''}]
  ]
  let chartType = ChartType.TABLE;
  if (sqlChartValue.value) {
    const graphType = sqlChartValue.value?.[0];
    chartType = graphType === 'table' ? ChartType.TABLE : ChartType.TREND;
  }
  try {
    await saveAnalyseReportList(
      ReportAnalyseModel.CUSTOM, 
      (reportId.value as string) || undefined,
      form.value.dashboard, 
      editReportData?.name || form.value.name, 
      editReportData?.description || form.value.description, 
      sqlReportParams.value,
      chartParams[0].graphStyle? chartParams : [],
      chartType
    )
    const successMsg = _.isEmpty(form.value.dashboard) 
      ? h('span', [
          '报表已保存'
        ])
      : h('span', [
          '报表已保存并添加至看板，',
          h('a', {
            style: { color: 'rgb(var(--primary-6))',cursor: 'pointer' },
            onClick: () => router.push({
                name: ROUTE_NAME.DASHBOARD,
            }),
          }, '去看板查看')
        ]);
    Message.success({
      content: () => successMsg,
      duration: 3000
    });
    saveVisible.value = false
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300);
// 更新报表数据
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  handleOk()
}
// 保存报表
const toSave = () => {
  if (reportId.value) {
    handleOk()
  }else{
    saveVisible.value = true
  }
}
// 跳转图表
const pushChart = () => {
  router.push({name: ROUTE_NAME.ANALYSE_CUSTOM_SQL_CHART, query: {showHeader:''}})
}


const queryParams = ref() // sqledit传参
// 初始化获取报表详情
const init = async () => {
  if(reportId.value){
    await detailReport(reportId.value as string).then(res => {
      reportData.value = res
      queryParams.value = res.queryParam
      editReportData.name = res?.name
      editReportData.description = res?.description
    })
  }
}
init()
onMounted(() => {
  // 接收sql图表数据
  if (sqlChartValue.value?.length && Object.keys(sqlChartQuery.value).length > 0){
    handleChange('result')
    showChartsBtn.value = false
    showStatementsBtn.value = true
    resultTableRef.value.sendResult(sqlChartQuery.value)
    resultTableRef.value.refreshTheChartData(sqlChartValue.value)
    if (sessionStorage.getItem('sql-query-params')) {
      sqlReportParams.value = JSON.parse(sessionStorage.getItem('sql-query-params') as any)
    }
  }else if(Object.keys(sqlChartQuery.value).length > 0){
    handleChange('result')
    showChartsBtn.value = false
    showStatementsBtn.value = true
    resultTableRef.value.sendResult(sqlChartQuery.value)
    resultTableRef.value.refreshTheChartData([])
    if (sessionStorage.getItem('sql-query-params')) {
      sqlReportParams.value = JSON.parse(sessionStorage.getItem('sql-query-params') as any)
    }
  } else{
    // localStorage.removeItem('reportsql')
    localStorage.removeItem('resultValue')
    // localStorage.removeItem('isInject')
    // sqlEditRef.value.clearlocal()
  }
})


// 导出
const exportXlsx = () => {
  resultTableRef.value.exportXlsx()
}
</script>

<style scoped lang="less">
.flex-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  // height: 74px;
  padding: 24px 24px 0px 24px;
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.imgTop {
  width: 30px;
  height: 30px;
  cursor: pointer;
}

.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;
}
.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}
.btn:hover {
    background-color: #f6f6f9;
}
</style>
