<template>
    <a-modal v-model:visible="modalVisible" :width="900" title-align="start" @cancel="handleCancel">
           <template #title>
               数据列选择
           </template>
           <div class="index-content">
             <div class="left">
               <a-collapse :default-active-key="['1']" :bordered="false" expand-icon-position="right">
                 <a-collapse-item key="1" header="数据列">
                   <div v-for="el in rowList" :key="el.code" class="list-box" :class="{ 'has-select': isAble(el) }" @click="addIndexItem(el)">
                     <span>{{ el.name }}</span>
                     <icon-arrow-right class="select-icon"/>
                   </div>
                 </a-collapse-item>
               </a-collapse>
             </div>
             <div class="right">
               <div class="title">已选择：支持排序</div>
               <a-divider style="margin: 8px auto;"/>
               <a-list v-if="checkList.length>0" :max-height="530">
                 <draggable :list="checkList" handle=".drag-handle">
                       <template #item="{ element,index }">
                           <a-list-item style="padding: 8px;">
                               <a-list-item-meta :description="element.name" >
                                 <template #avatar>
                                     <div style="display: flex;align-items: center;">
                                         <div class="drag-handle">
                                             <icon-drag-dot-vertical size="16"/>
                                         </div>
                                     </div>
                                 </template>
                               </a-list-item-meta>
                               <template #actions>
                                   <icon-delete @click="deleteDetailItem(index)"/>
                               </template>
                           </a-list-item>
                       </template>
                   </draggable>
               </a-list>
             </div>
           </div>
           <template #footer>
             <div class="footer">
               <div class="left">
                 <a-input v-model="editName" placeholder="请填写数据列名称" style="width: 240px;"/>
               </div>
               <div class="right">
                 <a-button style="margin-right: 8px;" class="cancel" @click="handleCancel">取消</a-button>
                 <a-button type="primary" :loading="saveLoading" style="margin-right: 8px;" @click="handleOk('update')">
                     保存
                 </a-button>
                 <a-button v-if="rowCode" type="primary" :loading="newLoading" @click="handleOk('add')">
                     另存为
                 </a-button>
               </div>
             </div>
           </template>
         </a-modal>
 </template>
 
 <script setup lang="ts">
 import {ref} from "vue";
 import draggable from 'vuedraggable'
 import {Message} from '@arco-design/web-vue';
 import {analyticsViewAdd, analyticsViewUpdate, getAnalyticsViewDetail} from "@/api/analyse/api"


 const emits = defineEmits(['updateRow'])
 const modalVisible = ref(false)
 const rowCode = ref('')
 const editName = ref('')
 const saveLoading = ref(false)
 const newLoading = ref(false)
 const checkList = ref<any>([])
 const rowList = [
    {
        code:'predict',
        name:'回本周期'
    },
    {
        code:'maEcpm',
        name:'变现ECPM'
    },
    {
        code:'apEcpm',
        name:'买量ECPM'
    },
    {
        code:'cumulativeProfit',
        name:'累计利润'
    },
    {
        code:'cumulativeRevenue',
        name:'累计收入'
    },
    {
        code:'cumulativeSpend',
        name:'累计投放'
    },
    {
        code:'cumulativeInstalls',
        name:'累计安装量'
    },
    {
        code:'runningDays',
        name:'运行天数'
    }
]
 const handleCancel = () => {
   modalVisible.value = false
 }
 
 
 const addIndexItem =(v) => {
   if (!checkList.value.some(item => item.code === v.code)) {
     checkList.value.push(v);
   }
 }
 
 const isAble = (v) => {
   const flag = checkList.value.some(item => item.code === v.code);
   return flag; // 返回右边是否存在
 }
 
 const deleteDetailItem = (index:number) =>{
     checkList.value.splice(index,1)
 }

 const handleOk = async (type:string) => {
     if(!checkList.value.length){
         Message.error('请至少选择一条数据')
         return
     }
     if(!editName.value){
         Message.error('请填写指标组名称')
         return
     }
     if(type === 'update' && rowCode.value){
         // 更新
         saveLoading.value = true
         const data = {
             code:rowCode.value,
             name:editName.value,
             indicator:checkList.value.map(item => item.code)
         }
         try {
             await analyticsViewUpdate(data).then(res => {
                 if(res){
                     emits('updateRow')
                     Message.success('保存成功')
                 }
             })
         } catch(error) {
            console.log(error);
         } finally {
             saveLoading.value = false
         }
         
     }else{
         // 另存
         newLoading.value = true
         const data = {
             type:11,
             name:editName.value,
             indicator:checkList.value.map(item => item.code)
         }
         try {
             await analyticsViewAdd(data).then(res => {
                 if(res){
                     Message.success('新建成功')
                     emits('updateRow',res.id)
                 }
             })
         } catch(error) {
            console.log(error);
         } finally {
             newLoading.value = false
         }
     }
     
     modalVisible.value = false
 }
 // 根据指标组code获取详情，checkList
 const getIndicatorsDetail = async () => {
   await getAnalyticsViewDetail(rowCode.value).then(res => {
     editName.value = res?.name || ''
     checkList.value = res?.indicator?.map(code => 
        rowList.find(row => row.code === code)
    );
   })
 }
 const openModal = async (code:string) =>{
     rowCode.value = code || ''
     editName.value = ''
     checkList.value = []
     if(rowCode.value){
         await getIndicatorsDetail()
     }
     modalVisible.value = true
 }
 
 
 defineExpose(
     {
         openModal
     }
 )
 </script>
 
 <style scoped lang="less">
 .footer{
     display: flex;
     justify-content: space-between;
     .left{
       display: flex;
     }
     .right{
       display: flex;
       justify-content: flex-end;
       button{
         border-radius: 4px;
       }
     }
     
 }
 .cancel {
     background: transparent;
     margin-right: 8px;
     &:hover {
         background-color: var(--color-secondary);
     }
 }
 .index-content{
   border: 1px solid var(--color-secondary);
   width: 100%;
   height: 100%;
   display: flex;
   .left{
     width: 50%;
     padding: 12px;
     height: 600px;
     border-right: 1px solid var(--color-secondary);
     overflow-y: scroll;
     .left-header{
       display: flex;
       button{
         border-radius: 4px;
       }
     }
     .search-content{
       width: 100%;
       padding: 8px;
       margin-top: 12px;
       background-color: var(--color-fill-1);
       .type-title{
         color: var(--color-text-1);
         font-size: 14px;
         line-height: 24px;
         padding-top: 8px;
         padding-bottom: 8px;
         overflow: hidden;
       }
     }
     .list-box {
         margin: 0 8px 8px;
         padding:0 12px;
         border-radius: 4px;
         color: var(--tant-text-gray-color-text1-2);
         font-size: 14px;
         line-height: 28px;
         height: 30px;
         background: #fff;
         cursor: pointer;
         display: flex;
         justify-content: space-between;
         align-items: center;
         .select-icon{
           opacity: 0;
         }
         &:hover .select-icon{
           opacity: 1;
         }
       }
     .has-select{
       cursor:no-drop;
       background: var(--color-secondary);
       &:hover .select-icon{
         opacity: 0;
       }
     }
   }
   .right{
     flex: 1;
     max-height: 600px;
     overflow: hidden;
     width: 100%;
     padding: 12px
   }
 }
 .drag-handle{
     cursor: grab;
     margin-right: 8px;
 }
 </style>