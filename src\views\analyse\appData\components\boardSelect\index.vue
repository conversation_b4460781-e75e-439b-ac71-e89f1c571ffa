<template>
   <a-modal v-model:visible="modalVisible" :width="680" title-align="start" @cancel="handleCancel" :footer="false">
      <template #title>
          面板编辑
      </template>
      <div class="view-content">
        <a-list v-if="sysList.length" :bordered="false" size="small">
          <template #header>
            <span class="header-underline">系统面板</span>
          </template>
          <a-list-item v-for="(item,idx) in sysList" :key="idx" @click="viewSelect(item)">
            <span style="line-height: 32px;">{{ item.name }}</span>
            <template #actions>
              <a-tooltip v-if="!item.isDefault" content="设为默认">
                <icon-check-circle @click.stop.prevent="setSysDefaultView(item,idx)"/>
              </a-tooltip>
              <icon-check-circle v-else class="star"/>
            </template>
          </a-list-item>
        </a-list>
        <a-list v-if="userList.length" :bordered="false" size="small">
          <template #header>
            <span class="header-underline">我的面板</span>
          </template>
          <a-list-item v-for="(item,idx) in userList" :key="idx" @click="viewSelect(item)">
            <a-tooltip v-if="!item.editing" content="点击编辑名称">
              <span style="line-height: 32px;" @click.stop="startEdit(item, 'user', idx)">{{ item.name }}</span>
            </a-tooltip>
            <a-input v-else :ref="el => setInputRef(el, `user-${idx}`)" v-model="item.editName" @blur="saveEdit(item, 'user', idx)" @keyup.enter="saveEdit(item, 'user', idx)" class="name-input"/>
            <template #actions>
              <a-tooltip v-if="!item.isDefault" content="设为默认">
                <icon-check-circle @click.stop.prevent="setUserDefaultView(item,idx)"/>
              </a-tooltip>
              <icon-check-circle v-else class="star"/>
              <a-tooltip content="分享">
                <icon-share-internal @click.stop.prevent="shareView(item)"/>
              </a-tooltip>
              <a-tooltip content="设置">
                <icon-settings @click.stop.prevent="setView(item,idx)"/>
              </a-tooltip>
              <a-tooltip content="删除">
                <icon-delete @click.stop.prevent="deleteView(item,idx)"/>
              </a-tooltip>
            </template>
          </a-list-item>
        </a-list>
        <a-list v-if="shareList.length" :bordered="false" size="small">
          <template #header>
            <span class="header-underline">分享给我的面板</span>
          </template>
          <a-list-item v-for="(item,idx) in shareList" :key="idx" @click="viewSelect(item)">
            <span v-if="item.permissionType === 4" style="line-height: 32px;">{{ item.name }}</span>
            <a-tooltip v-if="!item.editing && item.permissionType === 3" content="点击编辑名称">
              <span style="line-height: 32px;" @click.stop="startEdit(item, 'share', idx)">{{ item.name }}</span>
            </a-tooltip>
            <a-input v-if="item.editing && item.permissionType === 3" :ref="el => setInputRef(el, `share-${idx}`)" v-model="item.editName" @blur="saveEdit(item, 'share', idx)" @keyup.enter="saveEdit(item, 'share', idx)" class="name-input"/>
            <span style="color: #ccc;margin-left: 8px;">(创建人：{{ item.creator.name }})</span>
            <template v-if="item.permissionType === 3" #actions>
              <a-tooltip v-if="!item.isDefault" content="设为默认">
                <icon-check-circle @click.stop.prevent="setShareDefaultView(item,idx)"/>
              </a-tooltip>
              <icon-check-circle v-else class="star"/>
              <a-tooltip content="分享">
                <icon-share-internal @click.stop.prevent="shareView(item)"/>
              </a-tooltip>
              <a-tooltip content="设置">
                <icon-settings @click.stop.prevent="setView(item)"/>
              </a-tooltip>
              <a-tooltip content="删除">
                <icon-delete @click.stop.prevent="deleteShareView(item,idx)"/>
              </a-tooltip>
            </template>
            <template v-else #actions>
              <a-tooltip v-if="!item.isDefault" content="设为默认">
                <icon-check-circle @click.stop.prevent="setShareDefaultView(item,idx)"/>
              </a-tooltip>
              <icon-check-circle v-else class="star"/>
            </template>
          </a-list-item>
        </a-list>
      </div>
      <ShareBoard ref="shareRef"/>
      <BoardSet ref="viewSetRef" @permission-change="handlePermissionChange"/>
  </a-modal>
</template>

<script setup lang="ts">
import {nextTick, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {appDashboardSave, setDefaultAppDashboard} from "@/api/analyse/api"
import {removeDashboard} from "@/api/dashboard/api";
import ShareBoard from "./ShareBoard.vue";
import BoardSet from "./BoardSet.vue";

interface Props {
    boardViewData:any,
    layout:any
}
const props = defineProps<Props>();
const emits = defineEmits(['updateBoard'])
const modalVisible = ref(false)
const boardCode = ref('')
const userList = ref<any>([])
const shareList = ref<any>([])
const sysList = ref<any>([])
const editName = ref('')

const handleCancel = () => {
  modalVisible.value = false
}

const inputRefs = ref({})

const setInputRef = (el, key) => {
  if (el) {
    inputRefs.value[key] = el
  }
}

// 开始编辑名称
const startEdit = (item, type, idx) => {
  item.editing = true;
  item.editName = item.name;
  boardCode.value = item.code
  // 点击自动聚集
  nextTick(() => {
    const inputKey = `${type}-${idx}`
    const inputEl = inputRefs.value[inputKey]
    if (inputEl?.focus) {
      inputEl.focus();
    }
  });
};
// 保存编辑
const saveEdit = async (item, type, idx) => {
  if (!item.editName || item.editName.trim() === '') {
    Message.warning('面板名称不能为空');
    item.editName = item.name;
    item.editing = false;
    return;
  }
  
  if (item.editName === item.name) {
    item.editing = false;
    return;
  }

  try {
    const data = {
      code: item.code,
      name: item.editName,
      dashboardType: 'application'
    };
    
    await appDashboardSave(data);
    item.name = item.editName;
    item.editing = false;
    Message.success('重命名成功');
    emits('updateBoard');
  } catch (error) {
    Message.error('重命名失败');
    item.editName = item.name;
    item.editing = false;
  } finally{
    item.editing = false;
  }
};
// 设置我的面板默认
const setUserDefaultView = async (item,idx) => {
  try {
    await setDefaultAppDashboard({dashboardCode:item.code})
    Message.success('设置成功')
    userList.value = userList.value.map((el, index) => ({
      ...el,
      isDefault: index === idx
    }));
    shareList.value.forEach((el, index) => {
      el.isDefault = false
    });
    sysList.value.forEach((el, index) => {
      el.isDefault = false
    });
    emits('updateBoard')
  } catch(error) {
    console.error(error)
    Message.error('设置失败')
  }

}
// 设置分享给我的面板默认
const setShareDefaultView = async (item,idx) => { 
  try {
    await setDefaultAppDashboard({dashboardCode:item.code})
    Message.success('设置成功')
    shareList.value = shareList.value.map((el, index) => ({
      ...el,
      isDefault: index === idx
    }));
    userList.value.forEach((el, index) => {
      el.isDefault = false
    });
    sysList.value.forEach((el, index) => {
      el.isDefault = false
    });
    emits('updateBoard')
  } catch(error) {
    console.error(error)
    Message.error('设置失败')
  }
}
// 设置系统面板默认
const setSysDefaultView = async (item,idx) => { 
  try {
    await setDefaultAppDashboard({dashboardCode:item.code})
    Message.success('设置成功')
    sysList.value = sysList.value.map((el, index) => ({
      ...el,
      isDefault: index === idx
    }));
    userList.value.forEach((el, index) => {
      el.isDefault = false
    });
    shareList.value.forEach((el, index) => {
      el.isDefault = false
    });
    emits('updateBoard')
  } catch(error) {
    console.error(error)
    Message.error('设置失败')
  }
}

// 分享
const shareRef = ref()
const shareView = async (item) => {
  shareRef.value.openModal(item.code)
}
// 设置
const viewSetRef = ref()
const setView = async (item) => { 
  viewSetRef.value.openModal(item)
}
// 权限修改
const handlePermissionChange = (obj) => {
  userList.value = userList.value.map((el, index) => ({
    ...el,
    dashboardShare: el.code === obj.code ? obj.dashboardShare : el.dashboardShare
  }));
  shareList.value = shareList.value.map((el, index) => ({
    ...el,
    dashboardShare: el.code === obj.code ? obj.dashboardShare : el.dashboardShare
  }));
  emits('updateBoard')
}
// 处理删除当前code的情况
const handleDeleteCurrentView = async (code) => { 
  const list = sysList.value.concat(userList.value, shareList.value)
  if(code === boardCode.value){
    const defaultObj = list.find(item => item.isDefault)
    boardCode.value = defaultObj?.code || list[0]?.code
    if(boardCode.value){
      editName.value = list.find(item => item.code === boardCode.value)?.name || ''
    }
  }
}
// 删除
const deleteView = async (item,idx) => { 
  try {
    await removeDashboard([item.code])
    userList.value.splice(idx,1)
    await handleDeleteCurrentView(item.code)
    Message.success('删除成功')
    emits('updateBoard')
  } catch(error) {
    console.error(error)
    Message.error('删除失败')
  }
}
// 删除分享给我的面板
const deleteShareView = async (item,idx) => { 
  try {
    await removeDashboard([item.code])
    shareList.value.splice(idx,1)
    await handleDeleteCurrentView(item.code)
    Message.success('删除成功')
    emits('updateBoard')
  } catch(error) {
    console.error(error)
    Message.error('删除失败')
  }
}
const openModal = async (code:string) =>{
  userList.value = props.boardViewData?.user || []
  shareList.value = props.boardViewData?.share || []
  sysList.value = props.boardViewData?.sys || []
  editName.value = userList.value.concat(shareList.value, sysList.value).find(item => item.code === code)?.name || ''
  boardCode.value = code || ''
  modalVisible.value = true
}
// 选择面板
const viewSelect = async (item) => {
  editName.value = item.name
  boardCode.value = item.code
}

defineExpose(
  {
    openModal
  }
)
</script>

<style scoped lang="less">
.view-content{
  width: 100%;
  border: 1px solid var(--color-secondary);
  height: 600px;
  overflow-y: auto;
  :deep(.arco-list-item){
    cursor: pointer;
    &:hover{
      background-color: var(--color-secondary);
    }
    &.selected {
      background-color: var(--color-secondary);
      border-left: 3px solid var(--color-secondary);
    }
  }
  .star{
    color: var(--tant-decorative-yellow-color-decorative6-3) !important;
  }
}
.header-underline {
  text-decoration: underline;
  text-underline-offset: 4px;
}
.name-input{
  width: 240px;
}
</style>