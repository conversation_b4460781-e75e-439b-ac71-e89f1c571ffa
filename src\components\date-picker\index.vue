<template>
  <a-trigger
      v-model:popup-visible="visible"
      trigger="click"
      :update-at-scroll="true"
      :position="props.position || 'top'"
  >
    <a-button
        class="pick-button"
        style="display: flex; gap:4px; border: 1px solid #e3e4e5;"
        @click="()=>{visible=!visible}">
      {{ dateRangeTemp.dateText || formatDateRangeText(dateRangeTemp)}}
      <icon-calendar class="date-icon"/>
    </a-button>
    <template #content>
      <date-pick-panel :date-range="dateRangeTemp" :disabled-after-today="disabledAfterToday" @date-pick="datePick"/>
    </template>
  </a-trigger>
</template>

<script lang="ts" setup>
import DatePickPanel from "@/components/date-picker/components/DatePickPanel.vue";
import {DateRange} from "@/api/analyse/type";
import {ref, watch} from "vue";
import {formatDateRangeText} from "@/utils/dateUtil";

interface Props {
  dateRange: DateRange
  // 是否禁用今天以后的日期
  disabledAfterToday?: boolean
  // 弹窗位置
  position?:string
}

const props = withDefaults(defineProps<Props>(), {
  dateRange: {}, // 传入日期
  disabledAfterToday: false
})

// 日期选择
const emits = defineEmits(['datePick']);
// 选择面板显示
const visible = ref<boolean>(false);

const dateRangeTemp = ref(props.dateRange);

watch(() => props.dateRange, (newValue) => {
  dateRangeTemp.value = newValue
})

// 日期范围确认
const datePick = (pickedDateRange) => {
  visible.value = false;
  emits('datePick', pickedDateRange)
};
</script>

<style scoped lang="less">

.pick-button {
  background-color: #ffffff;
  padding: 8px;
  border-radius: 4px;
  color: var(--tant-text-gray-color-text1-2);
}

.pick-button:hover {
  color: #BDBEBF;
  background-color: #fff;
}

.date-icon {
  font-size: 16px;
  stroke-linecap: square;
  stroke-linejoin: round;
  stroke-width: 5;
}
</style>

