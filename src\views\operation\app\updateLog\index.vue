<script setup lang="ts">
import {computed, onMounted, reactive, ref, watch} from "vue";
import {getDelLog, getLogList, postAddLog, postUpdateLog} from "@/api/marketing/api";
import {useEventBus} from "@vueuse/core";
import _ from "lodash";
import dayjs from "dayjs";
import {Message} from "@arco-design/web-vue";
import note from "@/components/navbar-board/note.vue"
import {Alignment, Bold, ClassicEditor, Essentials, Font, FontBackgroundColor, Heading, ImageBlock, ImageInline, ImageUpload, Italic, Link, List, Undo} from "ckeditor5";

const openModal = ref(false)
const modalTitle = ref('添加日志')
const timeLine = ref();
const formRef = ref(null)
const eventBus = useEventBus('logList');
const deleteModalShow = reactive<Record<string, boolean>>({});
const changeModalShow = reactive<Record<string, boolean>>({});
const logTypeList = [
  {label: '更新日志', value: 1},
  {label: '数据变化', value: 2},
]
const Form = reactive({
  id:undefined,
  content: '',
  logType: 1,
  appVersion: '',
  logDate: dayjs().format('YYYY-MM-DD')
})
const Rules = {
  content: [
    {
      required: true,
      message:'请填写更新内容'
    }
  ],
  logType: [
    {
      required: true,
      message:'请选择更新类型'
    }
  ],
  logDate: [
    {
      required: true,
      message:'请选择更新日期'
    }
  ],
  appVersion: [
    {
      required: true,
      message:'请填写更新版本'
    }
  ],
}
const appId = ref('')
const groupedTimeLine = computed(() => {
  if (!timeLine.value || timeLine.value.length === 0) return new Map(); // 如果数据为空，返回空 Map

  return timeLine.value.reduce((acc: any, item: any) => {
    const year = item.logDate.slice(0, 4); // 提取年份
    const month = item.logDate.slice(5, 7); // 提取月份

    // 如果年份分组不存在，初始化为空 Map
    if (!acc.has(year)) {
      acc.set(year, new Map());
    }

    // 如果月份分组不存在，初始化为空数组
    if (!acc.get(year).has(month)) {
      acc.get(year).set(month, []);
    }

    // 添加到对应的月份分组
    acc.get(year).get(month).push(item);

    return acc;
  }, new Map<string, Map<string, any>>()); // 使用 Map，避免过多的类型定义
});
const openedGroups = reactive<Record<string, boolean>>({});

const groupedTimeLineArray = computed(() => {
 const list =  Array.from(groupedTimeLine.value, ([year, yearGroup]) => ({
    year,
    months: Array.from(yearGroup, ([month, monthItems]) => ({
      month,
      items: monthItems,
    }))
  }));
  return list
});
// 默认展开第一项
watch(groupedTimeLineArray, (newList) => {
  if (newList.length > 0) {
    openedGroups[newList[0].year] = true; // 更新 openedGroups
  }
});

const toggleYearGroup = (year: string) => {
  if (!(year in openedGroups)) {
    openedGroups[year] = true;  // 默认折叠
  }
  openedGroups[year] = !openedGroups[year];
};
const editLog = (obj?:any) => {
  // console.log(obj,'obj');
  if(obj && obj.appId){
    const {id, content, logType,appVersion,logDate} = obj
    Form.id = id
    Form.content = content
    Form.logType = logType
    Form.appVersion = appVersion
    Form.logDate = logDate
    modalTitle.value = '修改日志'
  }else{
    Form.id = undefined
    Form.content = ''
    Form.logType = 1
    Form.appVersion = ''
    modalTitle.value = '添加日志'
    Form.logDate = dayjs().format('YYYY-MM-DD')
  }
  openModal.value = true
}

const pushLog = () => {
  if (formRef.value) {
    formRef.value.validate(async (error: any) => {
      if (!error) {
        const config = {
          ...Form,
          appId: appId.value,
        }
        if(Form.id){
          await postUpdateLog(Form.id, Form.content).then((res) => {
            Message.success('日志更新成功')
            eventBus.emit('modify-log')
          }).catch((err) => {
            Message.error('日志更新失败')
          })
        }else{
          await postAddLog(config).then((res) => {
            Message.success('添加日志成功')
            eventBus.emit('add-log')
            Form.content=''
            Form.appVersion=''
            Form.logDate = dayjs().format('YYYY-MM-DD')
          }).catch((err) => {
            Message.error('添加日志失败', err)
          })
        }
        openModal.value = false
      } else {
        console.log(error)
        Message.error('请输入必填信息')
      }
    });
  }

};
const openDelModal = (year: string, month: string, dateIndex: number) => {
  deleteModalShow[`${year}-${month}-${dateIndex}`] = true; // 显示对应的删除弹窗
};
const openChangeModal = (year: string, month: string, dateIndex: number) => {
  changeModalShow[`${year}-${month}-${dateIndex}`] = true; // 显示对应的删除弹窗
};
// 关闭删除弹窗
const cancelItem = (year: string, month: string, dateIndex: number) => {
  deleteModalShow[`${year}-${month}-${dateIndex}`] = false; // 隐藏对应的删除弹窗
};
const cancelChangeItem = (year: string, month: string, dateIndex: number) => {
  changeModalShow[`${year}-${month}-${dateIndex}`] = false; // 隐藏对应的删除弹窗
};
const closeModal = () => {
  openModal.value = false
}
const deleteItem = (item: any) => {
  getDelLog(item.id).then((res) => {
    Message.success('删除日志成功')
    eventBus.emit('del-log')
  }).catch((err) => {
    Message.error('删除日志失败')
  })
  deleteModalShow.value = false
}
const modifyLog = (payload: any,item:any,year:string,month:string,day:number) => {

  postUpdateLog(item.id, payload).then((res) => {
    Message.success('日志更新成功')
    eventBus.emit('modify-log')
    changeModalShow[`${year}-${month}-${day}`] = false; // 隐藏对应的删除弹窗
  }).catch((err) => {
    Message.error('日志更新失败')
  })
}
const hoveredDate = reactive<Map<string, boolean>>(new Map()); // 存储每个日期的 hover 状态

const showDeleteButton = (year: string, month: string, dateIndex: number) => {
  // 存储当前日期是否 hover
  hoveredDate.set(`${year}-${month}-${dateIndex}`, true);
};

const hideDeleteButton = (year: string, month: string, dateIndex: number) => {
  // 取消当前日期的 hover 状态
  hoveredDate.set(`${year}-${month}-${dateIndex}`, false);
};

const isHovered = (year: string, month: string, dateIndex: number) => {
  // 判断当前日期是否处于 hover 状态
  return hoveredDate.get(`${year}-${month}-${dateIndex}`) || false;
};
eventBus.on(event => {
  if (event === 'add-log') {
    getLogList({appId: appId.value, logType: Form.logType}).then((resp) => {
      timeLine.value = _.cloneDeep(resp);
      // updateOpenedGroups();
    });
  }
  if (event === 'del-log') {
    getLogList({appId: appId.value, logType: Form.logType}).then((resp) => {
      timeLine.value = _.cloneDeep(resp);
      // updateOpenedGroups();
    });
  }
  if (event === 'modify-log') {
    getLogList({appId: appId.value, logType: Form.logType}).then((resp) => {
      timeLine.value = _.cloneDeep(resp);
      // updateOpenedGroups();
    });
  }
})
const getData = () => {
  appId.value = sessionStorage.getItem('app-id') || ''
  getLogList({appId: appId.value, logType: Form.logType}).then((resp) => {
    timeLine.value = _.cloneDeep(resp);
  });
}
const typeChange = () => {
  getData()
}
onMounted(() => {
  // getData()
});
defineExpose({getData})
const editor = ClassicEditor
const editorRef = ref(null)

const editorConfig = reactive({
  language: 'zh-cn',
  plugins: [Bold, Essentials, Italic, Undo, Font, Heading, Link,List,Alignment,ImageBlock,ImageInline,ImageUpload,FontBackgroundColor],
  toolbar: {
    items: ['undo', 'redo', '|', 'heading', 'bold', 'italic', 'fontColor', 'fontBackgroundColor', '|', 'alignment','bulletedList','imageUpload','numberedList', 'link',],
    shouldNotGroupWhenFull: true
  },
})
</script>

<template>
  <div class="pageCss">
    <div class="head">
      <h2>
      </h2>
      <div class="action-content">
        <a-select v-model:model-value="Form.logType" allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" style="width: 180px;border-radius: 4px;" @change="typeChange">
            <template #label="{ data }">
              <span>日志类型-{{data?.label}}</span>
            </template>
            <a-option v-for="(item,index) in logTypeList" :key="index" :value="item.value">
                {{ item.label }}
            </a-option>
        </a-select>
        <a-button class="button-add" @click="editLog">
          <icon-plus/>
          添加日志
        </a-button>
      </div>
    </div>
    <div class="body">
      <div v-for="(yearGroup, index) in groupedTimeLineArray" :key="index">
        <!-- 年份分组 -->
        <div class="group-header" @click="toggleYearGroup(yearGroup.year)">
          <H3>{{ yearGroup.year }}年</H3>
          <a-button :type="'text'">
            <template #icon>
              <icon-down v-if="openedGroups[yearGroup.year]"/>
              <icon-right v-else/>
            </template>
          </a-button>
        </div>
        <!-- 年份展开内容 -->
        <a-timeline
            v-show="openedGroups[yearGroup.year]"
            label-position="relative"
            :reverse="false">
          <div v-for="(monthGroup, monthIndex) in yearGroup.months" :key="monthIndex">
            <div class="group-header" style="margin-left: 20px">
              <h5>{{ monthGroup.month }} 月</h5>
            </div>
            <div>
              <a-timeline-item
                  v-for="(item, dateIndex) in monthGroup.items"
                  :key="dateIndex"
                  @mouseenter="showDeleteButton(yearGroup.year, monthGroup.month, dateIndex)"
                  @mouseleave="hideDeleteButton(yearGroup.year, monthGroup.month, dateIndex)"
              >
                <template #dot>
                  <icon-history/>
                </template>
                <template #label>
                  <div style="display: flex;flex-direction: column;justify-self: start">
                    <span>{{ item.logDate }}</span>
                    <span>版本号：{{ item.appVersion }}</span>
                  </div>
                </template>
                <div style="display: flex;flex-direction: column">
                  <span style="color: var(--color-text-3);font-size: 12px;">
                    更新人：{{ item.creator }}</span>
                  <div style="display:flex;flex-direction: row">
                    <div v-html="item.content"/>
                    <a-button-group
                        v-if="isHovered(yearGroup.year, monthGroup.month, dateIndex)" size="mini"
                        style="margin-left: 20px">

                      <a-button
                          class="delete-btn"
                          type="text"
                          status="danger"
                          @click="openDelModal(yearGroup.year, monthGroup.month, dateIndex)"
                      >
                        <icon-delete/>
                      </a-button>
                      <!-- <a-button
                          type="text"
                          @click="openChangeModal(yearGroup.year, monthGroup.month, dateIndex)">
                        <icon-edit/>
                      </a-button> -->
                      <a-button
                          type="text"
                          @click="editLog(item)">
                        <icon-edit/>
                      </a-button>
                    </a-button-group>
                  </div>
                  <a-modal
                      v-model:visible="deleteModalShow[`${yearGroup.year}-${monthGroup.month}-${dateIndex}`]"
                      width="auto"
                      :mask-closable="false"
                      :ok-button-props="{status:'danger'}"
                      :closable="true" ok-text="删除" title-align="start"
                      @ok="deleteItem(item)"
                      @cancel="cancelItem(yearGroup.year, monthGroup.month, dateIndex)">
                    <template #title>
                      删除日志
                    </template>
                    确认删除该日志吗？ 该操作不可恢复。
                  </a-modal>
                  <note
                      v-if="changeModalShow[`${yearGroup.year}-${monthGroup.month}-${dateIndex}`]"
                      :year="yearGroup.year"
                      :month="monthGroup.month"
                      :day="dateIndex"
                      :date="item"
                      @update-log="modifyLog"
                      @cancel-note="cancelChangeItem(yearGroup.year, monthGroup.month, dateIndex)"
                  />
                </div>
              </a-timeline-item>
            </div>
          </div>

        </a-timeline>
      </div>
      <div v-if="!groupedTimeLineArray.length" class="empty">
        <a-empty/>
      </div>
    </div>
    <a-modal
        :visible="openModal"
        title-align="start"
        :title="modalTitle"
        width="660px"
        :mask-closable="true"
        :footer="false"
        @cancel="closeModal">
      <div class="content-modal">
        <a-form ref="formRef" layout="vertical" :rules="Rules" :model="Form">
          <a-form-item field="content" label="更新内容：" validate-trigger="change">
            <div id="ckeditor" style="width: 100%;padding-bottom:8px;">
              <ckeditor
                  ref="editorRef"
                  v-model="Form.content"
                  :editor="editor"
                  :config="editorConfig"
                  placeholder="输入文本内容"
              ></ckeditor>
            </div>
          </a-form-item>
          <a-form-item field="logDate" label="更新日期：" validate-trigger="blur">
            <a-date-picker v-model:model-value="Form.logDate" :default-value="dayjs().format('YYYY-MM-DD')" format="YYYY-MM-DD" type="date" placeholder="请选择更新时间" :disabled="!!Form.id"/>
          </a-form-item>
          <a-form-item field="logType" label="更新类型：" validate-trigger="change">
            <a-select v-model:model-value="Form.logType" placeholder="请选择更新类型" :disabled="!!Form.id">
              <a-option v-for="item in logTypeList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
            </a-select>
          </a-form-item>

          <a-form-item field="appVersion" label="更新版本：" validate-trigger="blur">
            <a-input v-model:model-value="Form.appVersion" placeholder="请输入更新版本" :disabled="!!Form.id"/>
          </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="pushLog">
                保存
            </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.empty{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.action-content{
  display: flex;
}
.pageCss {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .head {
    display: flex;
    min-height: 50px;
    align-items: center;
    justify-content: space-between;

    .button-add {
      color: var(--tant-white-white-100);
      background-color: var(--tant-primary-color-primary-default);
      border: none;
      border-radius: 5px;
      cursor: pointer;
      margin-left: 12px;
      &:hover {
        background-color: var(--tant-primary-color-primary-hover);
      }
    }

    .content-modal {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

  }

  .body {
    height: 70vh;
    overflow-y: auto;
    width: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);

    .tab {
      display: flex;
      flex-direction: row;
    }

    .group-header {
      margin-left: 10px;
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 20px;
      cursor: pointer;
      .img {
        display: flex;
        align-items: center;

        .transform90 {
          transition: transform 0.3s ease;
          transform: rotate(90deg);
        }
      }

      img {
        transition: transform 0.3s ease;
      }

    }

  }
}

.button {
  border: none;
  border-radius: 5px;
  background-color: transparent;
}

.delete-btn {
  border-radius: 5px;

  &:hover {
    background-color: var(--tant-red-red-20);

  }
}

:deep(.arco-btn-size-mini) {
  height: auto;
  padding: 5px !important;
}

:deep(.arco-btn-group .arco-btn-status-danger:not(:last-child)) {
  border-right: none;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>