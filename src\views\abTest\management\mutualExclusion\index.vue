

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <a-input v-model="queryParams.name" class="title-input" placeholder="请输入互斥域分组回车搜索" @change="init">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            新建互斥域分组
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
        :columns="columns"
        :loading="loading"
        :data="tableData"
        :bordered="false"
        :hoverable="true"
        sticky-header
        :table-layout-fixed="true"
        :filter-icon-align-left="true"
        :column-resizable="true"
        :scroll="scroll"
        :pagination="false"
      >
        <template #flowDomains="{ record }">
          <a-tag v-for="item in record.flowDomains" :key="item.code" style="margin-right:5px;">
            {{item.name }}({{ item.flowPercentage }}%)
          </a-tag>
        </template>
        <template #action="{ record }">
          <a-dropdown position="bl" :hide-on-select="false">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption value="edit" @click="editIndex(record)">
                <icon-edit class="mr8"/>
                编辑
              </a-doption>
              <!-- <a-doption value="data">
                <icon-copy class="mr8"/>
                复制
              </a-doption> -->
              <a-popconfirm type="error" :content="`确认删除“${record.name}”?`" @ok="deleteItem(record)">
                <a-doption value="delete">
                  <icon-delete class="mr8"/>
                  删除
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange"/>
      </div>
    </div>
    <handleDrawer ref="handleRefs" @update-data="init"/>
  </div>

</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import selectApp from "@/components/selected-game-app/index.vue"
import {getDomainGroupList,deleteDomainGroupItem} from "@/api/ab/api";
import {useEventBus} from '@vueuse/core';
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from 'vue-router';
import handleDrawer from "./components/handleDrawer.vue";

const route = useRoute();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const queryParams = reactive({
  name:'',
  current: 1,
  pageSize: 10,
})
const total = ref(0)
// 模拟table数据
const loading = ref(false)

const tableData = ref<any>([])
const columns = ref<any>([
  {
    title: '互斥域名称',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '流量分配',
    dataIndex: 'flowDomains',
    slotName:'flowDomains',
    ellipsis: true,
    tooltip: true,
    minWidth: 240,
  },
  {
    title: '上级互斥域',
    dataIndex: 'parentName',
    ellipsis: true,
    slotName:'parentName',
    minWidth: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    tooltip: true,
    minWidth: 180,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    slotName: 'action',
  }
])

// Table页面滑动
const scroll = {
  y: 'calc(100vh - 256px)'
};
const init = () => {
  loading.value = true
  try {
    getDomainGroupList(queryParams).then(res => {
      if (res) {
        tableData.value = res.items
        total.value = res.total
      }
    }).catch((e) => {
      console.error('获取实验列表失败', e)
    }).finally(() => {
      loading.value = false
    })
  } catch (e) {
    loading.value = false
    console.error('获取实验列表异常', e)
  }
}
init()

const pageChange = (v) => {
  queryParams.current = v
  init()
}
const handleRefs = ref()
const createIndex = () => {
  handleRefs.value.openModal()
}
const editIndex = (record) => {
  handleRefs.value.openModal(record)
}
const deleteItem = async (record:any) => {
  try {
    await deleteDomainGroupItem(record.code);
    Message.success('删除成功');
    init();
  } catch (e) {
    Message.error('删除失败');
  }
}


localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
   init()
  }
})
</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}
.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
</style>