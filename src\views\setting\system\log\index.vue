<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          日期范围：
          <a-range-picker
              v-model:model-value="date"
              :allow-clear="false"
              shortcuts-position="left"
              :disabled-date="disableDate"
              style="width: 240px; background-color: var(--tant-bg-white-color-bg1-1);border:1px solid var(--tant-border-color-border1-1) "
              :shortcuts="[
              {
                label: '过去一周',
                value: () => [dayjs().subtract(1, 'week'), dayjs()],
              },
              {
                label: '过去两周',
                value: () => [dayjs().subtract(2, 'week'), dayjs()],
              },
              {
                label: '过去一月',
                value: () => [dayjs().subtract(1, 'month'), dayjs()],
              }]"
              @select="onDateSelect"
          />
        </div>
        <div class="filter-item">
          来源系统：
          <a-select v-model:model-value="selectedSource" :style="{width:'120px'}">
            <a-option>GrowthX</a-option>
            <a-option>问数</a-option>
            <a-option>gm</a-option>
            <a-option>lisgame</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          日志等级：
          <a-select v-model:model-value="selectedLevel" :style="{width:'120px'}" allow-clear>
            <a-option>INFO</a-option>
            <a-option>WARNING</a-option>
            <a-option>ERROR</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          检索内容：
          <a-input
              v-model:model-value="searchText"
              :style="{width:'240px'}"
              placeholder="请输入需要检索的文本"
              @press-enter="searchLog"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchLog">搜索</a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="pagination"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          @page-change="pageChange"
      >
        <template #pagination-left>
          <div class="pagination-left">
            共{{ pagination.total }}条记录
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getSystemAccessLog} from "@/api/setting/api";
import {formatTimestamp} from "@/utils/dateUtil";
import dayjs from "dayjs";
import {useRoute} from "vue-router";

const route = useRoute();
const columns = ref<any>([
  {
    title: '访问时间',
    dataIndex: 'accessTime',
    width: 170,
    render: (data) => {
      const {record} = data;
      return formatTimestamp(record.accessTime)
    },
    fixed: 'left',
  },
  {
    title: '日志等级',
    dataIndex: 'logLevel',
    width: 98,
    fixed: 'left',
  },
  {
    title: '用户',
    dataIndex: 'userName',
    width: 160,
    fixed: 'left',
  },
  {
    title: '地区',
    dataIndex: 'region',
    width: 88,
  },
  {
    title: 'IP',
    dataIndex: 'ipAddress',
    width: 128,
  },
  {
    title: '请求地址',
    dataIndex: 'apiRoute',
    ellipsis: true,
    tooltip: true,
    width: 300,
  },
  {
    title: '请求头',
    dataIndex: 'fullHeader',
    ellipsis: true,
    tooltip: true,
    width: 300,
  },
  {
    title: '请求参数',
    dataIndex: 'apiParams',
    ellipsis: true,
    tooltip: true,
    width: 300,
  },
  {
    title: '客户端',
    dataIndex: 'userAgent',
    ellipsis: true,
    tooltip: true,
    width: 300,
  },
  {
    title: '响应数据',
    dataIndex: 'response',
    ellipsis: true,
    tooltip: true,
    width: 300,
    render: (data) => {
      const {record} = data;
      return JSON.stringify(record.response)
    }
  },
  {
    title: '备注',
    dataIndex: 'notes',
    ellipsis: true,
    tooltip: true,
    width: 200,
  }
]);
const data = ref<any>([]);
const today = new Date().toISOString().split("T")[0];
const date = ref<string[]>([today, today]);

const loading = ref<boolean>(true);
const paramsCache = ref<boolean>({});
const dateLimit = ref<Date | undefined>();
const selectedSource = ref<string>('GrowthX');
const selectedLevel = ref<string>('INFO');
const searchText = ref<string>();
const pagination = ref<any>({
  defaultPageSize: 20,
  current: 1,
  total: 1,
  showPageSize:true,
  showJumper: true,
  autoAdjust: true,
});

const refresh = (pageParams) => {
  loading.value = true
  paramsCache.value = pageParams
  getSystemAccessLog(pageParams).then(res => {
    data.value = res.items
    pagination.value.total = res.total
    pagination.value.current = res.current
  }).finally(() => {
    loading.value = false
  })
}

const pageChange = (v: number) => {
  pagination.value.current = v
  refresh({
    ...paramsCache.value,
    current: v
  })
}

onMounted(() => {
  refresh({
    pageSize: pagination.value?.pageSize || 20,
    current: 1,
    source: selectedSource.value,
    logLevel: selectedLevel.value,
    startDate: date.value[0],
    endDate: date.value[1]
  })
})

const searchLog = () => {
  const params = {
    pageSize: pagination.value?.pageSize || 20,
    current: 1,
    source: selectedSource.value,
    logLevel: selectedLevel.value,
    startDate: date.value[0],
    endDate: date.value[1],
    text: searchText.value
  };
  refresh(params)
}

const onDateSelect = (value) => {
  if (value.length > 1) {
    dateLimit.value = undefined
    return
  }
  dateLimit.value = value[0]
}

const disableDate = (current: Date) => {
  if (dateLimit.value) {
    const maxDate = new Date(dateLimit.value)
    const minDate = new Date(dateLimit.value)
    minDate.setDate(new Date(dateLimit.value).getDate() - 30)
    maxDate.setDate(new Date(dateLimit.value).getDate() + 30)
    return current.getTime() > maxDate.getTime() || current.getTime() < minDate.getTime() || current.getTime() > new Date().getTime()
  }
  return current.getTime() > new Date().getTime()
}
</script>

<style scoped lang="less">

</style>