<template>
    <!-- 修改正文 -->
    <a-modal v-model:visible="modalVisible" :width="550" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert>对于多语言广告，会取以下正文的第一条来修改默认语言的正文。不支持帖子广告修改正文。</a-alert>
        <a-form ref="formRef" :model="form" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }" style="margin-top: 12px;width: 100%;">
            <a-form-item field="textList">
                <div class="text-list">
                    <div v-for="(item,index) in form.textList" :key="index" class="item">
                        <a-textarea v-model="item.text" :auto-size="{ minRows: 2, maxRows: 5 }" placeholder="请输入正文"/>
                        <div v-show="form.textList.length > 1" class="delete-wrapper">
                            <icon-delete @click="removeItem(index)"/>
                        </div>
                    </div>
                    <div v-if="form.textList.length < 5" class="add-wrapper" @click="addItem">添加</div>
                </div>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" :disabled="saveDisabled" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref,computed} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改正文')
const form = reactive({
    textList:[
        {
            text:''
        }
    ],
})
const formRef = ref()

const saveDisabled = computed(() => {
    return !form.textList.some(item => item.text && item.text.trim() !== '');
})
const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const removeItem = (index) => {
    form.textList.splice(index, 1)
}
const addItem = () => {
    form.textList.push({
        text:''
    })
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.text-list{
    width: 100%;
    .item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .delete-wrapper{
            flex-shrink: 0;
            margin-left: 10px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }
        &:hover{
            .delete-wrapper{
                opacity: 1;
            }
        }
    }
    .add-wrapper{
        color: rgb(var(--primary-6));
        cursor: pointer;
    }
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>