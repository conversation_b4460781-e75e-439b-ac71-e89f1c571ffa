<script setup lang="ts">
import {cloneDeep} from "lodash";
import {ref} from "vue";
import * as XLSX from 'xlsx';

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  isChartType:string
}

const props = defineProps<Props>()

const columns = ref<any>([
  {
    title:'日期',
    dataIndex:'title',
    slotName:'title',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'用户数',
    dataIndex:'userNum',
    slotName:'userNum',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'间隔数',
    dataIndex:'stepNum',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  }
]);
const tableData = ref<any>([
  {
    title:'合计',
    userNum:1742,
    stepNum:2118,
  },
  {
    title:'2024-09-16',
    userNum:12911,
    stepNum:9956,
  }
]);

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])

const checkData = ref<any>([])
const indeterminate = ref(false)
const checkedAll = ref(false)
const groupData = ref<any>([
  {
    value:'max',
    label:'最大值'
  },
  {
    value:'pct_up_4',
    label:'上四分位'
  },
  {
    value:'median',
    label:'中位数'
  },
  {
    value:'pct_down_4',
    label:'下四分位'
  },
  {
    value:'min',
    label:'最小值'
  },
  {
    value:'avg',
    label:'平均值'
  },
  {
    value:'pct_99',
    label:'99分位数'
  },
  {
    value:'pct_95',
    label:'95分位数'
  },
  {
    value:'pct_90',
    label:'90分位数'
  },
  {
    value:'pct_80',
    label:'80分位数'
  },
  {
    value:'pct_70',
    label:'70分位数'
  },
  {
    value:'pct_60',
    label:'60分位数'
  },
  {
    value:'pct_40',
    label:'40分位数'
  },
  {
    value:'pct_30',
    label:'30分位数'
  },
  {
    value:'pct_20',
    label:'20分位数'
  },
  {
    value:'pct_10',
    label:'10分位数'
  },
  {
    value:'pct_5',
    label:'5分位数'
  },
])


const handleColumns = () => {
  columns.value = [
    {
      title:'日期',
      dataIndex:'title',
      slotName:'title',
      minWidth:140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
      title:'用户数',
      dataIndex:'userNum',
      slotName:'userNum',
      minWidth:140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
      title:'间隔数',
      dataIndex:'stepNum',
      minWidth:140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    }
  ]
  if(checkData.value.length){
    checkData.value.forEach(item => {
      columns.value.push({
        title:item.label,
        dataIndex:item.value,
        slotName:item.value,
        sortable: { sortDirections: ['ascend', 'descend'] },
        minWidth:140
      })
    })
  }
}
// 全选
const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    checkData.value = groupData.value
  } else {
    checkedAll.value = false;
    checkData.value = []
  }
  handleColumns()
}
const handleChange = (values) => {
  if (values.length === groupData.value.length) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
  handleColumns()
}
const calculateSum = (array:any) => {
  return array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0);

};

// 判断周几
const getDayOfWeek = (dateString) => {
  const date = new Date(dateString);
  const days = ['日', '一', '二', '三', '四', '五', '六'];
  return `${dateString}(${days[date.getDay()]})`;
}
const freshData = () => {
  // const data = props.eventData.y.flatMap(item =>
  //   item.y_data.map(el => ({
  //     name: item.display_name + el.group.join(','),
  //     values: el.values
  //   }))
  // );
  // const columnsDate = props.eventData.x;
  // columns.value = [
  //     {
  //       title: '总体',
  //       dataIndex: 'name',
  //       fixed: 'left',
  //       minWidth: 140,
  //       sortable: { sortDirections: ['ascend', 'descend'] }
  //     },
  //     {
  //       title: '转化均值',
  //       dataIndex: 'sum',
  //       width: 140,
  //       sortable: { sortDirections: ['ascend', 'descend'] }
  //     },
  //     ...columnsDate.map((item, index) => ({
  //       title: getDayOfWeek(item),
  //       dataIndex: `date-${index + 1}`,
  //       width: 140,
  //       sortable: { sortDirections: ['ascend', 'descend'] }
  //     }))
  //   ];

  //   tableData.value = data.map(item => ({
  //     name: item.name,
  //     sum: calculateSum(item.values),
  //     ...columnsDate.reduce((acc, _, index) => {
  //       acc[`date-${index + 1}`] = Number(item.values[index]);
  //       return acc;
  //     }, {})
  //   }));
  
  copyColumns.value = cloneDeep(columns.value)
  copyTableData.value = cloneDeep(tableData.value)
};


freshData()

// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `间隔分析.xlsx`);
};
const createVisible = ref(false)
const detailVisible = ref(false)
const form = ref({
  name: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  createVisible.value = false;
}
// 保存报表
const saveReport = async () => {
}
defineExpose({
  freshData,
  exportXlsx
})
</script>

<template>
  <div class="chart-content" style="margin-bottom: 24px;">
    <div style="display: flex;justify-content: flex-end;margin-bottom: 24px;">
      <div v-if="props.isChartType === 'hxTrend'">
        <a-dropdown :hide-on-select="false">
          <div style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;">指标（{{checkData.length}}/{{groupData.length}}）<icon-down/></div>
          <template #content>
              <a-checkbox-group v-model="checkData" direction="vertical" @change="handleChange">
                  <a-checkbox v-for="(item,index) in groupData" :key="index" :value="item">
                    <a-doption style="width: 120px;">{{ item.label }}</a-doption>
                  </a-checkbox>
              </a-checkbox-group>
          </template>
          <template #footer>
            <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);">
              <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" @change="handleChangeAll" style="width: 100%;">全选</a-checkbox>
            </div>
          </template>
        </a-dropdown>
      </div>
      <div>
        <a-button style="margin-left: 12px;" @click="exportXlsx">导出</a-button>
      </div>
    </div>
    <a-spin style="width: 100%;height: 100%">
      <a-table :columns="columns" :data="tableData" :pagination="false" size="small" column-resizable :bordered="{cell:true}" >
        <template #title="{ record }">
          <icon-plus class="record-icon" @click="() => detailVisible = true"/>
          <span>{{record.title}}</span>
        </template>
        <template #userNum="{ record }">
          <div class="hover-box">
            <span>{{record.userNum}}</span>
            <svg class="add-group" @click="() => createVisible = true" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-4 0a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z"></path><path d="M1 10a2 2 0 012-2h3a2 2 0 012 2v3H7v-3a1 1 0 00-1-1H3a1 1 0 00-1 1v3H1v-3z"></path><path d="M13 10h-1V8h-2V7h2V5h1v2h2v1h-2v2z"></path></svg></svg>
          </div>
        </template>
      </a-table>
    </a-spin>
    <!-- 创建弹窗 -->
    <a-modal v-model:visible="createVisible" :on-before-ok="validateForm" width="480px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">
          创建结果分群
          <a-tooltip content="选择计算结果保存为分群，可用于进一步下钻分析。" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <div class="form-label">分群显示名</div>
        <a-form-item field="name" :hide-label="true" :hide-asterisk="true" required :rules="[{ required: true, message: '分群显示名不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <div class="form-label">分群备注(选填)</div>
        <a-form-item field="description" :hide-label="true">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:3, maxRows:10}" :show-word-limit="true" :max-length="200"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="boe-foot">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" @click="saveReport">创建</a-button>
        </div>
      </template>
    </a-modal>
    <!-- 分组详情 -->
    <a-modal v-model:visible="detailVisible" width="80%" :footer="false">
      <template #title>
        <div class="modal-title">2024-09-16(一)至2024-09-22(日)分组详情</div>
      </template>
      <a-table :columns="columns" :data="tableData" :pagination="false" size="small" column-resizable :bordered="{cell:true}" >
      </a-table>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.table-title {
    width: 100%;
    color: var(--tant-text-gray-color-text1-1);
    font-size: 14px;
    line-height: 24px;
    text-align: center;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
:deep(.arco-checkbox){
  padding: 0 12px;
  &:hover{
    background-color: var(--color-fill-2);
  }
}
:deep(.arco-dropdown-option){
  padding: 0;
}
.record-icon{
  margin-right: 5px;
  cursor: pointer;
  &:hover{
    color: var(--tant-primary-color-primary-hover);
  }
}
.hover-box{
  display: flex;
  align-items: center;
  &:hover{
    .add-group{
      opacity: 1;
    }
  }
  .add-group{
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;
    &:hover{
      color: #8d0088;
    }
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
.form-label{
  line-height: 32px;
  font-weight: 500;
}
</style>