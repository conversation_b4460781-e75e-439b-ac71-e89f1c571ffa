<template>
    <!-- 修改标题 -->
    <a-modal v-model:visible="modalVisible" :width="550" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert>对于多语言广告，会取以下标题的第一条修改默认语言标题。不支持帖子广告、转化发生位置为Facebook主页的广告或非动态素材轮播广告修改标题，系统会帮您自动过滤。</a-alert>
        <a-form ref="formRef" :model="form" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }" style="margin-top: 12px;width: 100%;">
            <a-form-item field="titleList">
                <div class="title-list">
                    <div v-for="(item,index) in form.titleList" :key="index" class="item">
                        <a-textarea v-model="item.title" :auto-size="{ minRows: 2, maxRows: 5 }" placeholder="请输入标题"/>
                        <div v-show="form.titleList.length > 1" class="delete-wrapper">
                            <icon-delete @click="removeItem(index)"/>
                        </div>
                    </div>
                    <div v-if="form.titleList.length < 5" class="add-wrapper" @click="addItem">添加</div>
                </div>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" :disabled="saveDisabled" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref,computed} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改标题')
const form = reactive({
    titleList:[
        {
            title:''
        }
    ],
})
const formRef = ref()

const saveDisabled = computed(() => {
    return !form.titleList.some(item => item.title && item.title.trim() !== '');
})
const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const removeItem = (index) => {
    form.titleList.splice(index, 1)
}
const addItem = () => {
    form.titleList.push({
        title:''
    })
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.title-list{
    width: 100%;
    .item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
        .delete-wrapper{
            flex-shrink: 0;
            margin-left: 10px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }
        &:hover{
            .delete-wrapper{
                opacity: 1;
            }
        }
    }
    .add-wrapper{
        color: rgb(var(--primary-6));
        cursor: pointer;
    }
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>