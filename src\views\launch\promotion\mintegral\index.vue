<template>
    <div class="page">
      <div class="page-head">
        <div class="title">
          {{ route.meta.locale }}
        </div>
        <div class="filter">
          <div class="filter-item">
            <DateRangePicker v-model="dateTime" />
          </div>
          <div class="filter-item">
              <a-button type="primary" @click="createAd">创建广告</a-button>
          </div>
        </div>
      </div>
      <div class="page-body">
        <a-tabs v-model:active-key="activeName" @change="handleTabChange">
            <a-tab-pane key="account" title="广告账户">
                <Account ref="accountRef" @change-tabs="changeTabs"/>
            </a-tab-pane>
            <a-tab-pane key="ad" title="广告">
                <Ad ref="adRef"/>
            </a-tab-pane>
            <a-tab-pane key="adUnit" title="广告单元">
                <AdUnit ref="adUnitRef"/>
            </a-tab-pane>
            <a-tab-pane key="creative" title="创意">
                <Creative ref="creativeRef"/>
            </a-tab-pane>
            <a-tab-pane key="subChannel" title="子渠道">
                <SubChannel ref="subChannelRef"/>
            </a-tab-pane>
            <a-tab-pane key="material" title="素材">
                <Material/>
            </a-tab-pane>
        </a-tabs>
      </div>
      <CreateAd ref="createAdRef"/>
    </div>
  
  </template>
  
  <script setup lang="ts">
  import { reactive, ref,provide, onMounted} from "vue";
  import dayjs from 'dayjs';
  import DateRangePicker from "@/components/date-quick-picker/index.vue";
  import CreateAd from "@/views/launch/promotion/components/CreateAd.vue";
  import {useRoute} from 'vue-router';
  import Account from "./pages/account.vue";
  import Ad from "./pages/ad.vue";
  import AdUnit from "./pages/adUnit.vue";
  import Material from "./pages/material/index.vue";
  import Creative from "./pages/creative.vue";
  import SubChannel from "./pages/subChannel.vue";
  
  const route = useRoute();
  const today = dayjs();
  const dateTime = ref<Date[]>([today.toDate(), today.toDate()])
  const activeName = ref('ad')
  provide('dateTime', dateTime);
 
  const accountRef = ref()
  const adRef = ref()
  const adUnitRef = ref()
  const creativeRef = ref()
  const subChannelRef = ref()

  
  // 创建refs映射对象
  const refsMap = {
    account: accountRef,
    ad: adRef,
    adUnit: adUnitRef,
    creative: creativeRef,
    subChannel: subChannelRef,
  }
  // 处理tab切换
  const handleTabChange = (key) => {
    const targetRef = refsMap[key]
    targetRef.value?.init()
  }

  const changeTabs = (val) => {
    activeName.value = val.tab
    // 获取对应的ref
    const targetRef = refsMap[val.tab]
    // 如果ref存在且有refresh方法，则调用
    if (targetRef?.value?.init) {
      targetRef.value.init(val)
    }
  }
  const createAdRef = ref()
  const createAd = () => {
    createAdRef.value.openModal()
  }
  const init = () => { 
    adRef.value.init()
  }
  onMounted(() => {
    init()
  })
  </script>
  
  <style scoped lang="less">
  </style>
