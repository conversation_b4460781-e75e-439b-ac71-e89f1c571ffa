<script setup lang="ts">
import {computed} from "vue";
import useChartOption from "@/hooks/chart-option";

interface Props {
  chartData: any
}

const props = defineProps<Props>()

// 计算动态图例数据
const legendData = computed(() => {
  const legends = [] as any;
  // 根据groups生成图例数据
  if (props.chartData?.groups) {
    props.chartData.groups.forEach((group) => {
      const groupName = group.join('-');
      legends.push(groupName);
    });
  }
  
  return legends;
});

// 计算series数据
const seriesData = computed(() => {
  const series = [] as any;
  
  // 根据groups生成series数据，使用total数据
  if (props.chartData?.groups && props.chartData?.y) {
    props.chartData.groups.forEach((group, groupIndex) => {
      const groupName = group.join('-');
      
      // 查找对应的y数据项
      const matchingYData = props.chartData.y.find(yItem => 
        JSON.stringify(yItem.group) === JSON.stringify(group)
      );
      
      if (matchingYData) {
        const seriesItem: any = {
          type: 'line',
          name: groupName,
          data: matchingYData.total || []
        };
        series.push(seriesItem);
      }
    });
  }
  
  return series;
});

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '0',
      top: '10',
      bottom: '48'
    },
    legend: {
      data: legendData.value,
      bottom: '0',
      type: 'scroll'
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      className: 'echarts-tooltip-diy',
      extraCssText: 'max-height: 300px; overflow-y: auto;',
      formatter: (params) => {
        // 计算当前时间点的总值
        const totalValue = params.reduce((sum, item) => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          return sum + value;
        }, 0);
        
        const paramsData = params.map(item => ({
          seriesName: item.seriesName,
          value: item.value,
          color: item.color,
          dataIndex: item.dataIndex
        }));
        // 按值从大到小排序
        paramsData.sort((a, b) => {
          const valueA = typeof a.value === 'number' ? a.value : parseFloat(a.value) || 0;
          const valueB = typeof b.value === 'number' ? b.value : parseFloat(b.value) || 0;
          return valueB - valueA;
        });
        let tooltipContent = params[0].name;
        paramsData.forEach(item => {
          const value = typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0;
          const percentage = totalValue > 0 ? ((value / totalValue) * 100).toFixed(2) : 0;
          const formattedValue = item.value;
          tooltipContent += `<br><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span> ${item.seriesName}: ${formattedValue} <span style="padding-left:8px;color:${item.color}">(${percentage}%)</span>`;
        });
        return tooltipContent;
      }
    },
    xAxis: {
      type: 'category',
      data: props.chartData?.x || [],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`;
          }
          if (value >= 1000) {
            return `${value / 1000}k`;
          }
          return value;
        }
      }
    },
    series: seriesData.value
  };
});


</script>

<template>
  <Chart :option="chartOption"/>
</template>

<style scoped lang="less">

</style>
