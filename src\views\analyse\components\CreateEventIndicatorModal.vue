<script setup lang="ts">

import {getNumberDisplayType} from "@/api/enum";
import {Message} from "@arco-design/web-vue";
import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import EventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import {saveIndicatorData} from "@/api/setting/api";
import {ref} from "vue";
import {cloneDeep} from "lodash";
import {useSessionStorage} from "@vueuse/core";

const visible = defineModel<boolean>("visible");
const appId = useSessionStorage('app-id', '')?.value;
const appIds = useSessionStorage('app-id-list', [])?.value;
const props = defineProps({
  indicator: {
    type: Object
  },
  // 隐藏计算公式
  calculateFormulaHidden: {
    type: Object
  },
  subjectCode: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['confirm', 'cancel','editIndicator'])
// 指标创建表单引用
const createIndicatorFormRef = ref(null)
// 指标创建表单值
const fromData = ref(cloneDeep(props.indicator))
// 公共指标
const inApp = ref<number>(props.indicator.type === 'operation' ? 0 : 1);
// 指标创建表单值校验
const createIndicatorFormRules = {
  name: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入指标名')
          }
          if (value && value.length > 80) {
            cb('指标名不能超过 80 个字符');
          }
          if (!(/^[a-z][a-z0-9_]{0,79}$/).test(value)) {
            cb('小写字母开头，可含小写字母/数字/下划线，不超过 80 个字符')
          }
          resolve(value)
        })
      }
    },
  ],
  displayName: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入显示名')
          }
          if (value && value.length > 80) {
            cb('显示名不能超过 80 个字符');
          }
          resolve(value);
        });
      }
    },
  ]
}

/**
 * 取消
 */
const handleIndicatorCreateCancel = () => {
  emits('cancel')
  visible.value = false
}

/**
 * 确认
 */
const handleIndicatorCreateConfirm = () => {
  
  if (createIndicatorFormRef.value) {
    createIndicatorFormRef.value.validate(async (errors: any) => {
      if (!errors) {
        const params = {
          // name: fromData.value.name,
          displayName: fromData.value.displayName,
          description: fromData.value.description,
          type: props.indicator.type,
          filter: props.indicator.filter,
          formula: props.indicator.formula,
          eventList: props.indicator.eventList,
          displayType: props.indicator.displayType,
          inApp: inApp.value,
          isBasic:props.indicator?.formula === 'A',
          subjectCode:props.indicator?.type === 'operation' ? props?.subjectCode : undefined
        }
        const res = await saveIndicatorData(params)
        if (res) {
          emits('confirm')
          Message.success('创建成功！')
        }else{
          Message.error('创建失败!')
        }
        visible.value = false
      }
    });
  }
}

const editIndicator = () => {
  emits('editIndicator')
  visible.value = false
}
</script>

<template>
  <a-modal v-model:visible="visible" width="520px" @cancel="handleIndicatorCreateCancel">
    <template #title>
      <div class="modal-title">创建指标</div>
    </template>
    <a-form ref="createIndicatorFormRef" :model="fromData" :rules="createIndicatorFormRules" layout="vertical">
      <a-form-item field="displayName" label="显示名">
        <a-input v-model="fromData.displayName" placeholder="80字以内" :max-length="80"/>
      </a-form-item>
      <!-- <a-form-item field="name" label="指标名">
        <a-input v-model="fromData.name" placeholder="80字以内" :max-length="80"/>
      </a-form-item> -->
      <a-form-item label="指标归属">
        <a-radio-group :model-value="inApp" @change="value =>  inApp = value">
          <a-radio :value="0">
            公共
          </a-radio>
          <a-radio v-if="props.indicator.type && props.indicator.type !== 'operation'" :value="1">
            应用 {{ appId }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="description" label="备注">
        <a-textarea v-model="fromData.description" placeholder="请输入" :max-length="200" allow-clear show-word-limit style="height: 100px;"/>
      </a-form-item>
    </a-form>
    <div v-if="!calculateFormulaHidden" class="calculate-formula">
      <div class="head">
        <div class="title">计算方式<span class="edit-btn" @click="editIndicator">编辑</span></div>
        <div class="note">展示格式：{{ getNumberDisplayType(fromData.displayType) }}</div>
      </div>
      <div class="indicator-content">
        <custom-indicator-display :indicator="fromData"/>
        <event-query-filter v-if="fromData.filter?.filters?.length>0" :filter="fromData.filter" :disabled="true" :show-detail-filter="true" :code-list="fromData.eventList"/>
      </div>
    </div>
    <template #footer>
      <div class="boe-foot">
        <a-button class="cancel" @click.stop="handleIndicatorCreateCancel">取消</a-button>
        <a-button type="primary" @click="handleIndicatorCreateConfirm">保存</a-button>
      </div>
    </template>
  </a-modal>

</template>

<style scoped lang="less">

.calculate-formula {
  .head {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0;
    line-height: 1.5715;
    white-space: normal;

    .title {
      font-weight: 500;
      max-width: 100%;
      color: var(--color-text-2);
      font-size: 14px;
      white-space: normal;

      .edit-btn {
        color: rgb(var(--primary-6));
        font-size: 12px;
        padding: 0 8px;
        cursor: pointer;
      }
    }

    .note {
      padding-left: 8px;
      color: var(--color-gray-blue-7);
      font-size: 12px;
    }
  }

  .indicator-content {
    padding: 4px 12px;
    background-color: var(--tant-fill-color-fill1-2);
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}
</style>