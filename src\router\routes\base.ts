import type {RouteRecordRaw} from 'vue-router';
import {REDIRECT_ROUTE_NAME, ROUTE_NAME} from '@/router/constants';

export const DEFAULT_LAYOUT = () => import('@/layout/default-layout.vue');
export const DASHBOARD_LAYOUT = () => import('@/layout/dashboard-layout.vue');
export const EMPTY_LAYOUT = () => import('@/layout/empty-layout.vue');
export const METADATA_LAYOUT=()=> import('@/layout/metadata-layout.vue')

export const REDIRECT_MAIN: RouteRecordRaw = {
  path: '/redirect',
  name: 'redirectWrapper',
  component: DEFAULT_LAYOUT,
  meta: {
    requiresAuth: true,
    hideInMenu: true,
  },
  children: [
    {
      path: '/redirect/:path',
      name: REDIRECT_ROUTE_NAME,
      component: () => import('@/views/redirect/index.vue'),
      meta: {
        requiresAuth: true,
        hideInMenu: true,
      },
    },
  ],
};

export const NOT_FOUND_ROUTE: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  name: ROUTE_NAME.NOT_FOUND,
  meta: {
    locale: 'menu.error.notFound',
    requiresAuth: true,
  },
  component: () => import('@/views/not-found/index.vue'),
};
