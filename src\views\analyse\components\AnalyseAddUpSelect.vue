<script setup lang="ts">

// 使用 v-model 进行数据绑定
const isAddUp = defineModel<boolean>("value");
const emits = defineEmits(['select'])

/**
 * 选中事件
 */
const select = (value) => {
  isAddUp.value = value;
  emits('select', value)
}
</script>

<template>
  <a-dropdown @select="select">
    <div class="select-btn">
      <span class="btn-label">{{ isAddUp ? '累加' : '当天' }}</span>
    </div>
    <template #content>
      <a-doption :value="false">当天</a-doption>
      <a-doption :value="true">累加</a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">
</style>