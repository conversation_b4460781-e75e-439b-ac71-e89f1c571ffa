<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%;">
            <a-form-item field="profitTarget" label="利润目标">
                <a-input-number v-model="form.profitTarget" :min="0"/>
            </a-form-item>
            <a-form-item field="appId" label="应用">
                <a-select
                v-model:model-value="form.appId"
                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                :filter-option="false"
                 :show-extra-options="false"
                 placeholder="选择应用"
                 allow-search
                 allow-clear
                 @search="handleSearch"
                 @clear="handleClear"
                 @dropdown-reach-bottom="handleReachBottom"
                >
                    <a-option v-for="item in appPageList" :key="item.code" :value="item.code" :disabled="disabledAppIds.has(item.code)">
                        {{ item.code }}-{{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, inject, reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {getAppPageList} from "@/api/marketing/api";
import {saveProfitTarget} from "@/api/analyse/api";

const modalVisible = ref(false)
const modalTitle = ref('添加应用')
const props = defineProps({
    tableData: {
        type: Array,
        default: () => ([])
    },
})
// 添加计算属性来处理禁用逻辑
const disabledAppIds = computed(() => {
    return new Set(props.tableData.map((item: any) => item.appId));
});
const form = reactive({
    profitTarget:undefined,
    appId:''
})
const pageParams = reactive({
    current: 1,
    pageSize: 10,
    text: '',
})

const rules = {
    profitTarget: [
        {
            required: true,
            message:'请填写利润目标',
        }
    ],
    appId: [
        {
            required: true,
            message:'请选择应用',
        }
    ],
}
const formRef = ref()
const appPageList = ref<any>([])
const emits = defineEmits(['updateData']);
const year = ref(inject('year'));

const appLoading = ref(false)
const getPageList = async () => {
    appLoading.value = true
    appPageList.value = []
    await getAppPageList(pageParams).then(res => {
        appPageList.value = res.items || []
    })
    appLoading.value = false
}
const openModal = async (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    pageParams.text = ''
    pageParams.current = 1
    pageParams.pageSize = 10
    await getPageList()
    modalVisible.value = true
}
const handleSearch = async (v) => {
  pageParams.text = v
  await getPageList()
}
const handleClear = async (v) => {
  pageParams.text = ''
  await getPageList()
}
const handleReachBottom = async () => {
  pageParams.pageSize += 10
  await getPageList()
}

const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            loading.value = true
            try {
                const data = {
                    label:'app',
                    code:form.appId,
                    year:year.value,
                    target:form.profitTarget
                }
                await saveProfitTarget(data)
                modalVisible.value = false;
                Message.success('保存成功');
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }finally {
                loading.value = false
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">

.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>