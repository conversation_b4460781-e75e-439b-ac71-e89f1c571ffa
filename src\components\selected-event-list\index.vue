<template>
  <a-select
    v-model:model-value="events"
    :loading="loading"
    multiple
    :style="{ width: '240px', backgroundColor: 'var(--tant-bg-white-color-bg1-1)' }"
    placeholder="选择事件"
    :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
    :max-tag-count="1"
    :scrollbar="true"
    :tag-nowrap="true"
    allow-search
    @change="eventChange"
    @remove="removeChange"
    @popup-visible-change="popupVisibleChange"
  >
    <a-option v-for="item in sortedEventList" :key="item.eventCode" :value="item.eventName"> {{ item.eventName }}-{{ item.eventDisplayName }} </a-option>
  </a-select>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { getEventList } from '@/api/setting/api';
  import { cloneDeep } from 'lodash';
  import { sortSelectedFirst } from '@/utils/sortUtil';
  import { useEventBus } from '@vueuse/core';
  import { LocalStorageEventBus } from '@/types/event-bus';

  const props = defineProps({
    defaultCodes: {
      type: Array,
      default: () => [],
    },
  });
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const loading = ref(false);
  const eventList = ref();
  const events = ref<any>([]);
  const sortedEventList = ref<any>([]);
  const emits = defineEmits(['eventsChange']);

  const eventChange = async () => {
    if (events.value.length) {
      // emits('eventsChange', events.value)
    }
  };

  const popupVisibleChange = (v) => {
    if (v) {
      sortedEventList.value = sortSelectedFirst(eventList.value, events.value);
    } else {
      emits('eventsChange', events.value);
    }
  };
  const removeChange = () => {
    emits('eventsChange', events.value);
  };
  const init = async () => {
    loading.value = true;
    const params = {
      inApp: 1,
    };
    await getEventList(params).then((res) => {
      eventList.value = res;
      // 过滤掉不在新事件列表中的已选事件
      if (events.value?.length) {
        const eventNames = eventList.value.map(item => item.eventName);
        events.value = events.value.filter(name => eventNames.includes(name));
      }
      sortedEventList.value = sortSelectedFirst(eventList.value, events.value);
    });
    loading.value = false;
  };
  init();
  localStorageEventBus.on(async (name, value) => {
    if (name === 'app-id'){
      await init();
      emits('eventsChange', events.value);
    }
  });
  watch(
    () => props.defaultCodes,
    (newValue, oldValue) => {
      events.value = cloneDeep(props.defaultCodes);
    },
    { immediate: true, deep: true }
  );
</script>

<style scoped lang="less"></style>
