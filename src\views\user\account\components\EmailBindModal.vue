<template>
  <a-modal :visible="visible" title="绑定邮箱" :footer="false" title-align="start" @cancel="handleCancel" @update:visible="updateVisible">
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="邮箱地址" field="email">
        <a-input v-model="form.email" placeholder="请输入邮箱地址" />
      </a-form-item>
      <!-- <a-form-item label="验证码" field="verifyCode">
        <a-input v-model="form.verifyCode" placeholder="请输入验证码" />
        <a-button :loading="sending" style="margin-left: 12px;" @click="sendVerifyCode">
          {{ sending ? '发送中...' : '获取验证码' }}
        </a-button>
      </a-form-item> -->
    </a-form>

    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleConfirm"> 立即绑定 </a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import type { FormInstance } from '@arco-design/web-vue';
  import { saveSystemMember } from '@/api/setting/api';

  interface Props {
    visible: boolean;
    code?: string;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'refresh'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const sending = ref(false);

  const form = reactive({
    email: '',
    // verifyCode: '',
  });

  const rules = {
    email: [
      { required: true, message: '请输入邮箱地址' },
      { type: 'email' as const, message: '请输入正确的邮箱地址' },
    ],
    // verifyCode: [
    //   { required: true, message: '请输入验证码' },
    //   { match: /^\d{6}$/, message: '验证码为6位数字' },
    // ],
  };

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        form.email = '';
        // form.verifyCode = '';
      } else {
        formRef.value?.resetFields();
      }
    }
  );

  const updateVisible = (value: boolean) => {
    emit('update:visible', value);
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const sendVerifyCode = async () => {
    const emailValid = await formRef.value?.validateField('email');
    if (emailValid) return;

    sending.value = true;
    try {
      await new Promise((resolve) => {
        setTimeout(() => resolve(undefined), 2000);
      });
      Message.success('验证码已发送');
    } catch (error) {
      Message.error('发送失败，请重试');
    } finally {
      sending.value = false;
    }
  };

  const handleConfirm = async () => {
    formRef.value?.validate(async (valid: any) => {
      if (!valid) {
        loading.value = true;
        try {
          await saveSystemMember({
            email: form.email,
            code: props.code,
          });
          Message.success('邮箱绑定成功');
          emit('refresh');
          updateVisible(false);
        } catch (error) {
          console.error('绑定失败，请重试');
        } finally {
          loading.value = false;
        }
      }
    });
  };
</script>

<style scoped lang="less">
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
