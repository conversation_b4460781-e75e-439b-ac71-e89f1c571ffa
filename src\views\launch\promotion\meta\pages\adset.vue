<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  v-model="filterParams.adsetIds"
                  placeholder="请选择广告组"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adsetOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdsetSearch"
                  @dropdown-reach-bottom="loadMoreAdsets"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告组-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adsetOptions" 
                    :key="item.adsetId" 
                    :value="item.adsetId" 
                    :label="item.adsetName">
                    {{ item.adsetName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.campaignIds"
                  placeholder="请选择广告系列"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="campaignOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleCampaignSearch"
                  @dropdown-reach-bottom="loadMoreCampaigns"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告系列-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in campaignOptions" 
                    :key="item.campaignId" 
                    :value="item.campaignId" 
                    :label="item.campaignName">
                    {{ item.campaignName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.accountIds"
                  placeholder="请选择广告账户"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="accountOptionsLoading"
                  :filter-option="false"
                  allow-search
                  :tag-nowrap="true"
                  @search="handleAccountSearch"
                  @dropdown-reach-bottom="loadMoreAccounts"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告账户-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in accountOptions" 
                    :key="item.accountId" 
                    :value="item.accountId" 
                    :label="item.accountName">
                    {{ item.accountName }}
                  </a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择创建"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>创建-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">不限</a-option>
                  <a-option value="2">是</a-option>
                  <a-option value="3">否</a-option>
              </a-select> -->
              <!-- <a-select
                  placeholder="请选择产品"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>产品-{{ data?.label }}</span>
                  </template>
              </a-select> -->
              <a-select
                  placeholder="请选择状态"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>状态-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">投放中</a-option>
                  <a-option value="2">已暂停</a-option>
                  <a-option value="3">已删除</a-option>
                  <a-option value="4">超预算</a-option>
                  <a-option value="5">审核中</a-option>
                  <a-option value="6">审核不通过</a-option>
                  <a-option value="7">其他状态</a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择渠道"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>渠道-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">Meta</a-option>
                  <a-option value="2">Mintegral</a-option>
              </a-select> -->
              <!-- <a-select
                  placeholder="请选择标签"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>标签-{{ data?.label }}</span>
                  </template>
              </a-select> -->
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption style="width: 136px;" @click="excelEdit">Excel编辑</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchOpen">开启</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchPause">暂停</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchCopy">复制广告组</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchTime">定时开关</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchChangeBid">修改出价</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="changeBidStrategy">修改竞价策略</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchReviseBudget">修改预算</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="linkAdBatch">查看广告</a-doption>
                    <a-doption :disabled="!selectedKeys.length">批量添加广告</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchDelete">删除</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchRelated">关联产品</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditName">修改名称</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditTag">编辑标签</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditBeneficiary">编辑受益方或赞助方</a-doption>
                </template>
            </a-dropdown>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-tooltip placement="top" content="可手动同步近3天(含今天)的数据，其他时段系统将定期自动同步">
                <a-button class="br4" style="margin-left: 12px;" @click="batchSync">
                    <template #icon>
                        <icon-sync />
                    </template>
                    <template #default>同步数据</template>
                </a-button>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="adsetId"
        >
          <template #effectiveStatus="{ record }">
            <a-switch v-model="record.effectiveStatus" checked-value="ACTIVE" unchecked-value="PAUSED" size="small" :before-change="() => statusChange(record)"/>
          </template>
          <template #actions="{record}">
            <div class="actions-content">
              <a class="actions-btn">详情</a>
              <a class="actions-btn">复制</a>
              <a class="actions-btn">添加广告</a>
              <a-dropdown>
                <a class="actions-btn">更多<icon-down style="font-size: 12px;"/></a>
                <template #content>
                  <a-doption>小时报表</a-doption>
                  <a-doption>广告日志</a-doption>
                  <a-doption>同步数据</a-doption>
                </template>
              </a-dropdown>
            </div>
          </template>
          <template #adsetName="{ record,rowIndex }">
            <div class="cell-content">
              <div class="title-box">
                <div class="text-link" @click="linkAd(record)">{{ record.adsetName }}</div>
                <a-trigger position="right" auto-fit-position :unmount-on-close="false" :popup-visible="record.isEditing">
                  <icon-edit class="icon" @click="showEdit(rowIndex)"/>
                  <template #content>
                    <div class="edit-title-box">
                      <a-input v-model="record.campaignName" placeholder="请输入广告组名称" maxlength="100" show-word-limit/>
                      <div class="edit-footer">
                        <a-button style="margin-right: 12px;" size="mini" @click="editCancel(rowIndex)">取消</a-button>
                        <a-button type="primary" size="mini" @click="editOk(rowIndex)">确定</a-button>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
              <div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </div>
          </template>
          <template #editLog="{ record }">
            <a-tooltip v-for="(item, index) in record.editLog" :key="index">
              <template #content>
                <div class="edit-log-box">
                  <div class="edit-log-item">
                    <div class="label">时间</div>
                    <div class="info">{{ item.dateTime }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">时区</div>
                    <div class="info">{{ item.timezone }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号ID</div>
                    <div class="info">{{ item.channelUserName }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号</div>
                    <div class="info">{{ item.operationEmail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">操作类型</div>
                    <div class="info">{{ item.operationDetail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑前</div>
                    <div class="info">{{ item.operationBefore }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑后</div>
                    <div class="info">{{ item.operationAfter }}</div>
                  </div>
                </div>
              </template>
              <img 
                :src="formatEditImg(item)" 
                style="width: 20px;height: 20px;margin-right: 8px;"
              />
            </a-tooltip>
          </template>
          <template #accountStatus="{ record }">
              <a-badge :status="record.accountStatus === 1 ? 'success' : 'normal'" :text="record.accountStatus === 1 ? 'ACTIVE' : 'DISABLED'"/>
          </template>
          <template #status="{ record }">
              <a-badge :status="record.status === 'ACTIVE' ? 'success' : 'normal'" :text="record.status"/>
          </template>
          <template #lifetimeBudget="{ record }">
            {{ !record.lifetimeBudget && !record.dailyBudget ? '使用Ad set预算' : record.dailyBudget }}
          </template>
          <template #dailyBudget="{ record }">
            {{ record.dailyBudget || '无预算' }}
          </template>
          <template #createdTime="{ record }">
            {{ record.createdTime ? dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template #startTime="{ record }">
            {{ record.startTime ? dayjs(record.startTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template #endTime="{ record }">
            {{ record.endTime ? dayjs(record.endTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template #isDynamicCreative="{ record }">
            {{ record.isDynamicCreative ? '是' : '否' }}
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'effectiveStatus'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
      <!-- 批量修改名称 -->
      <BatchEditName ref="nameRef"/>
      <!-- 关联产品 -->
      <RelatedProducts ref="productsRef"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 批量同步数据 -->
      <BatchSyncData ref="syncRef"/>
      <!-- 批量复制广告组 -->
      <CopyAdsetModal ref="copyRef"/>
      <!-- 定时开关 -->
      <TimeSwitch ref="timeRef"/>
      <!-- 批量编辑标签 -->
      <BatchEditTags ref="tagRef"/>
      <!-- 批量编辑受益方或赞助方 -->
      <BatchEditBeneficiary ref="beneficiaryRef"/>
      <!-- Excel编辑 -->
      <ExcelEdit ref="excelRef"/>
      <!-- 修改预算 -->
      <BatchReviseBudget ref="budgetRef"/>
      <!-- 修改出价 -->
      <BatchChangeBid ref="bidRef"/>
      <!-- 修改竞价策略 -->
      <ChangeBidStrategy ref="bidStrategyRef"/>
      <!-- 删除确认 -->
      <a-modal
        v-model:visible="deleteVisible"
        width="416px"
        :hide-title="true"
        :footer="false"
        @cancel="deleteCancel"
      >
      <div class="modal-content">
        <div class="modal-header">
          <icon-question-circle-fill style="color: rgb(var(--warning-6));font-size: 28px;margin-right: 8px;"/>
          删除
        </div>
        <div class="modal-body">
          当前选中1个adset，确定删除这些adset？此操作不可逆，请谨慎操作。
        </div>
      </div>
      <div class="modal-footer">
        <a-button style="margin-right: 24px;" @click="deleteCancel">取消</a-button>
        <a-button type="primary" @click="deleteOk">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject} from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import RelatedProducts from "@/views/launch/promotion/components/RelatedProducts.vue";
import BatchEditName from "@/views/launch/promotion/components/BatchEditName.vue";
import BatchSyncData from "@/views/launch/promotion/components/BatchSyncData.vue";
import {metaDrillList} from "@/views/launch/promotion/components/promotionData"
import TimeSwitch from "@/views/launch/promotion/components/TimeSwitch.vue";
import BatchEditTags from "@/views/launch/promotion/components/BatchEditTags.vue";
import ExcelEdit from "@/views/launch/promotion/components/ExcelEdit.vue";
import BatchReviseBudget from "@/views/launch/promotion/components/BatchReviseBudget.vue";
import DrillDrawer from "../components/DrillDrawer.vue";
import CopyAdsetModal from "../components/CopyAdsetModal.vue";
import BatchEditBeneficiary from "../components/BatchEditBeneficiary.vue";
import BatchChangeBid from "../components/BatchChangeBid.vue";
import ChangeBidStrategy from "../components/ChangeBidStrategy.vue";

// 设置数据
const filterParams = reactive({
  campaignIds: [],
  accountIds:[],
  adsetIds:[]
})
const dateTime = ref(inject('dateTime') as any[])
const emits = defineEmits(['changeTabs']);
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
// 模拟table数据
const loading = ref(false)
const dropdownList = metaDrillList.adset
const tableData = ref<any>([])
const noSumColumns = ref([
  'adsetName',
  'actions',
  'status',
  'lifetimeBudget',
  'dailyBudget',
  'editLog',
  'productName',
  'optimizationGoal',
  'isDynamicCreative',
  'bidStrategy',
  'startTime',
  'endTime',
  'createdTime'
])
const columns = ref<any>([
  { title: '', dataIndex: 'effectiveStatus',slotName:'effectiveStatus',width:100,fixed:'left' },
  { title: '广告组名称', dataIndex: 'adsetName',slotName:'adsetName',width:250,fixed:'left', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '操作', dataIndex: 'actions',slotName:'actions',width:260,fixed:'left',align:'center' },
  { title: '状态', dataIndex: 'status',slotName:'status',minWidth:180 },
  { title: '产品', dataIndex: 'productName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '预算', dataIndex: 'lifetimeBudget',slotName:'lifetimeBudget',minWidth:180 },
  { title: '日预算消耗', dataIndex: 'dailyBudget',slotName:'dailyBudget',minWidth:180 },
  { title: '最近操作', dataIndex: 'editLog',minWidth:180 },
  { title: '广告系列ID', dataIndex: 'campaignId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告系列名称', dataIndex: 'campaignName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告账户ID', dataIndex: 'accountId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告账户名称', dataIndex: 'accountName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '账户状态', dataIndex: 'accountStatus',minWidth:180,slotName:'accountStatus' },
  { title: '剩余预算', dataIndex: 'budgetRemaining',minWidth:180 },
  { title: '竞价策略', dataIndex: 'bidStrategy',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '开始时间', dataIndex: 'startTime',slotName:'startTime',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '结束时间', dataIndex: 'endTime',slotName:'endTime',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '创建时间', dataIndex: 'createdTime',slotName:'createdTime',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '成效目标', dataIndex: 'optimizationGoal',slotName:'optimizationGoal',minWidth:180 , sortable: {sortDirections: ['ascend', 'descend']}},
  { title: '动态创意', dataIndex: 'isDynamicCreative',slotName:'isDynamicCreative',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])
const adsetOptions = ref<any>([])
const adsetOptionsLoading = ref(false);
const adsetPagination = reactive({
  current: 1,
  pageSize: 20,
});
const campaignOptions = ref<any>([])
const campaignOptionsLoading = ref(false);
const campaignPagination = reactive({
  current: 1,
  pageSize: 20,
});
const accountOptions = ref<any>([])
const accountOptionsLoading = ref(false);
const accountPagination = reactive({
  current: 1,
  pageSize: 20,
});
// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const syncRef = ref()
const batchSync = () => {
  syncRef.value.openModal()
}
const formatEditImg = (val) => {
  const editImgMap = {
    "EDIT_BUDGET":"/icon/launch/operation-lifetimeBudget.svg",
    "EDIT_BID":"/icon/launch/operation-bid.svg",
    "EDIT_ON":"/icon/launch/operation-on.svg",
  }
  return editImgMap[val.operationType]
}
const linkAd = (record) => {
  emits('changeTabs',{id:record.id,tab:'ad'})
}
const confirmVisible = ref(false)
const confirmType = ref('')
const statusChange = (record) => {
  confirmType.value = record.authStatus ? 'pause' : 'open'
  confirmVisible.value = true
  return new Promise((resolve, reject) => {
    if(confirmVisible.value){
      resolve(false);
    }
    resolve(true);
  });
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'adset',value:val,recordData:record})
}
// 显示编辑弹出层
const showEdit = (index) => {
  tableData.value[index].isEditing = true;
};

// 取消编辑
const editCancel = (index) => {
  tableData.value[index].isEditing = false;
};

// 确认编辑
const editOk = (index) => {
  tableData.value[index].isEditing = false;
};
const deleteVisible = ref(false)
const deleteCancel = () => {
  deleteVisible.value = false
}
const deleteOk = () => {
  
}
// 查看广告
const linkAdBatch = () => {
  emits('changeTabs',{ids:selectedKeys.value,tab:'ad',type:'fromAdset'})
}
const batchDelete = () => {
  deleteVisible.value = true
}
// 批量修改名称
const nameRef = ref()
const batchEditName = () => {
  nameRef.value.openModal()
}
const productsRef = ref()
const batchRelated = () => {
  productsRef.value.openModal()
}

// 
const batchOpen = () => {
  confirmVisible.value = true
  confirmType.value = 'open'
}
const batchPause = () => {
  confirmVisible.value = true
  confirmType.value = 'pause'
}
// Excel编辑
const excelRef = ref()
const excelEdit = () => {
  excelRef.value.openModal('adset')
}
// 批量编辑标签
const tagRef = ref()
const batchEditTag = () => {
  tagRef.value.openModal('adset')
}
// 批量编辑受益方或赞助方
const beneficiaryRef = ref()
const batchEditBeneficiary = () => {
  beneficiaryRef.value.openModal()
}
// 定时开关
const timeRef = ref()
const batchTime = () => {
  timeRef.value.openModal()
}
// 复制广告组
const copyRef = ref()
const batchCopy = () => {
  copyRef.value.openModal()
}
// 修改预算
const budgetRef = ref()
const batchReviseBudget = () => {
  budgetRef.value.openModal('adset')
}
// 修改出价
const bidRef = ref()
const batchChangeBid = () => {
  bidRef.value.openModal()
}
// 修改竞价策略
const bidStrategyRef = ref()
const changeBidStrategy = () => {
  bidStrategyRef.value.openModal()
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'facebook',
      groupLabel:'adset',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      adsetOptions.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
  
}
// 加载广告组选项
const loadAdsetOptions = async () => {
  adsetOptionsLoading.value = true;
  
  try {
    const params = {
      channel:'facebook',
      groupLabel:'adset',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adsetPagination.pageSize,
      current:adsetPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adsetPagination.current === 1) {
      adsetOptions.value = items;
    } else {
      adsetOptions.value = [...adsetOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告组选项失败:', error);
  } finally {
    adsetOptionsLoading.value = false;
  }
}
const handleAdsetSearch = (v) => {
}
const loadMoreAdsets = async () => {
  adsetPagination.current += 1;
  // 获取更多广告组数据
  await loadAdsetOptions();
}
// 加载广告系列选项
const loadCampaignOptions = async () => {
  campaignOptionsLoading.value = true;
  
  try {
    const params = {
      channel:'facebook',
      groupLabel:'campaign',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:campaignPagination.pageSize,
      current:campaignPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (campaignPagination.current === 1) {
      campaignOptions.value = items;
    } else {
      campaignOptions.value = [...campaignOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    campaignOptionsLoading.value = false;
  }
};   
const handleCampaignSearch = (v) => {
}
const loadMoreCampaigns = async () => {
  campaignPagination.current += 1;
  // 获取更多广告系列数据
  await loadCampaignOptions();
}
// 加载广告账户选项
const loadAccountOptions = async () => {
  accountOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:accountPagination.pageSize,
      current:accountPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    if (accountPagination.current === 1) {
      accountOptions.value = items;
    } else {
      accountOptions.value = [...accountOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    accountOptionsLoading.value = false;
  }
};   
const handleAccountSearch = (v) => {
}
const loadMoreAccounts = async () => {
  accountPagination.current += 1;
  // 获取更多广告账户数据
  await loadAccountOptions();
}
const init = (filter?:any) => {
  // tableData.value = tableData.value.map(item => ({
  //   ...item,
  //   isEditing: false
  // }));
  filterParams.campaignIds = filter?.ids || []
  getList()
  loadAccountOptions()
  loadCampaignOptions()
}
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>