
<template>
    <!-- 批量编辑创意人 -->
    <a-modal v-model:visible="modalVisible" :width="780" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" style="height: 440px;overflow: auto;">
            <a-form-item field="setType" label="设置方式">
                <div>
                    <a-radio-group v-model="form.setType" type="button" @change="handleSetChange">
                        <a-radio value="unified">统一设置</a-radio>
                        <a-radio value="separate">单独设置</a-radio>
                        <a-radio value="smart">智能设置</a-radio>
                    </a-radio-group>
                    <div v-if="form.setType === 'smart'" class="smart-set">
                        当素材的
                        <a-select
                            placeholder="请选择"
                            allow-clear
                            style="width: 100px;">
                        </a-select>
                        包含创意人的
                        <a-select
                            placeholder="请选择"
                            allow-clear
                            style="width: 100px;">
                        </a-select>
                        时，自动给素材打上该创意人。
                    </div>
                </div>
            </a-form-item>
            <a-form-item field="setAs" label="将创意人设置为">
                <div>
                    <div v-if="form.setType === 'smart'">
                        <a-button style="margin-bottom: 12px;margin-right: 12px;">智能识别</a-button>
                        <span style="color: rgb(129, 129, 129);font-size: 12px;">您可对智能识别结果进行手动修改</span>
                    </div>
                    <a-radio-group v-if="form.setType === 'unified'" v-model="form.setAs" type="button">
                        <a-radio value="appoint">指定</a-radio>
                        <a-radio value="clear">清空</a-radio>
                    </a-radio-group>
                    <a-table
                        v-if="form.setType === 'separate'"
                        :columns="separateColumns"
                        :data="form.tableData"
                        :hoverable="true"
                        sticky-header
                        :pagination="false"
                    >
                        <template #creativer="{ rowIndex }">
                            <a-select v-model="form.tableData[rowIndex].creativer">
                            </a-select>
                        </template>
                    </a-table>
                    <a-table
                        v-if="form.setType === 'smart'"
                        :columns="smartColumns"
                        :data="form.tableData"
                        :hoverable="true"
                        sticky-header
                        :pagination="false"
                    >
                        <template #creativer="{ rowIndex }">
                            <a-select v-model="form.tableData[rowIndex].creativer">
                            </a-select>
                        </template>
                    </a-table>
                </div>
            </a-form-item>
            <a-form-item v-if="form.setAs === 'appoint' && form.setType === 'unified'" field="creativerId" label="">
                <a-select
                    v-model:model-value="form.creativerId"
                    placeholder="请选择"
                    allow-clear>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('批量编辑创意人')
const form = reactive({
    setType:'unified',
    setAs:'appoint',
    creativerId:'',
    tableData:[]
})
const separateColumns = ref<any>([
    { title: '素材名称', dataIndex: 'materialName' },
    { title: '素材ID', dataIndex: 'materialId',slotName:'materialId' },
    { title: '创意人', dataIndex: 'creativer',slotName:'creativer' },
])
const smartColumns = ref<any>([
    { title: '素材名称', dataIndex: 'materialName' },
    { title: '识别关键字', dataIndex: 'keyword',slotName:'keyword' },
    { title: '创意人', dataIndex: 'creativer',slotName:'creativer' },
])
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async (type: string) => {
    // 重置表单和验证
    formRef.value?.resetFields();
    formRef.value?.clearValidate();
    // 打开弹窗
    modalVisible.value = true;
};
const closeModal = () => {
    modalVisible.value = false
}
const handleSetChange = () => {

}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.smart-set{
    margin-top: 12px;
    padding: 6px 12px;
    border-radius: 4px;
    display: inline-block;
    background-color: #f5f6f7;
}
</style>