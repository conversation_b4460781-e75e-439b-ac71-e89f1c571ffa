<template>
    <div class="page">
      <div class="page-head">
        <div class="title">
          {{ route.meta.locale }}
        </div>
        <div class="filter">
          <div class="filter-item">
            <DateRangePicker v-model="dateTime" />
          </div>
          <div class="filter-item">
              <a-button type="primary" @click="createAd">创建广告</a-button>
          </div>
        </div>
      </div>
      <div class="page-body">
        <a-tabs v-model:active-key="activeName" @change="handleTabChange">
            <a-tab-pane key="product" title="产品">
                <Product @change-tabs="changeTabs"/>
            </a-tab-pane>
            <a-tab-pane key="user" title="优化师">
                <User/>
            </a-tab-pane>
            <a-tab-pane key="account" title="广告账户">
                <Account ref="accountRef" @change-tabs="changeTabs"/>
            </a-tab-pane>
            <a-tab-pane key="campaign">
                <template #title>
                    <span>广告系列</span>
                    <a-tooltip position="right" content="该页面汇总显示各个渠道的广告数据，对应层级如下：">
                        <icon-question-circle style="margin-left: 4px;"/>
                    </a-tooltip>
                </template>
                <Campaign ref="campaignRef" @change-tabs="changeTabs"/>
            </a-tab-pane>
            <a-tab-pane key="adset">
                <template #title>
                    <span>广告组</span>
                    <a-tooltip position="right" content="该页面汇总显示各个渠道的广告数据，对应层级如下：">
                        <icon-question-circle style="margin-left: 4px;"/>
                    </a-tooltip>
                </template>
                <Adset ref="adsetRef"/>
            </a-tab-pane>
            <a-tab-pane key="material" title="素材">
                <Material/>
            </a-tab-pane>
        </a-tabs>
      </div>
      <CreateAd ref="createAdRef"/>
    </div>
  
  </template>
  
  <script setup lang="ts">
  import { reactive, ref,provide} from "vue";
  import dayjs from 'dayjs';
  import DateRangePicker from "@/components/date-quick-picker/index.vue";
  import Product from "@/views/launch/promotion/summary/pages/product.vue";
  import User from "@/views/launch/promotion/summary/pages/user.vue";
  import Account from "@/views/launch/promotion/summary/pages/account.vue";
  import Campaign from "@/views/launch/promotion/summary/pages/campaign.vue";
  import Adset from "@/views/launch/promotion/summary/pages/adset.vue";
  import Material from "@/views/launch/promotion/summary/pages/material/index.vue";
  import CreateAd from "@/views/launch/promotion/components/CreateAd.vue";
  import {useRoute} from 'vue-router';
  
  const route = useRoute();
  const today = dayjs();
  const dateTime = ref<Date[]>([today.toDate(), today.toDate()])
  const activeName = ref('product')
  provide('dateTime', dateTime);
 
  const adsetRef = ref()
  const productRef = ref()
  const userRef = ref()
  const accountRef = ref()
  const campaignRef = ref()
  const materialRef = ref()
  
  // 创建refs映射对象
  const refsMap = {
    product: productRef,
    user: userRef,
    account: accountRef,
    campaign: campaignRef,
    adset: adsetRef,
    material: materialRef
  }
  // 处理tab切换
  const handleTabChange = (key) => {
    const targetRef = refsMap[key]
    targetRef.value?.init()
  }
  const changeTabs = (val) => {
    activeName.value = val.tab
    // 获取对应的ref
    const targetRef = refsMap[val.tab]
    // 如果ref存在且有refresh方法，则调用
    if (targetRef?.value?.init) {
      targetRef.value.init(val)
    }
  }
  const createAdRef = ref()
  const createAd = () => {
    createAdRef.value.openModal()
  }
  </script>
  
  <style scoped lang="less">
  </style>
