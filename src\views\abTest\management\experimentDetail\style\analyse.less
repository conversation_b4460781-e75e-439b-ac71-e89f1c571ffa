.analyse-body {
  position: relative;
  width: 100%;
  height: calc(100vh - 182px);
  margin: 0;
  overflow: hidden;

  .body-left {
    height: calc(100% + 16px);
    background: #fff;
    position: relative;
    margin-top: -16px;

    .query-condition {
      width: 100%;
      height: calc(100% - 56px);
      overflow-x: hidden;
      overflow-y: auto;
      box-sizing: border-box;
      position: relative;
    }

    .left-footer {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 56px;
      padding: 12px 48px 12px 20px;
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
      border-top: 1px solid var(--tant-border-color-border1-1);
      align-items: center;

      button {
        margin-left: 12px;
        border-radius: 4px;
      }

      :deep(.arco-btn-primary) {
        min-width: 80px;
      }
    }
  }

  .body-right {
    height: 100%;
    outline: none;
    background: #fff;
    position: relative;
    display: flex;
    transition: all .3s ease-in-out;

    .wrap {
      position: relative;
      flex: 2;
      height: 100%;
      padding-bottom: 24px;
      overflow: overlay;
      overflow-x: hidden;
      overflow-y: auto;
      white-space: normal;
      background-color: #fff;
      border-top-right-radius: 4px;
      transition: width .3s;
      display: flex;

      .body-right-content {
        width: 100%;
        height: calc(100% - 54px);
        border-radius: 2px;

        .right-header {
          position: relative;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          margin-top: -8px;
          padding: 8px 14px;
          border-bottom: 1px solid var(--tant-border-color-border1-1);
        }

        .right-header::before, .right-header::after {
          position: absolute;
          bottom: -1px;
          left: -24px;
          width: 24px;
          border-bottom: 1px solid var(--tant-border-color-border1-1);
          content: "";
        }

        .right-header::after {
          right: -24px;
          left: auto;
        }

        .right-content {
          width: 100%;
          position: relative;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }
      }
    }
  }

  .arrow {
    position: absolute;
    bottom: 15px;
    right: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 24px;
    color: var(--widget-color);
    font-size: 16px;
    background-color: #fff;
    border: 1px solid var(--tant-border-color-border1-1);
    border-right: 0;
    border-radius: 12px 0 0 12px;
    cursor: pointer;
    transition: all .3s;
  }

  .arrow:hover {
    color: var(--tant-primary-color-primary-default);
    border-color: var(--tant-primary-color-primary-default);
    box-shadow: var(--input-shadow);
    width: 34px;
  }

  .deg {
    left: 0;
    transform: rotate(180deg)
  }
}

.full-container {
  width: 100%;
  height: 100%;
}

.empty {
  height: 85%;
  width: 100%;
  min-height: 400px;
  margin: 0 24px 32px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;

  .empty-body {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);

    .empty-img {
      width: 400px;
      height: 256px;
    }

    .empty-description {
      color: var(--tant-text-gray-color-text1-3);
      font-size: 12px;
      width: 400px;
      word-break: break-all;
      text-align: center;
      margin: 0 auto;

      .title {
        margin-bottom: 12px;
        font-weight: 500;
        font-size: 16px;
        color: var(--tant-text-gray-color-text1-2);
      }

      .button-add {
        margin-top: 12px;
        color: var(--tant-white-white-100);
        background-color: var(--tant-primary-color-primary-default);
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }
    }

  }
}

