<template>
  <a-form ref="formRef" :model="form" :rules="rules">
    <a-form-item field="business" label="苹果账户">
      <a-select v-model="form.business" placeholder="请选择账户" style="width: 520px;" allow-clear>
        <a-option v-for="account in accounts" :key="account.business" :value="account.business">{{ account.business }}</a-option>
      </a-select>
    </a-form-item>
    <a-form-item field="orderId" label="订单编号">
      <a-input v-model="form.orderId" placeholder="请输入订单编号" style="width: 520px;" allow-clear />
    </a-form-item>
    <a-form-item>
      <a-button type="primary" :loading="loading" @click="queryOrder">查询</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import {reactive, ref} from 'vue';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {getAuthorizeList, verifyOrder} from "@/api/marketing/api"
import {Notification} from '@arco-design/web-vue';
import {LocalStorageEventBus} from "@/types/event-bus";

const localStorageEventBus = useEventBus(LocalStorageEventBus)
const appId = ref(useSessionStorage('app-id', '')?.value)
const accounts = ref<any>([])
const form = reactive({ business: '', orderId: '' });
const formRef = ref()
// 表单规则
const rules = {
  business: [
    {
      required: true,
      message: '请选择账户'
    }
  ],
  orderId: [
    {
      required: true,
      message: '请输入订单编号'
    }
  ],
}
const loading = ref(false);
const queryOrder = () => {
  formRef.value.validate(async (valid: any) => {
    if (!valid) {
      loading.value = true;
      const params = {
        appId: appId.value,
        business: form.business,
        orderId: form.orderId
      }
      try {
        const res = await verifyOrder(params);
        if(res){
          Notification.info({
            title: '验证结果',
            content: '有效',
          })
        }else{
          Notification.error({
            title: '验证结果',
            content: '无效',
          })
        }
      } catch (error) {
        console.error('Error verifying order:', error);
      } finally {
        loading.value = false;
      }
    }
  })
};
localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    appId.value = value
  }
})
const init = async () => {
  const res = await getAuthorizeList()
  accounts.value = res
}
init()
</script>

<style scoped>

</style>