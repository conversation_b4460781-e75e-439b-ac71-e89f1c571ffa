<script setup lang="ts">
import {Codemirror} from "vue-codemirror";
import {format} from 'sql-formatter';
import {defineEmits, defineExpose, onMounted, reactive, ref, watch} from "vue";
import {sql} from '@codemirror/lang-sql';
import useClipboard from 'vue-clipboard3'
import {Message} from '@arco-design/web-vue';
import {querySqlReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {saveAnalyseFavoriteList} from "@/api/analyse/api";
import {useStorage} from "@vueuse/core";
import sqlhelp from "./sqlhelp.vue";


interface Props {
   queryParams: any
}

const props = defineProps<Props>()
const sqlChartValue = useStorage("sql-chart-value", []);
const sqlhelpRef = ref()
const code = ref(``);
const extensions = ref([sql()]);
const {toClipboard} = useClipboard()
const showAutoComplete = ref<boolean>(true)
const visiblebookmark = ref<boolean>(false)
const bookmarkValue = ref('')
const selectActive = ref(false)
const loading = ref<boolean>(false)
const requestId = ref()
const addParamPopupShow = ref<boolean>(false)
const addSelectorPopupShow = ref([])
// 查询错误信息
const queryErrorMessage = ref<string>()

const emits = defineEmits<{
  (event: 'result', data: any): void;
  (event: 'report', data: any): void;
}>();

const form = reactive({
  name: '',
  posts: [] as any
})

const dataItem = ['PartDate',
  'Variable',
  'Number',
  'Text',
  'Time',
  'Selector']
const timeOut = ref()

// 自动补全
const autoComplete = (value: boolean) => {
  if (value) {
    extensions.value = [sql()]
  } else {
    extensions.value = []
  }
}

const removeDuplicates = (array: any) => {
  return [...new Set(array)]; // 数组去重
}
// 过滤空值
const filterEmptyValues = (array: any) => {
  return array.filter(item => item !== '');
};

const filterStrings = (array: any) => {
  // return array.map(item => item.replace(/\d+$/, '')).filter(Boolean);
  return array.replace(/\d+$/, ''); // 过滤数组数字
}

// 转换类型
const transSelect = (value: string) => {
  const typeMap: Record<string, string | number> = {
    'Selector': 'Selector',
    'Number': 'gteq',
    'Text': 'gteq',
    'Time': 'gteq',
    'PartDate': 0
  };
  return typeMap[value] !== undefined ? typeMap[value] : 'Variable';
};

// 监听 showAutoComplete 变化
watch(showAutoComplete, () => {
  autoComplete(showAutoComplete.value)
})
// 监听 code 变化
watch(code, (newValue, oldValue) => {
  const regex = /\${([^{}]*)}/g;
  let matches = [];
  let match;
  while ((match = regex.exec(newValue)) !== null) {
    matches.push(match[1]);
  }
  if (!matches.length) {
    form.posts = [];
    return;
  }
  matches = removeDuplicates(matches)
  matches = filterEmptyValues(matches)
  
  // 保存现有参数的值
  const existingParams = {};
  form.posts.forEach(item => {
    existingParams[item.name] = {
      value: item.value,
      value1: item?.value1,
      value2: item?.value2,
      select: item.select,
      selectorItems: item.selectorItems,
      selectedItem: item.selectedItem
    };
  });

  const vNames = form.posts.map(item => item.name);
  form.posts = form.posts.filter(item => matches?.includes(item.name));
  const x = matches?.filter((m) => !vNames.includes(m));
  
  x.forEach(item => {
    const paramName = item.includes(':') ? item.split(':')[1] : item;
    const paramType = item.includes(':') ? item.split(':')[0] : 'Variable';
    const newParam = {
      value: '',
      name: paramName,
      select: item.includes(':') ? transSelect(item.split(':')[0]) : 'Variable',
      type: paramType,
      selectorItems: [],
      selectedItem: ''
    };
    form.posts.push(newParam);
  });

  // 还原已存在参数的值
  form.posts.forEach(item => {
    if (existingParams[item.name]) {
      item.value = existingParams[item.name].value;
      item.value1 = existingParams[item.name].value1;
      item.value2 = existingParams[item.name].value2;
      item.select = existingParams[item.name].select;
      item.selectorItems = existingParams[item.name].selectorItems;
      item.selectedItem = existingParams[item.name].selectedItem;
    }
  });

});
  
// 添加参数
const addText = (value: any) => {
  code.value = ''
  code.value += 'SELECT '
  code.value += value[0]
  code.value += ' FROM '
  code.value += value[1]
  code.value += ' LIMIT 100;'
  Message.success('设置成功')
}
// 添加全部
const addTextAll = (value: any) => {
  code.value = value
  Message.success('设置成功')
}

const addsqlParams = (value:any) => {
  if (!value){return;}

  if (value.length===0){
      return
  }
  form.posts = []
  value.forEach((item: any) => {
    let formatValue;
    if (item.paramType === 'Text') {
      formatValue = item?.thresholds?.join(',');
    } else if (item.paramType === 'Variable') {
      formatValue = item?.thresholds[0]
    } else if (item.paramType === 'PartDate') {
      formatValue = [item.partDate?.startDate, item.partDate?.endDate];
    } else if (item.paramType === 'Time') {
      formatValue = item.calcuSymbol === 'scope' ? item?.thresholds : item?.thresholds[0]
    } else {
      formatValue = item?.thresholds;
    }
    form.posts.push({
      type:item.paramType,
      value: formatValue,
      value1: item?.thresholds?.[0],
      value2: item?.thresholds?.[1],
      name: item.paramName,
      select: item?.calcuSymbol || item?.useTimezone,
      selectorItems:item?.selectorItems,
      selectedItem:item?.selectedItem
    })
  })
}
const clearlocal = () => {
  // localStorage.removeItem('codeValue')
  // localStorage.removeItem('postsSql')
}

// 格式化
const formatSql = () => {
  if (code.value) {
    try {
      code.value = format(code.value, {language: 'mysql'});
      Message.success('格式化成功')
    } catch (e) {
      Message.info('格式化失败')
    }
  } else {
    Message.info('内容为空')
  }
};

// 清空
const clearSql = () => {
  try {
    code.value = '';
    form.posts = [];
    Message.success('清空成功')
  } catch (e) {
    Message.info('清空失败')
  }
}

// 粘贴
const copy = async () => {
  if (code.value) {
    try {
      await toClipboard(code.value)
      Message.success('复制成功')
    } catch (e) {
      Message.info('复制失败')
    }
  } else {
    Message.info('复制内容为空')
  }
}

const copyOne = async (value: any) => {
  try {
    await toClipboard(value)
    Message.success('复制成功')
  } catch (e) {
    Message.info('复制失败')
  }
}

const showHelp = () => {
  sqlhelpRef.value.show()
}

const lookForDuplicates = (name: string) => {
  return !!form.posts.find((item) => {
    return item.name === name
  })
}

const handleAdd = (item: string, select?: any,type?:string) => {
  addParamPopupShow.value = false
  if (type === 'Variable'){
    code.value += '${'
    code.value += `${type}${form.posts.length+1}`
    code.value += '}'
  }else {
    code.value += '${'
    code.value += `${type}:${item.toLowerCase()}${form.posts.length+1}`
    code.value += '}'
  }
};

const handleDelete = (index: any,prefix?:string) => {
  // form.posts.splice(index, 1)
  code.value = code.value.replace('${' + form.posts[index].name + '}', '')
  code.value = code.value.replace('${' + prefix + ':' + form.posts[index].name + '}', '')
}

const valueFramt = () => {
  // 提取 SQL 参数
  const regex = /\${([^{}]*)}/g;
  const matches = [];
  const sql = code.value.replace(regex, (match, p1) => {
    matches.push(p1);
    return '';
  });
  
  if (!sql) return false;

  // 基础参数对象
  const baseParam = {
    paramDisplay: "",
    paramRemark: ""
  };
  // 参数处理器映射
  const paramHandlers = {
    Variable: (item) => ({
      ...baseParam,
      paramName: item.name,
      paramType: item.type,
      calcuSymbol: item.select,
      thresholds: [item.value]
    }),
    Selector: (item) => ({
      ...baseParam,
      paramName: item.name,
      paramType: item.type,
      selectorItems: item.selectorItems,
      selectedItem: item.selectedItem
    }),
    PartDate: (item) => ({
      ...baseParam,
      paramName: item.name,
      paramType: item.type,
      useTimezone: item.select,
      partDate: {
        startDate: item.value[0],
        endDate: item.value[1]
      }
    }),
    Number: (item) => {
      let thresholds = [];
      if (item.select === 'scope') {
        thresholds = [item.value1, item.value2];
      } else if (item.select === 'ex' || item.select === 'nex') {
        thresholds = [];
      } else {
        thresholds = [item.value1];
      }
      return {
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        calcuSymbol: item.select,
        thresholds
      };
    },
    Text: (item) => {
      let thresholds = [];
      if (item.select === 'ex' || item.select === 'nex') {
        thresholds = [];
      } else {
        thresholds = item.value?.split(',').map(v => v.trim()).filter(Boolean) || [];
      }
      return {
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        calcuSymbol: item.select,
        thresholds
      };
    },
    Time: (item) => ({
      ...baseParam,
      paramName: item.name,
      paramType: item.type,
      calcuSymbol: item.select,
      thresholds: item.select === 'scope' ? [...item.value] : [item.value]
    })
  };
  
  // 生成参数列表
  const sqlParams = form.posts.map(item => {
    const handler = paramHandlers[item.type];
    return handler ? handler(item) : null;
  }).filter(Boolean);
  
  // localStorage.setItem('codeValue', JSON.stringify(code.value));
  // localStorage.setItem('postsSql', JSON.stringify(form.posts));
  emits('report', { sql: code.value, sqlParams });
  
  return { sql: code.value, sqlParams };
};

const freshData = () => {
  try{
    requestId.value = querySqlReportData(valueFramt());
  } catch(error) {
    console.error(error);
    loading.value = false
  }
  // emits('result',res)
};
watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }

  if (newData.result === null || newData.errorMessage) {
    queryErrorMessage.value = newData.errorMessage;
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }
  queryErrorMessage.value = undefined
  timeOut.value && clearTimeout(timeOut.value)
  loading.value = false
  emits('result', newData)
})
const formRef = ref();

// form 校验
const validateForm = () => {
  const invalidParams = form.posts.filter(post => {
    if (post.type === 'Number') {
      if (post.select === 'ex' || post.select === 'nex') {
        return false;
      }
      if (post.select === 'scope') {
        return !post.value1 || !post.value2;  // 同时校验 value1 和 value2
      }
      return !post.value1;
    }
    if (post.type === 'PartDate' || post.type === 'Time') {
      return !post.value || (post.select === 'scope' && (!Array.isArray(post.value) || post.value.length !== 2));
    }
    return !post.value;
  });

  if (invalidParams.length > 0) {
    Message.error('参数值不能为空');
    return false;
  }
  return true;
};
const submit = () => {
  if (code.value === '') {
    Message.info('请输入sql语句')
    return
  }
  if (!validateForm()) {
    return;
  }
  if (!valueFramt()) {
    Message.info('sql存在语法错误，请改正！')
    return
  }
  localStorage.removeItem('sql-chart-query')
  localStorage.removeItem('sql-chart-value')
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.info('SQL查询超时，请重试！')
    }
  }, 3*60*1000)
  freshData()
}
const handleOkbookmark = async () => {
  await saveAnalyseFavoriteList(bookmarkValue.value, code.value).then(res => {
    Message.success('保存成功')
    visiblebookmark.value = false
  }).catch(err => {
    Message.error('请检查网络')
  })

}
const showbookmark = () => {
  bookmarkValue.value = `书签_${Date.now().toString()}`
  visiblebookmark.value = true
}
const changeActive = (value: any) => {
  selectActive.value = value.length !== 1;
}

const pushselectorItems = (index:number) => {
  if (form.posts[index].selectorItems.length > 19) {
    Message.info('最多只支持设置20项');
    return
  }
  form.posts[index].selectorItems.push({selectorName: '', selectorValue: ''})
}
const delselectorItems = (index: number,index2:number) => {
  form.posts[index].selectorItems.splice(index2, 1)
}
const transSelector = (index:number,index2:number) => {
  addSelectorPopupShow.value[index] = false
  form.posts[index].value = 'Selector'
}

onMounted(()=>{
  // if (localStorage.getItem('codeValue')) {
  //   code.value = JSON.parse(localStorage.getItem('codeValue') as string)
  // }
  // if (localStorage.getItem('postsSql')) {
  //   form.posts = JSON.parse(localStorage.getItem('postsSql') as string)
  // }
})

// 监听 props.queryParams 变化
watch(() => props.queryParams, (newValue, oldValue) => {
  // 缓存中sql参数
  const localParams = JSON.parse(sessionStorage.getItem('sql-query-params') || '{}')
  // 优先使用props中参数，props中参数是接口请求的
  const paramsData = props.queryParams || localParams
  // 设置 SQL 语句
  code.value = paramsData.sql || '';
  // 设置参数列表
  if (paramsData.sqlParams?.length > 0) {
    form.posts = paramsData.sqlParams.map((item: any) => {
      let value;
      if (item.paramType === 'Text') {
        value = item?.thresholds?.join(',');
      } else if (item.paramType === 'Variable') {
        value = item?.thresholds[0]
      } else if (item.paramType === 'PartDate') {
        value = [item.partDate?.startDate, item.partDate?.endDate];
      } else if (item.paramType === 'Time') {
        value = item.calcuSymbol === 'scope' ? item?.thresholds : item?.thresholds[0]
      } else {
        value = item?.thresholds;
      }
      return {
        value,
        value1: item.thresholds?.[0],
        value2: item.thresholds?.[1],
        name: item.paramName,
        select: item?.calcuSymbol || item?.useTimezone,
        type: item.paramType,
        selectorItems: item.selectorItems || [],
        selectedItem: item.selectedItem || ''
      };
    });
  }
  if(code.value && !sqlChartValue.value?.length){
    submit();
  }
}, { immediate: true });

defineExpose({
  addText,
  addTextAll,
  addsqlParams,
  clearlocal
})
</script>

<template>
  <a-collapse :default-active-key="[1]" @change="changeActive">
    <a-collapse-item :key="1" header="语句输入框">
      <template #extra>
        <span v-show="!selectActive" style="color: rgb(30, 118, 240)" @click.stop="clearSql">清空</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span v-show="!selectActive" style="color: rgb(30, 118, 240)" @click.stop="formatSql">格式化</span>
      </template>
      <template #header>
        <span style="color: var(--tant-text-gray-color-text1-2);font-weight: 500;font-size: 16px;cursor: pointer;">
          语句输入框
        </span>
        <span v-if="selectActive" style="margin-left: 8px;color: var(--tant-text-gray-color-text1-3);font-weight: 400;font-size: 12px;">
          展开语句输入框，恢复编辑状态
        </span>
        <span v-else style="margin-left: 8px;color: var(--tant-text-gray-color-text1-3);font-weight: 400;font-size: 12px;">
          如果仅需调整动态参数，请收起语句输入框
        </span>
      </template>
      <codemirror v-model="code" placeholder="请输入SQL语句..." :style="{ maxHeight: '38vh',minHeight:'70px' }" :autofocus="true" :tabSize="2" :extensions="extensions"/>
    </a-collapse-item>
    <a-alert type="error" v-if="!!queryErrorMessage">{{queryErrorMessage}}</a-alert>
    <div class="flex-container">
      <div class="leftDiv">
        <a-space align="center">
          <a-form ref="formRef" :model="form">
            <a-form-item
              v-for="(post,index) of form.posts"
              :key="index"
              :field="post.type === 'Number' && post.select === 'scope'
                ? `posts[${index}].value1` 
                : `posts[${index}].value`"
              :hide-label="true">
              <span class="param-number">{{ index + 1 }}</span>
              <span class="param-title">{{ post.name.includes(':')?post.name.split(':')[1]:post.name }}</span>
              <div v-if="post.type==='PartDate'">
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" class="param-select">
                    <template #arrow-icon/>
                    <a-option :value="0">不受时区影响</a-option>
                    <a-option :value="1">受时区影响</a-option>
                  </a-select>
                  <a-range-picker v-model="post.value" style="width: 254px;" :allow-clear="false" />
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copyOne('${'+post.name+'}')"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'PartDate')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                  <span style="color: #915aff;white-space: nowrap;">"$part_date" BETWEEN '{{ post.value[0] }}' AND '{{ post.value[1] }}'</span>
                </a-space>
              </div>
              <div v-else-if="post.type==='Variable'">
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" default-value="Variable" class="param-select">
                    <template #arrow-icon/>
                    <a-option value="Variable">自定义</a-option>
                  </a-select>
                  <a-input v-model="post.value" :style="{width:'220px'}"/>
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copy"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Variable')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </div>
              <div v-else-if="post.type==='Number'">
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" class="param-select">
                    <template #arrow-icon/>
                    <a-option value="eq">等于</a-option>
                    <a-option value="neq">不等于</a-option>
                    <a-option value="lt">小于</a-option>
                    <a-option value="lteq">小于等于</a-option>
                    <a-option value="gt">大于</a-option>
                    <a-option value="gteq">大于等于</a-option>
                    <a-option value="ex">有值</a-option>
                    <a-option value="nex">无值</a-option>
                    <a-option value="scope">区间</a-option>
                  </a-select>
                  <a-input-number v-if="form.posts[index].select != 'ex' && form.posts[index].select != 'nex'" v-model="post.value1" :style="{width:'220px'}" :min="0"/>
                  <a-input-number v-if="form.posts[index].select == 'scope'" v-model="post.value2" :min="0" :style="{width:'220px'}"/>
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copyOne('${'+post.name+'}')"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Number')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                  <span v-if="form.posts[index].select=='eq'" style="color: #915aff;white-space: nowrap;">IN ({{ post.value1 }})</span>
                  <span v-if="form.posts[index].select=='neq'" style="color: #915aff;white-space: nowrap;">NOT IN ({{ post.value1 }})</span>
                  <span v-if="form.posts[index].select=='lt'" style="color: #915aff;white-space: nowrap;"> &lt;{{ post.value1 }}</span>
                  <span v-if="form.posts[index].select=='lteq'" style="color: #915aff;white-space: nowrap;">&le; {{ post.value1 }}</span>
                  <span v-if="form.posts[index].select=='gt'" style="color: #915aff;white-space: nowrap;">&gt;{{ post.value1 }}</span>
                  <span v-if="form.posts[index].select=='gteq'" style="color: #915aff;white-space: nowrap;">&ge;{{ post.value1}}</span>
                  <span v-if="form.posts[index].select=='ex'" style="color: #915aff;white-space: nowrap;">IS NOT NULL</span>
                  <span v-if="form.posts[index].select=='nex'" style="color: #915aff;white-space: nowrap;">IS NULL</span>
                  <span v-if="form.posts[index].select=='scope'" style="color: #915aff;white-space: nowrap;">BETWEEN {{ post.value1}}AND{{ post.value2}}</span>
                </a-space>
              </div>
              <div v-else-if="post.type==='Text'">
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" class="param-select">
                    <template #arrow-icon/>
                    <a-option value="eq">等于</a-option>
                    <a-option value="neq">不等于</a-option>
                    <a-option value="lt">小于</a-option>
                    <a-option value="lteq">小于等于</a-option>
                    <a-option value="gt">大于</a-option>
                    <a-option value="gteq">大于等于</a-option>
                    <a-option value="con">包含</a-option>
                    <a-option value="ex">有值</a-option>
                    <a-option value="nex">无值</a-option>
                  </a-select>
                  <a-input v-if="form.posts[index].select != 'ex' && form.posts[index].select != 'nex'" v-model="post.value" :style="{width:'220px'}"/>
                  <span v-if="form.posts[index].select=='eq'" style="color: #915aff;white-space: nowrap;">IN ({{ post.value }})</span>
                  <span v-if="form.posts[index].select=='neq'" style="color: #915aff;white-space: nowrap;">NOT IN ({{ post.value }})</span>
                  <span v-if="form.posts[index].select=='lt'" style="color: #915aff;white-space: nowrap;"> &lt;{{ post.value }}</span>
                  <span v-if="form.posts[index].select=='lteq'" style="color: #915aff;white-space: nowrap;">&le;{{ post.value }}</span>
                  <span v-if="form.posts[index].select=='gt'" style="color: #915aff;white-space: nowrap;">&gt;{{ post.value }}</span>
                  <span v-if="form.posts[index].select=='gteq'" style="color: #915aff;white-space: nowrap;">&ge;{{ post.value }}</span>
                  <span v-if="form.posts[index].select=='con'" style="color: #915aff;white-space: nowrap;">LIKE {{ post.value }}</span>
                  <span v-if="form.posts[index].select=='ex'" style="color: #915aff;white-space: nowrap;">IS NOT NULL</span>
                  <span v-if="form.posts[index].select=='nex'" style="color: #915aff;white-space: nowrap;">IS NULL</span>
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copyOne('${'+post.name+'}')"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Text')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </div>
              <div v-else-if="post.type==='Time'">
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" class="param-select" :trigger-props="{ autoFitPopupMinWidth: true}">
                    <template #arrow-icon/>
                    <a-option value="eq">等于</a-option>
                    <a-option value="neq">不等于</a-option>
                    <a-option value="lt">小于</a-option>
                    <a-option value="lteq">小于等于</a-option>
                    <a-option value="gt">大于</a-option>
                    <a-option value="gteq">大于等于</a-option>
                    <a-option value="scope" >时间区间
                      <a-tooltip content="选择具体时间范围，包含区间左右值" position="right" mini>
                        <icon-info-circle />
                      </a-tooltip>
                    </a-option>
                  </a-select>
                  <a-date-picker
                      v-if="form.posts[index].select !== 'scope'"
                      v-model="post.value"
                      style="width: 220px; "
                      :allow-clear="false"
                      show-time
                      :time-picker-props="{ defaultValue: '09:09:06' }"
                      format="YYYY-MM-DD HH:mm:ss"
                  />
                  <a-range-picker v-else v-model="post.value" style="width: 254px;" :allow-clear="false"/>
                  <span v-if="form.posts[index].select=='eq'" style="color: #915aff;white-space: nowrap;">IN ({{ post.value }})</span>
                  <span v-if="form.posts[index].select=='neq'" style="color: #915aff;white-space: nowrap;">NOT IN ({{ post.value }})</span>
                  <span v-if="form.posts[index].select=='lt'" style="color: #915aff;white-space: nowrap;">&lt;timestamp ({{ post.value }})</span>
                  <span v-if="form.posts[index].select=='lteq'" style="color: #915aff;white-space: nowrap;">&le;timestamp '{{ post.value }}'</span>
                  <span v-if="form.posts[index].select=='gt'" style="color: #915aff;white-space: nowrap;">&gt;timestamp '{{ post.value }}'</span>
                  <span v-if="form.posts[index].select=='gteq'" style="color: #915aff;white-space: nowrap;">&ge;timestamp '{{ post.value }}'</span>
                  <span v-if="form.posts[index].select=='scope'" style="color: #915aff;white-space: nowrap;">BETWEEN timestamp '{{ post.value[0] }}' AND timestamp '{{ post.value[1] }}'</span>
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copyOne('${'+post.name+'}')"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Time')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                  <!--              <span style="color: #915aff;width: 400px;">>= timestamp '{{post.value}}'</span>-->

                </a-space>
              </div>
              <div v-else-if="post.type==='Selector'">
                <a-space align="center" :size="10">
                  <a-trigger  v-model:popup-visible="addSelectorPopupShow[index]" trigger="click" :unmount-on-close="false" :update-at-scroll="true" align-point>
                    <div v-show="post.value !== 'Selector'" style="cursor: pointer;">
                      &nbsp;&nbsp;<icon-settings/>
                      设置
                    </div>
                    <template #content>
                      <div style="background-color: #fff;padding: 10px;width: 400px;">
                        <div >
                          <span>设置选择项 </span>
                        </div>
                        <div class="menuItem">
                          <span v-show="form.posts[index].selectorItems.length == 0" style="color: #bdbebf;font-weight: 500;font-size: 15px;margin-top: 8px;">
                            暂无选择项
                          </span>
                          <div v-for="(item,index2) in form.posts[index].selectorItems" :key="index2" class="itemOffspring">
                            <a-space style="margin-top: 5px;margin-bottom: 5px;">
                              <a-input v-model="form.posts[index].selectorItems[index2].selectorName" :style="{width:'100px'}" placeholder="请输入选项名"/>
                              <a-input v-model="form.posts[index].selectorItems[index2].selectorValue" :style="{width:'220px'}" placeholder="选中时内容将填入sql语句"/>
                            </a-space>
                            <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="delselectorItems(index,index2)"/>
                          </div>
                        </div>
                        <div style="display: flex;justify-content: space-between;margin-top: 8px;">
                          <div class="selectorItems" @click="pushselectorItems(index)">
                            <span><icon-plus/> 选择项 </span>
                          </div>
                          <div>
                            <a-button style="margin-right: 10px;" size="small" @click="()=> {addSelectorPopupShow[index] = false }">取消</a-button>
                            <a-button type="primary" size="small" @click="transSelector(index)">确定</a-button>
                          </div>
                        </div>
                      </div>
                    </template>
                  </a-trigger>
                  <div v-if="form.posts[index].value === 'Selector'" style="cursor: pointer;">
                    <a-select v-model="form.posts[index].selectedItem" :style="{width:'130px'}">
                      <a-option v-for="(item,index2) in form.posts[index].selectorItems" :key="index2" :value="item.selectorName" :label="item.selectorName" />
                    </a-select>
                  </div>

                  <a-tooltip content="设置选择项" position="top">
                    <icon-settings v-if="form.posts[index].value === 'Selector'" :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="addSelectorPopupShow[index]=true"/>
                  </a-tooltip>

                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copy"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Selector')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </div>
              <div v-else>
                <a-space align="center" :size="10">
                  <a-select v-model="form.posts[index].select" default-value="Variable" class="param-select">
                    <template #arrow-icon/>
                    <a-option value="Variable">自定义</a-option>
                  </a-select>
                  <a-input v-model="post.value" :style="{width:'220px'}"/>
                  <a-tooltip content="复制" position="top">
                    <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;" @click="copy"/>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top">
                    <a-popconfirm content="确认删除吗?" position="right" type="error" @ok="handleDelete(index,'Variable')">
                      <icon-delete :style="{fontSize:'20px'}" style="cursor: pointer;margin-left: 10px;"/>
                    </a-popconfirm>
                  </a-tooltip>
                </a-space>
              </div>
            </a-form-item>
          </a-form>
        </a-space>
        <a-space align="start" style="cursor: pointer;">
          <a-trigger v-model:popup-visible="addParamPopupShow" position="bl" trigger="click" auto-fit-position :unmount-on-close="false" style="box-shadow: var(--tant-small-shadow-small-overall);border-right: 3px;">
              <span v-show="!selectActive" class="add-param">
                <span class="add-param-icon">
                <icon-plus :style="{fontSize:'12px'}"/>
                </span>
                <span>动态参数</span>
              </span>
            <template #content>
              <div class="add-param-pop">
                <a-popover title="可变内容" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill @click="handleAdd('Variable','Variable','Variable')">
                    <div>可变内容</div>
                    <a-tag bordered>Variable</a-tag>
                  </div>
                  <template #content>
                    可选择具体时间或将自定义输入值作为可变内容，计算时会替换对应位置的$变量 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">... LIMIT ${limit_num}，可在查询时设置查询返回的结果条数</span>
                  </template>
                </a-popover>
                <a-popover title="事件时间" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill @click="handleAdd('PartDate',0,'PartDate')"
                  >
                    <div>事件时间</div>
                    <a-tag bordered>PartDate</a-tag>
                  </div>
                  <template #content>
                    在查询事件数据时，可使用时间选择控件调整查询事件的时间 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">
                      ... WHERE ${PartDate:pd} AND ...，在查询时可调整查询事件的时间范围
                    </span>
                  </template>
                </a-popover>
                <a-popover title="数值表达式" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill @click="handleAdd('Number','eq','Number')">
                    <div>数值表达式</div>
                    <a-tag bordered>Number</a-tag>
                  </div>
                  <template #content> 生成数值类型字段的条件表达式，可在分析时进行动态调整 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">... WHERE number ${Number:nm} AND ...，可调整数值型字段的筛选条件</span>
                  </template>
                </a-popover>
                <a-popover title="文本表达式" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill @click="handleAdd('Text','eq','Text')">
                    <div>文本表达式</div>
                    <a-tag bordered>Text</a-tag>
                  </div>
                  <template #content>生成字符串类型字段的条件表达式，可在分析时进行动态调整 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">... WHERE varchar ${Text:tx} AND ...，可调整字符串型字段的筛选条件</span>
                  </template>
                </a-popover>
                <a-popover title="时间表达式" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill @click="handleAdd('Time','gteq','Time')">
                    <div>时间表达式</div>
                    <a-tag bordered>Time</a-tag>
                  </div>
                  <template #content>生成时间类型字段的条件表达式，可在分析时进行动态调整 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">.. WHERE time ${Time:tm} AND ...，可调整时间型字段的筛选条件</span>
                  </template>
                </a-popover>
                <a-popover title="选择项" position="right" :content-style="{width: '250px'}">
                  <div class="spanHover" direction="vertical" fill style="cursor: pointer;background-color: #fff;" @click="handleAdd('Selector','Selector','Selector')">
                    <div>选择项</div>
                    <a-tag bordered>Selector</a-tag>
                  </div>
                  <template #content>可以将 SQL 语句片段作为选择项，在查询时选择合适的选项，即可将对应的语句片段作用到参数上 <br>
                    <a-tag color="arcoblue">样例</a-tag>
                    <br>
                    <span style="color: #7e7f80">WHERE ${Selector:filter} AND ...，可将多个复杂的筛选条件设置成选项，在分析时选择合适的筛选</span>
                  </template>
                </a-popover>
              </div>
            </template>
          </a-trigger>
        </a-space>
      </div>
      <div class="rightDiv">
        <a-space align="center">
          <a-trigger position="bottom" auto-fit-position :unmount-on-close="false">
            <icon-more-vertical :style="{fontSize:'20px'}" class="moreList"/>
            <template #content>
              <div style="background-color: #fff;border-radius: 5px;box-shadow: var(--tant-small-shadow-small-overall);">
                <a-space class="spanHover" direction="vertical" fill>
                  <span class="listSpan" @click="showHelp">查询帮助</span>
                </a-space>
                <a-popover :title="showAutoComplete?'语句自动补全已开启':'语句自动补全已关闭'" position="left" :content-style="{width: '230px'}">
                  <a-space class="spanHover" direction="vertical" fill>
                      <span class="listSpan" style="margin-right: 5px;">
                        自动补全&nbsp;&nbsp;&nbsp;&nbsp;
                        <a-switch v-model="showAutoComplete"/></span>
                  </a-space>
                  <template #content>
                    示例 <br>
                    <img src="/image/auto-complete.png" alt="" style="width: 90%;height: 90%;"/>
                  </template>
                </a-popover>
              </div>
            </template>
          </a-trigger>
          <a-tooltip content="复制语句" position="top">
            <icon-copy :style="{fontSize:'20px'}" style="cursor: pointer;margin-right: 10px;" @click="copy"/>
          </a-tooltip>
          <a-tooltip content="添加书签" position="top">
            <icon-subscribe :style="{fontSize:'20px'}" style="cursor: pointer;margin-right: 10px;" @click="showbookmark"/>
          </a-tooltip>
          <a-button type="primary" :disabled="selectActive" style="margin-right: 25px;border-radius: 4px;" :loading="loading" @click="submit">计算</a-button>
        </a-space>
      </div>
    </div>

  </a-collapse>
  <sqlhelp ref="sqlhelpRef"/>
  <a-modal v-model:visible="visiblebookmark" ok-text="添加" @ok="handleOkbookmark" @cancel="visiblebookmark = false">
    <template #title>
      添加书签
    </template>
    <div>
      <a-input v-model="bookmarkValue" allow-clear>
        <template #prepend>
          书签名
        </template>
      </a-input>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.flex-container {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 12px 18px 12px 20px;
  line-height: 32px;
}

.leftDiv {
  display: flex;
  flex-direction: column;
}

.rightDiv {
  padding-bottom: 2px;
}

:deep(.ͼ1.cm-focused) {
  outline: 0;
}

.moreList {
  margin-right: 10px;
}

.moreList:hover {
  background-color: #ebebec;
  border-radius: 5px;
}

.listSpan {
  cursor: pointer;
  margin-top: 5px;
  margin-right: 15px;
  margin-left: 15px;
  font-size: 14px;
  font-weight: 500;
}

.add-param {
  margin: 8px 4px;
  padding: 0 4px;
  color: var(--tant-primary-color-primary-default);
  border-radius: 2px;
  cursor: pointer;
  transition: all .3s;
}

.add-param-icon {
  margin-right: 8px;
  padding: 4px;
  color: var(--tant-primary-color-primary-default);
  font-size: 12px;
  background: var(--tant-primary-color-primary-fill);
  border-radius: 4px;
}

.add-param:hover {
  background-color: var(--tant-disabled-color-disabled-fill);
}

.add-param-pop {
  display: flex;
  flex-direction: column;
  padding: 4px;
  width: 175px;
  background-color: var(--tant-bg-white-color-bg1-1)
}

.param-select {
  margin: 0 20px;
}
.param-number{
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: rgb(126, 127, 128);
  line-height: 24px;
  text-align: center;
  background-color: var(--color-fill-2);
  border-radius: 4px;
}
.param-title {
  font-weight: 500;
  margin-right: 10px;
}

:deep(.arco-picker) {
  background-color: var(--tant-bg-white-color-bg1-1) !important;
  border: 1px solid var(--tant-border-color-border1-1) !important;
}

:deep(.arco-picker:hover) {
  background-color: var(--tant-bg-white-color-bg1-1) !important;
  border: 1px solid var(--tant-primary-color-primary-hover) !important;
}

.spanHover {
  cursor: pointer;
  background-color: #fff;
  position: relative;
  display: flex;
  padding: 8px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  border-radius: 4px;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.spanHover:hover {
  background-color: #f1f1f1;
  border-radius: 5px;
}

.arco-collapse-item {
  padding: 10px 20px 0;
}

.selectorItems {
  cursor: pointer;
  color: #1e76f0;
  margin-top: 5px;
}

.menuItem {
  border-bottom: 1px solid var(--color-neutral-3);
}
</style>