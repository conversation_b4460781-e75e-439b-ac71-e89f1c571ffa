<template>
  <div id="funnelRoot">
    <div class="analyse-content">
      <analyse-header
        :model="ReportAnalyseModel.FUNNEL"
        :query-param="queryParam"
        :header-info="headerInfo"
        :report-data="reportData"
        @call-computed-data="computedData"
        @update-report-form="updateReportForm"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="dataLoading" class="box">
                <div v-if="!dataLoading" class="query-condition">
                  <!-- 分析指标 -->
                  <analysisIndex
                      :analysis-index-data="analysisIndexData"
                      :associate-attribute="queryParam.associateAttribute"
                      :event-lists="evtLists"
                      @indicators-change="indicatorsChange"
                      @subject-change="subjectChange"
                      @associate-change="associateChange"
                      @reset-params="reset"/>
                  <!-- 全局筛选 -->
                  <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem :group-item="groupData" :only-one="true" @aggregates-change="aggregatesChange"/>
                  <div style="width: 100%;padding: 0 24px;">
                    <a-divider :margin="5" type="dashed"/>
                  </div>
                  <!-- 用户筛选、 -->
                  <userFilter :user-filter="userFilterData" @filters-change="userFiltersChange"/>
                </div>
                <div class="left-footer">
                  <div style="margin-right: auto">
                    <a-checkbox
                        :model-value="!queryParam.withTimeSeries"
                        style="margin-right: 8px;"
                        @change="withTimeSeriesChange">
                      忽略时序
                    </a-checkbox>
                    <a-tooltip content="勾选后忽略漏斗事件时序，只限制发生在同一时段" position="top">
                      <icon-info-circle size="16"/>
                    </a-tooltip>
                  </div>
                  <a-button @click="toSave">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="datePick"/>
                      </div>
                      <div v-if="queryParam.withTimeSeries" style="display: flex;align-items: center;">
                        分析窗口期：
                        <div style="display: flex;align-items: center;">
                          <a-dropdown @select="windowSelect">
                            <div class="select-label" :style="{border:'1px solid var(--tant-border-color-border1-1)'}">
                            {{ windowName }}<icon-down/>
                            </div>
                            <template #content>
                              <a-doption value="D">当天</a-doption>
                              <a-dsubmenu>
                                <template #default>天（即24小时）</template>
                                <template #content>
                                  <a-doption value="1d">1天</a-doption>
                                  <a-doption value="7d">7天</a-doption>
                                  <a-doption value="14d">14天</a-doption>
                                  <a-doption :value="`${dValue}d`" style="width: 150px;">
                                    <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                      <a-input-number v-model:model-value="dValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0"/>
                                      <div>天</div>
                                    </div>
                                  </a-doption>
                                </template>
                              </a-dsubmenu>
                              <a-dsubmenu>
                                <template #default>小时</template>
                                <template #content>
                                  <a-doption value="1h">1小时</a-doption>
                                  <a-doption value="3h">3小时</a-doption>
                                  <a-doption value="12h">12小时</a-doption>
                                  <a-doption :value="`${hValue}d`" style="width: 150px;">
                                    <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                      <a-input-number v-model:model-value="hValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0"/>
                                      <div>小时</div>
                                    </div>
                                  </a-doption>
                                </template>
                              </a-dsubmenu>
                              <a-dsubmenu>
                                <template #default>分钟</template>
                                <template #content>
                                  <a-doption value="1m">1分钟</a-doption>
                                  <a-doption value="5m">5分钟</a-doption>
                                  <a-doption value="60m">30分钟</a-doption>
                                  <a-doption :value="`${mValue}m`" style="width: 150px;">
                                    <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                      <a-input-number v-model:model-value="mValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0"/>
                                      <div>分钟</div>
                                    </div>
                                  </a-doption>
                                </template>
                              </a-dsubmenu>
                            </template>
                          </a-dropdown>
                          <a-tooltip content="用户触发步骤1起，在窗口期内完成后续步骤，算作后续步骤的转化" position="right">
                            <icon-info-circle style="margin-left: 4px;"/>
                          </a-tooltip>
                        </div>
                      </div>
                    </a-space>
                    <div>
                      <a-button-group style="background-color: #fff;margin-right: 20px;">
                        <a-popover title="转化图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="eventData?.funnelQueryResult.length == 0 || isChartType.includes('distribution')" @click="selectChart('distribution')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示每个步骤间的转化情况 <br>
                            <img :src="distribution" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover>
                        <!-- <a-popover title="趋势图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="eventData?.funnelQueryResult.length == 0 || isChartType.includes('trend')" @click="selectChart('trend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示转化率随时间的变化趋势 <br>
                            <img :src="trend" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover> -->

                      </a-button-group>
                      <!-- <a-tooltip content="可视化配置">
                        <a-button :disabled="!eventData.funnelQueryResult?.length" @click="onCollapse">
                          <template #icon>
                            <icon-settings/>
                          </template>
                        </a-button>
                      </a-tooltip> -->
                    </div>
                  </div>
                  <div v-if="eventData?.funnelQueryResult.length > 0" class="right-content">
                    <a-layout style="height: 490px;width: 100%" :style="showDrawer?'':''">
                      <a-layout-content v-if="!loading">
                        <a-alert v-if="eventData?.resultsExceedsLimit" style="margin-top: 5px;">
                          因数据条数过多，优先展示前1000条数据。建议修改分组项或添加更严格的过滤项以减少数据条数，或点击右上角下载全量数据。
                        </a-alert>
                        <event-echars-vue ref="eventechars" :event-data="eventData" :showlabel="showlabel" :y-max="yMax" :y-min="yMin"/>
                      </a-layout-content>
                      <a-layout-footer v-if="!loading">
                        <event-table-vue ref="eventtable" :event-data="eventData" :step-value-name="stepValueName" :chart-type="isChartType" :step-events="stepEvents"/>
                      </a-layout-footer>
                    </a-layout>
                  </div>
                  <div v-if="!loading && eventData?.funnelQueryResult.length == 0" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div class="drawer" :style="showDrawer ? {width: '320px'} : {width: '0px'}">
                <div class="drawer-header">
                  <div class="title">可视化配置</div>
                  <div class="icon">
                    <a-button @click="() => showDrawer = false">
                      <template #icon>
                        <icon-double-right/>
                      </template>
                    </a-button>
                  </div>
                </div>
                <a-collapse :default-active-key="['1']" expand-icon-position="right" class="customStyle" :bordered="false" @change="handleChangeCollapse">
                  <a-collapse-item key="1">
                    <template #header>
                      <icon-settings/>
                      通用配置
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示数值</span>
                          <a-tooltip content="仅对趋势图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                        <div class="title">
                          <span>步骤展示</span>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showlabel" size="small"/>
                        </div>
                        <div class="title">
                          <a-cascader v-model:model-value="stepValue" :options="options" :format-label="format" @change="stepChange"/>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                </a-collapse>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
    <!-- 保存弹窗 -->
    <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">保存报表</div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <a-form-item field="dashboard" label="保存至看板">
          <dashboard-select v-model:selected="form.dashboard" type="dashboard" @change="dashboardChange"/>
        </a-form-item>
        <a-form-item field="description" label="备注">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5 }"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click.stop="handleSaveCancel">取消</a-button>
        <a-button type="primary" :loading="saveLoading" @click="saveReport">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {h, provide, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {distribution} from '@/views/dashboard/components/img';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {ChartType} from "@/api/enum";
import {detailReport} from "@/api/report/api";
import {cloneDeep, debounce, isEmpty} from "lodash";
import {DateRange, ReportAnalyseModel} from "@/api/analyse/type";
import {queryFunnelReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {toolStore} from '@/store';
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from 'vue-router';
import router from "@/router";
import userFilter from "@/views/analyse/components/UserFilter.vue"
import {ROUTE_NAME} from "@/router/constants";
import {getDefaultObj, removeEnumList} from "@/views/analyse/components/util/verify";
import analysisIndex from "./components/analysisIndex.vue"
import globalFilter from "../components/globalFilter.vue"
import groupItem from "../components/groupItem.vue"
import eventTableVue from "./components/TableView.vue";
import eventEcharsVue from "./components/EcharsView.vue";
import analyseHeader from "../components/analyseHeader.vue"

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const paramsDataStorage = useSessionStorage("funnel-params-data", {});
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
const route = useRoute();
const reportId = ref(route.query.code);
const headerInfo = reactive({
  title: '漏斗分析',
  img: '/icon/topMenu/funnel.svg',
  tips: '以某段时间做过步骤1的用户为样本，查看窗口期内，指定步骤下用户的转化情况',
  root: '#funnelRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true,
  showAppSelect: true
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
const stepValue = ref('all')
const stepValueName = ref('全步骤')
const stepEvents = ref<any>([])
const options = ref<any>([]);
const format = (options) => {
  const labels = options.map(option => option.label)
  stepValueName.value = labels.join('到')
  return labels.join('到')
}
const stepChange = (v) => {

}
const analysisIndexData = ref<any>([])
const globalFilterData = ref({}) // 全局筛选传参
const groupData = ref<any>([]) // 分组项传参
const userFilterData = ref({}) // 用户筛选传参
const evtLists = ref<any>([])
const toolData = toolStore();
const dValue = ref(30)
const hValue = ref(24)
const mValue = ref(60)
const windowName = ref('')
// ------left start
const loading = ref(false)
const isReset = ref(false)
// 传参数组
const queryParam = reactive({
  eventList: [], // 事件列表
  associateAttribute: {}, // 关联属性
  filter: {}, // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  }, // 查询日期范围
  timeSpan: {
    number: 0,
    unit: 'DAY'
  }, // 分析窗口期
  funnelType: 'conversion', // 漏斗类型
  withTimeSeries: false,
  userFilter: {},// 用户筛选条件
})
const dataLoading = ref(false)
const requestId = ref()
const eventData = ref({
  y: [],
  funnelQueryResult: []
})
const eventBus = useEventBus('eventList');

const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const saveFormRef = ref();
const subjectName = ref('')
// const eventParamsList = ref() // 已有event事件列表传值
const handleAttrBus = debounce(async () => {
  const eventLists = queryParam.eventList
  eventBus.emit(eventLists);
  form.value.name = `${queryParam.eventList[0]?.eventDisplayName}到${queryParam.eventList[queryParam.eventList.length - 1]?.eventDisplayName}的${subjectName.value}转化漏斗`
}, 300)
// provide('eventParamsList', eventParamsList);
// 处理步骤options

const handleStepOptions = () => {
  // 处理可视化配置步骤展示options数据
  const length = stepEvents.value.length
  options.value = []
  options.value.push({
    value: 'all',
    label: '全步骤',
  })
  stepValue.value = 'all'
  if (length > 0) {
    // 生成其他步骤选项
    for (let i = 1; i < length; i++) {
      const option = {
        value: String(i),
        label: `步骤${i}`,
        children: [] as any
      };
      // 为当前步骤添加后续步骤作为子选项
      for (let j = i + 1; j <= length; j++) {
        option.children.push({
          value: `${i}-${j - i}`,
          label: `步骤${j}`
        });
      }
      // 只有当有子选项时才添加children属性
      if (option.children.length === 0) {
        delete option.children;
      }
      options.value.push(option);
    }
  }
}
// 分析指标传参
const indicatorsChange = (v) => {
  queryParam.eventList = v
  handleAttrBus()
  paramsDataStorage.value = queryParam
}

// 关联属性传参
const associateChange = (v) => {
  queryParam.associateAttribute = v
  paramsDataStorage.value = queryParam
}
const subjectChange = (v) => {
  subjectName.value = v.subjectName
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
  paramsDataStorage.value = queryParam
}
// 用户筛选传参
const userFiltersChange = (v) => {
  queryParam.userFilter = v
  paramsDataStorage.value = queryParam
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
  paramsDataStorage.value = queryParam
}

// 忽略时序
const withTimeSeriesChange = (v) => {
  queryParam.withTimeSeries = !v
  paramsDataStorage.value = queryParam
}

// 查询校验
const timeOut = ref()
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
   if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取三个需要验证的列表
  const firstList = params.eventList
      .filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.eventList
      .filter(event => event.filter?.filters?.length > 0)
      .map(event => event.filter.filters)
  // 添加对 userFilter 的校验
  const userFilterList = params?.userFilter?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.userFilter?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userFilter?.userCondition?.filters || [];
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList) &&
      verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};

// 计算
const computedData = () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.warning('事件查询超时，请重试！')
    }
  }, 60*1000)
  const params = {...queryParam}
  if (paramsVerify(params)) {
    if (Object.keys(params.associateAttribute).length === 0) {
      params.associateAttribute = undefined
    }
    requestId.value = queryFunnelReportData(removeEnumList(params));
  } else {
    Message.error('筛选条件参数错误')
    loading.value = false
  }
  // requestId.value = queryFunnelReportData(queryParam);

}
watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (newData.result === null) {
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }

  timeOut.value && clearTimeout(timeOut.value)
  eventData.value = newData.result
  stepEvents.value = newData.result?.stepEvents || []
  handleStepOptions()
  paramsDataStorage.value = queryParam
  loading.value = false
})
watch(() => queryParam.timeSpan, (newValue, oldValue) => {
  if (newValue) {
    if (newValue.unit === 'DAY') {
      windowName.value = newValue.number === 0 ? '当天' : `${newValue.number}天`
    } else if (newValue.unit === 'HOUR') {
      windowName.value = `${newValue.number}小时`
    } else if (newValue.unit === 'MINUTER') {
      windowName.value = `${newValue.number}分钟`
    }
  }
}, {immediate: true, deep: true})
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}

const handleSaveCancel = () => {
  saveVisible.value = false;
}
const saveLoading = ref(false)
const editReportData = reactive({
  name: '',
  description: ''
})

const dashboardData = ref()
const dashboardChange = (v) => {
  dashboardData.value = v
}
// 保存报表
const saveReport = debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    return;
  }
  saveLoading.value = true;
  try {
    const saveParams = {
      model: ReportAnalyseModel.FUNNEL,
      reportId: reportId.value || undefined,
      dashboard: form.value.dashboard,
      name: editReportData?.name || form.value.name,
      description: editReportData?.description || form.value.description,
      queryParam,
      chartParams: [],
      chartType: ChartType.TABLE
    };
    await saveAnalyseReportList(
        saveParams.model,
        saveParams.reportId,
        saveParams.dashboard,
        saveParams.name,
        saveParams.description,
        saveParams.queryParam,
        saveParams.chartParams,
        saveParams.chartType
    );
    dashboardSelected.value = { ...dashboardData.value }
    const successMsg = isEmpty(form.value.dashboard)
        ? h('span', [
          '报表已保存'
        ])
        : h('span', [
          '报表已保存并添加至看板，',
          h('a', {
            style: {color: 'rgb(var(--primary-6))', cursor: 'pointer'},
            onClick: () => router.push({
              name: ROUTE_NAME.DASHBOARD,
            }),
          }, '去看板查看')
        ]);
    Message.success({
      content: () => successMsg,
      duration: 3000
    });
    eventBus.emit('saveReport');
    if (!reportId.value) {
      saveVisible.value = false;
    }
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300);
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  saveReport()
}
const toSave = () => {
  if (reportId.value) {
    saveReport()
  } else {
    saveVisible.value = true
  }
}
// ---left end

// ------right start

const boardDate = ref<DateRange>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
provide('boardDate', boardDate)
const triggerVisible = ref<boolean>(false)
const datePick = (date: any) => {
  boardDate.value = date
  queryParam.dateRange = date;
  triggerVisible.value = false
  computedData()
};
const windowSelect = (v) => {
  const numbers = v.match(/\d+/g)?.join(',');
  const timeData = {
    number:0,
    unit:'DAY'
  }
  if (v === 'D') {
    windowName.value = '当天'
    timeData.number = 0
    timeData.unit = 'DAY'
  } else if (v.includes('d')) {
    windowName.value = `${numbers}天`
    timeData.number = Number(numbers)
    timeData.unit = 'DAY'
  } else if (v.includes('h')) {
    windowName.value = `${numbers}小时`
    timeData.number = Number(numbers)
    timeData.unit = 'HOUR'
  } else if (v.includes('m')) {
    windowName.value = `${numbers}分钟`
    timeData.number = Number(numbers)
    timeData.unit = 'MINUTE'
  }
  queryParam.timeSpan = timeData
  paramsDataStorage.value = queryParam
  computedData()
}
const showDrawer = ref<boolean>(false)
const eventtable = ref()
const eventechars = ref()
const isChartType = ref<string>('distribution')
const showlabel = ref<boolean>(false)
const yMax = ref<number | null>(null)
const yMin = ref<number | null>(null)

const handleChangeCollapse = (key: any) => {
  if (key.includes(2)) {
    eventechars.value.changeType('distribution')
  }
}

const onCollapse = () => {
  showDrawer.value = !showDrawer.value;
};
const selectChart = (type: string) => {
  isChartType.value = type
  eventechars.value.changeType(type)
}

const reportData = ref()
const init = async () => {
  if (dataLoading.value){
    // 正在初始化中
    return
  }
  dataLoading.value = true
  checkedSessionGroups.value = []
  toolData.updateTemporaryList([])
  evtLists.value = (await toolData.fetchEventModalList()).flatMap(category => category.items || []);
  // 切换应用时重新请求全部属性
  if(toolData.totalEvtIndCodes.length){
    await toolData.fetchAllAttrList()
  }
  let parsedData = {} as any
  if (reportId.value) {
    await detailReport(reportId.value as string).then(res => {
      parsedData = res.queryParam
      parsedData.dateRange = boardDate.value
      reportData.value = res
      editReportData.name = res?.name
      editReportData.description = res?.description
    })
  }
  // 如果没有从报表获取到数据，则尝试从会话存储获取
  if (Object.keys(parsedData).length === 0) {
    parsedData = paramsDataStorage.value
  }
  // 处理分析指标
  if (parsedData?.eventList && parsedData.eventList?.length > 0) {
    analysisIndexData.value = cloneDeep(parsedData.eventList)
  }
  if (!analysisIndexData.value.length) {
    const appOpenData = getDefaultObj(evtLists.value)
    const defalutData = {
      eventName: appOpenData.name,
      eventDisplayName: appOpenData.displayName,
      eventCode: appOpenData.code,
      eventType: appOpenData.type,
      type: appOpenData.objectType,
      eventAttrCode: '',
      eventAttrName: appOpenData.objectType === 'event' ? '总次数' : '',
      filter: {}
    }
    analysisIndexData.value.push({...defalutData}, {...defalutData})
  }
  queryParam.eventList = cloneDeep(analysisIndexData.value)
  // 处理事件筛选
  if (parsedData.filter) {
    globalFilterData.value = parsedData.filter
    queryParam.filter = parsedData.filter
  }
  // 处理分组项
  if (parsedData.aggregates && parsedData.aggregates.length > 0) {
    groupData.value = cloneDeep(parsedData.aggregates)
    queryParam.aggregates = cloneDeep(parsedData.aggregates)
  }
  // 处理用户筛选条件
  if (parsedData.userFilter) {
    userFilterData.value = parsedData.userFilter
    queryParam.userFilter = parsedData.userFilter
  }
  // 处理时间
  if (parsedData.dateRange) {
    boardDate.value = parsedData.dateRange
    queryParam.dateRange = parsedData.dateRange
  }else{
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    boardDate.value = date
    queryParam.dateRange = date
  }
  // 处理分析窗口期
  if (parsedData.timeSpan) {
    queryParam.timeSpan = parsedData.timeSpan
  }
  // 处理关联属性
  if (parsedData.associateAttribute) {
    queryParam.associateAttribute = parsedData.associateAttribute
  }
  // 处理时序选择
  if (parsedData.withTimeSeries !== undefined) {
    queryParam.withTimeSeries = parsedData.withTimeSeries
  }
  
  dataLoading.value = false
  setTimeout(() => {
    computedData()
  }, 1000)
}
init()

localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    init()
  }
})
const reset = () => {
  isReset.value = true
  analysisIndexData.value = []
  globalFilterData.value = {}
  queryParam.filter = {}
  groupData.value = []
  paramsDataStorage.value = {}
  queryParam.associateAttribute = {}
  userFilterData.value = {}
  queryParam.userFilter = {}
  queryParam.timeSpan = {
    number: 0,
    unit: 'DAY'
  }
  init()
}
</script>

<style scoped lang="less">
#funnelRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}


.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;
}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  text-align: right;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  //padding-bottom: 18px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-content::before, .right-content::after {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-content::after {
  right: -24px;
  left: auto;
}

.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}
.select-label{
  display: inline-block;
  height: 32px;
  padding: 0 8px 0 8px;
  color: var(--tant-text-gray-color-text1-2);
  line-height: 32px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: 4px;
  cursor: pointer;
  transition: all .3s;
  white-space: nowrap;
}
</style>