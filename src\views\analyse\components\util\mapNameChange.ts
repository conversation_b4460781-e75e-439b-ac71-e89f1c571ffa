import { toolStore} from '@/store';

const toolData = toolStore();
/**
 * 批量更新 eventList 中的 displayName 字段,如在指标编辑中更改了显示名，路由返回后根据code比对，及时更新缓存中的displayName
 * @param analysisList 需要处理的指标数组（如 analysisIndexData.value）
 */
export function updateDisplayNames(analysisList: any[]) {
    const mapList = toolData.toolModelList.flatMap(category => category.items || []);
    if (!Array.isArray(analysisList) || !Array.isArray(mapList)) return;
  
    analysisList.forEach(item => {
      if (!Array.isArray(item.eventList)) return;
      item.eventList.forEach(evt => {
        if (evt.type === 'event') {
          const match = mapList.find(m => m.code === evt.eventCode);
          if (match) {
            evt.eventDisplayName = match.displayName;
          }
        } else if (evt.type === 'indicator') {
          const match = mapList.find(m => m.code === evt.indicatorCode);
          if (match) {
            evt.indicatorDisplayName = match.displayName;
          }
        }
      });
    });
  }