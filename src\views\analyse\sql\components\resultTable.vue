<script setup lang="ts">
import {defineExpose, ref} from 'vue';
import useChartOption from "@/hooks/chart-option";
import {AnyObject} from "@/types/global";
import router from "@/router";
import * as XLSX from 'xlsx';
import {ROUTE_NAME} from "@/router/constants";


const columns = ref<any>([]);
const data = ref<any>([]);
const res = ref()
const img = 'data:image/png;base64,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'
const echarsOption = ref()
const isChart = ref(true)
const visibleDel = ref(false)
const resultData = ref()


const xAxis = ref([]);
const ySeries = ref([]);
const legendData = ref([]);
const isTable = ref(false)
const newColumns = ref()
const newTabledata = ref()
const isconversionTable = ref(true)

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '4.6%',
      right: '4.6%',
      top: '60',
      bottom: '80',
    },
    legend: {
      data: legendData.value,
      bottom: '19'
    },
    xAxis: {
      type: 'category',
      offset: 2,
      data: xAxis.value,

      axisLabel: {
        color: '#4E5969'
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },

      axisPointer: {
        show: true,
        lineStyle: {
          color: '#23ADFF',
          width: 2,
        },
      },
    },
    yAxis: [{
      type: 'value'
    },{
      type: 'value',
      position: 'right',
    }],
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

const sendResult = (value:any)=>{
  resultData.value = value
  columns.value = []
  data.value = []

  // const keys = Object.keys(value[0])
  const keys = value?.headers
  keys.forEach((item,index) => {
    if (index === 0) {
      columns.value.push({
        fixed :'left',
        title: keys[index],
        dataIndex: keys[index]
      })
    }else {
      columns.value.push({
        title: keys[index],
        dataIndex: keys[index],
        ellipsis: true,
        tooltip: true
      })
    }
  })
  columns.value.unshift({
    fixed:'left',
    title: '序号',
    dataIndex: 'index'
  })
  //  value?.rows.forEach((item:any,index:number) => {
  //    item.index = index+1
  //    data.value.push( keys.map((item:any,index:number)=>{  return  keys[index]:     } )       )
  // })
  data.value = value?.rows.map((subArray:any,index:number) => {
    const rowObject = subArray.reduce((accumulator:any, currentValue:any, index:number) => {
      accumulator[keys[index]] = currentValue;
      return accumulator;
    }, {});
    rowObject.index = index + 1
    return rowObject
  });
}

const transTable = ()=>{

  newColumns.value=[]
  newTabledata.value = []
  ySeries.value.forEach(series => {
    newColumns.value.push({ title: series.name, dataIndex: series.name, key: series.name });
  });
  xAxis.value.forEach((xValue, index) => {
    const rowData = { x: xValue };
    ySeries.value.forEach(series => {
      rowData[series.name] = series.data[index];
    });
    newTabledata.value.push(rowData);
  });
}

const refreshTheChartData = (value:any) => {
  if (value.length === 0){
    return
  }
  if (value[0]==='table'){
    isTable.value = true
    newColumns.value = value[1]
    newTabledata.value = value[2]
    isconversionTable.value = value[3]

  }else {
    chartOption.value.xAxis.axisPointer.show = false
    isTable.value = false
    xAxis.value = value[1]
    ySeries.value = value[2]
    legendData.value = value[3]
  }
  isChart.value = false
}
const chartsDel = ()=>{
  xAxis.value = []
  ySeries.value = []
  legendData.value = []
  isChart.value = true
}
const editCharts = ()=>{
  // if (localStorage.getItem('isInject')){
  //   localStorage.removeItem('isInject')
  // }
  router.push({name: ROUTE_NAME.ANALYSE_CUSTOM_SQL_CHART})
}
const formatTimestamp= (timestamp:any,s?:boolean)=> {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  data.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      if (!dataIndex) return '';
      
      const value = item[dataIndex];
      // 处理科学计数法的数字
      if (typeof value === 'number' && ['ut', 'st', 'it'].includes(dataIndex)) {
        return value.toString(); // 转为字符串以保持完整数字
      }
      return value;
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `spl查询结果_${formatTimestamp(resultData.value?.startTime).replace(/:/g, '-')}.xlsx`);
};

defineExpose({
  sendResult,
  refreshTheChartData,
  chartsDel,
  exportXlsx
})

</script>

<template>
  <div style="margin: 20px 25px 45px 25px;">
<!--    <div v-show="data.length>0">-->
<!--      SQL_ID：{{res?.data.queryId}} 查询时间：{{res?.data.finishedTime}} 耗时：{{res?.data.elapsedTimeMillis}}ms 结果： {{data?.length}}行-->
<!--    </div>-->
    <div style="display: flex;justify-content: space-between;">

    <div v-show="data.length>0">
      结果： {{data?.length}}行 &nbsp;&nbsp;&nbsp; 查询时间：{{formatTimestamp(resultData?.startTime)}}&nbsp;&nbsp;&nbsp;  耗时：{{resultData?.endTime - resultData?.startTime}}ms
    </div>

    <div v-show="newTabledata?.length>0||data?.length>0&&xAxis?.length!==0">
      <a-radio-group v-model="isChart" type="button">
        <a-tooltip content="明细数据">
          <a-radio :value="true"><icon-apps /></a-radio>
        </a-tooltip>
        <a-tooltip content="图表">
          <a-radio :value="false"><icon-bar-chart /></a-radio>
        </a-tooltip>
      </a-radio-group>
     <br>
      <a-space>
      <a-tooltip content="编辑图表">
        <div class="iconCharts" @click="editCharts">
          <icon-edit v-show="!isChart" style="width: 15px;height: 15px;" />
        </div>
      </a-tooltip>
      <a-tooltip content="删除图表">
        <div class="iconCharts" @click="visibleDel = true">
          <icon-delete v-show="!isChart" style="width: 15px;height: 15px;"/>
        </div>
      </a-tooltip>
      </a-space>
    </div>

    </div>
    <br><br>
    <div v-if="data?.length>0 && isChart">
      <a-table :columns="columns" :data="data" column-resizable :bordered="{cell:true}" :scroll="columns.length>7? { x: 120*columns.length }:false"/>
    </div>
    <div v-else-if="data?.length>0 && !isChart">
      <a-spin  style="width: 100%;height: 46vh;">
        <Chart v-if="!isTable" :option="chartOption"/>
        <a-table v-if="isTable" :show-header="isconversionTable" :columns="newColumns" style="padding: 0px 16px;" :data="newTabledata" column-resizable :bordered="{cell:true}" :scroll="newColumns.length>7? { x: 120*newColumns.length }:false"/>
      </a-spin>
    </div>
    <div v-else class="emty" >
      <div>
        <img :src="img" style=""/>
        <br><span class="span">暂无数据</span>
      </div>
    </div>
    <a-modal v-model:visible="visibleDel" width="auto" @ok="chartsDel" @cancel="visibleDel = false">
      <template #title>
        删除图表
      </template>
      <div>确认删除图表吗？该操作不可恢复</div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.span{
  color: var(--tant-text-gray-color-text1-3);
  font-size: 12px;
  width: 400px;
  word-break: break-all;
  margin: 0 auto;
}
.emty{
  text-align: center;
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  img{
    width: 56px;
    height: 56px;
  }
}
.iconCharts{
  width: 23px;
  height: 23px;
  border-radius: 5px;
  margin: 10px 4px 0 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    background-color: #f1f2f5;
  }
}
//.iconCharts:hover{
//  background-color: #f1f2f5;
//}
</style>