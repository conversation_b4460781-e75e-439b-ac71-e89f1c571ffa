<template>
    <div class="action-row">
      <div class="filter-row-eventRow">
        <div class="action-left">
          <div class="row-content">
            <div v-for="(event,eventIndex) in indicatorData.eventList" :key="eventIndex" class="event-item">
              <div class="left-index" @click="indexClick(numberToUpperLetter(eventIndex))">
                  <div class="index-text">{{ numberToUpperLetter(eventIndex) }}</div>
              </div>
              <EventIndicatorSelect :no-fetch-attr="true" :disabled="props.disabled" :panel-data="event" @analysis-index-change="panelSelectChange(eventIndex,$event)"/>
              <eventScreenPopup v-if="indicatorData.eventList.length>1" :info="event" :read-only="props.disabled" @event-screen="eventScreen(eventIndex,$event)"/>
              <div class="element-action">
                <a-tooltip content="删除" position="top">
                  <a-button v-if="indicatorData.eventList.length>1" class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteItem(eventIndex)">
                    <template #icon>
                      <icon-close-circle/>
                    </template>
                  </a-button>
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="action-right">
          <a-space align="center">
            <a-tooltip content="添加筛选" position="top">
              <a-button v-if="!props.disabled" class="btn-bg btn-26" @click="add()">
                <template #icon>
                  <icon-filter/>
                </template>
              </a-button>
            </a-tooltip>
          </a-space>
        </div>
      </div>
      <eventQueryFilter
          :ref="el => queryFilterRefs = el"
          :filter="indicatorData.filter"
          :show-detail-filter="true"
          :disabled="props.disabled"
          @query-filters-change="queryFiltersChange($event)"/>
      <div v-if="!props.disabled" class="ta-filter-button" @click="addEvent()">
        <icon-plus class="action"/>
        <div class="label">指标事件</div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import {ref, watch} from "vue";
  import _ from 'lodash';
  import {Indicator} from "@/api/analyse/type";
  import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
  import eventQueryFilter from "@/views/analyse/components/operation/eventQueryFilter.vue"
  import eventScreenPopup from "@/views/analyse/components/operation/eventScreenPopup.vue"
  import {numberToUpperLetter} from "@/utils/number-util";
  import {toolStore} from "@/store";

  const toolData = toolStore()
  const props = defineProps({
    indicatorListStore: {
      type: Array,
      default() {
        return [];
      },
    },
    initData: {
      type: Object,
    },
    disabled:{
      type:Boolean,
      default: () => false
    },
  })
  
  const indicatorData = ref<any>()
  
  const queryFiltersChange = (v) => {
    indicatorData.value.filter = v;
  }
  
  const init = async () => {
    // const indicatorList = _.cloneDeep(props.indicatorListStore);
    const indicatorList = toolData.toolModelList.flatMap(category => category.items || [])
    const defaultIndicator = {
      name: indicatorList[0]?.name,
      code: indicatorList[0]?.code,
      type: 'operation',
      displayType: {},
      displayName: `${indicatorList[0]?.displayName}`,
      unitName: '',
      eventList: [
        {
          eventName: indicatorList[0]?.name,
          eventDisplayName: indicatorList[0]?.displayName,
          eventCode: indicatorList[0]?.code,
          type: 'indicator',
          filter: {}
        },
      ],
      filter: {}
    }
    // 
    if (props.initData?.eventList?.length > 0) {
      defaultIndicator.eventList = _.cloneDeep(props.initData?.eventList).map(item => {
        return {
          eventName: item.indicatorName,
          eventDisplayName: item.indicatorDisplayName,
          eventCode: item.indicatorCode,
          type: item.type,
          filter: item.filter
        }
      })
    }
    indicatorData.value = _.cloneDeep(defaultIndicator)
  }
  
  init()
  const addEvent = () => {
    const [firstEventValue] = toolData.toolModelList.flatMap(category => category.items || [])
    const firstEvent = firstEventValue
    const eventItem = {
      eventName: firstEvent.name,
      eventCode: firstEvent.code,
      eventDisplayName: firstEvent.displayName,
      type: firstEvent.objectType,
      eventType: firstEvent.type,
      eventAttrCode: '',
      eventAttrName: firstEvent.objectType === 'event' ? '总次数' : '',
      filter: {}
    };
    indicatorData.value.eventList.push(eventItem);
  }
  const emits = defineEmits(['indicatorsChange', 'deleteList', 'openEditModal','indexChange'])
  
  const indicator = ref<Indicator[]>([])
  
  // 快捷点击
  const indexClick = (val) => {
    if(props.disabled) return
    emits('indexChange', val)
  }
  const panelSelectChange = (eventIndex: number, e: any) => {
    const filter = {
        ...indicatorData.value.eventList[eventIndex],
        ...e
    }
    const fieldsToRemove = e.type === 'indicator'
        ? ['eventCode', 'eventName', 'eventDisplayName']
        : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

    const cleanedFilter = _.omit(filter, fieldsToRemove);
    indicatorData.value.eventList[eventIndex] = cleanedFilter;
 }
  const eventScreen = (eventIndex: number, e: any) => {
    indicatorData.value.eventList[eventIndex].filter = e;
  }
  const deleteItem = (index: number) => {
    indicatorData.value.eventList.splice(index, 1)
    if(indicatorData.value.eventList.length === 1) {
      indicatorData.value.eventList[0].filter = {}
    }
  }
  // 添加并列条件
  const queryFilterRefs = ref<any>()
  const add = () => {
    queryFilterRefs.value.add()
  }
  
  watch(indicatorData, (newValue, oldValue) => {
    indicator.value = {
      displayName: newValue.displayName,
      displayType: newValue.displayType,
      name: newValue.name,
      type: newValue.type,
      formula: newValue.formula,
      eventList: newValue.eventList.map(item => {
        return {
          indicatorName: item.indicatorName || item.eventName,
          indicatorDisplayName: item.indicatorDisplayName || item.eventDisplayName,
          indicatorCode: item.indicatorCode || item.eventCode,
          type: item.type,
          filter: item.filter
        }
      }),
      filter: newValue.filter
    }
    emits('indicatorsChange', indicator.value)
  }, {deep: true, immediate: true})
  
  </script>
  
  <style scoped lang="less">
  .action-row {
    position: relative;
    height: auto;
    width: 100%;
    min-height: 24px;
    line-height: 24px;
    padding-right: 24px;
    padding-left: 24px;
  
    .action-left {
      align-items: flex-start;
      height: auto;
      display: flex;
  
      .row-content {
        flex-grow: 1;
        box-sizing: border-box;
        padding: 4px 0;
  
        .rename {
          min-width: 80px;
          max-width: calc(100% - 50px);
          height: 24px;
          padding: 0;
          line-height: 24px;
          background: inherit;
          margin-bottom: 6px;
          // font-weight: 600;
          font-size: 14px;
          display: flex;
          align-items: center;
  
          .placeholder {
            max-width: 260px;
            display: inline-block;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
            overflow: hidden;
            font-size: 14px;
            // white-space: pre;
            // vertical-align: middle;
            &:hover {
              color: var(--tant-primary-color-primary-default);
            }
          }
  
          :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
            font-weight: 600 !important;
          }
  
          :deep(.arco-input-wrapper) {
            border: none;
            background-color: transparent;
            font-weight: 600;
  
            &:hover {
              border: none;
              background-color: transparent;
              color: var(--tant-primary-color-primary-default);
            }
          }
        }
  
        .event-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding: 4px 0;
          overflow: hidden;
          line-height: 32px;
          white-space: normal;
          .left-index {
                display: flex;
                // width: 40px;
                align-items: flex-start;
                margin: 5px;

                .index-text {
                    display: inline-flex;
                    align-items: center;
                    height: 26px;
                    padding: 0 8px;
                    font-size: 14px;
                    vertical-align: top;
                    background-color: var(--tant-secondary-color-secondary-fill);
                    border: 1px solid transparent;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.3s;
                    box-sizing: border-box;
                }
            }
        }
      }
    }
  
    .action-right {
      position: absolute;
      top: 0;
      right: 24px;
      min-width: 40px;
      height: 36px;
      padding-top: 0 !important;
      display: flex;
      align-items: center;
      opacity: 0;
      transition: opacity .3s;
    }
    .element-action{
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
        margin-left: 8px;
    }
    .filter-btn {
      display: inline-flex;
      align-items: center;
      min-width: 40px;
      max-width: 200px;
      height: 26px;
      padding: 0 8px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: top;
      background-color: var(--tant-secondary-color-secondary-fill);
      border: 1px solid transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: all .3s;
      box-sizing: border-box;
  
      .btn-icon {
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
      }
  
      .filter-label {
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
      }
  
      &:hover {
        border-color: var(--tant-primary-color-primary-hover);
      }
    }
    &:hover {
      background-color: var(--tant-fill-color-fill1-2);
  
      :deep(.filter-btn) {
        background: #fff;
      }
  
      :deep(.filter-icon) {
        background: #fff;
      }
  
      .sub-action-left :deep(.filter-btn) {
        background: #fff;
      }
      :deep(.select-btn) {
        background: #fff;
      }
      .action-right {
        opacity: 1;
      }
      .element-action{
        opacity: 1;
      }
    }
  }
  .ta-filter-button {
    padding: 2px 0;
    display: flex;
    width: 100px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    transition: all .3s;
    margin-bottom: 16px;

    .action {
        border-radius: 4px;
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
        margin-right: 8px;
        padding: 3px;
        font-size: 18px;
    }

    .label {
        color: var(--tant-primary-color-primary-default);
    }

    &:hover .action {
        // background-color: var(--tant-primary-color-primary-fill);
        color: var(--tant-primary-color-primary-active);
    }

    &:hover .label {
        color: var(--tant-primary-color-primary-active);
    }
    }
  .btn-bg {
    background-color: transparent;
  
    &:hover {
      background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
  }
  
  .btn-26 {
    width: 26px;
    height: 26px;
    border-radius: 4px;
  }
  
  .btn-bg-delete {
    background-color: transparent;
  
    &:hover {
      color: var(--tant-status-danger-color-danger-default);
      background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
  }
  </style>