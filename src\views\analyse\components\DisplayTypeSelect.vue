<template>
  <a-trigger
      v-model:popup-visible="visible"
      trigger="click"
      :update-at-scroll="true"
      position="bl"
  >
    <div :class="['select-btn', { 'select-btn-disabled': disabled }]" @click="handleVisible">
      <span class="btn-label">{{ selectedType }}</span>
    </div>
    <template #content>
        <div v-if="!disabled" class="display-content">
            <div class="form">
                <div class="form-item">
                    <div class="label">展示方式:</div>
                    <div class="content">
                        <a-radio-group v-model:model-value="form.type" type="button">
                            <a-radio value="default">数值</a-radio>
                            <a-radio value="percent">百分数</a-radio>
                        </a-radio-group>
                    </div>
                </div>
                <div class="form-item">
                    <div class="label">小数位数:</div>
                    <div class="content">
                        <a-input-number v-model:model-value="form.decimalNum" :min="form.type === 'percent' ? 2 : 0"  :max="4" style="width: 128px;"/>
                    </div>
                </div>
                <div class="form-item">
                    <div class="label"></div>
                    <div class="content">
                        <a-checkbox :value="form.thousandSep" :default-checked="true" disabled>千分位</a-checkbox>
                    </div>
                </div>
            </div>
            <div class="footer">
                <a-button size="mini" style="margin-right: 12px;" @click="visible = false">取消</a-button>
                <a-button size="mini" type="primary" @click="selectType">确定</a-button>
            </div>
        </div>
    </template>
  </a-trigger>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import {getNumberDisplayType} from "@/api/enum";
import {NumberDisplayConfig} from "@/api/analyse/type";

const visible = ref<boolean>(false);
// 使用 v-model 进行数据绑定
const displayType = defineModel<NumberDisplayConfig>("displayType");
const props = defineProps({
    disabled:{
        type: Boolean,
        default: false
    }
})
const emits = defineEmits(['select'])

const form = reactive({
  type: 'default',
  decimalNum: 2,
  thousandSep: 1,
});

// 计算当前选中的类型
const selectedType = computed(() => getNumberDisplayType(displayType.value));

const handleVisible = () => {
    form.type = displayType.value?.type || "default";
    form.decimalNum = displayType.value?.decimalNum ?? 2;
    form.thousandSep = 1;
    visible.value = true;
}
/**
 * 确定
 */
const selectType = () => {
  displayType.value = {
    type: form.type,
    decimalNum: form.decimalNum,
    thousandSep: form.thousandSep
  };
  visible.value = false;
  emits('select', { ...form })
}
</script>

<style scoped lang="less">
.display-content{
  background: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  box-shadow: var(--tant-small-shadow-small-overall);
  padding: 12px;
  .form-item{
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    .label{
      width: 68px;
      margin-right: 12px;
    }
  }
  .footer{
    display: flex;
    justify-content: flex-end;
  }
}
.select-btn-disabled {
    cursor: not-allowed;
}
</style>