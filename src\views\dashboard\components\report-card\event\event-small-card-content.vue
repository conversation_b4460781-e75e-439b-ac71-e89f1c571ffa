<script setup lang="ts">
import {computed, reactive, ref, watch} from 'vue';
import {ReportQueryResponse, TimeParticleSize} from "@/api/type";
import _ from "lodash";
import {IndicatorQueryResultData, IndicatorStageSummary, StageResultData, WsEventAnalysisResultData} from "@/api/report/type";
import useReportDataStore from "@/store/modules/report";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  report: any
}

const reportDataStore = useReportDataStore()
const reportAnalysisData = ref<WsEventAnalysisResultData>();
const props = defineProps<Props>();
const lastData = ref<StageResultData>();
const huanBiData = ref<StageResultData>();
const weekTongBiData = ref<StageResultData>();
const summaryValue = ref<IndicatorStageSummary>();
const huanBi = ref<number>();
const hourTongBi = ref<number>();
const dayTongBi = ref<number>();
const weekTongBi = ref<number>();
const monthTongBi = ref<number>();
const yearTongBi = ref<number>();
const lastDateWeekDay = computed(() => {
  const numMap = ["日","一","二","三", "四","五","六"]
  return numMap[new Date('2025-03-30' || new Date()).getDay()];
})
const timeParticle = reactive<{
  timeParticleSize: string,
  firstDayDfWeek: number | null
}>({
  timeParticleSize: '',
  firstDayDfWeek: null
})
watch(() => reportDataStore, (newVal) => {
  timeParticle.timeParticleSize = reportDataStore.getTimeParticleSizes(props.report.objectCode)
}, {immediate:true, deep: true })
watch(() => props.reportAnalysisData, (newData, oldData) => {
  if (newData === undefined) {
    return
  }
  const result = newData?.result as WsEventAnalysisResultData;
  reportAnalysisData.value = result;
  // 同比环比数据
  if (result?.y?.length === 1 && result?.groupNum === 1) {
    const yData = result?.y[0]?.yData[0] as IndicatorQueryResultData;
    summaryValue.value = yData.summaryValue;
    huanBiData.value = yData.huanBi;
    lastData.value = yData.lastData;
    const tongBiData = yData.tongBi;
    const lastValue = yData.lastData?.yValue;
    const huanBiValue = yData.huanBi?.yValue;
    tongBiData?.forEach(tongBiDataItem => {
      const tongBiValue = tongBiDataItem.yValue;
      if (!lastValue || tongBiValue === undefined) {
        return;
      }
      const tongBi = _.round((lastValue - tongBiValue) / tongBiValue * 100, 2);
      switch (tongBiDataItem.timeParticleSize) {
        case TimeParticleSize.HOUR1:
          // 小时同比，按分钟查询时存在
          hourTongBi.value = tongBi
          break;
        case TimeParticleSize.DAY1:
          // 日同比，按小时查询时存在
          dayTongBi.value = tongBi
          break;
        case TimeParticleSize.WEEK1:
          // 周同比，按天查询时存在
          weekTongBi.value = tongBi
          weekTongBiData.value = tongBiDataItem
          break;
        case TimeParticleSize.MONTH1:
          // 月同比，按天、周查询时存在
          monthTongBi.value = tongBi
          break;
        case TimeParticleSize.YEAR1:
          // 年同比，按天、月、季度查询时存在
          yearTongBi.value = tongBi
          break;
        default:
      }
    })
    if (lastValue && huanBiValue !== undefined) {
      huanBi.value = _.round((lastValue - huanBiValue) / huanBiValue * 100, 2)
    }
  }
})
const formatYValue = (value: any) => {
  if (value === undefined || value === null || Number.isNaN(value)) {
    return '--'; // 如果值未加载或为 NaN，返回 '--'
  }

  const absoluteValue = Math.abs(value);
  const decimalStr = absoluteValue.toString().split(".")[1];

  // 检查小数位数
  if (!decimalStr) {
    return absoluteValue.toString(); // 整数直接返回
  }
  if (decimalStr.length === 1) {
    return absoluteValue.toFixed(1); // 一位小数，保留一位
  }
  return absoluteValue.toFixed(2); // 两位或更多小数，保留两位

};

</script>

<template>
  <div class="card-content">
    <div class="card-date">
      {{ lastData?.xValue || '--' }}({{ lastDateWeekDay }})
    </div>
    <div class="card-value"> {{ formatYValue(lastData?.yValue) }}<em class="value-unit"></em></div>
    <div v-if="timeParticle.timeParticleSize !== TimeParticleSize.TOTAL" class="card-trends">
      <a-popover
          :content="huanBi?`对比 ${huanBiData?.startTime} 到 ${huanBiData?.endTime}，${huanBi && huanBi<0?'下降':'上涨'}了${Math.abs(huanBi)}%`:'无数据'"
          :content-style="{ maxWidth: '160px' }"
          position="tl"
      >
        <div class="trend-item">
          <span class="trend-label">日环比</span>
          <div v-if="huanBi && huanBi<0" class="trend-value down">
            <icon-caret-down class="icon"/>
            <span>{{ huanBi !== undefined && formatYValue(huanBi) || '--' }}%</span>
          </div>
          <div v-else class="trend-value up">
            <icon-caret-up class="icon"/>
            <span>{{ huanBi !== undefined && formatYValue(huanBi) || '--' }}%</span>
          </div>
        </div>
      </a-popover>
      <a-popover
          :content="weekTongBi?`对比 ${weekTongBiData?.startTime} 到 ${weekTongBiData?.endTime}，${weekTongBi && weekTongBi<0?'下降':'上涨'}了${Math.abs(weekTongBi)}%`:'无数据'"
          :content-style="{ maxWidth: '160px' }"
          position="tl"
      >
        <div class="trend-item">
          <span class="trend-label">周同比</span>
          <div v-if="weekTongBi && weekTongBi<0" class="cp_value down">
            <icon-caret-down class="icon"/>
            <span>{{ weekTongBi !== undefined && formatYValue(weekTongBi) || '--' }}%</span>
          </div>
          <div v-else class="cp_value up">
            <icon-caret-up class="icon"/>
            <span>{{ weekTongBi !== undefined && formatYValue(weekTongBi) || '--' }}%</span>
          </div>
        </div>
      </a-popover>
    </div>
  </div>
</template>

<style scoped lang="less">
.card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .card-date {
    height: 20px;
    color: var(--tant-text-gray-color-text1-3);
    font: var(--tant-description-font-description-regular);
    text-align: left;
  }

  .card-value {
    height: 58px;
    margin-top: 8px;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header1-medium);
    text-align: left;

    .value-unit {
      margin-left: 16px;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-body-font-body-regular);
    }
  }

  .card-trends {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    height: 24px;

    .trend-item {
      display: flex;

      .trend-label {
        display: inline-block;
        margin-right: 8px;
        color: var(--tant-text-gray-color-text1-3);
        font: var(--tant-body-font-body-regular);
        vertical-align: top;
      }

      .trend-value {
        display: inline-block;
        font: var(--tant-header-font-header5-regular);
        text-align: left;
        vertical-align: top;

        .icon {
          font-size: 14px;
        }
      }

      .up {
        color: var(--tant-status-success-color-success-default);
      }

      .down {
        color: var(--tant-status-danger-color-danger-default);
      }
    }
  }
}

.compare-popover {
  max-width: 80px;
}

.option-icon {
  margin-right: -4px;
}
</style>
