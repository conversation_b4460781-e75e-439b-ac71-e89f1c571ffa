import {App} from 'vue';
import {use} from 'echarts/core';
import {CanvasRenderer} from 'echarts/renderers';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from 'echarts/charts';
import {DataZoomComponent, GraphicComponent, GridComponent, LegendComponent, MarkLineComponent, MarkPointComponent, TimelineComponent, TooltipComponent} from 'echarts/components';
import Chart from './chart/index.vue';
import Breadcrumb from './breadcrumb/index.vue';
import FeedbackPanel from './feedback-panel/index.vue';

// Manually introduce ECharts modules to reduce packing size

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  Boxplot<PERSON>hart,
  <PERSON>key<PERSON>hart,
  <PERSON>rid<PERSON>omponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  MarkPointComponent,
  <PERSON><PERSON><PERSON>Component,
  TimelineComponent
]);

export default {
  install(Vue: App) {
    Vue.component('Chart', Chart);
    Vue.component('Breadcrumb', Breadcrumb);
    Vue.component('FeedbackPanel', FeedbackPanel);
  },
};