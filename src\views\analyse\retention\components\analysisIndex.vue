

<template>
    <div class="guide">
        <div class="stickyBar">
            <div class="modal" style="width: calc(100% - 78px);">
                <img src="/icon/analysis/performAnalysis.svg" alt="">
                <span class="title">对</span>
                <analysisSubjectSelect :style="{width:'120px',margin:'0 8px'}" @subject-change="subjectChange"/>
                <span class="title">进行分析</span>
            </div>
            <a-tooltip content="创建指标" position="top">
                <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="createVisible = true">
                    <template #icon>
                        <IconFont type="icon-tianjiazhibiao"/>
                    </template>
                </a-button>
            </a-tooltip>
            <a-tooltip content="重置" position="top">
                <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="resetParams">
                    <template #icon>
                        <icon-loop style="font-size: 16px;"/>
                    </template>
                </a-button>
            </a-tooltip>
        </div>
        <div class="event-filter-box">
            <div class="subTitle">初始事件</div>
            <handleFilterList :handle-list="beginShowData" :show-edit="true" :show-rename="true" :only-event="true" @indicators-change="beginChange"/>
            <div class="subTitle">回访事件</div>
            <handleFilterList :handle-list="visitShowData" :show-edit="true" :show-rename="true" :only-event="true" @indicators-change="visitChange"/>
            <div class="subTitle">使用同时展示</div>
            <div style="padding: 6px 4px 0 24px;margin-bottom: 16px;">
                <a-switch v-model:model-value="meanwhile" @change="meanwhileSwitchChange"/>
            </div>
            <div v-if="meanwhile && !isAnalysisEdit" class="subTitle">同时展示回访的测试用户属性参与</div>
            <handleFilterList v-if="meanwhile && !isAnalysisEdit" :handle-list="retentionFirstShow" :show-sub="true" :show-edit="true" :show-rename="true" :only-event="true" @indicators-change="meanwhileChange" @open-edit-modal="openEditModal"/>
            <div v-if="meanwhile && isAnalysisEdit" class="subTitle">同时展示</div>
            <div v-if="meanwhile && isAnalysisEdit" class="action-row" style="padding: 6px 4px 6px 24px;margin-bottom: 16px;">
                <div class="action-left">
                    <div class="filter-btn" @click="openEditModal">
                        <span class="filter-label">{{editForm.name}}</span>
                    </div>
                </div>
                <div class="action-right">
                    <a-space align="center">
                        <a-tooltip content="切换为指标选择" position="top">
                            <a-button class="btn-bg btn-26" @click="switchAnalysisSelect">
                                <template #icon>
                                    <icon-align-left />
                                </template>
                            </a-button>
                        </a-tooltip>
                        <a-tooltip content="开启指标编辑" position="top">
                            <a-button class="btn-bg btn-26" @click="openEditModal">
                                <template #icon>
                                    <icon-formula />
                                </template>
                            </a-button>
                        </a-tooltip>
                    </a-space>
                </div>
            </div>
            <div class="subTitle">使用关联属性</div>
            <div style="padding: 6px 4px 0 24px;margin-bottom: 16px;">
                <a-switch v-model:model-value="relevance" @change="relevanceSwitchChange"/>
            </div>
            <div v-if="relevance" class="relevance-box">
                <div>
                    初始事件的
                    <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
                        <attrEnumSelect
                        :only-event="true"
                        :info="attribute1"
                        :code-list="beginData[0]?.eventList"
                        @tabs-change="tabChange1"/>
                    </div>
                    与<br>
                </div>
                <div v-if="meanwhile">
                    同时展示的
                    <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
                        <attrEnumSelect
                        :only-event="true"
                        :info="attribute2"
                        :code-list="retentionFirstIndicator[0]?.eventList"
                        @tabs-change="tabChange2"/>
                    </div>
                    与<br>
                </div>
                <div>
                    回访事件的
                    <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
                        <attrEnumSelect
                        :only-event="true"
                        :info="attribute3"
                        :code-list="visitData[0]?.eventList"
                        @tabs-change="tabChange3"/>
                    </div>
                    的值相等
                </div>
                
            </div>
        </div>
    </div>
      <!-- 创建指标 -->
      <a-modal v-model:visible="createVisible" width="520px" @cancel="handleCreateCancel">
        <template #title>
            <div class="modal-title">创建指标</div>
        </template>
        <a-form ref="createFormRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="displayName" label="显示名">
                <a-input v-model="form.displayName" placeholder="80字以内" :max-length="80"/>
            </a-form-item>
            <a-form-item field="name" label="指标名">
                <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
            </a-form-item>
            <a-form-item field="description" label="备注">
                <a-textarea v-model="form.description" placeholder="请输入" :max-length="200" allow-clear show-word-limit style="height: 100px;"/>
            </a-form-item>
        </a-form>
        <div class="metric">
            <div class="metricHead">
                <div class="metricHeadTitleContainer">
                    <div class="metricHeadTitle">指标口径</div>
                </div>
                <div class="formulaType">
                    <dateDropdown @time-span-change="weekChange"/>
                    <span style="margin: 0 4px;">的</span>
                    <a-select v-model:model-value="formData.retentionIndicatorType" style="width: 150px" @change="retentionIndicatorTypeChange">
                      <div v-for="(item,index) in calculationList" :key="index">
                        <a-option
                            :style="formData.retentionIndicatorType===item.value?'backgroundColor:var(--tant-secondary-color-secondary-fill)':''"
                            :value=item.value>{{ item.label }}
                        </a-option>
                      </div>
                    </a-select>
                </div>
            </div>
            <div class="indicator-content">
                <div class="retention-readonly-div">
                    <span class="retention-readonly-h1">展示格式：</span>
                    <span class="retention-readonly-p">{{formData.retentionText}}</span>
                </div>
                <div class="retention-readonly-div">
                    <span class="retention-readonly-h1">分析主体：</span>
                    <span class="retention-readonly-p">{{ subjectData.subjectName }}</span>
                </div>
                <div class="retention-readonly-div">
                    <span class="retention-readonly-h1">初始事件：</span>
                    <span v-if="beginData.length" class="retention-readonly-p">
                        <span v-for="(item,index) in beginData[0]?.eventList" :key="index">
                            <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;margin-right: 4px;">
                            <span class="event-label">{{item.eventDisplayName }}</span>
                        </span>
                    </span>
                </div>
                <eventQueryFilter v-if="beginData.length && beginData[0].filter && beginData[0].filter?.filters?.length" :filter="beginData[0].filter" :disabled="true"/>
                <div class="retention-readonly-div">
                    <span class="retention-readonly-h1">回访事件：</span>
                    <span v-if="visitData.length" class="retention-readonly-p">
                        <span v-for="(item,index) in visitData[0]?.eventList" :key="index">
                            <img src="/icon/analysis/eventImg.svg" alt="" style="width: 12px;height: 12px;margin-right: 4px;">
                            <span class="event-label">{{item.eventDisplayName }}</span>
                        </span>
                    </span>
                </div>
                <eventQueryFilter v-if="visitData.length && visitData[0].filter && visitData[0].filter?.filters?.length" :filter="visitData[0].filter" :disabled="true"/>
            </div>
        </div>
        <template #footer>
            <div class="boe-foot">
                <a-button class="cancel" @click.stop="handleCreateCancel">取消</a-button>
                <a-button type="primary" @click="saveIndex">保存</a-button>
            </div>
        </template>
    </a-modal>
     <!-- 编辑指标 -->
     <a-modal v-model:visible="editVisible" width="648px">
        <template #title>
            <div class="modal-title">编辑指标</div>
        </template>
        <a-alert>应用编辑公式计算“回访用户指标”，或自定义其与“初始日期指标”的运算</a-alert>
        <a-form ref="editFormRef" :model="editForm" layout="vertical" style="margin-top: 20px;">
            <a-form-item field="name" label="指标名" label-col-flex="70px" :rules="[{required:true,message:'请输入指标名'}]" alidate-trigger="blur">
                <a-input v-model="editForm.name" class="form-input" placeholder="请输入指标名" />
            </a-form-item>
            <a-form-item field="section">
                <template #label>
                <span class="roi-step">A</span>
                回访用户指标
                </template>
                <handleFilterList :show-sub="true" :is-custom="true" @indicators-change="meanwhileChange"/>
            </a-form-item>
            <a-form-item field="section">
                <template #label>
                <span class="roi-step">B</span>
                初始日期指标
                </template>
                <div style="width: 100%;">
                <handleFilterList v-if="isAddB" :show-rename="true" :show-display-type="true" :show-sub="true" :is-custom="true" :show-del="true" @indicators-change="BChange"/>
                <div v-if="!isAddB" class="ta-filter-button" @click="addBIndex">
                    <icon-plus class="action"/>
                    <span class="label">添加初始日期指标-B</span>
                </div>
                </div>
            </a-form-item>
            <a-form-item v-if="isAddB" field="section" label="指标计算">
                <div class="ab">
                    <span v-if="isAtoB" class="roi-step roi-step-pointer" @click="() => isAtoB = false">A</span>
                    <span v-else class="roi-step roi-step-pointer" @click="() => isAtoB = true">B</span>
                    <a-dropdown position="top" @select="symbolSelect">
                        <div class="roi-btn">{{dropSymbol}}</div>
                        <template #content>
                        <a-doption v-for="(item,index) in symbolList" :key="index" :value="item" style="width: 60px;">{{ item.symbol }}</a-doption>
                        </template>
                    </a-dropdown>
                    <span v-if="isAtoB" class="roi-step roi-step-pointer" @click="() => isAtoB = false">B</span>
                    <span v-else class="roi-step roi-step-pointer" @click="() => isAtoB = true">A</span>
                </div>
                <a-select v-model:model-value="showFormat" style="width: 150px" @change="showFormatChange">
                    <div v-for="(item,index) in formatData" :key="index">
                    <a-option
                        :style="showFormat === item.value?'backgroundColor:var(--tant-secondary-color-secondary-fill)':''"
                        :value="item.value">{{ item.label }}
                    </a-option>
                    </div>
                </a-select>
            </a-form-item>
        </a-form>
        <template #footer>
            <div class="boe-foot">
                <a-button class="cancel" @click.stop="handleEditCancel">取消</a-button>
                <a-button type="primary" @click="saveEditIndex">确认</a-button>
            </div>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import handleFilterList from "@/views/analyse/components/HandleFilterList.vue";
import {EventAttitude, Indicator, IndicatorEvent} from "@/api/analyse/type";
import dateDropdown from "@/views/analyse/components/dateDropdown.vue";
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import analysisSubjectSelect from "@/views/analyse/components/analysisSubjectSelect.vue"
import {cloneDeep, debounce} from 'lodash'
import {toolStore} from '@/store';
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";
import {IndicatorType} from "@/api/enum";


const props = defineProps({
    analysisIndexData:{
        type:Object,
        default:() => {}
    }
})
const toolData = toolStore();
const eventBus = useEventBus('eventList');

const subjectData = reactive({
    subject:'',
    subjectName:''
})
const paramsData = reactive({
    code:'',
    name:'',
    displayName:'',
    type:IndicatorType.RETENTION,
    displayType:{type: 'default', decimalNum: 2, thousandSep: 1},
    indicatorList: [] as Indicator[],
    retentionAssociateAttribute:[] as EventAttitude[],
    formula:''
    // retentionIndicatorType:'retention_rate'
})
const codeList = ref<any>([])

onMounted(() => {
  eventBus.on((event: any) => {
    codeList.value = event
  })
})
const showFormat = ref('default2')
const formatData = [
  {label: '2位小数', value: 'default2'},
  {label: '3位小数', value: 'default3'},
  {label: '4位小数', value: 'default4'},
  {label: '百分比', value: 'percent'},
  {label: '取整', value: 'default0'},
]
const beginData = ref<any>([])
const beginShowData = ref<any>([])
const visitData = ref<any>([])
const visitShowData = ref<any>([])
const retentionFirstIndicator = ref<any>([])
const retentionFirstShow = ref<any>([])
const retentionSecondIndicator = ref<any>([])
const meanwhile = ref(false)
// 是否开启指标编辑 切换指标选择
const isAnalysisEdit = ref(false)
const relevance = ref(false)
const isAddB = ref(false)
const emits = defineEmits(['analysisIndexChange','analysisSubjectChange','resetParams'])
const tabShowInfo = ref<any>([])
// 关联属性
const attribute1 = ref({})
const attribute2 = ref({})
const attribute3 = ref({})
watch(() => props.analysisIndexData,() => {
    if(props.analysisIndexData?.indicatorList?.length){
        const list = cloneDeep(props.analysisIndexData.indicatorList)
        const [beginShow, visitShow, retentionFirst] = list;
        beginShowData.value = [beginShow];
        visitShowData.value = [visitShow];
        retentionFirstShow.value = retentionFirst ? [retentionFirst] : [];
        meanwhile.value = retentionFirstShow.value.length > 0
    }
    relevance.value = props.analysisIndexData?.retentionAssociateAttribute?.length > 0
    if(props.analysisIndexData?.retentionAssociateAttribute?.length){
        tabShowInfo.value = props.analysisIndexData.retentionAssociateAttribute
    }
    if(relevance.value){
        attribute1.value = tabShowInfo.value[0]
        if(meanwhile.value){
            attribute2.value = props.analysisIndexData.retentionAssociateAttribute[1]
            attribute3.value = props.analysisIndexData.retentionAssociateAttribute[2]
        }else{
            attribute3.value = props.analysisIndexData.retentionAssociateAttribute[1]
        }
    }
},{immediate:true,deep:true})

const updateParamsData = debounce(() => {
    paramsData.indicatorList = [
        ...beginData.value,
        ...visitData.value,
        ...(meanwhile.value ? retentionFirstIndicator.value : []),
        ...(isAddB.value ? retentionSecondIndicator.value : []),
        ];
    paramsData.retentionAssociateAttribute = []
    if (relevance.value) paramsData.retentionAssociateAttribute.push(attribute1.value);
    if (meanwhile.value && relevance.value) paramsData.retentionAssociateAttribute.push(attribute2.value);
    if (relevance.value) paramsData.retentionAssociateAttribute.push(attribute3.value);
    
    emits('analysisIndexChange', paramsData);
}, 300); // 300ms 防抖

// 初始事件
const beginChange = (v) => {
    beginData.value = v;
    updateParamsData();
};
// 回访事件
const visitChange = (v) => {
    visitData.value = v;
    updateParamsData();
};
const meanwhileSwitchChange = (v) => {
    updateParamsData()
}
// 同时展示
const meanwhileChange = (v) => {
    retentionFirstIndicator.value = v;
    updateParamsData();
};
const relevanceSwitchChange = async (v) => {
    const indexList = paramsData.indicatorList?.flatMap(item => item.eventList) || []
    const flatList = handleAttrFlatData(toolData.allAttrList, indexList);
    const attrList = flatList.filter(category => category.categoryName === '事件属性').flatMap(category => category.itemData || []);
    const defalutData = {
        code:attrList[0].code,
        name:attrList[0].name,
        displayName:attrList[0].displayName,
        ...attrList[0]
    }
    attribute1.value = Object.keys(attribute1.value).length > 0 ? attribute1.value : defalutData
    attribute2.value = Object.keys(attribute2.value).length > 0 ? attribute2.value : defalutData
    attribute3.value = Object.keys(attribute3.value).length > 0 ? attribute3.value : defalutData
    updateParamsData()
}

const tabChange1 = (v) => {
    attribute1.value = {
        code:v.objectId,
        name:v.objectName,
        displayName:v.objectDisplayName,
        ...v
    }
    updateParamsData();
}
const tabChange2 = (v) => {
    attribute2.value = {
        code:v.objectId,
        name:v.objectName,
        displayName:v.objectDisplayName
    }
    updateParamsData();
}
const tabChange3 = (v) => {
    attribute3.value = {
        code:v.objectId,
        name:v.objectName,
        displayName:v.objectDisplayName
    }
    updateParamsData();
}

const subjectChange = (v) => {
    subjectData.subject = v.subject
    subjectData.subjectName = v.subjectName 
  emits('analysisSubjectChange',subjectData)
}

// 创建指标
const createVisible = ref(false)
const createFormRef = ref(null)
const form = reactive({
  type:'retention',
  displayType: { type: "percent", decimalNum: 2, thousandSep: 1},
  name: '',
  displayName: '',
  description: '',
})
const rules = {
  name: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入指标名')
          }
          if (value && value.length > 80) {
            cb('指标名不能超过 80 个字符');
          }
          if (!(/^[a-z][a-z0-9_]{0,79}$/).test(value)) {
            cb('小写字母开头，可含小写字母/数字/下划线，不超过 80 个字符')
          }
          resolve(value)
        })
      }
    },
  ],
  displayName: [
    {
      required: true,
      validator: (value, cb) => {
        return new Promise((resolve) => {
          if (!value) {
            cb('请输入显示名')
          }
          if (value && value.length > 80) {
            cb('显示名不能超过 80 个字符');
          }
          resolve(value);
        });
      }
    },
  ]
}
// 创建指标弹窗取消
const handleCreateCancel = () => {
    createVisible.value = false
}
const formData = ref({
    displayName:'',
    displayType:{},
    description:'',
    name:'',
    type:'',
    eventList:[] as IndicatorEvent[],
    filter:{},
    formula:'',
    timeSpan:{},
    retentionIndicatorType:'retention_rate',
    retentionText:'百分比'
})
// 指标编辑弹窗
const editVisible = ref(false)
const editForm =  reactive({
  name: '',
})
// A->B
const isAtoB = ref(true)
// 同时展示指标
const symbolList = ref<any>([
  {
    value:'add',
    symbol:'+'
  },
  {
    value:'minus',
    symbol:'-'
  },
  {
    value:'multiply',
    symbol:'×'
  },
  {
    value:'divide',
    symbol:'÷'
  }
])
const dropSymbol = ref('+')
const dropValue = ref('add')
const symbolSelect = (v) => {
  dropSymbol.value = v.symbol
  dropValue.value = v.value
}
// 打开编辑弹窗
const openEditModal = () => {
    editVisible.value = true
}
// 切换为指标选择
const switchAnalysisSelect = () => {
    isAnalysisEdit.value = false
}
const handleEditCancel = () => {
    editVisible.value = false
}
const editFormRef = ref(null)
const valueFormat = ref({type:'default',decimalNum:2,thousandSep: 1})
const getValueFormat = (v) => {
  const labels = {
    'default2': {type:'default',decimalNum:2,thousandSep: 1},
    'default3': {type:'default',decimalNum:3,thousandSep: 1},
    'default4': {type:'default',decimalNum:4,thousandSep: 1},
    'percent': {type:'percent',decimalNum:2,thousandSep: 1},
    'default0': {type:'default',decimalNum:0,thousandSep: 1},
  };
  valueFormat.value = labels[v] || {}

}
// 编辑指标保存
const saveEditIndex = () => {
    if (editFormRef.value) {
        editFormRef.value.validate((errors:any) => {
            if (!errors) {
                retentionFirstIndicator.value.forEach(element => {
                    element.name = editForm.name
                });
                if(isAddB.value){
                    retentionSecondIndicator.value.forEach(element => {
                        element.name = editForm.name
                    });
                }
                paramsData.displayType = isAddB.value ? valueFormat.value : {type:'default',decimalNum:2,thousandSep: 1}
                paramsData.formula = isAddB.value ? dropValue.value : ''
                updateParamsData()
                editVisible.value = false
                isAnalysisEdit.value = true
            }
        });
    }
}


// 初始日期指标
const BChange = (v) => {
  retentionSecondIndicator.value = v
//   updateParamsData();
  isAddB.value = v.length > 0
}

const addBIndex = () => {
  isAddB.value = !isAddB.value
  dropValue.value = isAddB.value ? 'add' : ''
}
const calculationList = ref<any>([
  {
    value:'retention_rate',
    label:'留存率'
  },
  {
    value:'retention_num',
    label:'留存人数'
  },
  {
    value:'churn_rate',
    label:'流失率'
  },
  {
    value:'churn_num',
    label:'流失人数'
  }
])
const weekChange = (v) => {
    formData.value.timeSpan = v
}
const retentionIndicatorTypeChange = (v) => {
    formData.value.retentionText = v === 'retention_rate' || v === 'churn_rate' ? '百分比' : '取整'
}
// 展示格式
const showFormatChange = (v) => {
  showFormat.value = v
  getValueFormat(showFormat.value)
}
// 保存指标
const saveIndex = () => {
    if (createFormRef.value) {
        createFormRef.value.validate((errors:any) => {
            if (!errors) {
                const params = {
                    displayName: form.displayName,
                    displayType: formData.value.displayType,
                    description:form.description,
                    name: form.name,
                    type: formData.value.type,
                    formula:formData.value.formula,
                    eventList: formData.value.eventList,
                    filter: formData.value.filter,
                }
                // saveIndicatorData(params).then(res => {
                //     if(res){
                //         Message.success('创建成功！')
                //     }else{
                //         Message.error('创建失败')
                //     }
                // })
                createVisible.value = false
            }
        });
    }
}
const resetParams = () => {
    emits('resetParams')
}
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .title{
                font-weight: 600;
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .subTitle{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
        }
        .relevance-box{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            line-height: 38px;
            &:hover{
                background-color: var(--tant-fill-color-fill1-2);
                :deep(.filter-btn){
                    background: #fff;
                }
            }
        }
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 4px 0;
                        overflow: hidden;
                        line-height: 32px;
                        white-space: normal;
                    }
                }
            }
            .action-right{
                position: absolute;
                top: 0;
                right: 24px;
                min-width: 40px;
                height: 36px;
                padding-top: 0 !important;
                display: flex;
                align-items: center;
                opacity: 0;
                transition: opacity .3s;
            }
            .filter-btn{
                display: inline-flex;
                align-items: center;
                min-width: 40px;
                max-width: 200px;
                height: 26px;
                padding: 0 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
                box-sizing: border-box;
                .btn-icon{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    margin-right: 5px;
                }
                .filter-label{
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    color: var(--tant-text-gray-color-text1-2);
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: top;
                }

                &:hover{
                    border-color: var(--tant-primary-color-primary-hover);
                }
            }
            .row-word{
                display: inline-block;
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
                vertical-align: top;
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .action-left :deep(.filter-btn){
                background: #fff;
            }
            .action-left :deep(.filter-icon){
                background: #fff;
            }
            .sub-action-left :deep(.filter-btn){
                background: #fff;
            }
            .action-right{
                opacity: 1;
            } 
        }
        }
        .row-foot{
            margin: 0;
            padding-left: 34px;
            transition: all .3s;
            .ta-filter-button{
                padding: 6px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                font-size: 14px;
                transition: all .3s;
                .action{
                    border-radius: 4px;
                    background-color: var(--tant-primary-color-primary-fill);
                    color: var(--tant-primary-color-primary-default);
                    margin-right: 8px;
                    padding: 3px;
                    font-size: 18px;
                }
                .label{
                    color: var(--tant-primary-color-primary-default);
                }
                &:hover .action{
                    background-color: var(--tant-primary-color-primary-fill-hover);
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
.cancel {
  background: transparent;
  margin-right: 8px;
  &:hover {
    background-color: var(--color-secondary);
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
:deep(.arco-textarea){
    // border: none;
}
.metric{
    margin-top: 38px;
    margin-left: 8px;
    .metricHead{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px; 
        .metricHeadTitleContainer{
            display: flex;
            .metricHeadTitle{
                margin-left: 6px;
                color: var(--color-deep-blue-8);
                font-weight: 500;
            }
        }
        .formulaType{
            padding-left: 8px;
            color: var(--color-gray-blue-7);
            font-size: 12px;
        }
    }
    .indicator-content{
        padding: 16px 24px;
        background-color: var(--tant-fill-color-fill1-2);
        border-radius: 4px;
        .event-item{
            display: inline-flex;
            align-items: center;
            margin-right: 16px; 
        }
        .event-label {
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
        }
        .retention-readonly-div{
            line-height: 32px;
            .retention-readonly-p{
                color: var(--tant-text-gray-color-text1-2);
                font-size: 12px;
            }
            .retention-readonly-h1{
                margin-right: 12px;
                color: var(--tant-text-gray-color-text1-2);
                font-weight: 500;
                font-size: 12px;
                line-height: 32px;
            }
        }
    }
}

.roi-step{
  margin-right: 8px;
  display: inline-block;
  width: 24px;
  height: 24px;
  color: var(--tant-bg-white-color-bg1-1);
  font-size: 12px;
  line-height: 24px;
  text-align: center;
  background-color: var(--tant-secondary-color-secondary-default);
  border-radius: 4px;
}
.roi-step-pointer {
    cursor: pointer;
    &:hover{
      color: var(--tant-text-gray-color-text1-2);
      background-color: var(--tant-secondary-color-secondary-fill);
    }
}
.roi-btn{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  font-size: 14px;
  background: var(--tant-bg-white-color-bg1-1);
  border: 1px solid #dbdee4;
  border-radius: 2px;
  cursor: pointer;
}
.ta-filter-button{
    padding: 6px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    transition: all .3s;
    .action{
        border-radius: 4px;
        background-color: var(--tant-primary-color-primary-fill);
        color: var(--tant-primary-color-primary-default);
        margin-right: 8px;
        padding: 3px;
        font-size: 18px;
    }
    .label{
        color: var(--tant-primary-color-primary-default);
    }
    &:hover .action{
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-hover);
    }
}
.ab{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>