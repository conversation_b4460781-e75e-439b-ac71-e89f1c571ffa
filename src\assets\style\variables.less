/**
 * 通用样式变量
 */
:root {
  --tant-token-set-order-0: global;
  --tant-token-set-order-1: ta;
  --tant-primary-color-primary-default: #1E76F0;
  --tant-primary-color-primary-hover: #3583EF;
  --tant-primary-color-primary-active: #185EC0;
  --tant-primary-color-primary-disable: #99B4F3;
  --tant-primary-color-primary-fill: #EAEEFD;
  --tant-primary-color-primary-fill-hover: #F8FAFE;
  --tant-primary-color-primary-fill-active: #CDD8FA;
  --tant-secondary-color-secondary-default: #202241;
  --tant-secondary-color-secondary-hover: #747C94;
  --tant-secondary-color-secondary-active: #000;
  --tant-secondary-color-secondary-disable: #B9BECF;
  --tant-secondary-color-secondary-fill: #F2F3F8;
  --tant-secondary-color-secondary-fill-hover: #F6F6F9;
  --tant-secondary-color-secondary-fill-active: #EBEDF3;
  --tant-secondary-color-secondary-transp-fill: #b9becf4d;
  --tant-secondary-color-secondary-transp-hover: #b9becf33;
  --tant-secondary-color-secondary-transp-active: #b9becf80;
  --tant-text-gray-color-text1-1: #212121;
  --tant-text-gray-color-text1-2: #333;
  --tant-text-gray-color-text1-3: #7E7F80;
  --tant-text-gray-color-text1-4: #BDBEBF;
  --tant-text-white-color-text2-1: #fff;
  --tant-text-white-color-text2-2: #ffffffb3;
  --tant-text-white-color-text2-3: #ffffff80;
  --tant-text-white-color-text2-4: #fff6;
  --tant-bg-white-color-bg1-1: #fff;
  --tant-bg-gray-color-bg2-1: #F3F3F5;
  --tant-fill-color-fill1-1: #F6F6F9;
  --tant-fill-color-fill1-2: #F9F9FB;
  --tant-fill-color-fill2-1: #ffffff80;
  --tant-fill-deep-color-fill3-1: #212121;
  --tant-fill-deep-color-fill3-2: #333;
  --tant-fill-deep-color-fill3-3: #545455;
  --tant-border-color-border1-1: #E3E4E5;
  --tant-border-color-border1-2: #D2D2D4;
  --tant-border-radius-small: 2px;
  --tant-border-radius-medium: 4px;
  --tant-border-radius-large: 6px;
  --tant-border-radius-full: 9999px;
  --tant-small-shadow-small-bottom: 0 4px 14px 0 #0a103205, 0 10px 20px 0 #0a103208, 0 14px 24px 0 #0a10320d;
  --tant-small-shadow-small-overall: 0 0 14px 0 #0a103205, 0 4px 20px 0 #0a103208, 0 8px 24px 0 #0a10320d;
  --tant-small-shadow-small-top: 0 -4px 14px 0 #0a103205,0 -10px 20px 0 #0a103208,0 -14px 24px 0 #0a10320d;
  --tant-small-shadow-small-left: -4px 0 14px 0 #0a103205, -10px 0 20px 0 #0a103208, -14px 0 24px 0 #0a10320d;
  --tant-small-shadow-small-right: 4px 0 14px 0 #0a103205, 10px 0 20px 0 #0a103208, 14px 0 24px 0 #0a10320d;
  --tant-shadow-color-shadow1-1: #0A1032;
  --tant-mask-color-mask-1: #0003;
  --tant-status-success-color-success-default: #129B76;
  --tant-status-success-color-success-hover: #16C294;
  --tant-status-success-color-success-active: #0D7459;
  --tant-status-success-color-success-disable: #9ADAC1;
  --tant-status-success-color-success-fill: #E9F6F1;
  --tant-status-success-color-success-fill-hover: #F4FAF7;
  --tant-status-success-color-success-fill-active: #CCEBDE;
  --tant-status-warning-color-warning-default: #F27E19;
  --tant-status-warning-color-warning-hover: #F1913D;
  --tant-status-warning-color-warning-active: #C26514;
  --tant-status-warning-color-warning-disable: #F7B79A;
  --tant-status-warning-color-warning-fill: #FDEFEA;
  --tant-status-warning-color-warning-fill-hover: #FDF6F4;
  --tant-status-warning-color-warning-fill-active: #FADACD;
  --tant-status-danger-color-danger-default: #E84D37;
  --tant-status-danger-color-danger-hover: #EB614D;
  --tant-status-danger-color-danger-active: #BA3E2C;
  --tant-status-danger-color-danger-disable: #F1A59F;
  --tant-status-danger-color-danger-fill: #FBEBEA;
  --tant-status-danger-color-danger-fill-hover: #FDF5F4;
  --tant-status-danger-color-danger-fill-active: #F7D1CF;
  --tant-status-info-color-info-default: #1E76F0;
  --tant-status-info-color-info-hover: #3583EF;
  --tant-status-info-color-info-active: #185EC0;
  --tant-status-info-color-info-disable: #99B4F3;
  --tant-status-info-color-info-fill: #EAEEFD;
  --tant-status-info-color-info-fill-hover: #F8FAFE;
  --tant-status-info-color-info-fill-active: #CDD8FA;
  --tant-status-invalid-color-invalid-default: #747C94;
  --tant-status-invalid-color-invalid-hover: #A1A7BB;
  --tant-status-invalid-color-invalid-active: #464762;
  --tant-status-invalid-color-invalid-disable: #DEE1EB;
  --tant-status-invalid-color-invalid-fill: #EBEDF3;
  --tant-status-invalid-color-invalid-fill-hover: #F6F6F9;
  --tant-status-invalid-color-invalid-fill-active: #DEE1EB;
  --tant-medium-shadow-medium-bottom: 0 14px 20px 0 #0a103205, 0 20px 40px 0 #0a103208, 0 40px 60px 0 #0a10320d;
  --tant-medium-shadow-medium-overall: 0 0 20px 0 #0a103205, 0 14px 40px 0 #0a103208, 0 20px 60px 0 #0a10320d;
  --tant-medium-shadow-medium-top: 0 -14px 20px 0 #0a103205,0 -20px 40px 0 #0a103208,0 -40px 60px 0 #0a10320d;
  --tant-medium-shadow-medium-left: -14px 0 20px 0 #0a103205, -20px 0 40px 0 #0a103208, -40px 0 60px 0 #0a10320d;
  --tant-medium-shadow-medium-right: 14px 0 20px 0 #0a103205, 20px 0 40px 0 #0a103208, 40px 0 60px 0 #0a10320d;
  --tant-large-shadow-large-bottom: 0 20px 50px 0 #0a103205, 0 30px 90px 0 #0a103208, 0 40px 130px 0 #0a10320d;
  --tant-large-shadow-large-overall: 0 0 50px 0 #0a103205, 0 20px 90px 0 #0a103208, 0 30px 130px 0 #0a10320d;
  --tant-large-shadow-large-top: 0 -20px 50px 0 #0a103205,0 -30px 90px 0 #0a103208,0 -40px 130px 0 #0a10320d;
  --tant-large-shadow-large-left: -20px 0 50px 0 #0a103205, -30px 0 90px 0 #0a103208, -40px 0 130px 0 #0a10320d;
  --tant-large-shadow-large-right: 20px 0 50px 0 #0a103205, 30px 0 90px 0 #0a103208, 40px 0 130px 0 #0a10320d;
  --tant-hanzi: pingfang sc, "TA-SourceHanSansSC";
  --tant-letter: helvetica neue;
  --tant-number: helvetica neue;
  --tant-font-size-small: 12px;
  --tant-font-size-regular: 14px;
  --tant-font-size-header4: 18px;
  --tant-font-size-header3: 22px;
  --tant-font-size-header2: 28px;
  --tant-font-size-header1: 38px;
  --tant-body-font-body-regular: 400 12px/22px pingfang sc, "TA-SourceHanSansSC";
  --tant-body-font-body-medium: 500 12px/22px pingfang sc, "TA-SourceHanSansSC";
  --tant-description-font-description-regular: 400 12px/20px pingfang sc, "TA-SourceHanSansSC";
  --tant-description-font-description-medium: 500 12px/20px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header5-medium: 500 16px/24px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header5-regular: 400 16px/24px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header4-medium: 500 20px/30px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header3-medium: 500 24px/36px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header2-medium: 500 28px/38px pingfang sc, "TA-SourceHanSansSC";
  --tant-header-font-header1-medium: 500 38px/58px pingfang sc, "TA-SourceHanSansSC";
  --tant-disabled-color-disabled-text: #BDBEBF;
  --tant-disabled-color-disabled-border: #EBEDF3;
  --tant-disabled-color-disabled-bg: #ffffff1a;
  --tant-disabled-color-disabled-fill: #F9F9FB;
  --tant-input-shadow-active-overall: 3px -3px 0 0 #DAE9FB, -3px 3px 0 0 #DAE9FB,-3px -3px 0 0 #DAE9FB, 3px 3px 0 0 #DAE9FB;
  --tant-input-shadow-error-active-overall: 3px 3px 0 0 #FFE6E5,-3px -3px 0 0 #FFE6E5, -3px 3px 0 0 #FFE6E5,3px -3px 0 0 #FFE6E5;
  --tant-decorative-blue-color-decorative1-1: #1E76F0;
  --tant-decorative-blue-color-decorative1-2: #3583EF;
  --tant-decorative-blue-color-decorative1-3: #99B4F3;
  --tant-decorative-blue-color-decorative1-4: #EAEEFD;
  --tant-decorative-orange-color-decorative2-1: #F27E19;
  --tant-decorative-orange-color-decorative2-2: #F1913D;
  --tant-decorative-orange-color-decorative2-3: #F7B79A;
  --tant-decorative-orange-color-decorative2-4: #FDEFEA;
  --tant-decorative-orange-color-decorative2-5: #61320A;
  --tant-decorative-cyen-color-decorative3-1: #14C9D6;
  --tant-decorative-cyen-color-decorative3-2: #34CFDA;
  --tant-decorative-cyen-color-decorative3-3: #9ADEE6;
  --tant-decorative-cyen-color-decorative3-4: #EAF7F9;
  --tant-decorative-purple-color-decorative4-1: #915AFF;
  --tant-decorative-purple-color-decorative4-2: #9670FC;
  --tant-decorative-purple-color-decorative4-3: #C0A9FF;
  --tant-decorative-purple-color-decorative4-4: #F1ECFF;
  --tant-decorative-purple-color-decorative4-5: #3A2466;
  --tant-decorative-dblue-color-decorative5-1: #0A1032;
  --tant-decorative-dblue-color-decorative5-2: #B9BECF;
  --tant-decorative-yellow-color-decorative6-1: #F5B400;
  --tant-decorative-yellow-color-decorative6-2: #F2BF32;
  --tant-decorative-yellow-color-decorative6-3: #F9D299;
  --tant-decorative-yellow-color-decorative6-4: #FDF4E9;
  --tant-data-v-multiple1-color-data-multiple1-1: #3080EF;
  --tant-data-v-multiple1-color-data-multiple1-2: #FA9239;
  --tant-data-v-multiple1-color-data-multiple1-3: #374BCD;
  --tant-data-v-multiple1-color-data-multiple1-4: #66D8E2;
  --tant-data-v-multiple1-color-data-multiple1-5: #FFBC04;
  --tant-data-v-multiple1-color-data-multiple1-6: #40AEFF;
  --tant-data-v-multiple1-color-data-multiple1-7: #0A8CA0;
  --tant-data-v-multiple1-color-data-multiple1-8: #FFB676;
  --tant-data-v-multiple1-color-data-multiple1-9: #EE6C59;
  --tant-data-v-multiple1-color-data-multiple1-10: #7856FF;
  --tant-data-v-multiple1-color-data-multiple1-11: #A37AF5;
  --tant-data-v-multiple1-color-data-multiple1-12: #48C6A8;
  --tant-data-v-single1-color-data-single1-1: #217DFF;
  --tant-data-v-function-color-data-function-line: #DEE1EB;
  --tant-data-v-function-color-data-function-tick: #DEE1EB;
  --tant-data-v-function-color-data-function-grid: #EBEDF3;
  --tant-data-v-function-color-data-function-shadow: #0104181a;
  --tant-data-v-function-color-data-function-label2: #A1A7BB;
  --tant-data-v-function-color-data-function-label1: #747C94;
  --tant-data-v-function-color-data-function-legend: #202241;
  --tant-data-v-function-color-data-function-serie-label: #202241;
  --tant-data-v-function-color-data-function-axis-title: #202241;
  --tant-data-v-function-color-data-function-zero-axis: #A1A7BB;
  --tant-data-v-function-color-data-function-axis-pointer: #A1A7BB;
  --tant-data-v-multiple2-color-data-multiple2-1: #4382B6;
  --tant-data-v-multiple2-color-data-multiple2-2: #87D1C8;
  --tant-data-v-multiple2-color-data-multiple2-3: #EE5761;
  --tant-data-v-multiple2-color-data-multiple2-4: #5FBC68;
  --tant-data-v-multiple2-color-data-multiple2-5: #EC8E8D;
  --tant-data-v-multiple2-color-data-multiple2-6: #A27159;
  --tant-data-v-multiple2-color-data-multiple2-7: #BAD418;
  --tant-data-v-multiple2-color-data-multiple2-8: #209490;
  --tant-data-v-multiple2-color-data-multiple2-9: #DFAC70;
  --tant-data-v-multiple2-color-data-multiple2-10: #E36994;
  --tant-data-v-multiple2-color-data-multiple2-11: #F7BA1E;
  --tant-data-v-multiple2-color-data-multiple2-12: #827E7D;
  --tant-data-v-multiple3-color-data-multiple3-1: #134FE1;
  --tant-data-v-multiple3-color-data-multiple3-2: #04DBC0;
  --tant-data-v-multiple3-color-data-multiple3-3: #DB4BFF;
  --tant-data-v-multiple3-color-data-multiple3-4: #FF6914;
  --tant-data-v-multiple3-color-data-multiple3-5: #04AA0A;
  --tant-data-v-multiple3-color-data-multiple3-6: #06D1F5;
  --tant-data-v-multiple3-color-data-multiple3-7: #805350;
  --tant-data-v-multiple3-color-data-multiple3-8: #FFB803;
  --tant-data-v-multiple3-color-data-multiple3-9: #217DFF;
  --tant-data-v-multiple3-color-data-multiple3-10: #48DF2F;
  --tant-data-v-multiple3-color-data-multiple3-11: #CF5015;
  --tant-data-v-multiple3-color-data-multiple3-12: #9720FF;
  --tant-data-v-multiple4-color-data-multiple4-1: #9F79EE;
  --tant-data-v-multiple4-color-data-multiple4-2: #F47774;
  --tant-data-v-multiple4-color-data-multiple4-3: #AED581;
  --tant-data-v-multiple4-color-data-multiple4-4: #4B8BE4;
  --tant-data-v-multiple4-color-data-multiple4-5: #FFAB66;
  --tant-data-v-multiple4-color-data-multiple4-6: #B6A2DE;
  --tant-data-v-multiple4-color-data-multiple4-7: #FFD214;
  --tant-data-v-multiple4-color-data-multiple4-8: #FD90C5;
  --tant-data-v-multiple4-color-data-multiple4-9: #6A9FE9;
  --tant-data-v-multiple4-color-data-multiple4-10: #70BE75;
  --tant-data-v-multiple4-color-data-multiple4-11: #FA9538;
  --tant-data-v-multiple4-color-data-multiple4-12: #2EC7C9;
  --tant-motion-duration-micro-fast: 100ms;
  --tant-motion-duration-micro-moderate: 150ms;
  --tant-motion-duration-micro-slow: 200ms;
  --tant-motion-duration-macro-faster: 250ms;
  --tant-motion-duration-macro-moderate: 300ms;
  --tant-motion-duration-macro-slower: 350ms;
  --tant-motion-duration-extra-slowest: 800ms;
  --tant-motion-easing-standard-linear: 0, 0, 1, 1;
  --tant-motion-easing-standard-ease-in: 0, 0, 0, 1;
  --tant-motion-easing-standard-ease-out: 0.2, 0, 0.8, 0.5;
  --tant-motion-easing-standard-ease-in-out: 0.42, 0, 0.58, 1;
  --tant-motion-easing-emphasized-linear: 0.2, 0, 0, 1;
  --tant-motion-easing-emphasized-ease-in: 0.05, 0.7, 0.1, 1;
  --tant-motion-easing-emphasized-ease-out: 0.3, 0, 0.8, 0.15;
  --tant-motion-easing-emphasized-ease-in-out: 0.6, 0, 0.14, 1;
  --tant-blue-blue-10: #F8FAFE;
  --tant-blue-blue-20: #EAEEFD;
  --tant-blue-blue-30: #CDD8FA;
  --tant-blue-blue-40: #99B4F3;
  --tant-blue-blue-50: #3583EF;
  --tant-blue-blue-60: #1E76F0;
  --tant-blue-blue-70: #185EC0;
  --tant-blue-blue-80: #124790;
  --tant-blue-blue-90: #0C2F60;
  --tant-blue-blue-100: #061830;
  --tant-cyan-cyan-10: #F3FAFB;
  --tant-cyan-cyan-20: #EAF7F9;
  --tant-cyan-cyan-30: #CCEDF1;
  --tant-cyan-cyan-40: #9ADEE6;
  --tant-cyan-cyan-50: #34CFDA;
  --tant-cyan-cyan-60: #14C9D6;
  --tant-cyan-cyan-70: #10A1AB;
  --tant-cyan-cyan-80: #0C7980;
  --tant-cyan-cyan-90: #085056;
  --tant-cyan-cyan-100: #04282B;
  --tant-orange-orange-10: #FDF6F4;
  --tant-orange-orange-20: #FDEFEA;
  --tant-orange-orange-30: #FADACD;
  --tant-orange-orange-40: #F7B79A;
  --tant-orange-orange-50: #F1913D;
  --tant-orange-orange-60: #F27E19;
  --tant-orange-orange-70: #C26514;
  --tant-orange-orange-80: #914C0F;
  --tant-orange-orange-90: #61320A;
  --tant-orange-orange-100: #301905;
  --tant-purple-purple-10: #F7F4FF;
  --tant-purple-purple-20: #F1ECFF;
  --tant-purple-purple-30: #DED3FF;
  --tant-purple-purple-40: #C0A9FF;
  --tant-purple-purple-50: #9670FC;
  --tant-purple-purple-60: #915AFF;
  --tant-purple-purple-70: #7448CC;
  --tant-purple-purple-80: #573699;
  --tant-purple-purple-90: #3A2466;
  --tant-purple-purple-100: #1D1233;
  --tant-green-green-10: #F4FAF7;
  --tant-green-green-20: #E9F6F1;
  --tant-green-green-30: #CCEBDE;
  --tant-green-green-40: #9ADAC1;
  --tant-green-green-50: #28CA9F;
  --tant-green-green-60: #16C294;
  --tant-green-green-70: #129B76;
  --tant-green-green-80: #0D7459;
  --tant-green-green-90: #094E3B;
  --tant-green-green-100: #04271E;
  --tant-yellow-yellow-10: #FEF9F4;
  --tant-yellow-yellow-20: #FDF4E9;
  --tant-yellow-yellow-30: #FBE7CC;
  --tant-yellow-yellow-40: #F9D299;
  --tant-yellow-yellow-50: #F2BF32;
  --tant-yellow-yellow-60: #F5B400;
  --tant-yellow-yellow-70: #C49000;
  --tant-yellow-yellow-80: #936C00;
  --tant-yellow-yellow-90: #624800;
  --tant-yellow-yellow-100: #312400;
  --tant-red-red-10: #FDF5F4;
  --tant-red-red-20: #FBEBEA;
  --tant-red-red-30: #F7D1CF;
  --tant-red-red-40: #F1A59F;
  --tant-red-red-50: #EB614D;
  --tant-red-red-60: #E84D37;
  --tant-red-red-70: #BA3E2C;
  --tant-red-red-80: #8B2E21;
  --tant-red-red-90: #5D1F16;
  --tant-red-red-100: #2E0F0B;
  --tant-dblue-dblue-5: #F9F9FB;
  --tant-dblue-dblue-10: #F6F6F9;
  --tant-dblue-dblue-15: #F2F3F8;
  --tant-dblue-dblue-20: #EBEDF3;
  --tant-dblue-dblue-30: #DEE1EB;
  --tant-dblue-dblue-40: #B9BECF;
  --tant-dblue-dblue-50: #A1A7BB;
  --tant-dblue-dblue-60: #747C94;
  --tant-dblue-dblue-70: #464762;
  --tant-dblue-dblue-80: #202241;
  --tant-dblue-dblue-90: #0A1032;
  --tant-dblue-dblue-100: #000;
  --tant-white-white-10: #ffffff1a;
  --tant-white-white-20: #fff3;
  --tant-white-white-30: #ffffff4d;
  --tant-white-white-40: #fff6;
  --tant-white-white-50: #ffffff80;
  --tant-white-white-60: #fff9;
  --tant-white-white-70: #ffffffb3;
  --tant-white-white-80: #fffc;
  --tant-white-white-90: #ffffffe6;
  --tant-white-white-100: #fff;
  --tant-slate-slate-10: #F3F3F5;
  --tant-slate-slate-20: #E3E4E5;
  --tant-slate-slate-30: #D2D2D4;
  --tant-slate-slate-40: #BDBEBF;
  --tant-slate-slate-50: #9B9C9D;
  --tant-slate-slate-60: #7E7F80;
  --tant-slate-slate-70: #545455;
  --tant-slate-slate-80: #333;
  --tant-slate-slate-90: #212121;
  --tant-slate-slate-100: #020202;
}

/**
* 全局变量
 */
:root {
  --nav-height: 48px;
  --font-size-title-larger: 20px;
  --font-size-title-default: 16px;
  --font-size-title-small: 14px;
  --font-size-body-default: 12px;
  --font-size-body-small: 10px;

  --layout-max-width: 1100px;
  --font-size-display-3: 54px;
  --font-size-body-2: 11px;
  --font-size-title-1: 14px;
  --font-size-caption: 10px;
  --font-size-display-2: 46px;
  --font-size-display-1: 34px;
  --font-size-title-3: 22px;
}

/**
 * 暗黑模式
 */
html[data-theme='dark'],
.part-dark-theme[data-theme='dark'],
html[data-prefers-color='dark'],
.part-dark-theme[data-prefers-color='dark'] {
  --tant-token-set-order-0: global;
  --tant-token-set-order-1: ta;
  --tant-primary-color-primary-default: #1e76f0;
  --tant-primary-color-primary-hover: #3583ef;
  --tant-primary-color-primary-active: #185ec0;
  --tant-primary-color-primary-disable: #99b4f3;
  --tant-primary-color-primary-fill: #1e76f033;
  --tant-primary-color-primary-fill-hover: #1e76f04d;
  --tant-primary-color-primary-fill-active: #1e76f01a;
  --tant-secondary-color-secondary-default: #d4d5e1;
  --tant-secondary-color-secondary-hover: #9fa0ac;
  --tant-secondary-color-secondary-active: #fff;
  --tant-secondary-color-secondary-disable: #6b6c77;
  --tant-secondary-color-secondary-fill: #2f303b;
  --tant-secondary-color-secondary-fill-hover: #2b2b39;
  --tant-secondary-color-secondary-fill-active: #36384f;
  --tant-secondary-color-secondary-transp-fill: #b9becf4d;
  --tant-secondary-color-secondary-transp-hover: #b9becf33;
  --tant-secondary-color-secondary-transp-active: #b9becf80;
  --tant-text-gray-color-text1-1: #eeeffc;
  --tant-text-gray-color-text1-2: #d2d3e0;
  --tant-text-gray-color-text1-3: #9c9eac;
  --tant-text-gray-color-text1-4: #5f606e;
  --tant-text-white-color-text2-1: #161616;
  --tant-text-white-color-text2-2: #686872;
  --tant-text-white-color-text2-3: #7e7f80;
  --tant-text-white-color-text2-4: #bdbebf;
  --tant-bg-white-color-bg1-1: #21212d;
  --tant-bg-gray-color-bg2-1: #1a1924;
  --tant-fill-color-fill1-1: #2c2c3a;
  --tant-fill-color-fill1-2: #272735;
  --tant-fill-color-fill2-1: #ffffff80;
  --tant-fill-deep-color-fill3-1: #f6f6fa;
  --tant-fill-deep-color-fill3-2: #333;
  --tant-fill-deep-color-fill3-3: #545455;
  --tant-border-color-border1-1: #323346;
  --tant-border-color-border1-2: #4b4d6a;
  --tant-border-radius-small: 2px;
  --tant-border-radius-medium: 4px;
  --tant-border-radius-large: 6px;
  --tant-border-radius-full: 9999px;
  --tant-small-shadow-small-bottom: 0 0 2px 0 var(--tant-border-color-border1-2),
  0 4px 14px 0 #00000040, 0 10px 20px 0 #00000040, 0 14px 24px 0 #00000040;
  --tant-small-shadow-small-overall: 0 0 1px 0 var(--tant-border-color-border1-2),
  0 0 14px 0 #00000040, 0 4px 20px 0 #00000040, 0 8px 24px 0 #00000040;
  --tant-small-shadow-small-top: 0 0 1px 0 var(--tant-border-color-border1-2),
    0 -4px 14px 0 #00000040, 0 -10px 20px 0 #00000040, 0 -14px 24px 0 #00000040;
  --tant-small-shadow-small-left: 0 0 1px 0 var(--tant-border-color-border1-2),
  -4px 0 14px 0 #00000040, -10px 0 20px 0 #00000040, -14px 0 24px 0 #00000040;
  --tant-small-shadow-small-right: 0 0 1px 0 var(--tant-border-color-border1-2),
  4px 0 14px 0 #00000040, 10px 0 20px 0 #00000040, 14px 0 24px 0 #00000040;
  --tant-shadow-color-shadow1-1: #0a1032;
  --tant-mask-color-mask-1: #000000b3;
  --tant-status-success-color-success-default: #129b76;
  --tant-status-success-color-success-hover: #16c294;
  --tant-status-success-color-success-active: #0d7459;
  --tant-status-success-color-success-disable: #9adac1;
  --tant-status-success-color-success-fill: #1d403f;
  --tant-status-success-color-success-fill-hover: #1c4643;
  --tant-status-success-color-success-fill-active: #1f2d34;
  --tant-status-warning-color-warning-default: #f27e19;
  --tant-status-warning-color-warning-hover: #f1913d;
  --tant-status-warning-color-warning-active: #c26514;
  --tant-status-warning-color-warning-disable: #f7b79a;
  --tant-status-warning-color-warning-fill: #553828;
  --tant-status-warning-color-warning-fill-hover: #603d27;
  --tant-status-warning-color-warning-fill-active: #362a2b;
  --tant-status-danger-color-danger-default: #e84d37;
  --tant-status-danger-color-danger-hover: #eb614d;
  --tant-status-danger-color-danger-active: #ba3e2c;
  --tant-status-danger-color-danger-disable: #f1a59f;
  --tant-status-danger-color-danger-fill: #4d2e31;
  --tant-status-danger-color-danger-fill-hover: #5e3331;
  --tant-status-danger-color-danger-fill-active: #713a34;
  --tant-status-info-color-info-default: #1e76f0;
  --tant-status-info-color-info-hover: #3583ef;
  --tant-status-info-color-info-active: #185ec0;
  --tant-status-info-color-info-disable: #99b4f3;
  --tant-status-info-color-info-fill: #20365e;
  --tant-status-info-color-info-fill-hover: #204580;
  --tant-status-info-color-info-fill-active: #203b6a;
  --tant-status-invalid-color-invalid-default: #9c9eac;
  --tant-status-invalid-color-invalid-hover: #50515c;
  --tant-status-invalid-color-invalid-active: #282a34;
  --tant-status-invalid-color-invalid-disable: #21232d;
  --tant-status-invalid-color-invalid-fill: #9c9eac33;
  --tant-status-invalid-color-invalid-fill-hover: #9c9eac1a;
  --tant-status-invalid-color-invalid-fill-active: #9c9eac4d;
  --tant-medium-shadow-medium-bottom: 0 0 2px 0 var(--tant-border-color-border1-2),
  0 14px 20px 0 #00000040, 0 20px 40px 0 #00000040, 0 40px 60px 0 #00000040;
  --tant-medium-shadow-medium-overall: 0 0 2px 0 var(--tant-border-color-border1-2),
  0 0 20px 0 #00000040, 0 14px 40px 0 #00000040, 0 20px 60px 0 #00000040;
  --tant-medium-shadow-medium-top: 0 0 2px 0 var(--tant-border-color-border1-2),
    0 -14px 20px 0 #00000040, 0 -20px 40px 0 #00000040, 0 -40px 60px 0 #00000040;
  --tant-medium-shadow-medium-left: 0 0 2px 0 var(--tant-border-color-border1-2),
  -14px 0 20px 0 #00000040, -20px 0 40px 0 #00000040, -40px 0 60px 0 #00000040;
  --tant-medium-shadow-medium-right: 0 0 2px 0 var(--tant-border-color-border1-2),
  14px 0 20px 0 #00000040, 20px 0 40px 0 #00000040, 40px 0 60px 0 #00000040;
  --tant-large-shadow-large-bottom: 0 0 2px 0 var(--tant-border-color-border1-2),
  0 20px 50px 0 #00000040, 0 30px 90px 0 #00000040, 0 40px 130px 0 #00000040;
  --tant-large-shadow-large-overall: 0 0 2px 0 var(--tant-border-color-border1-2),
  0 0 50px 0 #00000040, 0 20px 90px 0 #00000040, 0 30px 130px 0 #00000040;
  --tant-large-shadow-large-top: 0 0 2px 0 var(--tant-border-color-border1-2),
    0 -20px 50px 0 #00000040, 0 -30px 90px 0 #00000040, 0 -40px 130px 0 #00000040;
  --tant-large-shadow-large-left: 0 0 2px 0 var(--tant-border-color-border1-2),
  -20px 0 50px 0 #00000040, -30px 0 90px 0 #00000040, -40px 0 130px 0 #00000040;
  --tant-large-shadow-large-right: 0 0 2px 0 var(--tant-border-color-border1-2),
  20px 0 50px 0 #00000040, 30px 0 90px 0 #00000040, 40px 0 130px 0 #00000040;
  --tant-hanzi: pingfang sc, 'TA-SourceHanSansSC';
  --tant-letter: helvetica neue;
  --tant-number: helvetica neue;
  --tant-font-size-small: 12px;
  --tant-font-size-regular: 14px;
  --tant-font-size-header4: 18px;
  --tant-font-size-header3: 22px;
  --tant-font-size-header2: 28px;
  --tant-font-size-header1: 38px;
  --tant-body-font-body-regular: 400 12px/22px pingfang sc, 'TA-SourceHanSansSC';
  --tant-body-font-body-medium: 500 12px/22px pingfang sc, 'TA-SourceHanSansSC';
  --tant-description-font-description-regular: 400 12px/20px pingfang sc, 'TA-SourceHanSansSC';
  --tant-description-font-description-medium: 500 12px/20px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header5-medium: 500 16px/24px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header5-regular: 400 16px/24px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header4-medium: 500 20px/30px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header3-medium: 500 24px/36px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header2-medium: 500 28px/38px pingfang sc, 'TA-SourceHanSansSC';
  --tant-header-font-header1-medium: 500 38px/58px pingfang sc, 'TA-SourceHanSansSC';
  --tant-disabled-color-disabled-text: #5f606e;
  --tant-disabled-color-disabled-border: #2c2d3b;
  --tant-disabled-color-disabled-bg: #ffffff1a;
  --tant-disabled-color-disabled-fill: #282a34;
  --tant-input-shadow-active-overall: 3px -3px 0 0 #193c71, -3px 3px 0 0 #193c71,
    -3px -3px 0 0 #193c71, 3px 3px 0 0 #193c71;
  --tant-input-shadow-error-active-overall: 3px 3px 0 0 #692c27, -3px -3px 0 0 #692c27,
  -3px 3px 0 0 #692c27, 3px -3px 0 0 #692c27;
  --tant-decorative-blue-color-decorative1-1: #1e76f0;
  --tant-decorative-blue-color-decorative1-2: #3583ef;
  --tant-decorative-blue-color-decorative1-3: #99b4f3;
  --tant-decorative-blue-color-decorative1-4: #eaeefd;
  --tant-decorative-orange-color-decorative2-1: #f27e19;
  --tant-decorative-orange-color-decorative2-2: #f1913d;
  --tant-decorative-orange-color-decorative2-3: #f7b79a;
  --tant-decorative-orange-color-decorative2-4: #fdefea;
  --tant-decorative-orange-color-decorative2-5: #e0a571;
  --tant-decorative-cyen-color-decorative3-1: #14c9d6;
  --tant-decorative-cyen-color-decorative3-2: #34cfda;
  --tant-decorative-cyen-color-decorative3-3: #9adee6;
  --tant-decorative-cyen-color-decorative3-4: #eaf7f9;
  --tant-decorative-purple-color-decorative4-1: #915aff;
  --tant-decorative-purple-color-decorative4-2: #915aff99;
  --tant-decorative-purple-color-decorative4-3: #915aff66;
  --tant-decorative-purple-color-decorative4-4: #915aff1a;
  --tant-decorative-purple-color-decorative4-5: #3a2466;
  --tant-decorative-dblue-color-decorative5-1: #0a1032;
  --tant-decorative-dblue-color-decorative5-2: #b9becf;
  --tant-decorative-yellow-color-decorative6-1: #f5b400;
  --tant-decorative-yellow-color-decorative6-2: #f5b40099;
  --tant-decorative-yellow-color-decorative6-3: #f5b40066;
  --tant-decorative-yellow-color-decorative6-4: #f5b4001a;
  --tant-data-v-multiple1-color-data-multiple1-1: #217dff;
  --tant-data-v-multiple1-color-data-multiple1-2: #f99239;
  --tant-data-v-multiple1-color-data-multiple1-3: #374bcd;
  --tant-data-v-multiple1-color-data-multiple1-4: #66d8e2;
  --tant-data-v-multiple1-color-data-multiple1-5: #ffbc04;
  --tant-data-v-multiple1-color-data-multiple1-6: #40aeff;
  --tant-data-v-multiple1-color-data-multiple1-7: #0a8ca0;
  --tant-data-v-multiple1-color-data-multiple1-8: #ffb676;
  --tant-data-v-multiple1-color-data-multiple1-9: #ee6c59;
  --tant-data-v-multiple1-color-data-multiple1-10: #7856ff;
  --tant-data-v-multiple1-color-data-multiple1-11: #a37af5;
  --tant-data-v-multiple1-color-data-multiple1-12: #48c6a8;
  --tant-data-v-single1-color-data-single1-1: #217dff;
  --tant-data-v-function-color-data-function-line: #dee1eb;
  --tant-data-v-function-color-data-function-tick: #dee1eb;
  /* --tant-data-v-function-color-data-function-grid: #ebedf3; */
  --tant-data-v-function-color-data-function-grid: #2f303b;
  --tant-data-v-function-color-data-function-shadow: #0104181a;
  --tant-data-v-function-color-data-function-label2: #747c94;
  --tant-data-v-function-color-data-function-label1: #9095a9;
  --tant-data-v-function-color-data-function-legend: #202241;
  --tant-data-v-function-color-data-function-serie-label: #202241;
  --tant-data-v-function-color-data-function-axis-title: #fff;
  --tant-data-v-function-color-data-function-zero-axis: #a1a7bb;
  --tant-data-v-function-color-data-function-axis-pointer: #a1a7bb;
  --tant-motion-duration-micro-fast: 100ms;
  --tant-motion-duration-micro-moderate: 150ms;
  --tant-motion-duration-micro-slow: 200ms;
  --tant-motion-duration-macro-faster: 250ms;
  --tant-motion-duration-macro-moderate: 300ms;
  --tant-motion-duration-macro-slower: 350ms;
  --tant-motion-duration-extra-slowest: 800ms;
  --tant-motion-easing-standard-linear: 0, 0, 1, 1;
  --tant-motion-easing-standard-ease-in: 0, 0, 0, 1;
  --tant-motion-easing-standard-ease-out: 0.2, 0, 0.8, 0.5;
  --tant-motion-easing-standard-ease-in-out: 0.42, 0, 0.58, 1;
  --tant-motion-easing-emphasized-linear: 0.2, 0, 0, 1;
  --tant-motion-easing-emphasized-ease-in: 0.05, 0.7, 0.1, 1;
  --tant-motion-easing-emphasized-ease-out: 0.3, 0, 0.8, 0.15;
  --tant-motion-easing-emphasized-ease-in-out: 0.6, 0, 0.14, 1;
  --tant-blue-blue-10: #f8fafe;
  --tant-blue-blue-20: #eaeefd;
  --tant-blue-blue-30: #cdd8fa;
  --tant-blue-blue-40: #99b4f3;
  --tant-blue-blue-50: #3583ef;
  --tant-blue-blue-60: #1e76f0;
  --tant-blue-blue-70: #185ec0;
  --tant-blue-blue-80: #124790;
  --tant-blue-blue-90: #0c2f60;
  --tant-blue-blue-100: #061830;
  --tant-cyan-cyan-10: #f3fafb;
  --tant-cyan-cyan-20: #eaf7f9;
  --tant-cyan-cyan-30: #ccedf1;
  --tant-cyan-cyan-40: #9adee6;
  --tant-cyan-cyan-50: #34cfda;
  --tant-cyan-cyan-60: #14c9d6;
  --tant-cyan-cyan-70: #10a1ab;
  --tant-cyan-cyan-80: #0c7980;
  --tant-cyan-cyan-90: #085056;
  --tant-cyan-cyan-100: #04282b;
  --tant-orange-orange-10: #fdf6f4;
  --tant-orange-orange-20: #fdefea;
  --tant-orange-orange-30: #fadacd;
  --tant-orange-orange-40: #f7b79a;
  --tant-orange-orange-50: #f1913d;
  --tant-orange-orange-60: #f27e19;
  --tant-orange-orange-70: #c26514;
  --tant-orange-orange-80: #914c0f;
  --tant-orange-orange-90: #61320a;
  --tant-orange-orange-100: #301905;
  --tant-purple-purple-10: #f7f4ff;
  --tant-purple-purple-20: #f1ecff;
  --tant-purple-purple-30: #ded3ff;
  --tant-purple-purple-40: #c0a9ff;
  --tant-purple-purple-50: #9670fc;
  --tant-purple-purple-60: #915aff;
  --tant-purple-purple-70: #7448cc;
  --tant-purple-purple-80: #573699;
  --tant-purple-purple-90: #3a2466;
  --tant-purple-purple-100: #1d1233;
  --tant-green-green-10: #f4faf7;
  --tant-green-green-20: #e9f6f1;
  --tant-green-green-30: #ccebde;
  --tant-green-green-40: #9adac1;
  --tant-green-green-50: #28ca9f;
  --tant-green-green-60: #16c294;
  --tant-green-green-70: #129b76;
  --tant-green-green-80: #0d7459;
  --tant-green-green-90: #094e3b;
  --tant-green-green-100: #04271e;
  --tant-yellow-yellow-10: #fef9f4;
  --tant-yellow-yellow-20: #fdf4e9;
  --tant-yellow-yellow-30: #fbe7cc;
  --tant-yellow-yellow-40: #f9d299;
  --tant-yellow-yellow-50: #f2bf32;
  --tant-yellow-yellow-60: #f5b400;
  --tant-yellow-yellow-70: #c49000;
  --tant-yellow-yellow-80: #936c00;
  --tant-yellow-yellow-90: #624800;
  --tant-yellow-yellow-100: #312400;
  --tant-red-red-10: #fdf5f4;
  --tant-red-red-20: #fbebea;
  --tant-red-red-30: #f7d1cf;
  --tant-red-red-40: #f1a59f;
  --tant-red-red-50: #eb614d;
  --tant-red-red-60: #e84d37;
  --tant-red-red-70: #ba3e2c;
  --tant-red-red-80: #8b2e21;
  --tant-red-red-90: #5d1f16;
  --tant-red-red-100: #2e0f0b;
  --tant-dblue-dblue-5: #f9f9fb;
  --tant-dblue-dblue-10: #f6f6f9;
  --tant-dblue-dblue-15: #f2f3f8;
  --tant-dblue-dblue-20: #ebedf3;
  --tant-dblue-dblue-30: #dee1eb;
  --tant-dblue-dblue-40: #b9becf;
  --tant-dblue-dblue-50: #a1a7bb;
  --tant-dblue-dblue-60: #747c94;
  --tant-dblue-dblue-70: #464762;
  --tant-dblue-dblue-80: #202241;
  --tant-dblue-dblue-90: #0a1032;
  --tant-dblue-dblue-100: #000;
  --tant-white-white-10: #ffffff1a;
  --tant-white-white-20: #fff3;
  --tant-white-white-30: #ffffff4d;
  --tant-white-white-40: #fff6;
  --tant-white-white-50: #ffffff80;
  --tant-white-white-60: #fff9;
  --tant-white-white-70: #ffffffb3;
  --tant-white-white-80: #fffc;
  --tant-white-white-90: #ffffffe6;
  --tant-white-white-100: #fff;
}

/**
 * 表格样式
 */
.ta-next-table .ta-next-table-layout .ta-base-talbe-icon {
  display: inline-block;
  width: 25px;
  height: 22px;
  margin-left: 8px;
  padding: 3px 6px;
  color: var(--color-blue-9);
  background-color: var(--color-deep-blue-0);
  border-radius: 4px;
  cursor: pointer;
}

.ta-next-table .ta-next-table-layout .ta-base-talbe-icon:hover {
  background: var(--color-deep-blue-1);
}

.ta-next-table .ta-next-table-layout .ta-base-table-selected .ta-base-talbe-icon {
  background-color: var(--color-cyan-gray-0);
}

.ta-next-table .ta-next-table-layout .ta-base-table-selected .ta-base-talbe-icon:hover {
  background: var(--color-deep-blue-1);
}

.ta-next-table .ta-next-table-layout .art-table-header {
  scrollbar-width: none;
}

.ta-next-table .ta-next-table-layout .art-table-header table {
  width: 100%;
}

.ta-next-table .ta-next-table-layout .art-table-body {
  position: relative;
}

.ta-next-table .ta-next-table-layout .art-table-body table {
  width: 100%;
}

.ta-next-table .ta-next-table-layout .art-table-row td {
  word-wrap: break-word;
  word-break: break-all;
}

.ta-next-table .ta-next-table-layout .art-table-row td:hover .add-group {
  visibility: visible;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-jump {
  font-weight: 500;
  cursor: pointer;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-jump:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-abnormal {
  position: relative;
  top: -6px;
  right: -14px;
  height: 0;
  overflow: visible !important;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-abnormal div {
  float: right;
  height: 15px;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-middle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-compared {
  line-height: 36px;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-compared .compare-new {
  color: var(--text-color);
  font-size: 12px;
  line-height: 14px;
  text-align: left;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-compared .compare-trend {
  float: right;
  margin-left: 10px;
  font-size: 12px;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-compared .compare-old {
  color: var(--widget-color);
  font-size: 12px;
  line-height: 14px;
  text-align: left;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-center-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  width: 73%;
  max-width: 92px;
  margin: auto;
}

.ta-next-table .ta-next-table-layout .art-table-row td .group-con {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ta-next-table .ta-next-table-layout .art-table-row td .group-con-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-to-list {
  text-decoration: underline;
  cursor: pointer;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-to-list:hover {
  color: #8d0088;
}

.ta-next-table .ta-next-table-layout .art-table-row td .ta-base-table-multi-line {
  display: flex;
  flex-direction: column;
}

.ta-next-table .ta-next-table-layout .ta-base-table-limit-multi {
  display: -webkit-box;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 5;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.ta-next-table .ta-next-table-layout .ta-base-table-limit-double {
  display: -webkit-box;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.ta-next-table .ta-next-table-layout .ta-base-table-limit-single {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon-dis,
.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon {
  position: relative;
  left: -2px;
  display: inline-block;
  width: 0;
}

.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon-dis span,
.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon span {
  display: inline-block;
  width: 16px;
  color: var(--widget-color);
  font-size: 16px;
  cursor: pointer;
}

.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon-dis span:hover,
.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon span:hover {
  color: var(--primary-color);
}

.ta-next-table .ta-next-table-layout .ta-base-table-sort-icon-dis span {
  color: var(--widget-disable-color);
}

.ta-next-table .ta-next-table-layout .ta-base-table-hidden {
  visibility: hidden;
}

.ta-next-table .ta-next-table-layout .ta-base-table-sort-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ta-next-table .ta-next-table-layout .art-empty-table-cell {
  height: 300px;
}

.ta-next-table .art-table-header-row th > div > div:nth-child(3) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-top: 7px;
  margin-left: -4px !important;
  border-radius: 16px;
}

/*
*  CKEditor 样式覆盖
*/
.ta-ck-editor-heading1-custom {
  display: block;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header1-medium);
}

.ta-ck-editor-heading2-custom {
  display: block;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header2-medium);
}

.ta-ck-editor-heading3-custom {
  display: block;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header3-medium);
}

.ta-ck-editor-heading4-custom {
  display: block;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header4-medium);
}

.ta-ck-editor-heading5-custom {
  display: block;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header5-medium);
}

.ta-ck-editor-paragraph-custom {
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-body-font-body-regular);
}

.ck {
  --ck-dropdown-max-width: 200px;
}

.ck.ck-dropdown.ck-heading-dropdown .ck-button:focus {
  border: none !important;
  box-shadow: none !important;
}

.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__panel .ck-list__item {
  min-width: 110px !important;
  padding: 4px !important;
}

.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__panel .ck-list__item .ck-button {
  border: none !important;
}

.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__panel .ck-list__item .ck-button.ck-on {
  color: var(--tant-text-gray-color-text1-1) !important;
  background: var(--tant-secondary-color-secondary-fill-active) !important;
  border-radius: 4px !important;
  box-shadow: none !important;
}

.ck.ck-dropdown.ck-heading-dropdown .ck-dropdown__panel .ck-list__item .ck-button:hover:not(.ck-disabled) {
  color: var(--tant-text-gray-color-text1-1) !important;
  background: var(--tant-secondary-color-secondary-fill-hover);
  border-radius: 4px !important;
}

.ck.ck-dropdown__panel {
  overflow: hidden !important;
  border: none !important;
  border-radius: 4px !important;
  box-shadow: var(--tant-small-shadow-small-overall) !important;
}

.ck.ck-balloon-panel {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: var(--tant-small-shadow-small-overall) !important;
}

.ck.ck-balloon-panel .ck.ck-toolbar {
  border: none !important;
  border-radius: 8px !important;
}

.ck.ck-balloon-panel.ck-toolbar-container::before {
  margin-bottom: 0 !important;
  border-color: var(--tant-bg-white-color-bg1-1) transparent transparent transparent !important;
  border-width: var(--ck-balloon-arrow-height) var(--ck-balloon-arrow-half-width) 0 var(--ck-balloon-arrow-half-width);
}

.ck-toolbar__separator {
  height: 24px !important;
  margin-top: 7px !important;
}

.ck.ck-icon.ck-dropdown__arrow {
  display: none;
}

:root[lang='ja-JP'] {
  --font-family-jp: 'TA-NotoSansJP', 'TA-Roboto', 'Hiragino Sans', -apple-system,
  'TA-SourceHanSansSC', blinkmacsystemfont, 'Noto Sans JP', 'Segoe UI', roboto, 'Helvetica Neue',
  arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
  --tant-body-font-body-regular: 400 12px/22px var(--font-family-jp);
  --tant-body-font-body-medium: 500 12px/22px var(--font-family-jp);
  --tant-description-font-description-regular: 400 12px/20px var(--font-family-jp);
  --tant-description-font-description-medium: 500 12px/20px var(--font-family-jp);
  --tant-header-font-header5-medium: 500 16px/24px var(--font-family-jp);
  --tant-header-font-header5-regular: 400 16px/24px var(--font-family-jp);
  --tant-header-font-header4-medium: 500 20px/30px var(--font-family-jp);
  --tant-header-font-header3-medium: 500 24px/36px var(--font-family-jp);
  --tant-header-font-header2-medium: 500 28px/38px var(--font-family-jp);
  --tant-header-font-header1-medium: 500 38px/58px var(--font-family-jp);
}

:root[lang='ko-KR'] {
  --font-family-kr: 'TA-NotoSansKR', 'TA-Roboto', 'Noto Sans KR', -apple-system,
  'TA-SourceHanSansSC', blinkmacsystemfont, 'Segoe UI', roboto, 'Helvetica Neue', arial,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
  --tant-body-font-body-regular: 400 12px/22px var(--font-family-kr);
  --tant-body-font-body-medium: 500 12px/22px var(--font-family-kr);
  --tant-description-font-description-regular: 400 12px/20px var(--font-family-kr);
  --tant-description-font-description-medium: 500 12px/20px var(--font-family-kr);
  --tant-header-font-header5-medium: 500 16px/24px var(--font-family-kr);
  --tant-header-font-header5-regular: 400 16px/24px var(--font-family-kr);
  --tant-header-font-header4-medium: 500 20px/30px var(--font-family-kr);
  --tant-header-font-header3-medium: 500 24px/36px var(--font-family-kr);
  --tant-header-font-header2-medium: 500 28px/38px var(--font-family-kr);
  --tant-header-font-header1-medium: 500 38px/58px var(--font-family-kr);
}

html,
body,
#portalRoot {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

:root {
  --init-font-family: 'TA-Roboto', pingfang sc, -apple-system, 'TA-SourceHanSansSC',
  'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif',
  'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

@font-face {
  font-weight: 400;
  font-family: 'TA-Roboto';
  src: url('../font/Roboto-Regular.ttf') format('woff2');
}

@font-face {
  font-weight: 500;
  font-family: 'TA-Roboto';
  src: url('../font/Roboto-Medium.ttf') format('woff2');
}

@font-face {
  font-weight: 700;
  font-family: 'TA-Roboto';
  src: url('../font/Roboto-Bold.ttf') format('woff2');
}

@font-face {
  font-weight: 400;
  font-family: 'TA-Arimo';
  src: url('../font/Arimo-Regular.ttf') format('woff2');
}

@font-face {
  font-weight: 300;
  font-family: 'TA-Arimo-Italic';
  src: url('../font/Arimo-Italic.ttf') format('woff2');
}

@font-face {
  font-weight: 700;
  font-family: 'TA-Arimo-BoldItalic';
  src: url('../font/Arimo-BoldItalic.ttf') format('woff2');
}

@font-face {
  font-weight: 700;
  font-family: 'TA-Arimo-Bold';
  src: url('../font/Arimo-Bold.ttf') format('woff2');
}

@font-face {
  font-weight: 400;
  font-family: 'TA-NotoSansJP';
  src: url('../font/Noto_Sans_JP/NotoSansJP-Regular.ttf') format('woff2');
  font-display: swap;
}

@font-face {
  font-weight: 500;
  font-family: 'TA-NotoSansJP';
  src: url('../font/Noto_Sans_JP/NotoSansJP-Medium.ttf') format('woff2');
  font-display: swap;
}

@font-face {
  font-weight: 700;
  font-family: 'TA-NotoSansJP';
  src: url('../font/Noto_Sans_JP/NotoSansJP-Bold.ttf') format('woff2');
  font-display: swap;
}

@font-face {
  font-weight: 400;
  font-family: 'TA-NotoSansKR';
  src: url('../font/Noto_Sans_JP/NotoSansKR-Medium.ttf') format('woff2');
  font-display: swap;
}

@font-face {
  font-weight: 500;
  font-family: 'TA-NotoSansKR';
  src: url('../font/Noto_Sans_JP/NotoSansKR-SemiBold.ttf') format('woff2');
  font-display: swap;
}

@font-face {
  font-weight: 700;
  font-family: 'TA-NotoSansKR';
  src: url('../font/Noto_Sans_JP/NotoSansKR-Bold.ttf') format('woff2');
  font-display: swap;
}

//@font-face {
//  font-weight: 400;
//  font-family: 'TA-SourceHanSansSC';
//  src: url('../font/SourceHanSansSC/SourceHanSansSC-Normal.otf') format('woff2');
//  font-display: swap;
//}
//@font-face {
//  font-weight: 500;
//  font-family: 'TA-SourceHanSansSC';
//  src: url('../font/SourceHanSansSC/SourceHanSansSC-Regular.otf') format('woff2');
//  font-display: swap;
//}
//@font-face {
//  font-weight: 700;
//  font-family: 'TA-SourceHanSansSC';
//  src: url('../font/SourceHanSansSC/SourceHanSansSC-Bold.otf') format('woff2');
//  font-display: swap;
//}
html body {
  overflow: auto;
  color: var(--tant-text-gray-color-text1-2);
  font-family: 'TA-Roboto', -apple-system, 'TA-SourceHanSansSC', BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  background: var(--tant-bg-white-color-bg1-1);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html[lang='ja-JP'] body {
  font-family: var(--font-family-jp);
}

html[lang='jo-KR'] body {
  font-family: var(--font-family-kr);
}

* {
  scrollbar-width: thin;
  scrollbar-color: #e6e6e6 var(--tant-bg-white-color-bg1-1);
}

#portalRoot {
  min-width: 1200px;
}

#portalRoot .microVaWrapper {
  height: 100%;
}

#portalRoot .microTaWrapper {
  height: 100%;
}

#portalRoot .microHermesWrapper {
  height: 100%;
}

#tafixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

body p {
  margin-bottom: 0;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--tant-bg-white-color-bg1-1);
}

::-webkit-scrollbar-thumb {
  min-width: 24px;
  min-height: 24px;
  background-color: #e6e6e6;
  border: 1px solid var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c3c3c3;
}

::selection {
  color: unset;
  background: var(--tant-primary-color-primary-fill-active);
}

html[data-theme='dark'] ::selection {
  color: var(--tant-text-gray-color-text1-1);
  background: var(--tant-primary-color-primary-hover);
}

::-ms-input-placeholder {
  color: var(--tant-text-gray-color-text1-4);
  font-size: var(--font-size-body-default);
}

::placeholder {
  color: var(--tant-text-gray-color-text1-4);
  font-size: var(--font-size-body-default);
}

.ant-modal-root #vaRoot {
  height: calc(100vh - 120px);
}

.art-table ::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.opacity {
  opacity: 0;
}

#waterMark {
  position: fixed;
  top: 64px;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  display: block;
  width: 100%;
  background: none;
  visibility: visible;
  opacity: 1;
  pointer-events: none;
}

#panel-export-iframe {
  position: fixed;
  top: -100000px;
  left: -100000px;
  z-index: 0;
}
