/**
 * 简化的指标详情获取工具
 */
export default function useIndicatorDetailHelper() {
  /**
   * 从sessionStorage获取指标详情
   * @param code 指标编码
   */
  const getIndicatorDetailByCode = (code: string) => {
    try {
      const storedData = sessionStorage.getItem('indicator-list')
      if (!storedData) {
        return null
      }
      const indicatorList = JSON.parse(storedData)
      if (!Array.isArray(indicatorList)) {
        return null
      }
      const indicator = indicatorList.find((item: any) => item.code === code)
      if (indicator) {
        return indicator
      }
      return null
    } catch (error) {
      console.error('获取指标详情失败:', error)
      return null
    }
  }
  
  /**
   * 创建指标映射
   * @param uniqueData 表格数据中的唯一指标数据
   */
  const createIndicatorMap = (uniqueData: any[]) => {
    const indicatorMap = new Map()
    uniqueData.forEach(item => {
      if (item && item.code) {
        const detail = getIndicatorDetailByCode(item.code)
        if (detail) {
          indicatorMap.set(item.code, detail)
        } else {
          console.warn(`指标 ${item.code} 未找到详情数据`)
        }
      } else {
        console.warn('指标数据缺少 code 字段:', item)
      }
    })
    return indicatorMap
  }
  
  return {
    getIndicatorDetailByCode,
    createIndicatorMap
  }
}