

<template>
    <div class="guide">
        <div class="event-filter-box">
            <div v-for="(item,index) in eventListData" :key="index" class="action-row">
                <div class="filter-row-eventRow">
                    <div class="action-left">
                        <i class="drag-index">{{ index + 1 }}</i>
                        <icon-drag-dot-vertical class="hover-drag"/>
                        <div class="row-content">
                            <div v-if="item.rename" class="rename">
                                <a-input v-model="item.displayName" placeholder="请输入" @blur="reNameBlur(index)"/>
                            </div>
                            <div>
                                <div v-for="(event,eventIndex) in  item.eventList" :key="eventIndex" class="event-item">
                                    <EventIndicatorSelect :panel-data="event" @analysis-index-change="panelSelectChange(index,eventIndex,$event)"/>
                                    <div>
                                        <eventScreenPopup :info="event" @event-screen="eventScreen(index,eventIndex,$event)"/>
                                    </div>
                                    <div>
                                        <relevancePopup :info="event"/>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="action-right">
                        <a-space align="center">
                            <a-tooltip content="重命名" position="top">
                                <a-button class="btn-bg btn-26" @click="() => item.rename = true">
                                    <template #icon>
                                        <icon-pen />
                                    </template>
                                </a-button>
                            </a-tooltip>
                            <a-tooltip :content="item.filtersList.length>0 ? '已达分组上限' : '添加分组'" position="top">
                                <a-button class="btn-bg btn-26" :disabled="item.filtersList.length>0" @click="add(index)">
                                    <template #icon>
                                        <img class="option-icon" src="/icon/group.svg" alt="">
                                    </template>
                                </a-button>
                            </a-tooltip>
                            <a-tooltip content="删除" position="top">
                                <a-button v-if="eventListData.length>1" class="btn-bg-delete btn-26" @click="deleteList(index)">
                                    <template #icon>
                                        <icon-delete />
                                    </template>
                                </a-button>
                            </a-tooltip>
                        </a-space>
                    </div>
                </div>
                <div class="row-filters">
                    <div class="relation-editor">
                        <div v-if="item.filtersList.length>0" class="word" style="margin-right: 8px;">
                            按
                        </div>
                        <div class="relation-main">
                            <div v-for="(filter,filterIndex) in item.filtersList" :key="filterIndex" class="relation-row">
                                <div class="multi-filter-condition">
                                    <div v-if="filter.subFilters && filter.subFilters.length>1" class="relation-editor">
                                        <div class="relation-main">
                                            <div class="relation-row">
                                                <div class="multi-filter-condition">
                                                   <div v-for="(val,valIndex) in filter.subFilters" :key="valIndex" class="sub-action-row">
                                                        <div class="sub-action-left">
                                                            <attrEnumSelect :info="val" :code-list="item.eventList" @tabs-change="subfilChange(index,filterIndex,valIndex,$event)"/>
                                                         </div>
                                                         <div v-if="item.filtersList.length>0" class="word" style="margin: 0 8px;">
                                                            分组
                                                        </div>
                                                        <div class="sub-action-right">
                                                            <a-space align="center">
                                                                <a-tooltip content="删除" position="top">
                                                                    <a-button class="btn-bg-delete btn-26" @click="removeMultipleListItem(index,filterIndex,valIndex)">
                                                                        <template #icon>
                                                                            <icon-close-circle />
                                                                        </template>
                                                                    </a-button>
                                                                </a-tooltip>
                                                            </a-space>
                                                        </div>
                                                    </div>
                                                </div>
                                             </div>
                                        </div>
                                    </div>
                                    <div v-else class="sub-action-row">
                                        <div class="sub-action-left">
                                            <attrEnumSelect :info="filter" :code-list="item.eventList" @tabs-change="filChange(index,filterIndex,$event)"/>
                                        </div>
                                        <div v-if="item.filtersList.length>0" class="word" style="margin: 0 8px;">
                                            分组
                                        </div>
                                        <div class="sub-action-right">
                                            <a-space align="center">
                                                <a-tooltip content="删除" position="top">
                                                    <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index,filterIndex)">
                                                        <template #icon>
                                                            <icon-close-circle />
                                                        </template>
                                                    </a-button>
                                                </a-tooltip>
                                            </a-space>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row-foot">
            <div class="ta-filter-button" @click="addAnalysisIndex">
                <icon-plus class="action"/>
                <span class="label">归因事件</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import {Indicator} from "@/api/analyse/type";
import _ from 'lodash';
import {toolStore} from '@/store';
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import eventScreenPopup from "@/views/analyse/components/EventScreenPopup.vue"
import {getDefaultObj} from "@/views/analyse/components/util/verify";
import relevancePopup from "./relevancePopup.vue"

const eventBus = useEventBus('eventList');
const emits = defineEmits(['indicatorsChange'])
const props = defineProps({
  handleList:{
    type:Array,
    default:() => []
  }
})
const indicator = ref<Indicator[]>([])

const toolData = toolStore();
const eventListData = ref<any>([])
const evtLists = ref<any>([])
const init = async () => {
  evtLists.value = toolData.toolModelList.length > 0 
    ? toolData.toolModelList.flatMap(category => category.items || [])
    : (await toolData.fetchAllModalList()).flatMap(category => category.items || []);
  const appOpenData = getDefaultObj(evtLists.value)
  const list = [
        {
            name: '',
            rename: false,
            type: 'event',
            displayType: {type:'default',decimalNum:2,thousandSep: 1},
            displayName: '',
            eventList: [
                {
                    eventName: appOpenData.name,
                    eventCode: appOpenData.code,
                    eventDisplayName: appOpenData.displayName,
                    eventType:appOpenData.type,
                    type: appOpenData.objectType,
                    eventAttrCode: '',
                    eventAttrName: '总次数',
                    filter: {}
                },
            ],
            formula: '',
            filtersList: [],
            description:''
        }
    ]
  eventListData.value = _.cloneDeep(list)
}
init()
// eventListData.value = JSON.parse(JSON.stringify(props.handleList))
const reNameBlur = (index:number) => {
    if(eventListData.value[index].displayName === ''){
        eventListData.value[index].rename = false
    }
}

const panelSelectChange = (index:number,eventIndex:number,e:any) => {
    const filter = {
        ...eventListData.value[index].eventList[eventIndex],
        ...e
    }
    const fieldsToRemove = e.type === 'indicator'
    ? ['eventCode', 'eventName', 'eventDisplayName']
    : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

    const cleanedFilter = _.omit(filter, fieldsToRemove);
    eventListData.value[index].eventList[eventIndex] = cleanedFilter;
    eventListData.value[index].name = e.eventName || e.indicatorName
}
const eventScreen = (index:number,eventIndex:number,e:any) => {
    eventListData.value[index].eventList[eventIndex].filter = e;
}
// tabselect组件传参改变值
const subfilChange = (index:number,filterIndex:number,valIndex:number,e:any) => {
    const {objectName,type,displayName, objectType, calcuSymbol, thresholds, filterType, objectId} = e
    const filter = { ...eventListData.value[index].filtersList[filterIndex].subFilters[valIndex] };
    filter.objectName = objectName;
    filter.displayName = displayName;
    filter.type = type;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    filter.calcuSymbol = calcuSymbol
    filter.thresholds = thresholds
    eventListData.value[index].filtersList[filterIndex].subFilters[valIndex] = filter;
}
const filChange = (index:number,filterIndex:number,e:any) => {
    const {objectName,type,displayName, objectType, calcuSymbol, thresholds, filterType, objectId} = e
    const filter = { ...eventListData.value[index].filtersList[filterIndex] };
    filter.objectName = objectName;
    filter.displayName = displayName;
    filter.type = type;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    filter.calcuSymbol = calcuSymbol
    filter.thresholds = thresholds
    eventListData.value[index].filtersList[filterIndex] = filter;
}
// 添加事件指标
const addAnalysisIndex = () => {
    eventListData.value.push({
        name:evtLists.value[0].eventName,
        type:'event',
        displayName:`${evtLists.value[0].eventDisplayName}.总次数`,
        displayType:{},
        unitName:'',
        isAnd:true,
        filtersList:[],
        eventList:[
            {
                eventName:evtLists.value[0].eventName,
                eventDisplayName:evtLists.value[0].eventDisplayName,
                eventType:evtLists.value[0].eventType,
                eventAttrCode:'',
                eventAttrName:'总次数',
                type:'event',
                filter:{}
            }
        ],
    })
    eventBus.emit(eventListData.value);
}
// 添加并列条件
const add = (index:number) => {
    eventListData.value[index].filtersList.push({
        name:'',
        displayName:'',
        isAnd:true,
        objectType:'',
        objectId:'',
        filterType:'',
        calcuSymbol:'',
        thresholds:[],
        subFilters:[]
    } as never)
}
const deleteList = (index:number) => {
    eventListData.value.splice(index,1)
}

const deleteFilters = (index1:number,index:number) => {
    eventListData.value[index1].filtersList.splice(index,1)
}
const removeMultipleListItem = (index1:number,index:number,num:number) => {
    eventListData.value[index1].filtersList[index].subFilters.splice(num,1)
    if(eventListData.value[index1].filtersList[index].subFilters.length === 1){
        // 删的剩最后两个的时候，外层变为剩下的一个
        const filter = {...eventListData.value[index1].filtersList[index].subFilters[0]};
        filter.isAnd = true;
        filter.subFilters = [];
        eventListData.value[index1].filtersList[index] = filter
    }
}

watch(eventListData, (newValue, oldValue) => {
    indicator.value = newValue.map(item => {
        return {
            displayName: item.displayName,
            displayType:item.displayType,
            name: item.name,
            type: item.type,
            eventList: item.eventList.map(event => {
                const processedEvent = { ...event };
                if (processedEvent.type === 'indicator') {
                    delete processedEvent.eventCode;
                    delete processedEvent.eventName;
                    delete processedEvent.eventDisplayName;
                } else {
                    delete processedEvent.indicatorCode;
                    delete processedEvent.indicatorName;
                    delete processedEvent.indicatorDisplayName;
                }
                return processedEvent;
            }),
            filter: {
                logicalOperation:'and',
                filters:item.filtersList.map(item => {
                    return {
                        objectName: item.objectName,
                        displayName: item.displayName,
                        type:item.type,
                        logicalOperation: item.isAnd ? 'and' : 'or',
                        objectType: item.objectType,
                        objectId: item.objectId,
                        filterType: item.filterType,
                        calcuSymbol: item.calcuSymbol,
                        thresholds: item.thresholds,
                        subFilters: item.subFilters
                    }
                    
                })
            }
        }
    })
    emits('indicatorsChange',indicator.value)
    eventBus.emit(newValue); 

},{immediate:true,deep:true})
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .subTitle{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
        }
        .action-row{
            position: relative;
            height: auto;
            padding-right: 24px;
            padding-left: 24px;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            .handle-box{
                opacity: 0;
            }
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .drag-index{
                    margin-top: 9px;
                    margin-right: 12px;
                    flex-shrink: 0;
                    width: 24px;
                    height: 24px;
                    color: var(--tant-bg-white-color-bg1-1);
                    font-size: 12px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    background-color: var(--tant-secondary-color-secondary-default);
                    border-radius: 4px;
                    transition: all .3s;
                    opacity: 1;
                }
                .hover-drag{
                    position: absolute;
                    left: 0;
                    margin-top: 9px;
                    margin-left: 31px;
                    flex-shrink: 0;
                    // width: 24px;
                    // height: 24px;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    opacity: 0;
                    transition: all .3s;
                }
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 4px 0;
                        overflow: hidden;
                        line-height: 32px;
                        white-space: normal;
                        
                        
                    }
                }
            }
            .action-right{
                position: absolute;
                top: 0;
                right: 4px;
                min-width: 40px;
                height: 36px;
                padding-top: 0 !important;
                display: flex;
                align-items: center;
                opacity: 0;
                transition: opacity .3s;
            }
            .filter-btn{
                display: inline-flex;
                align-items: center;
                min-width: 40px;
                max-width: 200px;
                height: 26px;
                padding: 0 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
                box-sizing: border-box;
                .btn-icon{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    margin-right: 5px;
                }
                .filter-label{
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    color: var(--tant-text-gray-color-text1-2);
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: top;
                }

                &:hover{
                    border-color: var(--tant-primary-color-primary-hover);
                }
            }
            .row-word{
                display: inline-block;
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
                vertical-align: top;
            }
            &:hover{
                background-color: var(--tant-fill-color-fill1-2);
                .handle-box{
                    opacity: 1;
                    transition: all .3s;
                }
                .action-left .drag-index{
                    opacity: 0;
                    transition: all .3s;
                }
                .action-left .hover-drag{
                    opacity: 1;
                    transition: all .3s;
                }
                :deep(.filter-btn){
                    background: #fff;
                }
                :deep(.filter-icon){
                    background: #fff;
                }
                .sub-action-left :deep(.filter-btn){
                    background: #fff;
                }
                .action-right{
                    opacity: 1;
                } 
            }
        }
        .row-filters{
            padding-left: 38px;
            .relation-editor{
                box-sizing: border-box;
                position: relative;
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
                .word{
                    color: var(--tant-text-gray-color-text1-3);
                }
                .relation-relation {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: flex-start;
                    width: 24px;
                    margin-right: 8px;
                    flex-shrink: 0;
                    .relation-relation-line {
                        position: absolute;
                        left: 12px;
                        width: 1px;
                        background-color: var(--tant-border-color-border1-2);
                        transition: all .3s;
                        top: 6px;
                        height: calc(100% - 12px);
                    }
                    .relation-relation-value{
                        position: absolute;
                        text-transform: uppercase;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%);
                        height: 24px;
                        padding: 0 5px;
                        margin-top: -12px;
                        color: var(--tant-text-gray-color-text1-2);
                        font-size: 12px;
                        line-height: 22px;
                        text-align: center;
                        background-color: #fff;
                        border: 1px solid var(--tant-border-color-border1-2);
                        border-radius: 4px;
                        cursor: pointer;
                        word-break: keep-all;
                        transition: all .3s;
                    }
                    &:hover .relation-relation-line{
                        background-color: var(--tant-primary-color-primary-default);
                    }
                    &:hover .relation-relation-value{
                        color: var(--tant-primary-color-primary-default);
                        border-color: var(--tant-primary-color-primary-default);
                    }
                }
                .relation-main{
                    flex: 1 1;
                    .relation-row{
                        box-sizing: border-box;
                        .multi-filter-condition{
                            .sub-action-row{
                                padding: 4px 0;
                                align-items: center;
                                display: flex;
                                height: auto;
                                .sub-action-left{
                                    align-items: flex-start;
                                    height: auto;
                                    display: flex;
                                }
                                .sub-action-right{
                                    align-items: flex-start;
                                    height: auto;
                                    display: flex;
                                    display: flex;
                                    align-items: center;
                                    opacity: 0;
                                    transition: opacity .3s;
                                }
                                &:hover .sub-action-right{
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }
        .row-foot{
            margin: 0;
            padding-left: 24px;
            transition: all .3s;
            .ta-filter-button{
                padding: 6px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                font-size: 14px;
                transition: all .3s;
                .action{
                    border-radius: 4px;
                    background-color: var(--tant-primary-color-primary-fill);
                    color: var(--tant-primary-color-primary-default);
                    margin-right: 8px;
                    padding: 3px;
                    font-size: 18px;
                }
                .label{
                    color: var(--tant-primary-color-primary-default);
                }
                &:hover .action{
                    background-color: var(--tant-primary-color-primary-fill-hover);
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>