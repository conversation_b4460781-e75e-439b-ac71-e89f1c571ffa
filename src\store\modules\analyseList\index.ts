import {defineStore} from 'pinia';
import {getAnalyseClusterList, getAnalyseEventList, getAnalyseIndicatorList, getAnalyseTagList, getAnalyseUserList, getOperationFilterList, getOperationGroupList,} from "@/api/analyse/api";
import {getAnalyzeSubjectList} from "@/api/setting/api";
import {IndicatorType} from "@/api/enum";

const analyseStore = defineStore('analyseData', {
  state: () => ({
    evtLists: [] as any,  // 事件列表
    indLists: [] as any, // 指标列表
    userLists: [] as any, // 用户属性列表
    userGroups: [] as any, // 用户分群列表
    userTags: [] as any,  // 用户标签列表
    subjectLists: [] as any, // 分析主体
    eventSubjectLists: [] as any, // 事件分析主体
    operationIndexLists: [] as any, // 运营分析指标
    operationFilterLists: [] as any, // 应用分析过滤列表
    operationGroupLists: [] as any, // 应用分析分组列表
    revenueIndexLists: [] as any, // 变现分析指标
    spendIndexLists: [] as any, // 买量分析指标
    favoriteEventsIndictors: [] as any[], // 收藏的事件指标列表
    attributionIndexLists: [] as any, // 归因分析指标
  }),
  actions: {
    async fetchData(apiFunc: Function, stateKey: string, ...args: any) {
      try {
        const res = await apiFunc(...args);
        if (res) {
          this[stateKey] = res;
          return res;
        }
        console.log(`${stateKey} 数据为空:`, res);
        return [];
      } catch (error) {
        console.error(`获取${stateKey}失败:`, error);
        throw error;
      }
    },

    fetchEvtInfo() {
      return this.fetchData(getAnalyseEventList, 'evtLists', {
        types: 0,
        inApp: 1
      });
    },

    fetchIndInfo() {
      return this.fetchData(getAnalyseIndicatorList, 'indLists', {
        types: [IndicatorType.EVENT, IndicatorType.RETENTION],
        inApp: 1
      });
    },
    // 获取运营指标
    fetchOperationIndexInfo(subjectCode:string) {
      return this.fetchData(getAnalyseIndicatorList, 'operationIndexLists',
        {
          types: [IndicatorType.OPERATION],
          // inApp: 1,
          subjectCode
        })
    },
    // 获取归因指标
    fetchAttributionIndexInfo(subjectCode:string) {
      return this.fetchData(getAnalyseIndicatorList, 'attributionIndexLists',
        {
          types: [IndicatorType.TRAFFIC],
          inApp: 1,
          subjectCode
        })
    },
    fetchUserInfo() {
      return this.fetchData(getAnalyseUserList, 'userLists');
    },

    fetchGroupInfo() {
      return this.fetchData(getAnalyseClusterList, 'userGroups');
    },

    fetchTagInfo() {
      return this.fetchData(getAnalyseTagList, 'userTags');
    },

    fetchSubjectLists(source?: string[]) {
      return this.fetchData(getAnalyzeSubjectList, 'subjectLists', source || []);
    },
    fetchEventSubjectLists(source?: string[]) {
      return this.fetchData(getAnalyzeSubjectList, 'eventSubjectLists', source || []);
    },
    fetchOperationFilterLists(code: string) {
      return this.fetchData(getOperationFilterList, 'operationFilterLists', code);
    },

    fetchOperationGroupLists(code: string) {
      return this.fetchData(getOperationGroupList, 'operationGroupLists', code);
    },
     // 添加收藏
     addFavorite(item: any) {
      if (!this.favoriteEventsIndictors.some(event => event.eventCode === item.eventCode)) {
        this.favoriteEventsIndictors.push({ ...item, isCollect: true });
      }
    },

    // 取消收藏
    removeFavorite(eventCode: string) {
      this.favoriteEventsIndictors = this.favoriteEventsIndictors.filter(
        event => event.eventCode !== eventCode
      );
    },
  },
});

export default analyseStore;