<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import {graphic} from 'echarts';
import useLoading from '@/hooks/loading';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import _ from "lodash";
import {IndicatorQueryResult, IndicatorQueryResultData, WsEventAnalysisResultData} from "@/api/report/type";

interface Props {
  /**
   * 展示label
   */
  showLabel: boolean

  /**
   * 报表数据
   */
  reportAnalysisData: WsEventAnalysisResultData;
}

const props = defineProps<Props>()

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const {loading, setLoading} = useLoading(true);
const xAxis = ref<string[]>([]);
const ySeries = ref([]);
const legendData = ref([]);


const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '1%',
      top: '20',
      bottom: '50',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      type: 'scroll',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      offset: 2,
      data: xAxis.value,
      boundaryGap: false,
      axisLabel: {
        color: '#4E5969',
        // formatter(value: number, idx: number) {
        //   // if (idx === 0) return ;
        //   // if (idx === xAxis.value.length - 1) return;
        //   if (value.toString().length > 6) return value.toString().slice(6, 10);
        //   return value;
        // },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        interval: (idx: number) => {
          if (idx === 0) return false;
          if (idx === xAxis.value.length - 1) return false;
          return true;
        },
        lineStyle: {
          color: '#E5E8EF',
        },
      },
      axisPointer: {
        show: true,
        lineStyle: {
          color: '#23ADFF',
          width: 2,
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        formatter(value: any, idx: number) {
          if (idx === 0) return value;
          if (value > 1000 && value < 10000) return `${value / 1000}千`
          if (value >= 10000) return `${value / 10000}万`
          return `${value}`;
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E8EF',
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

function renderChart(wsResultData: WsEventAnalysisResultData) {
  if (!wsResultData) {
    return
  }
  const xData = wsResultData?.x || []
  const y = wsResultData?.y
  xAxis.value = [...xData]
  const ySeriesResult = [];
  y?.forEach((record: IndicatorQueryResult) => {
    const displayName = record.displayName;
    const yData = record?.yData;
    if (_.isEmpty(displayName) || _.isEmpty(yData)) {
      return undefined
    }
    yData.forEach((yDataItem: IndicatorQueryResultData) => {
      const groupsName = yDataItem.group.join('.');
      const key = `${displayName}`
      const values = yDataItem.values;
      if (values?.length < 1) {
        return
      }
      ySeriesResult.push({
        data: values,
        name: key,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        showSymbol: false,
        label: {
          show: props.showLabel
        },
        areaStyle: {
          opacity: 0.8,
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(17, 126, 255, 0.16)',
            },
            {
              offset: 1,
              color: 'rgba(17, 128, 255, 0)',
            },
          ]),
        },
      })
    })
  })

  // 累加
  ySeries.value = JSON.parse(JSON.stringify(ySeriesResult))
  for (let i = 0; i < ySeriesResult.length; i++) {
    legendData.value.push(ySeriesResult[i].name)
    for (let index = 0; index < ySeriesResult[i].data.length; index++) {
      if (index === 0) {
        ySeries.value[i].data[index] = ySeriesResult[i].data[index];
      } else if (index === 1) {
        ySeries.value[i].data[index] = ySeriesResult[i].data[index] + ySeriesResult[i].data[index - 1];
      } else {
        ySeries.value[i].data[index] = ySeriesResult[i].data[index] + ySeries.value[i].data[index - 1];
      }
    }
  }
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData, oldData) => {
  renderChart(newData);
})


onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})
</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
