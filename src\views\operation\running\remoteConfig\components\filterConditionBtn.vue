<template>
    <a-trigger v-model:popup-visible="triggerVisible" trigger="click" :unmount-on-close="false" position="bl" :popup-offset="-40" @hide="triggerHide">
        <a-button type="outline" shape="round" style="height: 24px;" @click="() => triggerVisible = true">
            <template #icon>
                <icon-filter />
            </template>
            <template #default>过滤条件</template>
        </a-button>
      <template #content>
        <div class="filter-basic">
            <div class="filter-flex">
                <div class="filter-left">
                    <div v-for="(item,index) in filterConditionList" :key="index" class="item-select" @click="itemSelect(item)">
                        <span>{{ item.name }}</span>
                        <icon-caret-right class="right-icon"/>
                    </div>
                </div>
                <div v-if="showDetailFilter" class="filter-right">
                    <div style="width: 100%;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                        <a-input v-model="searchInput" :placeholder="`搜索${searchName}`" style="border: none;height: 40px;">
                            <template #prefix>
                                <icon-search />
                            </template>
                        </a-input>
                    </div>
                    <div v-if="childrenList?.length" class="check-content">
                        <a-checkbox-group v-model:model-value="checkGroupList" direction="vertical">
                            <a-list ref="virtualList" :virtual-list-props="{ height: 240 }" style="width: 280px;" :data="filteredLists">
                                <template #item="{ item, index }">
                                    <a-list-item :key="index">
                                        <a-checkbox :value="item.code">{{ item.name }}</a-checkbox>
                                    </a-list-item>
                                </template>
                            </a-list>
                        </a-checkbox-group>
                    </div>
                    <div v-else class="check-content">
                        <a-empty />
                    </div>
                </div>
            </div>
            <div v-if="showDetailFilter" class="filter-footer">
                <a-button style="margin-right: 10px;" class="cancel" @click="() => triggerVisible = false">取消</a-button>
                <a-button type="primary" @click="applySelect">
                    应用
                </a-button>
            </div>
        </div>
      </template>
    </a-trigger>
    
</template>

<script setup lang="ts">
import {computed, ref} from "vue";
import {getConditionList, getOpAppList, getOpOsList} from "@/api/marketing/api";
import {cloneDeep} from "lodash";

const triggerVisible = ref(false)
const filterConditionList = ref([
    {
        name:'平台',
        code:'platform'
    },
    {
        name:'应用',
        code:'app'
    },
    {
        name:'条件',
        code:'condition'
    },
    {
        name:'群组',
        code:'group'
    }
])

const searchInput = ref('')
const childrenList = ref<any>([])
const showDetailFilter = ref(false)
const checkGroupList = ref([])
const searchName = ref('')
const triggerHide = () => {
    childrenList.value = []
    showDetailFilter.value = false
}
// 应用
const appList = ref()
const getApp = () => {
    getOpAppList().then(res => {
        appList.value=res
    })
}
// 应用平台
const appPlatform = ref()
const getPlatformList = () => {
  getOpOsList().then(res => {
    appPlatform.value=res
  })
}
// 条件
const conditionList = ref([])
const getConditions = async () => {
  const data = {
    appId:sessionStorage.getItem('app-id'),
    conditionScope:'app_specific'
  }
  await getConditionList(data).then(res => {
    conditionList.value = res.items || []
  })
}
// 群组
const groupList = ref([
    {
        name:'getway - Variant C',
        code:'d1'
    }
])
const init = async () => {
    await Promise.all([
        getApp(),
        getPlatformList(),
        getConditions()
    ])
}
init()
const itemSelect = (item) => {
    childrenList.value = []
    checkGroupList.value = []
    searchName.value = item.name
    if(item.code === 'platform'){
        childrenList.value = cloneDeep(appPlatform.value)
    }else if(item.code === 'app'){
        childrenList.value = cloneDeep(appList.value)
    }else if(item.code === 'condition'){
        childrenList.value = cloneDeep(conditionList.value)
    }else{
        childrenList.value = cloneDeep(groupList.value)
    }
    showDetailFilter.value = true
}
// 应用
const applySelect = () => {
    console.log(checkGroupList.value,'vvvv-check');
    triggerVisible.value = false
}
// 搜索时数据
const filteredLists = computed(() => {
    const str = searchInput.value.trim();
    const list = childrenList.value.filter(item => item?.name?.includes(str) || item?.code?.includes(str));
  
    return list
  });
</script>

<style scoped lang="less">
:deep(.arco-list-item){
    padding: 0!important;
    border: none!important;
}
:deep(.arco-list-bordered) {
    border: none;
}
.filter-basic{
    overflow: hidden;
    padding: 4px 0;
    background-color: var(--color-bg-popup);
    border: 1px solid var(--color-fill-3);
    border-radius: var(--border-radius-medium);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    .filter-flex{
        display: flex;
    }
    .filter-left{
        max-height: 260px;
        min-width: 200px;
        overflow-y: auto;
        .item-select{
            padding: 0 16px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            .right-icon{
                opacity: 0;
            }
            &:hover{
                background-color: var(--color-fill-2);
                .right-icon{
                    opacity: 1;
                }
            }
        }
    }
    .filter-right{
        border-left: 1px solid #f7f6f6;
        max-width: 520px;
        min-width: 180px;
        .check-content{
            padding: 8px;
            overflow:hidden;
            max-height: 240px;
            :deep(.arco-checkbox){
                line-height: 40px;
            }
        }
    }
    .filter-footer{
        display: flex;
        justify-content: flex-end;
        padding: 8px;
        border-top: 1px solid #f7f6f6;
        button{
            border-radius: 8px;
        }
        .cancel {
            background: transparent;
            margin-right: 8px;
            &:hover {
                background-color: var(--color-secondary);
            }
        }
    }
}
</style>