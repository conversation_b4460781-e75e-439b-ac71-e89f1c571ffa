<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%;">
            <a-form-item field="profitTarget" label="利润目标">
                <a-input-number v-model="form.profitTarget" :min="0"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {inject, reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {saveProfitTarget} from "@/api/analyse/api";

const props = defineProps({
    label:{
      type: String,
      default:''
    }
})
const year = ref(inject('year'));
const modalVisible = ref(false)
const modalTitle = ref('编辑利润目标')
const form = reactive({
    profitTarget:undefined,
    code:''
})
const loading = ref(false)
const rules = {
    profitTarget: [
        {
            required: true,
            message:'请填写利润目标',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);
const openModal = async (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    if(obj){
        form.profitTarget = obj.profitTarget
        form.code = obj?.code || obj?.appId
    }
    modalVisible.value = true
}

const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            loading.value = true
            try {
                const data = {
                    label:props.label,
                    code:form.code,
                    year:year.value,
                    target:form.profitTarget
                }
                await saveProfitTarget(data)
                modalVisible.value = false;
                Message.success('修改成功');
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            } finally {
                loading.value = false
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>