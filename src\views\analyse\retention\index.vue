<template>
  <div id="retentionRoot">
    <div class="analyse-content">
      <analyse-header
          :model="ReportAnalyseModel.RETENTION"
          :query-param="queryParam"
          :report-data="reportData"
          :header-info="headerInfo"
          @call-computed-data="computedData"
          @update-report-form="updateReportForm"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="dataLoading" class="box">
                <div v-if="!dataLoading" class="query-condition">
                  <analysisIndex
                    :analysis-index-data="analysisIndexData"
                    @analysis-index-change="analysisIndexChange"
                    @analysis-subject-change="analysisSubjectChange"
                    @reset-params="reset"/>
                  <!-- 全局筛选 -->
                  <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem :group-item="groupData" @aggregates-change="aggregatesChange"/>
                  <div style="width: 100%;padding: 0 24px;">
                    <a-divider :margin="5" type="dashed"/>
                  </div>
                  <!-- 用户筛选、 -->
                  <userFilter :user-filter="userFilterData" @filters-change="userFiltersChange"/>
                </div>
                <div class="left-footer">
                  <a-button @click="toSave">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="pickDate"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        分析周期：
                        <dateDropdown :time-span-data="timeSpanData" @time-span-change="timeSpanChange"/>
                      </div>
                    </a-space>
                    <div style="display: flex;">
                      <a-select v-model:model-value="queryParam.retentionType" :style="{width:'82px',marginRight:'8px'}" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                @change="retentionChange">
                        <a-option value="retention">留存</a-option>
                        <a-option value="churn">流失</a-option>
                      </a-select>
                      <a-button-group style="background-color: #fff;margin-right: 16px;">
                        <a-popover title="表格" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('table') || !eventData.retentionQueryResult?.length" @click="selectChart('table')">
                            <template #icon><img class="option-icon" src="/icon/table-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            <div>
                              <p>展示各日期下的留存分布情况</p>
                              <img :src="table" alt="" style="width: 90%;height: 90%;"/>
                            </div>
                          </template>
                        </a-popover>
                        <!-- <a-popover title="第N日趋势图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('nTrend') || !eventData.retentionQueryResult?.length" @click="selectChart('nTrend')">
                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示第N日的留存/流失指标的变化趋势<br>
                            <img :src="nTrend" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover> -->
                        <a-popover title="每日趋势图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('dTrend') || !eventData.retentionQueryResult?.length" @click="selectChart('dTrend')">
                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            对比在不同日期完成初始事件的用户，在后续第N日的留存/流失指标<br>
                            <img :src="dTrend" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover>
                      </a-button-group>
                      <a-tooltip content="可视化配置">
                        <a-button :disabled="!eventData.retentionQueryResult?.length" @click="onCollapse">
                          <template #icon>
                            <icon-settings/>
                          </template>
                        </a-button>
                      </a-tooltip>
                      <a-button style="margin-left: 12px;" @click="importTable()">导出</a-button>
                    </div>
                  </div>
                  <div v-if="!loading && eventData.retentionQueryResult?.length" class="right-content">
                    <a-alert v-if="eventData?.resultsExceedsLimit" style="margin-bottom: 5px;">
                      因数据条数过多，优先展示前1000条数据。建议修改分组项或添加更严格的过滤项以减少数据条数，或点击右上角下载全量数据。
                    </a-alert>
                    <event-table-vue v-show="isChartType == 'table'" ref="eventtable" :retention-type="queryParam.retentionType" :event-data="eventData"/>
                    <event-echars-vue
                        v-show="isChartType !== 'table'" ref="eventechars"
                        :chart-value-format="chartValueFormat"
                        :retention-type="queryParam.retentionType"
                        :event-data="eventData"
                        :showlabel="showlabel"
                        :y-max="yMax"
                        :y-min="yMin"/>
                  </div>
                  <div v-if="!loading && !eventData.retentionQueryResult?.length" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div class="drawer" :style="showDrawer ? {width: '320px'} : {width: '0px'}">
                <div class="drawer-header">
                  <div class="title">可视化配置</div>
                  <div class="icon">
                    <a-button @click="() => showDrawer = false">
                      <template #icon>
                        <icon-double-right/>
                      </template>
                    </a-button>
                  </div>
                </div>
                <a-collapse v-model:active-key="activeKey" expand-icon-position="right" class="customStyle" :bordered="false" @change="handleChangeCollapse">
                  <a-collapse-item key="1">
                    <template #header>
                      <icon-settings/>
                      通用配置
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <!-- <div class="title">
                          <span>只看同时展示数据</span>
                        </div> -->
                        <div class="title">
                          <span>看留存/流失的</span>
                          <a-tooltip content="对第N日趋势图、每日趋势图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                        <!-- <div class="title">
                          <span>只看关键日期</span>
                        </div> -->
                        <!-- <div class="title">
                          <span>隐藏不完整数据</span>
                        </div> -->
                        <div class="title">
                          <span>展示粒度</span>
                        </div>
                        <div class="title">
                          <span>阶段汇总</span>
                        </div>
                        <div class="title">
                          <span>显示数值</span>
                          <a-tooltip content="对第N日趋势图、每日趋势图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="label">
                        <!-- <div class="title">
                          <a-switch v-model="onlyMeanwhileData" size="small"/>
                        </div> -->
                        <div class="title">
                          <a-radio-group v-model:model-value="chartValueFormat" type="button" :disabled="isChartType == 'table'" @change="chartValueFormatChange">
                            <a-radio value="rate">比例</a-radio>
                            <a-radio value="num">人数</a-radio>
                          </a-radio-group>
                        </div>
                        <!-- <div class="title">
                          <a-switch v-model="onlyKeyDate" size="small"/>
                        </div> -->
                        <!-- <div class="title">
                          <a-switch v-model="hideData" size="small"/>
                        </div> -->
                        <div class="title">
                          <a-select v-model:model-value="displaySize" :style="{width:'82px'}" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="dateChange">
                            <a-option style="width: 120px;" value="d">按日</a-option>
                            <a-option value="w">
                              <span style="margin-right: 50px;">当周</span>
                              <a-trigger v-model:popup-visible="weekVisible" trigger="click" position="right" :update-at-scroll="true" @click="(event) => event.stopPropagation()">
                                <a-tooltip content="自定义周" position="right">
                                  <icon-calendar-clock class="week-icon"/>
                                </a-tooltip>
                                <template #content>
                                  <div class="week-set-body">
                                    <div class="week-set-title">自定义周</div>
                                    <div class="week-set-desc">
                                      按周统计时，将
                                      <span>{{ weekName }}</span>
                                      作为周起始日
                                    </div>
                                    <div class="week-set-weeks">
                                      <div v-for="(item,index) in weekDayList" :key="index" class="week-set-week" :class="{'week-active': weekName == item}" @click="weekChange(item)">{{ item }}</div>
                                    </div>
                                    <div class="week-set-btns">
                                      <a-button @click="() => weekVisible = false">取消</a-button>
                                      <a-button type="primary">确定</a-button>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </a-option>
                            <a-option value="m">当月</a-option>
                          </a-select>
                        </div>
                        <div class="title">
                          <a-button class="total" @click="showTotal">
                            <template #icon>
                              <icon-settings/>
                            </template>
                            配置
                          </a-button>
                        </div>
                        <div class="title">
                          <a-switch v-model="showlabel" size="small"/>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                  <!-- <a-collapse-item key="2">
                    <template #header>
                      <icon-nav/>
                      表格
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>展示方式</span>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          以
                          <div style="display: inline-block;">
                            <a-tooltip content="添加分组项后可用" position="top">
                              <a-select v-model:model-value="tableShowWays" default-value="date" :disabled="eventData.groupsDesc.length === 0" :style="{width:'72px',margin:'0 8px'}"
                                        :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                                <a-option value="date">日期</a-option>
                                <a-option value="group">分组</a-option>
                              </a-select>
                            </a-tooltip>
                          </div>
                          为行
                        </div>
                      </div>
                    </div>
                  </a-collapse-item> -->
                </a-collapse>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
    <!-- 阶段汇总配置 -->
    <a-modal :visible="visible" width="600px" title="阶段汇总配置" title-align="start" unmount-on-close @cancel="visible = false">
      <div class="config-box">
        <span style="font-size: 14px;color: #7e7f80;">可以为留存、同时展示数据的阶段汇总指标选择合适的计算方法，如选择留存人数的阶段总和等</span><br>
        <a-table :columns="configColumns" :pagination="false" size="small" :bordered="false" :data="configData" style="margin: 16px 0;">
          <template #name="{ rowIndex }">
            <i class="i">{{ rowIndex + 1 }}</i>
            <span style="font-weight: 500">{{ configData[rowIndex].name }}&nbsp;</span>
          </template>
          <template #type="{ rowIndex }">
            <a-select v-model="configData[rowIndex].type" :style="{width:'120px',borderRadius: '4px'}">
              <a-tooltip content="适用于总次数或总和类指标，基于不同时段的结果数据进行简单加和，不去重" position="right">
                <a-option value="sum">阶段总和</a-option>
              </a-tooltip>
              <a-tooltip content="适用于总和类的均值计算，基于不同时段的结果数据进行简单平均" position="right">
                <a-option value="avg">阶段均值</a-option>
              </a-tooltip>
            </a-select>
          </template>
        </a-table>
      </div>
      <template #footer>
        <div style="display: flex;justify-content: flex-end;">
          <div>
            <button style="margin-right: 10px;" class="btnQ" @click="visible = false">取消</button>
            <button style="" class="btnB" @click="checkValue">保存</button>
          </div>
        </div>
      </template>
    </a-modal>
    <!-- 保存弹窗 -->
    <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">保存报表</div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <a-form-item field="dashboard" label="保存至看板">
          <dashboard-select v-model:selected="form.dashboard" type="dashboard" @change="dashboardChange"/>
        </a-form-item>
        <a-form-item field="description" label="备注">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click.stop="handleSaveCancel">取消</a-button>
        <a-button :loading="saveLoading" type="primary" @click="saveReport">保存</a-button>
      </template>
    </a-modal>
  </div>

</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {h, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {dTrend, table} from "@/views/dashboard/components/img";
import {ChartType, IndicatorType} from "@/api/enum";
import {cloneDeep, debounce, isEmpty} from "lodash";
import {analyseStore, toolStore} from '@/store';
import {queryRetentionReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {ReportAnalyseModel} from "@/api/analyse/type";
import dateDropdown from "@/views/analyse/components/dateDropdown.vue";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import {LocalStorageEventBus} from "@/types/event-bus";
import {detailReport} from "@/api/report/api";
import {useRoute} from 'vue-router';
import router from "@/router";
import userFilter from "@/views/analyse/components/UserFilter.vue"
import {ROUTE_NAME} from "@/router/constants";
import {getDefaultObj} from "@/views/analyse/components/util/verify";
import globalFilter from "../components/globalFilter.vue"
import groupItem from "../components/groupItem.vue"
import analyseHeader from "../components/analyseHeader.vue"
import analysisIndex from "./components/analysisIndex.vue"
import eventTableVue from "./components/TableViewFlat.vue";
import eventEcharsVue from "./components/EcharsView.vue";

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const paramsDataStorage = useSessionStorage("retention-params-data", {});
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
const route = useRoute();
const reportId = ref(route.query.code);
const analyseData = analyseStore();
const toolData = toolStore()
const headerInfo = reactive({
  title: '留存分析',
  img: '/icon/topMenu/retention.svg',
  tips: '以某段时间做过初始事件的用户为样本，查看在指定日期后用户进行回访事件的留存情况',
  root: '#retentionRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true,
  showAppSelect: true
})
const eventData = ref({
  y: [],
  groupsDesc: [],
  retentionQueryResult: []
})
const dataName = ref('')
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// 查询暂无requestId返回
const requestId = ref()
const timeOut = ref()
// ------left start
const loading = ref(false)
const totalNum = ref(0)
const timeSpanData = ref({
  firstDayDfWeek: 1,
  number: 7,
  unit: "DAY"
})
const analysisIndexData = ref<any>({})
const globalFilterData = ref({}) // 全局筛选传参
const groupData = ref<any>([]) // 分组项传参
const userFilterData = ref({}) // 用户筛选传参
const isReset = ref(false)
// 传参数组
const queryParam = reactive({
  analyzeSubjectCode: '',
  analyzeSubjectName: '',
  indicator: {}, // 查询指标
  filter: {}, // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  }, // 查询日期范围
  timeSpan: timeSpanData.value,
  retentionType: 'retention', // 留存类型
  userFilter: {},// 用户筛选条件
})
const dataLoading = ref(false)
const eventBus = useEventBus('eventList');
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
// const eventParamsList = ref()
const handleAttrBus = debounce(async () => {
  const indexList = queryParam.indicator?.indicatorList?.flatMap(item => item.eventList) || []
  // eventParamsList.value = indexList
  eventBus.emit(indexList);
  if (indexList.length > 0) {
    form.value.name = `先${indexList[0]?.eventDisplayName}，后${indexList[1]?.eventDisplayName}的${queryParam.analyzeSubjectName}留存分析`
  }
}, 300)
// provide('eventParamsList', eventParamsList);
const analysisIndexChange = (v) => {
  queryParam.indicator = v
  handleAttrBus()
  paramsDataStorage.value = queryParam
}
const analysisSubjectChange = (v) => {
  const {subject, subjectName} = v
  queryParam.analyzeSubjectCode = subject
  queryParam.analyzeSubjectName = subjectName
  paramsDataStorage.value = queryParam
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
  paramsDataStorage.value = queryParam
}
// 用户筛选传参
const userFiltersChange = (v) => {
  queryParam.userFilter = v
  paramsDataStorage.value = queryParam
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
  paramsDataStorage.value = queryParam
}
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取三个需要验证的列表
  const firstList = params.indicator?.indicatorList
      .filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.indicator?.indicatorList
      .flatMap(item =>
          item.eventList
              .filter(event => event.filter?.filters?.length > 0)
              .map(event => event.filter.filters)
      );
  // 添加对 userFilter 的校验
  const userFilterList = params?.userFilter?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.userFilter?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userFilter?.userCondition?.filters || [];
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList) &&
      verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};
// 计算
const computedData = async () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.warning('事件查询超时，请重试！')
    }
  }, 60*1000)
  // requestId.value = queryRetentionReportData(queryParam);
  // setTimeout(() => {
  //   requestId.value = queryRetentionReportData(queryParam);
  // }, 2000);
  if (paramsVerify(queryParam)) {
    requestId.value = queryRetentionReportData(queryParam);
  } else {
    Message.error('筛选条件参数错误')
    loading.value = false
  }
}
watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (newData.result === null) {
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }
  timeOut.value && clearTimeout(timeOut.value)
  eventData.value = newData.result
  paramsDataStorage.value = queryParam
  dataName.value = `${queryParam.indicator?.indicatorList[0]?.eventList[0]?.eventDisplayName}${queryParam.analyzeSubjectName}数`
  totalNum.value = newData.result.totalNum
  loading.value = false
})
const saveVisible = ref(false)

const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
onMounted(() => {
  // eventBus.on((event: any) => {
  //   form.value.name = `${event[0]?.displayName}等${event.length}个`
  // });
})
const handleSaveCancel = () => {
  saveVisible.value = false;
}
const saveLoading = ref(false)
const editReportData = reactive({
  name: '',
  description: ''
})
const dashboardData = ref()
const dashboardChange = (v) => {
  dashboardData.value = v
}
// 保存报表
const saveReport = debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    return;
  }
  saveLoading.value = true;
  try {
    const saveParams = {
      model: ReportAnalyseModel.RETENTION,
      reportId: reportId.value || undefined,
      dashboard: form.value.dashboard,
      name: editReportData?.name || form.value.name,
      description: editReportData?.description || form.value.description,
      queryParam,
      chartParams: [],
      chartType: ChartType.TABLE
    };
    await saveAnalyseReportList(
        saveParams.model,
        saveParams.reportId,
        saveParams.dashboard,
        saveParams.name,
        saveParams.description,
        saveParams.queryParam,
        saveParams.chartParams,
        saveParams.chartType
    );
    dashboardSelected.value = { ...dashboardData.value }
    const successMsg = isEmpty(form.value.dashboard)
        ? h('span', [
          '报表已保存'
        ])
        : h('span', [
          '报表已保存并添加至看板，',
          h('a', {
            style: {color: 'rgb(var(--primary-6))', cursor: 'pointer'},
            onClick: () => router.push({
              name: ROUTE_NAME.DASHBOARD,
            }),
          }, '去看板查看')
        ]);
    Message.success({
      content: () => successMsg,
      duration: 3000
    });
    eventBus.emit('saveReport');
    if (!reportId.value) {
      saveVisible.value = false;
    }
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300)
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  saveReport()
}
const toSave = () => {
  if (reportId.value) {
    saveReport()
  } else {
    saveVisible.value = true
  }
}
// ---left end

// ------right start

const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
provide('boardDate', boardDate);

const pickDate = (date: any) => {
  boardDate.value = date
  queryParam.dateRange = date;
  computedData()
};

const timeSpanChange = (v) => {
  queryParam.timeSpan = v
  computedData()
}
const showDrawer = ref<boolean>(false)
const eventechars = ref()
const isChartType = ref<string>('table')
// 只看同时展示数据
const onlyMeanwhileData = ref<boolean>(false)
// 比列人数
const chartValueFormat = ref('rate')
// 只看关键日期
const onlyKeyDate = ref<boolean>(false)
// 隐藏不完整数据
const hideData = ref<boolean>(false)
// 展示粒度
const displaySize = ref('d')
// 显示数值
const showlabel = ref<boolean>(false)
const yMax = ref<number | null>(null)
const yMin = ref<number | null>(null)
// 展示方式
const tableShowWays = ref('date')

const weekDayList = ref(['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期天'])
const weekName = ref('星期一')
const weekVisible = ref(false)
const weekChange = (item) => {
  weekName.value = item
}
const chartValueFormatChange = (v) => {
  chartValueFormat.value = v
}
const activeKey = ref(['1', '2'])
const handleChangeCollapse = (key: any) => {
  if (key.includes('2')) {
    isChartType.value = 'table'
  }
}

const onCollapse = () => {
  showDrawer.value = !showDrawer.value;
};

const selectChart = (type: string) => {
  isChartType.value = type
  nextTick(() => {
    if (type !== 'table' && eventechars.value) {
      activeKey.value = ['1']
      eventechars.value.freshData(isChartType.value)
    }
  });
}


// ---right end
const eventtable = ref()
const retentionChange = (v) => {
  queryParam.retentionType = v
  eventtable.value.freshData(v)
}
const importTable = (name?: string) => {
  eventtable.value.exportXlsx(queryParam.dateRange, name)
}
// 阶段汇总配置
const visible = ref(false)
const configData = ref<any>([])
const configColumns = ref<any>([
  {
    title: '分析指标',
    dataIndex: 'name',
    slotName: 'name',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '汇总方式',
    dataIndex: 'type',
    slotName: 'type'
  }
])
const showTotal = () => {
  // eventtable.value.showTotal()
  const itemName = queryParam.retentionType === 'retention' ? '留存人数' : '流失人数'
  const configNameList = [dataName.value, itemName]
  configData.value = configNameList.map(item => {
    return {
      name: item,
      type: 'sum'
    }
  })
  visible.value = true
}

const checkValue = () => {
  // visible.value = false
}
const evtLists = ref<any>([])
const reportData = ref()
const init = async () => {
  if (dataLoading.value){
    // 正在初始化中
    return
  }
  dataLoading.value = true
  checkedSessionGroups.value = []
  let parsedData = {} as any
  if (reportId.value) {
    await detailReport(reportId.value as string).then(res => {
      parsedData = res.queryParam
      parsedData.dateRange = boardDate.value
      reportData.value = res
      editReportData.name = res?.name
      editReportData.description = res?.description
    })
  }
  // 如果没有从报表获取到数据，则尝试从会话存储获取
  if (Object.keys(parsedData).length === 0) {
    parsedData = paramsDataStorage.value
  }
  if(parsedData.analyzeSubjectCode){
    queryParam.analyzeSubjectCode = parsedData.analyzeSubjectCode
  }
  if(parsedData.analyzeSubjectName){
    queryParam.analyzeSubjectName = parsedData.analyzeSubjectName
  }
  // 分析
  if (parsedData.indicator) {
    queryParam.indicator = parsedData.indicator
    analysisIndexData.value = parsedData.indicator
    const indexList = analysisIndexData.value?.indicatorList?.flatMap(item => item.eventList) || []
    if (indexList.length > 0) {
      form.value.name = `先${indexList[0]?.eventDisplayName}，后${indexList[1]?.eventDisplayName}的${queryParam.analyzeSubjectName}留存分析`;
    }
  }
  // 留存类型
  // queryParam.retentionType = parsedData.retentionType ? parsedData.retentionType : queryParam.retentionType
  // 处理事件筛选
  if (parsedData.filter) {
    globalFilterData.value = parsedData.filter
    queryParam.filter = parsedData.filter
  }
  // 处理分组项
  if (parsedData.aggregates && parsedData.aggregates.length > 0) {
    groupData.value = cloneDeep(parsedData.aggregates)
    queryParam.aggregates = cloneDeep(parsedData.aggregates)
  }
  // 处理用户筛选条件
  if (parsedData.userFilter) {
    userFilterData.value = parsedData.userFilter
    queryParam.userFilter = parsedData.userFilter
  }
  // 处理时间
  if (parsedData.dateRange) {
    boardDate.value = parsedData.dateRange
    queryParam.dateRange = parsedData.dateRange
  }else{
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    boardDate.value = date
    queryParam.dateRange = date
  }
  // 处理timeSpan
  if (parsedData.timeSpan) {
    timeSpanData.value = parsedData.timeSpan
    queryParam.timeSpan = parsedData.timeSpan
  }else{
    const time = {
      firstDayDfWeek: 1,
      number: 7,
      unit: "DAY"
    }
    timeSpanData.value = time
    queryParam.timeSpan = time
  }
  toolData.updateTemporaryList([])
  // evtLists.value = analyseData.$state.evtLists.length > 0 ? analyseData.$state.evtLists : await analyseData.fetchEvtInfo();
  evtLists.value = (await toolData.fetchEventModalList()).flatMap(category => category.items || []);
  // 切换应用时重新请求全部属性
  if(toolData.totalEvtIndCodes.length){
    await toolData.fetchAllAttrList()
  }
  const defaultData = getDefaultObj(evtLists.value)
  if (!analysisIndexData.value?.indicatorList?.length) {
    const eventObj = {
      name: defaultData.name,
      type: IndicatorType.RETENTION,
      displayType: {},
      displayName: '',
      isBasic: true,
      eventList: [
        {
          eventName: defaultData.name,
          eventDisplayName: defaultData.displayName,
          eventCode: defaultData.code,
          eventType: defaultData.type,
          type: defaultData.objectType,
          eventAttrCode: '',
          eventAttrName: '',
          filter: {}
        },
      ],
      filter: {}
    };
    // 将对象转换为数组并复制两次
    const list = [eventObj, eventObj]
    // analysisIndexData.value.push(eventObj, eventObj);
    queryParam.indicator = {
      code: '',
      name: '',
      displayName: '',
      type: IndicatorType.RETENTION,
      displayType: {type: 'default', decimalNum: 2, thousandSep: 1},
      indicatorList: list,
      retentionAssociateAttribute: [],
      // retentionIndicatorType:'retention_rate'
    }
    analysisIndexData.value = queryParam.indicator
  }
  dataLoading.value = false
  setTimeout(() => {
    computedData()
  }, 1000)
}
init()

localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    init()
  }
})
const reset = () => {
  isReset.value = true
  paramsDataStorage.value = {}
  analysisIndexData.value = []
  globalFilterData.value = {}
  queryParam.filter = {}
  groupData.value = []
  userFilterData.value = {}
  queryParam.userFilter = {}
  init()
}
</script>

<style scoped lang="less">
#retentionRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 0;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  text-align: right;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  min-height: 0;
  flex: 1;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding-top: 24px;
  //padding-bottom: 18px;
  // border-bottom: 1px solid var(--tant-border-color-border1-1);
}


.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  // height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}

.week-set-body {
  width: 352px;
  padding: 16px 16px 8px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-medium-shadow-medium-bottom);

  .week-set-title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
    color: var(--tant-text-gray-color-text1-1);
    font-weight: 500;
    font-size: 16px;
  }

  .week-set-desc {
    display: block;
    margin-bottom: 16px;
    color: var(--tant-text-gray-color-text1-3);
    font-size: 14px;

    span {
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      padding: 0 4px;
    }
  }

  .week-set-weeks {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    margin-right: -16px;
    padding-bottom: 16px;

    .week-set-week {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 74px;
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 5px 16px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      white-space: nowrap;
      border: 1px solid var(--tant-border-color-border1-1);
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-active);
        border-color: var(--tant-secondary-color-secondary-fill-active);
      }
    }

    .week-active {
      background-color: var(--tant-secondary-color-secondary-fill-active);
      border-color: var(--tant-secondary-color-secondary-fill-active);
    }

  }

  .week-set-btns {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-right: -16px;
    margin-left: -16px;
    padding-top: 8px;
    padding-right: 16px;
    border-top: 1px solid var(--tant-border-color-border1-1);

    button {
      border-radius: var(--tant-border-radius-medium);
      margin-left: 8px;
    }
  }
}

.i {
  display: inline-block;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: var(--tant-bg-white-color-bg1-1);
  font-size: 12px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  background-color: var(--tant-secondary-color-secondary-default);
  border-radius: 4px;
  transition: all .3s;
  margin-right: 12px;
}

.btnQ {
  color: #333;
  background-color: transparent;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.btnQ:hover {
  background-color: #f1f2f5;
}

.btnB {
  cursor: pointer;
  color: #fff;
  background-color: #1e76f0;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
}

.btnB:hover {
  background-color: #3583ef;
  background: rgb(192, 215, 247);
}
</style>