<template>
  <a-config-provider :locale="locale">
    <router-view/>
<!--    <div-->
<!--        v-if="showWenshu"-->
<!--        id="wenshu"-->
<!--        ref="floatBall"-->
<!--        class="float-ball"-->
<!--        :class="{ 'hide-left': isHideLeft, 'hide-right': isHideRight }"-->
<!--        @mousedown="startDrag">-->
<!--      <a-popover trigger="click" position="lb">-->
<!--        <a-tooltip content="问数一下">-->
<!--          <span role="img">-->
<!--            <img src="/icon/question.svg" style="width: 35px;" alt="" draggable="false"/>-->
<!--          </span>-->
<!--        </a-tooltip>-->
<!--        <template #content>-->
<!--          <iframe-->
<!--              src="https://dify.ivymobile.com/chatbot/eo9UQ7PVF5IdyK02"-->
<!--              style="width: 1100px;border:none;height: 700px"-->
<!--              frameborder="0"-->
<!--              allow="microphone">-->
<!--          </iframe>-->
<!--        </template>-->
<!--      </a-popover>-->
<!--    </div>-->

    <!-- 反馈悬浮球 -->
    <div
        id="feedback"
        ref="feedbackFloatBall"
        class="float-ball feedback-ball"
        :class="{ 'feedback-hide-left': isFeedbackHideLeft, 'feedback-hide-right': isFeedbackHideRight }"
        @mousedown="startFeedbackDrag">
      <a-popover trigger="click" position="lb">
        <a-tooltip content="问题反馈">
          <span role="img">
            <img src="/icon/question.svg" style="width: 35px;" alt="" draggable="false"/>
          </span>
        </a-tooltip>
        <template #content>
          <FeedbackPanel />
        </template>
      </a-popover>
    </div>
  </a-config-provider>
</template>

<script lang="ts" setup>
import {computed, onMounted, onUnmounted, ref} from 'vue';
import enUS from '@arco-design/web-vue/es/locale/lang/en-us';
import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
import useLocale from '@/hooks/locale';
import {useEventBus, useStorage} from "@vueuse/core";
import {LogEventBus} from "@/types/event-bus";
import {getLatestReleaseVersion} from "@/api/system/api";
import useIndicatorPolling from '@/hooks/indicator-polling';
import FeedbackPanel from '@/components/feedback-panel/index.vue';

// 修复类型错误
const tokenStorage = useStorage<string>('token', '');
const showWenshu = ref<boolean>(tokenStorage.value?.length > 0);
const bus = useEventBus(LogEventBus);

// 初始化指标数据请求
const {
  fetchIndicatorData,
  resetLoadingState,
  clearIndicatorCache
} = useIndicatorPolling();

bus.on(() => {
  const tokenValue = useStorage<string>('token', '').value;
  const isLoggedIn = tokenValue?.length > 0;
  showWenshu.value = isLoggedIn;
  
  // 根据登录状态控制指标数据请求
  if (isLoggedIn) {
    fetchIndicatorData();
  } else {
    resetLoadingState();
    clearIndicatorCache();
  }
})
const {currentLocale} = useLocale();
const locale = computed(() => {
  switch (currentLocale.value) {
    case 'zh-CN':
      return zhCN;
    case 'en-US':
      return enUS;
    default:
      return enUS;
  }
});

const floatBall = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const isHideLeft = ref(false);
const isHideRight = ref(false);
const offsetX = ref(0);
const offsetY = ref(0);
const startX = ref(0); // 记录鼠标按下时的初始X坐标
const startY = ref(0); // 记录鼠标按下时的初始Y坐标
const threshold = 5; // 拖拽的最小距离阈值

const startDrag = (e: MouseEvent) => {
  if (!floatBall.value) return;
  isDragging.value = true;
  offsetX.value = e.clientX - floatBall.value.offsetLeft;
  offsetY.value = e.clientY - floatBall.value.offsetTop;
  startX.value = e.clientX; // 记录鼠标按下时的初始位置
  startY.value = e.clientY;
  floatBall.value.style.transition = 'none'; // 拖拽时取消过渡效果
};

const stopDrag = (e: MouseEvent) => {
  if (isDragging.value && floatBall.value) {
    isDragging.value = false;
    floatBall.value.style.transition = 'left 0.3s, right 0.3s';
    // 获取悬浮球位置
    const ballRect = floatBall.value?.getBoundingClientRect();

    // 如果靠近左侧
    if (ballRect?.left && ballRect.left <= 10) {
      isHideLeft.value = true;
      isHideRight.value = false;

    }
    // 如果靠近右侧
    else if (ballRect?.right && ballRect.right >= (window.innerWidth - 20)) {
      isHideRight.value = true;
      isHideLeft.value = false;

    }
    // 其他情况保持中间位置
    else {
      isHideLeft.value = false;
      isHideRight.value = false;
    }
  }
};

const onMouseMove = (e: MouseEvent) => {
  if (isDragging.value && floatBall.value) {
    e.preventDefault(); // 阻止默认行为
    let x = e.clientX - offsetX.value;
    let y = e.clientY - offsetY.value;

    // 限制拖拽范围在页面内
    x = Math.max(0, Math.min(window.innerWidth - floatBall.value.offsetWidth, x));
    y = Math.max(0, Math.min(window.innerHeight - floatBall.value.offsetHeight, y));

    // 拖拽时只更新 left 和 top，并且清除 right 属性
    floatBall.value.style.left = x + 'px';
    floatBall.value.style.top = y + 'px';
    floatBall.value.style.right = 'auto'; // 清除 right 防止冲突
  }
};

// 反馈悬浮球相关状态
const feedbackFloatBall = ref<HTMLElement | null>(null);
const isFeedbackDragging = ref(false);
const isFeedbackHideLeft = ref(false);
const isFeedbackHideRight = ref(false);
const feedbackOffsetX = ref(0);
const feedbackOffsetY = ref(0);
const feedbackStartX = ref(0);
const feedbackStartY = ref(0);

// 反馈悬浮球拖拽功能
const startFeedbackDrag = (e: MouseEvent) => {
  if (!feedbackFloatBall.value) return;
  isFeedbackDragging.value = true;
  feedbackOffsetX.value = e.clientX - feedbackFloatBall.value.offsetLeft;
  feedbackOffsetY.value = e.clientY - feedbackFloatBall.value.offsetTop;
  feedbackStartX.value = e.clientX;
  feedbackStartY.value = e.clientY;
  feedbackFloatBall.value.style.transition = 'none';
};

const stopFeedbackDrag = (e: MouseEvent) => {
  if (isFeedbackDragging.value && feedbackFloatBall.value) {
    isFeedbackDragging.value = false;
    feedbackFloatBall.value.style.transition = 'left 0.3s, right 0.3s';
    const ballRect = feedbackFloatBall.value?.getBoundingClientRect();

    if (ballRect?.left && ballRect.left <= 10) {
      isFeedbackHideLeft.value = true;
      isFeedbackHideRight.value = false;
    } else if (ballRect?.right && ballRect.right >= (window.innerWidth - 20)) {
      isFeedbackHideRight.value = true;
      isFeedbackHideLeft.value = false;
    } else {
      isFeedbackHideLeft.value = false;
      isFeedbackHideRight.value = false;
    }
  }
};

const onFeedbackMouseMove = (e: MouseEvent) => {
  if (isFeedbackDragging.value && feedbackFloatBall.value) {
    e.preventDefault();
    let x = e.clientX - feedbackOffsetX.value;
    let y = e.clientY - feedbackOffsetY.value;

    x = Math.max(0, Math.min(window.innerWidth - feedbackFloatBall.value.offsetWidth, x));
    y = Math.max(0, Math.min(window.innerHeight - feedbackFloatBall.value.offsetHeight, y));

    feedbackFloatBall.value.style.left = x + 'px';
    feedbackFloatBall.value.style.top = y + 'px';
    feedbackFloatBall.value.style.right = 'auto';
  }
};

onMounted(() => {
  getLatestReleaseVersion().then(res => {
    sessionStorage.setItem('web-version', JSON.stringify(res));
    localStorage.setItem('web-version', JSON.stringify(res));
  })

  // 初始化时检查登录状态，如果已登录则请求指标数据
  const tokenValue = useStorage<string>('token', '').value;
  const initialLoginState = tokenValue?.length > 0;
  if (initialLoginState) {
    fetchIndicatorData();
  }

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', stopDrag);
  document.addEventListener('mousemove', onFeedbackMouseMove);
  document.addEventListener('mouseup', stopFeedbackDrag);

  const showMargin = 20;  // 鼠标靠近多少距离时显示悬浮球
  const hideMargin = 60;  // 鼠标远离多少距离时隐藏悬浮球

  document.addEventListener('mousemove', (e: MouseEvent) => {
    const ballRect = floatBall.value?.getBoundingClientRect();
    const feedbackBallRect = feedbackFloatBall.value?.getBoundingClientRect();

    // 当悬浮球隐藏在左侧，且鼠标靠近左边时，显示悬浮球
    if (isHideLeft.value && e.clientX <= showMargin) {
      isHideLeft.value = false;
      if (floatBall.value) floatBall.value.style.left = '0px'; // 显示悬浮球
    }

    // 当悬浮球隐藏在右侧，且鼠标靠近右边时，显示悬浮球
    else if (isHideRight.value && e.clientX >= window.innerWidth - showMargin) {
      isHideRight.value = false;
      if (floatBall.value) floatBall.value.style.right = '0px'; // 显示悬浮球
    }

    // 当鼠标远离左侧，且悬浮球在左边时，隐藏悬浮球
    else if (!isHideLeft.value && e.clientX > hideMargin && ballRect?.left && ballRect.left <= showMargin) {
      isHideLeft.value = true;
      if (floatBall.value) floatBall.value.style.left = '-40px'; // 隐藏悬浮球到左侧
    }

    // 当鼠标远离右侧，且悬浮球在右边时，隐藏悬浮球
    else if (!isHideRight.value && e.clientX < window.innerWidth - hideMargin && ballRect?.right && ballRect.right >= window.innerWidth - showMargin) {
      isHideRight.value = true;
      if (floatBall.value) floatBall.value.style.right = '-40px'; // 隐藏悬浮球到右侧
    }

    // 反馈悬浮球的边缘吸附逻辑
    if (isFeedbackHideLeft.value && e.clientX <= showMargin) {
      isFeedbackHideLeft.value = false;
      if (feedbackFloatBall.value) feedbackFloatBall.value.style.left = '0px';
    } else if (isFeedbackHideRight.value && e.clientX >= window.innerWidth - showMargin) {
      isFeedbackHideRight.value = false;
      if (feedbackFloatBall.value) feedbackFloatBall.value.style.right = '0px';
    } else if (!isFeedbackHideLeft.value && e.clientX > hideMargin && feedbackBallRect?.left && feedbackBallRect.left <= showMargin) {
      isFeedbackHideLeft.value = true;
      if (feedbackFloatBall.value) feedbackFloatBall.value.style.left = '-40px';
    } else if (!isFeedbackHideRight.value && e.clientX < window.innerWidth - hideMargin && feedbackBallRect?.right && feedbackBallRect.right >= window.innerWidth - showMargin) {
      isFeedbackHideRight.value = true;
      if (feedbackFloatBall.value) feedbackFloatBall.value.style.right = '-40px';
    }
  });
});


onUnmounted(() => {
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', onFeedbackMouseMove);
  document.removeEventListener('mouseup', stopFeedbackDrag);
  
  // 组件卸载时重置加载状态
  resetLoadingState();
});
</script>

<style lang="less" scoped>
#wenshu {
  width: 60px;
  height: 60px;
  //margin-right: 60px;
  margin-bottom: 60px;
  border-radius: 50%;
  z-index: 999;
  position: fixed;
  right: 0;
  bottom: 0;
  cursor: pointer;
  //background-color: var(--tant-bg-white-color-bg1-1);
  background-color: var(--tant-primary-color-primary-default);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--tant-large-shadow-large-overall);
  //transform: translate(-50%, -50%);
  transition: left 0.3s, right 0.3s;
}

#feedback {
  width: 60px;
  height: 60px;
  margin-bottom: 130px; // 与问数悬浮球保持距离
  border-radius: 50%;
  z-index: 999;
  position: fixed;
  right: 0;
  bottom: 0;
  cursor: pointer;
  background-color: var(--tant-primary-color-primary-default);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--tant-large-shadow-large-overall);
  transition: left 0.3s, right 0.3s;
}

.hide-left {
  left: -40px !important; /* 左边隐藏 */
  right: auto !important;
  transition: 0.3s;
}

.hide-right {
  right: -40px !important; /* 右边隐藏 */
  left: auto !important;
  transition: 0.3s;
}

.feedback-hide-left {
  left: -40px !important;
  right: auto !important;
  transition: 0.3s;
}

.feedback-hide-right {
  right: -40px !important;
  left: auto !important;
  transition: 0.3s;
}
</style>