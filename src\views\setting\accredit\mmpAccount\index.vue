<template>
    <div class="page">
      <div class="page-head">
        <div class="title">
          {{ route.meta.locale }}
        </div>
        <div class="filter">
          <div class="filter-item">
            <a-button class="button" type="primary" style="margin-right: 30px" @click="handleAuthorize">
              <icon-plus style="margin-right: 3px"/>
              新增授权
            </a-button>
          </div>
        </div>
      </div>
      <div class="page-body">
        <div class="mmp-content">
          <div class="nav">
            <div class="nav-content">
              <div v-for="(item,index) in navList" :key="index" class="nav-item" :class="navIndex === index ? 'nav-active' : ''" @click="navSelect(index)">
                <img :src="item.imgUrl" alt="" class="nav-img">
                {{ item.name }}
              </div>
            </div>
          </div>
          <div class="content">
            <appsflyerPage v-if="navIndex === 0" ref="appsflyerRef"/>
            <gaPage v-if="navIndex === 1" ref="gaRef"/>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {useRoute} from 'vue-router';
import appsflyerPage from "./appsflyerPage.vue";
import gaPage from "./gaPage.vue";

const route = useRoute();
const navIndex = ref(0)
const navList = [
  {
    name:'Appsflyer',
    imgUrl:'/image/setting/appsflyer.png'
  },
  {
    name:'GA 4',
    imgUrl:'/image/setting/ga4.png'
  },
]
const navSelect = (index:number) => {
  navIndex.value = index
}

const appsflyerRef = ref()
const gaRef = ref()
const handleAuthorize = () => {
  if(navIndex.value === 0){
    appsflyerRef.value.handleForm()
  }else if(navIndex.value === 1){
    gaRef.value.handleForm()
  }
}
</script>

<style scoped lang="less">

.mmp-content{
  display: flex;
  width: 100%;
  height: 100%;
}
.nav{
  width: 21%;
  height: 100%;
  // flex-shrink: 0;
  margin-right: 12px;
  .nav-content{
    padding-right: 16px;
    width: 100%;
    height: 100%;
    border-right: 1px solid var(--color-neutral-3);
    .nav-item{
      padding: 0 12px;
      margin-bottom: 4px;
      line-height: 40px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;
      &:hover{
        background-color: var(--tant-fill-color-fill1-2);
        color: rgb(var(--primary-6));
      }
    }
    .nav-img{
      width: 28px;
      height: 28px;
      border-radius: 4px;
      margin-right: 8px;
    }
    .nav-active{
      background-color: var(--tant-fill-color-fill1-2);
      color: rgb(var(--primary-6));
    }
  }
}
.content{
  width: 79%;
}
</style>