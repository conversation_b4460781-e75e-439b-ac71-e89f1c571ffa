import {useSessionStorage} from '@vueuse/core'
import {PushModelType, ReportDataRequest, ReportQuerySource} from "@/api/type";
import {computed, ref} from "vue";
import {get16UUID} from "@/utils/strUtil";
import {ReportAnalyseModel} from "@/api/analyse/type";
import {reportAnalyseRequest} from './api'

const channel = `channel_${get16UUID()}`;

/**
 * 分析请求返回值，去除心跳
 */
// export const reportQueryResponse = ref<ReportQueryResponse>()
export const reportQueryResponse = ref()

/**
 * 报表数据查询
 *
 * @param querySource 查询来源
 * @param model 报表类型
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 * @param useCache 使用缓存
 * @param pushModelType 回传周期
 */
export const queryMultiAppReportData = (querySource: ReportQuerySource, model: ReportAnalyseModel, reportId?: string, dashboardId?: string, spaceId?: string, queryParam?: any, useCache = true, pushModelType = PushModelType.ONCE) => {
  const requestId = `request_${get16UUID()}`;
  const appIds = useSessionStorage("app-id-list", [])?.value;

  // 请求API
  reportAnalyseRequest({
    channel,
    requestId,
    appIds,
    model,
    queryParam,
    reportId,
    dashboardId,
    spaceId,
    useCache,
    pushModelType,
    querySource
  } as ReportDataRequest).then(res => {
    reportQueryResponse.value = {
      channel,
      requestId,
      result: res
    }
  }).catch(err => {
    console.error("请求报表分析数据失败",err.message)
    reportQueryResponse.value = {
      channel,
      requestId,
      result: null,
      errorMessage: err.message
    }
    throw err
  })
  return requestId

  // 请求WS
  // wsSend({
  //   channel,
  //   requestId,
  //   appId,
  //   model,
  //   queryParam,
  //   reportId,
  //   dashboardId,
  //   spaceId,
  //   useCache,
  //   pushModelType,
  //   querySource
  // } as ReportDataRequest)
  // return requestId
}

export const querySingleAppReportData = (querySource: ReportQuerySource, model: ReportAnalyseModel, reportId?: string, dashboardId?: string, spaceId?: string, queryParam?: any, useCache = true, pushModelType = PushModelType.ONCE) => {
  const requestId = `request_${get16UUID()}`;
  const appId = useSessionStorage('app-id', '')?.value;

  // 请求API
  reportAnalyseRequest({
    channel,
    requestId,
    appId,
    model,
    queryParam,
    reportId,
    dashboardId,
    spaceId,
    useCache,
    pushModelType,
    querySource
  } as ReportDataRequest).then(res => {
    reportQueryResponse.value = {
      channel,
      requestId,
      result: res
    }
  }).catch(err => {
    reportQueryResponse.value = {
      channel,
      requestId,
      result: null,
      errorMessage: err.message
    }
    console.error("请求报表分析数据失败",err.message)
    throw err
  })
  return requestId
}

/**
 * 自定义SQL查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const querySqlReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return queryMultiAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.CUSTOM, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 探索自定义SQL查询(暂不计入查询历史)
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const exploreSqlReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return queryMultiAppReportData(ReportQuerySource.SYSTEM, ReportAnalyseModel.CUSTOM, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 自定义事件分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryEventReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.EVENT, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 群组分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryGroupReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.GROUP, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 留存分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryRetentionReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.RETENTION, reportId, dashboardId, spaceId, queryParam, false);
}
/**
 * 归因分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryAttributionReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.APPLICATION, reportId, dashboardId, spaceId, queryParam, false);
}
/**
 * 投流分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryTrafficReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return queryMultiAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.APPLICATION, reportId, dashboardId, spaceId, queryParam, false);
}
/**
 * 综合分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryComprehensiveReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return queryMultiAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.APPLICATION, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 运营分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryOpearationReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.APPLICATION, reportId, dashboardId, spaceId, queryParam, false);
}

/**
 * 漏斗分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryFunnelReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.FUNNEL, reportId, dashboardId, spaceId, queryParam, false);
}
/**
 * 分布分析查询
 *
 * @param reportId 关联报表
 * @param dashboardId 关联看板
 * @param spaceId 关联空间
 * @param queryParam 查询参数
 */
export const queryScatterReportData = (queryParam: any, reportId?: string, dashboardId?: string, spaceId?: string,) => {
  return querySingleAppReportData(ReportQuerySource.MANUAL, ReportAnalyseModel.SCATTER, reportId, dashboardId, spaceId, queryParam, false);
}
/**
 * 分析请求返回值 api版本
 */
export const reportQueryResponseData = computed(() => {
  return reportQueryResponse.value
})

/**
 * 分析请求返回值 ws版本
 */
// export const reportQueryResponseData: ReportQueryResponse = wsData
