<template>
  <div class="page-container">
    <div v-if="userCode" class="content">
      <div v-if="userIds?.length > 1" class="user-select">
        选择用户ID:
        <a-select v-model:model-value="userCode" :style="{ width: '280px', marginLeft: '12px' }" placeholder="选择用户ID" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" :allow-search="true" @change="userChange">
          <a-option v-for="item in userIds" :key="item" :value="item">
            {{ item }}
          </a-option>
        </a-select>
      </div>
      <div v-if="!eventListFullScreen" class="info-header">
        <div class="title"> 基础信息 </div>
        <div class="operation">
          <div class="display-attributes">
            <div class="label"> 用户属性 </div>
            <a-select
              v-model="userAttributeSelectedList"
              multiple
              :max-tag-count="2"
              style="width: 400px; height: 32px"
              @remove="selectUserAttributes"
              @popup-visible-change="
                (visible) => {
                  if (!visible) selectUserAttributes();
                }
              "
            >
              <a-option v-for="item in userAttributeList" :key="item.code" :value="item.code" :label="item?.displayName" :disabled="item.name === 'user_id'" />
            </a-select>
          </div>
          <a-tooltip :content="basicInfoVisible ? '隐藏基础信息' : '显示基础信息'">
            <a-button v-if="basicInfoVisible" class="operation-btn" @click="basicInfoVisible = false">
              <template #icon>
                <icon-eye />
              </template>
            </a-button>
            <a-button v-else class="operation-btn" @click="basicInfoVisible = true">
              <template #icon>
                <icon-eye-invisible />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="刷新用户信息">
            <a-button class="operation-btn" @click="refreshBasicInfo">
              <template #icon>
                <icon-sync :spin="basicInfoLoading" />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="下载用户信息">
            <a-button class="operation-btn" @click="Message.info('程序猿正在加班加点开发中... ...')">
              <template #icon>
                <icon-download />
              </template>
            </a-button>
          </a-tooltip>
        </div>
      </div>
      <div v-if="!eventListFullScreen && basicInfoVisible && basicInfoLoading" class="info-list-loading">
        <a-spin :loading="basicInfoLoading" dot />
      </div>
      <div v-else-if="!eventListFullScreen && basicInfoVisible && userBasicInfoList?.length > 0" class="info-list">
        <div v-for="basicInfo in userBasicInfoList" :key="basicInfo.code" class="info-item">
          <div class="info-label">
            {{ basicInfo.displayName }}
          </div>
          <div v-if="basicInfo.value == null || basicInfo.value == undefined" class="operation"> -- </div>
          <a-tooltip v-else :content="basicInfo.value">
            <div class="info-value">
              {{ basicInfo.value }}
            </div>
          </a-tooltip>
        </div>
      </div>
      <div v-else-if="!eventListFullScreen && basicInfoVisible && !userBasicInfoList?.length" class="empty-tag">无用户信息</div>
      <a-divider v-if="!eventListFullScreen" :margin="12" />
      <div v-if="!eventListFullScreen" class="info-header">
        <div class="title"> 所属标签 </div>
        <div class="operation">
          <a-tooltip :content="tagInfoVisible ? '隐藏标签信息' : '显示标签信息'">
            <a-button v-if="tagInfoVisible" class="operation-btn" @click="tagInfoVisible = false">
              <template #icon>
                <icon-eye />
              </template>
            </a-button>
            <a-button v-else class="operation-btn" @click="tagInfoVisible = true">
              <template #icon>
                <icon-eye-invisible />
              </template>
            </a-button>
          </a-tooltip>
        </div>
      </div>
      <div v-if="!eventListFullScreen && basicInfoVisible && basicInfoLoading" class="tag-list-loading">
        <a-spin :loading="basicInfoLoading" dot />
      </div>
      <div v-else-if="!eventListFullScreen && tagInfoVisible" class="tag-list">
        <a-tag v-for="userTag in userTagInfoList" :key="userTag.code" class="tag-item">{{ userTag.name + '：' + userTag.value }}</a-tag>
        <div v-if="!(userTagInfoList?.length > 0)" class="empty-tag"> 无用户标签</div>
      </div>
      <a-divider v-if="!eventListFullScreen" :margin="12" />
      <div class="info-header">
        <div class="title"> 事件序列 </div>
        <div class="operation">
          <div class="display-attributes">
            <div class="label"> 事件属性 </div>
            <a-select
              v-model="eventAttributeSelectedList"
              multiple
              :max-tag-count="2"
              style="width: 308px; height: 32px"
              @remove="selectEventAttributes"
              @popup-visible-change="
                (visible) => {
                  if (!visible) selectEventAttributes();
                }
              "
            >
              <a-option v-for="item in eventAttributeList" :key="item.code" :value="item.code" :label="item?.displayName" :disabled="item.name === 'st' || item.name === 'event_name'" />
            </a-select>
          </div>
          <select-data-source style="margin-right: 12px" @selected="refreshEventList" />
          <a-tooltip :content="eventListFullScreen ? '退出全屏' : '全屏'">
            <a-button v-if="eventListFullScreen" class="operation-btn" @click="eventListFullScreen = false">
              <template #icon>
                <icon-fullscreen-exit />
              </template>
            </a-button>
            <a-button v-else class="operation-btn" @click="eventListFullScreen = true">
              <template #icon>
                <icon-fullscreen />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="刷新事件序列">
            <a-button class="operation-btn" @click="refreshEventList">
              <template #icon>
                <icon-sync :spin="basicInfoLoading" />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="下载事件序列，最多支持3000条">
            <a-button class="operation-btn" @click="Message.info('程序猿正在加班加点开发中... ...')">
              <template #icon>
                <icon-download />
              </template>
            </a-button>
          </a-tooltip>
        </div>
      </div>
      <div :style="{ height: eventListTableHeight }">
        <a-table
          :columns="eventInfoColumns"
          :data="eventInfoList"
          :pagination="pagination"
          :loading="eventListLoading"
          size="small"
          column-resizable
          style="height: 100%"
          :scroll="{ x: '100%', y: '100%' }"
          :scrollbar="true"
          :hoverable="true"
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          @page-change="
            (page) => {
              pagination.current = page;
              refreshEventList();
            }
          "
          @page-size-change="
            (pageSize) => {
              pagination.pageSize = pageSize;
              refreshEventList();
            }
          "
        >
          <template #event-name-filter="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
            <div class="custom-filter">
              <a-space direction="vertical" :size="8">
                <a-select
                  :model-value="filterValue"
                  placeholder="请选择事件"
                  allow-clear
                  allow-search
                  multiple
                  :options="getEventNameFilterOptions()"
                  :style="{ width: '200px' }"
                  :trigger-props="{
                    position: 'top',
                    autoFitPopupMinWidth: true,
                    updateAtScroll: true,
                  }"
                  @update:model-value="(val) => setFilterValue(val)"
                >
                </a-select>
                <div class="custom-filter-footer">
                  <a-button size="mini" @click="handleFilterReset">重置</a-button>
                  <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
                </div>
              </a-space>
            </div>
          </template>
          <template #pagination-left>
            <div class="pagination-left"> 共 {{ pagination.total }} 条 </div>
          </template>
        </a-table>
      </div>
    </div>
    <a-empty v-else style="width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center"> 请通过用户ID查询信息 </a-empty>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getEventAttrList, getUserAttrList, getUserInfoDetail, getUserInfoEventList, getUserInfoIdList } from '@/api/setting/api';
  import { useSessionStorage } from '@vueuse/core';
  import selectDataSource from '@/components/selected-data-source/index.vue';
  import { convertKeysToSnakeCase, getCustomStringLength } from '@/utils/strUtil';
  import { formatTimestamp } from '@/utils/dateUtil';
  import { debounce } from 'lodash';

  const props = defineProps({
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  });
  // 一对多用户列表
  const userIds = ref<any>([]);
  /**
   * 用户编码
   */
  const userCode = ref<string>();

  /**
   * 全屏显示事件列表
   */
  const eventListFullScreen = ref(false);

  /**
   * 基础信息显示
   */
  const basicInfoVisible = ref(true);

  /**
   * 标签信息显示
   */
  const tagInfoVisible = ref(true);

  /**
   * 用户基础信息
   */
  const userBasicInfoList = ref<any>([]);

  /**
   * 用户标签信息
   */
  const userTagInfoList = ref<any>([]);

  /**
   * 基础信息加载
   */
  const basicInfoLoading = ref(true);
  /**
   * 事件列表加载中
   */
  const eventListLoading = ref(true);

  /**
   * 分页参数
   */
  const pagination = ref({
    showPageSize: true,
    pageSize: 30,
    current: 1,
    total: 0,
  });

  /**
   * 事件信息列表
   */
  const eventInfoList = ref<any>([]);

  /**
   * 事件信息列
   */
  const eventInfoColumns = ref<any>([]);

  /**
   * 用户id属性编码
   */
  const userIdAttrCode = ref('');

  /**
   * 用户属性
   */
  const userAttributeList = ref<any>([]);

  /**
   * 选择的用户属性
   */
  const userAttributeSelectedList = useSessionStorage('user-info-user-attribute-selected-list', []);

  /**
   * 默认展示用户属性
   */
  const defaultUserAttributeSelectedList = ['user_id', 'network_type', 'app_version', 'installed_date', 'device_model', 'appsflyer_id', 'media_source', 'campaign_id', 'bx', 'country', 'carrier', 'ad_id', 'campaign_name', 'ad_type', 'media_channel', 'adset_name', 'adset_id', 'ad_name', 'avl_ram'];

  /**
   * 事件发生事件属性编码
   */
  const eventStAttrCode = ref('');

  /**
   * 事件名称属性编码
   */
  const eventNameAttrCode = ref('');

  /**
   * 事件属性列表
   */
  const eventAttributeList = ref<any>([]);
  /**
   * 选择的事件属性
   */
  const eventAttributeSelectedList = useSessionStorage('user-info-event-attribute-selected-list', []);

  /**
   * 事件序列列表高度
   */
  const eventListTableHeight = computed(() => {
    const userSelectHeight = userIds?.value?.length > 1 ? 40 : 0;
    if (eventListFullScreen.value) {
      return `calc(100% - ${userSelectHeight + 50}px)`;
    }
    // 增加 user-select 区域高度
    const infoHeight = 170 + (basicInfoVisible.value ? 140 : 0) + (tagInfoVisible.value ? 42 : 0) + userSelectHeight;
    return `calc(100% - ${infoHeight}px)`;
  });

  /**
   * 刷新用户基础信息
   */
  const refreshBasicInfo = async () => {
    try {
      basicInfoLoading.value = true;
      const params = {
        code: userCode.value,
        attributes: userAttributeSelectedList.value,
        indicators: [],
        show_tags: true,
        showGroups: false,
        showAbExperiment: false,
      };
      const res = await getUserInfoDetail(params);
      userBasicInfoList.value = res?.attributes;
      userTagInfoList.value = res?.tag || [];
    } catch (e) {
      console.log(e);
      userBasicInfoList.value = [];
      userTagInfoList.value = [];
    } finally {
      basicInfoLoading.value = false;
    }
  };

  /**
   * 选择用户属性
   */
  const selectUserAttributes = () => {
    refreshBasicInfo();
  };

  /**
   * 刷新事件序列
   */
  const refreshEventList = debounce(async () => {
    if (!userCode.value) return;
    try {
      eventListLoading.value = true;
      const params = {
        code: userCode.value,
        attributes: eventAttributeSelectedList.value,
        data_source: sessionStorage.getItem('data-source'),
        pageSize: pagination.value.pageSize,
        current: pagination.value.current,
        eventNameList: props.queryParams.events,
        dateRange: props.queryParams.date,
      };
      const res = await getUserInfoEventList(params);
      pagination.value.total = res?.total;
      eventInfoList.value = convertKeysToSnakeCase(res?.items);
    } catch (e) {
      console.log(e);
      eventInfoList.value = [];
      pagination.value.total = 0;
    } finally {
      eventListLoading.value = false;
    }
  }, 300);

  /**
   * 选择事件属性
   */
  const selectEventAttributes = () => {
    eventInfoColumns.value = eventAttributeList.value
      ?.filter((item) => {
        return eventAttributeSelectedList.value.indexOf(item.code) > -1;
      })
      ?.map((item) => {
        // 字段特殊处理
        if (item.name === 'st') {
          return {
            title: `${item?.displayName} (UTC+08)`,
            dataIndex: item.code,
            render: (value) => {
              const { record } = value;
              if (record[item.code] === undefined || record[item.code] === null) {
                return '--';
              }
              return `${formatTimestamp(Number.parseInt(record[item.code]))}.${record[item.code].toString().substring(10, 13)}`;
            },
            width: 200,
            sortable: { sortDirections: ['ascend', 'descend'] },
            fixed: 'left',
            ellipsis: true,
            tooltip: true,
          };
        }
        // 字段特殊处理
        if (item.name === 'event_name') {
          return {
            title: item?.displayName,
            dataIndex: item.code,
            width: 180,
            sortable: { sortDirections: ['ascend', 'descend'] },
            fixed: 'left',
            ellipsis: true,
            tooltip: true,
            filterable: {
              filter: (value, row) => {
                if (!value || value.length === 0) return true;
                return value.includes(row[item.code]);
              },
              slotName: 'event-name-filter',
              multiple: true,
            },
          };
        }

        return {
          title: item?.displayName,
          dataIndex: item.code,
          render: (value) => {
            const { record } = value;
            if (record[item.code] === undefined || record[item.code] === null) {
              return '--';
            }
            return record[item.code];
          },
          width: getCustomStringLength(item?.displayName) + 50,
          sortable: { sortDirections: ['ascend', 'descend'] },
          ellipsis: true,
          tooltip: true,
        };
      });
    refreshEventList();
  };
  const queryData = () => {
    getUserAttrList({
      inApp: 1,
    }).then((res) => {
      if (res?.length > 0) {
        const userIdAttr = res?.filter((item) => item.name === 'user_id');
        if (userIdAttr?.length > 0) {
          userIdAttrCode.value = userIdAttr[0]?.code;
          userAttributeList.value = [...userIdAttr, ...res.filter((item) => item.name !== 'user_id')];
          const initUserAttributeSelectedList = userAttributeList.value
            ?.filter((item) => {
              return userAttributeSelectedList.value.indexOf(item.code) > -1;
            })
            ?.map((item) => item?.code);
          userAttributeSelectedList.value =
            initUserAttributeSelectedList?.length > 1
              ? initUserAttributeSelectedList
              : userAttributeList.value
                  ?.filter((item) => {
                    return defaultUserAttributeSelectedList.indexOf(item.name) > -1;
                  })
                  ?.map((item) => item?.code);
          selectUserAttributes();
        }
      }
    });
    getEventAttrList({
      inApp: 1,
    }).then((res) => {
      if (res?.length > 0) {
        const eventNameAttr = res?.filter((item) => item.name === 'event_name');
        const eventStAttr = res?.filter((item) => item.name === 'st');
        if (eventNameAttr?.length > 0 && eventStAttr?.length > 0) {
          eventNameAttrCode.value = eventNameAttr[0]?.code;
          eventStAttrCode.value = eventStAttr[0]?.code;
          eventAttributeList.value = [...eventStAttr, ...eventNameAttr, ...res.filter((item) => item.name !== 'st' && item.name !== 'event_name')];
          const initEventAttributeSelectedList = eventAttributeList.value
            ?.filter((item) => {
              return eventAttributeSelectedList.value.indexOf(item.code) > -1;
            })
            ?.map((item) => item?.code);
          eventAttributeSelectedList.value =
            initEventAttributeSelectedList?.length > 2
              ? initEventAttributeSelectedList
              : eventAttributeList.value
                  ?.filter((item) => {
                    return item.type === 1;
                  })
                  ?.map((item) => item?.code);
          selectEventAttributes();
        }
      }
    });
  };
  const init = async (code: string) => {
    if (!code) {
      return;
    }
    const res = await getUserInfoIdList(code);
    userIds.value = res || [];
    userCode.value = userIds.value?.[0];
    queryData();
  };
  const userChange = (value) => {
    queryData();
  };
  /**
   * 获取事件名称筛选选项
   */
  const getEventNameFilterOptions = () => {
    if (!eventInfoList.value?.length) return [];
    const eventNameAttr = eventAttributeList.value?.find((attr) => attr.name === 'event_name');
    if (!eventNameAttr) return [];

    return Array.from(new Set(eventInfoList.value.map((row) => row[eventNameAttr.code])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value,
      }));
  };
  defineExpose({
    init,
    refreshEventList,
  });
</script>

<style scoped lang="less">
  @import '@/views/data/user/info/table.less';

  .page-container {
    height: 100%;
    width: 100%;

    .header {
      background: var(--color-bg-2);
      margin-bottom: 24px;
    }

    .content {
      background: var(--color-bg-2);
      height: 100%;
      .user-select {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .info-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        width: 100%;

        .title {
          color: var(--tant-text-gray-color-text1-1);
          font-weight: bolder;
          font-size: 20px;
        }

        .operation {
          display: flex;

          .display-attributes {
            flex: 1;
            display: flex;
            align-items: center;
            margin-right: 12px;

            .label {
              margin-right: 12px;
            }
          }

          .operation-btn {
            background-color: transparent;

            &:hover {
              background-color: var(--tant-secondary-color-secondary-transp-hover);
            }
          }
        }
      }

      .info-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        height: 128px;
        overflow-y: auto;

        .info-item {
          width: 25%;
          display: flex;
          line-height: 32px;

          .info-label {
            margin-right: 12px;
            color: var(--tant-text-gray-color-text1-3);
          }

          .info-value {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow-y: auto;
            flex: 1;
          }
        }
      }

      .tag-list {
        width: 100%;
        height: 30px;
        display: flex;
        flex-wrap: wrap;
        overflow-y: auto;

        .tag-item {
          margin-right: 6px;
          margin-bottom: 6px;
        }

        .empty-tag {
          color: var(--tant-text-gray-color-text1-3);
        }
      }

      .info-list-loading,
      .tag-list-loading {
        width: 100%;
        height: 128px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .tag-list-loading {
        height: 30px;
      }

      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin: 16px 0;

        .display-attributes {
          flex: 1;
          display: flex;
          align-items: center;

          .label {
            margin-right: 12px;
          }
        }

        .operation {
          .operation-btn {
            background-color: transparent;

            &:hover {
              background-color: var(--tant-secondary-color-secondary-transp-hover);
            }
          }
        }
      }
    }
  }
  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
</style>
