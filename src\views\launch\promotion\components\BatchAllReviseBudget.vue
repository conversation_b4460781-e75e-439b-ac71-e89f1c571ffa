<template>
    <!-- 修改预算 -->
    <a-modal v-model:visible="modalVisible" :width="940" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-tabs v-model:active-key="activeName">
            <a-tab-pane key="all" title="全部(1)">
                <a-form ref="formRef" :model="form" :auto-label-width="true" style="height: 400px;overflow: auto;">
                    <a-form-item field="newBudget" label="新日计算">
                        <a-radio-group v-model="form.newBudget" type="button">
                            <a-radio value="1">固定值</a-radio>
                            <a-radio value="2">增加</a-radio>
                            <a-radio value="3">减少</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item field="nums">
                        <a-select v-model="form.budgetType" placeholder="请选择" style="margin-right: 12px;width: 100px;">
                            <a-option value="number">数值</a-option>
                            <a-option v-if="form.newBudget !== '1'" value="percent">百分比</a-option>
                        </a-select>
                        <a-input-number v-model="form.nums" :min="0" style="width: 200px;"/>
                    </a-form-item>
                    <a-form-item field="tableData">
                        <a-table
                            :columns="allColumns"
                            :data="form.tableData"
                            :hoverable="true"
                            sticky-header
                            :table-layout-fixed="true"
                            :column-resizable="true"
                            :pagination="true"
                        >
                            <template #newDailyBudget="{ rowIndex }">
                                <a-input v-model="form.tableData[rowIndex].newDailyBudget" allow-clear>
                                    <template #append>
                                        USD
                                    </template>
                                </a-input>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="unable" title="不能修改(1)">
                <a-table
                    :columns="unableColumns"
                    :data="unableTableData"
                    :hoverable="true"
                    sticky-header
                    :table-layout-fixed="true"
                    :column-resizable="true"
                    :pagination="true"
                    style="height: 420px;"
                >
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <div v-if="activeName === 'all'" class="tip">
            日预算：不限预算（原始金额：600）
        </div>
        <div class="footer">
            <div class="footer-left">
                <a-checkbox>
                    设置为定时任务
                    <a-tooltip placement="top" content="设置定时任务后，可在任务中心的批量编辑中找到相关任务">
                        <icon-question-circle style="margin-left: 8px;"/>
                    </a-tooltip>
                </a-checkbox>
            </div>
            <div class="footer-right">
                <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
                <a-button type="primary" :loading="loading" @click="saveData">
                    保存
                </a-button>
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改预算')
const activeName = ref('all')
const form = reactive({
    newBudget:'1',
    budgetType:'number',
    nums:undefined,
    tableData:[
        {
            adName:'10093_MTG_ColorBlock_20241011',
            dailyBudgetType:'不限',
            newDailyBudget:'',
        },
    ]
})
const allColumns = ref<any>([
    { title: '广告', dataIndex: 'adName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '日预算', dataIndex: 'dailyBudgetType',minWidth:180 },
    { title: '新日预算', dataIndex: 'newDailyBudget',slotName:'newDailyBudget',minWidth:254 },
])
const unableColumns = ref<any>([
    { title: '广告单元', dataIndex: 'adUnit',minWidth:180,ellipsis: true,tooltip: true },
    { title: '不可修改原因', dataIndex: 'reason',minWidth:180 },
])
const unableTableData = ref<any>([
    {
        adUnit:'10093_MTG_ColorBlock_20241011',
        reason:'广告单元包含已暂停的广告',
    },
])
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.tip{
    white-space: pre;
    text-align: right;
}
</style>