import type {LocationQueryRaw, Router} from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import {useUserStore} from '@/store';
import {isLogin} from '@/utils/auth';
import {ROUTE_NAME} from "@/router/constants";

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    const userStore = useUserStore();
    if (isLogin()) {
      if (userStore.code) {
        next();
      } else {
        try {
          await userStore.info();
          next();
        } catch (error) {
          await userStore.logout();
          next({
            name: ROUTE_NAME.LOGIN,
            query: {
              redirect: to.path,
              ...to.query,
            } as LocationQueryRaw,
          });
        }
      }
    } else {
      if (to.name === ROUTE_NAME.LOGIN) {
        next();
        return;
      }
      next({
        name: ROUTE_NAME.LOGIN,
        query: {
          redirect: to.path,
          ...to.query,
        } as LocationQueryRaw,
      });
    }
  });
}
