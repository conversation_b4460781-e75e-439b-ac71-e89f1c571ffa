<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale ? $t(route.meta.locale) : '消息通知' }}</div>
    </div>
    <div class="page-body">
      <message-box />
    </div>
  </div>
</template>

<script setup lang="ts">
import {useRoute} from 'vue-router';
import MessageBox from '@/components/message-box/index.vue';

const route = useRoute();
</script>

<style scoped lang="less"></style>