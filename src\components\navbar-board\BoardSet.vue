

<template>
  <a-modal
      v-model:visible="shareVisible"
      title-align="start"
      :mask-closable="false"
      width="474px">
    <template #title>
      看板设置
    </template>
    <div class="set-board">
      <div class="setting">
        <div class="setting-item">
          <span class="setting-item-title">公开范围</span>
          <div class="setting-item-content">
            <a-select
              v-model="setData.shareType"
              :style="{width:'200px'}"
              placeholder="选择公开范围">
              <a-option value="non_public" label="不公开"></a-option>
              <a-option value="group" label="组内公开"></a-option>
              <a-option value="public" label="全局公开"></a-option>
            </a-select>
          </div>
        </div>
        <div class="setting-item">
          <span class="setting-item-title">公开权限</span>
          <div class="setting-item-content">
            <a-select
              v-model="setData.permissionType"
              :style="{width:'200px'}"
              placeholder="选择权限类型">
              <a-option :value="4" label="查看者">
                <div class="label">查看者</div>
              </a-option>
              <a-option :value="3" label="协作者">
                <div>
                  <div class="label">协作者</div>
                  <div class="tag">添加或移除看板报表、修改看板设置、编辑看板中的全部报表</div>
                </div>
              </a-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
        <div class="footer">
          <a-button style="margin-right: 10px;" @click="shareHandleCancel">取消</a-button>
          <a-button type="primary" :loading="loading" @click="handleOk">
              应用
          </a-button>
        </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">

import {reactive, ref} from "vue";
import {setDashboard} from "@/api/dashboard/api";
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue'
import {DashboardEventBus, RefreshEvent} from '@/types/event-bus';

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const dashboardEventBus = useEventBus<string>(DashboardEventBus);
const shareVisible = ref(false)
const loading = ref(false)
const props = defineProps({
  dashboardShare: {
    type: Object,
    default: () => ({})
  }
})

const setData = reactive({
    shareType: 'non_public',
    permissionType: 4
});

const shareHandleCancel = () => {
  shareVisible.value = false;
};

const handleOk = async () => {
  loading.value = true;
  try {
    const data = {
      dashboardId: dashboardSelected.value.dashboardId,
      ...setData
    };
    console.log(data,'共享data');
    
    await setDashboard(data);
    Message.success('看板设置成功');
    // 发送刷新事件
    dashboardEventBus.emit(RefreshEvent);
    shareVisible.value = false;
  } catch (error) {
    console.error('更新共享设置失败:', error);
    Message.error('看板设置设置失败');
  } finally {
    loading.value = false;
  }
};

const getShareData = async () => {
  try {
    // const res = await getDashboardShareDetail(dashboardSelected.value.dashboardId);
  } catch (error) {
    // console.error('获取共享设置失败:', error);
  }
};

const openModal = async () => {
  shareVisible.value = true;
  setData.shareType = props.dashboardShare?.shareType || 'non_public';
  setData.permissionType = props.dashboardShare?.permissionType || 4;
  await getShareData();
};

defineExpose({
  openModal
});
</script>

<style scoped lang="less">

.set-board {
  padding: 0 12px;

  .setting {
    .setting-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .setting-item-title {
        font-size: 14px;
        color: var(--tant-text-gray-color-text1-2);
        margin-right: 16px;
      }

      .setting-item-content {
        // padding-left: 16px;
      }
    }
  }
}

.label {
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-body-font-body-regular);
  height: 38px;
  display: flex;
  align-items: center;
}

.tag {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 12px;
  line-height: 18px;
  white-space: normal;
  width: 180px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}
</style>