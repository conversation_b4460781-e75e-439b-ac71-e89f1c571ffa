<template>
  <div>
    <a-select
          v-model:model-value="team"
          :loading="loading"
          allow-search
          :style="{width:'320px',borderRadius:'4px',marginLeft:'12px'}"
          @change="teamChange">
          <template #header>
              <div v-if="allowHandle" style="padding: 6px 12px;" >
                  <a-button type="text" @click="addTeam">
                      <template #icon>
                          <icon-plus />
                      </template>
                      新建团队
                  </a-button>
              </div>
          </template>
          <a-option v-for="item in teamList" :key="item.code" :value="item.code">
              <div class="team-item" style="width: 100%;">
                  <span class="name">{{ item.name }}</span>
                  <div v-if="allowHandle" class="extra">
                      <icon-edit class="icon" style="margin-right: 8px;" @click.stop="editTeamItem(item)"/>
                      <icon-delete class="icon" @click.stop="removeTeamItem(item)"/>
                  </div>
              </div>
          </a-option>
      </a-select>
      <handleModal ref="handleRefs" :groups-list="teamList" @update-data="init"/>
    </div>
  </template>
  
  <script lang="ts" setup>
  import {ref} from 'vue';
  import {getTeamList} from "@/api/analyse/api";
  import {deleteTeam} from "@/api/marketing/api";
  import {Message, Modal} from '@arco-design/web-vue'
  import handleModal from './handleModal.vue'

  const props = defineProps({
    allowHandle: {
      type: Boolean,
      default: false
    },
  })
  const team = defineModel<string>('team', { default: '' })
  const loading = ref(false)
  const teamList = ref()
  const emits = defineEmits(['change'])

  const teamChange = (value) => {
    emits('change', value)
  }
  const handleRefs = ref()
  const addTeam = () => {
      handleRefs.value.openModal()
  }
  // 编辑团队列表
  const editTeamItem = (record) => {
      handleRefs.value.openModal(record)
  }
  // 删除团队列表
  const removeTeamItem = (record) => {
      Modal.confirm({
          title: '确认删除',
          titleAlign: 'start',
          content: `确定要删除团队"${record.name}"吗？`,
          modalClass: 'custom-confirm-modal',
          onOk: async () => {
              await deleteTeam(record.code)
              const index = teamList.value.findIndex(item => item.code === record.code);
              if (index > -1) {
                  teamList.value.splice(index, 1);
              }
              if (team.value === record.code) {
                  team.value = teamList.value[0]?.code || '';
              }
              Message.success('删除成功');
          }
      });
  }
  const init = async () => {
    loading.value = true
    await getTeamList().then(res => {
        teamList.value = res
        team.value = teamList.value[0]?.code || ''
    })
    loading.value = false
  }
  init()
  </script>
  
  <style scoped lang="less">
  :deep(.arco-select-option-content){
      width: 100%;
  }
  .team-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name{
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
      }
      .extra{
          .icon{
              cursor: pointer;
          }
      }
  }
  :global(.custom-confirm-modal .arco-modal-footer) {
    text-align: right;
  }
  </style>
    
