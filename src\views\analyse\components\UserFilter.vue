<template>
    <!-- 用户筛选 -->
    <div class="guide">
      <div class="stickyBar" :style="isEmpty ? { color: 'var(--tant-text-gray-color-text1-4)' } : {}">
        <div class="modal" :style="!isEmpty ? { width: 'calc(100% - 46px)' } : {}">
          <svg style="margin-right: 8px;" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 13V3l6 6h-4v4h-2z"></path>
              <path d="M9 3v18l-6-6h4V3h2z"></path>
              <path d="M21 17v-2h-7v2h7z"></path>
              <path d="M21 21v-2h-7v2h7z"></path>
            </svg>
          </svg>
          <span class="title">{{ props.componentName }}</span>
          <div class="model-btn">
            <a-dropdown trigger="hover" :popup-translate="[18, 0]">
              <a-button>
                <template #icon>
                  <icon-more-vertical class="nav-icon"/>
                </template>
              </a-button>
              <template #content>
                <a-doption @click="addEvent('sequentially')">
                  <template #icon>
                    <icon-plus/>
                  </template>
                  <template #default>做过/没做过的事件</template>
                </a-doption>
                <!-- <a-doption @click="addEvent('inOrder')">依次/没依次做过的事件</a-doption> -->
                <a-doption v-show="isUser" @click="addEvent('userProperty')">
                  <template #icon>
                    <icon-plus/>
                  </template>
                  <template #default>用户属性满足</template>
                </a-doption>
                <a-doption v-show="!isEmpty"  @click="createCondition">
                  <template #icon>
                    <icon-save/>
                  </template>
                  <template #default>创建条件分群</template>
                </a-doption>
              </template>
            </a-dropdown>
          </div>
        </div>
        <a-tooltip content="统一筛选周期" position="top">
          <a-switch v-show="!isEmpty" v-model:model-value="useOuterDateRange" size="small" style="margin-left: 8px;" @change="switchChange"/>
        </a-tooltip>
      </div>
      <ClusterIndex ref="refGroupEvent" :only-evt="true" :code-list="codeList" :user-filter="props.userFilter" @change="filtersChange"/>
      <createGroup ref="createRef" :user-filter-data="userFilterData"/>
    </div>
  </template>
  
<script setup lang="ts">
import {computed, inject, onMounted, ref, watch} from "vue";
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue"
import createGroup from "@/views/analyse/components/userFilterComponent/CreateConditionGroup.vue"
import {Message} from '@arco-design/web-vue';
import {useEventBus} from '@vueuse/core';

const emits = defineEmits(['filtersChange'])
const boardDate = inject('boardDate');
const isUser = ref(true)
const props = defineProps({
  componentName: {
    type: String,
    default: '用户筛选'
  },
  // 回显传参
  userFilter:{
    type:Object,
    default:() => {}
  }
})
const eventBus = useEventBus('eventList');
const codeList = ref<any>([])
onMounted(() => {
  eventBus.on((event: any) => {
    codeList.value = event
  })
})
const userFilterData = ref()
const useOuterDateRange = ref(false)
const refGroupEvent = ref()

onMounted(() => {
  useOuterDateRange.value = props.userFilter?.useOuterDateRange ?? false;
  if (useOuterDateRange.value) {
    refGroupEvent.value.changeShowCycle(true)
    refGroupEvent.value.handleUnifiedCycle(boardDate.value);
  }else{
    refGroupEvent.value.changeShowCycle(false)
  }
})

// 
const queryFilterRef = ref()
const add = () => {
  queryFilterRef.value.add()
}
const addEvent = async (type: string) => {
  switch (type) {
    case 'sequentially':
      refGroupEvent.value.add()
      break;
    case 'inOrder':
      refGroupEvent.value.addDone()
      break;
    case 'userProperty':
      refGroupEvent.value.addUser()
      break;
    default:
      break;
  }
}
const switchChange = (val: boolean) => {
    if (val) {
      refGroupEvent.value.changeShowCycle(true)
      refGroupEvent.value.handleUnifiedCycle(boardDate.value);
    }else{
      refGroupEvent.value.changeShowCycle(false)
    }
    userFilterData.value.useOuterDateRange = useOuterDateRange.value
    emits('filtersChange',userFilterData.value)
};
watch(() => boardDate, (newValue) => {
  if (useOuterDateRange.value) {
    refGroupEvent.value.changeShowCycle(true)
    refGroupEvent.value.handleUnifiedCycle(boardDate.value);
  }
}, { deep: true })
const filtersChange = (v) => {
  userFilterData.value = v
  userFilterData.value.useOuterDateRange = useOuterDateRange.value
  emits('filtersChange',userFilterData.value)
}
  // 判断是否为空的计算属性
  const isEmpty = computed(() => {
  // 优先使用内部状态，如果内部状态为空则使用 props 的值
  const data = userFilterData.value || props.userFilter || {};
  const { eventCondition, userCondition } = data;
  return !(
    eventCondition?.singleConditionExpressions?.length ||
    eventCondition?.sequenceConditionExpressions?.length ||
    userCondition?.filters?.length
  );
});
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };

  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };

  // 添加对 userFilter 的校验
  const userFilterList = params?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userCondition?.filters || [];

  // 验证所有列表
  return verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};
const createRef = ref()
const createCondition = () => {
  if(paramsVerify(userFilterData.value)){
    createRef.value.openModal()
  }else{
    Message.error('筛选条件参数错误')
  }
}
defineExpose({
  add,
})
</script>
  
  <style scoped lang="less">
  .filter-btn {
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon {
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      margin-right: 5px;
    }
    .filter-label {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }
    &:hover {
      border-color: var(--tant-primary-color-primary-hover);
    }
  }
  .guide {
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar {
      position: sticky;
      top: 0;
      z-index: 999;
      width: 100% !important;
      margin: 0 !important;
      padding: 12px;
      background-color: var(--tant-bg-white-color-bg1-1);
      display: flex;
      align-items: center;
      .modal {
        width: 100%;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        padding: 8px 12px;
        // color: var(--tant-text-gray-color-text1-4);
        font-size: 16px;
        line-height: 18px;
        vertical-align: top;
        background-color: var(--tant-text-white-color-text2-1);
        border-radius: 4px;
        &:hover {
          background-color: var(--tant-secondary-color-secondary-fill-hover);
        }
        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
  
        .title {
          flex-grow: 1;
          font-weight: 600;
          max-width: calc(100% - 48px);
        }
  
        .model-btn {
          margin-left: auto;
  
          button {
            width: 24px;
            height: 24px;
            background-color: transparent;
          }
        }
      }
    }
  
    .event-filter-box {
      box-sizing: border-box;
      min-height: 32px;
      font-size: 14px;
      transition: all .3s;
  
      .action-row {
        position: relative;
        height: auto;
        width: 100%;
        min-height: 24px;
        line-height: 24px;
        padding-right: 24px;
        padding-left: 24px;
  
        .action-left {
          align-items: flex-start;
          height: auto;
          display: flex;
  
          .drag-index {
            margin-top: 5px;
            margin-right: 12px;
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            color: var(--tant-bg-white-color-bg1-1);
            font-size: 12px;
            font-style: normal;
            line-height: 24px;
            text-align: center;
            background-color: var(--tant-secondary-color-secondary-default);
            border-radius: 4px;
            transition: all .3s;
            opacity: 1;
          }
  
          .hover-drag {
            position: absolute;
            left: 0;
            margin-top: 5px;
            margin-right: 12px;
            flex-shrink: 0;
            // width: 24px;
            // height: 24px;
            font-size: 16px;
            font-style: normal;
            line-height: 24px;
            text-align: center;
            border-radius: 4px;
            opacity: 0;
            transition: all .3s;
          }
  
          .row-content {
            flex-grow: 1;
            box-sizing: border-box;
            padding: 4px 0;
  
            .rename {
              min-width: 80px;
              max-width: calc(100% - 50px);
              height: 24px;
              padding: 0;
              line-height: 24px;
              background: inherit;
              margin-bottom: 6px;
              font-weight: 600;
              font-size: 14px;
              display: flex;
              align-items: center;
  
              .placeholder {
                max-width: 260px;
                display: inline-block;
                height: 32px;
                line-height: 32px;
                padding: 0 10px;
                overflow: hidden;
                font-size: 14px;
                // white-space: pre;
                // vertical-align: middle;
                &:hover {
                  color: var(--tant-primary-color-primary-default);
                }
              }
            }
  
            .event-item {
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              padding: 4px 0;
              overflow: hidden;
              line-height: 32px;
              white-space: normal;
  
  
            }
          }
        }
  
        .action-right {
          position: absolute;
          top: 0;
          right: 4px;
          min-width: 40px;
          height: 36px;
          padding-top: 0 !important;
          display: flex;
          align-items: center;
          opacity: 0;
          transition: opacity .3s;
        }
  
        .filter-btn {
          display: inline-flex;
          align-items: center;
          min-width: 40px;
          max-width: 200px;
          height: 26px;
          padding: 0 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: top;
          background-color: var(--tant-secondary-color-secondary-fill);
          border: 1px solid transparent;
          border-radius: 4px;
          cursor: pointer;
          transition: all .3s;
          box-sizing: border-box;
  
          .btn-icon {
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            margin-right: 5px;
          }
  
          .filter-label {
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
          }
  
          &:hover {
            border-color: var(--tant-primary-color-primary-hover);
          }
        }
  
        .row-word {
          display: inline-block;
          margin: 0 4px;
          color: var(--tant-text-gray-color-text1-2);
          vertical-align: top;
        }
  
        &:hover {
          background-color: var(--tant-fill-color-fill1-2);
  
          .action-left .drag-index {
            opacity: 0;
            transition: all .3s;
          }
  
          .action-left .hover-drag {
            opacity: 1;
            transition: all .3s;
          }
  
          .action-left .row-content :deep(.filter-btn) {
            background: #fff;
          }
  
          .sub-action-left :deep(.filter-btn) {
            background: #fff;
          }
          .action-right {
            opacity: 1;
          }
        }
      }
    }
  }
  
  .btn-bg {
    background-color: transparent;
  
    &:hover {
      background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
  }
  
  .btn-26 {
    width: 26px;
    height: 26px;
    border-radius: 4px;
  }
  
  .btn-bg-delete {
    background-color: transparent;
  
    &:hover {
      color: var(--tant-status-danger-color-danger-default);
      background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
  }
  </style>