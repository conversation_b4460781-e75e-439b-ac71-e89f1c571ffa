

<template>
    <!-- 分组项 -->
    <div class="guide">
        <div class="stickyBar" :style="groupList.length>0? {} : { color: 'var(--tant-text-gray-color-text1-4)' }">
            <div class="modal">
                <icon-apps class="model-icon"/>
                <span class="title">分组项</span>
                <div class="model-btn">
                    <a-button @click="addItem">
                        <template #icon>
                            <icon-plus class="nav-icon"/>
                        </template>
                    </a-button>
                </div>
            </div>
        </div>
        <div class="event-filter-box">
            <draggable :list="groupList" handle=".hover-drag">
                <template #item="{ element,index }">
                    <div class="action-row">
                        <div class="filter-row-eventRow">
                            <div class="action-left">
                                <i class="drag-index">{{ index + 1 }}</i>
                                <icon-drag-dot-vertical class="hover-drag"/>
                                <div class="row-content">
                                    <div class="event-item">
                                        <tabsSelect :info="element" :disabled-list="groupList" @tabs-change="tabsChange(index,$event)"/>
                                        <div class="action-right">
                                            <a-tooltip content="移除" position="top">
                                                <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteItem(index)">
                                                    <template #icon>
                                                        <icon-close-circle />
                                                    </template>
                                                </a-button>
                                            </a-tooltip>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </draggable>
        </div>
    </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import tabsSelect from "./tabsSelect.vue"
import draggable from 'vuedraggable'
import {cloneDeep} from "lodash";
import {analyseStore} from '@/store';


const props = defineProps({
    groupItem:{
        type:Array,
        default: () => []
    },
})
const analyseData = analyseStore();
const emits = defineEmits(['aggregatesChange'])
const groupList = ref<any>([])
const aggregates = ref<any>([])
const triggerVisible = ref(false)


onMounted(async () => {
})
const tabsChange = (index,v) => {
    const {objectName,objectDisplayName,objectType,filterType,objectId} = v
    const filter = { ...groupList.value[index] };
    filter.objectName = objectName;
    filter.objectDisplayName = objectDisplayName;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    groupList.value[index] = filter;
}
const deleteItem = (index:number) => {
    groupList.value.splice(index,1)
}

// 添加处理数据
const addItem = async () => {
    const list  = analyseData.$state.operationGroupLists || []
    const addList = [...list]
    for (let i = 0; i < addList.length; i++) {
        const item = addList[i];
        const exists = groupList.value.some(groupItem => groupItem.objectId === item.code);
        if (!exists) { // 如果该项未被添加
            groupList.value.push({
                ...item,
                objectId: item.code,
                objectDisplayName: item.displayName,
                objectName: item.name,
                objectType: item.dataType,
                dataUrl: item.dataUrl,
            });
            // 一旦添加了第一条未被添加的项，退出循环
            break;
        }
    }
}
watch(() => props.groupItem, (newVal) => {
    groupList.value = cloneDeep(props.groupItem).map(item => {
      return {
        ...item,
        filterType: item.aggregateType,
      }
    })
}, { immediate: true, deep: true })
watch(groupList.value, (newValue, oldValue) => {
    aggregates.value = groupList.value.map(item => {
        const aggregate = {};
        if (item.filterType) aggregate.aggregateType = item.filterType;
        if (item.objectName) aggregate.objectName = item.objectName;
        if (item.objectDisplayName) aggregate.objectDisplayName = item.objectDisplayName;
        if (item.objectId) aggregate.objectId = item.objectId;
        if (item.objectType) aggregate.objectType = item.objectType;
        return aggregate;
    })
    emits('aggregatesChange',aggregates.value)
},{immediate:true,deep:true})
</script>

<style scoped lang="less">
.filter-btn{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon{
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .model-icon{
                margin-right: 8px;
            }
            .title{
                flex-grow: 1;
                font-weight: 600;
                max-width: calc(100% - 48px);
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .drag-index{
                    margin-top: 5px;
                    margin-right: 12px;
                    flex-shrink: 0;
                    width: 24px;
                    height: 24px;
                    color: var(--tant-text-gray-color-text1-3);
                    background-color: var(--tant-disabled-color-disabled-fill);
                    font-size: 12px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    transition: all .3s;
                    opacity: 1;
                }
                .hover-drag{
                    position: absolute;
                    left: 5px;
                    margin-top: 9px;
                    margin-left: 24px;
                    flex-shrink: 0;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    opacity: 0;
                    transition: all .3s;
                    cursor: grab;
                }
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .event-item{
                        display: flex;
                        align-items: center;
                    }
                    .action-right {
                        display: flex;
                        align-items: center;
                        opacity: 0;
                        transition: opacity .3s;
                    }
                }
            }
            .set-icon{
                font-size: 16px;
                height: 26px;
                width: 26px;
                text-align: center;
                line-height: 26px;
                border-radius: 4px;
                cursor: pointer;
                color: var(--tant-text-gray-color-text1-3);
                transition: all .3s;
                border: 1px solid transparent;
                background-color: var(--tant-secondary-color-secondary-fill);
                margin-left: 8px;
                &:hover{
                    border: 1px solid var(--tant-primary-color-primary-default)
                }
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .set-icon{
                background: #fff;
            }
            .action-left .drag-index{
                opacity: 0;
                transition: all .3s;
            }
            .action-left .hover-drag{
                opacity: 1;
                transition: all .3s;
            }
            .action-left .row-content :deep(.filter-btn){
                background: #fff;
            }
            .row-content .action-right{
                opacity: 1;
            } 
        }
        }
        }
       
}
.branch-container{
    display: block !important;
    margin: 0;
    padding: 0 24px 6px 24px;
    font-size: 14px;
    border-radius: 4px;
    .branch-header{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 8px 4px 8px 0;
        .branch-icon{
            background-color: var(--tant-secondary-color-secondary-transp-hover);
            margin-right: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .word{
            margin: 0 8px;
            color: var(--tant-text-gray-color-text1-3);
            font-size: 14px;
        }
    }
    .branch-item{
        position: relative;
        margin: 0 40px;
        .line{
            position: absolute;
            top: 6px;
            bottom: 6px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
        }
        .item-list{
            min-height: 26px;
            padding: 0 18px;
            .sub-action-row{
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;
                .sub-action-left{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    position: relative;
                    .drag-index{
                        flex-shrink: 0;
                        width: 37px;
                        height: 24px;
                        margin-right: 8px;
                        margin-top: 4px;
                        color: var(--tant-text-gray-color-text1-3);
                        background-color: var(--tant-disabled-color-disabled-fill);
                        font-size: 12px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        transition: all .3s;
                        opacity: 1;
                    }
                    .hover-drag{
                        position: absolute;
                        flex-shrink: 0;
                        font-size: 16px;
                        left: 8px;
                        top: 6px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        opacity: 0;
                        transition: all .3s;
                    }
                }
                .sub-action-right{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    display: flex;
                    align-items: center;
                    opacity: 0;
                    transition: opacity .3s;
                    margin-left: 8px;
                }
                &:hover .sub-action-right{
                    opacity: 1;
                }
                &:hover .drag-index{
                    opacity: 0;
                }
                &:hover .hover-drag{
                    opacity: 1;
                }
            }
        }
        
    }
    .row-foot{
        margin: 0;
        padding:0 24px;
        transition: all .3s;
        .ta-filter-button{
            padding: 6px;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            transition: all .3s;
            .action{
                border-radius: 4px;
                background-color: var(--tant-primary-color-primary-fill);
                color: var(--tant-primary-color-primary-default);
                margin-right: 8px;
                padding: 3px;
                font-size: 18px;
            }
            .label{
                color: var(--tant-primary-color-primary-default);
            }
            &:hover .action{
                background-color: var(--tant-primary-color-primary-fill-hover);
                color: var(--tant-primary-color-primary-hover);
            }
        }
    }
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        
        .sub-action-left :deep(.filter-btn){
            background: #fff;
        }
    }
}
.range-block{
    position: relative;
    padding: 16px;
    background: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-overall);
    .range-title{
        margin-bottom: 5px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 600;
        font-size: 14px;
    }
    .range-content{
        width: 268px;
        margin-top: 14px;
        display: flex;
        align-items: flex-start;
    }
    .inline{
        display: inline-block;
    }
    .range-footer{
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
        button{
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
    .custom-range{
        position: absolute;
        bottom: 0;
        right: -230px;
        width: 230px;
        background-color: #fff;
        box-shadow: var(--tant-medium-shadow-medium-bottom);
        .custom-range-set-top{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            padding: 0 8px;
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 500;
            font-size: 14px;
            .custom-range-set-act{
                color: var(--widget-color);
                font-weight: 400;
                font-size: 12px;
                cursor: pointer;
                -webkit-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }
        }
        .custom-range-set-all{
            max-height: 240px;
            padding: 8px;
            overflow-y: auto;
            .custom-range-set-range{
                width: 100%;
                padding: 4px;
                .custom-range-set-symbol {
                    padding: 0 4px;
                }
                .custom-range-set-static {
                    display: inline-block;
                    width: 70px;
                    height: 24px;
                    color: var(--tant-text-gray-color-text1-4);
                    // line-height: 24px;
                    text-align: center;
                    background-color: var(--light-bg-color);
                    border: 1px solid var(--tant-border-color-border1-1);
                }
                .input-number{
                    display: inline-block;
                    width: 70px;
                    height: 24px;
                }
                .custom-range-set-del{
                    opacity: 0;
                    display: inline-block;
                    width: 14px;
                    margin-left: 4px;
                    font-size: 14px;
                    cursor: pointer;
                    &:hover{
                        color: var(--tant-status-danger-color-danger-default);
                    }
                }
                &:hover .custom-range-set-del{
                    opacity: 1;
                }
            }
        }
        .custom-range-set-adds {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            font-size: 12px;
            .custom-range-set-add {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 32px;
                color: var(--widget-color);
                cursor: pointer;
                &:hover{
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
        .custom-range-set-batch{
            width: 100%;
            padding: 12px 8px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            background-color: var(--normal-bg-color);
            border-top: 1px solid var(--tant-border-color-border1-1);
            .input-number{
                display: inline-block;
                width: 56px;
                margin: 0 4px;
            }
            .custom-range-set-btn{
                display: inline-block;
                margin-left: 8px;
                color: var(--widget-color);
                font-size: 14px;
                cursor: pointer;
                &:hover{
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}

.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>