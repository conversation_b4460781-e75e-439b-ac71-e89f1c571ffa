<script setup lang="ts">
  import PureMutiLine from '@/views/special-space/component/overview/chart/pure-muti-line.vue';

  interface Props {
    /**
     * 类型
     */
    type: string;

    /**
     * 日期
     */
    date: string[] | undefined;

    /**
     * 分组
     */
    group: string[];

    /**
     * 数据
     */
    data: any[];

    /**
     * 数据名
     */
    dataName: string;

    /**
     * 加载中
     */
    loading: boolean;
  }

  const props = defineProps<Props>();
  const emits = defineEmits(['showFullScreen']);
</script>

<template>
  <div class="report-half-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ props?.dataName }}
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="全屏">
          <div
            class="operation-icon"
            @click="
              () => {
                emits('showFullScreen', `pure-muti-line-card-${props.type}`, {
                  group: props.group,
                  data: props.data?.map((item) => item.total) || [],
                  dataName: props.dataName,
                });
              }
            "
          >
            <icon-fullscreen class="icon" />
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-content">
      <a-spin :loading="props.loading" style="width: 100%; height: 100%">
        <pure-muti-line :date="props.date" :group="props.group" :data="props.data?.map((item) => item.total) || []" :data-name="props.dataName" />
      </a-spin>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import './style.less';
  .report-card {
    .report-card-toolbox {
      display: block !important;
    }
  }
</style>
