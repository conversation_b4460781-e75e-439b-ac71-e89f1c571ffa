import {MessageRecord} from '@/api/notify/api';

// 反馈消息类型枚举
export enum FeedbackMessageType {
  NEW_FEEDBACK = 'new_feedback',           // 新反馈通知处理人
  FEEDBACK_REPLIED = 'feedback_replied',   // 反馈被回复通知反馈人
  FEEDBACK_STATUS_CHANGED = 'feedback_status_changed' // 反馈状态变更通知相关人员
}

// 反馈消息数据结构
export interface FeedbackMessageData {
  feedbackId: number;        // 反馈ID
  feedbackTitle: string;     // 反馈标题
  feedbackType: string;      // 反馈类型
  feedbackCreator: string;   // 反馈创建人
  feedbackAssignee: string;  // 反馈处理人
  feedbackStatus: string;    // 反馈状态
  replyContent?: string;     // 回复内容（如果有）
  replyAuthor?: string;      // 回复人（如果有）
}

// 扩展消息记录接口，添加额外数据字段
export interface ExtendedMessageRecord extends MessageRecord {
  extraData?: any;  // 额外数据，用于存储反馈相关信息
}

// 创建新反馈消息通知处理人
export function createNewFeedbackMessage(data: FeedbackMessageData): MessageRecord {
  return {
    id: Date.now(), // 使用时间戳作为临时ID
    type: 'message',
    title: '新问题反馈',
    subTitle: `来自${data.feedbackCreator}`,
    content: `您有一个新的问题反馈需要处理：${data.feedbackTitle} (ID:${data.feedbackId})`,
    time: new Date().toLocaleString(),
    status: 0, // 未读
    messageType: 1, // 消息类型：已开通
  };
}

// 创建反馈回复消息通知反馈人
export function createFeedbackRepliedMessage(data: FeedbackMessageData): MessageRecord {
  return {
    id: Date.now(),
    type: 'message',
    title: '问题反馈回复',
    subTitle: `来自${data.replyAuthor}`,
    content: `您的问题反馈"${data.feedbackTitle}"收到了回复：${data.replyContent} (ID:${data.feedbackId})`,
    time: new Date().toLocaleString(),
    status: 0, // 未读
    messageType: 2, // 消息类型：进行中
  };
}

// 创建反馈状态变更消息
export function createFeedbackStatusChangedMessage(data: FeedbackMessageData): MessageRecord {
  return {
    id: Date.now(),
    type: 'message',
    title: '问题反馈状态变更',
    subTitle: '',
    content: `您关注的问题反馈"${data.feedbackTitle}"状态已更新为：${data.feedbackStatus} (ID:${data.feedbackId})`,
    time: new Date().toLocaleString(),
    status: 0, // 未读
    messageType: 2, // 消息类型：进行中
  };
}

// 模拟发送消息到用户（实际项目中应该调用后端API）
export function sendMessageToUser(message: MessageRecord, userId: string): Promise<boolean> {
  // 这里应该调用实际的API发送消息
  console.log(`Sending message to user ${userId}:`, message);
  // 模拟异步操作
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(true);
    }, 100);
  });
}