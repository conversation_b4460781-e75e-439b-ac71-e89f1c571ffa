<script setup lang="ts">
import {ref} from "vue";
import {round} from "lodash";
import PureLine from "@/views/special-space/component/overview/chart/pure-line.vue";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefineddefined

  /**
   * 标题
   */
  title: string | undefined

  /**
   * 数据
   */
  data: number[]

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()
const emits = defineEmits(['showFullScreen']);
const diff = ref<number>();
const diffRate = ref<number>();

const valueLength = props.data?.length;
if (valueLength > 0) {
  const lastDayValue = props.data[valueLength - 1];
  const last2DaysValue = props.data[valueLength - 2];
  diff.value = round(lastDayValue - last2DaysValue, 2)
  diffRate.value = round((lastDayValue - last2DaysValue) * 100 / last2DaysValue, 2)
}


</script>

<template>
  <div class="report-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ props?.title }}
      </div>
      <div class="report-card-indicator">
        <div class="report-card-indicator-item">
          <div class="report-card-indicator-title">
          </div>
          <div v-if="diff ===0" class="report-card-indicator-data">
            -- ( -- %)
          </div>
          <div v-if="diff && diff >=0" class="report-card-indicator-data up">
            ↑ +{{ diff.toLocaleString() }} (+{{ diffRate }}%)
          </div>
          <div v-if="diff && diff < 0" class="report-card-indicator-data down">
            ↓ {{ diff.toLocaleString() }} ({{ diffRate }}%)
          </div>
        </div>
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="全屏">
          <div class="operation-icon" @click="()=>{emits('showFullScreen','overview-card-profit')}">
            <icon-fullscreen class="icon"/>
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-content">
      <pure-line :date="props.date" :data="props.data" :data-name="props.dataName"/>
    </div>
  </div>

</template>

<style scoped lang="less">
@import './style.less';
</style>