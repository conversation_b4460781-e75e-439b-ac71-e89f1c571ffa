<template>
    <div class="page-content">
        <div class="setting" style="margin-bottom:16px">
            <div class="label">类型</div>
            <div class="setting-content">
                <a-radio-group v-model="filterParams.viewType" type="button">
                    <a-radio value="1">素材</a-radio>
                    <a-radio value="2">素材组</a-radio>
                    <a-radio value="3">设计师</a-radio>
                    <a-radio value="4">标签</a-radio>
                    <a-radio value="5">创意人</a-radio>
                </a-radio-group>
            </div>
        </div>
        <Material v-if="filterParams.viewType === '1'"/>
        <MaterialGroup v-if="filterParams.viewType === '2'"/>
        <Designer v-if="filterParams.viewType === '3'"/>
        <Tags v-if="filterParams.viewType === '4'"/>
        <Creativer v-if="filterParams.viewType === '5'"/>
    </div>
  </template>
  
  <script setup lang="ts">
  import { reactive, ref} from "vue";
  import Material from "./material.vue"
  import MaterialGroup from "./materialGroup.vue";
  import Designer from "./designer.vue";
  import Tags from "./tags.vue";
  import Creativer from "./creativer.vue";
  
  // 设置数据
  const filterParams = reactive({
    viewType: '1',
  })
  const init = () => {
  }
  init()
  defineExpose({
    init
  })
  </script>
  
  <style scoped lang="less">
  @import '@/views/launch/promotion/style/common.less';
  </style>