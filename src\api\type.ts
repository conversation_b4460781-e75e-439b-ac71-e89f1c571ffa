import type {AxiosRequestConfig, AxiosResponse} from 'axios'
import {ReportAnalyseModel} from "@/api/analyse/type";

// http 相关实体

/**
 * 接口请求返回实体
 */
export interface ResponseData<T> {

  /**
   * 响应码
   */
  code: number

  /**
   * 相应数据体
   */
  data: T

  /**
   * 响应消息
   */
  msg: string

  /**
   * 时间戳
   */
  timestamp: number
}

/**
 * 分页实体
 */
export interface PageDto<T> {

  /**
   * 每页大小
   */
  size: number

  /**
   * 当前页
   */
  current: number

  /**
   * 总页数
   */
  pages: number

  /**
   * 总数量
   */
  total: number

  /**
   * 相应数据体
   */
  records: Array<T>[]
}

// eslint-disable-next-line import/prefer-default-export
export class ApiError extends Error {

  public readonly url: string

  public readonly status: number

  public readonly statusText: string

  public readonly body: any

  public readonly request: AxiosRequestConfig

  constructor(request: AxiosRequestConfig, response: AxiosResponse, message: string) {
    super(message)

    this.name = 'ApiError'
    this.request = request
    this.url = request.url?.toString() || ''
    this.status = response.status
    this.statusText = response.statusText
    this.body = response.data
  }
}

// websocket 相关实体

/**
 * ws 响应数据
 */
export interface WsResultData {
  /**
   * 查询开始时间，毫秒时间戳
   */
  'startTime': number

  /**
   * 查询结束时间，毫秒时间戳
   */
  'endTime': number
}

/**
 * ws响应实体
 */
export interface ReportQueryResponse {

  /**
   * 请求id，由页面发起的请求id
   */
  'requestId': string;

  /**
   * 请求结果
   */
  'result': WsResultData;

  /**
   * 请求状态
   */
  'status': string;

  /**
   * 连接通道
   */
  'channel': string;
}

/**
 * 报表数据请求发起来源
 */
export enum ReportQuerySource {

  /**
   * 系统自动发起
   */
  SYSTEM = 0,

  /**
   * 手动发起计算
   */
  MANUAL = 1,
}

/**
 * 报表回传模式
 */
export enum PushModelType {

  /**
   * 有新数据时实时更新
   */
  REALTIME = 'real',

  /**
   * 请求时回传一次
   */
  ONCE = 'once',

  /**
   * 定时回传
   */
  SCHEDULED = 'scheduled'
}

/**
 * 报表数据请求参数实体
 */
export interface WsReportDataRequestQueryParam {

}

/**
 * ws请求实体
 */
export interface ReportDataRequest {

  /**
   * 连接通道
   */
  'channel': string;

  /**
   * 请求id
   */
  'requestId': string;

  /**
   * 应用ids
   */
  'appIds'?: string[];
  /**
   * 应用id
   */
  'appId'?: string;

  /**
   * 报表类型
   */
  'model': ReportAnalyseModel;

  /**
   * 查询参数
   */
  'queryParam'?: WsReportDataRequestQueryParam;

  /**
   * 关联报表
   */
  'reportId'?: string;

  /**
   * 关联看板
   */
  'dashboardId'?: string;

  /**
   * 关联空间
   */
  'spaceId'?: string;

  /**
   * 是否使用缓存
   */
  'useCache': boolean;

  /**
   * 推送方式
   */
  'pushModelType': PushModelType;

  /**
   * 查询来源
   */
  'querySource': ReportQuerySource;
}

/**
 * 时间跨度枚举类型
 */
export enum TimeParticleSize {
  /**
   * 1 分钟
   */
  MINUTE1 = 'm1',
  /**
   * 5 分钟
   */
  MINUTE5 = 'm5',
  /**
   * 10 分钟
   */
  MINUTE10 = 'm10',
  /**
   * 1 小时
   */
  HOUR1 = 'h1',
  /**
   * 1 天
   */
  DAY1 = 'D1',
  /**
   * 1 周
   */
  WEEK1 = 'W1',
  /**
   * 1 月
   */
  MONTH1 = 'M1',
  /**
   * 1 季度
   */
  QUARTER1 = 'Q1',
  /**
   * 1 年
   */
  YEAR1 = 'Y1',
  /**
   * 合计
   */
  TOTAL = 'T0',
}

// 定义 FieldType 枚举类型
export enum FieldType {
  // 字符串类型
  STRING = 'string',
  // 布尔类型
  BOOLEAN = 'boolean',
  // 日期类型
  DATE = 'date',
  // 日期时间类型
  DATETIME = 'datetime',
  // 列表类型
  ARRAY = 'array',
  // 对象组
  VARIANT = "variant",
  // 整数类型
  INT = 'int',
  // 小数类型
  FLOAT = 'float',
}

// 获取 FieldType 中文描述的函数
export function getFieldTypeName(value: FieldType): string {
  switch (value) {
    case FieldType.STRING:
      return '文本';
    case FieldType.BOOLEAN:
      return '布尔';
    case FieldType.DATE:
      return '日期';
    case FieldType.DATETIME:
      return '时间';
    case FieldType.ARRAY:
      return '列表';
    case FieldType.VARIANT:
      return '对象';
    case FieldType.INT:
      return '整数';
    case FieldType.FLOAT:
      return '小数';
    default:
      return '未知';
  }
}