import _ from "lodash";
import dayjs from "dayjs";
import {DateRange} from "@/api/analyse/type";

export function getLastDays(currentDay: string | undefined, days: number): string[] {
  const dates: string[] = [];
  const today = currentDay !== null && currentDay !== undefined ? new Date(currentDay) : new Date();

  if (days <= 0) {
    return dates;
  }

  for (let i = 0; i < days; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    dates.push(`${month}/${day}`);
  }

  return dates.reverse();
}

export function getDays(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const latestDate = _.isEmpty(endDate) ? new Date() : new Date(endDate);
  let days = 7;
  if (!_.isEmpty(startDate)) {
    days = (latestDate - new Date(startDate)) / (24 * 60 * 60 * 1000) + 1
  }

  for (let i = 0; i < days; i++) {
    const date = new Date(latestDate);
    date.setDate(date.getDate() - i);

    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    dates.push(`${month}/${day}`);
  }

  return dates.reverse();
}

export function getFullDays(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const latestDate = _.isEmpty(endDate) ? new Date() : new Date(endDate);
  let days = 7;
  if (!_.isEmpty(startDate)) {
    days = (latestDate - new Date(startDate)) / (24 * 60 * 60 * 1000) + 1
  }

  for (let i = 0; i < days; i++) {
    const date = new Date(latestDate);
    date.setDate(date.getDate() - i);

    const year = String(date.getFullYear());
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    dates.push(`${year}-${month}-${day}`);
  }

  return dates.reverse();
}

export function getDateStr(date: any): string[] {
  if (!_.isEmpty(date)) {
    return ''
  }
  const dateValue = new Date(date);
  dateValue.setDate(dateValue.getDate() - i);

  const year = String(dateValue.getFullYear());
  const month = String(dateValue.getMonth() + 1).padStart(2, '0');
  const day = String(dateValue.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`
}

export function formatTimestamp(timestamp: number, offset = '+08:00'): string {
  const date = new Date(timestamp);

  // 解析偏移，例如 "+08:00" -> +480 分钟
  const match = offset.match(/^([+-])(\d{2}):(\d{2})$/);
  if (!match) {
    console.warn(`Invalid timezone offset: ${offset}, fallback to UTC.`);
  }

  const [, sign = '+', hourStr = '00', minStr = '00'] = match || [];
  const offsetMinutes = (parseInt(hourStr) * 60 + parseInt(minStr)) * (sign === '-' ? -1 : 1);

  // 当前 UTC 时间的毫秒数 + 偏移分钟
  const localTime = new Date(date.getTime() + offsetMinutes * 60 * 1000);

  const year = localTime.getUTCFullYear();
  const month = String(localTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(localTime.getUTCDate()).padStart(2, '0');
  const hours = String(localTime.getUTCHours()).padStart(2, '0');
  const minutes = String(localTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(localTime.getUTCSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

export function getCurrentPSTDateISO(date?: Date): string {
  const now = _.isEmpty(date) ? new Date() : date;
  const options = {
    timeZone: 'America/Los_Angeles',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  };
  const formatter = new Intl.DateTimeFormat('en-US', options);
  const [month, day, year] = formatter.format(now).split('/');
  return `${year}-${month}-${day}`;
}

export function getCurrentUTCDateISO(date?: Date): string {
  const now = !date ? new Date() : date;
  const options = {
    timeZone: 'UTC',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  };
  const formatter = new Intl.DateTimeFormat('en-US', options);
  const [month, day, year] = formatter.format(now).split('/');
  return `${year}-${month}-${day}`;
}

export function getCurrentPSTDateTime() {
  const now = new Date();
  const options = {
    timeZone: 'America/Los_Angeles', // PST对应的时区
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false, // 24小时制
  };

  // 使用Intl.DateTimeFormat格式化日期和时间
  const formatter = new Intl.DateTimeFormat('en-US', options);
  const formatted = formatter.format(now); // 格式: MM/DD/YYYY, HH:mm:ss

  // 拆分并重新组织格式为 YYYY-MM-DD HH:mm:ss
  const [date, time] = formatted.split(', ');
  const [month, day, year] = date.split('/');
  return `${year}-${month}-${day} ${time}`;
}

export function getCurrentUTCDateTime() {
  const now = new Date();
  const options = {
    timeZone: 'UTC', // PST对应的时区
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false, // 24小时制
  };

  // 使用Intl.DateTimeFormat格式化日期和时间
  const formatter = new Intl.DateTimeFormat('en-US', options);
  const formatted = formatter.format(now); // 格式: MM/DD/YYYY, HH:mm:ss

  // 拆分并重新组织格式为 YYYY-MM-DD HH:mm:ss
  const [date, time] = formatted.split(', ');
  const [month, day, year] = date.split('/');
  return `${year}-${month}-${day} ${time}`;
}

/**
 * 静态日期 转为 动态日期
 * YYYY-MM-DD -> number
 * @param date 动态日期
 */
export const dateStaticToDynamic = (date: string | Date): number => {
  const today = dayjs().endOf('day');
  const targetDate = dayjs(date);
  const difference = today.diff(targetDate, 'day');
  // 动态时间最晚不能超过今天
  return Math.max(difference, 0);
}

/**
 * 动态日期 转为 静态日期
 * number -> YYYY-MM-DD
 * @param value 动态日期
 */
export const dateDynamicToStatic = (value: string | number): string => {
  return dayjs().subtract(Number(value), 'day').format('YYYY-MM-DD')
}

/**
 * 获取日期范围的文本显示
 */
export const formatDateRangeText = (dateRange: DateRange) => {
  if (!dateRange) {
    return undefined;
  }
  const {startDate, endDate, recentStartDate, recentEndDate} = dateRange;
  // 动态时间特殊处理
  if (recentEndDate !== null && recentEndDate !== undefined) {
    // 自某日至今
    if (recentEndDate === 0) {
      if (recentStartDate && recentStartDate > 0) {
        if (recentStartDate === 0) {
          return '今日'
        }
        return `最近${recentStartDate + 1}天`
      }
    }

    // 自某日至昨日
    if (recentEndDate === 1) {
      if (recentStartDate && recentStartDate > 0) {
        if (recentStartDate === 1) {
          return '昨日'
        }
        return `过去${recentStartDate}天`
      }
    }
  }

  const startText = (recentStartDate !== null && recentStartDate !== undefined) ?
    recentStartDate === 0 ? '今日' :
      recentStartDate === 1 ? '昨日' :
        `${recentStartDate}天前`
    : startDate
  const endText = (recentEndDate !== null && recentEndDate !== undefined) ?
    recentEndDate === 0 ? '今日' :
      recentEndDate === 1 ? '昨日' :
        `${recentEndDate}天前`
    : endDate
  return startText === endText ? startText : `${startText} -> ${endText}`
}

/**
 * 获取日期范围的开始日期
 * 格式：YYYY-MM-DD
 *
 * @param dateRange 日期范围
 */
export const getDateRangeStartDate = (dateRange: DateRange) => {
  if (!dateRange) {
    return undefined;
  }
  const {startDate, recentStartDate} = dateRange;
  if (recentStartDate !== null && recentStartDate !== undefined) {
    return dateDynamicToStatic(recentStartDate)
  }
  return startDate;
}

/**
 * 获取日期范围的结束日期
 * 格式：YYYY-MM-DD
 *
 * @param dateRange 日期范围
 */
export const getDateRangeEndDate = (dateRange: DateRange) => {
  if (!dateRange) {
    return undefined;
  }
  const {endDate, recentEndDate} = dateRange;
  if (recentEndDate !== null && recentEndDate !== undefined) {
    return dateDynamicToStatic(recentEndDate)
  }
  return endDate;
}

/**
 * 压缩日期范围为最后一天
 *
 * @param dateRange 日期范围
 */
export const compressDateRangeToEndDate = (dateRange: DateRange): DateRange => {
  if (!dateRange) {
    return dateRange;
  }
  let newDateRange = {};
  const {endDate, recentEndDate} = dateRange;
  if (recentEndDate !== null && recentEndDate !== undefined) {
    newDateRange = {
      recentStartDate: recentEndDate,
      recentEndDate
    }
  } else {
    newDateRange = {
      startDate: endDate,
      endDate,
    }
  }
  const dateText = formatDateRangeText(newDateRange)
  return {
    ...newDateRange,
    dateText
  };
}

