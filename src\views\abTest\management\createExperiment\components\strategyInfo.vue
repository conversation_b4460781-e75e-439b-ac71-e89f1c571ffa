<template>
  <a-spin :loading="dataLoading" class="form-content">
    <a-form ref="formRef" :model="form" :rules="rules" label-align="left" style="width: 100%;">
      <div class="form-label">
        <div class="title">流量设置</div>
        <div class="extra"></div>
      </div>
      <a-form-item field="isMutualExclusion" label="是否互斥">
        <a-radio-group v-model:model-value="form.isMutualExclusion" @change="exclusionChange">
          <a-radio :value="0">否</a-radio>
          <a-radio :value="1">流量层互斥</a-radio>
          <a-radio :value="2">直属互斥域</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="form.isMutualExclusion === 1" field="flowLayerCode" label="" :hide-asterisk="true">
        <a-select
          v-model:model-value="form.flowLayerCode"
          :style="{width:'320px'}"
          :loading="layerLoading"
          placeholder="请选择流量层"
          :filter-option="false"
          allow-search
          @search="handleLayerSearch"
          @change="layerChange">
          <a-option v-for="layer of layerOptions" :key="layer.code" :value="layer.code">{{ layer?.flowDomain?.domainGroup?.name }} - {{ layer?.flowDomain?.name }} - {{ layer.name }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="form.isMutualExclusion === 2" field="flowDomainCode" label="" :hide-asterisk="true">
        <a-select
          v-model:model-value="form.flowDomainCode"
          :style="{width:'320px'}"
          :loading="domainLoading"
          placeholder="请选择互斥域"
          :filter-option="false"
          allow-search
          @search="handleDomainSearch">
          <a-option v-for="domain of domainOptions" :key="domain.code" :value="domain.code">{{ domain?.domainGroup?.name }} - {{ domain.name }}</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="flowPercentage" label="实验流量" validate-trigger="blur">
        <a-input-number
            v-model="form.flowPercentage"
            :step="0.1"
            :precision="1"
            :formatter="formatter"
            :parser="parser"
            :max="remainFlowPercentage"
            style="width: 330px;">
          <template #suffix>
            该流量层最多可占用{{remainFlowPercentage}}%
          </template>
        </a-input-number>
      </a-form-item>
      <a-form-item field="enableGradual" label="流量生效方式">
        <a-radio-group v-model:model-value="form.enableGradual">
          <a-radio :value="false">立即生效</a-radio>
          <a-radio :value="true">平滑生效</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item v-if="form.enableGradual" field="gradualDuration" label="平滑生效时间">
        <a-input-number v-model="form.gradualDuration" :step="1" :min="0" style="width: 200px;" placeholder="建议0-1440区间">
          <template #suffix>
            min
          </template>
        </a-input-number>
      </a-form-item>
      <a-form-item field="userScope" label="受众范围">
        <a-select v-model="form.userScope" :style="{width:'330px'}" placeholder="请选择受众范围" @change="scopeChange">
          <a-option value="all">活跃用户</a-option>
          <a-option value="new">新增用户</a-option>
        </a-select>
      </a-form-item>
      <div class="form-label">
        <div class="title">受众规则</div>
        <div class="extra"></div>
      </div>
      <div class="condition-item">
        <cluster-index ref="tagConditionRef" :only-evt="true" :is-provide-client="true" :user-filter="form.userRules" @change="filtersChange"/>
        <a-dropdown trigger="hover" :popup-translate="[18, 0]">
          <div class="ta-filter-button">
            <div class="ta-filter-button-icon">
              <icon-plus/>
            </div>
            添加筛选条件
          </div>
          <template #content>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('sequentially')">做过/没做过的事件</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('inOrder')">依次/没依次做过的事件</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('userProperty')">用户属性满足</a-doption>
            <a-doption v-if="form.userScope === 'all'" @click="addEvent('groupProperty')">用户分群满足</a-doption>
            <a-doption @click="addEvent('clientParams')">客户端参数</a-doption>
          </template>
        </a-dropdown>
      </div>
      <a-form-item field="consistency" label="体验一致性" tooltip="开启后，实验期间用户生效版本不随流量、受众条件调整发生变化">
        <a-switch v-model="form.consistency" disabled :checked-value="true" :unchecked-value="false"/>
      </a-form-item>
      <a-form-item label="">
        <a-button style="border-radius: 4px;" :loading="loading" @click="backCurrent">
          上一步
        </a-button>
        <a-button
            type="primary"
            style="border-radius: 4px;margin: 0 24px;"
            :loading="loading"
            @click="handleSubmit">
          下一步
        </a-button>
        <a-button style="border-radius: 4px;" :loading="loading" @click="cancleEdit">
          取消
        </a-button>
      </a-form-item>
    </a-form>
  </a-spin>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {toolStore} from '@/store';
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue";
import {getDomainList, getExperimentDetail, getLayerList, saveExperiment} from "@/api/ab/api";
import {removeEnumList} from "@/views/analyse/components/util/verify";
import {validateClientConditionRules,paramsVerify} from "@/views/abTest/commonUtil";

const props = defineProps({
  code: {
    type: String,
    default: ''
  }
})

const toolData = toolStore();
const form = reactive({
  enableGradual: false,
  isMutualExclusion: 0,
  flowLayerCode: '',
  flowDomainCode: '',
  flowPercentage: 100,
  consistency: true,
  gradualDuration: undefined,
  userScope: 'all',
  userRules: {}
})
const rules = {
  enableGradual: [
    {
      required: true,
      message: '请选择生效方式',
    }
  ],
  consistency: [
    {
      required: true,
      message: '请选择体验一致性',
    }
  ],
  gradualDuration: [
    {
      required: true,
      message: '请输入平滑生效时间',
    }
  ],
  userScope: [
    {
      required: true,
      message: '请选择受众范围',
    }
  ],
  flowLayerCode: [
    {
      required: true,
      message: '请选择流量层',
    }
  ]
}
const formatter = (value: number | string) => {
  return `${value}%`;
};

const parser = (value: string) => {
  return value.replace('%', '');
};
const loading = ref(false)
const layerOptions = ref<any>([]);
const domainOptions = ref<any>([]);
const layerLoading = ref(false)
const domainLoading = ref(false)
const formRef = ref();
const emits = defineEmits(['toNext', 'toPrev', 'cancleEdit'])


const scopeChange = () => {
  form.userRules = {}
}
const filtersChange = (v) => {
  form.userRules = v
}
const tagConditionRef = ref()
/**
 * 添加筛选条件
 * @param type 条件类型
 */
const addEvent = async (type: string) => {
  switch (type) {
    case 'sequentially':
      tagConditionRef.value.add()
      break;
    case 'inOrder':
      tagConditionRef.value.addDone()
      break;
    case 'userProperty':
      tagConditionRef.value.addUser()
      break;
    case 'groupProperty':
      tagConditionRef.value.addGroup()
      break;
    case 'clientParams':
      tagConditionRef.value.addClientParams()
      break;
    default:
      break;
  }
}
const handleSubmit = async () => {
  formRef.value.validate(async (valid: any) => {
    if (!valid) {
      loading.value = true
      try {
        if (!paramsVerify(form.userRules)) {
          Message.error('筛选条件参数错误')
          return
        }
        if (!validateClientConditionRules(form.userRules?.clientCondition?.rules)) {
          Message.error('客户端条件参数错误')
          return
        }
        const data = removeEnumList(form.userRules)
        const params = {
          code: props.code,
          flowPercentage: form.flowPercentage,
          flowLayerCode: form.flowLayerCode,
          flowDomainCode: form.flowDomainCode,
          enableGradual: form.enableGradual,
          gradualDuration: form.gradualDuration,
          userScope: form.userScope,
          userRules: data,
          consistency: form.consistency,
          remainEditStep: 2
        }
        await saveExperiment(params)
        emits('toNext')
      } catch (e) {
        // Message.error('保存失败，请重试')
        console.error(e)
      } finally {
        loading.value = false
      }
    }
  })
};
const cancleEdit = () => {
  emits('cancleEdit')
}
const backCurrent = () => {
  emits('toPrev')
}

// 获取流量层列表
const getLayerOptions = async (name?: string) => {
  layerLoading.value = true;
  try {
    const params = {
      name: name || '',
      current: 1,
      pageSize: 20,
    }
    const res = await getLayerList(params);
    layerOptions.value = res.items || [];
  } catch (e) {
    layerOptions.value = [];
  } finally {
    layerLoading.value = false;
  }
}
const handleLayerSearch = async (value) => {
  if (value) {
    await getLayerOptions(value);
  } else {
    layerOptions.value = [];
  }
};
// 获取最多可占用流量
const remainFlowPercentage = ref(0)
const layerChange = (value) => {
  const selectedLayer = layerOptions.value.find(item => item.code === value)
  remainFlowPercentage.value = selectedLayer?.remainFlowPercentage
}
// 是否互斥切换
const exclusionChange = (value) => {
  if (value === 0) {
    form.flowLayerCode = ''
    remainFlowPercentage.value = 100
  }
}
// 获取互斥域列表
const getDomainOptions = async (name?: string) => {
  domainLoading.value = true;
  try {
    const params = {
      name: name || '',
      current: 1,
      pageSize: 20,
    }
    const res = await getDomainList(params);
    domainOptions.value = res.items || [];
  } catch (e) {
    domainOptions.value = [];
  } finally {
    domainLoading.value = false;
  }
}

const handleDomainSearch = async (value) => {
  if (value) {
    await getDomainOptions(value);
  } else {
    domainOptions.value = [];
  }
};

getLayerOptions()
getDomainOptions()
const dataLoading = ref(false)
const init = async () => {
  dataLoading.value = true
  try {
    await toolData.fetchAllModalList()
    if(toolData.allAttrList.length === 0){
      await toolData.fetchAllAttrList()
    }
    if (props.code) {
      const res = await getExperimentDetail(props.code)
      form.flowPercentage = res?.flowPercentage || 100
      form.enableGradual = res.enableGradual || false
      form.gradualDuration = res.gradualDuration
      form.userScope = res.userScope || 'all'
      form.userRules = res.userRules || {}
      form.consistency = res.consistency || true
      form.isMutualExclusion = res?.flowLayer?.code ? 1 : 0
      form.flowLayerCode = res?.flowLayer?.code || ''
      if(form.flowLayerCode) {
        layerChange(form.flowLayerCode)
      }else{
        remainFlowPercentage.value = 100
      }
    }
  } catch (error) {
    console.error('初始化策略信息失败：', error)
  } finally {
    dataLoading.value = false
  }
}
init()
</script>

<style scoped lang="less">
.form-content{
  width: 100%;
  height: 100%;
}
.form-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;

  .title {
    color: #141414e6;
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
  }

  .extra {
    margin-left: 16px;
  }
}

.condition-item {
  padding: 20px;
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: 8px;
  margin-bottom: 16px;
}

.ta-filter-button {
  display: inline-flex;
  color: var(--tant-primary-color-primary-default);
  align-items: center;
  padding: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all .3s;

  &:hover {
    background-color: var(--tant-bg-white-color-bg1-1);
    color: var(--tant-primary-color-primary-hover);
  }
}
</style>