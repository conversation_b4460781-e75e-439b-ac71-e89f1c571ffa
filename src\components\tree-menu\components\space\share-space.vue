<script setup lang="ts">

export interface UserGroup {
  // 组名
  groupName: string
  // ID
  id: string
  isDefault: boolean
  userNum: number
  // 看板身份权限
  boardAuthority?: string

}

export interface User {
  // 用户名
  userName: string;
  // 用户ID
  userId: string;
  // 所属分组ID
  groupId: string;
  // 看板身份权限
  boardAuthority?: string
}

const shareVisible = defineModel('visible')
const shareHandleCancel = () => {
  shareVisible.value = false;
}
const users: User[] = [
  {userName: '张三', userId: 'a', groupId: '1', boardAuthority: '创建人'},
  {userName: '李四11111', userId: 'b', groupId: '1', boardAuthority: '查看者'},
  {userName: '王五', userId: 'c', groupId: '1', boardAuthority: '协作者'},
  {userName: '赵六', userId: 'd', groupId: '2', boardAuthority: ''},
  {userName: '孙七', userId: 'e', groupId: '2', boardAuthority: ''},
  {userName: '周八', userId: 'f', groupId: '3', boardAuthority: ''},
];
const getInitials = (fullName: string) => {
  if (!fullName) {
    return '';
  }
  const names = fullName?.trim().split(' ');
  return names.map(name => name.charAt(0).toUpperCase()).join('');

}
</script>

<template>
  <a-modal
      :visible="shareVisible"
      title-align="start"
      :mask-closable="false"
      :footer="false"
      :width="474"

      @cancel="shareHandleCancel">
 <template #title >
   <div >
     查看空间共享状态
   </div>
 </template>
    <div class="modalTitle">
      <div class="space-body">
        <a-row class="space-user-name">
          空间管理者
        </a-row>
        <div >
          <div v-for="avatar in users" :key="avatar.userId">
            <div v-if="avatar.boardAuthority==='创建人'">
              <div class="space-user-avatar">
                  <a-avatar
                      :size="27"
                      :style="{backgroundColor:'var(--tant-decorative-orange-color-decorative2-5)'}">
                    {{ getInitials(avatar.userName) }}
                  </a-avatar>
                  {{ avatar.userName }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="space-body">
        <a-row class="space-user-name">
          空间成员
        </a-row>
        <div class="space-member">
          <div v-for="(avatar) in users" :key="avatar.userName">
            <div v-if="avatar.boardAuthority==='查看者'||avatar.boardAuthority==='协作者'" class="space-user">
              <div class="space-user-avatar">
                <a-avatar
                    :size="27"
                    :style="{backgroundColor:'var(--tant-decorative-orange-color-decorative2-1)'}">
                  <div v-if="avatar.userName">{{ getInitials(avatar.userName) }}</div>
                  <div v-else>{{ getInitials(avatar.groupName) }}}</div>
                </a-avatar>
                <div v-if="avatar.userName" class="">{{ avatar.userName }}</div>
                <div v-else class="">{{ avatar.groupName }}</div>
              </div>
                <div class="boardAuthority">{{ avatar.boardAuthority }}</div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">


  .space-body {
    padding-bottom: 10px;

    .space-user-name {
      color: var(--tant-text-gray-color-text1-3);
      font: var(--tant-description-font-description-regular);
      align-self: center;
      white-space: nowrap;
      height: 32px;

    }

    .space-user-avatar {
      display: flex;
      flex-direction: row;
      gap: 10px;
      height: 40px;
      align-items: center;
      width: 245px;
    }
  }


.boardAuthority {
  display: flex;
  align-items: center;
  width: 100px;
}

.space-member {
  height: 30vh;
  overflow-y: hidden;
}

.modalTitle{
  padding: 0 12px 8px;
}
.space-user{
  display: flex;
  gap:10px;
}
</style>