<!-- 分析页面头部组件 -->
<template>
  <div class="report-header">
    <div class="header-left">
      <a-space align="center">
        <a-tooltip :content="props.headerInfo.title" position="right">
          <img :src="props.headerInfo.img" alt=""/>
        </a-tooltip>
        <span v-if="!props.reportData?.code" class="name">{{ route.meta.locale }}</span>
        <a-tooltip :content="`点击修改名称：${formData.name}`" position="top">
          <a-input v-if="props.reportData?.code" v-model="formData.name" class="report-name-input" @change="updateChange"/>
        </a-tooltip>
        <a-tooltip :content="`点击修改备注：${formData.description}`" position="top">
          <a-input v-if="props.reportData?.code" v-model="formData.description" class="report-description-input" placeholder="备注" @change="updateChange">
            <template #prefix>
              <img src="/icon/tooltip.svg" alt="" style="width: 16px;height: 16px;">
            </template>
          </a-input>
        </a-tooltip>
        <a-tooltip :content="props.headerInfo.tips" position="right">
          <icon-info-circle v-if="!props.reportData?.code" size="16"/>
        </a-tooltip>
      </a-space>
    </div>
    <div class="header-right">
      <a-space align="center">
        <select-app-list v-if="props.headerInfo.multipleApp"/>
        <select-app v-if="props.headerInfo.showAppSelect"/>
        <select-data-source :disabled="props.model === 'event'"/>
        <time-zone-select/>
        <a-tooltip v-if="props.headerInfo.showUpdateTime" :content="'最后更新：' + refreshTime" position="top">
          <a-button class="button-background" @click="refresh">
            <template #icon>
              <icon-refresh class="nav-icon"/>
            </template>
          </a-button>
        </a-tooltip>
        <a-tooltip v-if="props.headerInfo.showDownloadData" content="以页面格式下载全量数据" position="top">
          <a-button :loading="dataDownloadLoading" class="button-background" @click="dataDownload">
            <template #icon>
              <icon-to-bottom class="nav-icon"/>
            </template>
          </a-button>
        </a-tooltip>
        <a-button class="button-background" @click="showReport">
          已存报表
        </a-button>
      </a-space>
    </div>
    <stored-report-vue  v-model:visible="visible" class="report-vue" :popup-container="props.headerInfo.root"/>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import dayjs from 'dayjs';
import timeZoneSelect from "@/components/time-zone-select/index.vue";
import selectAppList from "@/components/selected-game-app-list/index.vue"
import selectApp from "@/components/selected-game-app/index.vue"
import storedReportVue from "@/views/analyse/components/SavedReport.vue";
import selectDataSource from "@/components/selected-data-source/index.vue"
import {getAnalyticsDataDownload} from "@/api/analyse/api";
import {useRoute} from "vue-router";

const route = useRoute();
const props = defineProps({
    headerInfo: {
      type: Object,
      default() {
        return {
            title:'',
            img:'',
            tips:'',
            root:'',
            showUpdateTime:true,
            showDownloadData:true,
            showMoreValue: true,
            multipleApp:false,
            showAppSelect:true
        };
      },
    },
    reportData:{
      type: Object,
      default:() => {}
    },
    model:{
      type: String,
      default:''
    },
    queryParam:{
      type: Object,
      default:() => {}
    }
})

const formData = reactive({
  name:props.reportData?.name || '',
  description:props.reportData?.description || ''
})
watch(() => props.reportData, (newVal) => {
  if (newVal) {
    formData.name = newVal.name || ''
    formData.description = newVal.description || ''
  }
}, { immediate: true })
const visible = ref<boolean>(false)
// 更新时间
const refreshTime = ref(dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'))
const emits = defineEmits(['callComputedData','callExportXlsx','updateReportForm']);
// 更新
const refresh = () => {
  emits('callComputedData')
  refreshTime.value = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
}
// 下载全量数据
const dataDownloadLoading = ref(false)
const dataDownload = async () => {
  // emits('callExportXlsx')
  dataDownloadLoading.value = true
  const downNameMap = {
    'application': '运营分析_全量数据.csv',
    'event': '事件分析_全量数据.csv',
    'funnel': '漏斗分析_全量数据.csv',
    'retention': '留存分析_全量数据.csv',
    'group': '群组分析_全量数据.csv',
    'scatter': '分布分析_全量数据.csv',
  }
  const res = await getAnalyticsDataDownload(props.model, props.queryParam)
  const url = window.URL.createObjectURL(new Blob([res.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download',  downNameMap[props.model]);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  dataDownloadLoading.value = false
}
const showReport = () => {
  visible.value = !visible.value
};

const updateChange = () => {
  emits('updateReportForm',formData)
}

</script>

<style scoped lang="less">
  .report-header {
    width: 100%;
    height: 74px;
    padding: 0 0 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }

    .name {
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 20px;
    }

    .button-background {
      background-color: #ffffff;
      padding: 8px;
      border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
    }

    .button-background:hover {
      color: #BDBEBF;
      background-color: #fff;
    }

    :deep(.arco-btn-secondary) {
      background: #fff;
    }
  }
  .report-name-input {
    min-width: 100px;
    height: 34px;
    overflow: hidden;
    line-height: 34px;
    font-size: 20px;
    border: none;
    background-color: transparent;
    font-weight: 600;
    :deep(.arco-input) {
      &:hover, &:focus-within {
        background-color: #fff !important;
      }
    }
    :deep(.arco-input-size-medium){
      font-size: 20px;
      font-weight: 500;
      border-radius: 4px;
    }
}
.report-description-input {
  height: 34px;
  overflow: hidden;
  line-height: 34px;
  border: none;
  background-color: transparent;
  transition: width 0.3s;
  // :deep(.arco-input) {
  //   width: auto;
  //   &:hover, &:focus-within {
  //     background-color: var(--tant-secondary-color-secondary-transp-active);
  //   }
  // }
  &:hover, &:focus-within{
    background-color: var(--tant-secondary-color-secondary-transp-active);
  }
  :deep(.arco-input-size-medium){
    font-size: 16px;
    border-radius: 4px;
    color: var(--tant-text-gray-color-text1-2);
  }
}
</style>