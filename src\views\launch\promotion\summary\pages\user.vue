<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  placeholder="请选择所属账户"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>所属账户-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">不限</a-option>
                  <a-option value="2">所属账户</a-option>
                  <a-option value="3">协助账户</a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择渠道"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>渠道-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">Meta</a-option>
                  <a-option value="2">Mintegral</a-option>
              </a-select> -->
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
              <div class="text">详细数据</div>
          </div>
          <div class="wrap-right">
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
            :columns="columns"
            :loading="loading"
            :data="tableData"
            :bordered="false"
            :hoverable="true"
            sticky-header
            :table-layout-fixed="true"
            :filter-icon-align-left="true"
            :column-resizable="true"
            :scroll="scroll"
            :pagination="false"
            :summary="true"
          >
          <template #user="{ record }">
            <div class="cell-content">
              <div class="text">{{ record.user }}</div>
            </div>
          </template>
          <template #summary-cell="{ column,record }">
            <div v-if="column.dataIndex === 'user'">
              <div class="text">汇总</div>
            </div>
            <div v-else>{{record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : ''}}</div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject} from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';

// 设置数据
const filterParams = reactive({
  viewType: '1',
  showChildrenMaterial: false,
  showLaunchData: false,
})
const dateTime = ref(inject('dateTime') as any[])
const total = ref(0)
// 模拟table数据
const loading = ref(false)
const tableData = ref<any>([
  {
    user: 'mockName1',
    cost: 1000,
    impressions: 1000,
    cpm: 1000,
    clicks: 1000,
    cpc: 1000,
    ctr: 1000,
    conversions: 1000,
  },
  {
    user: 'mockName2',
    cost: 1000,
    impressions: 1000,
    cpm: 1000,
    clicks: 1000,
    cpc: 1000,
    ctr: 1000,
    conversions: 1000,
  }
])
const columns = ref<any>([
  { title: '优化师', dataIndex: 'user',slotName:'user',width:250,fixed: 'left', },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '创建广告组数', dataIndex: 'adGroupCount',width:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '创建广告数', dataIndex: 'adCount',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '创建流水', dataIndex: 'transactionFlow', sortable: {sortDirections: ['ascend', 'descend']} }
])

// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const getList = () => {}
const init = () => {
}

// 分页
const pageChange = (v) => {
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>