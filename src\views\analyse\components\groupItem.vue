

<template>
    <!-- 分组项 -->
    <div class="guide">
        <div class="stickyBar" :style="groupList.length>0? {} : { color: 'var(--tant-text-gray-color-text1-4)' }">
            <!-- <div class="modal" :style="condition || eventDataList.length >1 ? { width: 'calc(100% - 42px)' } : {}"> -->
            <div class="modal">
                <!-- <img src="/icon/topMenu/event.svg"/> -->
                <icon-apps class="model-icon"/>
                <span class="title">分组项</span>
                <div v-if="(props.onlyOne && groupList.length != 1) || !props.onlyOne" class="model-btn">
                    <a-button @click="addItem">
                        <template #icon>
                            <icon-plus class="nav-icon"/>
                        </template>
                    </a-button>
                </div>
            </div>
            <!-- <a-tooltip v-if="!condition && eventDataList.length>1" position="top">
                <template #content>
                    <div>开始事件拆分</div>
                    <div style="color: #ccc;">针对指定事件按事件属性作为分组项进行计算，不影响未选择的事件</div>
                </template>
                <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="() => condition = !condition">
                    <template #icon>
                        <icon-branch />
                    </template>
                </a-button>
            </a-tooltip>
            <a-tooltip v-if="condition && eventDataList.length>1" position="top">
                <template #content>
                    <div>取消事件拆分</div>
                </template>
                <a-button style="width: 36px;height: 40px;background-color: var(--tant-secondary-color-secondary-transp-hover);color: var(--tant-primary-color-primary-default)" @click="() => condition = !condition">
                    <template #icon>
                        <icon-branch />
                    </template>
                </a-button>
            </a-tooltip> -->
        </div>
        <!-- <div v-if="condition" class="branch-container">
            <div class="branch-header">
                <div class="branch-icon btn-26">
                    <icon-branch />
                </div>
                <a-select v-model:modelValue="branchData.type" :style="{width:'56px'}" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option style="width: 100px;">首先</a-option>
                    <a-option>最后</a-option>
                </a-select>
                <span class="word">对事件</span>
                <div style="position: relative">
                    <a-select v-model:modelValue="branchData.eventSelectList" style="min-width:80px;max-width:200px;" multiple :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <template #label="{ data }">
                            <span><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path><path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path></svg></svg>{{data?.label}}</span>
                        </template>
                        <a-option v-for="(el,index) in eventDataList" :key="index" style="width: 200px;">{{ el }}</a-option>
                    </a-select>
                </div>
                <span class="word">进行拆分计算</span>
            </div>
            <div class="branch-item">
                <em class="line"></em>
                <div>
                    <div v-for="(item,index) in branchData.branchList" :key="index" class="item-list">
                        <div class="sub-action-row">
                            <div class="sub-action-left">
                                <i class="drag-index"><icon-branch />{{ index + 1 }}</i>
                                <icon-drag-dot-vertical class="hover-drag"/>
                                <tabsSelect :only-event="true"/>
                            </div>
                            <div v-if="branchData.branchList && branchData.branchList.length>1" class="sub-action-right">
                                <a-space align="center">
                                    <a-tooltip content="删除" position="top">
                                        <a-button class="btn-bg-delete btn-26" @click="deleteBranch(item.id)">
                                            <template #icon>
                                                <icon-close-circle />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row-foot">
                <div class="ta-filter-button" @click="addBranch">
                    <icon-plus class="action"/>
                    <span class="label">拆分项</span>
                </div>
            </div>
        </div> -->
        <div class="event-filter-box">
            <draggable :list="groupList" item-key="objectId" handle=".hover-drag">
                <template #item="{ element,index }">
                    <div class="action-row">
                        <div class="filter-row-eventRow">
                            <div class="action-left">
                                <i class="drag-index">{{ index + 1 }}</i>
                                <icon-drag-dot-vertical class="hover-drag"/>
                                <div class="row-content">
                                    <div class="event-item">
                                        <attrEnumSelect :info="element" :disabled-list="groupList" :code-list="codeList" @tabs-change="tabsChange(index,$event)"/>
                                        <a-trigger v-if="element?.objectType == 'date' || element?.objectType == 'datetime'" v-model:popup-visible="triggerVisible" trigger="click"  show-arrow position="tl" :update-at-scroll="true">
                                            <div class="set-icon">
                                                <icon-settings />
                                            </div>
                                            <template #content>
                                                <div class="range-block">
                                                    <div class="range-title">分组方式</div>
                                                        <div class="range-content">
                                                            <div class="inline">
                                                                <a-radio-group v-model:model-value="element.isSum" direction="vertical" @change="sumChange(index,$event)">
                                                                    <a-radio :value="1">汇总</a-radio>
                                                                    <a-radio :value="0">不汇总</a-radio>
                                                                </a-radio-group>
                                                            </div>
                                                            <div class="inline" style="margin-left: 20px;">
                                                                <a-cascader v-model:model-value="element.timeSummaryType" :options="options" :disabled="!element.isSum" style="min-width: 80px;max-width:160px;" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" />
                                                                <div style="line-height: 32px;color: #ccc;">默认按天</div>
                                                            </div>
                                                        </div>
                                                    <div class="range-footer">
                                                        <a-button type="primary" @click="cancelTime(index)">取消</a-button>
                                                        <a-button type="primary" @click="() => triggerVisible = false">应用</a-button>
                                                    </div>
                                                </div>
                                            </template>
                                        </a-trigger>
                                        <numberGroupSet v-if="element?.objectType == 'int' || element?.objectType == 'float'" :set-data="element" @number-set="numberSet(index,$event)"/>
                                        <div class="action-right">
                                            <a-tooltip content="移除" position="top">
                                                <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteItem(index)">
                                                    <template #icon>
                                                        <icon-close-circle />
                                                    </template>
                                                </a-button>
                                            </a-tooltip>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </draggable>
        </div>
    </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import draggable from 'vuedraggable'
import numberGroupSet from "@/views/analyse/components/numberGroupSet.vue"
import {cloneDeep} from "lodash";
import {toolStore} from '@/store';
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";


const toolData = toolStore();
const props = defineProps({
    groupItem:{
        type:Array,
        default: () => []
    },
    onlyOne:{
        type:Boolean,
        default: false
    }
})
const emits = defineEmits(['aggregatesChange'])
const eventBus = useEventBus('eventList');
const groupList = ref<any>([])
const aggregates = ref<any>([])
const condition = ref(false)
const triggerVisible = ref(false)


// 拆分
const eventDataList =ref<any>([])

const branchData = ref({
    type:'首先',
    eventSelectList:[],
    branchList:[
        {
            id:'01',
            name:'城市——账户'
        }
    ]
})
const options = ref([
  {
    value:'D1',
    label:'按天'
  },{
  value: 'm1',
  label: '按分钟'
  },{
    value:'h1',
    label:'按小时'
  },{
    value:'W1',
    label:'按周'
  },{
    value:'M1',
    label:'按月'
  },{
    value:'more',
    label:'更多',
    children:[
      {
        value:'Q1',
        label:'按季'
      },{
        value:'Y1',
        label:'按年'
      },
    ]
  }
])
const codeList = ref<any>([])
onMounted(async () => {
    eventBus.on((event: any) => {
        codeList.value = event
    })
})
const tabsChange = (index,v) => {
    const {objectName,objectDisplayName,objectType,filterType,objectId} = v
    const filter = { ...groupList.value[index] };
    filter.objectName = objectName;
    filter.objectDisplayName = objectDisplayName;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    if (filter.objectType === 'date' || filter.objectType === 'datetime') {
        filter.isSum= filter?.isSum ? filter.isSum : 0
        filter.timeSummaryType=  filter?.timeSummaryType ? filter.timeSummaryType : 'D1'
    } else if (filter.objectType === 'float' || filter.objectType === 'int') {
        filter.numberSummaryType= filter?.numberSummaryType ? filter.numberSummaryType: 'st12'
        filter.numberSummaryScope= filter?.numberSummaryScope ? filter.numberSummaryScope: [0]
    }else{
        delete filter?.isSum;
        delete filter?.timeSummaryType;
        delete filter?.numberSummaryType;
        delete filter?.numberSummaryScope;
    }
    groupList.value[index] = filter;
}
const deleteItem = (index:number) => {
    groupList.value.splice(index,1)
}


const numberSet = (index:number,e:any) => {
    groupList.value[index].numberSummaryType = e.numberSummaryType
    groupList.value[index].numberSummaryScope = e.numberSummaryScope
}
const sumChange = (index:number,v) => {
    if(v === 0) {
        groupList.value[index].timeSummaryType = 'D1'
    }else{
        groupList.value[index].timeSummaryType = 'h1'
    }
}
const cancelTime = (index:number) => {
    groupList.value[index].timeSummaryType = 'D1'
    groupList.value[index].isSum = 0
    triggerVisible.value = false
}
// 添加处理数据
const addItem = async () => {
    // 遍历 dataList.value，查找第一条未被添加的项
    const flatList = handleAttrFlatData(toolData.allAttrList, codeList.value);
    const addList = flatList.filter(category => category.itemData?.length > 0).flatMap(category => category.itemData || []);
    for (let i = 0; i < addList.length; i++) {
        const item = addList[i];
        const exists = groupList.value.some(groupItem => groupItem.objectId === item.code);
        if (!exists) { // 如果该项未被添加
            if (item.dataType === 'date' || item.dataType === 'datetime') {
                groupList.value.push({
                    ...item,
                    objectId: item.code,
                    objectName: item.name,
                    objectDisplayName: item.displayName,
                    objectType: item.dataType,
                    filterType: item.attributeType,
                    isSum: 0,
                    timeSummaryType: 'D1'
                });
            } else if (item.dataType === 'float' || item.dataType === 'int') {
                groupList.value.push({
                    ...item,
                    objectId: item.code,
                    objectName: item.name,
                    objectDisplayName: item.displayName,
                    objectType: item.dataType,
                    filterType: item.attributeType,
                    openBatch: false,
                    stepSize: 50,
                    stepNum: 1,
                    numberSummaryType: 'st12',
                    numberSummaryScope: [0]
                });
            } else {
                groupList.value.push({
                    ...item,
                    objectId: item.code,
                    objectName: item.name,
                    objectDisplayName: item.displayName,
                    objectType: item.dataType,
                    filterType: item.attributeType,
                });
            }
            // 一旦添加了第一条未被添加的项，退出循环
            break;
        }
    }
}
watch(() => props.groupItem, (newVal) => {
    groupList.value = cloneDeep(props.groupItem).map(item => {
      return {
        ...item,
        filterType: item.aggregateType,
      }
    })
}, { immediate: true, deep: true })
watch(groupList.value, (newValue, oldValue) => {
    aggregates.value = groupList.value.map(item => {
        const aggregate = {};
        aggregate.aggregateType = item.filterType;
        if (item.objectName) aggregate.objectName = item.objectName;
        if (item.objectDisplayName) aggregate.objectDisplayName = item.objectDisplayName;
        if (item.objectId) aggregate.objectId = item.objectId;
        if (item.objectType) aggregate.objectType = item.objectType;
        if (item.numberSummaryType) aggregate.numberSummaryType = item.numberSummaryType;
        if (item.numberSummaryScope) {
            aggregate.numberSummaryScope = item.numberSummaryScope.map(scopeItem => scopeItem);
        }
        if (item.timeSummaryType) aggregate.timeSummaryType = item.timeSummaryType;
        if (item.timeSummaryType) aggregate.isSum = item.isSum;
        return aggregate;
        // return{
        //     aggregateType:item.filterType,
        //     objectName:item.name,
        //     objectId:item.objectId,
        //     objectType:item.objectType,
        //     numberSummaryType:item.numberSummaryType,
        //     numberSummaryScope:item.numberSummaryScope?.map(item => item),
        //     timeSummaryType:item.timeSummaryType
        // }
    })
    emits('aggregatesChange',aggregates.value)
},{immediate: true,deep:true})
</script>

<style scoped lang="less">
.filter-btn{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon{
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .model-icon{
                margin-right: 8px;
            }
            .title{
                flex-grow: 1;
                font-weight: 600;
                max-width: calc(100% - 48px);
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .drag-index{
                    margin-top: 5px;
                    margin-right: 12px;
                    flex-shrink: 0;
                    width: 24px;
                    height: 24px;
                    color: var(--tant-text-gray-color-text1-3);
                    background-color: var(--tant-disabled-color-disabled-fill);
                    font-size: 12px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    transition: all .3s;
                    opacity: 1;
                }
                .hover-drag{
                    position: absolute;
                    left: 5px;
                    margin-top: 9px;
                    margin-left: 24px;
                    flex-shrink: 0;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    opacity: 0;
                    transition: all .3s;
                    cursor: grab;
                }
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .event-item{
                        display: flex;
                        align-items: center;
                    }
                    .action-right {
                        display: flex;
                        align-items: center;
                        opacity: 0;
                        transition: opacity .3s;
                    }
                }
            }
            .set-icon{
                font-size: 16px;
                height: 26px;
                width: 26px;
                text-align: center;
                line-height: 26px;
                border-radius: 4px;
                cursor: pointer;
                color: var(--tant-text-gray-color-text1-3);
                transition: all .3s;
                border: 1px solid transparent;
                background-color: var(--tant-secondary-color-secondary-fill);
                margin-left: 8px;
                &:hover{
                    border: 1px solid var(--tant-primary-color-primary-default)
                }
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .set-icon{
                background: #fff;
            }
            .action-left .drag-index{
                opacity: 0;
                transition: all .3s;
            }
            .action-left .hover-drag{
                opacity: 1;
                transition: all .3s;
            }
            .action-left .row-content :deep(.filter-btn){
                background: #fff;
            }
            :deep(.select-btn) {
                background: #fff;
            }
            .row-content .action-right{
                opacity: 1;
            } 
        }
        }
        }
       
}
.branch-container{
    display: block !important;
    margin: 0;
    padding: 0 24px 6px 24px;
    font-size: 14px;
    border-radius: 4px;
    .branch-header{
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 8px 4px 8px 0;
        .branch-icon{
            background-color: var(--tant-secondary-color-secondary-transp-hover);
            margin-right: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .word{
            margin: 0 8px;
            color: var(--tant-text-gray-color-text1-3);
            font-size: 14px;
        }
    }
    .branch-item{
        position: relative;
        margin: 0 40px;
        .line{
            position: absolute;
            top: 6px;
            bottom: 6px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
        }
        .item-list{
            min-height: 26px;
            padding: 0 18px;
            .sub-action-row{
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;
                .sub-action-left{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    position: relative;
                    .drag-index{
                        flex-shrink: 0;
                        width: 37px;
                        height: 24px;
                        margin-right: 8px;
                        margin-top: 4px;
                        color: var(--tant-text-gray-color-text1-3);
                        background-color: var(--tant-disabled-color-disabled-fill);
                        font-size: 12px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        transition: all .3s;
                        opacity: 1;
                    }
                    .hover-drag{
                        position: absolute;
                        flex-shrink: 0;
                        font-size: 16px;
                        left: 8px;
                        top: 6px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        opacity: 0;
                        transition: all .3s;
                    }
                }
                .sub-action-right{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    display: flex;
                    align-items: center;
                    opacity: 0;
                    transition: opacity .3s;
                    margin-left: 8px;
                }
                &:hover .sub-action-right{
                    opacity: 1;
                }
                &:hover .drag-index{
                    opacity: 0;
                }
                &:hover .hover-drag{
                    opacity: 1;
                }
            }
        }
        
    }
    .row-foot{
        margin: 0;
        padding:0 24px;
        transition: all .3s;
        .ta-filter-button{
            padding: 6px;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            transition: all .3s;
            .action{
                border-radius: 4px;
                background-color: var(--tant-primary-color-primary-fill);
                color: var(--tant-primary-color-primary-default);
                margin-right: 8px;
                padding: 3px;
                font-size: 18px;
            }
            .label{
                color: var(--tant-primary-color-primary-default);
            }
            &:hover .action{
                background-color: var(--tant-primary-color-primary-fill-hover);
                color: var(--tant-primary-color-primary-hover);
            }
        }
    }
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        
        .sub-action-left :deep(.filter-btn){
            background: #fff;
        }
    }
}
.range-block{
    position: relative;
    padding: 16px;
    background: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-overall);
    .range-title{
        margin-bottom: 5px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 600;
        font-size: 14px;
    }
    .range-content{
        width: 268px;
        margin-top: 14px;
        display: flex;
        align-items: flex-start;
    }
    .inline{
        display: inline-block;
    }
    .range-footer{
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
        button{
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
    .custom-range{
        position: absolute;
        bottom: 0;
        right: -230px;
        width: 230px;
        background-color: #fff;
        box-shadow: var(--tant-medium-shadow-medium-bottom);
        .custom-range-set-top{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 30px;
            padding: 0 8px;
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 500;
            font-size: 14px;
            .custom-range-set-act{
                color: var(--widget-color);
                font-weight: 400;
                font-size: 12px;
                cursor: pointer;
                -webkit-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }
        }
        .custom-range-set-all{
            max-height: 240px;
            padding: 8px;
            overflow-y: auto;
            .custom-range-set-range{
                width: 100%;
                padding: 4px;
                .custom-range-set-symbol {
                    padding: 0 4px;
                }
                .custom-range-set-static {
                    display: inline-block;
                    width: 70px;
                    height: 24px;
                    color: var(--tant-text-gray-color-text1-4);
                    // line-height: 24px;
                    text-align: center;
                    background-color: var(--light-bg-color);
                    border: 1px solid var(--tant-border-color-border1-1);
                }
                .input-number{
                    display: inline-block;
                    width: 70px;
                    height: 24px;
                }
                .custom-range-set-del{
                    opacity: 0;
                    display: inline-block;
                    width: 14px;
                    margin-left: 4px;
                    font-size: 14px;
                    cursor: pointer;
                    &:hover{
                        color: var(--tant-status-danger-color-danger-default);
                    }
                }
                &:hover .custom-range-set-del{
                    opacity: 1;
                }
            }
        }
        .custom-range-set-adds {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 12px;
            font-size: 12px;
            .custom-range-set-add {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 32px;
                color: var(--widget-color);
                cursor: pointer;
                &:hover{
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
        .custom-range-set-batch{
            width: 100%;
            padding: 12px 8px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            background-color: var(--normal-bg-color);
            border-top: 1px solid var(--tant-border-color-border1-1);
            .input-number{
                display: inline-block;
                width: 56px;
                margin: 0 4px;
            }
            .custom-range-set-btn{
                display: inline-block;
                margin-left: 8px;
                color: var(--widget-color);
                font-size: 14px;
                cursor: pointer;
                &:hover{
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}

.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>