<template>
  <a-modal
    v-model:visible="visible"
    title-align="start"
    :title="formData.code ? '编辑维表属性' : '新增维表属性'"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
      <a-form-item field="name" label="属性字段" :rules="[{ required: true, message: '请输入属性字段' }]">
        <a-input v-model="formData.name" placeholder="请输入属性字段" :disabled="!!formData?.code"/>
      </a-form-item>
      <a-form-item field="displayName" label="显示名" :rules="[{ required: true, message: '请输入显示名' }]">
        <a-input v-model="formData.displayName" placeholder="请输入显示名" />
      </a-form-item>
      <a-form-item field="dataType" label="数据类型" :rules="[{ required: true, message: '请选择数据类型' }]">
        <a-select v-model="formData.dataType" placeholder="请选择数据类型" :disabled="!!formData?.code">
          <a-option value="string">文本</a-option>
          <a-option value="boolean">布尔</a-option>
          <a-option value="date">日期</a-option>
          <a-option value="datetime">时间</a-option>
          <a-option value="array">列表</a-option>
          <a-option value="variant">对象</a-option>
          <a-option value="int">整数</a-option>
          <a-option value="float">小数</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="description" label="备注说明">
        <a-textarea
          v-model="formData.description"
          placeholder="请输入备注说明"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleOk">保存</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from 'vue';
import {Message} from '@arco-design/web-vue';
import {saveDimensionAttr} from "@/api/setting/api";
import {useRoute} from 'vue-router';

const route = useRoute();
const tableCode = ref(route.query.tableCode as string);
const visible = ref(false);
const formRef = ref();

const formData = reactive({
  code:'',
  name: '',
  displayName: '',
  dataType: '',
  description: '',
});

const rules = {
  // code: [{ required: true, message: '请输入属性字段' }],
  // name: [{ required: true, message: '请输入显示名' }],
  // dataType: [{ required: true, message: '请选择数据类型' }]
};

const emit = defineEmits(['success']);

const openModal = (record?: any) => {
  visible.value = true;
  if (record) {
    Object.assign(formData, record);
  } else {
    Object.keys(formData).forEach(key => {
      formData[key] = '';
    });
  }
};

const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

const loading = ref(false);
const handleOk = async () => {
  formRef.value.validate(async (valid:any) => {
    if (!valid) {
      loading.value = false;
      try {
        const data = {
          dimTable: tableCode.value,
          ...formData
        }
        await saveDimensionAttr(data);
        Message.success('保存成功');
        emit('success');
        visible.value = false;
      } catch (error) {
        Message.error('保存失败');
      } finally {
        loading.value = false;
      }
    }
  })
  
};

defineExpose({
  openModal
});
</script>

<style scoped lang="less">
</style>