
<template>
    <a-modal v-model:visible="modalVisible" :width="560" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="adChannel" label="选择变现平台" validate-trigger="change">
                <a-select v-model:model-value="form.adChannel" placeholder="请选择变现平台" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in props.sourceList" :key="item.code" :value="item.name">{{item.name}}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="adLabel" label="选择广告类型" validate-trigger="change">
                <a-select v-model:model-value="form.adLabel" placeholder="请选择广告类型" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in props.adFormatList" :key="item.code" :value="item.name">{{item.name}}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="adName" label="广告名称" validate-trigger="blur">
              <a-input v-model="form.adName" placeholder="请输入广告名称"/>
            </a-form-item>
            <a-form-item field="adId" label="广告ID" validate-trigger="blur">
              <a-input v-model="form.adId" placeholder="请输入广告ID" :disabled="modalTitle === '编辑广告映射'"/>
            </a-form-item>
            <a-form-item field="adStatus" label="选择广告状态" validate-trigger="change">
                <a-select v-model:model-value="form.adStatus" placeholder="请选择广告状态" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option :value="1">在线</a-option>
                    <a-option :value="0">弃用</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="cpViewEnabled" label="CP授权" validate-trigger="change">
                <a-select v-model:model-value="form.cpViewEnabled" placeholder="请选择CP授权" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option :value="1">是</a-option>
                    <a-option :value="0">否</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="cpViewRate" label="CP授权比例" validate-trigger="blur">
              <a-input-number v-model="form.cpViewRate" :min="0"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addAdItem, updateAdItem,} from "@/api/marketing/api";

const props = defineProps({
    sourceList:{
        type:Array,
        default: () => [],
    },
    adFormatList:{
        type:Array,
        default: () => [],
    }
})
const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    adLabel:'',
    adChannel:'',
    adName:'',
    adId:'',
    adStatus:0,
    cpViewEnabled:0,
    cpViewRate:undefined
})
const rules = {
    adChannel: [
        {
            required: true,
            message:'请选择变现平台'
        }
    ],
    adLabel: [
        {
            required: true,
            message:'请选择广告类型'
        }
    ],
    adName: [
        {
            required: true,
            message:'请输入广告名称'
        }
    ],
    adId: [
        {
            required: true,
            message:'请输入广告ID'
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);

const adItemId = ref('')
const openModal = async (type:string,record?:object) => {
    modalTitle.value = type === 'edit' ? '编辑广告映射' : '添加广告映射'
    if(type === 'edit'){
        form.adLabel=record?.adLabel
        form.adChannel=record?.adChannel
        form.adName=record?.adName
        form.adId=record?.adId
        form.adStatus=record?.adStatus
        form.cpViewEnabled=record?.cpViewEnabled
        form.cpViewRate=record?.cpViewRate
        console.log(record,'form');
        adItemId.value = record?.id
    }else{
        formRef.value.resetFields()
        formRef.value.clearValidate()
    }
    
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    const appId = sessionStorage.getItem('app-id') || ''
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try{
                if(modalTitle.value === '编辑广告映射'){
                    updateAdItem(adItemId.value,form).then(res => {
                        Message.success('修改成功')
                        modalVisible.value = false
                        emits('updateData')
                    })
                }else{
                    addAdItem(appId,form).then(res => {
                        Message.success('创建成功')
                        modalVisible.value = false
                        emits('updateData')
                    })
                }
                
            } catch (error) {
                console.log(error);
            } finally{
                loading.value = false
            }
        
        }else{
            // Message.warning('请填写必要数据')
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>