<!-- 账号设置 -->
<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
    </div>
    <div class="page-body">
      <div class="account-container">
        <!-- 基本信息 -->
        <div class="basic-info">
          <div class="section-title">基础信息</div>
          <div class="user-avatar">
            <a-avatar :size="64" class="avatar">
              <img v-if="avatar" :src="avatar" alt="avatar" />
              <span v-else class="avatar-text">{{ processedName }}</span>
            </a-avatar>
          </div>
          <!-- 用户名显示/编辑 -->
          <div v-if="!isEditingName" class="user-name-display">
            <div class="user-name">{{ userStore.nickname }}</div>
            <icon-edit class="edit-icon" @click="startEditName" />
          </div>
          <div v-else class="user-name-edit">
            <a-input ref="nameInput" v-model="editableUserInfo.displayName" class="editable-input" :placeholder="userStore.name" @blur="finishEditName" @keyup.enter="() => (isEditingName = false)" />
          </div>
          <div class="user-id">{{ userStore.name }}</div>
          <div class="system-version">
            <div class="version-label">系统版本号</div>
            <div class="version-number">1.0.0</div>
          </div>
        </div>
        <a-divider direction="vertical" />
        <!-- 账号安全 -->
        <div class="security-settings">
          <div class="section-title">账号安全</div>
          <div class="security-grid">
            <!-- 手机号设置 -->
            <div class="security-card">
              <div class="card-header">
                <icon-mobile class="card-icon" />
                <div class="card-title">
                  <span class="title">手机号</span>
                  <a-tag v-if="!userStore.mobile" color="orange">未绑定</a-tag>
                  <a-tag v-else color="green">已绑定</a-tag>
                </div>
              </div>
              <div v-if="userStore.mobile" class="card-desc">{{ maskedPhone }}可通过手机号接收消息</div>
              <div v-else class="card-desc">绑定后，可通过手机号接收消息</div>
              <a-button v-if="userStore.mobile" @click="unBindPhone"> 解绑 </a-button>
              <a-button v-else type="primary" @click="handlePhoneBinding"> 绑定 </a-button>
            </div>
            <!-- 邮箱设置 -->
            <div class="security-card">
              <div class="card-header">
                <icon-email class="card-icon" />
                <div class="card-title">
                  <span class="title">邮箱</span>
                  <a-tag v-if="!userStore.email" color="orange">未绑定</a-tag>
                  <a-tag v-else color="green">已绑定</a-tag>
                </div>
              </div>
              <div v-if="userStore.email" class="card-desc">{{ userStore.email }}可通过邮箱接收消息</div>
              <div v-else class="card-desc">绑定后，可通过邮箱接收消息</div>
              <a-button v-if="userStore.email" @click="unBindEmail"> 解绑 </a-button>
              <a-button v-else type="primary" @click="handleEmailBinding"> 绑定 </a-button>
            </div>
            <!-- 密码设置 -->
            <div class="security-card">
              <div class="card-header">
                <icon-lock class="card-icon" />
                <div class="card-title">
                  <span class="title">密码</span>
                </div>
              </div>
              <div class="card-desc">********</div>
              <a-button @click="handlePasswordChange">修改</a-button>
            </div>
          </div>
        </div>
      </div>
      <!-- 手机号绑定弹窗 -->
      <PhoneBindModal v-model:visible="phoneModalVisible" :code="userStore.code" @refresh="handleRefresh" />
      <!-- 邮箱绑定弹窗 -->
      <EmailBindModal v-model:visible="emailModalVisible" :code="userStore.code" @refresh="handleRefresh" />
      <!-- 修改密码弹窗 -->
      <PasswordChangeModal v-model:visible="passwordModalVisible" :code="userStore.code" @refresh="handleRefresh" />
      <!-- 解绑弹窗 -->
      <a-modal :visible="unBindVisible" width="350px" :title="unBindTitle" title-align="start" @ok="unBindOk" @cancel="unBindCancel">
        <div>{{ unBindContent }}</div>
      </a-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, reactive, nextTick, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@/store';
  import { saveSystemMember } from '@/api/setting/api';
  import PhoneBindModal from './components/PhoneBindModal.vue';
  import EmailBindModal from './components/EmailBindModal.vue';
  import PasswordChangeModal from './components/PasswordChangeModal.vue';

  const route = useRoute();
  const userStore = useUserStore();
  const avatar = computed(() => {
    return userStore.avatar;
  });
  const processedName = computed(() => {
    if (!userStore.name) return '';
    // 判断开头是汉字
    const startsWithChinese = /^[\u4e00-\u9fa5]/.test(userStore.name);

    if (startsWithChinese) {
      return userStore.name.slice(-2); // 开头是汉字，取后两位
    }
    return userStore.name.slice(0, 3);
  });

  // 格式化手机号显示（前4位显示为*号）
  const maskedPhone = computed(() => {
    if (!userStore.mobile) return '';
    const phone = userStore.mobile;
    if (phone.length === 11) {
      return `****${phone.slice(4)}`;
    }
    return phone;
  });

  const editableUserInfo = reactive({
    displayName: '',
  });
  // 编辑名字相关
  const isEditingName = ref(false);
  const nameInput = ref();

  const unBindVisible = ref(false);
  const unBindTitle = ref('');
  const unBindContent = ref('');

  const startEditName = () => {
    isEditingName.value = true;
    nextTick(() => {
      nameInput.value?.focus();
    });
  };

  const finishEditName = async () => {
    try {
      await saveSystemMember({
        nickname: editableUserInfo.displayName,
        code: userStore.code,
      });
      Message.success('修改成功');
      isEditingName.value = false;
      userStore.info();
    } catch (error) {
      console.error(error);
    }
  };

  // 弹窗控制
  const phoneModalVisible = ref(false);
  const emailModalVisible = ref(false);
  const passwordModalVisible = ref(false);

  // 处理弹窗显示
  const handlePhoneBinding = () => {
    phoneModalVisible.value = true;
  };
  const unBindPhone = () => {
    unBindTitle.value = '解绑手机号';
    unBindContent.value = '解绑后无法通过手机接收消息。确认解绑吗？';
    unBindVisible.value = true;
  };
  const handleEmailBinding = () => {
    emailModalVisible.value = true;
  };
  const unBindEmail = () => {
    unBindTitle.value = '解绑邮箱';
    unBindContent.value = '解绑后无法通过邮箱接收消息。确认解绑吗？';
    unBindVisible.value = true;
  };
  const handlePasswordChange = () => {
    passwordModalVisible.value = true;
  };

  // 刷新用户信息回调
  const handleRefresh = () => {
    userStore.info();
  };

  const unBindOk = () => {
    if (unBindTitle.value === '解绑手机号') {
      saveSystemMember({
        mobile: '',
        code: userStore.code,
      }).then(() => {
        Message.success('解绑成功');
        userStore.info();
        unBindVisible.value = false;
      });
    }
    if (unBindTitle.value === '解绑邮箱') {
      saveSystemMember({
        email: '',
        code: userStore.code,
      }).then(() => {
        Message.success('解绑成功');
        userStore.info();
        unBindVisible.value = false;
      });
    }
  };
  const unBindCancel = () => {
    unBindVisible.value = false;
  };
  onMounted(() => {
    editableUserInfo.displayName = userStore.nickname || '';
  });
</script>

<style scoped lang="less">
  .account-container {
    display: flex;
    gap: 40px;
    max-width: 1200px;
    height: 100%;
  }

  .basic-info {
    flex: 0 0 300px;
    padding: 30px;
    text-align: center;
    height: 100%;
  }

  .security-settings {
    flex: 1;
    padding: 30px;
    background: #fff;
  }

  .section-title {
    font-size: 18px;
    //   font-weight: 600;
    color: #1d2129;
    margin-bottom: 24px;
    text-align: left;
  }

  .basic-info .section-title {
    text-align: left;
  }

  .user-avatar {
    margin-bottom: 16px;

    .avatar {
      background: #4a90e2;

      .avatar-text {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
      }
    }
  }

  // 用户名显示/编辑样式
  .user-name-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;

    .user-name {
      font-size: 16px;
      // font-weight: 600;
      color: #1d2129;
    }

    .edit-icon {
      font-size: 16px;
      color: #86909c;
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #4a90e2;
      }
    }
  }

  .user-name-edit {
    margin-bottom: 8px;

    .editable-input {
      text-align: center;
    }
  }

  .user-id {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 8px;
  }

  .system-version {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e5e6eb;

    .version-label {
      font-size: 14px;
      color: #86909c;
      margin-bottom: 8px;
    }

    .version-number {
      font-size: 16px;
      color: #1d2129;
    }
  }

  .security-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .security-card {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }

  .card-icon {
    font-size: 20px;
    color: #4a90e2;
    margin-top: 2px;
  }

  .card-title {
    flex: 1;
    display: flex;
    align-items: center;

    .title {
      flex-shrink: 0;
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-right: 8px;
    }
  }

  .card-desc {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 16px;
    line-height: 1.5;
    word-break: normal;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  // 响应式设计
  @media (max-width: 768px) {
    .account-container {
      flex-direction: column;
      gap: 20px;
    }

    .basic-info {
      flex: none;
    }

    .security-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 1024px) {
    .security-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
