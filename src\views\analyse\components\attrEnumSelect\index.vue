<template>
  <div>
    <!-- 新事件指标下拉筛选 -->
    <div class="screen-body">
      <a-trigger
          v-model:popup-visible="triggerVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          :update-at-scroll="true"
          style="z-index: 1002;"
          @click="handleTriggerVisible"
      >
        <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
          <div v-if="!props.disabled" class="select-btn">
            <IconFont
                v-if="filterData.filterType"
                :type="iconTypeMap[filterData.filterType]"
                :size="14"
                class="btn-icon"
            />
            <span class="btn-label">{{ filterData.objectName ? filterData.objectName + '-' : '' }}{{ filterData.objectDisplayName }}</span>
          </div>
          <template #content>
            <div class="trigger-box">
              <div class="card-header">
                <div class="card-header-container">
                  <div class="header-icon">
                    <IconFont
                        v-if="filterData.filterType"
                        :type="iconTypeMap[filterData.filterType]"
                        :size="14"
                        class="btn-icon"
                    />
                  </div>
                  <div class="header-title">
                    <div class="name">{{ filterData.objectDisplayName }}</div>
                  </div>
                  <div class="header-type">{{ getAttrName(filterData) }}</div>
                </div>
                <div class="title-sub">{{ filterData.objectName }}</div>
              </div>
              <div class="card-desc">
                <div v-if="filterData.note" class="span-desc">{{ filterData.note }}</div>
                <div v-else class="span-desc">暂无备注</div>
              </div>
              <div class="card-footer">
                <div v-if="filterData.filterType !== 'cluster'">
                  {{ getLabel(filterData.objectType) }}
                </div>
                <div class="action">
                  <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                  <a-tooltip content="前往属性详情" position="top">
                    <div class="action-icon" @click="toAttrDetail(filterData)">
                      <icon-launch/>
                    </div>
                  </a-tooltip>
                </div>
              </div>
            </div>
          </template>
        </a-trigger>
        <template #content>
          <div class="select-panel">
            <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
              <a-input ref="searchInputRef" v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                <template #prefix>
                  <icon-search/>
                </template>
              </a-input>
            </div>
            <div v-if="searchInput === ''" class="list-container">
              <div class="list-category">
                <div class="category-container">
                  <div
                      v-for="(item, index) in originList"
                      :key="index"
                      class="category-item"
                      :class="{ 'item-active': activeIndex === index,}"
                      @click="categoryChange(item.categoryName,index)">
                    {{ item.categoryName }}
                  </div>
                </div>
                <!-- <a-button>管理分组</a-button> -->
              </div>
              <div class="item-list">
                <div ref="selectListRef" class="select-list">
                  <a-list ref="virtualListRef" :virtual-list-props="{ height: 350, buffer: 10 }" :data="virtualItemList" :scrollbar="false" @scroll="handleScroll">
                    <template #item="{ item, index }">
                      <a-list-item :key="`panel-${item.objectId || item?.categoryName}-${index}`">
                        <div class="list-content">
                          <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                            <!-- <icon-star-fill v-if="index == 0" class="star"/> -->
                            <span>{{ item.categoryName }}</span>
                          </div>
                          <div v-if="index == 0 && !originList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                            <!-- 点击选项右<icon-star/>侧添加为常用 -->
                            暂无数据
                          </div>
                          <div
                              v-if="item.objectDisplayName"
                              class="list-box"
                              :class="{
                                'list-active': item.objectId === filterData.objectId || chooseLists.includes(item.objectId),
                                'list-disabled': chooseLists.includes(item.objectId)
                              }"
                              style="height: 30px; width: calc(100% - 10px)"
                              @click="!chooseLists.includes(item.objectId) && listChange(item)">
                            <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                              <div>
                                <IconFont
                                    v-if="item.filterType"
                                    :type="iconTypeMap[item.filterType]"
                                    :size="14"
                                    class="btn-icon"
                                    style="margin-right:4px"
                                />
                                <span class="desc">{{ item.objectName ? item.objectName + '-' : '' }}{{ item.objectDisplayName }}</span>
                                <a-tooltip v-if="!item.isFavorite" content="点击收藏" position="top">
                                  <div class="icon" @click.stop="starClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                                <a-tooltip v-else content="取消收藏" position="top">
                                  <div class="isStar" @click.stop="cancelClick(item)">
                                    <icon-star-fill/>
                                  </div>
                                </a-tooltip>
                              </div>
                              <template #content>
                                <div class="trigger-box">
                                  <div class="card-header">
                                    <div class="card-header-container">
                                      <div class="header-icon">
                                        <IconFont
                                            v-if="item.filterType"
                                            :type="iconTypeMap[item.filterType]"
                                            :size="14"
                                            class="btn-icon"
                                        />
                                      </div>
                                      <div class="header-title">
                                        <div class="name">{{ item.objectDisplayName }}</div>
                                      </div>
                                      <div class="header-type">{{ getAttrName(item) }}</div>
                                    </div>
                                    <div class="title-sub">{{ item.objectName }}</div>
                                  </div>
                                  <div class="card-desc">
                                    <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                    <div v-else class="span-desc">暂无备注</div>
                                  </div>
                                  <div class="card-footer">
                                    <div v-if="item.filterType !== 'cluster'">
                                      {{ getLabel(item.objectType) }}
                                    </div>
                                    <div v-else></div>
                                    <div class="action">
                                      <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                      <a-tooltip content="前往属性详情" position="top">
                                        <div class="action-icon" @click="toAttrDetail(item)">
                                          <icon-launch/>
                                        </div>
                                      </a-tooltip>
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                        </div>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
              </div>
            </div>
            <div v-else class="list-container" style="padding-top: 16px;">
              <div class="item-list">
                <div class="list-metric">
                  <a-list :virtual-list-props="{ height: 350 }" :data="filteredLists">
                    <template #item="{ item, index }">
                      <a-list-item :key="index">
                        <div class="list-content">
                          <div v-if="item.typeName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                            <icon-star-fill v-if="item.typeName.includes('收藏')" class="star"/>
                            {{ item.typeName }}
                          </div>
                          <div
                              v-if="item.objectDisplayName"
                              class="list-box"
                              :class="{
                                'list-active': item.objectId === filterData.objectId || chooseLists.includes(item.objectId),
                                'list-disabled': chooseLists.includes(item.objectId)
                              }"
                              style="height: 30px; width: calc(100% - 10px)"
                              @click="!chooseLists.includes(item.objectId) && itemChange(item)">
                            <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                              <div>
                                <IconFont
                                    v-if="item.filterType"
                                    :type="iconTypeMap[item.filterType]"
                                    :size="14"
                                    class="btn-icon"
                                    style="margin-right:4px"
                                />
                                <span class="desc1">{{ item.objectDisplayName }}</span>
                                <span class="desc2">{{ item?.objectName }}</span>
                                <a-tooltip v-if="!item.isFavorite" content="点击收藏" position="top">
                                  <div class="icon" @click.stop="itemClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                                <a-tooltip v-else content="取消收藏" position="top">
                                  <div class="isStar" @click.stop="cancelItemClick(item)">
                                    <icon-star-fill/>
                                  </div>
                                </a-tooltip>
                              </div>
                              <template #content>
                                <div class="trigger-box">
                                  <div class="card-header">
                                    <div class="card-header-container">
                                      <div class="header-icon">
                                        <IconFont
                                            v-if="item.filterType"
                                            :type="iconTypeMap[item.filterType]"
                                            :size="14"
                                            class="btn-icon"
                                        />
                                      </div>
                                      <div class="header-title">
                                        <div class="name">{{ item.objectDisplayName }}</div>
                                      </div>
                                      <div class="header-type">{{ getAttrName(item) }}</div>
                                    </div>
                                    <div class="title-sub">{{ item.objectName }}</div>
                                  </div>
                                  <div class="card-desc">
                                    <div v-if="item.note" class="span-desc">{{ item.note }}</div>
                                    <div v-else class="span-desc">暂无备注</div>
                                  </div>
                                  <div class="card-footer">
                                    <div v-if="item.filterType !== 'cluster'">
                                      {{ getLabel(item.objectType) }}
                                    </div>
                                    <div v-else></div>
                                    <div class="action">
                                      <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                      <a-tooltip content="前往属性详情" position="top">
                                        <div class="action-icon" @click="toAttrDetail(item)">
                                          <icon-launch/>
                                        </div>
                                      </a-tooltip>
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                        </div>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <div v-if="props.disabled">
        <div class="select-btn-disabled">
          <IconFont
              v-if="filterData.filterType"
              :type="iconTypeMap[filterData.filterType]"
              :size="14"
              class="btn-icon"
          />
          <span class="btn-label">{{ filterData.objectName }}-{{ filterData.objectDisplayName }}</span>
        </div>
      </div>
      <!-- 计算公式，枚举值选择器 -->
      <FormulaValuesSelect
          v-if="props.showDetailFilter"
          :info="filterData"
          :disabled="props.disabled"
          :code-list="props.codeList"
          @formula-values-change="formulaValuesChange"/>
    </div>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {toolStore} from '@/store';
import router from "@/router";
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";
import {cancelFavorite, saveFavorite} from "@/api/analyse/api";
import {ROUTE_NAME} from "@/router/constants";
import {debounce} from "lodash";
import FormulaValuesSelect from "./FormulaValuesSelect.vue";

const toolData = toolStore();
const triggerVisible = ref(false);
const emits = defineEmits(['tabsChange']);
const handleTriggerVisible = () => {
  triggerVisible.value = !triggerVisible.value;
};
const props = defineProps({
  // 回显数据
  info: {
    type: Object,
    default() {
      return {};
    },
  },
  // 禁用只读
  disabled: {
    type: Boolean,
    default: false
  },
  // 只有事件列表
  onlyEvent: {
    type: Boolean,
    default: false
  },
  // 只有用户属性列表
  onlyUser: {
    type: Boolean,
    default: false
  },
  // 只有用户分群列表
  onlyGroup: {
    type: Boolean,
    default: false
  },
  // 不包括事件列表
  excludeEvent: {
    type: Boolean,
    default: false
  },
  // code数据[eventCode | indicatorCode]
  codeList: {
    type: Array,
    default: () => []
  },
  // 不可选数据
  disabledList: {
    type: Array,
    default: () => []
  },
  // 是否提供计算公式，枚举值选择器
  showDetailFilter: {
    type: Boolean,
    default: false
  },
});
// 图标
const iconTypeMap = {
  event: 'icon-shijianshuxing2',
  user: 'icon-yonghushuxing',
  cluster: 'icon-yonghufenqun',
  tag: 'icon-icon_yonghubiaoqian',
  abtest: 'icon-ABceshi1'
} as any;
// 方法获取标签
const getLabel = (objectType: any) => {
  const labels: { [key: string]: string } = {
    string: '文本',
    int: '整数',
    float: '小数',
    boolean: '布尔',
    date: '日期',
    array: '列表',
    datetime: '日期时间',
    variant: '对象组'
  };
  return labels[objectType] || '';
}
// 方法获取属性
const getAttrName = (value: any) => {
  const clusterTypeLabel: { [key: number]: string } = {
    1: '条件分群',
    2: 'ID分群',
    3: 'SQL分群',
    4: '结果分群',
  }
  const tagLabel: { [key: number]: string } = {
    1: '条件标签',
    2: 'ID标签',
    3: 'SQL标签',
    // 4: '首末次标签',
    4: '指标值标签',
  }
  const labels: { [key: number]: string } = {
    1: '预置属性',
    2: '自定属性',
    3: '虚拟属性',
    4: '维度表属性',
  };
  if (value.filterType && value.filterType === 'cluster') {
    return clusterTypeLabel[value.type] || '';
  }
  if (value.filterType && value.filterType === 'tag') {
    return tagLabel[value.type] || '';
  }
  if (value.filterType && value.filterType === 'abtest') {
    return '';
  }
  return labels[value.type] || '';
};
// 前往属性详情
const toAttrDetail = (record:any) => {
  if (record.filterType === 'cluster') {
    router.push({name: ROUTE_NAME.USER_GROUP_CONDITION,query: {text: record.objectName || record.objectDisplayName},})
  } else if (record.filterType === 'tag') {
    router.push({name: ROUTE_NAME.USER_TAG,query: {text: record.objectName || record.objectDisplayName}})
  } else if (record.filterType === 'user') {
    router.push({name: ROUTE_NAME.OPERATION_ANALYSE_USER_ATTRIBUTE,query: {text: record.objectName || record.objectDisplayName}})
  } else if (record.filterType === 'abtest') {
    router.push({name: ROUTE_NAME.AB_TEST_EXPERIMENT,query: {text: record.objectName || record.objectDisplayName}})
  } else {
    router.push({name: ROUTE_NAME.OPERATION_ANALYSE_EVENT_ATTRIBUTE,query: {text: record.objectName || record.objectDisplayName}})
  }
}
const filterData = ref<any>({
  objectName: props.info.objectName || '',
  objectDisplayName: props.info.objectDisplayName || '',
  objectType: props.info.objectType || '',
  objectId: props.info.objectId || '',
  type: props.info.type || '',
  filterType: props.info?.filterType,
  note: '' // 备注
});

//   原数组
const originList = ref<any[]>([]);

interface originItem {
  objectId: string;
  objectName: string;
  objectDisplayName: string;
  objectType: string;
  note: string;
  isFavorite: boolean;
  favoriteCode?: string;
}

interface CategoryItem {
  categoryName: string;
}

type virtualItem = CategoryItem | originItem;
// 搜索框
const searchInput = ref('')
// 虚拟列表  事件数组
const virtualItemList = ref<virtualItem[]>([]);
const getVirtualItemList = (arr) => {
  virtualItemList.value = arr.flatMap((category) => {
    const categoryItem: CategoryItem = {
      categoryName: category.categoryName,
    };
    return [categoryItem, ...category.itemData];
  });
}
const handleDataList = async () => {
  originList.value = []
  if (toolData.allAttrList.length) {
    const flatList = handleAttrFlatData(toolData.allAttrList, props.codeList);
    let attrList = []
    if (props.onlyUser) {
      attrList = flatList.filter(category => category.categoryName === '用户属性')
    } else if (props.onlyGroup) {
      attrList = flatList.filter(category => category.categoryName === '用户分群')
    } else if (props.onlyEvent) {
      attrList = flatList.filter(category => category.categoryName === '事件属性')
    } else if (props.excludeEvent) {
      attrList = flatList.filter(category => category.categoryName !== '事件属性')
    } else {
      attrList = flatList
    }
    originList.value = attrList.map(item => {
      return {
        ...item,
        itemData: item.itemData.map(attr => {
          return {
            objectName: attr.name,
            objectDisplayName: attr.displayName,
            objectType: attr.dataType,
            objectId: attr.code,
            note: attr.note || '',
            type: attr.type,
            filterType: attr.attributeType,
            isFavorite: attr.isFavorite ?? false,
            favoriteCode: attr?.favoriteCode
          }
        })
      }
    })
    // 添加收藏分类并填充收藏数据
    const favoriteItems = originList.value
        .flatMap(category => category.itemData)
        .filter(item => item.isFavorite);
    originList.value.unshift({
      categoryName: '收藏',
      itemData: favoriteItems
    })
    getVirtualItemList(originList.value)
  }
};

//   事件容器，左右点击滚动效果联动
const activeIndex = ref(0)
const virtualListRef = ref(null);
const selectListRef = ref(null);
// 添加一个标志来控制是否响应滚动事件
const isManualScrolling = ref(false);

const categoryChange = async (categoryName: string, index: number) => {
  // 设置标志，表示正在手动滚动
  isManualScrolling.value = true;
  
  await nextTick();
  activeIndex.value = index; // 先更新 activeIndex，确保左侧高亮正确

  // 计算目标分类在 virtualItemList 中的位置
  const targetIndex = virtualItemList.value.findIndex(item => item.categoryName === categoryName);
  
  if (targetIndex !== -1) {
    // 如果有数据，正常滚动
    virtualListRef.value?.scrollIntoView({ index: targetIndex });
  } else {
    // 如果没有数据，找到该分类在 originList 中的位置，计算应该滚动到的位置
    const categoryPosition = originList.value.findIndex(item => item.categoryName === categoryName);
    if (categoryPosition !== -1) {
      // 假设每个分类占固定高度，或者动态计算
      const estimatedScrollPosition = categoryPosition * 30; // 调整这个值
      virtualListRef.value?.$el.querySelector('.arco-virtual-list')?.scrollTo({
        top: estimatedScrollPosition,
        behavior: 'smooth'
      });
    }
  }
  
  // 使用 setTimeout 延迟重置标志，确保滚动动画完成后再恢复
  setTimeout(() => {
    isManualScrolling.value = false;
  }, 500); // 500ms 应该足够滚动动画完成
};

const handleScroll = () => {
  const scrollContainer = virtualListRef.value?.$el.querySelector('.arco-virtual-list');
  if (!scrollContainer) return;

  let lastKnownIndex = -1;

  const observer = new IntersectionObserver((entries) => {
    // 如果正在手动滚动，不响应滚动事件
    if (isManualScrolling.value) return;
    
    if (scrollContainer.scrollTop <= 10) {
      activeIndex.value = 0;
      return;
    }

    // 找出最接近顶部的可见分类（且必须完全可见）
    let closestEntry = null;
    let minDistance = Infinity;

    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio >= 0.9) { // 确保大部分可见
        const distance = Math.abs(entry.boundingClientRect.top);
        if (distance < minDistance) {
          minDistance = distance;
          closestEntry = entry;
        }
      }
    });

    if (closestEntry) {
      const categoryName = closestEntry.target.querySelector('span')?.textContent?.trim();
      if (categoryName) {
        const newIndex = originList.value.findIndex(el => categoryName === el.categoryName);
        if (newIndex !== -1 && newIndex !== lastKnownIndex) {
          lastKnownIndex = newIndex;
          activeIndex.value = newIndex;
        }
      }
    }
  }, {
    root: scrollContainer,
    threshold: [0.5, 0.9], // 检测部分可见和完全可见
    rootMargin: '0px 0px -30% 0px' // 底部减少检测区域，避免误判
  });

  const categoryElements = scrollContainer.querySelectorAll('.list-name');
  categoryElements.forEach(element => {
    observer.observe(element);
  });

  // 防抖处理
  const handleScrollEvent = debounce(() => {
    // 如果正在手动滚动，不响应滚动事件
    if (isManualScrolling.value) return;
    
    if (scrollContainer.scrollTop <= 10) {
      activeIndex.value = 0;
      lastKnownIndex = 0;
    }
  }, 100);

  scrollContainer.addEventListener('scroll', handleScrollEvent);

  return () => {
    observer.disconnect();
    scrollContainer.removeEventListener('scroll', handleScrollEvent);
  };
};


onMounted(() => {
  if (originList.value && originList.value.length > 0) {
    activeIndex.value = 0;
  }
  handleScroll();
});

onUnmounted(() => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    const cleanup = handleScroll();
    if (cleanup) cleanup();
  }
});
// 赋值默认计算公式
const defaultCalcuSymbol = (objectType?: string) => {
  const rangeLists = {
    'string': 'eq',
    'int': 'eq',
    'float': 'eq',
    'array': 'con',
    'date': 'scope',
    'datetime': 'scope',
    'boolean': 'eq',
    'variant': 'con',
  };
  return rangeLists[objectType || 'array'] || ''
}
//   点击
const listChange = (item: any) => {
  filterData.value = {
    ...filterData.value,
    filterType: item.filterType,
    objectType: ["cluster", "tag", "abtest"].includes(item.filterType) ? undefined :item.objectType,
    objectName: ["cluster", "tag", "abtest"].includes(item.filterType) ? '' : item.objectName,
    objectDisplayName: item.objectDisplayName,
    type: item.type,
    objectId: item.objectId,
    note: item.note,
    calcuSymbol: defaultCalcuSymbol(item.objectType),
    thresholds: [],
    selectArr: [],
    selectData: '',
    enumList: []
  }
  triggerVisible.value = false;
  emits('tabsChange', filterData.value);
};

// 搜索时数据
const filteredLists = computed(() => {
  const str = searchInput.value.trim();
  if (!str) return [];

  // 获取所有分类的数据并保持原有分类
  const searchResults = originList.value.map(category => {
    const filteredItems = category.itemData.filter(
        item => item?.objectDisplayName?.toLowerCase().includes(str.toLowerCase()) ||
            item?.objectName?.toLowerCase().includes(str.toLowerCase())
    );
    if (filteredItems.length > 0) {
      return [
        {typeName: category.categoryName},
        ...filteredItems
      ];
    }
    return [];
  });
  // 扁平化结果数组
  return searchResults.flat().filter(item => item);
});

// 搜索点击
const itemChange = (item: any) => {
  filterData.value = {
    ...filterData.value,
    filterType: item.filterType,
    objectType: ["cluster", "tag", "abtest"].includes(item.filterType) ? undefined :item.objectType,
    objectName: ["cluster", "tag", "abtest"].includes(item.filterType) ? '' : item.objectName,
    objectDisplayName: item.objectDisplayName,
    type: item.type,
    objectId: item.objectId,
    note: item.note,
    calcuSymbol: defaultCalcuSymbol(item.objectType),
    thresholds: [],
    selectArr: [],
    selectData: '',
    enumList: []
  }
  triggerVisible.value = false;
  emits('tabsChange', filterData.value);
}
const formulaValuesChange = (val: any) => {
  filterData.value.calcuSymbol = val?.calcuSymbol
  filterData.value.thresholds = val?.thresholds
  filterData.value.enumList = val?.enumList
  filterData.value.selectArr = val?.selectArr
  filterData.value.selectData = val?.selectData
  emits('tabsChange', filterData.value);
}
// 收藏对象枚举
const objectTypeMap = {
  event: 3,    // 事件属性
  user: 4,     // 用户属性
  cluster: 5,  // 用户分群
  tag: 6,       // 用户标签
  abtest: 7      // ab测试
} as any;

// 事件收藏
const starClick = async (item: any) => {
  try {
    const data = {
      objectType: objectTypeMap[item.filterType],
      objectCode: item.objectId,
    }
    await saveFavorite(data)
    // 更新 store 中属性分类收藏状态
    // toolData.updateAttrFavoriteStatus(item.objectId, true);
    toolData.updateAllAttrFavoriteStatus(item.objectId, true);
  } catch (error) {
    console.error('收藏失败:', error);
  }
};

// 取消收藏
const cancelClick = async (item: any) => {
  try {
    await cancelFavorite(item.favoriteCode)
    // 更新 store 中属性分类收藏状态
    // toolData.updateAttrFavoriteStatus(item.objectId, false);
    toolData.updateAllAttrFavoriteStatus(item.objectId, false);
  } catch (error) {
    console.error('取消收藏失败:', error);
  }
};

// 搜索列表的收藏/取消收藏
const itemClick = (item: any) => {
  starClick(item);
};

const cancelItemClick = (item: any) => {
  cancelClick(item);
};
// 根据id在原数据中查找对象
const findFromOriginList = (objectId) => {
  if (!objectId || !originList.value) return '';
  
  // 遍历所有分类及其项目
  for (const category of originList.value) {
    if (!category.itemData) continue;
    
    // 在当前分类中查找匹配的项目
    const foundItem = category.itemData.find(item => item.objectId === objectId);
    if (foundItem) {
      return foundItem;
    }
  }
  
  return {};
};
watch(
    () => props.info,
    (newVal) => {
      filterData.value.objectName = newVal.objectName || filterData.value.objectName || filterData.value.name
      filterData.value.objectId = newVal.objectId || filterData.value.objectId || filterData.value.code
      filterData.value.objectDisplayName = newVal.objectDisplayName || filterData.value.objectDisplayName || filterData.value.displayName
      if (["cluster", "tag", "abtest"].includes(newVal.filterType)) {
        filterData.value.objectType = undefined
        filterData.value.objectName = ''
      } else {
        filterData.value.objectType = newVal.objectType || filterData.value.objectType
      }
      const findItem = findFromOriginList(filterData.value.objectId)
      filterData.value.note = findItem?.note
      filterData.value.type = newVal?.type
      filterData.value.filterType = newVal?.filterType || filterData.value.filterType
      filterData.value.calcuSymbol = newVal.calcuSymbol || ''
      filterData.value.thresholds = newVal?.thresholds || []
      filterData.value.enumList = newVal?.enumList || []
      filterData.value.selectArr = newVal?.selectArr || []
      filterData.value.selectData = newVal?.selectData || ''
    },
    {immediate: true, deep: true}
);
const chooseLists = computed(() => {
  return props.disabledList.map(item => item.objectId)
});
watch(
    () => toolData.allAttrList,
    (newVal) => {
      if (newVal?.length) {
        handleDataList();
        const defaultData = originList.value
            .filter(category => category.itemData?.length > 0)
            .flatMap(category => category.itemData || [])[0];
        filterData.value.objectName = filterData.value?.objectName;
        filterData.value.objectDisplayName = filterData.value.objectDisplayName || defaultData?.objectDisplayName;
        filterData.value.filterType = filterData.value?.filterType;
        if (["cluster", "tag", "abtest"].includes(newVal.filterType)) {
          filterData.value.objectType = undefined
          filterData.value.objectName = ''
        } else {
          filterData.value.objectType = newVal.objectType || filterData.value.objectType
        }
        filterData.value.objectId = filterData.value.objectId || defaultData?.objectId;
        const findItem = findFromOriginList(filterData.value.objectId)
        filterData.value.note = findItem?.note
        filterData.value.type = filterData.value?.type;
      }
    },
    {immediate: true}
);


watch(() => props.codeList,
    async (newVal) => {
      if (newVal.length) {
        await handleDataList()
        const findItem = findFromOriginList(filterData.value.objectId)
        filterData.value.note = findItem?.note
      }
    },
    {immediate: true, deep: true}
);
const searchInputRef = ref();
// 默认聚焦
watch(triggerVisible, (val) => {
  if (val) {
    nextTick(() => {
      if (searchInputRef.value?.focus) {
        searchInputRef.value.focus();
      }
    });
  }
});
</script>

<style scoped lang="less">
.screen-body {
  display: flex;
}

.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.select-panel {
  position: relative;
  width: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-bottom);
}

.list-container {
  position: relative;
  display: flex;
  flex-direction: row;

  .list-category {
    height: 350px;
    position: relative;
    flex-shrink: 0;
    width: 100px;
    padding: 10px 0 0 10px;
    border-right: 1px solid var(--tant-border-color-border1-1);

    .category-container {
      height: calc(100% - 32px);
      overflow-y: auto;

      .category-item {
        width: 100%;
        margin-bottom: 10px;
        padding: 2px 0 2px 2px;
        color: var(--tant-text-gray-color-text1-3);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item-active {
        font-weight: 500;
        border-right: 2px solid var(--tant-primary-color-primary-default);
      }
    }

    .arco-btn {
      margin-left: -10px;
      width: 100px;
      border-radius: 0 0 0 4px;
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-2);
      text-shadow: none;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: 1px solid var(--tant-border-color-border1-1);
      box-shadow: none;
      border-left: none;
      border-bottom: none;
    }
  }

  .item-list {
    flex: 1 1;
    .select-list {
      // position: relative;
      height: 350px;
      width: 260px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }

    .list-group {
      width: 100%;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    .favorite {
      height: 30px;
      width: 100%;
      padding-left: 18px;
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
    }

    .list-name {
      border-top: 1px solid var(--tant-border-color-border1-1);
      margin-top: 8px;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      height: 45px;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    //.list-content:first-child .list-name{
    //    border-top: none;
    //    margin-top: 0;
    //}
    .list-box {
      margin: 0 8px;
      padding-left: 8px;
      border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 28px;
      cursor: pointer;

      .desc {
        display: inline-block;
        width: 180px;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1, .desc2 {
        display: inline-block;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1 {
        width: 140px;
      }

      .desc2 {
        width: 120px;
      }

      .icon {
        opacity: 0;
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
      }

      .icon:hover {
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);

        .icon {
          opacity: 100;
          pointer-events: all;
        }
      }

      &.list-disabled {
        cursor: not-allowed;
        opacity: 0.6;
        // pointer-events: none;
      }

      .isStar {
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
        opacity: 100;
        pointer-events: all;
      }
    }

    .list-active {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }

    .list-metric {
      position: relative;
      height: 350px;
      width: 360px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }
  }
}

.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);
  .tip{
    opacity: 0;
    padding-right: 4px;
  }
  &:hover{
    .tip{
      opacity: 1;
    }
  }
  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;

      .header-icon {
        // margin-top: 2px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }

      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }

  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;

    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}
</style>
