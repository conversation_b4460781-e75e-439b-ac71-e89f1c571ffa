<template>
  <div class="content">
    <div v-if="tableData.length" style="margin-bottom: 16px;">
    <a-select v-model:model-value="viewType" :style="{ width: '120px' }" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="viewChange">
      <template #label="{ data }">
        <div class="chart-type-select-label">
          <img class="select-icon" :src="'/icon/' + data?.value + '-chart.svg'" alt="" />
          <span>{{ data?.label }}</span>
        </div>
      </template>
      <a-option value="table">
        <template #icon>
          <img class="option-icon" src="/icon/table-chart.svg" alt="" />
        </template>
        <template #default>表格</template>
      </a-option>
      <a-option value="trend">
        <template #icon>
          <img class="option-icon" src="/icon/trend-chart.svg" alt="" />
        </template>
        <template #default>趋势图</template>
      </a-option>
    </a-select>
    </div>

    <a-table v-show="viewType === 'table'" :columns="columns" :data="tableData" :loading="loading" :pagination="pagination" :filter-icon-align-left="true" :style="{ width: '100%' }" :scroll="{ y: 'calc(100% - 56px)' }" @page-size-change="pageSizeChange" @filter-change="handleTableFilter">
      <!-- 添加自定义筛选模板 -->
      <template v-for="(_, index) in groupsDescData" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
        <div class="custom-filter">
          <a-space direction="vertical" :size="8">
            <a-select
              :model-value="filterValue"
              placeholder="请选择"
              allow-clear
              allow-search
              multiple
              :options="getFilterOptionsByIndex(index)"
              :style="{ width: '200px' }"
              :trigger-props="{
                position: 'top',
                autoFitPopupMinWidth: true,
                updateAtScroll: true,
              }"
              @update:model-value="(val) => setFilterValue(val)"
            >
            </a-select>
            <div class="custom-filter-footer">
              <a-button size="mini" @click="handleFilterReset">重置</a-button>
              <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
            </div>
          </a-space>
        </div>
      </template>
    </a-table>
    <div v-show="viewType === 'trend'" class="chart">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <Chart :option="option" />
      </a-spin>
    </div>
    <!-- <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div> -->
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { getStrategyGamewayData } from '@/api/analyse/api';
  import { range } from 'lodash';
  import useChartOption from '@/hooks/chart-option';

  const viewType = ref('table');
  const option = ref<any>();
  const queryParams = reactive({
    current: 1,
    pageSize: 10,
  });
  const loading = ref(false);
  const total = ref(0);
  const columns = ref<any>([]);
  // 示例数据
  const tableData = ref<any>([]);

  const parentParams = ref({});
  const groupsData = ref<any>([]);
  const groupsDescData = ref<any>([]);
  const pagination = ref<any>({
    pageSize: 10,
    pageSizeOptions: [5, 10, 15, 20],
    showPageSize: 'true',
    showTotal: 'true',
  });
  const pageSizeChange = (size) => {
    pagination.value.pageSize = size;
  };
  const resultData = ref();

  const renderChart = (filtersValueObj?: any) => {
    if (resultData.value === undefined || Object.keys(resultData.value).length === 0) {
      return;
    }
    const { groupQueryResult, timeSpan } = resultData.value;

    let filteredSeriesData = groupQueryResult[0].resultData;
    let filteredGroupsData = groupQueryResult?.[0].resultData.map((data) => data.group.join(','));
    // 如果有筛选条件，过滤数据
    if (filtersValueObj && Object.keys(filtersValueObj).length > 0) {
      filteredSeriesData = groupQueryResult[0].resultData.filter((item) => {
        // 检查每个筛选条件
        return Object.entries(filtersValueObj).every(([dataIndex, filters]: [string, any]) => {
          if (!filters || filters.length === 0) return true;
          // 从 dataIndex 中提取分组索引，如 "groupName0" -> 0
          const groupIndexMatch = dataIndex.match(/groupName(\d+)/);
          if (groupIndexMatch) {
            const groupIndex = parseInt(groupIndexMatch[1]);
            // 检查对应位置的值是否在筛选列表中
            return filters.includes(item.group[groupIndex]);
          }
          return true;
        });
      });
      filteredGroupsData = filteredSeriesData.map((data) => data.group.join(','));
    }
    groupsData.value = filteredGroupsData;

    // 横坐标
    let timeSpanPrefix;
    switch (timeSpan?.unit) {
      case 'DAY':
        timeSpanPrefix = 'D';
        break;
      case 'WEEK':
        timeSpanPrefix = 'W';
        break;
      case 'MONTH':
        timeSpanPrefix = 'M';
        break;
      default:
        timeSpanPrefix = '';
        break;
    }
    const xData = range(0, (timeSpan?.number ?? 0) + 1).map((i) => `${timeSpanPrefix}${i}`);

    const { chartOption } = useChartOption(() => {
      return {
        grid: {
          left: '40',
          right: '0',
          top: '20',
          bottom: '48',
        },
        legend: {
          data: filteredGroupsData,
          bottom: '0',
          type: 'scroll',
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          enterable: true,
          confine: true,
          className: 'echarts-tooltip-diy',
          extraCssText: 'max-height: 300px; overflow-y: auto;',
        },
        xAxis: {
          type: 'category',
          data: xData,
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter(value: number) {
              if (value >= 1000000) {
                return `${value / 1000000}M`;
              }
              if (value >= 1000) {
                return `${value / 1000}k`;
              }
              return value;
            },
          },
        },
        series: filteredSeriesData.map((item: any) => {
          return {
            name: item.group.join(','),
            type: 'line',
            data: item.values,
            label: {
              show: true,
              position: 'top',
              formatter(params) {
                if (!params.value || typeof params.value !== 'number') {
                  return params.value;
                }
                if (params.value >= 1000000) {
                  return `${Math.round(params.value / 1000000)}M`;
                }
                if (params.value >= 1000) {
                  return `${Math.round(params.value / 1000)}k`;
                }
                return Math.round(params.value);
              },
            },
          };
        }),
      };
    });
    option.value = chartOption.value;
  };
  const filtersValue = ref<any>({});
  const handleTableFilter = (dataIndex, filters) => {
    filtersValue.value[dataIndex] = filters;
    // 传递完整的筛选信息，包含列索引和筛选值
    renderChart(filtersValue.value);
  };
  const viewChange = (v) => {
    //   if (v === 'chart') {
    //     renderChart()
    //   }
  };
  // 添加获取筛选选项的函数
  const getFilterOptionsByIndex = (index) => {
    if (!tableData.value?.length) return [];
    return Array.from(new Set(tableData.value.map((row) => row[`groupName${index}`])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value: value,
      }));
  };
  const getData = async (data?: any) => {
    if (data) {
      parentParams.value = data;
    }
    const params = {
      ...queryParams,
      ...parentParams.value,
    };
    loading.value = true;
    try {
      const res = await getStrategyGamewayData(params);
      const rawData = res.items || {};
      resultData.value = rawData;
      const { groupsDesc, groups, groupQueryResult, timeSpan, newUserCount, income, arpu, ecpmBanner, ecpmInter, ecpmReward } = rawData;
      renderChart();
      groupsDescData.value = groupsDesc;
      // 生成表头
      let timeSpanPrefix = 'D';
      if (timeSpan?.unit === 'WEEK') timeSpanPrefix = 'W';
      if (timeSpan?.unit === 'MONTH') timeSpanPrefix = 'M';
      // 生成表体数据
      const tableDataTemp: any[] = [];
      groups?.forEach((groupArr, groupIdx) => {
        const row: any = {};
        groupArr.forEach((groupVal, idx) => {
          row[`groupName${idx}`] = groupVal;
        });
        row.newUserCount = newUserCount[groupIdx];
        row.income = income[groupIdx];
        row.arpu = arpu[groupIdx];
        row.ecpmBanner = ecpmBanner[groupIdx];
        row.ecpmInter = ecpmInter[groupIdx];
        row.ecpmReward = ecpmReward[groupIdx];
        // 指标单选，暂时不做处理（后续改成多选可在groupQueryResult循环中push数据{...data,indicatorName:queryItem.displayName}）
        if (groupQueryResult && groupQueryResult.length > 0) {
          groupQueryResult.forEach((queryItem: any) => {
            const result = queryItem.resultData.find((item: any) => item.group.join(',') === groupArr.join(','));
            if (result) {
              result.values.forEach((val: any, idx: number) => {
                row[`${timeSpanPrefix}${idx}`] = val;
              });
            }
          });
        }
        tableDataTemp.push(row);
      });

      const groupColumns =
        groupsDesc?.map((group, index) => ({
          title: group.name,
          dataIndex: `groupName${index}`,
          width: 120,
          fixed: 'left',
          sortable: { sortDirections: ['ascend', 'descend'] },
          filterable: {
            // 使用自定义筛选插槽
            slotName: `group-filter-${index}`,
            filter: (value, row) => {
              if (!value || value.length === 0) return true;
              return value.includes(row[`groupName${index}`]);
            },
            multiple: true,
          },
        })) || [];

      // 指标列
      const indicatorColumns = [
        {
          title: '新增人数',
          dataIndex: 'newUserCount',
          width: 120,
          sortable: { sortDirections: ['ascend', 'descend'] },
        },
        {
          title: '总收入',
          dataIndex: 'income',
          width: 100,
          sortable: { sortDirections: ['ascend', 'descend'] },
        },
        {
          title: 'ARPU',
          dataIndex: 'arpu',
          width: 100,
          sortable: { sortDirections: ['ascend', 'descend'] },
        },
        {
          title: 'Ecpm',
          children: [
            {
              title: 'banner',
              dataIndex: 'ecpmBanner',
              width: 100,
              sortable: { sortDirections: ['ascend', 'descend'] },
            },
            {
              title: 'inter',
              dataIndex: 'ecpmInter',
              width: 100,
              sortable: { sortDirections: ['ascend', 'descend'] },
            },
            {
              title: 'reward',
              dataIndex: 'ecpmReward',
              width: 100,
              sortable: { sortDirections: ['ascend', 'descend'] },
            },
          ],
        },
      ];

      // 时间列
      const resultColumns = Array.from({ length: (timeSpan?.number ?? 0) + 1 }, (_, i) => ({
        title: `${timeSpanPrefix}${i}`,
        dataIndex: `${timeSpanPrefix}${i}`,
        width: 100,
        sortable: { sortDirections: ['ascend', 'descend'] },
        render: ({ record }) => {
          const val = record[`${timeSpanPrefix}${i}`];
          if (parentParams.value?.indicator === 'retention') {
            return `${(val * 100).toFixed(2)}%`;
          }
          return val;
        },
      }));

      columns.value = [...groupColumns, ...indicatorColumns, ...resultColumns];

      // 更新表格数据
      tableData.value = tableDataTemp;
      total.value = res.total;
    } catch (e) {
      tableData.value = [];
      console.error('获取数据失败', e);
    } finally {
      loading.value = false;
    }
  };
  const pageChange = (v) => {
    queryParams.current = v;
    getData();
  };
  defineExpose({
    getData,
  });
</script>

<style lang="less" scoped>
  .pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
  }
  .content {
    width: 100%;
    height: 100%;
  }
  .chart {
    width: 100%;
    height: calc(100% - 56px);
  }
  // 添加自定义筛选样式
  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
  .chart-type-select-label {
    display: flex;
    width: 100%;
    align-items: center;
    .select-icon {
      padding-right: 4px;
    }
  }

  .option-icon {
    margin-right: -4px;
  }
</style>
