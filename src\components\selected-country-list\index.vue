<template>
      <a-select
        v-model:model-value="countrys"
        :loading="loading"
        multiple
        :style="{width:'240px',backgroundColor: 'var(--tant-bg-white-color-bg1-1)'}"
        placeholder="选择国家"
        :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
        :max-tag-count="1"
        :scrollbar="true"
        :filter-option="false"
        @search="searchCountry"
        @change="countryChange"
        @remove="removeChange"
        @popup-visible-change="popupVisibleChange">
        <template #label="{ data }">
            <span>国家-{{ data?.label }}</span>
        </template>
        <a-option v-for="item in sortedCountryList" :key="item.code" :value="item.code">
            {{ item.name }}
        </a-option>
    </a-select>
  </template>
  
  <script lang="ts" setup>
  import {ref, watch,computed} from 'vue';
  import {getCountryList,countrySearch} from "@/api/marketing/api";
  import { cloneDeep } from 'lodash';
  import { sortSelectedFirst } from "@/utils/sortUtil";

  
  const props = defineProps({
    defaultCodes: {
      type: Array,
      default: () => ['global']
    }
  })
  const loading = ref(false)
  const countryList = ref()
  const countrys = ref<any>([])
  const sortedCountryList = ref<any>([])
  const allData = ref()
  const emits = defineEmits(['countrysChange'])

  const searchCountry = async (v) => {
    loading.value = true
    const data = {
        selectCountryCode:countrys.value,
        content:v
    }
    if (!v || v.trim() === '') {
      // 搜索内容为空时，恢复全量数据
      countryList.value = cloneDeep(allData.value)
    } else {
      await countrySearch(data).then(res => {
          countryList.value = []
          countryList.value = res;
      })
    }
    sortedCountryList.value = sortSelectedFirst(countryList.value,countrys.value)
    loading.value = false
 }
  const countryChange = async () => {
    if(countrys.value.length){
        // emits('countrysChange', countrys.value)
    }else{
        searchCountry('')
    }
  }

  const popupVisibleChange = (v) => {
    if(v){
      countryList.value = cloneDeep(allData.value)
      sortedCountryList.value = sortSelectedFirst(countryList.value,countrys.value)
    }else{
      emits('countrysChange', countrys.value)
    }
  }
  const removeChange = () => {
    emits('countrysChange', countrys.value)
  }
  const init = async () => {
    loading.value = true
    await getCountryList().then(res => {
        countryList.value = res
        allData.value = res
        sortedCountryList.value = sortSelectedFirst(countryList.value,countrys.value)
    })
    loading.value = false
  }
  init()
  watch(() => props.defaultCodes,(newValue,oldValue) => {
    countrys.value = cloneDeep(props.defaultCodes)
  },{immediate:true,deep:true})

  </script>
  
  <style scoped lang="less">
  </style>
    