<template>
    <!-- 日周月下拉选择 -->
    <a-dropdown @select="windowSelect">
        <a-tooltip position="top" mini :disabled="props.disabledPop">
            <template #content>
                事件之间的时间窗口，不可大于已选 <br> 择的日期范围
            </template>
            <div class="select-label">
                {{ windowName }}<icon-down />
            </div>
        </a-tooltip>
        <template #content>
            <a-dsubmenu>
                <template #default>天(即24小时)</template>
                <template #content>
                    <a-doption value="d">1天</a-doption>
                    <a-doption value="7d">7天</a-doption>
                    <a-doption value="14d">14天</a-doption>
                    <a-doption :value="`${dValue}d`" style="width: 150px;">
                        <div class="doption-item">
                            <a-input-number
                                v-model:model-value="dValue"
                                :style="{ width: '60px', height: '24px' }"
                                :default-value="30"
                                :precision="0"
                                :min="0"
                                @click="(event) => event.stopPropagation()"
                            />
                            <div>天</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-dsubmenu>
                <template #default>小时</template>
                <template #content>
                    <a-doption value="h">1小时</a-doption>
                    <a-doption value="3h">3小时</a-doption>
                    <a-doption value="12h">12小时</a-doption>
                    <a-doption :value="`${hValue}h`" style="width: 150px;">
                        <div class="doption-item">
                            <a-input-number
                                v-model:model-value="hValue"
                                :style="{ width: '60px', height: '24px' }"
                                :default-value="24"
                                :precision="0"
                                :max="24"
                                :min="0"
                                @click="(event) => event.stopPropagation()"
                                />
                            <div>小时</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-dsubmenu>
                <template #default>分钟</template>
                <template #content>
                    <a-doption value="m">1分钟</a-doption>
                    <a-doption value="5m">5分钟</a-doption>
                    <a-doption value="30m">30分钟</a-doption>
                    <a-doption :value="`${mValue}m`" style="width: 150px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                            <a-input-number
                                v-model:model-value="mValue"
                                :style="{ width: '60px', height: '24px' }"
                                :default-value="60"
                                :precision="0"
                                :max="60"
                                :min="0"
                                @click="(event) => event.stopPropagation()"
                                />
                            <div>分钟</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
        </template>
    </a-dropdown>
</template>

<script setup lang="ts">
import {ref,watch} from "vue";

const emits = defineEmits(['change'])
const dValue = ref(30)
const hValue = ref(24)
const mValue = ref(60)
const windowName = ref('1天')
const timeWindow = ref({
    value: 1,
    unit: "day",
})
const windowSelect = (v) => {
    const numbers = v.match(/\d+/g)?.join(',');
    timeWindow.value.value = numbers ? Number(numbers) : 0
    if (v.includes('d')) {
        windowName.value = numbers ? `${numbers}天` : '天'
        timeWindow.value.unit = 'day'
    } else if (v.includes('h')) {
        windowName.value = numbers ? `${numbers}小时` : '小时'
        timeWindow.value.unit = 'hour'
    } else if (v.includes('m')) {
        windowName.value = numbers ? `${numbers}分钟` : '分钟'
        timeWindow.value.unit = 'minute'
    }
    emits('change', timeWindow.value)
}

const props = defineProps({
    disabledPop: {
        type: Boolean,
        default: false
    },
    timeWindowData: {
        type: Object,
        default: () => ({ value: 1, unit: 'day' })
    }
})
watch(() => props.timeWindowData, (val:any) => {
    if (!val) return;
    timeWindow.value = { ...val };
    if (val.unit === 'day') {
        dValue.value = val.value;
        windowName.value = `${val.value}天`;
    } else if (val.unit === 'hour') {
        hValue.value = val.value;
        windowName.value = `${val.value}小时`;
    } else if (val.unit === 'minute') {
        mValue.value = val.value;
        windowName.value = `${val.value}分钟`;
    }
}, { immediate: true });
</script>

<style lang="less" scoped>
.select-label {
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    // background-color: var(--tant-secondary-color-secondary-fill);
    background-color: #fff;
    border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;

    .btn-icon {
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }

    .filter-label {
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }

    &:hover {
        border-color: var(--tant-primary-color-primary-hover);}
}
.select-label:active{

    border: 1px solid var(--tant-primary-color-primary-active);
    box-shadow: var(--tant-input-shadow-active-overall);
}

.arco-dropdown-open .arco-icon-down {
    transform: rotate(180deg);
}

.doption-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 120px;

    .hover-icon {
        opacity: 0;
    }

    &:hover {
        .hover-icon {
            opacity: 1;
        }
    }
}

.week-set-body {
    width: 352px;
    padding: 16px 16px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-bottom);

    .week-set-title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }

    .week-set-desc {
        display: block;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;

        span {
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            padding: 0 4px;
        }
    }

    .week-set-weeks {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-right: -16px;
        padding-bottom: 16px;

        .week-set-week {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 74px;
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 5px 16px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            white-space: nowrap;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 4px;
            cursor: pointer;
            user-select: none;

            &:hover {
                background-color: var(--tant-secondary-color-secondary-fill-active);
                border-color: var(--tant-secondary-color-secondary-fill-active);
            }
        }

        .week-active {
            background-color: var(--tant-secondary-color-secondary-fill-active);
            border-color: var(--tant-secondary-color-secondary-fill-active);
        }

    }

    .week-set-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: -16px;
        margin-left: -16px;
        padding-top: 8px;
        padding-right: 16px;
        border-top: 1px solid var(--tant-border-color-border1-1);

        button {
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
}
</style>
