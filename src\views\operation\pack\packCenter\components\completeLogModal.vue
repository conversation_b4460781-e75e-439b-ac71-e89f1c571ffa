<template>
    <a-modal v-model:visible="modalVisible" :width="700" title-align="start" title="日志" :footer="false" @cancel="closeModal">
        <a-spin :style="{display: 'block'}" :loading="loading">
            <div class="content">
                <a-textarea ref="logRef" :style="{height: '550px'}" v-model:model-value="logContent"></a-textarea>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";

const modalVisible = ref(false)
const loading = ref(false)
const logContent = ref('')
const logRef = ref()

const openModal = async () => {
    modalVisible.value = true
    loading.value = true
}

const setLogContent = async (logStr: string) => {
    loading.value = false
    logContent.value = logStr
    await nextTick(() => {
        if (logRef.value) {
            logRef.value.textareaRef.scrollTop = logRef.value.textareaRef.scrollHeight;
        }
    })
}

const closeModal = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal,
        setLogContent
    }
)
</script>

<style scoped lang="less">
:deep(.arco-textarea) {
    resize: none;
}
</style>
