<template>
  <a-layout class="layout">
    <a-layout-header class="layout__header">
      <div class="header-title">
        <button class="title-button" @click="goBack">
          <span role="img">
            <icon-left size="13"/>
          </span>
        </button>
        <span style="align-items: center">返回</span>
      </div>
    </a-layout-header>

    <a-layout-content class="layout__content">
      <component :is="modelData[reportModel]"/>
    </a-layout-content>
  </a-layout>
</template>
  
  <script setup lang="ts">
  import {ref} from "vue";
  import router from "@/router";
  import {useRoute} from 'vue-router';
  import eventPage from "@/views/analyse/event/index.vue"
  import funnelPage from "@/views/analyse/funnel/index.vue"
  import sqlPage from "@/views/analyse/sql/index.vue"
  import retentionPage from "@/views/analyse/retention/index.vue"
  import applicationPage from "@/views/analyse/application/index.vue"
  import groupPage from "@/views/analyse/group/index.vue"
  import scatterPage from "@/views/analyse/scatter/index.vue"
  import {ROUTE_NAME} from "@/router/constants";

  const route = useRoute();
  const reportModel = ref(route.query.model);

  const modelData = {
    'event': eventPage,
    'funnel': funnelPage,
    'custom': sqlPage,
    'retention': retentionPage,
    'application': applicationPage,
    'group': groupPage,
    'scatter': scatterPage,
  }
  const goBack = () => {
    router.push({
        name: ROUTE_NAME.DASHBOARD,
    });
  }
  </script>
  
  <style scoped lang="less">
  .layout {
  background-color: var(--tant-bg-gray-color-bg2-1);
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;

  .layout__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 12px 24px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .header-title {
      flex: 1 1;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header4-medium);
      align-items: center;

      .title-button {
        color: var(--tant-text-gray-color-text1-2);
        background-color: transparent;
        border: none;
        margin-right: 4px;
        padding: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;
        transition: .5s;

      }

      .title-button > span {
        display: flex;
        align-items: center;
        line-height: normal;
      }

    }
  }
}
  </style>