<template>
  <div>
    <!-- 事件指标下拉筛选 -->
    <a-trigger
        v-model:popup-visible="triggerVisible"
        trigger="click"
        :unmount-on-close="false"
        position="bl"
        :update-at-scroll="true"
        style="z-index: 1002;"
        @click="handleTriggerVisible"
    >
      <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
        <div class="filter-btn">
          <svg
              v-if="selectObject.type == 'event'"
              class="btn-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
              fill="currentColor"
          >
            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
              <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
            </svg>
          </svg>
          <!-- <icon-launch v-if="selectObject.type == 'event'" class="btn-icon"/> -->
          <icon-computer v-else class="btn-icon"/>
          <span class="filter-label">{{ selectObject.eventName }}-{{ selectObject.eventDisplayName }}</span>
        </div>
        <template #content>
          <div class="trigger-box">
            <div class="card-header">
              <div class="card-header-container">
                <div class="header-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
                    <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                      <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                    </svg>
                  </svg>
                </div>
                <div class="header-title">
                  <div class="name">{{ selectObject.eventDisplayName }}</div>
                </div>
                <div v-if="selectObject.eventType === 1" class="header-type">预置事件</div>
                <div v-if="selectObject.eventType === 2" class="header-type">自定义事件</div>
                <div v-if="selectObject.eventType === 3" class="header-type">虚拟事件</div>
                <div v-if="selectObject.eventType === 'event'" class="header-type">事件指标</div>
                <div v-if="selectObject.eventType === 'retention'" class="header-type">留存指标</div>
              </div>
              <div class="title-sub">{{ selectObject.eventName }}</div>
            </div>
            <div class="card-desc">
              <div v-if="selectObject.eventNote" class="span-desc">{{ selectObject.eventNote }}</div>
              <div v-else class="span-desc">暂无备注</div>
            </div>
            <div class="card-footer">
              <div></div>
              <div class="action">
                <a-tooltip content="前往事件详情" position="top">
                  <div class="action-icon" @click="toEventDetail">
                    <icon-launch/>
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <template #content>
        <div class="select-panel">
          <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
            <a-input v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
              <template #prefix>
                <icon-search/>
              </template>
            </a-input>
          </div>
          <a-tabs v-if="searchInput === ''" v-model:active-key="selectObject.type">
            <a-tab-pane key="event">
              <template #title>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                  <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                    <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                  </svg>
                </svg>
                事件
              </template>
              <div class="list-container">
                <div class="list-category">
                  <div class="category-container">
                    <div
                        v-for="(item, index) in panelList"
                        :key="index"
                        class="category-item"
                        :class="{
                      'item-active': activeIndex === index,
                    }"
                        @click="categoryChange(item.categoryName,index)"
                    >{{ item.categoryName }}
                    </div
                    >
                    <!-- <div class="category-item item-active">资源消耗获取</div> -->
                  </div>
                  <a-button>管理分组</a-button>
                </div>
                <div class="item-list">
                  <div ref="selectListRef" class="select-list">
                    <a-list ref="virtualList" :virtual-list-props="{ height: 350 }" :data="virtualPanelList" @scroll="handleScroll">
                      <template #item="{ item, index }">
                        <a-list-item :key="index">
                          <div class="list-content">
                            <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                              <icon-star v-if="index == 0" class="star"/>
                              <span>{{ item.categoryName }}</span>
                            </div>
                            <div v-if="index == 0 && !panelList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                              点击选项右
                              <icon-star/>
                              侧添加收藏
                            </div>
                            <div v-if="item.eventDisplayName" class="list-box" :class="{ 'list-active': item.eventCode == selectObject.eventCode }" style="height: 30px; width: calc(100% - 16px)"
                                 @click="listChange(item)">
                              <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                                <div>
                                  <span class="desc">{{ item.eventName }}-{{ item.eventDisplayName }}</span>
                                  <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                    <div class="icon" @click.stop="starClick(item)">
                                      <icon-star/>
                                    </div>
                                  </a-tooltip>
                                  <a-tooltip v-else content="取消收藏" position="top">
                                    <div class="isStar" @click.stop="cancelClick(item)">
                                      <icon-star/>
                                    </div>
                                  </a-tooltip>
                                </div>
                                <template #content>
                                  <div class="trigger-box">
                                    <div class="card-header">
                                      <div class="card-header-container">
                                        <div class="header-icon">
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="1em"
                                              height="1em"
                                              viewBox="0 0 24 24"
                                              fill="currentColor"
                                          >
                                            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                              <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                              <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                            </svg>
                                          </svg>
                                        </div>
                                        <div class="header-title">
                                          <div class="name">{{ item.eventDisplayName }}</div>
                                        </div>
                                        <!-- <div class="header-type">自定义事件</div> -->
                                        <div v-if="item.eventType === 1" class="header-type">预置事件</div>
                                        <div v-if="item.eventType === 2" class="header-type">自定义事件</div>
                                        <div v-if="item.eventType === 3" class="header-type">虚拟事件</div>
                                      </div>
                                      <div class="title-sub">{{ item.eventName }}</div>
                                    </div>
                                    <div class="card-desc">
                                      <div v-if="item.eventNote" class="span-desc">{{ item.eventNote }}</div>
                                      <div v-else class="span-desc">暂无备注</div>
                                    </div>
                                    <div class="card-footer">
                                      <div></div>
                                      <div class="action">
                                        <a-tooltip content="前往事件详情" position="top">
                                          <div class="action-icon" @click="toEventDetail">
                                            <icon-launch/>
                                          </div>
                                        </a-tooltip>
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </div>
                          </div>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </div>
              </div>
            </a-tab-pane>
            <a-tab-pane key="indicator">
              <template #title>
                <icon-computer/>
                指标
              </template>
              <div class="list-container">
                <div class="item-list">
                  <div class="list-metric">
                    <a-list :virtual-list-props="{ height: 350 }" :data="virtualMertricList">
                      <template #item="{ item, index }">
                        <a-list-item :key="index">
                          <div class="list-content">
                            <div v-if="item.mertricName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                              <icon-star class="star"/>
                              <span>{{ item.mertricName }}</span>
                            </div>
                            <div v-if="index == 0 && !mertricList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                              点击选项右
                              <icon-star/>
                              侧添加收藏
                            </div>
                            <div v-if="item.eventDisplayName" class="list-box" :class="{ 'list-active': item.eventDisplayName == selectObject.eventDisplayName, }"
                                 style="height: 30px; width: calc(100% - 16px)" @click="mertricListChange(item)">
                              <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                                <div>
                                  <span class="desc">{{ item.eventName }}-{{ item.eventDisplayName }}</span>
                                  <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                    <div class="icon" @click.stop="mertricClick(item)">
                                      <icon-star/>
                                    </div>
                                  </a-tooltip>
                                  <a-tooltip v-else content="取消收藏" position="top">
                                    <div class="isStar" @click.stop="cancelMertricClick(item)">
                                      <icon-star/>
                                    </div>
                                  </a-tooltip>
                                </div>
                                <template #content>
                                  <div class="trigger-box">
                                    <div class="card-header">
                                      <div class="card-header-container">
                                        <div class="header-icon">
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="1em"
                                              height="1em"
                                              viewBox="0 0 24 24"
                                              fill="currentColor"
                                          >
                                            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                              <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                              <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                            </svg>
                                          </svg>
                                        </div>
                                        <div class="header-title">
                                          <div class="name">{{ item.eventDisplayName }}</div>
                                        </div>
                                        <div v-if="item.eventType === 'event'" class="header-type">事件指标</div>
                                        <div v-else class="header-type">留存指标</div>
                                      </div>
                                      <div class="title-sub">{{ item.eventName }}</div>
                                    </div>
                                    <div class="card-desc">
                                      <div v-if="item.eventNote" class="span-desc">{{ item.eventNote }}</div>
                                      <div v-else class="span-desc">暂无备注</div>
                                    </div>
                                    <div class="card-footer">
                                      <div></div>
                                      <div class="action">
                                        <a-tooltip content="前往指标详情" position="top">
                                          <div class="action-icon" @click="goIndexItem(item.eventCode)">
                                            <icon-menu/>
                                          </div>
                                        </a-tooltip>
                                        <a-tooltip content="前往指标列表" position="top">
                                          <div class="action-icon" @click="toIndexList">
                                            <icon-launch/>
                                          </div>
                                        </a-tooltip>
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </div>
                          </div>
                        </a-list-item>
                      </template>
                    </a-list>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
          <div v-else class="list-container" style="padding-top: 16px;">
            <div class="item-list">
              <div class="list-metric">
                <a-list :virtual-list-props="{ height: 350 }" :data="filteredLists">
                  <template #item="{ item, index }">
                    <a-list-item :key="index">
                      <div class="list-content">
                        <div v-if="item.typeName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <icon-star v-if="item.typeName.includes('收藏')" class="star"/>
                          {{ item.typeName }}
                        </div>
                        <div v-if="item.eventDisplayName" class="list-box" :class="{ 'list-active': item.eventDisplayName == selectObject.eventDisplayName, }"
                             style="height: 30px; width: calc(100% - 16px)" @click="itemChange(item)">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                            <div>
                              <span class="desc1">{{ item.eventDisplayName }}</span>
                              <span class="desc2">{{ item.eventName }}</span>
                              <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                <div class="icon" @click.stop="itemClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                              <a-tooltip v-else content="取消收藏" position="top">
                                <div class="isStar" @click.stop="cancelItemClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24"
                                          fill="currentColor"
                                      >
                                        <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                          <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                        </svg>
                                      </svg>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.eventDisplayName }}</div>
                                    </div>
                                    <div v-if="item.eventType === 1" class="header-type">预置事件</div>
                                    <div v-if="item.eventType === 2" class="header-type">自定义事件</div>
                                    <div v-if="item.eventType === 3" class="header-type">虚拟事件</div>
                                    <div v-if="item.eventType === 'event'" class="header-type">事件指标</div>
                                    <div v-else class="header-type">留存指标</div>
                                  </div>
                                  <div class="title-sub">{{ item.eventName }}</div>
                                </div>
                                <div class="card-desc">
                                  <div v-if="item.eventNote" class="span-desc">{{ item.eventNote }}</div>
                                  <div v-else class="span-desc">暂无备注</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <a-tooltip v-if="item.type === 'indicator'" content="前往指标详情" position="top">
                                      <div class="action-icon" @click="goIndexItem(item.eventCode)">
                                        <icon-menu/>
                                      </div>
                                    </a-tooltip>
                                    <a-tooltip v-if="item.type === 'event'" content="前往事件详情" position="top">
                                      <div class="action-icon" @click="toEventDetail">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>

                                    <a-tooltip v-else content="前往指标列表" position="top">
                                      <div class="action-icon" @click="toIndexList">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </div>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-trigger>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {analyseStore} from '@/store';
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

const analyseData = analyseStore();

  const triggerVisible = ref(false);
  const emits = defineEmits(['analysisIndexChange']);
  const handleTriggerVisible = () => {
    triggerVisible.value = !triggerVisible.value;
  };
  const props = defineProps({
    panelData: {
      type: Object,
      default() {
        return {};
      },
    },
  });
  // 前往事件详情
  const toEventDetail = () =>{
    router.push({name: ROUTE_NAME.SETTING_ANALYSE_EVENT})
  }
  // 前往指标列表
  const toIndexList = () => {
    router.push({name:ROUTE_NAME.SETTING_ANALYSE_INDICATOR})
  }
  // 前往指标详情
  const goIndexItem = (codeId?:string) => {
    router.push({
      name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR_CREATE,
      query: { code: codeId },
    });
  };
  const selectObject = ref({
    eventName: props.panelData.eventName || '',
    eventDisplayName:props.panelData.eventDisplayName || '',
    eventType:props.panelData.eventType || '',
    eventCode:props.panelData.eventCode || '',
    type: props.panelData.type || 'event', // 事件 | 指标
    eventNote:'' // 备注
  });

//   原 事件数组
  const panelList = ref<any[]>([
    {
      categoryName: '收藏的事件',
      itemData: [],
    },
    {
      categoryName: '基础事件',
      itemData: [],
    }
  ]);
  interface EventItem {
    eventCode: string;
    eventName: string;
    eventDisplayName: string;
    eventType: string;
    eventNote: string;
    isCollect: boolean;
  }
  interface CategoryItem {
    categoryName: string;
  }
  interface MertricItem {
    mertricName: string;
  }
  type PanelItem = CategoryItem | EventItem;

  const mertricList = ref<any>([
    {
      mertricName: '收藏的指标',
      itemData: [],
    },
    {
      mertricName: '指标',
      itemData: [],
    },
  ]);
  const searchInput = ref('')
// 虚拟列表  事件数组
  const virtualPanelList = ref<PanelItem[]>([]);
// 虚拟列表  指标数组
  const virtualMertricList = ref<PanelItem[]>([]);
  const getVirtualPanelList = (arr) => {
    virtualPanelList.value = arr.flatMap((category) => {
      const categoryItem: CategoryItem = {
        categoryName: category.categoryName,
      };
      return [categoryItem, ...category.itemData];
    });
    
  }
  const getVirtualMertricList = (arr) => {
    virtualMertricList.value = arr.flatMap((mertric) => {
      const mertricItem: MertricItem = {
        mertricName: mertric.mertricName,
      };
      return [mertricItem, ...mertric.itemData];
    });
  }
  const getEventList = async () => {
    const data = analyseData.$state.evtLists.length > 0 ? analyseData.$state.evtLists : await analyseData.fetchEvtInfo();
    if (data && data.length) {
      panelList.value[1].itemData = data.map((item, index) => {
        return {
          eventName: item.eventName,
          eventDisplayName:item.eventDisplayName,
          eventType: item.eventType,
          eventCode:item.eventCode,
          eventNote: item.eventNote,
          isCollect: false,
        };
      });
    }
    getVirtualPanelList(panelList.value)
  };
  // getEventList();

  const getMertricList = async () => {
    const data = analyseData.$state.indLists.length > 0 ? analyseData.$state.indLists : await analyseData.fetchIndInfo();
    if (data && data.length) {
      mertricList.value[1].itemData = data.map((item, index) => {
        return {
          eventName: item.name,
          eventDisplayName:item.displayName,
          eventType: item.type,
          eventCode:item.code,
          eventNote: item.description,
          isCollect: false,
        };
      });
    }
    getVirtualMertricList(mertricList.value)
  }
  // getMertricList()
//   事件容器，左右点击滚动效果联动
  const activeIndex = ref(0)
  const virtualList = ref(null);
  const selectListRef = ref(null);
  const categoryChange = async (categoryName: string,index:number) => {
    await nextTick()
    activeIndex.value = index;
    const targetIndex = virtualPanelList.value.findIndex(item => item.categoryName === categoryName);
    if (targetIndex !== -1) {
      virtualList.value.scrollIntoView({index:targetIndex});
    }

  };
  
  const handleScroll = () => {
    const scrollContainer = document.querySelector('.arco-virtual-list');
    if (!scrollContainer) return;

    const {scrollTop} = scrollContainer; // 获取 scrollTop
    const items = scrollContainer.querySelectorAll('.list-name');
    if (items && items.length>0) {
      let sIndex = 0
      items.forEach((item,index) => {
        if(scrollTop+60 >= item.offsetTop){
        panelList.value.forEach((el,num) => {
          if(item.textContent.trim() === el.categoryName){
            sIndex = num
          }
        })
        }
      })
      activeIndex.value = sIndex
    }
  };
  onMounted(() => {
    // 初始化 categoryName
    if (panelList.value && panelList.value.length > 0) {
      activeIndex.value = 0
    }

    // 添加滚动事件监听
    const scrollContainer = document.querySelector('.arco-virtual-list');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
    }
  });
  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    const scrollContainer = document.querySelector('.arco-virtual-list');
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', handleScroll);
    }
  });
//   事件点击
  const listChange = (item: any) => {
    selectObject.value = {
      ...selectObject.value,
      eventName: item.eventName,
      eventDisplayName: item.eventDisplayName,
      type: 'event',
      eventType: item.eventType,
      eventCode: item.eventCode,
      eventNote: item.eventNote,
    }
    triggerVisible.value = false;
    emits('analysisIndexChange', selectObject.value);
  };
//   指标点击
  const mertricListChange = (item: any) => {
    selectObject.value = {
      ...selectObject.value,
      eventName: item.eventName,
      eventDisplayName: item.eventDisplayName,
      type: 'indicator',
      eventType: item.eventType,
      eventCode: item.eventCode,
      eventNote: item.eventNote,
    }
    triggerVisible.value = false;
    emits('analysisIndexChange', selectObject.value);
  };
const flattenData = (array) => {
  const data = array.reduce((acc, item) => {
    const key = item.filterType; // 使用 type 作为分组的键
    if (!acc[key]) {
      acc[key] = []; // 如果没有该键，初始化为一个空数组
    }
    acc[key].push(item); // 将当前项添加到对应的分组中
    return acc;
  }, {});
  const flattenedArray = [];
  // 处理收藏事件
  if (data.eventCollect) {
    flattenedArray.push({ typeName: '收藏的事件' });
    flattenedArray.push(...data.eventCollect);
  }
  // 处理收藏指标
  if (data.indicatorCollect) {
    flattenedArray.push({ typeName: '收藏的指标' });
    flattenedArray.push(...data.indicatorCollect);
  }
  // 处理事件
  if (data.event) {
    flattenedArray.push({ typeName: '事件' }); // 添加事件属性类型
    flattenedArray.push(...data.event); // 添加事件数据
  }
  // 处理指标
  if (data.indicator) {
    flattenedArray.push({ typeName: '指标' });
    flattenedArray.push(...data.indicator);
  }

  return flattenedArray;
};
  // 搜索时数据
  const filteredLists = computed(() => {
    const str = searchInput.value.trim();
    const pclist = panelList.value[0].itemData.map(item => {
      return {
        ...item,
        type: 'event',
        filterType:'eventCollect'
      }
    })
    const plist = panelList.value[1].itemData.map(item => {
      return {
        ...item,
        type: 'event',
        filterType:'event'
      }
    })
    const mclist = mertricList.value[0].itemData.map(item => {
      return {
        ...item,
        type: 'indicator',
        filterType:'indicatorCollect'
      }
    })
    const mlist = mertricList.value[1].itemData.map(item => {
      return {
        ...item,
        type: 'indicator',
        filterType:'indicator'
      }
    })
    const list = [...pclist, ...plist, ...mclist, ...mlist].filter(item => item?.eventDisplayName?.includes(str) || item?.eventName?.includes(str));
    
    return flattenData(list)
  });
  // 搜索点击
  const itemChange = (item:any) => {
    selectObject.value = {
      ...selectObject.value,
      eventName: item.eventName,
      eventDisplayName: item.eventDisplayName,
      type: item.type,
      eventCode: item.eventCode,
    }
    triggerVisible.value = false;
    emits('analysisIndexChange', selectObject.value);
  }
  // 事件收藏
  const starClick = (item: any) => {
    const existingItem = panelList.value[0].itemData.find(
      (el) => el.eventCode === item.eventCode
    );
    if (!existingItem) {
      // item.isCollect = true; // 标记为已收藏
      panelList.value[1].itemData.forEach(pp => {
        if(pp.eventCode === item.eventCode){
          pp.isCollect = true;
        }
      })
      panelList.value[0].itemData.push(item); // 仅在不存在时添加
      getVirtualPanelList(panelList.value)
    }
  };
  // 取消事件收藏
  const cancelClick = (item: any) => {
    panelList.value[1].itemData.forEach(pp => {
      if(pp.eventCode === item.eventCode){
        pp.isCollect = false;
      }
    })
    panelList.value[0].itemData = panelList.value[0].itemData.filter(
      (el) => el.eventCode !== item.eventCode
    );
    getVirtualPanelList(panelList.value)
  };
  // 收藏指标
  const mertricClick = (item: any) => {
    const existingItem = mertricList.value[0].itemData.find(
      (el) => el.eventCode === item.eventCode
    );
    if (!existingItem) {
      // item.isCollect = true; // 标记为已收藏
      mertricList.value[1].itemData.forEach(pp => {
        if(pp.eventCode === item.eventCode){
          pp.isCollect = true;
        }
      })
      mertricList.value[0].itemData.push(item); // 仅在不存在时添加
      getVirtualMertricList(mertricList.value)
    }
  };
  // 取消指标收藏
  const cancelMertricClick = (item: any) => {
    mertricList.value[1].itemData.forEach(pp => {
      if(pp.eventCode === item.eventCode){
        pp.isCollect = false;
      }
    })
    mertricList.value[0].itemData = mertricList.value[0].itemData.filter(
      (el) => el.eventCode !== item.eventCode
    );
    getVirtualMertricList(mertricList.value)
  };
  const itemClick = (item:any) => {
    if(item.type === 'event') {
      starClick(item)
    }else{
      mertricClick(item)
    }
  }
  const cancelItemClick = (item:any) => {
    if(item.type === 'event') {
      cancelClick(item)
    }else{
      cancelMertricClick(item)
    }
  }
  watch(
    () => props.panelData,
    (newVal) => {
      selectObject.value.eventName = newVal.eventName || '';
      selectObject.value.eventDisplayName = newVal.eventDisplayName || '';
      selectObject.value.eventType = newVal.eventType || '';
      selectObject.value.eventCode = newVal.eventCode || '';
      selectObject.value.type = newVal.type || '';
      getEventList();
      getMertricList()
    },
    { immediate: true }
  );
</script>

<style scoped lang="less">
.fixed-modal{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}
.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;
  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
    flex-shrink: 0;
  }
  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}
.select-panel {
  position: relative;
  width: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-bottom);
}
.list-container {
  position: relative;
  display: flex;
  flex-direction: row;
  .list-category {
    height: 350px;
    position: relative;
    flex-shrink: 0;
    width: 100px;
    padding: 10px 0 0 10px;
    border-right: 1px solid var(--tant-border-color-border1-1);
    .category-container {
      height: calc(100% - 32px);
      overflow-y: auto;
      .category-item {
        width: 100%;
        margin-bottom: 10px;
        padding: 2px 0 2px 2px;
        color: var(--tant-text-gray-color-text1-3);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .item-active {
        font-weight: 500;
        border-right: 2px solid var(--tant-primary-color-primary-default);
      }
    }
    .arco-btn {
      margin-left: -10px;
      width: 100px;
      border-radius: 0 0 0 4px;
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-2);
      text-shadow: none;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: 1px solid var(--tant-border-color-border1-1);
      box-shadow: none;
      border-left: none;
      border-bottom: none;
    }
  }
  .item-list {
    flex: 1 1;
    .select-list {
      // position: relative;
      height: 350px;
      width: 260px;
      overflow: auto;
      will-change: transform;
      direction: ltr;
      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }
      :deep(.arco-list-bordered) {
        border: none;
      }
    }
    .list-group {
      width: 100%;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }
    .favorite {
      height: 30px;
      width: 100%;
      padding-left: 18px;
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
    }
    .list-name {
      border-top: 1px solid var(--tant-border-color-border1-1);
      margin-top: 8px;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      height: 45px;
      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }
    //.list-content:first-child .list-name{
    //    border-top: none;
    //    margin-top: 0;
    //}
    .list-box {
      margin: 0 8px;
      padding-left: 12px;
      border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 28px;
      cursor: pointer;
      .desc {
        display: inline-block;
        width: 180px;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .desc1,.desc2 {
        display: inline-block;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .desc1{
        width: 140px;
      }
      .desc2{
        width: 120px;
      }
      .icon {
        opacity: 0;
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
      }
      .icon:hover {
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
        .icon {
          opacity: 100;
          pointer-events: all;
        }
      }
      .isStar {
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
        opacity: 100;
        pointer-events: all;
      }
    }
    .list-active {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }
    .list-metric {
      position: relative;
      height: 350px;
      width: 360px;
      overflow: auto;
      will-change: transform;
      direction: ltr;
      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }
      :deep(.arco-list-bordered) {
        border: none;
      }
    }
  }
}
.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);
  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;
    .card-header-container {
      display: flex;
      flex-direction: row;
      .header-icon {
        margin-top: 2px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }
      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);
        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }
      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }
    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }
  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;
    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }
  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;
    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}
</style>