<template>
    <!-- 修改广告单元分地区出价 -->
    <a-modal v-model:visible="modalVisible" :width="940" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-tabs v-model:active-key="activeName" @change="handleTabChange">
            <a-tab-pane key="bid" title="分地区(1)">
                <a-form ref="formRef" :model="form" :auto-label-width="true" style="height: 420px;overflow: auto;">
                    <a-form-item field="newBudget" label="新D7 ROAS目标">
                        <a-radio-group v-model="form.newBudget" type="button">
                            <a-radio value="1">固定值</a-radio>
                            <a-radio value="2">增加</a-radio>
                            <a-radio value="3">减少</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item field="nums">
                        <a-select v-model="form.budgetType" placeholder="请选择" style="margin-right: 12px;width: 100px;">
                            <a-option value="number">数值</a-option>
                            <a-option v-if="form.newBudget !== '1'" value="percent">百分比</a-option>
                        </a-select>
                        <a-input-number v-model="form.nums" :min="0" style="width: 200px;"/>
                    </a-form-item>
                    <a-form-item field="tableData">
                        <a-table
                            :columns="bidColumns"
                            :data="form.tableData"
                            :hoverable="true"
                            sticky-header
                            :table-layout-fixed="true"
                            :column-resizable="true"
                            :pagination="false"
                        >
                            <template #area="{ rowIndex }">
                                <a-select v-model="form.tableData[rowIndex].area" placeholder="请选择" multiple :max-tag-count="1" :tag-nowrap="true" allow-search style="width: 100%;">
                                    <a-option value="1">美国</a-option>
                                    <a-option value="2">日本</a-option>
                                </a-select>
                            </template>
                            <template #newRoas="{ rowIndex }">
                                <a-input v-model="form.tableData[rowIndex].newRoas" allow-clear>
                                    <template #append>
                                        %
                                    </template>
                                </a-input>
                            </template>
                            <template #actions="{ record, rowIndex }">
                                <a class="actions-btn" @click="handleBatchAddArea(rowIndex)">批量添加地区</a>
                                <a class="actions-btn" @click="handleAddRow">添加行</a>
                                <a class="actions-btn" @click="handleDeleteRow(rowIndex)">删除</a>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="unable" title="不能修改(1)">
                <a-table
                    :columns="unableColumns"
                    :data="unableTableData"
                    :hoverable="true"
                    sticky-header
                    :table-layout-fixed="true"
                    :column-resizable="true"
                    :pagination="false"
                    style="height: 420px;"
                >
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <div class="footer">
            <div class="footer-left">
                <a-checkbox>
                    设置为定时任务
                    <a-tooltip placement="top" content="设置定时任务后，可在任务中心的批量编辑中找到相关任务">
                        <icon-question-circle style="margin-left: 8px;"/>
                    </a-tooltip>
                </a-checkbox>
            </div>
            <div class="footer-right">
                <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
                <a-button type="primary" :loading="loading" @click="saveData">
                    保存
                </a-button>
            </div>
        </div>
        <a-modal v-model:visible="importVisible" title="批量导入" width="650" title-align="start" :footer="false">
            <a-textarea v-model="importData" placeholder="支持以名称或2位地区代码批量导入，以英文逗号(,)、回车区隔" :auto-size="{ minRows: 4, maxRows: 6 }" />
            <div class="footer">
                <div class="footer-left">
                </div>
                <div class="footer-right">
                    <a-button style="margin-right: 10px;" class="cancel" @click="cancelImport">取消</a-button>
                    <a-button type="primary" @click="sureImport">
                        确定
                    </a-button>
                </div>
            </div>
        </a-modal>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改出价')
const activeName = ref('bid')
const form = reactive({
    newBudget:'1',
    budgetType:'number',
    nums:undefined,
    tableData:[
        {
            adUnitName:'10093_MTG_ColorBlock_20241011',
            roas:'26',
            newRoas:'',
            area:[]
        },
    ]
})
const bidColumns = ref<any>([
    { title: '广告单元', dataIndex: 'adUnitName',ellipsis: true,tooltip: true },
    { title: '地区', dataIndex: 'area',slotName:'area' },
    { title: 'D7 ROAS目标', dataIndex: 'roas' },
    { title: '新D7 ROAS目标', dataIndex: 'newRoas',slotName:'newRoas'},
    { title: '操作', dataIndex: 'actions',slotName:'actions',width:240 },
])
const unableColumns = ref<any>([
    { title: '广告单元', dataIndex: 'adUnitName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '不可修改原因', dataIndex: 'reason',minWidth:180 },
])
const unableTableData = ref<any>([
    {
        adUnitName:'10093_MTG_ColorBlock_20241011',
        reason:'广告单元包含已暂停的广告',
    },
])
const formRef = ref()

const importVisible = ref(false)
const importData = ref('')

const emits = defineEmits(['updateData']);

const handleTabChange = (key) => {
    if (key === 'target') {
        form.newBudget = '1'
        form.budgetType = 'number'
        form.nums = undefined
    }
}
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}
const handleAddRow = () => {
    form.tableData.push(
        {
            adUnitName:'',
            roas:'',
            newRoas:'',
            area:[]
        },
    )
}
const handleDeleteRow = (index: number) => { 
    form.tableData.splice(index, 1)
}
const importIndex = ref(0)
const handleBatchAddArea = (index: number) => {
    importData.value = ''
    importIndex.value = index
    importVisible.value = true
}
const cancelImport = () => { 
    importVisible.value = false
}
const sureImport = () => { 
    if (!importData.value) {
        importVisible.value = false
        return
    }
    // 将文本按换行符分割并过滤空值
    const values = [...new Set(importData.value.split('\n').filter(item => item.trim()))]
    form.tableData[importIndex.value].area = values
    importVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.tip{
    white-space: pre;
    text-align: right;
}
.actions-btn{
  color: #0b75ff;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color .2s ease;
  margin-right: 8px;
}
</style>