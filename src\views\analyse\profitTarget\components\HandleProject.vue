<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%;">
            <a-form-item field="profitTarget" label="利润目标">
                <a-input-number v-model="form.profitTarget" :min="0"/>
            </a-form-item>
            <a-form-item field="teamCode" label="项目组">
                <a-select v-model="form.teamCode" placeholder="请选择项目组" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in projectTeam" :key="item.code" :value="item.code" :disabled="disabledTeams.has(item.code)">{{ item.name }}</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, inject, reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {getOpTeamList} from "@/api/marketing/api";
import {saveProfitTarget} from "@/api/analyse/api";

const modalVisible = ref(false)
const modalTitle = ref('添加项目组')

const props = defineProps({
    tableData: {
        type: Array,
        default: () => ([])
    },
})
// 添加计算属性来处理禁用逻辑
const disabledTeams = computed(() => {
    return new Set(props.tableData.map((item: any) => item.code));
});
const form = reactive({
    profitTarget:undefined,
    teamCode:''
})
const year = ref(inject('year'));
const projectTeam = ref()
const rules = {
    profitTarget: [
        {
            required: true,
            message:'请填写利润目标',
        }
    ],
    teamCode: [
        {
            required: true,
            message:'请选择项目组',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);

const getList = async () => {
    await getOpTeamList().then(res => {
        projectTeam.value = res
    })
}

const openModal = async (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    await getList()
    modalVisible.value = true
}

const closeModal = () => {
    modalVisible.value = false
}

const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            loading.value = true
            try {
                const data = {
                    label:'team',
                    code:form.teamCode,
                    year:year.value,
                    target:form.profitTarget
                }
                await saveProfitTarget(data)
                modalVisible.value = false;
                Message.success('保存成功');
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }finally {
                loading.value = false
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>