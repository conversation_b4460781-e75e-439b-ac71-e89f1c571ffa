import { ref, onMounted, onUnmounted } from 'vue'
import { getIndicatorList } from '@/api/setting/api'
import { isLogin } from '@/utils/auth'

/**
 * 请求指标数据的组合式函数
 * 在登录状态下首次请求指标列表并缓存到 sessionStorage
 */
export default function useIndicatorPolling() {
  const isLoading = ref(false)
  const indicatorData = ref<any[]>([])

  // 从 sessionStorage 获取缓存的指标数据
  const getStoredIndicatorData = () => {
    try {
      const storedData = sessionStorage.getItem('indicator-list')
      if (storedData) {
        indicatorData.value = JSON.parse(storedData)
      }
    } catch (error) {
      console.warn('读取缓存的指标数据失败:', error)
    }
  }

  // 将指标数据保存到 sessionStorage
  const saveIndicatorData = (data: any[]) => {
    try {
      sessionStorage.setItem('indicator-list', JSON.stringify(data))
      indicatorData.value = data
    } catch (error) {
      console.error('保存指标数据到缓存失败:', error)
    }
  }

  // 请求指标列表数据
  const fetchIndicatorList = async () => {
    if (!isLogin()) {
      return
    }
    try {
      isLoading.value = true
      const response = await getIndicatorList({})
      if (response) {
        saveIndicatorData(response)
      }
    } catch (error) {
      console.error('请求指标列表失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 请求指标数据
  const fetchIndicatorData = async () => {
    if (!isLogin()) {
      return
    }
    if (isLoading.value) {
      return
    }
    await fetchIndicatorList()
  }

  // 重置加载状态
  const resetLoadingState = () => {
    isLoading.value = false
  }

  // 手动刷新指标数据
  const refreshIndicatorData = () => {
    return fetchIndicatorList()
  }

  // 清除缓存的指标数据
  const clearIndicatorCache = () => {
    try {
      sessionStorage.removeItem('indicator-list')
      indicatorData.value = []
    } catch (error) {
      console.error('清除指标数据缓存失败:', error)
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    // 先加载缓存数据
    getStoredIndicatorData()
    // 如果用户已登录且缓存为空，则请求数据
    if (isLogin() && indicatorData.value.length === 0) {
      fetchIndicatorData()
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    resetLoadingState()
  })

  return {
    isLoading,
    indicatorData,
    fetchIndicatorData,
    resetLoadingState,
    refreshIndicatorData,
    clearIndicatorCache,
    getStoredIndicatorData
  }
}