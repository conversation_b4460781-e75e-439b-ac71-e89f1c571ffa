<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="显示名">
                <a-input v-model="form.name"/>
            </a-form-item>
            <a-form-item field="description" label="描述">
                <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addOpTagList} from "@/api/marketing/api";


const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    code:'',
    name: '',
    description:''
})
const rules = {
    name: [
        {
            required: true,
            message:'请填写显示名',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);
const openModal = (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    
    if(obj){
        const {code,name,description} = obj
        form.code = code
        form.name = name
        form.description = description
        modalTitle.value = '编辑应用标签'
    }else{
        form.code =''
        modalTitle.value = '创建应用标签'
    }
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            try {
                await addOpTagList(form);
                if(form.code){
                    Message.success('保存成功')
                }else{
                    Message.success('创建成功');
                }
                modalVisible.value = false;
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>