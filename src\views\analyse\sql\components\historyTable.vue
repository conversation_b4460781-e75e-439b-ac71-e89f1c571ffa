<script setup lang="ts">
import {defineEmits, defineExpose, ref} from 'vue';
import {getAnalyseHistoryList} from "@/api/analyse/api";

const emit = defineEmits<{
  (event: 'history', data: any,sqlParams:any): void;
}>();

const data = ref([]);

const formatTimestamp= (timestamp:any,s?:boolean)=> {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

const refreshData = async ()=>{
  await getAnalyseHistoryList().then((res) =>{
    data.value = res.map((item,index) => {
      return {
        finishedTime:formatTimestamp(item.endTime),
        costTime:item.endTime-item.startTime,
        sql:item.queryParam.sql,
        index,
        queryParam:item.queryParam.sqlParams
      }
    })
  })
}



defineExpose({
  refreshData
})

const historyEmit = (value:any,sqlParams:any) =>{
  emit('history',value,sqlParams)
}

</script>

<template>
  <div style="margin: 20px 25px 45px 25px;">
    查看最近的{{data.length}} 条查询历史 <br><br>
    <a-table :data="data">
      <template #columns>
        <a-table-column title="序号" data-index="rowIndex" :min-width="100" align="center">
          <template #cell="{ record }">
            {{record.index+1}}
          </template>
        </a-table-column>
        <a-table-column title="查询时间" data-index="finishedTime" :min-width="200" align="left"></a-table-column>
        <a-table-column title="查询耗时(ms)" data-index="costTime" :min-width="200" align="center"></a-table-column>
        <a-table-column title="查询语句" data-index="sql" :min-width="600" align="left"></a-table-column>
        <a-table-column title="操作" :min-width="100" align="left">
          <template #cell="{ record }">
            <a-tooltip content="设置" position="top">
              <icon-settings style="cursor: pointer;font-size: 16px;" @click="historyEmit(record.sql,record.queryParam)"/>
            </a-tooltip>
            <a-tooltip content="查看" position="top">
              <icon-eye style="margin-left: 10px;cursor: pointer;font-size: 16px;" />
            </a-tooltip>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="less">

</style>