import service from '@/api/axios.config'
import {AxiosPromise, AxiosResponse} from "axios";
import {ResponseData} from "@/api/type";
import {convertKeysToSnakeCase} from "@/utils/strUtil";

// 封装通用的GET请求
export function getRequest<T>(url: string, params: any = undefined): AxiosPromise<T> {
  return service.get<ResponseData<T>, AxiosResponse<T>>(url, {
    method: 'get',
    params: convertKeysToSnakeCase(params)
  });
}

// 封装通用的POST请求
export function postRequest<T>(url: string, data: any = undefined): AxiosPromise<T> {
  return service.post<T, AxiosResponse<T>>(url, convertKeysToSnakeCase(data), {
    method: 'post'
  });
}

// 封装上传文件的POST请求
export function uploadRequest<T>(url: string, data: any = undefined): AxiosPromise<T> {
  return service.post<T, AxiosResponse<T>>(url, data, {
    method: 'post',
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

export default service;
