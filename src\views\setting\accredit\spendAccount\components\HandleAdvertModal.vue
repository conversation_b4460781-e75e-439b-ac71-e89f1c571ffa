
<template>
    <!-- meta编辑广告账户 -->
    <a-modal v-model:visible="modalVisible" :width="560" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules">
            <a-form-item field="channel" label="渠道" validate-trigger="blur">
                facebook
            </a-form-item>
            <a-form-item field="belongPerson" label="所属人员" validate-trigger="change">
                <a-cascader v-model:model-value="form.belongPerson" :options="belongOptions" />
            </a-form-item>
            <a-form-item field="assistPerson" label="协助人员">
                <a-cascader v-model:model-value="form.assistPerson" :options="belongOptions" />
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('编辑广告账户')
const form = reactive({
    channel:'',
    assistPerson:'',
    belongPerson:'',
})
const rules = {
    belongPerson: [
        {
            required: true,
            message:'请选择所属人员',
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const belongOptions = [
    {
        value: '1',
        label: 'admin',
        children: [
            {
                value: '1-1',
                label: '李总',
            },
            {
                value: '1-2',
                label: '李哥',
            },
        ],
    },
    {
        value: '2',
        label: 'manager',
        children: [
            {
                value: '2-1',
                label: '张总',
            },
        ],
    },
];
const openModal = async (channel?:string) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.href-name {
    text-decoration: underline;
    color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: auto;
    cursor: pointer;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    // button{
    //     border-radius: 4px;
    // }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>