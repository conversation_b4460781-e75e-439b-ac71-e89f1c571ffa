// import axios from 'axios';
// import type { TableData } from '@arco-design/web-vue/es/table/interface';
import {AxiosPromise} from "axios";
import {getRequest, postRequest} from "@/api/request";
import {ReportDto} from "@/api/report/type";
import {dashboardCopyRequest, DashboardDto, DashboardSaveDto} from "@/api/dashboard/type";
import _ from "lodash";

// export function saveDashboard(dashboard: DashboardSaveDto): AxiosPromise<any> {
//   return postRequest<any>('/api/dashboard/save', {...dashboard, 'dashboardName': dashboard.name});
// }
// 节流后的 saveDashboard 函数，确保每 300 毫秒内最多执行一次请求
export const saveDashboard = _.throttle(
    (dashboard: DashboardSaveDto): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/save', { ...dashboard, dashboardName: dashboard.name });
    },
    300, // 节流间隔时间（毫秒），可以根据需要调整
    { trailing: false } // 配置项：关闭最后一次触发
);

// 节流后的 moveDashboard 函数
export const moveDashboard = _.throttle(
    (moveList: any[]): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/move', { move_list: moveList });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

// 节流后的 removeDashboard 函数
export const removeDashboard = _.throttle(
    (dashboardId: string[]): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/remove', { dashboardId });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);

// 节流后的 copyDashboard 函数
export const copyDashboard = _.throttle(
    (dashboard: dashboardCopyRequest): AxiosPromise<any> => {
      return postRequest<any>('/api/dashboard/copy', {
        ...dashboard,
        dashboardName: dashboard.name,
      });
    },
    300, // 节流间隔时间（毫秒）
    { trailing: false }
);
export function detailDashboard(dashboardId: string): AxiosPromise<DashboardDto> {
  return getRequest<any>('/api/dashboard/detail', {'dashboardId': dashboardId});
}

export function listDashboard(folderId: number,spaceId: number): AxiosPromise<DashboardDto> {
  return getRequest<any>('/api/dashboard/list', {'folderId': folderId,'spaceId': spaceId});
}

export function saveDashboardUI(config: any): AxiosPromise<DashboardDto> {
  return postRequest<any>('/api/dashboard/ui_config', config);
}
export function removeDashboardUI(config: any): AxiosPromise<DashboardDto> {
  return getRequest<any>('/api/dashboard/ui_config/remove', config);
}
export function copyReportUI(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/analyse/report/copy', config);
}
export function saveNote(config: any): AxiosPromise<DashboardDto> {
  return postRequest<any>('/api/dashboard/note/save', {...config});
}

export function delNote(dashboardId: string,noteId: string): AxiosPromise<DashboardDto> {
  return getRequest<any>('/api/dashboard/note/remove', {'dashboardId': dashboardId,'noteId': noteId});
}

export function detailNote(noteId: string): AxiosPromise<ReportDto> {
  return getRequest<any>('/api/dashboard/note/detail', {'noteId': noteId});
}
// 看板分享
export function shareDashboard(config: any): AxiosPromise<DashboardDto> {
  return postRequest<any>('/api/dashboard/share', {...config});
}
// 应用分组
export function getGroupUsers(): AxiosPromise<ReportDto> {
  return getRequest<any>('/api/sys/member/group/users');
}
// 空间分享
export function shareDashboardSapce(config: any): AxiosPromise<DashboardDto> {
  return postRequest<any>('/api/dashboard/space/share', {...config});
}
// 看板分享详情
export function getDashboardShareDetail(id:string): AxiosPromise<ReportDto> {
  return getRequest<any>(`/api/dashboard/share/detail?dashboard_id=${id}`);
}
// 看板空间分享详情
export function getDashboardSpaceShareDetail(id:string): AxiosPromise<ReportDto> {
  return getRequest<any>(`/api/dashboard/space/share/detail?space_id=${id}`);
}
// 看板设置
export function setDashboard(config: any): AxiosPromise<DashboardDto> {
  return postRequest<any>('/api/dashboard/setting', {...config});
}