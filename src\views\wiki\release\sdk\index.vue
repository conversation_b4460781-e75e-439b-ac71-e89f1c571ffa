<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          日期范围：
          <a-range-picker
              v-model:model-value="dateLimit"
              :allow-clear="false"
              shortcuts-position="left"
              style="width: 240px; background-color: var(--tant-bg-white-color-bg1-1);border:1px solid var(--tant-border-color-border1-1) "
              :shortcuts="[
              {
                label: '过去一周',
                value: () => [dayjs().subtract(1, 'week'), dayjs()],
              },
              {
                label: '过去两周',
                value: () => [dayjs().subtract(2, 'week'), dayjs()],
              },
              {
                label: '过去一月',
                value: () => [dayjs().subtract(1, 'month'), dayjs()],
              }]"
          />
        </div>
        <div class="filter-item">
          检索内容：
          <a-input
              v-model:model-value="searchText"
              :style="{width:'240px'}"
              placeholder="请输入需要检索的文本"
              @press-enter="searchRecord"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchRecord">搜索</a-button>
        </div>
      </div>
    </div>
    <a-spin class="page-body" :loading="loading">
      <a-anchor v-if="!loading && Object.keys(releaseDateCountMap)?.length>0" line-less :change-hash="false" smooth scroll-container="scroll-container">
        <a-anchor-link v-for="item in releaseVersionList" :href="'#'+item.versionName"><span class="title"><b>{{ item.versionName }}</b></span></a-anchor-link>
      </a-anchor>
      <a-timeline v-if="!loading && releaseVersionList?.length>0" id="scroll-container" label-position="relative">
        <a-timeline-item v-for="item in releaseVersionList" :id="item.versionName" :key="item.releaseDate" class="timeline-item">
          <template #dot>
            <icon-history/>
          </template>
          <template #label>
            <div class="time"> {{ dayjs(item.createTime).format('YYYY-MM-DD hh:mm:ss') }}</div>
          </template>
          <div class="line">
            <div class="version-code"> {{ item.versionName }}</div>
            <a-tag size="small" :color="getStatusColor(item.status)">
              {{ getStatusName(item.status) }}
            </a-tag>
            <icon-edit class="edit-btn" @click="showEditVisible(item)"/>
          </div>
          <MdPreview :modelValue="item.releaseNotes"  previewTheme="smart-blue" />
        </a-timeline-item>
      </a-timeline>
      <a-empty v-if="!loading && releaseVersionList?.length<=0" class="empty">
        查询无果，暂无数据!
      </a-empty>
    </a-spin>
    <a-modal
        :visible="editVisible"
        title-align="start"
        title="编辑发布记录"
        width="660px"
        :mask-closable="true"
        :footer="false"
        @cancel="closeEditor">
      <div class="content-modal">
        <a-form layout="vertical" :model="editLogItem">
          <a-form-item field="versionName" label="版本号：" validate-trigger="blur">
            <a-input v-model:model-value="editLogItem.versionName" disabled placeholder="请输入版本号"/>
          </a-form-item>
          <a-form-item field="releaseNotes" label="发布说明：" validate-trigger="change">
            <div id="ckeditor" style="width: 100%;">
              <a-textarea v-model:model-value="editLogItem.releaseNotes" max-length="2000" show-word-limit :auto-size="{maxRows:20, minRows:4}"/>
            </div>
          </a-form-item>
        </a-form>
        <div class="footer">
          <a-button style="margin-right: 10px;" class="cancel" @click="closeEditor">取消</a-button>
          <a-button type="primary" @click="confirmEditor">
            保存
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import {useRoute} from "vue-router";
import {getReleaseVersionList, saveReleaseVersion} from "@/api/system/api";
import {onMounted, ref} from "vue";
import {SystemReleaseVersionDto} from "@/api/system/type";
import {Message} from "@arco-design/web-vue";

import {MdPreview} from 'md-editor-v3';
import 'md-editor-v3/lib/preview.css';

const route = useRoute();

/**
 * 发布日期计数映射
 */
const releaseDateCountMap = ref<{ [key: string]: number }>({});

/**
 * 搜索关键词
 */
const searchText = ref<string>();

/**
 * 加载中
 */
const loading = ref<boolean>(true);

/**
 * 编辑日志项
 */
const editLogItem = ref<SystemReleaseVersionDto>()

/**
 * 编辑弹窗是否显示
 */
const editVisible = ref<boolean>(false)

/**
 * 系统发布记录
 */
const releaseVersionList = ref<SystemReleaseVersionDto[]>([]);
const today = new Date().toISOString().split("T")[0];
/**
 * 日期范围
 */
const dateLimit = ref<string[]>([today, today]);

/**
 * 获取状态名称
 * @param status 状态
 * @returns 状态名称
 */
const getStatusName = (status: string) => {
  if (!status) {
    return status
  }
  switch (status.toLowerCase()) {
    case 'draft':
      return '草稿'
    case 'published':
      return '已发布'
    case 'deprecated':
      return '已下线'
    default:
      return status
  }
}
/**
 * 获取状态标签颜色
 * @param status 状态
 * @returns 状态标签颜色
 */
const getStatusColor = (status: string) => {
  if (!status) {
    return 'gray'
  }
  switch (status.toLowerCase()) {
    case 'draft':
      return 'gold'
    case 'published':
      return 'green'
    case 'deprecated':
      return 'red'
    default:
      return 'gray'
  }
}

/**
 * 搜索记录
 */
const searchRecord = () => {
  loading.value = true;
  getReleaseVersionList({
    platform: "sdk",
    pageNum: 1,
    pageSize: 1000,
    searchText: searchText.value,
    startDate: dateLimit.value[0],
    endDate: dateLimit.value[1]
  }).then(res => {
    const itemsSorted = res.items?.sort((a, b) => b.createTime - a.createTime);
    releaseVersionList.value = itemsSorted?.map(releaseVersionItem => {
      const changelog = releaseVersionItem?.releaseNotes || ''
      // 处理字符串为数组，过滤空行
      const logLines = changelog
          .split('\n')
          .map(line => line.trim())
          .filter(line => line);
      return {
        ...releaseVersionItem,
        logLines
      }
    });
    const releaseDateCountMapTemp = {}
    itemsSorted.forEach(item => {
      if (!releaseDateCountMapTemp[item.releaseDate]) {
        releaseDateCountMapTemp[item.releaseDate] = 0
      }
      releaseDateCountMapTemp[item.releaseDate] = releaseDateCountMapTemp[item.releaseDate] + 1
    })
    releaseDateCountMap.value = releaseDateCountMapTemp;
    loading.value = false
  })
}

/**
 * 显示编辑弹窗
 * @param item
 */
const showEditVisible = (item: SystemReleaseVersionDto) => {
  editLogItem.value = item
  editVisible.value = true
}

/**
 * 关闭编辑弹窗
 */
const closeEditor = () => {
  editLogItem.value = {}
  editVisible.value = false
}

/**
 * 确认编辑
 */
const confirmEditor = () => {
  if (!editLogItem.value?.versionCode) {
    return
  }
  saveReleaseVersion({
    versionCode: editLogItem.value.versionCode,
    releaseNotes: editLogItem.value.releaseNotes
  }).then(res => {
    Message.success('保存成功!')
    searchRecord()
    closeEditor()
  }).catch(err => {
    console.error(err)
    Message.error("保存失败! " + err.detail)
  })
}

onMounted(() => {
  searchRecord();
})
</script>

<style scoped lang="less">
@import '../style/default.less';
</style>