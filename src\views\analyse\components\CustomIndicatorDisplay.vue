<script setup lang="ts">
  import { watch, computed } from 'vue';
  import { splitFormula } from '@/utils/indicator-util';
  import { letterToNumber } from '@/utils/number-util';
  import _ from 'lodash';
  import EventScreenPopup from '@/views/analyse/components/EventScreenPopup.vue';
  import { toolStore } from '@/store';

  const props = defineProps<{
    // 指标数据
    indicator?: any;
    // 是否有悬浮效果
    allowHover?: boolean;
  }>();
  const toolData = toolStore();
  watch(
    () => props.indicator,
    (newVal) => {
      const codeList = props.indicator?.eventList?.filter((item: any) => item && item.eventCode).map((item: any) => item.eventCode) || [];
      toolData.addMultipleEvtIndCodes(codeList);
    },
    { immediate: true }
  );
  // 计算 eventItem 的映射

  const eventItem = (item: string) => {
    const index = letterToNumber(item);
    return /^[A-Z]$/.test(item) && index !== null ? props.indicator?.eventList?.[index] : null;
  };
  
  // 留存指标公式模板配置
  const retentionFormulaTemplates = {
    retention_rate: [
      { type: 'event', index: 1, suffix: '的总人数' },
      { type: 'text', content: '/' },
      { type: 'event', index: 0, suffix: '的总人数' },
      { type: 'text', content: '*' },
      { type: 'text', content: '100%' }
    ],
    retention_num: [
      { type: 'event', index: 1, suffix: '的总人数' }
    ],
    churn_rate: [
      { type: 'text', content: '1 - ' },
      { type: 'event', index: 1, suffix: '的总人数' },
      { type: 'text', content: '/' },
      { type: 'event', index: 0, suffix: '的总人数' },
      { type: 'text', content: '*' },
      { type: 'text', content: '100%' }
    ],
    churn_num: [
      { type: 'event', index: 0, suffix: '的总人数' },
      { type: 'text', content: ' - ' },
      { type: 'event', index: 1, suffix: '的总人数' }
    ]
  };
  
  // 计算当前显示模板
  const currentTemplate = computed(() => {
    const type = props.indicator?.retentionIndicatorType;
    return retentionFormulaTemplates[type as keyof typeof retentionFormulaTemplates] || null;
  });
</script>

<template>
  <div>
    <div v-if="indicator?.type != 'retention'" :class="allowHover ? 'formula-content' : 'formula-content formula-content-disable'">
      <div v-for="(item, index) in splitFormula(indicator.formula || 'A')" :key="index" class="formula-item">
        <div v-if="/^[A-Z]$/.test(item)" class="select-btn">
          <IconFont v-if="'event' === eventItem(item)?.type" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" />
          <IconFont v-if="'indicator' === eventItem(item)?.type" type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right: 4px" />
          <span class="btn-label"> {{ eventItem(item)?.eventName || eventItem(item)?.indicatorName }}-{{ eventItem(item)?.eventDisplayName || eventItem(item)?.indicatorDisplayName }} </span>
          <event-screen-popup :read-only="true" :info="eventItem(item)" :code-list="indicator?.eventList" />
          <span v-if="'event' === eventItem(item)?.type" class="btn-label">
            <span style="padding: 0 3px">的</span>
            <span v-if="_.isEmpty(eventItem(item)?.statisticalName)">
              {{ eventItem(item)?.eventAttrName }}
            </span>
            <span v-else> {{ eventItem(item)?.eventAttrDisplayName }}.{{ eventItem(item)?.statisticalName }} </span>
          </span>
        </div>
        <div v-else class="select-btn">
          <span class="btn-label">
            {{ item }}
          </span>
        </div>
      </div>
    </div>
    <div v-else>
      <div v-if="currentTemplate" :class="allowHover ? 'formula-content' : 'formula-content formula-content-disable'">
        <div v-for="(item, index) in currentTemplate" :key="index" class="formula-item">
          <div v-if="(item as any).type === 'event'" class="select-btn">
            <IconFont v-if="'event' === (indicator.eventList[(item as any).index])?.type" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" />
            <IconFont v-if="'indicator' === (indicator.eventList[(item as any).index])?.type" type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right: 4px" />
            <span class="btn-label">
              {{ (indicator.eventList[(item as any).index])?.eventName || (indicator.eventList[(item as any).index])?.indicatorName }}-{{ (indicator.eventList[(item as any).index])?.eventDisplayName || (indicator.eventList[(item as any).index])?.indicatorDisplayName }}
              {{ (item as any).suffix }}
            </span>
          </div>
          <div v-else-if="(item as any).type === 'text'" class="select-btn">
            <span class="btn-label">{{ (item as any).content }}</span>
          </div>
        </div>
      </div>
      <!-- 默认显示（同时展示或其他情况） -->
      <div v-else :class="allowHover ? 'formula-content' : 'formula-content formula-content-disable'">
        <div class="formula-item">
          <div class="select-btn">
            <IconFont v-if="'event' === (indicator.eventList[1])?.type" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" />
            <IconFont v-if="'indicator' === (indicator.eventList[1])?.type" type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right: 4px" />
            <span class="btn-label"> {{ (indicator.eventList[1])?.eventName || (indicator.eventList[1])?.indicatorName }}-{{ (indicator.eventList[1])?.eventDisplayName || (indicator.eventList[1])?.indicatorDisplayName }} </span>
          </div>
        </div>
        <div class="formula-item">
          <div class="select-btn">
            <IconFont v-if="'event' === (indicator.eventList[0])?.type" type="icon-icon-dianjifangkuang" :size="14" class="btn-icon" />
            <IconFont v-if="'indicator' === (indicator.eventList[0])?.type" type="icon-zhibiao" :size="14" class="btn-icon" style="margin-right: 4px" />
            <span class="btn-label"> {{ (indicator.eventList[0])?.eventName || (indicator.eventList[0])?.indicatorName }}-{{ (indicator.eventList[0])?.eventDisplayName || (indicator.eventList[0])?.indicatorDisplayName }} </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .formula-content {
    width: 100%;
    display: flex;
    cursor: pointer;
    flex-wrap: wrap;

    .formula-item {
      margin-right: 4px;
      margin-bottom: 4px;
      max-width: 100%;

      &:last-child {
        margin-right: 0;
      }
      
      // 修复省略号问题，增加最大宽度
      .btn-label {
        max-width: 300px;
      }
    }

    &:hover {
      .select-btn {
        background-color: var(--tant-bg-white-color-bg1-1);
        border-color: var(--tant-primary-color-primary-hover);
      }
    }
  }

  .formula-content-disable {
    cursor: default;

    &:hover {
      .select-btn {
        cursor: default;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
      }
    }
  }
</style>
