<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {onMounted, ref, watch} from "vue";

interface Props {
   /**
   * 数据
   */
  chartData: any

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()

const option = ref<any>();

const refresh = () => {
  const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#235894']
  const nameStyle = {}
  colorList.forEach((color, i) => {
    nameStyle[`name${i}`] = {
      color,
      padding: [0, 4, 0, 6],
      fontSize: 14,
      fontWeight: 900,
      lineHeight: 33
    }
  })
  
  // 获取最新日期的数据用于最终的可视化显示
  const latestDateIndex = props.chartData?.x?.length - 1 || 0;
  
  // 生成x轴数据（根据groups生成）
  const xData = props.chartData?.groups?.map(group => group.join('-')) || [];
  
  // 生成y轴数据（使用最新日期的total数据）
  const yData = [];
  if (props.chartData?.groups && props.chartData?.y) {
    props.chartData.groups.forEach(group => {
      // 查找对应的y数据项
      const matchingYData = props.chartData.y.find(yItem => 
        JSON.stringify(yItem.group) === JSON.stringify(group)
      );
      
      if (matchingYData) {
        yData.push(matchingYData.total?.[latestDateIndex] || 0);
      } else {
        yData.push(0);
      }
    });
  }
  
  const {chartOption} = useChartOption(() => {
    return {
      baseOption: {
        grid: {
          left: '36',
          right: '36',
          top: '16',
          bottom: '96'
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const {value, dataIndex} = params;
            // 根据数据索引获取对应的分组信息
            const group = props.chartData?.groups?.[dataIndex];
            const groupName = group?.join('-') || '未知';
            
            // 查找对应的y数据项获取变化率
            const matchingYData = props.chartData?.y?.find(yItem => 
              JSON.stringify(yItem.group) === JSON.stringify(group)
            );
            const diffRate = matchingYData?.totalDiffRate?.[latestDateIndex] || 0;
            const diffRateStr = diffRate < 0 ? `下降 ${-diffRate}%` : `上涨 ${diffRate}%`
            return `${groupName}<br/>${props.dataName}: ${value?.toLocaleString('en-US')}<br/>日环比: ${diffRateStr}<br/>`;
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter(value: number) {
              if (value >= 1000000) {
                return `${value / 1000000}M`; // 转换为 M 单位
              }
              if (value >= 1000) {
                return `${value / 1000}k`; // 转换为 k 单位
              }
              return value;
            }
          }
        },
        xAxis: {
          type: 'category',
          data: xData.map((name, i) => {
            // 处理过长的名称
            if (name?.length > 16) {
              name = name.slice(0, 13) + '...';
            }
            return name;
          }).slice(0, 50),
          axisLabel: {
            interval: 0, // 设置为0确保所有的标签都会显示
            align: 'center', // 调整文字对齐方式
            rotate: 60,  // 标签倾斜的角度（比如45度）
            margin: 48   // 调整与轴线的距离，单位是像素
          }
        },
        series: [
          {
            name: 'Direct',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'top',
              formatter(params) {
                if (!params.value || typeof params.value !== 'number') {
                  return params.value;
                }
                if (params.value >= 1000000) {
                  return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
                }
                if (params.value >= 1000) {
                  return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
                }
                return Math.round(params.value); // 保留整数
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: yData.slice(0, 50)
          }
        ]
      }
    };
  });
  option.value = chartOption.value
}

watch(props, () => {
  refresh()
})

onMounted(() => {
  refresh()
})

</script>

<template>
  <Chart :option="option"/>
</template>

<style scoped lang="less">
</style>