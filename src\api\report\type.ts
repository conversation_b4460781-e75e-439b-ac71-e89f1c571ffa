import {FieldType, TimeParticleSize, WsResultData} from "@/api/type";
import {UserDto} from "@/api/authorize/type";
import {ReportAnalyseModel} from "@/api/analyse/type";

/**
 * 报表实体
 */
export interface ReportDto {

  /**
   * 报表编码
   */
  'code'?: string

  /**
   * 报表类型
   */
  'model'?: ReportAnalyseModel

  /**
   * 报表名称
   */
  'name'?: string

  /**
   * 报表描述
   */
  'description'?: string

  /**
   * 查询参数
   */
  'queryParam'?: string

  /**
   * 查询sql
   */
  'queryStatement'?: string

  /**
   * 创建人
   */
  'creator'?: UserDto

  /**
   * 创建时间
   */
  'createTime'?: number

  /**
   * 更新时间
   */
  'updateTime'?: number
  /**
   * 图标数据
   */
  'chartParams'?: any

}

/**
 * 请求分组类型
 */
export enum QueryGroupFormat {
  // 按数值分组
  NUMBER = 'number',
  // 按文本分组
  STRING = 'string',
  // 按范围分组
  RANGE = 'range',
  // 按天分组
  DAY = 'day',
  // 按小时分组
  HOUR = 'hour',
  // 按分钟分组
  MINUTE = 'minute',
  // 按周分组
  WEEK = 'week',
  // 按月分组
  MONTH = 'month',
  // 按季度分组
  QUARTER = 'quarter',
  // 按年分组
  YEAR = 'year',
}

/**
 * 指标阶段性汇总接口
 */
export interface IndicatorStageSummary {
  /**
   * 平均值
   */
  mean: number;
  /**
   * 最大值
   */
  max: number;
  /**
   * 最大值所处日期时间
   */
  max_at: string[];
  /**
   * 最小值
   */
  min: number;
  /**
   * 最小值所处日期时间
   */
  minAt: string[];
  /**
   * 总和
   */
  sum: number;
  /**
   * 中位数（可选）
   */
  median?: number;
  /**
   * 众数（可选）
   */
  mode?: number;
  /**
   * 方差（可选）
   */
  variance?: number;
  /**
   * 标准差（可选）
   */
  standardDeviation?: number;
}

/**
 * 阶段数据接口
 */
export interface StageResultData {
  /**
   * 时间跨度
   */
  timeParticleSize: TimeParticleSize;
  /**
   * 开始时间
   */
  startTime: string;
  /**
   * 结束时间
   */
  endTime: string;
  /**
   * 横坐标值
   */
  xValue: string;
  /**
   * 纵坐标值
   */
  yValue: number;
}


/**
 * 指标查询结果数组纵坐标
 */
export interface IndicatorQueryResultData {

  /**
   * 当前分组
   */
  'group': Array<string>

  /**
   * 纵坐标值，当前分组查询结果集
   */
  'values': number[]

  /**
   * 对比时间段纵坐标值，当前分组查询结果集
   */
  'valuesCompared'?: Array<Array<number>>

  /**
   * 汇总数据
   */
  'summaryValue': IndicatorStageSummary

  /**
   * 对比时间段汇总数据
   */
  'summaryValueCompared'?: IndicatorStageSummary

  /**
   *  最新周期数据
   */
  lastData: StageResultData


  /**
   *  环比周期数据
   */
  huanBi?: StageResultData


  /**
   *  同比周期数据
   */
  tongBi?: StageResultData[]
}

/**
 * 报表纵坐标数据
 */
export interface IndicatorQueryResult {

  /**
   * 指标编码
   */
  indicatorCode?: string;

  /**
   * 指标名称
   */
  indicatorName?: string;

  /**
   * 展示名称
   */
  displayName: string;

  /**
   * 当前指标分组数量
   */
  groupNum: number;

  /**
   * 当前指标分组内容，无分组项则为总体
   */
  'groups': Array<Array<string>>

  /**
   * 当前指标纵坐标，查询结果数组
   */
  yData: IndicatorQueryResultData[]

}

/**
 * 分组描述
 */
export interface QueryGroupDesc {

  /**
   * 分组名称
   */
  name: string

  /**
   * 分组类型
   */
  format: QueryGroupFormat
}

/**
 * ws 事件分析响应数据
 */
export interface WsEventAnalysisResultData extends WsResultData {
  /**
   * 数据表列配置
   */
  'groupsDesc': QueryGroupDesc[]

  /**
   * 数据总行数
   */
  'totalNum': number

  /**
   * 分组数量
   */
  'groupNum': number

  /**
   * 分组内容，无分组项则为总体
   */
  'groups': Array<Array<string>>

  /**
   * 横坐标数据，时间日期数组
   */
  'x': string[]

  /**
   * 对比时间段横坐标，时间日期数组
   */
  'xComparedList': Array<Array<string>>

  /**
   * 纵坐标，查询结果数组
   */
  'y': IndicatorQueryResult[]
}

// 定义 DataField 接口
export interface DataField {
  // 字段名称，必填
  name: string;
  // 字段类型，必填，是 FieldType 枚举类型中的一种
  type: FieldType;
  // 字段在数据库中的原始类型，可选
  original_type?: string;
  // 来源数据库名称，可选
  catalog?: string;
  // 来源数据库结构，可选
  schema?: string;
  // 来源数据表名称，可选
  table?: string;
}

/**
 * ws 自定义分析响应数据
 */
export interface WsCustomAnalysisResultData extends WsResultData {
  /**
   * 查询结果列名称
   */
  'headers': string[]

  /**
   * 查询结果数据
   */
  'rows': Array<Array<any>>

  /**
   * 数据总行数
   */
  'lineNumber': number

  /**
   * 数据字段
   */
  'dataFields': DataField[]
}
