import {isEmpty} from "lodash";
import {h} from "vue";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

/**
 *
 * @param dashboard
 */
export const reportSaveSuccessMessage = (dashboard: string | undefined) => isEmpty(dashboard)
  ? h('span', [
    '报表已保存'
  ])
  : h('span', [
    '报表已保存并添加至看板，',
    h('a', {
      style: {color: 'rgb(var(--primary-6))', cursor: 'pointer'},
      onClick: () => router.push({
        name: ROUTE_NAME.DASHBOARD,
      }),
    }, '去看板查看')
  ]);