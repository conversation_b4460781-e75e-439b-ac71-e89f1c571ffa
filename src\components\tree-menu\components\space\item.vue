<script setup lang="ts">
import {SpaceDto} from "@/api/space/type";
import {onMounted, ref} from "vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {useDashboardStore} from "@/store";
import {removeSpace, saveSpace} from "@/api/space/api";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {Message} from "@arco-design/web-vue";
import _ from "lodash";
import shareSpace from "./ShareSpace.vue"
import screenSpace from "./screen-space.vue";
import folder from "../folder/index.vue";
import Dashboard from "../dashboard/index.vue";

interface Props {
  /**
   * 看板空间
   */
  space?: SpaceDto
}


const emit = defineEmits(['add']);
const props = defineProps<Props>()

const unfoldSpaces = useSessionStorage("unfoldSpaces", new Set<string>());
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const cacheEventBus = useEventBus(CacheEventBus)
const dashboardStore = useDashboardStore()

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})
const spaceFold = ref<boolean>(true)
// 空间操作
const deleteModalShow = ref(false);
const screenModalShow = ref(false)
const shareModalShow = ref(false)
const myRenameSelected = ref<string | undefined>(props.space?.name)
const renameModalShow = ref(false);
const handoverShow=ref(false)
const foldSpace = () => {
  spaceFold.value = !spaceFold.value
  if (props.space && !spaceFold.value) {
    unfoldSpaces.value.add(props.space.spaceId)
    return;
  }
  if (props.space && spaceFold.value) {
    unfoldSpaces.value.delete(props.space.spaceId)
  }
}
const handMoveModelValue=ref()
const searchQuery=ref()
onMounted(() => {
  if (!props.space) {
    return
  }
  // spaceFold.value = !unfoldSpaces.value.has(props.space.spaceId)
  if (props.space.spaceId === dashboardStore.dashboardSelected?.spaceId) {
    unfoldSpaces.value.delete(props.space)
    spaceFold.value = false
  }

})
const shareRefs = ref()
// 开启弹窗
const handleSpaceOperation = (value: string) => {
  switch (value) {
    case "screen":
      screenModalShow.value = true
      break
    case "share":
      // shareModalShow.value = true
      console.log('sss',props.space?.spaceId);
      shareRefs.value.openModal(props.space?.spaceId)
      break
    case "rename":
      renameModalShow.value = true
      break
    case "delete":
      deleteModalShow.value = true
      break
    case "handover":
      handoverShow.value = true
      break
    default:
  }
}
/**
 * 删除
 */
const deleteConfirm = () => {
  if (!props.space?.spaceId) {
    console.warn("删除空间失败，空间编号为空!")
    return
  }
  const dashboardInFolder = props.space?.folders?.some(Folder =>
      Folder.dashboards?.some(item => item.dashboardId === dashboardSelected.value.dashboardId)
  );
const mySpace=dashboardStore.spaceList.find((space: any) => space.authority===1)
  const dashboardInSpace = props.space?.dashboards?.some(item => item.dashboardId === dashboardSelected.value.dashboardId);
  removeSpace(props.space?.spaceId).then((resp) => {

    if (dashboardInFolder || dashboardInSpace) {
      treeMenuEventBus.emit(RefreshEvent);
    } else {
      cacheEventBus.emit('delete-event', {
        type: 'space',
        spaceId: props.space?.spaceId,
      })
    }
  }).catch((e) => {
    Message.error("删除空间失败！", e)
  })
  if (dashboardInFolder || dashboardInSpace) {
    dashboardSelected.value = {
      ...dashboardSelected.value,
      folderId: mySpace.folders.find(item => item.authority = 1).folderId,
      spaceId: mySpace.spaceId
    };
  }
  deleteModalShow.value = false;
};
const deleteCancel = () => {
  deleteModalShow.value = false;
}
/**
 * 重命名
 */
const renameConfirm = () => {
  if (props.space?.spaceId) {
    if (_.isEmpty(myRenameSelected.value)) {
      Message.warning('项目空间名称不能为空');
      return;
    }
    saveSpace({name: myRenameSelected.value, spaceId: props.space.spaceId})
        .then((resp) => {
          cacheEventBus.emit('rename-event', {
            type: 'space',
            spaceId: props.space?.spaceId,
            name: myRenameSelected.value,
          })
          Message.success('编辑成功')
        })
        .catch((e) => {
          Message.error("重命名空间失败！", e);
        });
  } else {
    Message.error('看板 ID 无效');
  }
  renameModalShow.value = false;
}
const renameCancel = () => {
  myRenameSelected.value = props.space?.name
  renameModalShow.value = false
}
/**
 * 移交
 */
const handoverConfirm=()=>{
  if(!handMoveModelValue.value){
    Message.warning('请选择移交对象')
  }
  else{
    handoverShow.value=false
  }
}
const handoverCancel=()=>{
  handoverShow.value=false

}
const showIcon = ref(false)
const leftShowIcon = ref(false)
const rightShowIcon = ref(false)

const leftPopupVisibleChange = (v: boolean) => {
  showIcon.value = v
  leftShowIcon.value = v
}
const rightPopupVisibleChange = (v: boolean) => {
  showIcon.value = v
  rightShowIcon.value = v
}
</script>

<template>
  <div class="container">
    <div
        class="space"
        :style="{backgroundColor:showIcon?'var(--tant-secondary-color-secondary-fill-hover)':''}"
        @click="foldSpace">
      <div class="name">
        <div class="arrow">
          <img :class="spaceFold?'':'transform90'" src="/icon/arrow-right.svg" alt=""/>
        </div>
        <div class="icon">
          <img v-if="spaceFold" src="/icon/cube.svg" alt=""/>
          <img v-else src="/icon/cube-open.svg" alt=""/>
        </div>
        <div>
          {{ space?.name }}
        </div>
      </div>
      <div class="operation" :style="showIcon?'visibility: visible;':''">
        <a-dropdown
            position="bl" @click.stop @select="(value:string)=>{emit('add',value,space?.spaceId)}"
            @popup-visible-change="leftPopupVisibleChange">
          <a-tooltip content="新建文件夹或看板">
            <a-button
                v-if="space?.authority !== 4"
                type="text" class="create-btn"
                :style="leftShowIcon?'background-color: var(--tant-secondary-color-secondary-transp-hover);':''">
              <template #icon>
                <div v-if="space?.authority">
                  <icon-plus size="15"/>
                </div>
              </template>
            </a-button>
          </a-tooltip>

          <template #content>
            <a-doption value="addDashboard">新建看板</a-doption>
            <a-doption value="addFolder">新建文件夹</a-doption>
          </template>
        </a-dropdown>
        <a-dropdown
            position="bl" @click.stop @select="handleSpaceOperation"
            @popup-visible-change="rightPopupVisibleChange">
          <a-button
              v-if="space?.authority !== 4"
              type="text" class="create-btn"
              :style="rightShowIcon?'background-color: var(--tant-secondary-color-secondary-transp-hover);':''">
            <template #icon>
              <div v-if="space?.authority">
                <icon-more-vertical size="15"/>
              </div>
              <div v-else>
                <a-tooltip content="查看共享状态">
                  <icon-user/>
                </a-tooltip>
              </div>
            </template>
          </a-button>
          <template #content>
            <a-doption value="share">分享空间</a-doption>
            <a-doption v-if="space?.authority === 2" value="handover">移交空间</a-doption>
            <a-doption value="screen">空间页面筛选</a-doption>
            <a-doption value="rename">重命名</a-doption>
            <a-doption v-if="space?.authority !== 3" class="delete-option" value="delete">删除</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <screen-space
        v-if="screenModalShow"
        v-model:visible="screenModalShow"/>
    <share-space ref="shareRefs"/>
    <folder
        v-show="!spaceFold"
        :folders="space?.folders" :space-id="space?.spaceId"
        @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"/>
    <dashboard
        v-show="!spaceFold"
        :dashboards="space?.dashboards"
        :space-id="space?.spaceId"
        :allow-add="space?.allowAdd"
        @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"/>
    <a-modal
        v-model:visible="deleteModalShow" ok-text="删除" title-align="start"
        :ok-button-props="{status:'danger'}" :mask-closable="false" @ok="deleteConfirm"
        @cancel="deleteCancel"
    >
      <template #title>
        删除空间
      </template>
      <div>
        确认删除【{{ space?.name }}】吗？&nbsp;&nbsp;<br/>
        删除后，空间下的看板将移动至看板创建者的【我的看板/未分组文件夹】，该操作不可恢复。
      </div>
    </a-modal>
    <a-modal
        :visible="renameModalShow"
        width="452px"
        :closable="false" ok-text="重命名" title-align="start"
        @ok="renameConfirm"
        @cancel="renameCancel">
      <template #title>
        重命名项目空间
      </template>
      <div class="rename-input">
        <a-input
            v-model="myRenameSelected"
            :style="{width:'388px'}"
            placeholder="请输入项目空间名称"
            allow-clear/>
      </div>
    </a-modal>
    <a-modal
        :visible="handoverShow"
        width="500px"
        top="400"

        :align-center="false"
        :closable="false" ok-text="移交" title-align="start"
        @ok="handoverConfirm"
        @cancel="handoverCancel">
      <template #title>
        移交空间
      </template>
      <div>
        <div class="line">
          <div class="contentNoTit">
            选择项目成员作为新的管理员
            <span class="spanMoveDesc">(移交后，你将变为空间成员)</span>
          </div>
        </div>
        <div class="line">
          <div class="contentNoTit">
            <a-select v-model:model-value="handMoveModelValue" allow-search :show-header-on-empty="true" placeholder="请选择">
              <a-option>1</a-option>
              <a-option>2</a-option>
<!--              <template #header>-->
<!--                <div class="selectSearch">-->
<!--                  <a-input v-model:model-value="searchQuery" placeholder="搜索看板" style="border: none;width: 100%;">-->
<!--                    <template #prefix>-->
<!--                      <icon-search/>-->
<!--                    </template>-->
<!--                  </a-input>-->
<!--                </div>-->
<!--              </template>-->
              <template #prefix>
                <icon-user />
              </template>
            </a-select>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.space {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;

    .arrow {
      height: 16px;
      margin-right: 8px;
      color: var(--tant-text-gray-color-text1-3);
      visibility: visible;
    }

    .transform90 {
      transform: rotate(90deg);
    }

    .icon {
      height: 16px;
      margin-right: 8px;
    }
  }

  .operation {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-right: 8px;
    visibility: hidden;

    .create-btn {
      height: 24px;
      width: 24px;
      color: var(--tant-secondary-color-secondary-default);
      font: var(--tant-body-font-body-regular);
      text-transform: capitalize;
      border-radius: var(--tant-border-radius-medium);
      margin-left: 8px;
    }
  }
}

.space:hover {
  background-color: var(--tant-secondary-color-secondary-fill-hover);
}

.create-btn:hover {
  background-color: var(--tant-secondary-color-secondary-transp-hover);
}

.space:hover .operation {
  visibility: visible;
}

.delete-option:hover {
  background-color: var(--tant-status-danger-color-danger-fill);
  color: var(--tant-status-danger-color-danger-default);
}

.rename-input {
  display: flex;
  align-items: center;
  justify-content: center;
}
.line{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 16px;
  .contentNoTit{
    width: 100%;
    .spanMoveDesc{
      color: var(--tant-text-gray-color-text1-3);
    }
  }

}
.selectSearch{
  height: 38px;
  width: 100%;
  line-height: 38px;
}
</style>