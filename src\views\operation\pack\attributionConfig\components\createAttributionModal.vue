<template>
    <a-modal
        v-model:visible="modalVisible" title-align="start" :title="modalTitle" :footer="false"
    >
        <a-form ref="formRef" :model="formData" layout="vertical">
            <a-form-item :disabled="originItem && originItem.code" field="code" :rules="[{required:true,message:'属性编码不能为空'}]" label="属性编码">
                <a-input v-model="formData.code" placeholder="请输入属性编码" allow-clear/>
            </a-form-item>
            <a-form-item field="name" :rules="[{required:true,message:'属性名称不能为空'}]" label="显示名称">
                <a-input v-model="formData.name" placeholder="请输入属性名称" allow-clear/>
            </a-form-item>
            <a-form-item :disabled="originItem && originItem.valueType" field="valueType" :rules="[{required:true,message:'属性值类型不能为空'}]" label="数据类型">
                <a-select v-model="formData.valueType" placeholder="请选择属性值类型">
                    <a-option v-for="item in Object.values(FieldType)" :value="item">{{ getFieldTypeName(item) }}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="obtainMethod" :rules="[{required:true,message:'取值途径不能为空'}]" label="输入方式">
                <a-select v-model="formData.obtainMethod" placeholder="请选择属性取值途径">
                    <a-option v-for="item in Object.values(AttributionInputType)" :value="item">{{ getAttributionInputTypeComment(item) }}</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="createLoading" @click="saveData">
                <span v-if="modalType === 'create'">创建属性</span>
                <span v-else>保存</span>
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import _ from "lodash";
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {FieldType, getFieldTypeName} from "@/api/type";
import {AttributionInputType, getAttributionInputTypeComment} from "@/api/enum";
import {saveAttribution} from "@/api/marketing/api";

interface AttributeItem {
    code?:string;
    name?: string;
    valueType?: string;
    color?: string;
}

const isValid = ref(false);
const createLoading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref('定义新的属性')
const modalType = ref('')
const formRef = ref()
const formData = reactive({});

const originItem = ref<AttributeItem>();

const openModal = async (val?:AttributeItem,type?:string) => {
    originItem.value = _.cloneDeep(val)
    modalVisible.value = true
    modalTitle.value = type ? '修改属性' : '定义新的属性'
    modalType.value = type || 'create'
    formRef.value.resetFields()
    formRef.value.clearValidate()

    formData.code = val?.code || ''
    formData.name = val?.name || ''
    formData.valueType = val?.valueType || ''
    formData.obtainMethod = val?.obtainMethod || ''
}

const closeModal = () => {
    modalVisible.value = false
}

const emits = defineEmits(['attributionChange']);
const saveData = async () => {
    formRef.value.validate((errors: any) => {
        isValid.value = !errors && formData.code !== '' && formData.name !== '' && formData.valueType !== '' && formData.obtainMethod !== '';
        console.log(isValid.value,'isValid.value');
    }).then(() => {
        if (!isValid.value) {
            Message.warning("请检查错误")
            return
        }

        const reqData = {
            code: formData.code,
            name: formData.name,
            valueType: formData.valueType,
            obtainMethod: formData.obtainMethod,
        }
        createLoading.value = true
        saveAttribution(reqData).then(() => {
            createLoading.value = true
            Message.success('操作成功')
            emits('attributionChange')
        }).catch((e) => {
            console.log(e)
            Message.error('操作失败')
        }).finally(() => {
            createLoading.value = false
        })
    });
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>
