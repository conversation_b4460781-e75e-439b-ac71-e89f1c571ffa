<!-- 数据量排序选择 -->
<template>
    <a-dropdown @select="sortTypeSelect">
        <a-button style="margin-left: 8px;">
            <template #icon><img class="option-icon" :src="sortImg" alt=""/></template>
        </a-button>
        <template #content>
            <a-doption v-for="item in sortList" :key="item.value" :value="item" :style="sortType === item.value ? {backgroundColor: 'var(--tant-secondary-color-secondary-fill)'} : {}">
            <div style="display: flex;align-items: center;width: 100%;">
                <img class="option-icon" :src="item.img" alt="" style="margin-right: 4px;"/>
                <span>{{ item.label }}</span>
            </div>
            </a-doption>
        </template>
    </a-dropdown>
</template>

<script lang="ts" setup>
import { ref} from "vue";

const sortType = ref('toLess')
const sortImg = ref('/icon/analysis/toLess.svg')
const sortList = ref<any>([
  {
    value:'toMore',
    label:'数据量少→多',
    img:'/icon/analysis/toMore.svg'
  },
  {
    value:'toLess',
    label:'数据量多→少',
    img:'/icon/analysis/toLess.svg'
  },
  {
    value:'toZ',
    label:'分组项A→Z',
    img:'/icon/analysis/toZ.svg'
  },
  {
    value:'toA',
    label:'分组项Z→A',
    img:'/icon/analysis/toA.svg'
  }
])
const sortTypeSelect = (v) => {
  sortImg.value = v.img
  sortType.value = v.value
}
</script>