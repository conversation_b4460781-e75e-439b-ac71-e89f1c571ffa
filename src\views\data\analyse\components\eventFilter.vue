<template>
  <div class="relation-editor">
    <div v-if="eventListData.length>0" class="relation-relation">
      <em class="relation-relation-line"></em>
      <div v-if="eventListData.length>1" class="relation-relation-value-dis" @click="logicalChange">
        <span>或</span>
      </div>
    </div>
    <div class="relation-main" style="width: 100%">
      <div v-for="(item,index) in eventListData" :key="index" class="action-row">
        <div class="filter-row-eventRow">
          <div class="action-left">
            <div class="row-content">
              <div>
                <div v-for="(event,eventIndex) in item.eventList" :key="eventIndex" class="event-item">
                  <i class="index">{{ index+1 }}</i>
                  <panelSelect :panel-data="event" @analysis-index-change="panelSelectChange(index,eventIndex,$event)"/>
                </div>
              </div>
            </div>
          </div>
          <div class="action-right">
            <a-space align="center">
              <a-tooltip content="添加筛选" position="top">
                <a-button class="btn-bg btn-26" @click="add(index)">
                  <template #icon>
                    <icon-filter/>
                  </template>
                </a-button>
              </a-tooltip>
              <a-tooltip content="删除" position="top">
                <a-button class="btn-bg-delete btn-26" @click="deleteEventList(index)">
                  <template #icon>
                    <icon-close-circle/>
                  </template>
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
        <div class="row-filters">
          <div class="relation-editor">
            <div v-if="item.filtersList" class="relation-relation">
              <em class="relation-relation-line"></em>
              <div v-if="item.filtersList.length>1" class="relation-relation-value" @click="logicalChange">
                <span v-if="isAnd">且</span>
                <span v-else>或</span>
              </div>
            </div>
            <div class="relation-main">
              <div v-for="(filter,filterIndex) in item.filtersList" :key="filterIndex" class="relation-row">
                <div class="multi-filter-condition">
                  <div v-if="filter.multipleList && filter.multipleList.length>1" class="relation-editor">
                    <div class="relation-relation">
                      <em class="relation-relation-line"></em>
                      <div class="relation-relation-value" @click="() => filter.isAnd = !filter.isAnd">
                        <span v-if="filter.isAnd">且</span>
                        <span v-else>或</span>
                      </div>
                    </div>
                    <div class="relation-main">
                      <div class="relation-row">
                        <div class="multi-filter-condition">
                          <div v-for="(val,valIndex) in filter.multipleList" :key="valIndex" class="sub-action-row">
                            <div class="sub-action-left">
                              <attrEnumSelect
                                :code-list="item.eventList"
                                :show-detail-filter="true"
                                :info="val"
                                @tabs-change="subfilChange(index,filterIndex,valIndex,$event)"/>
                            </div>
                            <div class="sub-action-right">
                              <a-space align="center">
                                <a-tooltip content="添加并列条件" position="top">
                                  <a-button class="btn-bg btn-26" @click="addItemFilters(index,filterIndex)">
                                    <template #icon>
                                      <icon-filter/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                                <a-tooltip content="删除" position="top">
                                  <a-button class="btn-bg-delete btn-26"
                                            @click="removeMultipleListItem(index,filterIndex,valIndex)">
                                    <template #icon>
                                      <icon-close-circle/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                              </a-space>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="sub-action-row">
                    <div class="sub-action-left">
                      <attrEnumSelect
                        :code-list="item.eventList"
                        :show-detail-filter="true"
                        :info="filter"
                        @tabs-change="filChange(index,filterIndex,$event)"/>
                    </div>
                    <div class="sub-action-right">
                      <a-space align="center">
                        <a-tooltip content="添加并列条件" position="top">
                          <a-button class="btn-bg btn-26" style="margin-left: 8px;"
                                    @click="addFilters(index,filter,filterIndex)">
                            <template #icon>
                              <icon-filter/>
                            </template>
                          </a-button>
                        </a-tooltip>
                        <a-tooltip content="删除" position="top">
                          <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index,filterIndex)">
                            <template #icon>
                              <icon-close-circle/>
                            </template>
                          </a-button>
                        </a-tooltip>
                      </a-space>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="item.filtersList&&item.filtersList.length" class="row-foot">
          <div class="ta-filter-button" @click="add(index)">
            <icon-plus class="action"/>
            <span class="label">筛选条件</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import {Indicator} from "@/api/analyse/type";
import panelSelect from "@/views/analyse/components/panelSelect.vue"
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import {getAnalyseEventList} from "@/api/analyse/api";
import _ from "lodash"


const evtLists = ref<any>([])
const itemObject = ref()
const eventBus = useEventBus('eventList');
const eventListData = ref<any>([])
const isAnd = ref(true)

const indicator = ref<Indicator[]>([])
const addEvent = () => {
  eventListData.value.push(_.cloneDeep(itemObject.value))
}
const logicalChange = () => {
  isAnd.value = !isAnd.value
  indicator.value.forEach(item => {
    item.filter.logicalOperation = isAnd.value ? 'and' : 'or'
  })
}
const panelSelectChange = (index: number, eventIndex: number, e: any) => {
  const {eventName, eventDisplayName, type} = e
  const filter = {...eventListData.value[index].eventList[eventIndex]};
  filter.eventName = eventName;
  filter.eventDisplayName = eventDisplayName;
  filter.type = type;
  eventListData.value[index].eventList[eventIndex] = filter;
  eventListData.value[index].name = eventName
}

// tabselect组件传参改变值
const subfilChange = (index: number, filterIndex: number, valIndex: number, e: any) => {
  const {name, objectType, calcuSymbol, thresholds, filterType, objectId} = e
  const filter = {...eventListData.value[index].filtersList[filterIndex].multipleList[valIndex]};
  filter.name = name;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  eventListData.value[index].filtersList[filterIndex].multipleList[valIndex] = filter;
}
const filChange = (index: number, filterIndex: number, e: any) => {
  const {name, objectType, calcuSymbol, thresholds, filterType, objectId} = e
  const filter = {...eventListData.value[index].filtersList[filterIndex]};
  filter.name = name;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  eventListData.value[index].filtersList[filterIndex] = filter;
}
// 添加并列条件
const add = (index1: number) => {
  // eventListData.value[index1].filtersList = [];
  eventListData.value[index1].filtersList.push({
    name: '',
    isAnd: true,
    objectType: '',
    objectId: '',
    filterType: '',
    calcuSymbol: '',
    thresholds: [],
    multipleList: []
  } as never)
}
// 添加并列条件
const addFilters = (index1: number, item: any, index: number) => {
  if (!eventListData.value[index1].filtersList[index].multipleList.length) {
    eventListData.value[index1].filtersList[index].multipleList.push({
      name: item.name,
      objectType: item.objectType,
      objectId: '',
      filterType: '',
      calcuSymbol: '',
      thresholds: [],
    })
  }
  eventListData.value[index1].filtersList[index].multipleList.push({
    name: '',
    objectType: '',
    objectId: '',
    filterType: '',
    calcuSymbol: '',
    thresholds: [],
  })
  // console.log( eventListData.value[index1].filtersList,' filtersList.value');

}
const addItemFilters = (index1: number, index: number) => {
  eventListData.value[index1].filtersList[index].multipleList.push({
    name: '',
    objectType: '',
    objectId: '',
    filterType: '',
    calcuSymbol: '',
    thresholds: [],
  } as never)
}
const deleteFilters = (index1: number, index: number) => {
  eventListData.value[index1].filtersList.splice(index, 1)
}
const deleteEventList = (index: number) => {
  eventListData.value.splice(index, 1)

}
const removeMultipleListItem = (index1: number, index: number, num: number) => {
  eventListData.value[index1].filtersList[index].multipleList.splice(num, 1)
  if (eventListData.value[index1].filtersList[index].multipleList.length === 1) {
    // 删的剩最后两个的时候，外层变为剩下的一个
    const data = eventListData.value[index1].filtersList[index].multipleList[0]
    eventListData.value[index1].filtersList[index] = {
      name: data.name,
      isAnd: true,
      objectType: data.objectType,
      objectId: data.objectId,
      filterType: data.filterType,
      calcuSymbol: data.calcuSymbol,
      thresholds: data.thresholds,
      multipleList: []
    }
  }
}
watch(eventListData, (newValue, oldValue) => {
  eventBus.emit('virtualEvent', newValue);  // 发射事件
  console.log(newValue,'new')
  indicator.value = newValue?.map(item => {
    const filters = item.filtersList.reduce((acc, el) => {
      if (el.multipleList.length <= 1) {
        acc.filters.push({
          objectName: el.name,
          objectType: el.objectType,
          objectId: el.objectId,
          filterType: el.filterType,
          calcuSymbol: el.calcuSymbol,
          thresholds: el.thresholds
        })
      } else {
        acc.subFilters.push({
          logicalOperation: el.isAnd ? 'and' : 'or',
          filters: el.multipleList?.map(item => ({
            objectName: item.name,
            objectType: item.objectType,
            objectId: item.objectId,
            filterType: item.filterType,
            calcuSymbol: item.calcuSymbol,
            thresholds: item.thresholds
          }))
        })
      }
      return acc
    }, {filters: [], subFilters: []})

    return {
      displayName: item.displayName,
      displayType: item.displayType,
      name: item.name,
      type: item.type,
      eventList: item.eventList,
      filter: {
        logicalOperation: isAnd.value ? 'and' : 'or',
        filters: filters.filters,
        subFilters: filters.subFilters
      }
    }
  })
}, {deep: true, immediate: true})
onMounted(() => {
  const data = {
    types:0,
    inApp:1
  }
  getAnalyseEventList(data).then(res => {
    evtLists.value = res
    itemObject.value = {
      name: '',
      type: 'event',
      displayType: {
        // type: 'default',
        // decimalNum: 2,
        // thousandSep: 1
      },
      displayName: '',
      eventList: [
        {
          eventName: evtLists.value[0].eventName,
          eventDisplayName: evtLists.value[0].eventDisplayName,
          type: 'event',
          eventAttrCode: '',
          eventAttrName: '总次数',
          filter: {}
        },
      ],
      filtersList: [],
      description: '',
      note: '',
    }
  })
})
defineExpose({addEvent,eventListData})
</script>

<style scoped lang="less">
.action-row {
  position: relative;
  height: auto;
  width: 100%;
  min-height: 24px;
  line-height: 24px;
  padding-right: 24px;

  .filter-row-eventRow {
    height: 36px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .action-left {
      align-items: flex-start;
      height: auto;
      display: flex;

      .row-content {
        flex-grow: 1;
        box-sizing: border-box;
        padding: 4px 0;

        .rename {
          min-width: 80px;
          max-width: calc(100% - 110px);
          height: 24px;
          padding: 0;
          line-height: 24px;
          background: inherit;
          margin-bottom: 6px;
          // font-weight: 600;
          font-size: 14px;
          display: flex;
          align-items: center;

          .placeholder {
            max-width: 260px;
            display: inline-block;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
            overflow: hidden;
            font-size: 14px;
            // white-space: pre;
            // vertical-align: middle;
            &:hover {
              color: var(--tant-primary-color-primary-default);
            }
          }

          :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
            font-weight: 600 !important;
          }

          :deep(.arco-input-wrapper) {
            border: none;
            background-color: transparent;
            font-weight: 600;

            &:hover {
              border: none;
              background-color: transparent;
              color: var(--tant-primary-color-primary-default);
            }
          }
        }

        .event-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding: 4px 0;
          overflow: hidden;
          line-height: 32px;
          white-space: normal;
        }
      }
    }

    .action-right {
      min-width: 40px;
      height: 36px;
      margin-left: 70px;
      padding-top: 0 !important;
      display: flex;
      align-items: center;
      opacity: 0;
      transition: opacity .3s;
    }
  }


  .filter-btn {
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;

    .btn-icon {
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      margin-right: 5px;
    }

    .filter-label {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }

    &:hover {
      border-color: var(--tant-primary-color-primary-hover);
    }
  }

  .row-word {
    display: inline-block;
    margin: 0 4px;
    color: var(--tant-text-gray-color-text1-2);
    vertical-align: top;
  }

  &:hover {
    background-color: var(--tant-fill-color-fill1-2);

    .action-left .row-content :deep(.filter-btn) {
      background: #fff;
    }

    .action-left .row-content :deep(.filter-icon) {
      background: #fff;
    }

    .sub-action-left :deep(.filter-btn) {
      background: #fff;
    }

    .action-right {
      opacity: 1;
    }
  }
}

.row-filters {
  // padding-left: 38px;
  .relation-editor {
    box-sizing: border-box;
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;

    .relation-relation {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      width: 24px;
      margin-right: 8px;
      flex-shrink: 0;

      .relation-relation-line {
        position: absolute;
        left: 12px;
        width: 1px;
        background-color: var(--tant-border-color-border1-2);
        transition: all .3s;
        top: 6px;
        height: calc(100% - 12px);
      }

      .relation-relation-value {
        position: absolute;
        text-transform: uppercase;
        top: 50%;
        left: 50%;
        transform: translate(-50%);
        height: 24px;
        padding: 0 5px;
        margin-top: -12px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        background-color: #fff;
        border: 1px solid var(--tant-border-color-border1-2);
        border-radius: 4px;
        cursor: pointer;
        word-break: keep-all;
        transition: all .3s;
      }

      &:hover .relation-relation-line {
        background-color: var(--tant-primary-color-primary-default);
      }

      &:hover .relation-relation-value {
        color: var(--tant-primary-color-primary-default);
        border-color: var(--tant-primary-color-primary-default);
      }
    }

    .relation-main {
      flex: 1 1;

      .relation-row {
        box-sizing: border-box;

        .multi-filter-condition {
          .sub-action-row {
            padding: 4px 0;
            align-items: center;
            display: flex;
            height: auto;

            .sub-action-left {
              align-items: flex-start;
              height: auto;
              display: flex;
            }

            .sub-action-right {
              align-items: flex-start;
              height: auto;
              display: flex;
              align-items: center;
              opacity: 0;
              transition: opacity .3s;
            }

            &:hover .sub-action-right {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}

.row-foot {
  margin: 0;
  // padding-left: 34px;
  transition: all .3s;

  .ta-filter-button {
    padding: 6px;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    transition: all .3s;

    .action {
      border-radius: 4px;
      background-color: var(--tant-primary-color-primary-fill);
      color: var(--tant-primary-color-primary-default);
      margin-right: 8px;
      padding: 3px;
      font-size: 18px;
    }

    .label {
      color: var(--tant-primary-color-primary-default);
    }

    &:hover .action {
      background-color: var(--tant-primary-color-primary-fill-hover);
      color: var(--tant-primary-color-primary-hover);
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.relation-editor {
  box-sizing: border-box;
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;

  .relation-relation {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 24px;
    margin-right: 8px;
    flex-shrink: 0;

    .relation-relation-line {
      position: absolute;
      left: 12px;
      width: 1px;
      background-color: var(--tant-border-color-border1-2);
      transition: all .3s;
      top: 6px;
      height: calc(100% - 12px);
    }

    .relation-relation-value-dis {
      position: absolute;
      text-transform: uppercase;
      top: 50%;
      left: 50%;
      transform: translate(-50%);
      margin-top: -12px;
      font-size: 12px;
      text-align: center;
      background-color: #fff;
      border: 1px solid var(--tant-border-color-border1-2);
      border-radius: 4px;
      word-break: keep-all;
      transition: all .3s;
      height: 20px;
      padding: 0 4px;
      color: var(--tant-disabled-color-disabled-text) !important;
      line-height: 18px;
      border-color: var(--tant-border-color-border1-2) !important;
      cursor: not-allowed;
    }

  }
}

.index {
  color: var(--tant-text-gray-color-text1-3);
  background-color: var(--tant-disabled-color-disabled-fill);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  font-size: 12px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  border-radius: 4px;
  transition: all .3s;
}
</style>