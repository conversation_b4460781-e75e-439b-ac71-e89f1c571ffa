<template>
    <icon-font :type="type" :size="size"/>
  </template>
  
  <script setup lang="ts">
  import {Icon} from '@arco-design/web-vue';

  // 替换成你的 iconfont 项目链接
  const IconFont = Icon.addFromIconFontCn({ 
    src: 'https://at.alicdn.com/t/c/font_4868870_hj23jyp6x4.js' // 在 iconfont.cn 项目设置中获取 Symbol 链接
  });
  
  defineProps({
    type: {
      type: String,
      required: true
    },
    size: {
      type: [Number, String],
      default: 16
    }
  });
  </script>