

<template>
    <div class="page">
      <div class="page-head">
        <div class="title">
          {{ route.meta.locale }}
        </div>
        <div class="filter">
          <div class="filter-item">
            <select-app/>
          </div>
          <div class="filter-item">
              <a-select
                v-model:model-value="queryParams.tag"
                style="width: 240px;"
                placeholder="请选择设计师"
                allow-search
                multiple
                :tag-nowrap="true"
                @change="init">
              </a-select>
          </div>
          <div class="filter-item">
              <a-select
                v-model:model-value="queryParams.tag"
                style="width: 240px;"
                placeholder="请选择标签"
                allow-search
                multiple
                :tag-nowrap="true"
                @change="init">
              </a-select>
          </div>
          <div class="filter-item">
              <a-select
                v-model:model-value="queryParams.tag"
                style="width: 240px;"
                placeholder="请选择创意人"
                allow-search
                multiple
                :tag-nowrap="true"
                @change="init">
              </a-select>
          </div>
          <div class="filter-item">
              <a-input v-model:model-value="queryParams.name" placeholder="请输入素材名称回车搜索" style="width: 240px;" @change="init"/>
          </div>
        </div>
      </div>
      <div class="page-body">
        <a-breadcrumb>
            <a-breadcrumb-item>默认素材库</a-breadcrumb-item>
            <a-breadcrumb-item>111</a-breadcrumb-item>
        </a-breadcrumb>
        <div class="setting">
            <div class="label">设置</div>
            <a-select
                v-model:model-value="setParams.viewType"
                style="width: 240px;">
                <template #label="{ data }">
                <span>视图-{{ data?.label }}</span>
                </template>
                <a-option value="1" label="列表"></a-option>
                <a-option value="2" label="网格"></a-option>
            </a-select>
            <a-checkbox :model-value="setParams.showChildrenMaterial" style="margin-left: 24px;">显示子文件夹素材</a-checkbox>
            <a-checkbox :model-value="setParams.showLaunchData" style="margin-left: 24px;">显示投放数据</a-checkbox>
        </div>
        <a-divider />
        <div class="table-wrap">
            <div class="wrap-left">
                <a-dropdown>
                    <a-button type="primary" class="br4">
                        <template #icon>
                            <icon-cloud-download />
                        </template>
                        <template #default>上传<icon-down style="margin-left: 12px;"/></template>
                    </a-button>
                    <template #content>
                        <a-doption>上传素材</a-doption>
                        <a-doption>上传文件夹</a-doption>
                    </template>
                </a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-folder />
                    </template>
                    <template #default>新建文件夹</template>
                </a-button>
                <a-dropdown>
                    <a-button class="br4" style="margin-left: 12px;">
                        <template #icon>
                            <icon-tags />
                        </template>
                        <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                    </a-button>
                    <template #content>
                        <a-doption>下载</a-doption>
                        <a-doption>编辑素材</a-doption>
                        <a-doption>编辑标签</a-doption>
                        <a-doption>编辑设计师</a-doption>
                        <a-doption>编辑创意人</a-doption>
                        <a-doption>编辑制作费用</a-doption>
                        <a-doption>编辑素材组</a-doption>
                        <a-doption>编辑备注</a-doption>
                        <a-doption>编辑状态</a-doption>
                        <a-doption>复制</a-doption>
                    </template>
                </a-dropdown>
            </div>
            <div class="wrap-right">
                <a-button class="br4">
                    <template #icon>
                        <icon-refresh />
                    </template>
                    <template #default>刷新</template>
                </a-button>
                <a-dropdown>
                    <a-button class="br4" style="margin-left: 12px;">
                        <template #icon>
                            <icon-download />
                        </template>
                        <template #default>导出<icon-down style="margin-left: 12px;"/></template>
                    </a-button>
                    <template #content>
                        <a-doption>仅预览地址列表</a-doption>
                        <a-doption>含缩略图列表</a-doption>
                        <a-doption>导出拒审信息</a-doption>
                    </template>
                </a-dropdown>
                <a-button class="br4" style="margin-left: 12px;" @click="handleColumns">
                    <template #icon>
                        <icon-menu />
                    </template>
                    <template #default>自定义列</template>
                </a-button>
            </div>
        </div>
        <a-table
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
        >
        </a-table>
        <div class="pagination">
          <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <CustomColumnModal v-model:visible="columnsVisible"/>
    </div>
  
  </template>
  
  <script setup lang="ts">
  import { reactive, ref} from "vue";
  import {useRoute} from 'vue-router';
  import router from "@/router";
  import {ROUTE_NAME} from "@/router/constants";
  import dayjs from 'dayjs';
  import {Message} from '@arco-design/web-vue';
  import CustomColumnModal from "@/views/launch/material/components/CustomColumnModal.vue";
  

  const route = useRoute();
  // 查询数据
  const queryParams = reactive({
    name: '',
    status: undefined,
    tag:[],
    current: 1,
    pageSize: 10,
  })
  // 设置数据
  const setParams = reactive({
    viewType: '1',
    showChildrenMaterial: false,
    showLaunchData: false,
  })
  const total = ref(0)
  // 模拟table数据
  const loading = ref(false)
  
  const tableData = ref<any>([])
  const columns = ref<any>([
    { type: 'selection', width: 48, align: 'center', fixed: 'left' },
    { title: '素材', dataIndex: 'material', sortable: true },
    { title: 'Local ID', dataIndex: 'localId' },
    { title: '设计师', dataIndex: 'designer' },
    { title: '标签', dataIndex: 'tags' },
    { title: '花费', dataIndex: 'cost', sortable: true },
    { title: '展示数', dataIndex: 'impressions', sortable: true },
    { title: '千次展示成本', dataIndex: 'cpm', sortable: true },
    { title: '点击数', dataIndex: 'clicks', sortable: true },
    { title: '点击率', dataIndex: 'ctr', sortable: true }
  ])
  
  // Table页面滑动
  const scroll = {
    y: 'calc(100vh - 256px)'
  };
  // 自定义列
  const columnsVisible = ref(false)
  const handleColumns = () => {
    columnsVisible.value = true
  }
  const init = () => {
  }
  init()
  // 分页
  const pageChange = (v) => {
    queryParams.current = v
    init()
  }
  </script>
  
  <style scoped lang="less">
  .pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
  }
  .setting{
    display: flex;
    align-items: center;
    padding-top: 16px;
    .label{
      width: 60px;
    }
  }
  .table-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .wrap-left{
    }
    .wrap-right{
    }
  }
  .br4{
    border-radius: 4px;
  }
  </style>