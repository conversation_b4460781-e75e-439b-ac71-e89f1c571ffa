<script setup lang="ts">

import {h, onMounted, ref} from "vue";
import cubeImg from '/public/icon/cube-open.svg';
import folderImg from '/public/icon/folder.svg';
import fileImg from '/public/icon/file.svg';
import {TreeNodeData} from "@arco-design/web-vue";
import {SpaceDto} from "@/api/space/type";
import {listSpace} from "@/api/space/api";

interface Props {

  /**
   * 选择类型
   */
  type: 'space' | 'folder' | 'dashboard'
  /**
   * 是否显示指定类型
   */
  isDisabled?: string
}

const props = defineProps<Props>();
const positions = ref([]);
const itemSelected = defineModel<string>("selected", {default: ''});
const emits = defineEmits(['change'])

onMounted(() => {
  listSpace(true).then((resp: SpaceDto[]) => {
    
    const spaces = resp || []
    positions.value = spaces.map(space => {
      return {
        'key': space.spaceId,
        'title': space.name,
        'icon': () => h(cubeImg),
        'disabled': props.type !== 'space',
        'children': props.type === 'space' ? [] : [
          ...(space.folders?.map(folder => {
            if (folder.name === props.isDisabled) {
              return null;
            }
            return {
              'key': folder.folderId,
              'title': folder.name,
              'icon': () => h(folderImg),
              'disabled': props.type !== 'folder',
              'children': props.type === 'folder' ? [] : folder.dashboards?.map(dashboard => ({
                'key': dashboard.dashboardId,
                'title': dashboard.name,
                'icon': () => h(fileImg)
              }))
            }
          }) || []),
          ...(space.dashboards?.map(dd => {
            if (dd.name === props.isDisabled) {
              return null;
            }
            return {
              'key': dd.dashboardId,
              'title': dd.name,
              'icon': () => h(fileImg),
              'children': props.type === 'folder' ? [] : dd.dashboards?.map(dashboard => ({
                'key': dashboard.dashboardId,
                'title': dashboard.name,
                'icon': () => h(fileImg)
              }))
            }
          }) || [])
        ].filter(item => item !== null)
      } as TreeNodeData
    }) || []
  })
  
})
const handleChange = (v) => {
  let folderId = '';
  let spaceId = '';
  // 遍历所有空间查找选中的项
  console.log(positions.value,'mmmm');
  
  positions.value.forEach(space => {
    if (space.key === v) {
      // 选中的是空间
      spaceId = v;
    } else if (space.children) {
      space.children.forEach(item => {
        if (item.key === v) {
          spaceId = space.key;
          if (item.children && item.children.length > 0) {
            // 选中的是文件夹
            folderId = v;
          } else {
            // 选中的是空间下的看板
            folderId = '';
          }
        } else if (item.children) {
          // 检查文件夹下的看板
          item.children.forEach(dashboard => {
            if (dashboard.key === v) {
              spaceId = space.key;
              folderId = item.key;
            }
          });
        }
      });
    }
  });
  emits('change', { spaceId, folderId, dashboardId: v });
}
</script>

<template>
  <a-tree-select
      v-model="itemSelected"
      :data="positions"
      placeholder="请选择添加位置"
      @change="handleChange">
    <template #label="{ data }" >
      <div  class="select-option">
        <img v-if="type==='space'" class="icon" src="/icon/cube-open.svg" alt=""/>
        <img v-if="type==='folder'" class="icon" src="/icon/folder.svg" alt=""/>
        <img v-if="type==='dashboard'" class="icon" src="/icon/file.svg" alt=""/>
        <div class="label">
          {{ data.label }}
        </div>
      </div>
    </template>
  </a-tree-select>
</template>

<style scoped lang="less">
.select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

:deep(.arco-tree-node-disabled .arco-tree-node-title) {
  color: var(--color-text-1);
  background: none;
  cursor: pointer;
}
</style>