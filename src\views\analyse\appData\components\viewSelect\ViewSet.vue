<template>
  <a-modal v-model:visible="shareVisible" title-align="start" :mask-closable="false" width="474px">
    <template #title>
      视图设置
    </template>
    <div class="set-board">
      <div class="setting">
        <div class="setting-item">
          <span class="setting-item-title">公开范围</span>
          <div class="setting-item-content">
            <a-select
              v-model="setData.shareType"
              :style="{width:'200px'}"
              placeholder="选择公开范围">
              <a-option value="non_public" label="不公开"></a-option>
              <a-option value="group" label="组内公开"></a-option>
              <a-option value="public" label="全局公开"></a-option>
            </a-select>
          </div>
        </div>
        <div class="setting-item">
          <span class="setting-item-title">公开权限</span>
          <div class="setting-item-content">
            <a-select
              v-model="setData.permissionType"
              :style="{width:'200px'}"
              placeholder="选择权限类型">
              <a-option :value="4" label="查看者">
                <div class="label">查看者</div>
              </a-option>
              <a-option :value="3" label="协作者">
                <div>
                  <div class="label">协作者</div>
                  <div class="tag">添加或移除视图报表、修改视图设置、编辑视图中的全部报表</div>
                </div>
              </a-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <a-button style="margin-right: 10px;" @click="shareHandleCancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleOk">
            应用
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">

import {reactive, ref} from "vue";
import {saveAttributionView} from "@/api/analyse/api"
import {Message} from '@arco-design/web-vue'

const shareVisible = ref(false)
const loading = ref(false)

const emits = defineEmits(['permissionChange'])

const setData = reactive({
    shareType: 'non_public',
    permissionType: 4
});
const viewCode = ref('')

const shareHandleCancel = () => {
  shareVisible.value = false;
};

const handleOk = async () => {
  loading.value = true;
  try {
    const data = {
      code: viewCode.value,
      share:setData
    };
    await saveAttributionView(data);
    Message.success('视图设置成功');
    emits('permissionChange',data)
    shareVisible.value = false;
  } catch (error) {
    console.error('更新共享设置失败:', error);
    Message.error('视图设置失败');
  } finally {
    loading.value = false;
  }
};

const openModal = async (record:any) => {
    viewCode.value = record.code || ''
    setData.shareType = record.share?.shareType || 'non_public';
    setData.permissionType = record.share?.permissionType || 4;
    shareVisible.value = true;
};

defineExpose({
  openModal
});
</script>

<style scoped lang="less">

.set-board {
  padding: 0 12px;
  .setting {
    .setting-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .setting-item-title {
        font-size: 14px;
        color: var(--tant-text-gray-color-text1-2);
        margin-right: 16px;
      }
      .setting-item-content {
        // padding-left: 16px;
      }
    }
  }
}

.label {
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-body-font-body-regular);
  height: 38px;
  display: flex;
  align-items: center;
}

.tag {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 12px;
  line-height: 18px;
  white-space: normal;
  width: 180px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}
</style>