import {saveMenuFilterConfig} from '@/api/setting/api';
import {debounce} from 'lodash';
import {useRoute} from 'vue-router';
import {watch} from 'vue';

/**
 * 筛选条件保存工具
 */
export class FilterConfigUtil {
  private menuCode: string;

  private debouncedSave: any;

  private urlParams?: string;

  constructor(menuCode: string, delay = 1000) {
    this.menuCode = menuCode;
    // 使用防抖避免频繁保存
    this.debouncedSave = debounce(this.saveConfig.bind(this), delay);
  }

  /**
   * 立即保存筛选条件
   * @param configJson 筛选条件配置
   * @param description 配置描述
   */
  async saveConfig(configJson: any, description = ''): Promise<void> {
    try {
      const params: any = {
        menuCode: this.menuCode,
        configJson,
        status: 1,
        description
      };
      
      // 如果有urlParams，则添加到参数中
      if (this.urlParams) {
        params.urlParams = this.urlParams;
      }
      const token = localStorage.getItem('token');
      if(token){
        await saveMenuFilterConfig(params);
      }
    } catch (error) {
      console.error(`菜单 ${this.menuCode} 筛选条件保存失败:`, error);
    }
  }

  /**
   * 防抖保存筛选条件
   * @param configJson 筛选条件配置
   * @param description 配置描述
   */
  saveConfigDebounced(configJson: any, description = ''): void {
    this.debouncedSave(configJson, description);
  }

  /**
   * 更新菜单代码
   * @param newMenuCode 新的菜单代码
   */
  updateMenuCode(newMenuCode: string): void {
    this.menuCode = newMenuCode;
  }
  
  /**
   * 更新URL参数
   * @param urlParams URL参数
   */
  updateUrlParams(urlParams: string): void {
    this.urlParams = urlParams;
  }
}

/**
 * 创建筛选条件保存工具实例
 * @param menuCode 菜单代码
 * @param delay 防抖延迟时间（毫秒）
 * @param urlParams URL参数
 */
export function createFilterConfigUtil(menuCode: string, urlParams?: string, delay = 1000): FilterConfigUtil {
  const util = new FilterConfigUtil(menuCode, delay);
  if (urlParams) {
    util.updateUrlParams(urlParams);
  }
  return util;
}

/**
 * 使用页面筛选条件的组合式函数
 * @param paramsRef 参数引用
 * @param autoSave 是否自动保存
 * @param urlParams URL参数
 */
export function usePageFilter(paramsRef: any, urlParams?: string, autoSave = false) {
  const route = useRoute();
  const menuCode = route.name as string;
  
  // 创建保存工具
  const filterUtil = createFilterConfigUtil(menuCode, urlParams, 1000);
  
  // 保存方法
  const saveFilter = (description?: string) => {
    return filterUtil.saveConfig(paramsRef, description);
  };
  
  // 防抖保存方法
  const saveFilterDebounced = (description?: string) => {
    filterUtil.saveConfigDebounced(paramsRef, description);
  };
  
  // 如果启用自动保存，监听参数变化
  if (autoSave) {
    watch(paramsRef, () => {
      saveFilterDebounced('');
    }, { deep: true });
  }
  
  return {
    saveFilter,
    saveFilterDebounced,
    filterUtil
  };
}