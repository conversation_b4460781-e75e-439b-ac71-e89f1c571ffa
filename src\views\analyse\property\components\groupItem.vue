

<template>
    <!-- 分组项 -->
    <div class="guide">
        <div class="stickyBar" :style="attributeList.length>0? {} : { color: 'var(--tant-text-gray-color-text1-4)' }">
            <div class="modal">
                <icon-apps class="model-icon"/>
                <span class="title">按</span>
                <a-select v-model:model-value="groupType" default-value="attribute" :style="{width:'72px',margin:'0 8px'}" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in barList" :key="item.value" style="width: 140px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
                <span class="title">进行分组统计</span>
                <div v-if="groupType === 'attribute' && attributeList.length < 2" class="model-btn">
                    <a-button @click="addItem">
                        <template #icon>
                            <icon-plus class="nav-icon"/>
                        </template>
                    </a-button>
                </div>
                <div v-if="groupType === 'crowd'" class="model-btn">
                    <a-button @click="addCrowd">
                        <template #icon>
                            <icon-plus class="nav-icon"/>
                        </template>
                    </a-button>
                </div>
            </div>
        </div>
        <div v-show="groupType === 'attribute'" class="event-filter-box">
            <div v-for="(item,index) in attributeList" :key="item.code" class="action-row">
                <div class="filter-row-eventRow">
                    <div class="action-left">
                        <i class="drag-index">{{ index + 1 }}</i>
                        <icon-drag-dot-vertical class="hover-drag"/>
                        <div class="row-content">
                            <div class="event-item">
                                <div style="display: flex; align-items: center;">
                                    <tabsSelect :info="item" :exclude-event="true" @tabs-change="tabsChange(index,$event)"/>
                                    <a-trigger v-if="item?.objectType == 'date' || item?.objectType == 'datetime'" v-model:popup-visible="triggerVisible" trigger="click"  show-arrow position="tl" :update-at-scroll="true">
                                        <div class="set-icon">
                                            <icon-settings />
                                        </div>
                                        <template #content>
                                            <div class="range-block">
                                                <div class="range-title">分组方式</div>
                                                    <div class="range-content">
                                                        <div class="inline">
                                                            <a-radio-group v-model:model-value="item.isSum" direction="vertical" @change="sumChange(index,$event)">
                                                                <a-radio :value="1">汇总</a-radio>
                                                                <a-radio :value="0">不汇总</a-radio>
                                                            </a-radio-group>
                                                        </div>
                                                        <div class="inline" style="margin-left: 20px;">
                                                            <a-cascader v-model:model-value="item.timeSummaryType" :options="options" :disabled="!item.isSum" style="min-width: 80px;max-width:160px;" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" />
                                                        </div>
                                                    </div>
                                                <div class="range-footer">
                                                    <a-button type="primary" @click="cancelTime(index)">取消</a-button>
                                                    <a-button type="primary" @click="() => triggerVisible = false">应用</a-button>
                                                </div>
                                            </div>
                                        </template>
                                    </a-trigger>
                                    <numberGroupSet v-if="item?.objectType == 'int' || item?.objectType == 'float'" @number-set="numberSet(index,$event)"/>
                                </div>
                                <div class="action-right">
                                    <a-tooltip content="移除" position="top">
                                        <a-button class="btn-bg-delete btn-26" @click="deleteItem(index)">
                                            <template #icon>
                                                <icon-close-circle />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-show="groupType === 'crowd'" class="event-filter-box">
            <div v-for="(item,index) in crowdData" :key="index" class="action-row">
                <div class="filter-row-eventRow">
                    <div class="action-left">
                        <i class="drag-index">{{ index + 1 }}</i>
                        <icon-drag-dot-vertical class="hover-drag"/>
                        <div class="row-content">
                            <div class="event-item">
                                <div v-if="item.rename" class="rename">
                                    <a-input v-model="item.displayName" placeholder="请输入" @blur="reNameBlur(index)"/>
                                </div>
                                <div v-else class="rename">
                                    {{item.displayName}}
                                </div>
                                <div class="action-right">
                                    <a-space align="center">
                                        <a-tooltip content="重命名" position="top">
                                            <a-button v-if="!item.rename" class="btn-bg btn-26" @click="() => item.rename = true">
                                                <template #icon>
                                                    <icon-pen />
                                                </template>
                                            </a-button>
                                        </a-tooltip>
                                        <a-tooltip content="取消重命名" position="top">
                                            <a-button v-if="item.rename" class="btn-bg btn-26" @click="() => item.rename = false">
                                                <template #icon>
                                                    <icon-close-circle />
                                                </template>
                                            </a-button>
                                        </a-tooltip>
                                        <a-tooltip content="删除人群" position="top">
                                            <a-button class="btn-bg-delete btn-26" @click="deleteCrowd(index)">
                                                <template #icon>
                                                    <icon-delete />
                                                </template>
                                            </a-button>
                                        </a-tooltip>
                                    </a-space>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row-filters">
                    <div class="relation-editor">
                        <div v-if="item.filtersList" class="relation-relation">
                            <em class="relation-relation-line"></em>
                            <div v-if="item.filtersList.length>1" class="relation-relation-value" @click="logicalChange(index)">
                                <span v-if="item.isAnd">且</span>
                                <span v-else>或</span>
                            </div>
                        </div>
                        <div class="relation-main">
                            <div v-for="(filter,filterIndex) in item.filtersList" :key="filterIndex" class="relation-row">
                                <div class="multi-filter-condition">
                                    <div v-if="filter.multipleList && filter.multipleList.length>1" class="relation-editor">
                                        <div class="relation-relation">
                                            <em class="relation-relation-line"></em>
                                            <div class="relation-relation-value" @click="() => filter.isAnd = !filter.isAnd">
                                                <span v-if="filter.isAnd">且</span>
                                                <span v-else>或</span>
                                            </div>
                                        </div>
                                        <div class="relation-main">
                                            <div class="relation-row">
                                                <div class="multi-filter-condition">
                                                   <div v-for="(val,valIndex) in filter.multipleList" :key="valIndex" class="sub-action-row">
                                                        <div class="sub-action-left">
                                                            <tabsSelect :show-detail-filter="true" :exclude-event="true" :info="val" @tabs-change="subfilChange(index,filterIndex,valIndex,$event)"/>
                                                         </div>
                                                        <div class="sub-action-right">
                                                            <a-space align="center">
                                                                <a-tooltip content="添加并列条件" position="top">
                                                                    <a-button class="btn-bg btn-26" @click="addItemFilters(index,filterIndex)">
                                                                        <template #icon>
                                                                            <icon-filter />
                                                                        </template>
                                                                    </a-button>
                                                                </a-tooltip>
                                                                <a-tooltip content="删除" position="top">
                                                                    <a-button class="btn-bg-delete btn-26" @click="removeMultipleListItem(index,filterIndex,valIndex)">
                                                                        <template #icon>
                                                                            <icon-close-circle />
                                                                        </template>
                                                                    </a-button>
                                                                </a-tooltip>
                                                            </a-space>
                                                        </div>
                                                    </div>
                                                </div>
                                             </div>
                                        </div>
                                    </div>
                                    <div v-else class="sub-action-row">
                                        <div class="sub-action-left">
                                            <tabsSelect :show-detail-filter="true" :exclude-event="true" :info="filter" @tabs-change="filChange(index,filterIndex,$event)"/>
                                        </div>
                                        <div class="sub-action-right">
                                            <a-space align="center">
                                                <a-tooltip content="添加并列条件" position="top">
                                                    <a-button class="btn-bg btn-26" @click="addFilters(index,filter,filterIndex)">
                                                        <template #icon>
                                                            <icon-filter />
                                                        </template>
                                                    </a-button>
                                                </a-tooltip>
                                                <a-tooltip content="删除" position="top">
                                                    <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index,filterIndex)">
                                                        <template #icon>
                                                            <icon-close-circle />
                                                        </template>
                                                    </a-button>
                                                </a-tooltip>
                                            </a-space>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="item.filtersList&&item.filtersList.length" class="row-foot">
                    <div class="ta-filter-button" @click="add(index)">
                        <icon-plus class="action"/>
                        <span class="label">筛选条件</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {Indicator} from "@/api/analyse/type";
import tabsSelect from "@/views/analyse/components/tabsSelect.vue"
import {analyseStore} from '@/store';
import numberGroupSet from "@/views/analyse/components/numberGroupSet.vue"

const groupType = ref('attribute')
const analyseData = analyseStore()
const emits = defineEmits(['attributeChange','crowdChange'])

// 属性
const attributeList = ref<any>([])
const aggregates = ref<any>([])
const triggerVisible = ref(false)

const barList = ref<any>([
    {
        label:'人群',
        value:'crowd'
    },
    {
        label:'属性',
        value:'attribute'
    }
])
// 人群
const crowdData = ref<any>([
    {
        displayName:'人群1',
        rename:false,
        isAnd:true,
        filtersList:[
            {
                name:'package_name',
                isAnd:true,
                objectType:'string',
                objectId:'',
                filterType:'',
                calcuSymbol:'',
                thresholds:[],
                multipleList:[]
            }
        ]
    },
    {
        displayName:'人群2',
        rename:false,
        isAnd:true,
        filtersList:[
            {
                name:'package_name',
                isAnd:true,
                objectType:'string',
                objectId:'',
                filterType:'',
                calcuSymbol:'',
                thresholds:[],
                multipleList:[]
            }
        ]
    }
])
const options = ref([
  {
    value:'D1',
    label:'按天'
  },{
  value: 'm1',
  label: '按分钟'
  },{
    value:'h1',
    label:'按小时'
  },{
    value:'W1',
    label:'按周'
  },{
    value:'M1',
    label:'按月'
  },{
    value:'more',
    label:'更多',
    children:[
      {
        value:'Q1',
        label:'按季'
      },{
        value:'Y1',
        label:'按年'
      },
    ]
  }
])

const tabsChange = (index,v) => {
    const {name,objectType,filterType,objectId} = v
    const filter = { ...attributeList.value[index] };
    filter.name = name;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    attributeList.value[index] = filter;
}
const deleteItem = (index:number) => {
    attributeList.value.splice(index,1)
}
const dataList = ref([
    {
        code:'06',
        name:'事件时间',
        objectType:'datetime'
    },
    {
        code:'07',
        name:'角色等级',
        objectType:'float'
    },
    {
        code:'01',
        name:'来源渠道',
        attr:'维度表属性'
    },
    {
        code:'02',
        name:'应用版本',
        attr:'维度表属性'
    },
    {
        code:'03',
        name:'数据源',
    },
    {
        code:'04',
        name:'设备号',
    },
    {
        code:'05',
        name:'事件名称',
        attr:'维度表属性'
    },
    
])
const getEventList = async () => {
    const data = analyseData.$state.evtLists.length > 0 ? analyseData.$state.evtLists : await analyseData.fetchEvtInfo()
    if(data && data.length){
        dataList.value = data.map((item,index) => {
          return {
            name:item.name,
            code:item.code,
            filterType:'event',
            objectType:'float',
          }
        })
    }
}
getEventList()
const numberSet = (index:number,e:any) => {
    attributeList.value[index].numberSummaryType = e.numberSummaryType
    attributeList.value[index].numberSummaryScope = e.numberSummaryScope
}

const sumChange = (index:number,v) => {
    if(v === 0) {
        attributeList.value[index].timeSummaryType = 'D1'
    }else{
        attributeList.value[index].timeSummaryType = 'h1'
    }
}
const cancelTime = (index:number) => {
    attributeList.value[index].timeSummaryType = 'D1'
    attributeList.value[index].isSum = 0
    triggerVisible.value = false
}
// 添加处理数据
const addItem = () => {
    const index = attributeList.value.length || 0
    if(index < dataList.value.length){
        if(dataList.value[index]?.objectType === 'date' || dataList.value[index]?.objectType === 'datetime'){
            attributeList.value.push({
                ...dataList.value[index],
                objectId:dataList.value[index].code,
                isSum:0,
                timeSummaryType:'D1'
            })
        }else if(dataList.value[index]?.objectType === 'float' || dataList.value[index]?.objectType === 'int'){
            attributeList.value.push({
                ...dataList.value[index],
                objectId:dataList.value[index].code,
                openBatch:false,
                stepSize:50,
                stepNum:1,
                numberSummaryType:'st12',
                numberSummaryScope:[
                    {number:0}
                ]
            })
        }else{
            attributeList.value.push({
                ...dataList.value[index],
                objectId:dataList.value[index].code,
            })
        }
    }
}
watch(attributeList.value, (newValue, oldValue) => {
    aggregates.value = newValue.map(item => {
        return{
            aggregateType:item.filterType || '',
            objectName:item.name || '',
            objectId:item.objectId || '',
            objectType:item.objectType || '',
            numberSummaryType:item.numberSummaryType || '',
            numberSummaryScope:item.numberSummaryScope ? item.numberSummaryScope.map(item => item.number) : [],
            timeSummaryType:item.timeSummaryType || ''
        }
    })
    emits('attributeChange',aggregates.value)
},{immediate:true})

const reNameBlur = (index:number) => {
    if(crowdData.value[index].displayName === ''){
        crowdData.value[index].rename = false
    }
}
const addCrowd = () => {
    crowdData.value.push({
        displayName:`人群${crowdData.value.length+1}`,
        rename:false,
        isAnd:true,
        filtersList:[
            {
                name:'package_name',
                isAnd:true,
                objectType:'string',
                objectId:'',
                filterType:'',
                calcuSymbol:'',
                thresholds:[],
                multipleList:[]
            }
        ]
    })
}
const deleteCrowd = (index:number) => {
    crowdData.value.splice(index,1)
}
const logicalChange = (index:number) => {
    crowdData.value[index].isAnd = !crowdData.value[index].isAnd
}
const subfilChange = (index:number,filterIndex:number,valIndex:number,e:any) => {
    const {name,objectType,calcuSymbol,thresholds,filterType,objectId} = e
    const filter = { ...crowdData.value[index].filtersList[filterIndex].multipleList[valIndex] };
    filter.name = name;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    filter.calcuSymbol = calcuSymbol
    filter.thresholds = thresholds
    crowdData.value[index].filtersList[filterIndex].multipleList[valIndex] = filter;
}
const filChange = (index:number,filterIndex:number,e:any) => {
    const {name,objectType,calcuSymbol,thresholds,filterType,objectId} = e
    const filter = { ...crowdData.value[index].filtersList[filterIndex] };
    filter.name = name;
    filter.objectType = objectType;
    filter.objectId = objectId
    filter.filterType = filterType
    filter.calcuSymbol = calcuSymbol
    filter.thresholds = thresholds
    crowdData.value[index].filtersList[filterIndex] = filter;
}
// 添加并列条件
const add = (index1:number) => {
    crowdData.value[index1].filtersList.push({
        name:'package_name',
        isAnd:true,
        objectType:'string',
        objectId:'',
        filterType:'',
        calcuSymbol:'',
        thresholds:[],
        multipleList:[]
    } as never)
}
// 添加并列条件
const addFilters = (index1:number,item:any,index:number) => {
    if(!crowdData.value[index1].filtersList[index].multipleList.length){
        crowdData.value[index1].filtersList[index].multipleList.push({
            name: item.name,
            objectType:item.objectType,
            objectId: item.objectId,
            filterType: item.filterType,
            calcuSymbol: item.calcuSymbol,
            thresholds: item.thresholds,
        })
    }
    crowdData.value[index1].filtersList[index].multipleList.push({
        name: '',
        objectType:'',
        objectId:'',
        filterType:'',
        calcuSymbol:'',
        thresholds:[],
    })
}
const addItemFilters = (index1:number,index:number) => {
    crowdData.value[index1].filtersList[index].multipleList.push({
        name: '',
        objectType:'',
        objectId:'',
        filterType:'',
        calcuSymbol:'',
        thresholds:[],
    } as never)
}
const deleteFilters = (index1:number,index:number) => {
    crowdData.value[index1].filtersList.splice(index,1)
}
const removeMultipleListItem = (index1:number,index:number,num:number) => {
    crowdData.value[index1].filtersList[index].multipleList.splice(num,1)
    if(crowdData.value[index1].filtersList[index].multipleList.length === 1){
        // 删的剩最后两个的时候，外层变为剩下的一个
        const data = crowdData.value[index1].filtersList[index].multipleList[0]
        crowdData.value[index1].filtersList[index] = {
            name:data.name,
            isAnd:true,
            objectType:data.objectType,
            objectId:data.objectId,
            filterType:data.filterType,
            calcuSymbol:data.calcuSymbol,
            thresholds:data.thresholds,
            multipleList:[]
        }
    }
}

const indicator = ref<Indicator[]>([])
watch(crowdData.value, (newValue, oldValue) => {
    indicator.value = newValue.map(item => {
        const filters = item.filtersList.reduce((acc, el) => {
            if (el.multipleList.length <= 1) {
                acc.filters.push({ 
                    objectName: el.name,
                    objectType: el.objectType,
                    objectId:el.objectId,
                    filterType: el.filterType,
                    calcuSymbol:el.calcuSymbol,
                    thresholds:el.thresholds
                })
            } else {
                acc.subFilters.push({
                    logicalOperation: el.isAnd ? 'and' : 'or',
                    filters: el.multipleList.map(item => ({ 
                        objectName: item.name,
                        objectType: item.objectType,
                        objectId:item.objectId,
                        filterType: item.filterType,
                        calcuSymbol: item.calcuSymbol,
                        thresholds:item.thresholds
                    }))
                })
            }
            return acc
        }, { filters: [], subFilters: [] })

        return {
            displayName: item.displayName,
            filter: {
                logicalOperation: item.isAnd ? 'and' : 'or',
                filters: filters.filters,
                subFilters: filters.subFilters
            }
        }
    })
    emits('crowdChange',indicator.value)
},{immediate:true})
</script>

<style scoped lang="less">
.filter-btn{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon{
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .model-icon{
                margin-right: 8px;
            }
            .title{
                // flex-grow: 1;
                font-weight: 600;
                // max-width: calc(100% - 48px);
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .drag-index{
                    margin-top: 5px;
                    margin-right: 12px;
                    flex-shrink: 0;
                    width: 24px;
                    height: 24px;
                    color: var(--tant-text-gray-color-text1-3);
                    background-color: var(--tant-disabled-color-disabled-fill);
                    font-size: 12px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    transition: all .3s;
                    opacity: 1;
                }
                .hover-drag{
                    position: absolute;
                    left: 5px;
                    margin-top: 9px;
                    margin-left: 24px;
                    flex-shrink: 0;
                    font-size: 16px;
                    font-style: normal;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 4px;
                    opacity: 0;
                    transition: all .3s;
                }
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }
                    .action-right {
                        display: flex;
                        align-items: center;
                        opacity: 0;
                        transition: opacity .3s;
                    }
                }
            }
            .set-icon{
                font-size: 16px;
                height: 26px;
                width: 26px;
                text-align: center;
                line-height: 26px;
                border-radius: 4px;
                cursor: pointer;
                color: var(--tant-text-gray-color-text1-3);
                transition: all .3s;
                border: 1px solid transparent;
                background-color: var(--tant-secondary-color-secondary-fill);
                margin: 0 8px;
                &:hover{
                    border: 1px solid var(--tant-primary-color-primary-default)
                }
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .set-icon{
                background: #fff;
            }
            .action-left .drag-index{
                opacity: 0;
                transition: all .3s;
            }
            .action-left .hover-drag{
                opacity: 1;
                transition: all .3s;
            }
            .action-left .row-content :deep(.filter-btn){
                background: #fff;
            }
            .sub-action-left :deep(.filter-btn){
                background: #fff;
            }
            .row-content .action-right{
                opacity: 1;
            } 
        }
        }
        .row-filters{
            padding-left: 38px;
            .relation-editor{
                box-sizing: border-box;
                position: relative;
                display: flex;
                width: 100%;
                height: 100%;
                .relation-relation {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: flex-start;
                    width: 24px;
                    margin-right: 8px;
                    flex-shrink: 0;
                    .relation-relation-line {
                        position: absolute;
                        left: 12px;
                        width: 1px;
                        background-color: var(--tant-border-color-border1-2);
                        transition: all .3s;
                        top: 6px;
                        height: calc(100% - 12px);
                    }
                    .relation-relation-value{
                        position: absolute;
                        text-transform: uppercase;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%);
                        height: 24px;
                        padding: 0 5px;
                        margin-top: -12px;
                        color: var(--tant-text-gray-color-text1-2);
                        font-size: 12px;
                        line-height: 22px;
                        text-align: center;
                        background-color: #fff;
                        border: 1px solid var(--tant-border-color-border1-2);
                        border-radius: 4px;
                        cursor: pointer;
                        word-break: keep-all;
                        transition: all .3s;
                    }
                    &:hover .relation-relation-line{
                        background-color: var(--tant-primary-color-primary-default);
                    }
                    &:hover .relation-relation-value{
                        color: var(--tant-primary-color-primary-default);
                        border-color: var(--tant-primary-color-primary-default);
                    }
                }
                .relation-main{
                    flex: 1 1;
                    .relation-row{
                        box-sizing: border-box;
                        .multi-filter-condition{
                            .sub-action-row{
                                padding: 4px 0;
                                align-items: center;
                                display: flex;
                                height: auto;
                                .sub-action-left{
                                    align-items: flex-start;
                                    height: auto;
                                    display: flex;
                                }
                                .sub-action-right{
                                    align-items: flex-start;
                                    height: auto;
                                    display: flex;
                                    display: flex;
                                    align-items: center;
                                    opacity: 0;
                                    margin-left: 8px;
                                    transition: opacity .3s;
                                }
                                &:hover .sub-action-right{
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }
        .row-foot{
            margin: 0;
            padding-left: 34px;
            transition: all .3s;
            .ta-filter-button{
                padding: 6px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                font-size: 14px;
                transition: all .3s;
                .action{
                    border-radius: 4px;
                    background-color: var(--tant-primary-color-primary-fill);
                    color: var(--tant-primary-color-primary-default);
                    margin-right: 8px;
                    padding: 3px;
                    font-size: 18px;
                }
                .label{
                    color: var(--tant-primary-color-primary-default);
                }
                &:hover .action{
                    background-color: var(--tant-primary-color-primary-fill-hover);
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
       
}
.branch-container{
    display: block !important;
    margin: 0;
    padding: 0 24px 6px 24px;
    font-size: 14px;
    border-radius: 4px;
    .branch-item{
        position: relative;
        margin: 0 40px;
        .line{
            position: absolute;
            top: 6px;
            bottom: 6px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
        }
        .item-list{
            min-height: 26px;
            padding: 0 18px;
            .sub-action-row{
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;
                .sub-action-left{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    position: relative;
                    .drag-index{
                        flex-shrink: 0;
                        width: 37px;
                        height: 24px;
                        margin-right: 8px;
                        margin-top: 4px;
                        color: var(--tant-text-gray-color-text1-3);
                        background-color: var(--tant-disabled-color-disabled-fill);
                        font-size: 12px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        transition: all .3s;
                        opacity: 1;
                    }
                    .hover-drag{
                        position: absolute;
                        flex-shrink: 0;
                        font-size: 16px;
                        left: 8px;
                        top: 6px;
                        font-style: normal;
                        line-height: 24px;
                        text-align: center;
                        border-radius: 4px;
                        opacity: 0;
                        transition: all .3s;
                    }
                }
                .sub-action-right{
                    align-items: flex-start;
                    height: auto;
                    display: flex;
                    display: flex;
                    align-items: center;
                    opacity: 0;
                    transition: opacity .3s;
                    margin-left: 8px;
                }
                &:hover .sub-action-right{
                    opacity: 1;
                }
                &:hover .drag-index{
                    opacity: 0;
                }
                &:hover .hover-drag{
                    opacity: 1;
                }
            }
        }
        
    }
    .row-foot{
        margin: 0;
        padding:0 24px;
        transition: all .3s;
        .ta-filter-button{
            padding: 6px;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            transition: all .3s;
            .action{
                border-radius: 4px;
                background-color: var(--tant-primary-color-primary-fill);
                color: var(--tant-primary-color-primary-default);
                margin-right: 8px;
                padding: 3px;
                font-size: 18px;
            }
            .label{
                color: var(--tant-primary-color-primary-default);
            }
            &:hover .action{
                background-color: var(--tant-primary-color-primary-fill-hover);
                color: var(--tant-primary-color-primary-hover);
            }
        }
    }
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        
        .sub-action-left :deep(.filter-btn){
            background: #fff;
        }
    }
}
.range-block{
    position: relative;
    padding: 16px;
    background: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-overall);
    .range-title{
        margin-bottom: 5px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 600;
        font-size: 14px;
    }
    .range-content{
        width: 268px;
        margin-top: 14px;
        display: flex;
        align-items: flex-start;
    }
    .inline{
        display: inline-block;
    }
    .range-footer{
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
        button{
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}

.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>