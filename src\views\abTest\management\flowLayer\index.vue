<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <a-input v-model="queryParams.name" placeholder="请输入流量层名称回车搜索" style="width: 240px" @change="init">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-tree-select
              v-model="queryParams.flowDomainCode"
              :allow-search="true"
              :allow-clear="true"
              :disable-filter="true"
              :data="treeData"
              :loading="treeLoading"
              placeholder="选择所属互斥域"
              style="width: 240px"
              @search="onSearch"
              @change="init"
          ></a-tree-select>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            新建流量层
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
      >
        <template #action="{ record }">
          <a-dropdown position="bl" :hide-on-select="false">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption value="edit" @click="editIndex(record)">
                <icon-edit class="mr8"/>
                编辑
              </a-doption>
              <!-- <a-doption value="data">
                <icon-copy class="mr8"/>
                复制
              </a-doption> -->
              <a-popconfirm type="error" :content="`确认删除“${record.name}”?`" @ok="deleteItem(record)">
                <a-doption value="delete">
                  <icon-delete class="mr8"/>
                  删除
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange"/>
      </div>
    </div>
    <handleModal ref="handleRefs" @update-data="init"/>
  </div>

</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import selectApp from "@/components/selected-game-app/index.vue"
import {deleteLayerItem, getDomainGroupList, getLayerList} from "@/api/ab/api"
import {useEventBus} from '@vueuse/core';
import {useRoute} from 'vue-router';
import {LocalStorageEventBus} from "@/types/event-bus";
import handleModal from "./components/handleModal.vue"


const route = useRoute()
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const queryParams = reactive({
  name: '',
  flowDomainCode: undefined,
  current: 1,
  pageSize: 10,
})
const total = ref(0)
// 模拟table数据
const loading = ref(false)
const tableData = ref<any>([])
const columns = ref<any>([
  {
    title: '流量层名称',
    dataIndex: 'name',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '所属互斥域',
    dataIndex: 'flowDomain',
    ellipsis: true,
    slotName: 'flowDomain',
    minWidth: 100,
    render: (value) => {
      const {record} = value;
      return record?.flowDomain?.domainGroup?.name + (record?.flowDomain?.domainGroup?.name ? " - " : "") + record?.flowDomain?.name
    },
  },
  {
    title: '实验数',
    dataIndex: 'experimentNumber',
    ellipsis: true,
    minWidth: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    tooltip: true,
    minWidth: 180,
    render: (value) => {
      const {record} = value;
      return record?.description || '-'
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
    slotName: 'action',
  }
])

// Table页面滑动
const scroll = {
  y: 'calc(100vh - 256px)'
};
const treeLoading = ref(false);
const treeData = ref<any>([]);

// 处理互斥域组数据
function convertDomainGroupToTree(data) {
  return data.map(group => {
    const children: any[] = [];
    // 处理 flowDomains
    if (group.flowDomains && group.flowDomains.length) {
      children.push(...group.flowDomains.map(domain => {
        const domainChildren: any[] = [];
        // 处理 flow_layers（如需要可加到 children）
        if (domain.flowLayers && domain.flowLayers.length) {
          domainChildren.push(...domain.flowLayers.map(layer => ({
            key: layer.code,
            title: layer.name
          })));
        }
        // 处理 subDomainGroups
        if (domain.subDomainGroups && domain.subDomainGroups.length) {
          domainChildren.push(...convertDomainGroupToTree(domain.subDomainGroups));
        }
        return {
          key: domain.code,
          title: domain.name,
          children: domainChildren.length ? domainChildren : undefined
        };
      }));
    }
    return {
      key: group.code,
      title: group.name,
      children: children.length ? children : undefined
    };
  });
}

// 获取互斥域列表
const getflowDomainList = async (name?: string) => {
  treeLoading.value = true;
  treeData.value = []
  try {
    const params = {
      name,
      current: 1,
      pageSize: 20,
    }
    const res = await getDomainGroupList(params);
    if (res.items && res.items.length) {
      treeData.value = convertDomainGroupToTree(res.items);
    }
  } catch (e) {
    console.error('获取互斥域列表失败', e)
  } finally {
    treeLoading.value = false;
  }
}
getflowDomainList()
// 搜索互斥域
const onSearch = async (value: any) => {
  await getflowDomainList(value)
};
const init = () => {
  loading.value = true
  try {
    const params: any = {...queryParams};
    if (!params.name) {
      delete params.name;
    }
    if (params.flowDomainCode === undefined || params.flowDomainCode === null || params.flowDomainCode === '') {
      delete params.flowDomainCode;
    }
    getLayerList(params).then(res => {
      if (res) {
        tableData.value = res.items
        total.value = res.total
      }
    }).catch((e) => {
      console.error('获取流量层列表失败', e)
    }).finally(() => {
      loading.value = false
    })
  } catch (e) {
    loading.value = false
    console.error('获取流量层列表异常', e)
  }
}
init()

// 分页
const pageChange = (v) => {
  queryParams.current = v
  init()
}
const handleRefs = ref()
// 新建
const createIndex = () => {
  handleRefs.value.openModal()
}
// 编辑
const editIndex = (record) => {
  handleRefs.value.openModal(record)
}
// 删除
const deleteItem = async (record: any) => {
  try {
    await deleteLayerItem(record.code);
    Message.success('删除成功');
    init();
  } catch (e) {
    // Message.error(e.msg || '删除失败');
  }
}


localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    init()
  }
})
</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
</style>