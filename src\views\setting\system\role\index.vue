<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchRole"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchRole">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="()=>editModalShow=true">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #optional="{ record }">
          <a-button v-if="record.code !='admin'" type="text" @click="editRole(record)">编辑</a-button>
          <a-popconfirm v-if="record.code !='admin' && record.code !='guest'" :content="`确认删除“${record.name}”?`" @ok="deleteRole(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :top="60"
        :align-center="false"
        :visible="editModalShow"
        title-align="start"
        :title="formData.code?'编辑角色':'新增角色'"
        :ok-text="formData.code?'更新':'保存'"
        @cancel="editCancel"
        @ok="editConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-form-item v-show="formData.code" :required="formData.code" field="code" label="角色编码">
          <a-input v-model="formData.code" disabled/>
        </a-form-item>
        <a-form-item field="name" :rules="[{required:true,message:'角色名称不能为空'}]" label="角色名称">
          <a-input v-model="formData.name" placeholder="请输入角色名称"/>
        </a-form-item>
        <a-form-item field="status" label="角色状态">
          <a-switch v-model="formData.status" :checked-value="1" :unchecked-value="0"/>
        </a-form-item>
        <a-form-item field="menus" label="菜单权限">
          <a-tree-select
              :model-value="formData.menus"
              dropdown-class-name="menu-multi-tree-select-wrapper"
              allow-clear
              allow-search
              tree-check-strictly
              :filter-tree-node="(searchValue, nodeData) =>  nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1"
              tree-checkable
              :max-tag-count="3"
              placeholder="请选择菜单权限"
              :data="treeData"
              @change="value => selectMenu(value)"
          />
        </a-form-item>
        <a-form-item field="permission.application" label="应用权限">
          <a-checkbox-group v-model:model-value="formData.permission.application">
            <a-checkbox :value="1">查看</a-checkbox>
            <a-checkbox :value="2">新增</a-checkbox>
            <a-checkbox :value="3">编辑</a-checkbox>
            <a-checkbox :value="4">删除</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item field="permission.event" label="事件权限">
          <a-checkbox-group v-model:model-value="formData.permission.event">
            <a-checkbox :value="1">查看</a-checkbox>
            <a-checkbox :value="2">新增</a-checkbox>
            <a-checkbox :value="3">编辑</a-checkbox>
            <a-checkbox :value="4">删除</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item field="description" label="角色描述">
          <a-textarea v-model="formData.description" placeholder="请输入角色描述"/>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getSystemMenu, getSystemRole, removeSystemRole, saveSystemRole} from "@/api/setting/api";
import {getMenuList} from "@/api/authorize/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {useRoute} from "vue-router";

const route = useRoute();
const data = ref<any>([]);
const menuData = ref<any>([]);
const treeData = ref<any>([]);
const loading = ref<boolean>(true);
const searchCode = ref<string>();
const searchName = ref<string>();
const formData = ref<any>({
  permission: {
    application: [],
    event: [],
  }
});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const columns = ref<any>([
  {
    title: '角色名称',
    dataIndex: 'name',
    width: 160,
    fixed: 'left',
  },
  {
    title: '角色描述',
    dataIndex: 'description',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '角色状态',
    dataIndex: 'status',
    render: (value) => {
      const {record} = value;
      return record.status ? '启用' : '禁用'
    },
    width: 100,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 240
  },
]);

// 递归处理 treeData中白名单路由
const processTreeData = (data) => {
  return data?.filter(item => item.requiresAuth)?.map(item => {
    const processChildren = (children) => {
      return children?.filter(child => {
        let {requiresAuth} = child;
        child.children?.forEach(filterChild => {
          requiresAuth = requiresAuth || filterChild.requiresAuth;
        });
        return requiresAuth;
      })?.map(child => {
        return {
          ...child,
          key: child.code,
          title: child.name,
          children: processChildren(child.children) // 递归处理子节点
        };
      });
    };

    return {
      ...item,
      key: item.code,
      title: item.name,
      children: processChildren(item.children) // 递归处理一级节点的子节点
    };
  });
};

const searchRole = () => {
  const pageParams = {
    code: searchCode.value,
    name: searchName.value,
  };
  loading.value = true
  getSystemRole(pageParams).then(res => {
    data.value = res
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  getSystemMenu().then(res => {
    menuData.value = res
  })
  getMenuList().then(res => {
    treeData.value = processTreeData(res)
  })
  searchRole()
})

const editRole = (record: any) => {
  formData.value = {...record};
  editModalShow.value = true;
}

const formSubmit = (value) => {
  if (_.isEmpty(value)) {
    Message.warning("角色数据不能为空！")
  }
  saveSystemRole(value).then(res => {
    Message.info("角色数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    searchRole()
  }).catch(e => {
    Message.error("角色数据保存失败！", e)
  })
}

const editConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}

const deleteRole = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除角色id不能为空！")
  }
  removeSystemRole(code).then(res => {
    Message.info("角色数据删除成功！")
    searchRole()
  }).catch(e => {
    Message.error("角色数据删除失败！", e)
  })
}

// 递归获取父节点
const getMenuParents = (menu, tree = treeData.value, parents = []) => {
  for (const node of tree) {
    if (node.key === menu) {
      return parents;
    }
    if (node.children?.length > 0) {
      const result = getMenuParents(menu, node.children, [...parents, node.key]);
      if (result.length > 0) {
        return result;
      }
    }
  }
  return [];
};

// 递归获取子节点
const getMenuChildren = (menu, tree = treeData.value) => {
  for (const node of tree) {
    if (node.key === menu) {
      const children = [];
      const collectChildren = (nodes) => {
        nodes.forEach((child) => {
          children.push(child.key);
          if (child.children?.length > 0) {
            collectChildren(child.children);
          }
        });
      };
      if (node.children?.length > 0) {
        collectChildren(node.children);
      }
      return children;
    }
    if (node.children?.length > 0) {
      const result = getMenuChildren(menu, node.children);
      if (result.length > 0) {
        return result;
      }
    }
  }
  return [];
};

// 菜单选择处理函数
const selectMenu = (value) => {
  const oldValue = formData.value.menus || [];
  const addMenu = value.filter(item => !oldValue.includes(item));

  if (!_.isEmpty(addMenu)) {
    // 新增菜单时，增加其父节点和其所有子节点
    let allParents = [];
    let allChildren = [];
    
    // 处理所有新增菜单项的父子节点
    addMenu.forEach(menu => {
      allParents = [...allParents, ...getMenuParents(menu)];
      allChildren = [...allChildren, ...getMenuChildren(menu)];
    });
    
    formData.value.menus = [...new Set([...value, ...allParents, ...allChildren])];
    return;
  }

  const subMenu = oldValue.filter(item => !value.includes(item));
  if (!_.isEmpty(subMenu)) {
    // 删除菜单时，移除其所有子节点
    const menuChildren = getMenuChildren(subMenu[0]);
    formData.value.menus = value.filter(item => !menuChildren.includes(item));
    console.log("subMenu", subMenu);
  }
};


</script>

<style scoped lang="less">
:global(.menu-multi-tree-select-wrapper .arco-tree-select-tree-wrapper) {
  max-height: 500px !important;
}
</style>