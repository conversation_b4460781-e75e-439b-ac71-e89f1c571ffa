.report-card-container {
  .report-card,.report-half-card {
    width: calc(33.33% - 8px);
    height: 300px;
    margin-top: 12px;
    border-radius: 4px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .report-card-header {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 18px 24px 14px;

      .report-card-title {
        font-weight: bold;
        font-size: 14px;
      }

      .report-card-toolbox {
        padding: 0 4px;
        display: none;
        cursor: pointer;

        .operation-icon {
        }

        border-radius: 4px;
      }

      .report-card-toolbox:hover {
        background-color: var(--tant-bg-gray-color-bg2-1);
      }

      .report-card-indicator {
        display: flex;
        align-items: center;

        .report-card-indicator-item {
          display: flex;
          align-items: end;

          .report-card-indicator-title {
            font-size: 12px;
          }

          .report-card-indicator-data {
            font-size: 16px;
          }
        }

        .up {
          color: green;
        }

        .down {
          color: red;
        }
      }
    }


    .report-card-content {
      height: calc(100% - 70px);
      margin: 0 24px 16px;
    }
  }
  .report-half-card {
    width: calc(50% - 6px);
    height: 500px;
  }
  .report-card:hover .report-card-toolbox,.report-half-card:hover .report-card-toolbox {
    display: inline-block;
  }

  .report-card:hover .report-card-indicator,.report-half-card:hover .report-card-indicator {
    display: none;
  }

  .small-report-card {
    width: calc(16.66% - 10px);
    height: 150px;
    border-radius: 4px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .report-card-header {
      height: 22px;
      display: flex;
      align-items: center;
      margin: 18px 24px 4px;

      .report-card-title {
        font-weight: bold;
        font-size: 14px;
      }
    }

    .report-card-content {
      height: calc(100% - 60px);
      margin: 0 24px 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .primary-data {
        display: flex;
        align-items: end;
        font-size: 32px;
        font-weight: bold;
      }

      .secondary-data {
        display: flex;
        align-items: end;
        font-size: 16px;
      }

      .up {
        color: green;
      }

      .down {
        color: red;
      }
    }
  }
}