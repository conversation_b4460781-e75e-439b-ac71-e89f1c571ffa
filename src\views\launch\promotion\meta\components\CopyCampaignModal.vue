<template>
    <!-- 批量复制广告系列 -->
    <a-modal v-model:visible="modalVisible" :width="700" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert>仅支持复制系统创建且使用系统素材库素材的广告，当前已选中20个广告系列，自动过滤掉0个不符要求的广告系列和415个非系统创建的广告，您希望将广告系列复制到</a-alert>
        <a-form ref="formRef" :rules="rules" :model="form" style="margin-top: 12px;height: 300px;">
            <a-form-item field="accountType" label="账户">
                <a-radio-group v-model="form.accountType" type="button" @change="handleTypeChange">
                    <a-radio value="old">原账户</a-radio>
                    <a-radio value="new">新账户</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item v-if="form.accountType === 'new'" field="accountId" label="新账户">
                <a-select
                    v-model:model-value="form.accountId"
                    placeholder="请选择"
                    allow-clear>
                </a-select>
            </a-form-item>
            <a-form-item v-if="form.accountType === 'new'" field="fbList" label="FB个人号">
                <a-table
                    :columns="columns"
                    :data="form.fbList"
                    :hoverable="true"
                    sticky-header
                    :pagination="false"
                >
                    <template #fbId="{ rowIndex }">
                        <a-select v-model="form.fbList[rowIndex].fbId">
                        </a-select>
                    </template>
                </a-table>
            </a-form-item>
            <a-form-item field="numbers" label="复制数量">
                <a-input-number v-model="form.numbers" :min="1"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('复制广告系列')
const form = reactive({
    accountType:'old',
    numbers:1,
    accountId:'',
    fbList:[
        {
            accountName:'',
            fbId:''
        }
    ]
})
const columns = [
    {
        title: '账户',
        dataIndex: 'accountName',
    },
    {
        title: 'FB个人号ID',
        dataIndex: 'fbId',
        slotName: 'fbId',
    },
]
const rules = {
    accountId: [
        {
            required: true,
            message:'请选择账户',
        }
    ],
    fbList: [
        {
            required: true,
            message:'请选择FB个人号',
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const handleTypeChange = () => {

}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>