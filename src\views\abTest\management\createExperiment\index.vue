<template>
  <a-layout class="layout">
    <a-layout-header class="layout__header">
      <div class="header-title">
        <button class="title-button" @click="goBack">
          <span role="img">
            <icon-left size="13"/>
          </span>
        </button>
        <span style="align-items: center">{{ route?.query?.code ? '编辑实验' : '创建实验' }}</span>
      </div>
    </a-layout-header>

    <a-layout-content class="layout__content">
        <div class="container">
            <a-steps v-model:current="currentStep" style="margin-bottom: 20px;">
                <a-step>
                    输入基本信息
                    <template #icon>
                        <icon-mind-mapping />
                    </template>
                </a-step>
                <a-step>
                    设置生效策略
                    <template #icon>
                        <icon-record-stop />
                    </template>
                </a-step>
                <a-step>
                    配置实验版本
                    <template #icon>
                        <icon-experiment />
                    </template>
                </a-step>
                <a-step>
                    添加实验指标
                    <template #icon>
                        <icon-bookmark />
                    </template>
                </a-step>
            </a-steps>
            <basicInfo v-if="currentStep ===1" :code="experimentCode" @to-next="addCurrent" @cancle-edit="goBack"/>
            <strategyInfo v-if="currentStep ===2" :code="experimentCode" @to-next="addCurrent" @to-prev="reduceCurrent" @cancle-edit="goBack"/>
            <versionInfo v-if="currentStep ===3" :code="experimentCode" @to-next="addCurrent" @to-prev="reduceCurrent" @cancle-edit="goBack"/>
            <indicatorInfo v-if="currentStep ===4" :code="experimentCode" @to-next="addCurrent" @to-prev="reduceCurrent" @cancle-edit="goBack"/>
        </div>
    </a-layout-content>
    <a-modal
        v-model:visible="modalVisible"
        :ok-button-props="{status:'normal'}"
        :align-center="false"
        :cancel-button-props="{type:'text' }"
        width="320px"
        top="120px"
        cancel-text="继续编辑"
        ok-text="退出" title-align="start"
        @ok="deleteConfirm" >
      <template #title>
        退出编辑
      </template>
      <div>
        实验创建尚未完成，基本信息填写完成的实验将保存至草稿箱， 是否确认跳出实验创建?
      </div>
    </a-modal>
  </a-layout>
</template>

<script setup lang="ts">
import {ref} from "vue";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import {useRoute} from 'vue-router';
import basicInfo from './components/basicInfo.vue'
import strategyInfo from './components/strategyInfo.vue'
import versionInfo from './components/versionInfo.vue'
import indicatorInfo from './components/indicatorInfo.vue'


const route = useRoute()
const modalVisible = ref(false)
const experimentCode = ref('');

const currentStep = ref(1)
const goBack = () => {
    modalVisible.value = true
//   router.back()
}
const deleteConfirm = ()=>{
    router.push({
        name: ROUTE_NAME.AB_TEST_EXPERIMENT,
    });
}

const addCurrent = (code?: string) => {
  if (code) experimentCode.value = code;
  currentStep.value += 1;
}
const reduceCurrent = () => {
  if (currentStep.value > 1) {
    currentStep.value -= 1;
  }
}
// 获取详情
const init = () => {
  // 编辑时,接口获取详情
  experimentCode.value = route.query.code as string || '';
  if(route.query.remainEditStep !== undefined){
    const routeStep = Number(route.query.remainEditStep) ?? 3
    currentStep.value = 5 - routeStep;
  }
}
init()
</script>

<style scoped lang="less">
.layout {
  background-color: var(--tant-bg-gray-color-bg2-1);
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;

  .layout__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 12px 24px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .header-title {
      flex: 1 1;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header4-medium);
      align-items: center;

      .title-button {
        color: var(--tant-text-gray-color-text1-2);
        background-color: transparent;
        border: none;
        margin-right: 4px;
        padding: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;
        transition: .5s;

      }

      .title-button > span {
        display: flex;
        align-items: center;
        line-height: normal;
      }

    }
  }

  .layout__content {
    display: flex;
    flex: 1 1;
    height: calc(100vh - 100px);
    margin: 24px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
  }
}
.container{
    width: 100%;
    height: calc(100vh - 120px);
    overflow: auto;
    padding: 20px 70px;
}
</style>