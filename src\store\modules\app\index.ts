import {defineStore} from 'pinia';
import type {NotificationReturn} from '@arco-design/web-vue/es/notification/interface';
import type {RouteRecordNormalized} from 'vue-router';
import defaultSettings from '@/config/settings.json';
import {getMenuList} from '@/api/authorize/api';
import {AppState} from './types';
import {menuToRouter} from "@/utils/menuUtil";

const useAppStore = defineStore('app', {
  state: (): AppState => ({...defaultSettings}),

  getters: {
    appCurrentSetting(state: AppState): AppState {
      return {...state};
    },
    appDevice(state: AppState) {
      return state.device;
    },
    appAsyncMenus(state: AppState): RouteRecordNormalized[] {
      return state.serverMenu as unknown as RouteRecordNormalized[];
    },
  },

  actions: {
    // Update app settings
    updateSettings(partial: Partial<AppState>) {
      // @ts-ignore-next-line
      this.$patch(partial);
    },

    // Change theme color
    toggleTheme(dark: boolean) {
      // this.theme = 'dark';
      // document.body.setAttribute('arco-theme', 'dark');
      // 禁用黑色主题
      this.theme = 'light';
      document.body.removeAttribute('arco-theme');
    },
    toggleDevice(device: string) {
      this.device = device;
    },
    toggleMenu(value: boolean) {
      this.hideMenu = value;
    },
    async fetchServerMenuConfig() {
      let notifyInstance: NotificationReturn | null = null;
      try {
        // notifyInstance = Notification.info({
        //   id: 'menuNotice', // Keep the instance id the same
        //   content: 'loading',
        //   closable: true,
        // });
        const data = await getMenuList();
        this.serverMenu = menuToRouter(data);
        // notifyInstance = Notification.success({
        //   id: 'menuNotice',
        //   content: 'success',
        //   closable: true,
        // });
      } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        // notifyInstance = Notification.error({
        //   id: 'menuNotice',
        //   content: 'error',
        //   closable: true,
        // });
      }
    },
    clearServerMenu() {
      this.serverMenu = [];
    },
  },
});

export default useAppStore;
