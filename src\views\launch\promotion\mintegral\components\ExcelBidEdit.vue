
<template>
    <!-- Excel出价编辑/预算编辑 -->
    <a-modal v-model:visible="modalVisible" :width="625" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="form-content">
            <div class="form-item">
                <div class="index-title">
                    <span class="index">1</span>
                    <span>下载文件，支持导出前1万行</span>
                </div>
                <div class="word">
                    1.空白文件只包含表头信息，需要手动填入待编辑的广告ID <br>
                    2.数据文件会导出当前列表的数据，目前仅支持导出前50,000行，如果超出限制，请增加筛选条件后再下载
                </div>
                <div class="btn">
                    <a-button style="margin-right: 12px;">空白文件</a-button>
                    <a-button v-if="handleType === 'subChannel'">数据文件</a-button>
                </div>
                <div class="word">请注意，单次上传的数据条数不能超过50000条，模版说明如下：</div>
                <a-table
                    :columns="columns"
                    :data="tableData"
                    :hoverable="true"
                    :pagination="false"
                    size="small"
                    style="margin-left: 25px;"
                >
                </a-table>
            </div>
            <div class="form-item">
                <div class="index-title">
                    <span class="index">2</span>
                    <span> 请对第一步下载的文件进行编辑后再上传</span>
                </div>
                <div class="btn">
                    <a-upload draggable action="/" accept=".xlsx,.xls" :limit="1"/>
                </div>
            </div>
            <div class="tip">提示：点击确定后，可以在任务列表中查看执行结果</div>
        </div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('Excel出价编辑')


const emits = defineEmits(['updateData']);
const handleType = ref('')

const columns = reactive([
    {
        title: '模板列',
        dataIndex: 'name',
        minWidth: 180,
    },
    {
        title: '列值说明',
        dataIndex: 'describe',
    },
])
const bidData = [
    {
        name:'Offer Name',
        describe:'用于录入需要更新出价的广告单元名称',
    },
    {
        name:'geo',
        describe:'需要更新出价的广告单元定向地区缩写，示例： CN',
    },
    {
        name:'sub source',
        describe:'需要变更价格的子渠道，例如：mtg1234567890',
    },
    {
        name:'bid way',
        describe:'1、出价类型，可选枚举包括fixed，ratio两种 2、fixed：最终出价=bid输入值 3、ratio：最终出价=bid×geo价格',
    },
    {
        name:'bid',
        describe:'1、子渠道出价的值，小数点后只能保留3位2、若子渠道价格恢复默认，即，继承关联地区的价格，该值传“default”3、当 bid way=ratio时， bid单元格必须包含 % 或者为 default，例如85%',
    },
    {
        name:'currency',
        describe:'1、出价币种，示例 USD,CNY 2、该币种与广告单元在Mintegral所用币种一致，不允许切换',
    },
]
const budgetData = [
    {
        name:'Offer Name',
        describe:'用于录入需要更新出价的广告单元名称',
    },
    {
        name:'geo',
        describe:'1、设置预算的地区，示例： CN2、多个地区共享预算，将地区录入相同单元格，并用英文逗号分隔即可3、geo写ALL，表示不单独设置分地区预算，offer下所有定向区域共享指定预算',
    },
    {
        name:'budget type',
        describe:'预算设置类型，budget表示按金额设置预算',
    },
    {
        name:'daily budget',
        describe:'1、可用的日预算的值2、预算不限，设置该值为“open”',
    },
    {
        name:'total budget',
        describe:'1、用于设置该预算组可用的总预算金额2、不设置总预算，该值为“open”',
    }
]
const tableData = ref<any[]>([])
const openModal = async (type:string) => {
    handleType.value = type
    modalTitle.value = type === 'subChannel' ? 'Excel出价编辑' : 'Excel预算编辑'
    tableData.value = type === 'subChannel' ? bidData : budgetData
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.form-content{
    max-height: 740px;
    overflow-y: auto;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.form-item{
    position: relative;
    line-height: 32px;
    font-size: 12px;
    margin-bottom: 24px;
    .index{
        margin-right: 5px;
        border-radius: 50%;
        color: #0b75ff;
        background: #e6f1ff;
        font-size: 12px;
        width: 18px;
        height: 18px;
        display: inline-block;
        text-align: center;
        line-height: 18px;
    }
    .btn{
        margin-left: 25px;
        display: flex;
        align-items: center;
    }
}
.word{
    line-height: 20px;
    margin-left: 25px;
    margin-bottom: 10px;
    word-break: break-word;
}
.tip{
    color: #999;
    margin: 0 25px 0;
    font-size: 12px;
}
</style>