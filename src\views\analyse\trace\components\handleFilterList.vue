

<template>
    <div v-for="(item,index) in eventListData" :key="index" class="action-row">
        <div class="filter-row-eventRow">
            <div class="action-left">
                <div class="row-content">
                    <div style="display: flex;align-items: center;">
                        <a-dropdown @select="eventDropSelect">
                            <div class="filter-btn">
                                <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
                                    <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                        <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                    </svg>
                                </svg>
                                <span class="filter-label">{{ eventDropName }}</span>
                            </div>
                            <template #content>
                                <a-doption v-for="(drop,dropIndex) in dropList" :key="dropIndex" :value="item.label">{{ drop.label }}</a-doption>
                            </template>
                        </a-dropdown>
                        <span class="word">作为</span>
                        <a-dropdown @select="eventTypeSelect">
                            <div class="filter-btn">
                                <span class="filter-label">{{eventDropType}}</span>
                            </div>
                            <template #content>
                                <a-doption value="begin">初始事件</a-doption>
                                <a-doption value="end">结束事件</a-doption>
                            </template>
                        </a-dropdown>
                    </div>
                </div>
            </div>
            <div class="action-right">
                <a-space align="center">
                    <a-tooltip content="指标筛选" position="top">
                        <a-button class="btn-bg btn-26" @click="add(index)">
                            <template #icon>
                                <icon-filter />
                            </template>
                        </a-button>
                    </a-tooltip>
                </a-space>
            </div>
        </div>
        <eventQueryFilter :ref="el => queryFilterRefs[index] = el" :filter="item.filter" :code-list="item.eventList" :show-detail-filter="true" :only-event="true" @query-filters-change="queryFiltersChange(index,$event)"/>
        <div v-if="item.filtersList&&item.filtersList.length" class="row-foot">
            <div class="ta-filter-button" @click="add(index)">
                <icon-plus class="action"/>
                <span class="label">添加条件</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import {Indicator} from "@/api/analyse/type";
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import {cloneDeep} from "lodash"

const props = defineProps({
  handleList:{
    type:Array,
    default:() => []
  },
  showSub:{
    type:Boolean,
    default:false
  },
})
const eventBus = useEventBus('eventList');

const dropList = ref<any>([
    {
        label:'用户登录'
    },
    {
        label:'账号注册'
    },
    {
        label:'用户登出'
    },
    {
        label:'角色升级'
    },
    {
        label:'设备激活'
    },
    {
        label:'游戏启动'
    },
    {
        label:'游戏登出'
    },
    {
        label:'在线数据'
    },
    {
        label:'发起订单'
    },
])
const eventDropName = ref('用户登录')
const eventDropType = ref('初始事件')
const eventDropSelect = (v) => {
    eventDropName.value = v
}
const eventTypeSelect = (v) => {
    eventDropType.value = v === 'begin' ? '初始事件' : '结束事件'
}
const emits = defineEmits(['traceChange'])

const indicator = ref<Indicator[]>([])

const eventListData = ref<any>([])
eventListData.value = cloneDeep(props.handleList)
const queryFilterRefs = ref<any>([])



// 添加并列条件
const add = (index1:number) => {
    queryFilterRefs.value[index1].add()
}
const queryFiltersChange = (index:number,v) => {
  eventListData.value[index].filter = v;
}




watch(eventListData.value, (newValue, oldValue) => {
    indicator.value = newValue.map(item => {
        return {
            displayName: item.displayName,
            displayType:item.displayType,
            name: item.name,
            type: item.type,
            eventList: item.eventList,
            filter: item.filter
        }
    })
    emits('traceChange',indicator.value)

},{immediate:true})
</script>

<style scoped lang="less">
.action-row{
    position: relative;
    height: auto;
    width: 100%;
    min-height: 24px;
    line-height: 24px;
    padding-right: 24px;
    padding-left: 24px;
    .action-left{
        align-items: flex-start;
        height: auto;
        display: flex;
        .row-content{
            flex-grow: 1;
            box-sizing: border-box;
            padding: 4px 0;
            .rename{
                min-width: 80px;
                max-width: calc(100% - 50px);
                height: 24px;
                padding: 0;
                line-height: 24px;
                background: inherit;
                margin-bottom: 6px;
                // font-weight: 600;
                font-size: 14px;
                display: flex;
                align-items: center;
                .placeholder{
                    max-width: 260px;
                    display: inline-block;
                    height: 32px;
                    line-height: 32px;
                    padding: 0 10px;
                    overflow: hidden;
                    font-size: 14px;
                    // white-space: pre;
                    // vertical-align: middle;
                    &:hover{
                        color: var(--tant-primary-color-primary-default);
                    }
                }
                :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                    font-weight: 600!important;
                }
                :deep(.arco-input-wrapper){
                    border: none;
                    background-color: transparent;
                    font-weight: 600;
                    &:hover{
                        border: none;
                        background-color: transparent;
                        color: var(--tant-primary-color-primary-default);
                    }
                }
            }
            .event-item{
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                padding: 4px 0;
                overflow: hidden;
                line-height: 32px;
                white-space: normal;
            }
        }
    }
    .action-right{
        position: absolute;
        top: 0;
        right: 24px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
    }
    .filter-btn{
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;
        .btn-icon{
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            margin-right: 5px;
        }
        .filter-label{
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
        }

        &:hover{
            border-color: var(--tant-primary-color-primary-hover);
        }
    }
    .row-word{
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
    }
    &:hover{
    background-color: var(--tant-fill-color-fill1-2);
    .action-left .row-content :deep(.filter-btn){
        background: #fff;
    }
    .action-left .row-content :deep(.filter-icon){
        background: #fff;
    }
    .sub-action-left :deep(.filter-btn){
        background: #fff;
    }
    .action-right{
        opacity: 1;
    }
}
}

.row-foot{
    margin: 0;
    // padding-left: 34px;
    transition: all .3s;
    .ta-filter-button{
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;
        .action{
            border-radius: 4px;
            background-color: var(--tant-primary-color-primary-fill);
            color: var(--tant-primary-color-primary-default);
            margin-right: 8px;
            padding: 3px;
            font-size: 18px;
        }
        .label{
            color: var(--tant-primary-color-primary-default);
        }
        &:hover .action{
            background-color: var(--tant-primary-color-primary-fill-hover);
            color: var(--tant-primary-color-primary-hover);
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
.word{
    margin: 0 8px;
    color: var(--tant-text-gray-color-text1-3);
    font-size: 14px
}
</style>