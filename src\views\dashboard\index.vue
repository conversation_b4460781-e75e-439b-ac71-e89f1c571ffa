<template>
  <div class="container">
    <div v-if="loading" class="spin">
      <a-spin :loading="loading" class="spin-icon" dot>
        <template #tip>
          <span class="spin-tip">加载中...</span>
        </template>
      </a-spin>
    </div>
    <div v-else-if="_.isEmpty(dashboardStore.dashboardSelected?.dashboardId)&&showOtherDashboard===''" class="no-choose">
      <div class="empty-body">
        <div style="box-sizing: border-box">
          <div class="empty-img">
            <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
          </div>
          <div class="empty-description">
            <div class="title">暂未选择任何看板，请点击左侧看板列表选择</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="showOtherDashboard===''&&dashboardStore.dashboardSelected?.dashboardId" class="dashboard">
      <Nav
          :refresh-all-reports="refreshAllReports"
          :last-refresh-time="lastRefreshTime"
          :dashboard="dashboardDetail"
          @add-note="layoutAddedNote"
          @date-range-change="dateRangeChange"
      />
      <div v-if="layout?.length>0" class="card-layout">
        <grid-layout
            v-model:layout="layout"
            :col-num="20"
            :row-height="30"
            :is-resizable="false"
            :margin="[15, 15]"
            @layout-updated="layoutUpdated"
        >
          <grid-item
              v-for="(item, index) in layout"
              :key="item.objectCode"
              v-viewport="(v) => handleVisibilityChange(item.i, v)"
              :data-item-id="item.i"
              :x="item.x"
              :y="item.y"
              :w="item.w"
              :h="item.h"
              :i="item.i"
              drag-allow-from=".drag-allow-from"
              drag-ignore-from=".no-drag"
              :is-resizable="item.objectType === 0"
              :min-w="4"
              :min-h="2"
              :is-draggable="item.isDraggable">
            <ReportCard
                v-if="item.objectType === 1 && visibleMap[item.i]"
                :ref="setReportCardRef(index)"
                :report="item"
                :dashboard-id="dashboardSelected.dashboardId"
                :query-param="queryParam"
                @resize="resize"
                @delete-report="deleteReport"
                @fetch-dashboard="fetchDashboard"
                @change-params="changeParams"
                @copy-report="copyReport"
            />
            <NoteCard
                v-if="item.objectType === 0 && visibleMap[item.i]"
                :ref="setReportCardRef(index)"
                class="drag-allow-from no-drag"
                :report="item"
                :dashboard-id="dashboardSelected.dashboardId"
                @resize="resize"
                @add-note="layoutAddedNote"
            />
          </grid-item>
        </grid-layout>
      </div>
      <div v-else class="empty">
        <div class="empty-body">
          <div style="box-sizing: border-box">
            <div class="empty-img">
              <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
            </div>
            <div class="empty-description">
              <div class="title">暂未添加任何图表，请点击添加</div>
              <a-dropdown trigger="click" position="top" :popup-max-height="false" @select="handleSelect">
                <a-button class="button-add">
                  <icon-plus/>
                  添加图表
                </a-button>
                <template #content>
                  <a-dsubmenu trigger="hover">
                    <template #default>新建报表</template>
                    <template #content>
                      <a-doption
                          v-for="(item,index) in selectOptions" :key="index" :value="item.value"
                          @click="gotoItem(item.path)">
                        <template #icon>
                          <img :src="`/icon/topMenu/${item.value}.svg`" style="width: 14px; height: 14px;" alt=""/>
                        </template>
                        {{ item.label }}
                      </a-doption>
                    </template>
                  </a-dsubmenu>
                  <a-doption value="exist">已存报表</a-doption>
                </template>
              </a-dropdown>
              <exist-report ref="existModalRef"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {defineAsyncComponent, nextTick, onMounted, reactive, ref, watch} from 'vue';
import {GridItem, GridLayout, Layout} from 'grid-layout-plus';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {detailDashboard, saveDashboardUI} from "@/api/dashboard/api";
import {DashboardDto} from "@/api/dashboard/type";
import dayjs from "dayjs";
import {useDashboardStore} from "@/store";
import _ from "lodash";
import {DashboardEventBus, RefreshEvent, UpdateReportCountEvent} from "@/types/event-bus";
import ExistReport from "@/components/navbar-board/exist-report.vue";
import router from "@/router";
import {ReportAnalyseModel} from "@/api/analyse/type";
import {ROUTE_NAME} from "@/router/constants";
import {roundPageSizeH} from "@/views/dashboard/util";
import {useRoute} from 'vue-router';
import Nav from './components/nav.vue';

const route = useRoute();
const isFirstUpdate = ref(true)
const showOtherDashboard = ref('')
const dashboardEventBus = useEventBus<string>(DashboardEventBus)
const updateCountBus = useEventBus(UpdateReportCountEvent)
// 用于跟踪当前请求的看板ID，避免竞态条件
const currentRequestDashboardId = ref('')
const ReportCard = defineAsyncComponent(() => import('@/views/dashboard/components/report-card.vue'));
const NoteCard = defineAsyncComponent(() => import('@/views/dashboard/components/note-card.vue'));
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const lastRefreshTime = ref<string | undefined>(dayjs().format('YYYY-MM-DD hh:mm:ss'));
const dashboardDetail = ref<DashboardDto>()
const layout = ref<Layout>()
const reportCardRefs = ref([]);
const setReportCardRef = (index: number) => (el: any) => {
  reportCardRefs.value[index] = el;
};
const loading = ref(true)
// 可视性映射，用于跟踪组件是否可见
const visibleMap = ref<Record<string, boolean>>({});

// 处理可见性变化
const handleVisibilityChange = (itemId: string, isVisible: boolean) => {
  visibleMap.value[itemId] = isVisible;
};

// 初始化首屏可见组件
const initVisibleComponents = () => {
  // 使用 IntersectionObserver 检测首屏可见的组件
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const gridItem = entry.target;
        const itemId = gridItem.getAttribute('data-item-id');
        if (itemId) {
          visibleMap.value[itemId] = true;
        }
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '100px'
  });

  // 观察所有 grid-item
  const gridItems = document.querySelectorAll('.vue-grid-item');
  gridItems.forEach(item => {
    const itemId = item.getAttribute('data-item-id');
    if (itemId) {
      observer.observe(item);
    }
  });

  // 短时间后断开观察器，避免内存泄漏
  setTimeout(() => {
    observer.disconnect();
  }, 1000);
};
const dashboardStore = useDashboardStore()
const gotoItem = (value: string) => {
  router.push({
    name: value,
  });
};
// 获取组件引用的辅助函数
const getComponentRef = (item: any, index: number) => {
  // 根据组件类型返回对应的ref
  return reportCardRefs.value[index];
};

// 只刷新可见组件的函数
const refreshVisibleComponents = () => {
  layout.value?.forEach((item, index) => {
    // 只刷新已经可见的组件
    if (visibleMap.value[item.i]) {
      const componentRef = getComponentRef(item, index);
      if (componentRef && typeof componentRef.freshData === 'function') {
        componentRef.freshData();
      }
    }
  });
};

// 刷新报表 - 只刷新可见组件
const refreshAllReports = _.debounce(async () => {
  lastRefreshTime.value = dayjs().format('YYYY-MM-DD hh:mm:ss')
  // 只刷新可见的组件
  refreshVisibleComponents();
}, 500)

// 刷新所有组件（保留原有逻辑，用于特殊情况）
const refreshAllReportsForce = _.debounce(async () => {
  lastRefreshTime.value = dayjs().format('YYYY-MM-DD hh:mm:ss')
  const promises = reportCardRefs.value
      .filter(ref => ref) // 过滤掉空值
      .map(reportCardRef => reportCardRef.freshData())

  await Promise.all(promises) // 并行执行所有请求
}, 500)
const selectOptions = [
  {label: '事件分析', value: ReportAnalyseModel.EVENT, path: ROUTE_NAME.ANALYSE_EVENT},
  {label: '留存分析', value: ReportAnalyseModel.RETENTION, path: ROUTE_NAME.ANALYSE_RETENTION},
  {label: '漏斗分析', value: ReportAnalyseModel.FUNNEL, path: ROUTE_NAME.ANALYSE_FUNNEL},
  {label: '属性分析', value: ReportAnalyseModel.PROPERTY, path: ROUTE_NAME.ANALYSE_PROPERTY},
  {label: '路径分析', value: ReportAnalyseModel.TRACE, path: ROUTE_NAME.ANALYSE_TRACE},
  {label: '分布分析', value: ReportAnalyseModel.SCATTER, path: ROUTE_NAME.ANALYSE_SCATTER},
  {label: 'SQL分析', value: ReportAnalyseModel.CUSTOM, path: ROUTE_NAME.ANALYSE_CUSTOM_SQL},
  {label: '间隔分析', value: ReportAnalyseModel.INTERVAL, path: ROUTE_NAME.ANALYSE_INTERVAL},
  {label: '归因分析', value: ReportAnalyseModel.ATTRIBUTION, path: ROUTE_NAME.ANALYSE_ATTRIBUTION},
  {label: '运营分析', value: ReportAnalyseModel.APPLICATION, path: ROUTE_NAME.ANALYSE_APPLICATION},
  {label: '群组分析', value: ReportAnalyseModel.GROUP, path: ROUTE_NAME.ANALYSE_GROUP},
]
const queryParam = reactive<any>({})
const layoutUpdated = (newLayout: Layout) => {
  if (isFirstUpdate.value) {
    isFirstUpdate.value = false
    return
  }
  if (_.eq(newLayout, layout.value)) {
    // todo 减少重复处理
  }
  // 检查当前是否还是同一个看板，避免切换看板时的竞态条件
  const currentDashboardId = dashboardStore.dashboardSelected?.dashboardId || dashboardSelected.value.dashboardId;
  if (currentDashboardId !== currentRequestDashboardId.value) {
    console.warn('看板已切换，忽略过期的布局更新');
    return;
  }
  let minXY = 0;
  newLayout.forEach(config => {
    minXY = Math.min(minXY, config.x, config.y);
  });
  if (minXY < 0) {
    layout.value = newLayout.map(config => {
      return {
        ...config,
        x: Math.max(config.x, 0),
        y: Math.max(config.y, 0),
      }
    })
  }
  // 新布局保存
  saveDashboardUI({
    dashboardId: currentDashboardId,
    uiConfig: newLayout.map(config => {
      return {
        ...config,
        x: Math.max(config.x, 0),
        y: Math.max(config.y, 0),
        code: config.i,
        type: config.objectType,
        configParams: config.configParams
      }
    })
  })
}


const existModalRef = ref()
const handleSelect = (value: string) => {
  switch (value) {
    case "exist":
      existModalRef.value.openModal()
      break
    default:
  }
}

const resize = (size: string, code: number, pageSize: number | undefined) => {
  let width: number
  let height: number
  switch (size) {
    case 'small':
      width = 5
      height = 5
      break
    case 'middle':
      width = 10
      height = pageSize ? pageSize * 0.67 + 4.25 : 10.95
      break
    case 'large':
      width = 20
      height = pageSize ? pageSize * 0.67 + 4.25 : 10.95
      break
    default:
      width = 10
      height = 10.95
  }

  layout.value = layout.value?.map(item => {
    if (item.objectCode === code) {
      let xValue = item.x;
      if (xValue + width > 20) {
        xValue = 20 - width
      }
      return {
        ...item,
        w: width,
        h: height,
        x: xValue
      }
    }
    return item;
  })
};

const changeParams = (value: string, code: number) => {
  layout.value = layout.value?.map(item => {
    if (item.objectCode === code) {
      return {
        ...item,
        configParams: value
      }
    }
    return item;
  })
}
const deleteReport = (code: string) => {
  layout.value = layout.value?.filter(item => {
    return item.objectCode !== code;
  })
  // 更新看板中数量
  const countData = {
    dashboardId: dashboardSelected.value.dashboardId,
    count: layout.value?.length || 0
  }
  updateCountBus.emit(UpdateReportCountEvent, countData)
}

const copyReport = (newReportConfig: any) => {
  // 添加复制的报表到layout中
  layout.value?.push(newReportConfig);

  // 更新看板中数量
  const countData = {
    dashboardId: dashboardSelected.value.dashboardId,
    count: layout.value?.length || 0
  }
  updateCountBus.emit(UpdateReportCountEvent, countData)
}


/**
 * 查询看板数据
 */
function fetchDashboard(dashboardId?: string) {
  isFirstUpdate.value = true
  loading.value = true
  // if (!dashboardStore.dashboardSelected?.dashboardId || dashboardStore.dashboardSelected?.dashboardId === '') {
  //   loading.value = false
  //   return
  // }
  // 如果 store 中的数据丢失，从 localStorage 恢复
  if (!dashboardStore.dashboardSelected && dashboardSelected.value.dashboardId) {
    dashboardStore.dashboardSelected = dashboardSelected.value;
  }

  const currentDashboardId = dashboardStore.dashboardSelected?.dashboardId || dashboardSelected.value.dashboardId;

  if (!currentDashboardId || currentDashboardId === '') {
    loading.value = false;
    return;
  }

  // 记录当前请求的看板ID，用于后续校验
  currentRequestDashboardId.value = currentDashboardId;

  detailDashboard(currentDashboardId).then((response: DashboardDto) => {
    // 检查返回时是否还是请求的同一个看板，避免竞态条件
    if (currentRequestDashboardId.value !== currentDashboardId) {
      console.warn('看板已切换，忽略过期的请求响应');
      return;
    }

    dashboardDetail.value = response

    layout.value = response.uiConfig?.map(config => {
      return {
        ...config,
        h: roundPageSizeH(config.h),
        i: config.objectCode
      }
    })

    // 看板数据加载完成后，重置可见性映射
    visibleMap.value = {};

    loading.value = false

    // 等待 DOM 更新后，初始化首屏可见组件
    nextTick(() => {
      initVisibleComponents();
    });
  }).catch(e => {
    // 同样检查错误处理时的看板状态
    if (currentRequestDashboardId.value === currentDashboardId) {
      loading.value = false;
    }
    console.error(e)
  })
}

const layoutAddedNote = (note: any, size: string) => {
  // 偶发界面重叠
  const newloayout = {
    objectType: 0,
    objectCode: note,
    x: 0,
    y: layout.value?.length * 10,
    h: size === 'small' ? 5 : size === 'middle' ? 10.95 : 10.95,
    w: size === 'small' ? 5 : size === 'middle' ? 10 : 20,
    i: note,
    configParams: {},
    isDraggable: true,
    move: false
  };
  layout.value.push(newloayout)
}

dashboardEventBus.on((event) => {
  if (event === RefreshEvent) {
    // 更新时不会触发路由监听，refresh时确保route中的数据是最新的
    const currentAppId = useSessionStorage('app-id', '').value;
    if (route.meta) {
      route.meta.pageConfig = {
        ...(route.meta.pageConfig || {}),
        appId: currentAppId,
        boardDate:queryParam?.dateRange
      };
    }
    fetchDashboard()
  }
})

// 监听store的看板选择来决定loading
watch(() => dashboardStore.dashboardSelected, (newValue, oldValue) => {
  if (newValue?.dashboardId === oldValue?.dashboardId && newValue?.folderId === oldValue?.folderId && newValue?.spaceId === oldValue?.spaceId) return
  // 清空当前请求ID，准备切换到新看板
  currentRequestDashboardId.value = '';
  fetchDashboard()
  showOtherDashboard.value = ''
}, {deep: true});
watch(() => dashboardSelected, (newVal) => {
  dashboardSelected.value = newVal.value
}, {deep: true})

onMounted(() => {
  lastRefreshTime.value = dayjs().format('YYYY-MM-DD hh:mm:ss')

  if (!dashboardStore.dashboardSelected?.dashboardId || dashboardStore.dashboardSelected?.dashboardId === '') {
    loading.value = false

  }
  showOtherDashboard.value = dashboardSelected?.value.dashboardId.slice(-2)
  fetchDashboard()
  showOtherDashboard.value = ''
})

// 时间变化
const dateRangeChange = (v: any) => {
  queryParam.dateRange = v
  // 时间变化时只刷新可见的组件
  refreshVisibleComponents()
}

// 监听组件可见性变化，当组件变为可见时自动加载数据
// 在 dashboard 中，每个 ReportCard 组件会在 mounted 时自动调用 freshData
// 所以这里不需要额外的刷新逻辑，组件可见性由 v-if 控制即可
</script>

<style lang="less" scoped>
.container {
  display: flex;
  min-width: 800px;
  background-color: var(--color-fill-2);
  height: 100%;
  width: 100%;

  .spin {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .spin-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .spin-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--tant-slate-slate-50);
      }
    }

  }

  .no-choose {
    width: 100%;
    height: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);

    .empty-body {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: var(--color-white);

      .empty-img {
        width: 400px;
        height: 256px;
      }

      .empty-description {
        color: var(--tant-text-gray-color-text1-3);
        font-size: 12px;
        width: 400px;
        word-break: break-all;
        text-align: center;
        margin: 0 auto;

        .title {
          margin-top: 10px;
          margin-bottom: 12px;
          font-weight: 500;
          font-size: 16px;
          color: var(--tant-text-gray-color-text1-2);
        }


      }

    }
  }

  .dashboard {
    width: 100%;
    height: calc(100% - 36px);
    margin: 18px;

    .card-layout {
      height: calc(100% - 56px);
      overflow: auto;
      margin: 18px -14px 0;

      .drag-allow-from {
        width: 100px;
      }

      .vgl-layout {
        //--vgl-placeholder-opacity: 0;
        margin-top: -14px;
        --vgl-placeholder-bg: green;
        height: 100% !important;
      }
    }

    .empty {
      height: calc(100% - 56px);
      margin-top: 18px;
      background-color: var(--tant-bg-white-color-bg1-1);
      border-radius: 4px;

      .empty-body {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: var(--color-white);

        .empty-img {
          width: 400px;
          height: 256px;
        }

        .empty-description {
          color: var(--tant-text-gray-color-text1-3);
          font-size: 12px;
          width: 400px;
          word-break: break-all;
          text-align: center;
          margin: 0 auto;

          .title {
            margin-bottom: 12px;
            font-weight: 500;
            font-size: 16px;
            color: var(--tant-text-gray-color-text1-2);
          }

          .button-add {
            margin-top: 12px;
            color: var(--tant-white-white-100);
            background-color: var(--tant-primary-color-primary-default);
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            padding: 5px 16px;
            font: var(--tant-body-font-body-regular);
            text-transform: capitalize;
            border-radius: var(--tant-border-radius-medium);
            box-shadow: unset;
          }
        }

      }
    }
  }
}
</style>
