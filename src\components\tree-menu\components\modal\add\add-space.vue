<script setup lang="ts">
import {ref} from "vue";
import {saveSpace} from "@/api/space/api";
import {Message} from '@arco-design/web-vue';
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {useEventBus} from "@vueuse/core";
import _ from "lodash";

const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)

const visible = defineModel<boolean>("visible", {default: false});
const spaceName = ref<string>();
const cacheEventBus = useEventBus(CacheEventBus)

const handleOk = () => {
  if (!spaceName.value?.trim()) {
    Message.warning("空间名称不能为空")
    return;
  }
  saveSpace({name: spaceName.value}).then((resp) => {
    const newSpace=_.cloneDeep(resp)
    cacheEventBus.emit('add-event', {
      type: 'space',
      space: newSpace,
    })
    spaceName.value = undefined
    visible.value = false;
  }).catch((e) => {
    Message.error("创建空间失败！", e)
  })
}
const handleCancel = () => {
  visible.value = false;
  spaceName.value = undefined
}
</script>

<template>
  <a-modal
      :visible="visible"
      :mask-closable="false"
      :width="450"
      title-align="start"
      ok-text="创建"
      title="创建空间"
      @ok="handleOk"
      @cancel="handleCancel">
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          空间名称
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-input v-model:model-value="spaceName" placeholder="请输入空间名称"/>
      </a-col>
    </a-row>
  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}
</style>