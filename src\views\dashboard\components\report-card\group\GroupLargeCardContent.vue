<script setup lang="ts">

import {computed, ref, watch} from "vue";
import {ReportQueryResponse} from "@/api/type";
import {ChartType} from "@/api/enum";
import GroupTrend from "@/views/dashboard/components/table-chart/group/GroupTrend.vue";
import GroupTable from "@/views/dashboard/components/table-chart/group/GroupTable.vue";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  // 报表信息
  report: any;
}

const props = defineProps<Props>();
const emits = defineEmits(['pageSizeChange']);
// eslint-disable-next-line vue/no-dupe-keys
const reportAnalysisData = ref<any>();

watch(() => props.reportAnalysisData, (newData) => {
  if (newData === undefined) {
    return
  }
  reportAnalysisData.value = newData?.result;
})

const tableRef = ref()
const chartType = ref<ChartType>(ChartType.TABLE);
const exportXlsx = (date: any, name?: string) => {
  tableRef.value?.exportXlsx(date, name)
}
// 图标高度
const chartHeight = computed(() => reportAnalysisData.value?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)')
defineExpose({
  exportXlsx
})
</script>

<template>
  <div class="card-content" style="display: flex;">
    <div class="card-toolbar">
      <div class="card-filter">
      </div>
      <div class="card-config">
        <a-select
            v-model="chartType"
            :bordered="false"
            :style="{width:'92px',padding:'0 0 0 13px',position:'relative'}"
        >
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/'+data?.value+'-chart.svg'" alt=""/>
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon>
          </template>
          <a-option :value="ChartType.TABLE">
            <template #icon>
              <img class="option-icon" src="/icon/table-chart.svg" alt=""/>
            </template>
            <template #default>表格</template>
          </a-option>
          <a-option :value="ChartType.TREND">
            <template #icon>
              <img class="option-icon" src="/icon/trend-chart.svg" alt=""/>
            </template>
            <template #default>趋势图</template>
          </a-option>
        </a-select>
      </div>
    </div>
    <a-alert v-if="reportAnalysisData?.resultsExceedsLimit" style="margin-top: 5px;">
      因数据条数过多，优先展示前1000条数据
    </a-alert>
    <div class="data-chart" :style="{height: chartHeight}">
      <div class="chart-box">
        <group-table v-if="chartType === ChartType.TABLE" ref="tableRef" :event-data="reportAnalysisData" :page-size="Math.round((props.report?.h - 4.25) / 0.67)"
                     @page-size-change="ps => emits('pageSizeChange',ps )"/>
        <group-trend v-if="chartType === ChartType.TREND" ref="trendRef" :event-data="reportAnalysisData" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@import "@/views/dashboard/style/card.less";
</style>
