import {mergeConfig} from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configImageminPlugin from './plugin/imagemin';

export default mergeConfig(
  {
    mode: 'production',
    plugins: [
      configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configImageminPlugin(),
    ],
    define: {
      'process.env': {
        "BASE_API_URL": "https://backend.ivymobile.cn/",
        "BASE_WS_URL": "wss://backend.ivymobile.cn",
        "BASE_PACK_URL": "wss://backend.ivymobile.com",
      },
      'process.env_offshore_tencent': {
        "BASE_API_URL": "https://backend.ivymobile.cn/",
        "BASE_WS_URL": "wss://backend.ivymobile.cn",
        "BASE_PACK_URL": "wss://backend.ivymobile.com",
      },
      // 'process.env_offshore_gcp': {
      //   "BASE_API_URL": "https://backend.ivymobile.cn/",
      //   "BASE_WS_URL": "wss://backend.ivymobile.cn",
      //   "BASE_PACK_URL": "wss://backend.ivymobile.com",
      // },
      'process.env_onshore_tencent': {
        "BASE_API_URL": "https://backendcn.ivymobile.cn/",
        "BASE_WS_URL": "wss://backendcn.ivymobile.cn",
        "BASE_PACK_URL": "wss://backend.ivymobile.com",
      }
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            arco: ['@arco-design/web-vue'],
            chart: ['echarts', 'vue-echarts'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
    esbuild: {
      drop: ['console', 'debugger'],
      // drop: ['debugger'],
    },
  },
  baseConfig
);
