<!-- 应用管理 -->
<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <a-input
              v-model:model-value="pageParams.text"
              style="background-color: #FFF;width: 300px"
              placeholder="搜索应用名称、应用ID或应用品牌套件"
              allow-clear
              @change="searchChange"
          >
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-select v-model="pageParams.osName" :style="{width:'180px'}" placeholder="平台" allow-clear @change="getList">
            <template #label="{ data }">
              <span>平台-{{ data?.label }}</span>
            </template>
            <a-option value="android">Android</a-option>
            <a-option value="ios">IOS</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-select v-model="pageParams.appStatus" :style="{width:'180px'}" placeholder="应用状态" allow-clear @change="getList">
            <template #label="{ data }">
              <span>应用状态-{{ data?.label }}</span>
            </template>
            <a-option :value="1">已上架</a-option>
            <a-option :value="0">已下架</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-select v-model="pageParams.accessStatus" :style="{width:'180px'}" placeholder="接入状态" allow-clear @change="getList">
            <template #label="{ data }">
              <span>接入状态-{{ data?.label }}</span>
            </template>
            <a-option :value="1">已接入</a-option>
            <a-option :value="0">未接入</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-button class="button" type="primary" style="margin-left: 16px" @click="openAddModal">
            <icon-plus style="margin-right: 3px"/>
            新增应用
          </a-button>
        </div>

      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :pagination="false"
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scrollbar="scrollbar"
          :scroll="scroll">
        <template #appName="{ record }">
          <div class="name-box">
            <div class="avator" @click="toConfig(record)">
              <img :src="record?.icon || 'image/game-default.svg'" alt="">
            </div>
            <div class="desc">
              <div class="title">{{ record.code }}-{{ record.name }}</div>
              <div class="tip">{{ record.package || record.storeId }}</div>
            </div>
          </div>
        </template>
        <template #appStatus="{ record }">
          <a-tag v-if="record.status === 1" color="green">已上架</a-tag>
          <a-tag v-else color="gray">已下架</a-tag>
        </template>
        <template #isInited="{ record }">
            <a-switch v-model="record.isInited" :checked-value="1" :unchecked-value="0" :disabled="record.isInited" size="small" :before-change="() => initChange(record)"/>
        </template>
        <template #accessStatus="{ record }">
          <a-tag v-if="record.accessStatus === 1" color="green">已接入</a-tag>
          <a-tag v-else color="gray">未接入</a-tag>
        </template>
        <template #action="{ record }">
          <a-dropdown v-model:popup-visible="popupVisible[record.code]"  position="bl" :hide-on-select="false" @select="handleSelect($event,record)">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption value="config">
                <icon-settings class="mr8"/>
                应用配置
              </a-doption>
              <a-doption value="data">
                <icon-computer class="mr8"/>
                数据面板
              </a-doption>
              <a-doption v-if="record.status === 1" value="store">
                <icon-launch class="mr8"/>
                前往商店
              </a-doption>
              <a-popconfirm
                :content="`确认归档【${record.code }-${record.name }】？`"
                @ok="fileOk(record)"
                @cancel="fileCancel(record)">
                <a-doption v-if="!record.isArchived"  value="file" @click.stop>
                  <icon-file class="mr8"/>
                  应用归档
                </a-doption>
              </a-popconfirm>
            </template>
          </a-dropdown>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange"/>
      </div>
    </div>
    <addAppModal ref="addRef" @update-data="getList"/>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import {getAppInit, getAppPageList, setAppArchive} from "@/api/marketing/api";
import router from "@/router";
import {Message} from '@arco-design/web-vue';
import {useRoute} from "vue-router";
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {ROUTE_NAME} from "@/router/constants";
import addAppModal from "./compontents/addAppModal.vue";

const route = useRoute();
const total = ref(0)
const loading = ref(false)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
  text: route.query.text as string || '',
  appStatus: undefined,
  osName: undefined,
  accessStatus:undefined,
  // platform:-1
})
const columns = [
  {
    title: '应用名称',
    dataIndex: 'name',
    ellipsis: "true",
    slotName: 'appName',
    width: 400,
    fixed: 'left'
  },
  {
    title: '应用状态',
    dataIndex: 'status',
    ellipsis: "true",
    slotName: 'appStatus',
    minWidth: 100,
  },
  {
    title: '昨日活跃用户数',
    dataIndex: 'lastActiveUsers',
    ellipsis: "true",
    // slotName: '',
    minWidth: 200,
  },
  {
    title: '上架日期',
    dataIndex: 'onlineDate',
    ellipsis: "true",
    // slotName: '',
    minWidth: 200,
  },
  {
    title: '初始化状态',
    dataIndex: 'isInited',
    ellipsis: "true",
    slotName: 'isInited',
    minWidth: 120,
  },
  {
    title: '默认聚合数据源',
    dataIndex: 'firstDataSource',
    ellipsis: "true",
    minWidth: 120,
  },
  {
    title: '默认事件数据源',
    dataIndex: 'eventDataSource',
    ellipsis: "true",
    minWidth: 120,
  },
  {
    title: '接入状态',
    dataIndex: 'accessStatus',
    ellipsis: "true",
    slotName: 'accessStatus',
    minWidth: 120,
  },
  {
    title: '',
    dataIndex: 'action',
    ellipsis: "true",
    slotName: 'action',
    align: 'right',
    fixed: 'right',
    minWidth: 180,
  },
]
const tableData = ref<any>([])
const localStorageEventBus = useEventBus(LocalStorageEventBus)

const scrollbar = ref(true)
const scroll = {x: 1000,y: 'calc(100vh - 250px)'}


const getList = async () => {
  loading.value = true;

  try {
    const params = {
      ...pageParams,
      ...(pageParams.accessStatus === "" && { accessStatus: undefined }),
      ...(pageParams.appStatus === "" && { appStatus: undefined }),
      ...(pageParams.osName === "" && { osName: undefined })
    };

    const res = await getAppPageList(params);

    tableData.value = res.items;
    total.value = res.total;
  } catch (error) {
    console.error('Failed to fetch list:', error);
    // 可以考虑在这里添加错误处理，如显示错误提示
  } finally {
    loading.value = false;
  }
};
const searchChange = (v) => {
  pageParams.text = v
  const query = { ...route.query };
  if (pageParams.text) {
    query.text = pageParams.text;
  } else {
    delete query.text;
  }
  router.replace({ query });
  getList()
}
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
const popupVisible = ref<Record<string, boolean>>({});
const toConfig = (record) => {
  sessionStorage.setItem('app-id', record.code)
  localStorageEventBus.emit("app-id", record.code)
  router.push({name: ROUTE_NAME.APP_CONFIG, query: {code: record.code}})
}
const initChange = async (record) => {
  try {
    await getAppInit(record.code)
    Message.success('应用初始化成功')
    getList()
    return true
  } catch (e) {
    Message.error(e?.detail?.[0]?.msg || '应用初始化失败')
    return false
  }
}
const handleSelect = (v, record) => {
  if (v === 'config') {
    sessionStorage.setItem('app-id', record.code)
    localStorageEventBus.emit("app-id", record.code)
    router.push({name: ROUTE_NAME.APP_CONFIG, query: {code: record.code}})
  } else if (v === 'store' && record.platformLink) {
    window.open(record.platformLink, '_blank');
  }
  if(v !=='file'){
    popupVisible.value[record.code] = false;
  }
}
// 添加归档确认和取消方法
const fileOk = async (record) => {
  popupVisible.value[record.code] = false;
  try {
    await setAppArchive(record.code).then(res => {
      Message.success('归档成功');
      getList()
    })
  } catch (error) {
    Message.error('归档失败');
  }
}

const fileCancel = (record) => {
  popupVisible.value[record.code] = false;
}
const addRef = ref()
const openAddModal = () => {
  addRef.value.openModal()
}
onMounted(() => {
  getList()
})
</script>

<style scoped lang="less">
:deep(.arco-tag) {
  border-radius: 4px;
}

.mr8 {
  margin-right: 8px;
}

.setting {
  width: 20px;
  height: 20px;
  background: transparent;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}

.page-body {
  height: 100%;
  width: 100%;
  background-color: var(--tant-bg-white-color-bg1-1);

  .body-head {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .right-action {
      display: flex;
    }
  }
}

.name-box {
  display: flex;
  align-items: center;

  .avator {
    width: 48px;
    height: 48px;
    border-radius: 4px;
    margin-right: 12px;
    flex-shrink: 0;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
  }

  .desc {
    .title, .tip {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 300px;
    }

    .tip {
      color: #adabab;
    }
  }
}
</style>