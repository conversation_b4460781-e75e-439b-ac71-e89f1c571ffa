<script setup lang="ts">
import {computed, ref, watch} from "vue";
import {useLocalStorage} from '@vueuse/core';
import * as XLSX from 'xlsx';
import {cloneDeep} from "lodash"
import {DateRange} from "@/api/analyse/type";
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {createCustomSorter, getCustomStringLength} from "@/utils/strUtil";
import { TimeParticleSize } from '@/api/enum';

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  secondaryIndicatorOnly: boolean;
}

const props = defineProps<Props>()

const columns = ref<any>([]);
const tableData = ref<any>([]);

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])

// 添加分组详情处理逻辑
const tableGroupData = ref<any>([]);
const tableGroupColumns = ref<any>([]);
const groupDateStr = ref('');

const indicatorData = ref({})
const secondaryIndicatorData = ref({})

// 处理表格颜色深浅
const getBackgroundColor = (value) => {
    if(props.secondaryIndicatorOnly){
      return 'rgb(255, 255, 255)';
    }
    // 如果没有值，返回白色
    if (value === null || value === undefined || value === '') {
        return 'rgb(255, 255, 255)'; // 白色
    }
   // 确保值是数字
   const numValue = Number(value);
    if (Number.isNaN(numValue)) {
        return 'rgb(255, 255, 255)';
    }
    // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
    const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
    return `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
}

// 格式化时间列
const formatTimeColumn = (dateString:string) => {
  const {timeParticleSize}  = props.eventData
  if(timeParticleSize === TimeParticleSize.DAY1){
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if(timeParticleSize === TimeParticleSize.WEEK1){
    return `${dateString}当周`;
  }
  if(timeParticleSize === TimeParticleSize.MONTH1){
    return `${dateString}月`;
  }
  return `${dateString}`;
}


const freshData = () => {
  const { scatterQueryResult, stages,indicator,secondaryIndicator} = props.eventData;
  indicatorData.value = indicator?.eventList?.[0]
  secondaryIndicatorData.value = secondaryIndicator?.eventList?.[0]
  // 生成列配置
  const baseColumns = [
    {
      title: '事件发生时间',
      dataIndex: 'date',
      slotName: 'date',
      minWidth: 240,
      sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
      title: '全部用户',
      dataIndex: 'userNum',
      slotName: 'userNum',
      minWidth: 140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    },
  ];
  // 根据stages生成列
  const stageColumns = [];
  if (stages && stages.length > 0) {
    stages.forEach((stage, index) => {
      stageColumns.push({
        title: stage,
        dataIndex: `stage${index + 1}`,
        rateIndex: `rate${index + 1}`,
        secondaryIndex: `secondaryStage${index + 1}`,
        slotName: `stage${index + 1}`,
        minWidth: 140,
        sortable: { sortDirections: ['ascend', 'descend'] }
      });
    });
  }
  columns.value = [...baseColumns, ...stageColumns];
  // 处理表格数据
  const processedData = [];
  // 如果有scatterQueryResult数据，则处理数据
  if (scatterQueryResult && scatterQueryResult.length > 0) {
    const firstData = scatterQueryResult[0];
    const secondaryData = scatterQueryResult[1];
    // 处理每个日期的数据
    for (let i = 0; i < firstData.length; i++) {
      const firstItem = firstData[i];
      const secondaryItem = secondaryData?.[i];
      if (!firstItem) {
        return;
      }
      const rowData = {
        date: firstItem.eventDate,
        userNum: firstItem.summaryDataSum || 0,
        secondaryUserNum: secondaryItem?.summaryDataSum || 0,
        groupData: firstItem?.groupData || [], // 存储分组数据，用于详情展示
        secondaryGroupData: secondaryItem?.groupData || []// 存储同时展示指标的分组数据
      };
      // 处理每个阶段的数据
      if (firstItem.summaryData && firstItem.summaryData.length > 0) {
        firstItem.summaryData.forEach((value, index) => {
          const stageIndex = index + 1;
          const numValue = value || 0;
          // 计算百分比 = summaryData / summaryDataSum * 100
          const percent = firstItem.summaryDataSum ? (numValue / firstItem.summaryDataSum * 100) : 0;
          rowData[`stage${stageIndex}`] = numValue;
          rowData[`rate${stageIndex}`] = percent;
        });
      }
      // 处理同时展示指标的阶段数据
      if (secondaryItem?.summaryData && secondaryItem.summaryData.length > 0) {
        secondaryItem.summaryData.forEach((value, index) => {
          const stageIndex = index + 1;
          const numValue = value || 0;
          rowData[`secondaryStage${stageIndex}`] = numValue;
        });
      }
      processedData.push(rowData);
    }
  }
  // 如果没有数据，使用默认数据
  if (processedData.length === 0) {
    processedData.push({
      date: '暂无数据',
      userNum: 0,
      secondaryUserNum: 0
    });
  }
  
  tableData.value = processedData;
  copyColumns.value = cloneDeep(columns.value);
  copyTableData.value = cloneDeep(tableData.value);
};
freshData()

// 导出
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `分布分析${name ? `_${name}` : ''}_${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}.xlsx`);
};
const createVisible = ref(false)
const detailVisible = ref(false)
const form = ref({
  name: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  createVisible.value = false;
}
// 保存报表
const saveReport = async () => {
}
// 获取筛选选项
const getFilterOptions = (columnIndex: number) => {
  if (!tableGroupData.value?.length) return [];
  return Array.from(new Set(tableGroupData.value.map(row => row[`group${columnIndex}`])))
    .filter(value => value !== undefined)
    .map(value => ({
      label: value === null ? '(空)' : value,
      value: value === null ? null : value
    }));
};

// 打开分组详情
const openGroupDetail = (record, rowIndex) => {
  const {groupsDesc} = props.eventData;
  // 设置日期字符串
  groupDateStr.value = record.date;
  // 构建表格列配置
  const groupColumns = groupsDesc?.map((item, idx) => ({
    title: item.name,
    dataIndex: `group${idx}`,
    slotName: `group${idx}`,
    width: getCustomStringLength(item.name)+ 80,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: (a, b, extra) => {
        const { direction } = extra;
        const format = item.format;
        const valueA = a[`group${idx}`];
        const valueB = b[`group${idx}`];
        return createCustomSorter(format)(valueA, valueB, direction);
      }
    },
    filterable: {
      filter: (value, row) => !value?.length || value.includes(row[`group${idx}`] ?? null),
      slotName: `group-filter-${idx}`,
      multiple: true
    }
  }));
  const baseColumns = [
    {
      title: '全部用户数',
      dataIndex: 'userNum',
      slotName: 'userNum',
      minWidth: 140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    },
  ];
  // 获取stages相关的列，根据主要指标列筛选
  const stageColumns = columns.value
    .filter(col => col.dataIndex !== 'date' && 
                  col.dataIndex !== 'userNum')
    ?.map(col => ({
      ...col,
      minWidth: 140
    }));
  tableGroupColumns.value = [...groupColumns, ...baseColumns, ...stageColumns];
  // 处理分组数据
  const allGroupData = record.groupData?.map(groupItem => {
    // 基础数据对象
    const groupData = {
      // 添加分组信息
      ...groupItem.group.reduce((acc, value, idx) => ({
        ...acc,
        [`group${idx}`]: value
      }), {}),
      userNum: groupItem.valueSum,
      // 添加每个阶段的数据和百分比
      ...groupItem.values.reduce((acc, value, idx) => {
        const total = groupItem.valueSum; // 使用该分组的用户数作为总数
        return {
          ...acc,
          [`stage${idx+1}`]: value,
          [`rate${idx+1}`]: total ? Number(((value / total) * 100).toFixed(2)) : 0,
        };
      }, {})
    };
    // 查找对应的同时展示数据
    const secondaryGroup = record.secondaryGroupData?.find(sg =>
      JSON.stringify(sg.group) === JSON.stringify(groupItem.group)
    );
    if (secondaryGroup) {
      groupData.secondaryUserNum = secondaryGroup.valueSum;
      // 添加 secondary 阶段数据和百分比
      secondaryGroup.values.forEach((value, idx) => {
        groupData[`secondaryStage${idx+1}`] = value;
      });
    }
    return groupData;
  });
  tableGroupData.value = allGroupData;
  detailVisible.value = true;
};
// 是否有同时展示指标
const hasSecondaryIndicator = computed(() => {
  const { scatterQueryResult } = props.eventData;
  return scatterQueryResult[1] !== undefined && scatterQueryResult[1].length;
});
// 是否有分组项
const hasGroupData = computed(() => {
  const { groupsDesc } = props.eventData;
  return groupsDesc !== undefined && groupsDesc?.length;
})
// 监听视图宽度变化刷新table，否则横向滚动条会有拖动问题
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
const tableKey = ref(0)
watch(splitSize, (newSize, oldSize) => {
  if (newSize) {
    tableKey.value += 1
  }
})
// 千分位
const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
defineExpose({
  freshData,
  exportXlsx
})
</script>

<template>
  <div class="chart-content" style="width: 100%;height: 100%;margin-bottom: 24px;">
    <a-spin style="width: 100%;height: 100%">
      <a-table :key="tableKey" :columns="columns" :data="tableData" :pagination="false" size="small" column-resizable :bordered="{cell:true}" :scroll="{y:'100%',x:'100%'}">
        <!-- 动态处理所有列 -->
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record, rowIndex }">
          <div class="cell-class" :style="index > 1 ? `background: ${getBackgroundColor(record[column.rateIndex])}` : ''">
            <!-- 第一列 - 日期 -->
            <div v-if="index === 0">
              <icon-plus v-if="hasGroupData" class="record-icon" @click="openGroupDetail(record, rowIndex)"/>
              <span>{{formatTimeColumn(record[column.dataIndex])}}</span>
            </div>
            <!-- 从第二列开始 - 数据 -->
            <div v-if="index > 0 && !secondaryIndicatorOnly" class="hover-box">
              <!-- 用户数量 -->
              <a-tooltip
                v-if="index ===1"
                :content="`
                  ${formatTimeColumn(record.date)}
                  参与
                  ${indicatorData.eventDisplayName}
                  的用户总共有
                  ${record[column.dataIndex] || '-'}个
                  ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record.secondaryUserNum:''}`"
                position="top">
                <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
              </a-tooltip>
              <a-tooltip
                v-if="index > 1"
                :content="`
                  ${formatTimeColumn(record.date)}
                  参与
                  ${indicatorData.eventDisplayName}
                  的用户中，有
                  ${record[column.dataIndex] || '-'}个用户的
                  ${indicatorData.eventAttrName}等于${column.title}
                  ,占比${record[column.rateIndex]?.toFixed(2)}%
                  ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record[column.secondaryIndex]:''}`"
                position="top">
                <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
              </a-tooltip>
              <svg v-if="record[column.dataIndex]" class="add-group" @click="() => createVisible = true" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-4 0a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z"></path><path d="M1 10a2 2 0 012-2h3a2 2 0 012 2v3H7v-3a1 1 0 00-1-1H3a1 1 0 00-1 1v3H1v-3z"></path><path d="M13 10h-1V8h-2V7h2V5h1v2h2v1h-2v2z"></path></svg></svg>
            </div>
            <!-- 百分比显示 -->
            <div v-if="index > 1 && !secondaryIndicatorOnly">{{ record[column.rateIndex] !== undefined && record[column.rateIndex] !== null ? `${record[column.rateIndex]?.toFixed(2)}%` : '-' }}</div>
            <!-- 同时展示总数 -->
            <a-tooltip
              v-if="index === 1 && hasSecondaryIndicator && secondaryIndicatorOnly"
              :content="`
                ${formatTimeColumn(record.date)}
                参与
                ${indicatorData.eventDisplayName}
                的用户的
                ${secondaryIndicatorData.eventDisplayName}.
                ${secondaryIndicatorData.eventAttrName}为
                ${record.secondaryUserNum}`"
              position="top">
              <span>{{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}</span>
            </a-tooltip>
            <div v-if="index === 1 && hasSecondaryIndicator && !secondaryIndicatorOnly">{{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}</div>
            <!-- 同时展示数 -->
            <a-tooltip
              v-if="index > 1 && hasSecondaryIndicator && secondaryIndicatorOnly"
              :content="`
                ${formatTimeColumn(record.date)}
                参与
                ${indicatorData.eventDisplayName}
                ，且
                ${indicatorData.eventAttrName}等于${column.title}
                的用户的
                ${secondaryIndicatorData.eventDisplayName}.
                ${secondaryIndicatorData.eventAttrName}为
                ${record[column.secondaryIndex]}`"
              position="top">
              <span>{{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}</span>
            </a-tooltip>
            <div v-if="index > 1 && hasSecondaryIndicator && !secondaryIndicatorOnly">{{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}</div>
          </div>
        </template>
      </a-table>
    </a-spin>
    <!-- 创建弹窗 -->
    <a-modal v-model:visible="createVisible" :on-before-ok="validateForm" width="480px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">
          创建结果分群
          <a-tooltip content="选择计算结果保存为分群，可用于进一步下钻分析。" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <div class="form-label">分群显示名</div>
        <a-form-item field="name" :hide-label="true" :hide-asterisk="true" required :rules="[{ required: true, message: '分群显示名不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <div class="form-label">分群备注(选填)</div>
        <a-form-item field="description" :hide-label="true">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:3, maxRows:10}" :show-word-limit="true" :max-length="200"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="boe-foot">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" @click="saveReport">创建</a-button>
        </div>
      </template>
    </a-modal>
    <!-- 分组详情 -->
    <a-modal v-model:visible="detailVisible" width="80%" :footer="false">
      <template #title>
        <div class="modal-title">{{formatTimeColumn(groupDateStr)}}分组详情</div>
      </template>
      <a-table :columns="tableGroupColumns" :data="tableGroupData" :pagination="tableGroupData?.length && tableGroupData.length>100" size="small" column-resizable :bordered="{cell:true}" :scroll="{y:500}" :filter-icon-align-left="true">
        <!-- 添加筛选器插槽 -->
        <template v-for="(_, idx) in props.eventData?.groupsDesc" :key="idx" #[`group-filter-${idx}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions(idx)"
                :virtual-list-props="{height:200}"
                :style="{ width: '180px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template v-for="(column, index) in tableGroupColumns" :key="index" #[column.slotName]="{ record, rowIndex }">
          <div class="cell-class" :style="index > tableGroupColumns.length - columns.length + 1 ? `background: ${getBackgroundColor(record[column.rateIndex])}` : ''">
            <!-- 分组列 -->
            <div v-if="index < tableGroupColumns.length - columns.length + 1">
              <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
            </div>
            <!-- 全部用户数列 && 非只展示同时数据 -->
            <div v-if="index === tableGroupColumns.length - columns.length + 1 && !secondaryIndicatorOnly">
              <a-tooltip
                :content="`
                ${formatTimeColumn(groupDateStr)}
                参与
                ${indicatorData.eventDisplayName}
                的用户总共有
                ${record.userNum || '-'} 个
                 ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record.secondaryUserNum:''}`"
                position="top">
                <span>{{ record.userNum !== undefined && record.userNum !== null ? formatNumber(record.userNum) : '-' }}</span>
              </a-tooltip>
            </div>
            <!-- 同时-全部用户数列 && 只展示同时数据 -->
            <a-tooltip
              v-if="index === tableGroupColumns.length - columns.length + 1 && hasSecondaryIndicator && secondaryIndicatorOnly"
              :content="`
                ${formatTimeColumn(groupDateStr)}
                参与
                ${indicatorData.eventDisplayName}
                的用户的
                ${secondaryIndicatorData.eventDisplayName}.
                ${secondaryIndicatorData.eventAttrName}为
                ${record.secondaryUserNum}`"
              position="top">
              <span>{{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}</span>
            </a-tooltip>
            <!-- 同时-全部用户数列 && 非只展示同时数据 -->
            <div v-if="index === tableGroupColumns.length - columns.length + 1 && hasSecondaryIndicator && !secondaryIndicatorOnly">
              <span>{{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}</span>
            </div>
            <!-- 数据列 -->
            <div v-if="index > tableGroupColumns.length - columns.length + 1 && !secondaryIndicatorOnly" class="hover-box">
              <a-tooltip
                v-if="index > 1"
                :content="`
                ${formatTimeColumn(groupDateStr)}
                参与
                ${indicatorData.eventDisplayName}
                的用户中，有
                ${record[column.dataIndex] || '-'} 个用户的
                ${indicatorData.eventAttrName}等于${column.title},占比${record[column.rateIndex]?.toFixed(2)}%
                ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record[column.secondaryIndex]:''}`"
                position="top">
                <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
              </a-tooltip>
            </div>
            <!-- 百分比显示 -->
            <div v-if="index > tableGroupColumns.length - columns.length + 1 && !secondaryIndicatorOnly">{{ record[column.rateIndex] !== undefined && record[column.rateIndex] !== null ? `${record[column.rateIndex]?.toFixed(2)}%` : '-' }}</div>
            <!-- 同时展示数 -->
            <a-tooltip
              v-if="index > tableGroupColumns.length - columns.length + 1 && hasSecondaryIndicator && secondaryIndicatorOnly"
              :content="`
                ${formatTimeColumn(groupDateStr)}
                参与
                ${indicatorData.eventDisplayName}
                ，且
                ${indicatorData.eventAttrName}等于${column.title}
                的用户的
                ${secondaryIndicatorData.eventDisplayName}.
                ${secondaryIndicatorData.eventAttrName}为
                ${record[column.secondaryIndex]}`"
              position="top">
              <div>{{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}</div>
            </a-tooltip>
            <div v-if="index > tableGroupColumns.length - columns.length + 1 && hasSecondaryIndicator && !secondaryIndicatorOnly">{{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}</div>
          </div>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
.cell-class {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}
.record-icon{
  margin-right: 5px;
  cursor: pointer;
  &:hover{
    color: var(--tant-primary-color-primary-hover);
  }
}
.hover-box{
  display: flex;
  align-items: center;
  &:hover{
    .add-group{
      opacity: 1;
    }
  }
  .add-group{
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;
    &:hover{
      color: #8d0088;
    }
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
.form-label{
  line-height: 32px;
  font-weight: 500;
}
:deep(tbody .arco-table-cell) {
  padding: 0;
}

.cell-class {
  padding: 5px 16px;
  height: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>