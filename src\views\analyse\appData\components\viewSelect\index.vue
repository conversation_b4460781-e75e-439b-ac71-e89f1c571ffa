<template>
  <a-modal v-model:visible="modalVisible" :width="720" title-align="start" style="z-index: 998" :footer="false" @cancel="handleCancel">
    <template #title> 视图编辑 </template>
    <div>
      <div v-if="!userList.length" style="display: flex; justify-content: flex-end;margin-bottom: 12px;">
        <a-button type="primary" size="small" @click="addView">新增视图</a-button>
      </div>
      <div class="view-content">
        <a-list v-if="sysList.length" :bordered="false" size="small">
          <template #header>
            <span class="header-underline">系统视图</span>
          </template>
          <a-list-item v-for="(item, idx) in sysList" :key="idx">
            <span style="line-height: 32px">{{ item.name }}</span>
            <template #actions>
              <div>
                <a-tooltip content="视图指标编辑">
                  <icon-edit class="action-icon" @click.stop.prevent="editIndicator(item)" />
                </a-tooltip>
                <a-tooltip v-if="!item.isDefault" content="设为默认">
                  <icon-check-circle class="action-icon" @click.stop.prevent="setSysDefaultView(item)" />
                </a-tooltip>
                <icon-check-circle v-else class="star action-icon" />
              </div>
            </template>
          </a-list-item>
        </a-list>
        <a-list v-if="userList.length" :bordered="false" size="small">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span class="header-underline">我的视图</span>
              <span>
                <a-tooltip content="新增视图">
                  <icon-plus-circle style="cursor: pointer; color: rgb(var(--primary-6))" @click="addView" />
                </a-tooltip>
              </span>
            </div>
          </template>
          <a-list-item v-for="(item, idx) in userList" :key="idx">
            {{ item.name }}
            <template #actions>
              <div>
                <a-tooltip content="视图指标编辑">
                  <icon-edit class="action-icon" @click.stop.prevent="editIndicator(item)" />
                </a-tooltip>
                <a-tooltip v-if="!item.isDefault && pageType != 'comprehensive'" content="设为默认">
                  <icon-check-circle class="action-icon" @click.stop.prevent="setUserDefaultView(item)" />
                </a-tooltip>
                <icon-check-circle v-if="item.isDefault && pageType != 'comprehensive'" class="action-icon star" />
                <a-tooltip content="分享">
                  <icon-share-internal class="action-icon" @click.stop.prevent="shareView(item)" />
                </a-tooltip>
                <a-tooltip content="设置">
                  <icon-settings class="action-icon" @click.stop.prevent="setView(item)" />
                </a-tooltip>
                <a-tooltip content="删除">
                  <icon-delete class="action-icon" @click.stop.prevent="deleteView(item)" />
                </a-tooltip>
              </div>
            </template>
          </a-list-item>
        </a-list>
        <a-list v-if="shareList.length" :bordered="false" size="small">
          <template #header>
            <span class="header-underline">分享给我的视图</span>
          </template>
          <a-list-item v-for="(item, idx) in shareList" :key="idx">
            {{ item.name }}<span style="color: #ccc; margin-left: 8px">(创建人：{{ item.creator?.name || '无名氏' }})</span>
            <template #actions>
              <div>
                <a-tooltip v-if="item.permissionType === 3" content="视图指标编辑">
                  <icon-edit class="action-icon" @click.stop.prevent="editIndicator(item)" />
                </a-tooltip>
                <a-tooltip v-if="!item.isDefault && pageType != 'comprehensive'" content="设为默认">
                  <icon-check-circle class="action-icon" @click.stop.prevent="setShareDefaultView(item)" />
                </a-tooltip>
                <icon-check-circle v-if="item.isDefault && pageType != 'comprehensive'" class="action-icon star" />
                <a-tooltip v-if="item.permissionType === 3" content="分享">
                  <icon-share-internal class="action-icon" @click.stop.prevent="shareView(item)" />
                </a-tooltip>
                <a-tooltip v-if="item.permissionType === 3" content="设置">
                  <icon-settings class="action-icon" @click.stop.prevent="setView(item)" />
                </a-tooltip>
                <a-tooltip v-if="item.permissionType === 3" content="删除">
                  <icon-delete class="action-icon" @click.stop.prevent="deleteShareView(item)" />
                </a-tooltip>
              </div>
            </template>
          </a-list-item>
        </a-list>
      </div>
    </div>
    <!-- 分享 -->
    <ShareView ref="shareRef" />
    <!-- 设置 -->
    <ViewSet ref="viewSetRef" @permission-change="handlePermissionChange" />
    <!-- 指标选择 -->
    <IndicatorSelect ref="indicatorSelectRef" :show-type="showType || false" :indicator-list="props.indicatorList" :filter-params="props.filterParams" :page-type="props.pageType" @update-view="refreshView" @update-indicator="() => emits('updateIndicator')"/>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import '@/views/analyse/css/analyse.less';
  import { Message } from '@arco-design/web-vue';
  import { deleteAttributionView, setDefaultAttributionView } from '@/api/analyse/api';
  import ShareView from './ShareView.vue';
  import ViewSet from './ViewSet.vue';
  import IndicatorSelect from './IndicatorSelect.vue';

  interface Props {
    indicatorList: any; // 指标列表
    appViewData: any; // 应用视图数据
    filterParams: any; // 过滤参数
    pageType: string; // 页面类型
    showType?: boolean;
  }
  const props = defineProps<Props>();
  const emits = defineEmits(['updateView', 'updateIndicator']);
  const modalVisible = ref(false);
  const viewCode = ref('');
  const sysList = ref<any>([]);
  const userList = ref<any>([]);
  const shareList = ref<any>([]);

  const handleCancel = () => {
    modalVisible.value = false;
  };

  // 视图指标编辑
  const indicatorSelectRef = ref();
  const editIndicator = (item) => {
    indicatorSelectRef.value.openModal(item);
  };
  const addView = () => {
    indicatorSelectRef.value.openModal();
  };
  // 设置系统视图默认
  const setSysDefaultView = async (item) => {
    try {
      await setDefaultAttributionView({ viewCode: item.code, subjectCode: props.filterParams?.subjectCode });
      Message.success('设置成功');
      emits('updateView');
    } catch (error) {
      console.error(error);
      Message.error('设置失败');
    }
  };
  // 设置我的视图默认
  const setUserDefaultView = async (item) => {
    try {
      await setDefaultAttributionView({ viewCode: item.code, subjectCode: props.filterParams?.subjectCode });
      Message.success('设置成功');
      emits('updateView');
    } catch (error) {
      console.error(error);
      Message.error('设置失败');
    }
  };
  // 设置分享给我的视图默认
  const setShareDefaultView = async (item) => {
    try {
      await setDefaultAttributionView({ viewCode: item.code, subjectCode: props.filterParams?.subjectCode });
      Message.success('设置成功');
      emits('updateView');
    } catch (error) {
      console.error(error);
      Message.error('设置失败');
    }
  };
  // 分享
  const shareRef = ref();
  const shareView = async (item) => {
    shareRef.value.openModal(item.code);
  };
  // 设置
  const viewSetRef = ref();
  const setView = async (item) => {
    viewSetRef.value.openModal(item);
  };
  // 权限修改
  const handlePermissionChange = (obj) => {
    emits('updateView');
  };

  // 删除
  const deleteView = async (item) => {
    try {
      await deleteAttributionView(item.code);
      Message.success('删除成功');
      emits('updateView');
    } catch (error) {
      console.error(error);
      Message.error('删除失败');
    }
  };
  // 删除分享给我的视图
  const deleteShareView = async (item) => {
    try {
      await deleteAttributionView(item.code);
      Message.success('删除成功');
      emits('updateView');
    } catch (error) {
      console.error(error);
      Message.error('删除失败');
    }
  };
  const refreshView = async (v) => {
    emits('updateView', v);
  };
  const openModal = async (code: string) => {
    sysList.value = props.appViewData?.sys || [];
    userList.value = props.appViewData?.user || [];
    shareList.value = props.appViewData?.share || [];
    viewCode.value = code || '';
    modalVisible.value = true;
  };
  watch(
    () => props.appViewData,
    (newVal) => {
      sysList.value = newVal?.sys || [];
      userList.value = newVal?.user || [];
      shareList.value = newVal?.share || [];
    },
    { deep: true }
  );
  defineExpose({
    openModal,
  });
</script>

<style scoped lang="less">
  .action-icon {
    display: inline-block;
    cursor: pointer;
    margin-left: 20px;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
    }
    .right {
      display: flex;
      justify-content: flex-end;
      button {
        border-radius: 4px;
      }
    }
  }
  .cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
      background-color: var(--color-secondary);
    }
  }
  .view-content {
    width: 100%;
    border: 1px solid var(--color-secondary);
    height: 600px;
    overflow-y: auto;
    :deep(.arco-list-item) {
      cursor: pointer;
      &:hover {
        background-color: var(--color-secondary);
      }
    }
    .star {
      color: var(--tant-decorative-yellow-color-decorative6-3) !important;
    }
  }
  .index-content {
    border: 1px solid var(--color-secondary);
    width: 100%;
    height: 100%;
    display: flex;
    .left {
      width: 50%;
      padding: 12px;
      height: 600px;
      border-right: 1px solid var(--color-secondary);
      overflow-y: scroll;
      .left-header {
        display: flex;
        button {
          border-radius: 4px;
        }
      }
      .search-content {
        width: 100%;
        padding: 8px;
        margin-top: 12px;
        background-color: var(--color-fill-1);
        .type-title {
          color: var(--color-text-1);
          font-size: 14px;
          line-height: 24px;
          padding-top: 8px;
          padding-bottom: 8px;
          overflow: hidden;
        }
      }
      .list-box {
        margin: 0 8px 8px;
        padding: 0 12px;
        border-radius: 4px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        line-height: 28px;
        height: 30px;
        background: #fff;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .select-icon {
          opacity: 0;
        }
        &:hover .select-icon {
          opacity: 1;
        }
      }
      .has-select {
        cursor: no-drop;
        background: var(--color-secondary);
        &:hover .select-icon {
          opacity: 0;
        }
      }
    }
    .right {
      flex: 1;
      max-height: 600px;
      overflow: hidden;
      width: 100%;
      padding: 12px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        a {
          color: #0b75ff;
          cursor: pointer;
        }
      }
    }
  }
  .drag-handle {
    cursor: grab;
    margin-right: 8px;
  }
  .header-underline {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
</style>
