import {AxiosPromise} from "axios";
import {getRequest, postRequest} from "@/api/request";

// 应用列表接口
export function getOpAppList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/list', {});
}
// 应用类别接口
export function getOpCategoryList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/category/list', {});
}
// 添加应用类别接口
export function addOpCategoryList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/category/add', {...data});
}
// 应用类别删除
export function deleteCategory(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/category/remove?code=${code}`, {});
}
// 应用类别详情
export function getCategoryDetail(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/category/detail?code=${code}`, {});
}
// 应用标签接口
export function getOpTagList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/tag/list', {});
}
// 添加应用标签接口
export function addOpTagList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/tag/add', {...data});
}
// 应用标签删除
export function deleteTag(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/tag/remove?code=${code}`, {});
}
// 开发商接口
export function getOpDeveloperList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/developer/list', {});
}
// 添加开发商接口
export function addOpDeveloperList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/developer/add', {...data});
}
// 开发商删除
export function deleteDeveloper(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/developer/remove?code=${code}`, {});
}
// 开发商详情
export function getDeveloperDetail(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/developer/detail?code=${code}`, {});
}
// 获取项目分组列表
export function getOpGroupList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/group/list', {});
}
// 添加项目分组接口
export function addOpGroupList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/group/add', {...data});
}
// 项目分组删除
export function deleteGroup(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/group/remove?code=${code}`, {});
}
// 项目分组详情
export function getGroupDetail(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/group/detail?code=${code}`, {});
}
// 项目团队接口
export function getOpTeamList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/team/list', {});
}
// 项目团队详情
export function getTeamDetail(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/team/detail?code=${code}`, {});
}
// 添加项目团队接口
export function addOpTeamList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/team/add', {...data});
}
// 项目团队删除
export function deleteTeam(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/team/remove?code=${code}`, {});
}
// 国家组接口
export function getCountruGroupList(): AxiosPromise<any> {
    return getRequest<any>('/api/sys/country_group/list', {});
}
// 添加/更新项目团队接口
export function saveCountryGroup(data): AxiosPromise<any> {
    return postRequest<any>('/api/sys/country_group/save', {...data});
}
// 国家组删除
export function deleteCountryGroup(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/sys/country_group/remove?code=${code}`, {});
}
// 获取商店平台列表
export function getStoreList(name:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/platform/list?os_name=${name}`, {});
}
// 添加商店平台列表
export function addStoreList(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/platform/add', {...data});
}
// 平台渠道接口
export function getOpMediaSourceList(type): AxiosPromise<any> {
    return getRequest<any>(`/api/op/media_source/list?media_source_type=${type}`, {});
}
// 广告类型接口
export function getAdFormatList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/ad_format/list', {});
}
// 事件指标列表
export function getRepCategoryList(): AxiosPromise<any> {
    return getRequest<any>('/api/sys/rep/category/list', {});
}
// 添加事件指标接口
export function addRepCategoryList(data): AxiosPromise<any> {
    return postRequest<any>('/api/sys/rep/category/save', {...data});
}
// 删除事件指标接口
export function deleteRepCategory(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/sys/rep/category/remove?code=${code}`, {});
}
// 元事件前缀匹配
export function saveEventPrefixMatch(prefix:string): AxiosPromise<any> {
    return getRequest<any>(`/api/data/event/prefix/match?event_prefix=${prefix}`, {});
}
// 指标分组详情
export function getRepCategoryGroupDetail(code:string): AxiosPromise<any> {
    return getRequest<any>(`/api/sys/rep/category/detail?code=${code}`, {});
}
// 指标分组批量更新
export function updateRepCategoryGroups(data): AxiosPromise<any> {
    return postRequest<any>('/api/sys/rep/category/multi/update', {...data});
}
// 获取分组指标列表
export function getRepIndicatorList(data): AxiosPromise<any> {
    const params = new URLSearchParams();
    if (data.categoryCode) {
        params.append('category_code', data.categoryCode);
    }
    if (data.inApp !== undefined && data.inApp !== null) {
        params.append('in_app', data.inApp);
    }
    const queryString = params.toString();
    const url = `/api/sys/rep/category/indicator/list${queryString ? `?${queryString}` : ''}`;
    return getRequest<any>(url, {});
}
// 获取分组事件列表
export function getRepEventList(data): AxiosPromise<any> {
    const params = new URLSearchParams();
    if (data.categoryCode) {
        params.append('category_code', data.categoryCode);
    }
    if (data.inApp !== undefined && data.inApp !== null) {
        params.append('in_app', data.inApp);
    }
    const queryString = params.toString();
    const url = `/api/sys/rep/category/event/list${queryString ? `?${queryString}` : ''}`;
    return getRequest<any>(url, {});
}

// 系统类型接口
export function getOpOsList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/os/list', {});
}
// 广告单元查询接口
export function getAdList(data): AxiosPromise<any> {
    return getRequest<any>('/api/op/ad_item/list', {...data});
}
// 广告单元删除
export function deleteAdItem(id:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/ad_item/del?ad_item_id=${id}`, {});
}
// 添加广告单元
export function addAdItem(appId,data): AxiosPromise<any> {
    return postRequest<any>(`/api/op/ad_item/add?app_id=${appId}`, {...data});
}
// 更新广告单元
export function updateAdItem(id,data): AxiosPromise<any> {
    return postRequest<any>(`/api/op/ad_item/update?ad_item_id=${id}`, {...data});
}
// 添加应用
export function postAddApp(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/add', {...data});
}
// 删除应用
export function getDeleteApp(appId:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/del', {appId});
}
// 更新应用
export function postUpdateApp(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/update', {...data});
}
// 添加应用推广信息
export function postPromotionUpdateApp(id,data): AxiosPromise<any> {
    return postRequest<any>(`/api/op/app/promotion/update?app_id=${id}`, {...data});
}
// 获取应用推广信息
export function getPromotionInfo(id): AxiosPromise<any> {
    return getRequest<any>(`/api/op/app/promotion/info?app_id=${id}`, {});
}
// 获得应用更新日志
export function getLogList(config:any): AxiosPromise<any> {
    return getRequest<any>('/api/op/log/list', {...config});
}
// 添加应用更新日志
export function postAddLog(config:any): AxiosPromise<any> {
    return postRequest<any>('/api/op/log/add', {...config});
}
// 删除更新日志
export function getDelLog(logId:any): AxiosPromise<any> {
    return getRequest<any>('/api/op/log/del', {logId});
}
// 获得更新日志详情
export function getInfoLog(): AxiosPromise<any> {
    return getRequest<any>('/api/op/log/info', {});
}
// 更新应用更新日志
export function postUpdateLog(logId:number,content:string ): AxiosPromise<any> {
    return postRequest<any>('/api/op/log/update?log_id='+logId, {content});
}
// 获取交叉推广分类列表
export function getCrossCategoryList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/category/list', {});
}
// 获取交叉推广分组列表
export function getCrossGroupList(category:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/list', {category});
}
// 获取交叉推广分组详情
export function getCrossDetail(category:string,name:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/info', {category,name});
}
// 添加交叉推广分组
export function addCrossGroup(category:string,name:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/add', {category,name});
}
// 删除交叉推广分组
export function deleteCrossGroup(category:string,name:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/delete', {category,name});
}
// 添加交叉推广分组成员
export function addCrossGroupMember(params:any): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/member/add', {...params});
}
// 删除交叉推广分组成员
export function deleteCrossGroupMember(params:any): AxiosPromise<any> {
    return getRequest<any>('/api/op/running/cross_promotion/group/member/delete', {...params});
}
// 添加交叉推广内容
export function saveCrossPromotion(config:any): AxiosPromise<any> {
    return postRequest<any>('/api/op/running/cross_promotion/group/regular/add', {...config});
}
// 应用搜索
export function appSearch(appId:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/search', {appId});
}
// 获得应用基本信息
export function getAppInfo(appId:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/info', {appId});
}
// 应用初始化
export function getAppInit(appId:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/init', {appId});
}
// 留存模型查询接口
export function getRetentionModel(): AxiosPromise<any> {
    return getRequest<any>('/api/op/retention_model/list', {});
}
// 添加留存模型查询接口
export function addRetentionModel(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/retention_model/add', {...data});
}
// 更新留存模型查询接口
export function updateRetentionModel(data): AxiosPromise<any> {
    return postRequest<any>('/api/op/retention_model/update', {...data});
}
// 应用列表分页查询接口
export function getAppPageList(data:any): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/page/list', {...data});
}
// 归档应用
export function setAppArchive(appId:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/app/archive?app_id=${appId}`, {});
}
// 获取应用下发数据可用平台列表
export function getAppPlatformList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/platform/list', {});
}
// 添加应用下发数据可用平台
export function platformAdd(name:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/platform/add', {name});
}
// 获取应用下发数据可用内存列表
export function getAppMemList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/mem/list', {});
}
// 添加应用下发数据可用内存
export function memAdd(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/mem/add', {...params});
}
// 更新应用下发数据可用内存
export function memUpdate(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/mem/update', {...params});
}
// 获取应用下发数据可用买量来源渠道列表
export function getAppSourceList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/source/list', {});
}
// 添加应用下发数据可用买量来源渠道列表
export function sourceAdd(name:string): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/source/add', {name});
}
// 获取应用下发数据可用数据版本列表
export function getAppVersionList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/data_version/list', {...params});
}
// 添加应用下发数据可用数据版本列表
export function versionAdd(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/data_version/save', {...params});
}
// 删除数据版本
export function deleteDataVersion(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/data_version/remove', {...params});
}
// 获取广告来源列表
export function getAdSourceList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/ad_source/list', {});
}
// 添加广告来源列表
export function adSourceAdd(name:string): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/ad_source/save', {name});
}
// 获取条件列表
export function getConditionList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/condition/list', {...params});
}
// 新增/更新 条件
export function saveCondition(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/condition/save', {...params});
}
// 删除列表
export function deleteConditionItem(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/condition/remove', {...params});
}
// 获取条件详情
export function getConditionDetail(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/distribution/remote_config/condition/info`, {...params});
}
// 获取应用下发数据可用国家地区列表
export function getAppAreaList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/app/distribution/area/list', {});
}
// 添加应用下发数据可用国家地区列表
export function areaAdd(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/distribution/area/add', {...params});
}
// 更新应用下发数据可用国家地区列表
export function areaUpdate(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/distribution/area/update', {...params});
}
// 获取国家列表
export function getCountryList(): AxiosPromise<any> {
    return getRequest<any>('/api/sys/country/list', {});
}
// 国家模糊查询接口
export function countrySearch(params): AxiosPromise<any> {
    return postRequest<any>('/api/sys/country/search', {...params});
}
// 添加条件值（特殊）
export function addSpecialCondition(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/distribution/special_condition/add', {...params});
}
// 获取条件值（特殊）
export function getSpecialConditionInfo(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/app/distribution/special_condition/info', {...params});
}
// 获取默认条件（特殊）
export function getConditionDefault(appId:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/app/distribution/special_condition/default?app_id=${appId}`, {});
}
// 应用支付验证配置详情
export function getPurchaseDetail(appId:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/purchase/app/detail?app_id=${appId}`, {});
}
// 应用支付验证配置保存
export function savePurchaseInfo(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/server/purchase/app/save', {...params});
}
// 应用支付订单统计
export function getPurchaseStatistic(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/server/purchase/app/order/statistics', {...params});
}
// 应用支付订单列表
export function getPurchaseOrders(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/server/purchase/app/orders', {...params});
}
// 应用登录验证配置详情
export function getLoginAppDetail(appId:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/login/app/detail?app_id=${appId}`, {});
}
// 应用登录验证统计
export function getLoginAppStatistic(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/server/login/app/statistics', {...params});
}
// 应用登录平台列表
export function getLoginAppPlatformList(): AxiosPromise<any> {
    return getRequest<any>('/api/op/server/login/platform', {});
}
// 应用支付验证配置保存
export function saveLoginAppInfo(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/server/login/app/save', {...params});
}
// 授权账户列表
export function getAuthorizeList(): AxiosPromise<any> {
    return getRequest<any>('/api/sys/auth/purchase/apple/list', {});
}
// 应用支付订单验证
export function verifyOrder(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/server/purchase/app/order/verify', {...params});
}
// 获取参数列表 | 详情
export function getConfigParamList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/param/list', {...params});
}
// 新增 ｜ 更新 参数
export function saveRemoteConfig(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/param/save', {...params});
}
// 删除参数
export function removeParam(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/param/remove', {...params});
}
// 获取参数列表 | 详情
export function getParamInfo(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/param/list', {...params});
}
// 获取创建条件可用属性列表
export function getRemoteConfigAttrList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/attribute/list', {...params});
}
// 获取创建条件可用属性值列表
export function getRemoteConfigAttrValue(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/attribute/value/list', {...params});
}
// 排序 条件
export function sortCondition(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/condition/sort', {...params});
}
// 获取配置可用属性表
export function getAttributionList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/attribution_config/attribution/list', {...params});
}
// 添加/更新 配置可用属性表
export function saveAttribution(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/attribution_config/attribution/save', {...params});
}
// 获取应用打包配置
export function getPackConfig(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/packaging/pack_config/config/get', {...params});
}
// 添加/更新 应用打包配置
export function savePackConfig(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/packaging/pack_config/config/save', {...params});
}
// 添加 应用打包记录
export function savePackHistory(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/packaging/pack_history/history/save', {...params});
}
// 获取应用打包记录
export function getPackHistoryList(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/packaging/pack_history/history/list', {...params});
}
// 获取应用打包记录详情
export function getPackHistory(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/packaging/pack_history/history/info', {...params});
}
// 获取应用打包版本与数据版本
export function getAppVersionDataVersion(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/packaging/pack_history/history/app_version_data_version', {...params});
}
// 获取下发可用条件列表
export function getConditionAvailable(params): AxiosPromise<any> {
    return getRequest<any>('/api/op/distribution/remote_config/condition/available', {...params});
}
// 获取下发可用条件列表
export function saveConditionRelated(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/distribution/remote_config/condition/related', {...params});
}
// 获取热更新应用存储桶列表
export function getFileResourceAppBucketList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/app/bucket/list`, {...params});
}
// 获取热更新应用版本列表
export function getFileResourceAppVersionList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/app/version/list`, {...params});
}
// 获取热更新应用内子类名称列表
export function getFileResourceAppClassnamesList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/app/classname/list`, {...params});
}
// 获取热更新应用资源列表
export function getFileResourceAppResourceVersionList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/app/resource_version/list`, {...params});
}
// 获取热更新列表
export function getFileResourceList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/list`, {...params});
}
// 获取应用资源版本子列表
export function getFileResourceSubList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/sub/list`, {...params});
}
// 设置资源版本为默认热更新资源
export function setFileResourceDefault(params): AxiosPromise<any> {
    return postRequest<any>(`/api/op/server/file_resource/resource/default`, {...params});
}
// 刷新应用资源
export function refreshFileResource(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/refresh`, {...params});
}
// 删除应用资源
export function deleteFileResource(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/delete`, {...params});
}
// 获取热更新资源文件列表
export function getFileResourceFileList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/file_resource/resource/app/file/list`, {...params});
}
// 分位数下发配置保存
export function saveQuantileConfig(params): AxiosPromise<any> {
    return postRequest<any>('/api/op/server/quantile/config/save', {...params});
}
// 分位数下发配置详情
export function getQuantileConfig(appId:string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/quantile/config/detail?app_id=${appId}`, {});
}
//获取IOS打包-apple账户列表
export function getAppleAccounts(): AxiosPromise<any> {
    return getRequest<any>(`/api/op/packaging/pack_config/apple_accounts/get`, {});
}
//获取IOS打包-unity版本列表
export function getUnityVersions(): AxiosPromise<any> {
    return getRequest<any>(`/api/op/packaging/pack_config/unity_versions/get`, {});
}
//获取IOS打包-sdk_type列表
export function getSdkTypes(sdkUrl: string): AxiosPromise<any> {
    return getRequest<any>(`/api/op/packaging/pack_config/sdk_type/get?sdk_url=${sdkUrl}`, {});
}
//分位数下发列表
export function getQuantileList(params): AxiosPromise<any> {
    return getRequest<any>(`/api/op/server/quantile/list`, {...params});
}