<template>
  <div class="page-container">
    <a-page-header
        class="header"
        title="用户列表"
        :show-back="false"
    >
      <template #extra>
        <a-space>
          <a-button @click="back">返回</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="content">
      <div v-if="filterVisible && !filterLoading" class="query-filters">
        <div v-if="scene===USER_LIST_SCENE.USER_GROUP" class="filter-line">
          <div class="filter-item">
            <div class="filter-label">
              分群编码
            </div>
            <div class="filter-content">
              <a-tooltip :content="groupCode ">
                <span> {{ groupCode }}  </span>
              </a-tooltip>
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-label">
              分群名称
            </div>
            <div class="filter-content">
              <a-tooltip :content="uerGroupDetail?.displayName ">
              <span>
              {{ uerGroupDetail?.displayName }}
              </span>
              </a-tooltip>
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-label">
              数据来源
            </div>
            <div class="filter-content">
              <a-tooltip :content="uerGroupDetail.dataSource == 'event' ? 'IVY' : 'AF'  ">
              <span>
                {{ uerGroupDetail.dataSource == 'event' ? 'IVY' : 'AF' }}
              </span>
              </a-tooltip>
            </div>
          </div>
        </div>
        <div v-if="scene===USER_LIST_SCENE.USER_GROUP" class="filter-line">
          <div class="filter-item w-100">
            <div class="filter-label">
              分群条件
            </div>
            <div class="filter-content h-200">
              <cluster-index :user-filter="uerGroupDetail.condition" disabled/>
            </div>
          </div>
        </div>
        <div v-if="scene===USER_LIST_SCENE.USER_TAG" class="filter-line">
          <div class="filter-item">
            <div class="filter-label">
              标签编码
            </div>
            <div class="filter-content">
              <a-tooltip :content="tagCode ">
                <span> {{ tagCode }}  </span>
              </a-tooltip>
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-label">
              标签名称
            </div>
            <div class="filter-content">
              <a-tooltip :content="userTagDetail?.displayName ">
              <span>
              {{ userTagDetail?.displayName }}
              </span>
              </a-tooltip>
            </div>
          </div>
          <div class="filter-item">
            <div class="filter-label">
              数据来源
            </div>
            <div class="filter-content">
              <a-tooltip :content="userTagDetail.dataSource == 'event' ? 'IVY' : 'AF'  ">
              <span>
                {{ userTagDetail.dataSource == 'event' ? 'IVY' : 'AF' }}
              </span>
              </a-tooltip>
            </div>
          </div>
        </div>
        <div v-if="scene===USER_LIST_SCENE.USER_TAG && userTagDetail?.type===4" class="filter-line">
          <div class="filter-item w-100">
            <div class="filter-label">
              标签指标
            </div>
            <div class="filter-content h-76">
              <custom-indicator-display style="margin-top: 4px" :indicator="userTagDetail.indicatorStage?.indicator"/>
            </div>
          </div>
        </div>
      </div>
      <div v-if="filterVisible && filterLoading" class="query-filters-loading">
        <a-spin dot/>
      </div>
      <a-divider v-if="filterVisible" :margin="12"/>
      <div class="table-title" :style="filterVisible?{}:{marginTop:0}">
        <div class="display-attributes">
          <div class="label">
            展示字段
          </div>
          <a-select
              v-model="userAttributeSelectedList"
              multiple
              :max-tag-count="2"
              style="width: 400px;height: 32px"
              @remove="selectUserAttributes"
              @popup-visible-change="visible => {if (!visible) selectUserAttributes()}">
            <a-option v-for="(item,index) in userAttributeList" key="code" :value="item.code" :label="item?.displayName" :disabled="item.name==='user_id'"/>
          </a-select>
        </div>
        <div class="operation">
          <a-tooltip :content="filterVisible?'隐藏用户来源详情':'显示用户来源详情'">
            <a-button v-if="filterVisible" class="operation-btn" @click="filterVisible=false">
              <template #icon>
                <icon-eye/>
              </template>
            </a-button>
            <a-button v-else class="operation-btn" @click="showQueryFilter">
              <template #icon>
                <icon-eye-invisible/>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="刷新">
            <a-button class="operation-btn" @click="refreshTable">
              <template #icon>
                <icon-sync :spin="userListLoading"/>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="下载文件，最多支持导出3000行">
            <a-button class="operation-btn" @click="Message.info('程序猿正在加班加点开发中... ...')">
              <template #icon>
                <icon-download/>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip content="导出为新分群">
            <a-button class="operation-btn" @click="Message.info('程序猿正在加班加点开发中... ...')">
              <template #icon>
                <icon-share-external/>
              </template>
            </a-button>
          </a-tooltip>
        </div>
      </div>
      <div :style="{height:userInfoTableHeight}">
        <a-table
            :columns="userInfoColumns"
            :data="userInfoList"
            :pagination="pagination"
            :loading="userListLoading"
            size="small"
            column-resizable
            style="height: 100%"
            :scroll="{x:'100%', y:'100%'}"
            :scrollbar="true"
            :hoverable="true"
            sticky-header
            :table-layout-fixed="true"
            :bordered="{cell:true}"
            @page-change="page => {
              pagination.current = page;
              refreshTable();
            }"
            @page-size-change="pageSize => {
              pagination.pageSize = pageSize;
              refreshTable();
            }"
        >
          <template #user_id="{record}">
            <a-button
                v-if="record?.[userIdAttrCode] !== null && record?.[userIdAttrCode] !== undefined"
                type="text"
                @click="goToUserDetail(record)">
              {{ record?.[userIdAttrCode] }}
            </a-button>
            <div v-else>
              --
            </div>
          </template>
          <template #pagination-left>
            <div class="pagination-left">
              共 {{ pagination.total }} 人
            </div>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useRoute, useRouter} from 'vue-router'
import {computed, onMounted, ref} from "vue";
import {getUserAttrList, getUserGroupDetail, getUserInfoList, getUserTagDetail} from "@/api/setting/api";
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue";
import {Message} from "@arco-design/web-vue";
import {useSessionStorage} from "@vueuse/core";
import {USER_LIST_SCENE} from "@/views/data/user/info/constants";
import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import {ROUTE_NAME} from "@/router/constants";
import {convertKeysToSnakeCase} from "@/utils/strUtil";

const router = useRouter()
const route = useRoute()

/**
 * 过滤加载状态
 */
const filterLoading = ref(true)

/**
 * 过滤显示状态
 */
const filterVisible = ref(false)

/**
 * 用户信息加载中
 */
const userListLoading = ref(true)

/**
 * 用户信息
 */
const userInfoList = ref([])

/**
 * 分页参数
 */
const pagination = ref({
  showPageSize: true,
  pageSize: 20,
  current: 1
})

/**
 * 用户属性
 */
const userAttributeList = ref([])

/**
 * 选择的用户属性
 */
const userAttributeSelectedList = useSessionStorage("user-info-user-attribute-selected-list", [])

/**
 * 默认展示用户属性
 */
const defaultUserAttributeSelectedList = ['user_id', 'network_type', 'app_version', 'installed_date', 'device_model', 'appsflyer_id', 'media_source', 'campaign_id', 'bx', 'country', 'carrier', 'ad_id', 'campaign_name', 'ad_type', 'media_channel', 'adset_name', 'adset_id', 'ad_name', 'avl_ram']

/**
 * 用户信息列
 */
const userInfoColumns = ref([])

/**
 * 用户id属性编码
 */
const userIdAttrCode = ref('')

/**
 * 列表场景
 */
const scene = ref<string>(route.query.scene);

/**
 * 分群编码
 */
const groupCode = ref<string>(route.query.groupCode);

/**
 * 标签编码
 */
const tagCode = ref<string>(route.query.tagCode);

/**
 * 标签值
 */
const tagValue = ref<string>(route.query.tagValue);

/**
 * 分群/标签 执行版本
 */
const execLogCode = ref<string>(route.query.execLogCode);

/**
 * 用户分组详情
 */
const uerGroupDetail = ref<any>();

/**
 * 用户标签详情
 */
const userTagDetail = ref<any>();

/**
 * 用户信息列表高度
 */
const userInfoTableHeight = computed(() => {
  if (!filterVisible.value) {
    return 'calc(100% - 52px)'
  }
  switch (scene.value) {
    case USER_LIST_SCENE.USER_GROUP:
      return 'calc(100% - 310px)'
    case USER_LIST_SCENE.USER_TAG:
      if (userTagDetail.value.type === 4) {
        return 'calc(100% - 186px)'
      }
      return 'calc(100% - 114px)'
    default:
  }
  return 'calc(100% - 310px)'
})

function back() {
  router.back()
}

/**
 * 跳转用户详情
 */
const goToUserDetail = (record: any) => {
  router.push({
    name: ROUTE_NAME.USER_DETAIL,
    query: {
      userCode: record?.[userIdAttrCode.value]
    },
  });
}

/**
 * 显示过滤内容
 */
const showQueryFilter = () => {
  filterLoading.value = true
  switch (scene.value) {
    case USER_LIST_SCENE.USER_GROUP:
      getUserGroupDetail(groupCode.value).then((res) => {
        uerGroupDetail.value = res
        filterLoading.value = false
      })
      break
    case USER_LIST_SCENE.USER_TAG:
      getUserTagDetail(tagCode.value).then((res) => {
        userTagDetail.value = res
        filterLoading.value = false
      })
      break
    default:
      Message.warning('用户查询错误！')
  }
  filterVisible.value = true
}

/**
 * 刷新列表
 */
const refreshTable = () => {
  userListLoading.value = true
  let params = {
    order_by: userIdAttrCode.value,
    attributes: userAttributeSelectedList.value,
    pageSize: pagination.value.pageSize,
    current: pagination.value.current,
  }
  switch (scene.value) {
    case USER_LIST_SCENE.USER_GROUP:
      params = {
        ...params,
        scene: 1,
        filter: {
          groupCode: groupCode.value,
          execLogCode: execLogCode.value
        }
      }
      break
    case USER_LIST_SCENE.USER_TAG:
      params = {
        ...params,
        scene: 2,
        filter: {
          tagCode: tagCode.value,
          tagValue: tagValue.value,
          execLogCode: execLogCode.value
        }
      }
      break
    default:
      Message.warning('用户查询错误！')
  }
  getUserInfoList(params).then((res) => {
    userInfoList.value = convertKeysToSnakeCase(res.items)
    pagination.value = {
      ...pagination.value,
      total: res?.total,
    }
    userListLoading.value = false
  })
}

/**
 * 选择用户属性
 */
const selectUserAttributes = () => {
  userInfoColumns.value = userAttributeList.value?.filter(item => {
    return userAttributeSelectedList.value.indexOf(item.code) > -1;
  })?.map(item => {
    // 字段特殊处理
    if (item.name === 'user_id') {
      return {
        title: item?.displayName,
        dataIndex: item.code,
        slotName: item.name,
        fixed: 'left',
        width: 180,
        ellipsis: true,
        tooltip: true,
      }
    }

    return {
      title: item?.displayName,
      dataIndex: item.code,
      render: (value) => {
        const {record} = value;
        if (record[item.code] === undefined || record[item.code] === null) {
          return '--'
        }
        return record[item.code]
      },
      width: 180,
      ellipsis: true,
      tooltip: true,
    }
  })
  refreshTable()
}

onMounted(() => {
  // 初始化用户属性
  getUserAttrList({
    inApp: 1
  }).then((res) => {
    if (res?.length > 0) {
      const userIdAttr = res?.filter((item) => item.name === 'user_id');
      if (userIdAttr?.length > 0) {
        userIdAttrCode.value = userIdAttr[0]?.code
        userAttributeList.value = [...userIdAttr, ...res.filter((item) => item.name !== 'user_id')];
        const initUserAttributeSelectedList = userAttributeList.value?.filter(item => {
          return userAttributeSelectedList.value.indexOf(item.code) > -1;
        })?.map(item => item?.code)
        userAttributeSelectedList.value = initUserAttributeSelectedList?.length > 1 ? initUserAttributeSelectedList : userAttributeList.value?.filter(item => {
          return defaultUserAttributeSelectedList.indexOf(item.name) > -1;
        })?.map(item => item?.code);
        selectUserAttributes()
      }
    }
  })
})
</script>

<style scoped lang="less">
@import "../table.less";
.page-container {
  height: 100vh;
  width: 100%;

  .header {
    background: var(--color-bg-2);
    margin-bottom: 24px;
  }

  .content {
    padding: 24px;
    background: var(--color-bg-2);
    height: calc(100% - 88px);

    .query-filters {
      width: 100%;

      .filter-line {
        width: 100%;
        display: flex;
        overflow: hidden;

        .filter-item {
          width: 25%;
          display: flex;
          line-height: 32px;

          .filter-label {
            margin-right: 12px;
            color: var(--tant-text-gray-color-text1-3);
          }

          .filter-content {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow-y: auto;
            flex: 1;
          }
        }

        .w-50 {
          width: 50%;
        }

        .w-75 {
          width: 75%;
        }

        .w-100 {
          width: 100%;
        }

        .h-200 {
          height: 200px;
        }

        .h-76 {
          height: 76px;
        }
      }

    }

    .query-filters-loading {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .table-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin: 16px 0;

      .display-attributes {
        flex: 1;
        display: flex;
        align-items: center;

        .label {
          margin-right: 12px;
        }
      }

      .operation {
        .operation-btn {
          background-color: transparent;

          &:hover {
            background-color: var(--tant-secondary-color-secondary-transp-hover);
          }
        }
      }
    }
  }
}
</style>