<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="uuid" label="用户ID">
                <a-input v-model="form.uuid" placeholder="请输入" allow-clear/>
            </a-form-item>
            <a-form-item field="orderId" label="平台订单号">
                <a-input v-model="form.orderId" placeholder="请输入" allow-clear/>
            </a-form-item>
            <a-form-item field="payload" label="payload">
                <a-input v-model="form.payload" placeholder="请输入" allow-clear/>
            </a-form-item>
            <a-form-item field="orderStatus" label="订单状态">
                <a-select
                    v-model:model-value="form.orderStatus"
                    placeholder=""
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in ORDER_STATUS_OPTIONS" :key="item.value" :value="item.value">{{ item.label }}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="environment" label="环境">
                <a-select
                    v-model:model-value="form.environment"
                    placeholder=""
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option value="all">全部</a-option>
                    <a-option value="Production">正式</a-option>
                    <a-option value="Sandbox">沙盒测试</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" @click="closeModal">取消</a-button>
            <a-button style="margin-right: 10px;" @click="resetData">重置</a-button>
            <a-button type="primary" @click="filterData">
                筛选
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";


const ORDER_STATUS_OPTIONS = [
  { value: 'all', label: '全部' },
  { value: 0, label: '预支付' },
  { value: 1, label: '支付中' },
  { value: 2, label: '支付失败' },
  { value: 3, label: '支付成功' },
  { value: 4, label: '收货确认' },
  { value: 5, label: '退款/退订' }
] as const;
const modalVisible = ref(false)
const modalTitle = ref('搜索条件')
const form = reactive({
    uuid:'',
    orderId:'',
    payload:'',
    orderStatus: 'all',
    environment: 'all',
})
const rules = {
}
const formRef = ref()
const emits = defineEmits(['initData']);
const openModal = () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const resetData = () => {
    formRef.value.resetFields()
    emits('initData', form);
    modalVisible.value = false
}
const filterData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            emits('initData', form);
            modalVisible.value = false
        }
    })
}

defineExpose(
    {
        openModal,
        resetData
    }
)
</script>

<style scoped lang="less">

.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}

</style>
