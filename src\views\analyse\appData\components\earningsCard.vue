<template>
    <div ref="cardRef" class="card">
       <div class="drag-allow-from card-title">
          <div class="title">
            {{ indicatorName }}
          </div>
          <div class="operation">
            <a-dropdown :popup-max-height="false" :popup-container="cardRef">
              <a-tooltip content="更多">
                <div class="operation-icon">
                  <icon-more class="icon"/>
                </div>
              </a-tooltip>
              <template #content>
                <a-doption @click="editItem"><icon-edit size="14" style="margin-right: 4px;"/>编辑</a-doption>
                <a-doption @click="deleteItem"><icon-delete size="14" style="margin-right: 4px;"/>删除</a-doption>
              </template>
            </a-dropdown>
          </div>
       </div>
       <div class="date">{{lastDate}}</div>
       <a-spin :loading="loading" class="card-content">
          <div v-if="totalNum" class="num">{{totalNum}}</div>
          <div v-else class="num">-</div>
          <div v-if="diffNum" class="secondary-data">
            <div v-if="diffNum === '-'" > - </div>
            <div v-else-if="diffNum>0" class="up"> ↑ {{formatNumber(diffNum)}}%</div>
            <div v-else class="down"> ↓ {{formatNumber(diffNum)}}%</div>
          </div>
          <div v-else class="secondary-data">-</div>
        </a-spin>
        <kpiSelect ref="kpiRef" @transfer-data="transferData"/>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue';
import { TimeParticleSize } from '@/api/enum';
import {queryComprehensiveReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import kpiSelect from './kpiSelect.vue';

interface Props {

  /**
   * 报表信息
   */
  report: any;
  indicatorList:any;
  filterParams:any
}
const props = defineProps<Props>();
const cardRef = ref<HTMLElement>();
const kpiRef = ref()
const loading = ref(false)
const indicator = ref('')
const indicatorName = ref('总收益')
const timeOut = ref()
const requestId = ref('')
const totalNum = ref()
const diffNum = ref()
const lastDate = ref('')

const emits = defineEmits(['deleteItem','updateParams'])
// 编辑
const editItem = () => {
  kpiRef.value.openModal()
}
// 删除
const deleteItem = () => {
  const id = props.report.i
  emits('deleteItem',id)
}
interface IndicatorsItem {
  code?: string;
  name?: string;
  displayName?: string;
  type?: string;
}
const computedData = () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
    }
  }, 20000)
  const indicatorsItem: IndicatorsItem = {}
  const indexList = props.indicatorList.flatMap(item => item.items)
  indexList.forEach(item => {
    if(item.code === indicator.value){
      indicatorsItem.displayName= item.displayName
      indicatorsItem.name= item.name
      indicatorsItem.code = item.code
      indicatorsItem.type="operation"
    }
  })
  const filterData = {
    logicalOperation:'and',
    filters:[
      {
        calcuSymbol: "eq",
        displayName: "国家",
        filterType: "operation",
        logicalOperation: "and",
        objectId: "operation_attribute202412210011",
        objectName: "country",
        objectType: "string",
        subFilters: [],
        thresholds: props.filterParams.country
      }
    ]
  }
  const queryParams = {
    indicators:[indicatorsItem],
    filter:props.filterParams?.country?.length ? filterData : {},
    subject: "application",
    timeParticleSize: TimeParticleSize.DAY1,
    dateRange:props.filterParams.date,
    teamCodes: props.filterParams.teamCodes,
  }
  requestId.value = queryComprehensiveReportData(queryParams);
}
const init = () => {
  if(props.report){
    indicator.value = props.report?.configParams?.indicator
    const indexList = props.indicatorList.flatMap(item => item.items)
    indexList.forEach(item => {
      if(item.code === indicator.value){
        indicatorName.value= item.displayName
      }
    })
    computedData()
  }
}
init()
// watch(() => props.filterParams, (newValue) => {
//   if(newValue && indicator.value){
//     computedData()
//   }
// },{deep:true});
const transferData = (v) => {
  indicator.value = v.indicator
  const indexList = props.indicatorList.flatMap(item => item.items)
    indexList.forEach(item => {
      if(item.code === indicator.value){
        indicatorName.value= item.displayName
      }
  })
  const data = {
    indicator:indicator.value,
  }
  emits('updateParams',props.report.i,data)
  computedData()
}
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';
  const value = Number(num);
  if (value === 0) return '0';
  const fixedNum = value.toFixed(2);
  const [intPart, decimalPart] = fixedNum.split('.');
  const formattedInt = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return decimalPart ? `${formattedInt}.${decimalPart}` : formattedInt;
}
const calculatePercentageDifference = (a, b) => {
  // console.log(a,b)
    const oldValue = a || 0
    const newValue = b || 0
    if (oldValue === 0) {
        return newValue === 0 ? 0 : '-';
    }
    const difference = newValue - oldValue;
    return (difference / oldValue) * 100;
};
watch(reportQueryResponseData, async (newData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (timeOut.value) {
    clearTimeout(timeOut.value);
  }
  const dateLength = newData?.result?.x.length
  const curIndex = dateLength > 1 ? dateLength - 2 : dateLength - 1
  const dataValues = newData?.result?.y[0]?.yData[0]?.values
  lastDate.value = newData?.result?.x[curIndex]
  totalNum.value = formatNumber(dataValues[curIndex])
  diffNum.value = calculatePercentageDifference(dataValues[curIndex - 1],dataValues[curIndex])
  loading.value = false
})

defineExpose({
  init
})
</script>

<style scoped lang="less">
.card {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  transition: all 0.3s;
  background: #fff;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 46px;
  margin-bottom: 0;
  padding: 16px 24px 8px;
  line-height: 20px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom: none;
  cursor: grab;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  .title-text {
    display: inline-block;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header5-medium);
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .title{
    font-weight: bold;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .operation {
    display: none;
    align-items: center;
    transition: opacity 0.3s;
    
  }

  .operation-icon {
    padding: 4px;
    display: inline-block;
    margin-left: 8px;
    cursor: pointer;
    border-radius: 4px;
    &:hover{
      background-color: var(--tant-bg-gray-color-bg2-1);
    }
    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }
}
.card:hover .card-title .operation {
  display: flex;
  unicode-bidi: isolate;
}

.card:hover {
  box-shadow: var(--tant-medium-shadow-medium-overall);
  border: 1px solid var(--tant-primary-color-primary-default);
  border-radius: 4px;
}
.date{
  height: 20px;
  color: var(--tant-text-gray-color-text1-3);
  font: var(--tant-description-font-description-regular);
  text-align: left;
  padding: 0 24px;
}
.card-content{
    width: 100%;
    height: calc(100% - 66px);
    // margin: 0 24px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .num{
      display: flex;
      align-items: end;
      font-size: 26px;
      font-weight: bold;
    }
    .secondary-data {
      display: flex;
      align-items: end;
      font-size: 16px;
    }

    .up {
      color: green;
    }

    .down {
      color: red;
    }
}
</style>