<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {computed, ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import dayjs from 'dayjs';
import {cloneDeep} from "lodash";
import {useSessionStorage} from '@vueuse/core';
import dataSortBtn from "../../components/dataSortBtn.vue";
import pieChart from "./pieChart.vue";

interface Props {
  /**
   * 展示label
   */
   chartType:string
  showlabel: boolean;
  showRate:boolean;
  // 同屏上限
  limitScreen:number;
  // 分组值
  groupNum:number;
  /**
   * 报表数据
   */
  eventData: any;
  // y最大值
  yMax: number | null
  // y最小值
  yMin: number | null
  // 时间格式化
  timeFormat:string
  /**
   * 高度100%显示
   */
  heightFull?:boolean
  optionStyle?:any // 选项组样式
}

const props = defineProps<Props>()

const echartType = ref('')
echartType.value = props.chartType
const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([{selected:{}}]);
const selected = ref<string[]>([]);
const indeterminate = ref(false)
const checkedAll = ref(false)

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])
const primaryName = ref('')
const secondaryName = ref('')
const hasSecondaryAxis = ref(false)
const configData = ref<any>([])
// const chartType = ref('trend')

const indicatorsInput = ref('') // 指标搜索
const indicators = ref<any>([]) // 指标
const groupsInput = ref('') // 分组搜索
const groups = ref<any>([]) // 分组
const selectedGroups = ref<string[]>([]);
const checkedDefault = ref(true)
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
groups.value = props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
}).filter(item => item !== '总体') || [];

const initGroups = () => { 
  const validGroups = checkedSessionGroups.value.filter(item => groups.value.includes(item));
  const defaultLimit = Math.min(10, groups.value.length);
  
  selectedGroups.value = validGroups.length > 0 
    ? validGroups 
    : groups.value.slice(0, defaultLimit);
  checkedDefault.value = validGroups.length === 0;
} 
initGroups()
function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);

const lineSetList = ref<any>([])

const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
const formatValue = (
    num: number | string | null | undefined,
    displayType?: { type: string; decimalNum: number }
): string => {
    if (num === null || num === undefined || num === '' || Number.isNaN(Number(num))) {
        return '';
    }
    const n = Number(num);
    if (Number.isNaN(n)) return '';
    let formattedValue = '';
    if (displayType?.type) {
        switch (displayType.type) {
            case 'default':
                formattedValue = formatNumber(n.toFixed(displayType.decimalNum));
                break;
            case 'percent':
                formattedValue = formatNumber((n * 100).toFixed(displayType.decimalNum));
                break;
            default:
                formattedValue = formatNumber(n.toFixed(2));
        }
    } else {
        formattedValue = formatNumber(n.toFixed(2));
    }
    return formattedValue;
};
const percentNameList = ref<any>([])
const {chartOption} = useChartOption(() => {
  const lineItem = lineSetList.value
  const lineItemLength = lineSetList.value.length
  return {
      grid: {
        left: hasSecondaryAxis.value && echartType.value === 'trend' ? '10%' : '0%',
        right: hasSecondaryAxis.value && echartType.value === 'trend' ? '10%' : '0%',
        top: '40',
        bottom: '100',
        containLabel: true
      },
      legend: {
        data: legendData.value,
        bottom: '60',
        type: 'scroll', // 设置图例为滚动类型
        orient: 'horizontal', // 横向显示图例
      },
      xAxis: {
        type: 'category',
        data: xAxis.value,
        axisLabel:{
          color:'#8E8E8E'
        },
        axisTick:{
          show:false
        },
        name:lineItemLength > 0 && echartType.value === 'trend' ? `${lineItem[lineItemLength-1].lineName}${lineItem[lineItemLength-1].showValue ? `:${lineItem[lineItemLength-1].lineValue}` : ''}`: '',
        nameGap:lineItemLength > 0 && echartType.value === 'trend' ? -36 : null,
        axisLine: lineItemLength > 0 && echartType.value === 'trend' ? {
            show: true,
            lineStyle: {
                color: lineItem[lineItemLength-1].lineColor,
                type: lineItem[lineItemLength-1].lineType
            }
        } : {
          show:false
        },
      },
      yAxis: hasSecondaryAxis.value && echartType.value === 'trend' ? [
        {
          type: 'value',
          max: props.yMax,
          min: props.yMin,
          name: primaryName.value,
          nameLocation: 'center',
          nameRotate:90,
          nameGap: 80,
          axisLabel: {
            color: '#8E8E8E'
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F2F3F5'
            }
          }
        },
        {
          type: 'value',
          max: props.yMax,
          min: props.yMin,
          name: secondaryName.value,
          nameLocation: 'center',
          nameRotate:90,
          nameGap: 70,
          axisLabel: {
            color: '#8E8E8E'
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false
          }
        }
      ] : {
          type: 'value',
          max: props.yMax,
          min: props.yMin,
          axisLabel: {
            color: '#8E8E8E'
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F2F3F5'
            }
          }
      },
      tooltip: {
        trigger: echartType.value !== 'distribution' ? 'axis' : 'item',
        appendToBody: true,
        enterable: true,
        confine:true,
        axisPointer: {
          type: axisPointerType.value
        },
        formatter: (params) => {
          if(echartType.value !== 'distribution'){
            // 线图
            const seriesInfo = params.map((param, index) => {
              const matchingY = ySeries.value.find(y => y.name === param.seriesName);
              if (matchingY) {
                  return `
                      <div style="display: flex; align-items: center; margin-bottom: 5px;">
                        <span style="flex-shrink: 0;">${param.marker}</span>
                        <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                        <span style="flex-shrink: 0;">${formatValue(param.value, matchingY.displayType)}${matchingY.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
                      </div>
                  `;
              }
              return '';
            }).join('');
            const tooltipHtml = `
              <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${params[0].axisValue}</div>
                ${seriesInfo}
              </div>
            `;
            return tooltipHtml;
          }
          // 柱状图 - 当trigger为'item'时，params是单个对象而不是数组
          const paramsArray = Array.isArray(params) ? params : [params];
          const seriesInfo = paramsArray.map((param, index) => {
            const matchingY = percentNameList.value.find(y => y.displayName === param.axisValueLabel);
            // 使用 formatValue 统一格式化
            const value = formatValue(
              typeof param.value === 'number' ? param.value : Number(param.value),
              matchingY?.displayType || { type: 'default', decimalNum: 2 }
            );
            return `
              <div style="display: flex; align-items: center; margin-bottom: 5px;">
                <span style="flex-shrink: 0;">${param.marker}</span>
                <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                <span style="flex-shrink: 0;">${value}${matchingY?.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
              </div>
            `;
          }).join('');
          const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${paramsArray[0].name}">${paramsArray[0].name}</div>
              ${seriesInfo}
            </div>
          `;
          return tooltipHtml;
        }
      },
      graphic: {
        elements: graphicElements.value,
      },
      series: ySeries.value,
    };
});

// 数组累加
const calculateCumulativeSums = (array:any) => {
  const numericArray = array.map(Number);
  const result = [];

  for (let i = 0; i < numericArray.length; i++) {
    if (i === 0) {
      result.push(numericArray[i]);
    } else {
      const sum = numericArray[i] + result[i - 1];
      result.push(sum);
    }
  }
  return result;
};
// 数组相加
const calculateSum = (array:any) => {
  return Number(array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0));
};

// 时间格式化x轴数据
const formatterXData = () => {
  const formatMap = {
    'm1': 'YYYY-MM-DD HH:mm:ss',
    'm5': 'YYYY-MM-DD HH:mm:ss',
    'm10': 'YYYY-MM-DD HH:mm:ss',
    'h1': 'YYYY-MM-DD HH:mm',
    'D1': 'YYYY-MM-DD',
    'W1':'YYYY-MM-DD',
    'M1':'YYYY/MM',
    'Q1':'YYYY-M',
    'Y1':'YYYY',
  };

  const format = formatMap[props.timeFormat] || null;

  if (format) {
    const list = props.eventData?.x?.map(item => props.timeFormat !== 'T0' ? dayjs(item).format(format) : item) || [];
    const suffixMap = {
        'W1': '当周',
        'M1': '月',
        'Q1': '季度',
        'Y1': '年'
    };

    if (props.timeFormat in suffixMap) {
        xAxis.value = list.map(item => `${item}${suffixMap[props.timeFormat]}`);
    } else {
        xAxis.value = list;
    }
    // xAxis.value = list;
  } else {
    xAxis.value = props.eventData.x;
  }
}

// 辅助线设置
const guideLineSet = () => {

}

const handleChartSeries = () => {
  const isIndicator = indicators.value.length > 1
  const isOverall = groups.value.length > 0;
  ySeries.value = ySeriesData.value.filter(range => {
    const nameParts = range.name.split('.(');
    const indicatorPart = nameParts[0];
    const groupPart = nameParts.length > 1 ? nameParts[1].replace(')', '') : range.name;

    // 精确匹配指标
    const matchesSelected = isIndicator && selected.value.some(group => 
      indicatorPart === group || range.name === group
    );
    
    // 精确匹配分组
    const matchesGroups = isOverall && selectedGroups.value.some(group => 
      groupPart === group || range.name === group
    );
    // const matchesSelected = isIndicator && selected.value.some(group => range.name.includes(group));
    // const matchesGroups = isOverall && selectedGroups.value.some(group => range.name.includes(group));
    if (isIndicator && !isOverall) {
      return matchesSelected; // 仅匹配 indicators
    }
    if (isIndicator && isOverall) {
      return matchesSelected && matchesGroups; // 同时匹配 indicators 和 groups
    }
    if (!isIndicator && isOverall) {
      return matchesGroups; // 仅匹配 groups
    }
    // 如果以上条件都不满足，则使用 selected.value 进行过滤
    return selected.value.includes(range.name);
  });
}
const formatYAxisName = (name: string, maxLength: number = 1) => {
  if (!name) return '';
  // const reg = new RegExp(`.{1,${maxLength}}`, 'g');
  // const nameString = name.match(reg)?.join('\n') || name
  return name.length > 40 ? name.substring(0, 40) + '...' : name;
};
// arr 指标显示设置
const freshData = (arr?: any, line?: any) => {
  axisPointerType.value = 'line';
  formatterXData();
  const data = [];
  const yLength = props.eventData?.y.length;
  
  props.eventData?.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr;
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if (yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {
        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        displayType: item.displayType,
        values: el.values,
        valuesCompared: el.valuesCompared
      });
    });
  });

  legendData.value = data.map(item => item.name);
  indicators.value = props.eventData?.y.map(item => item.displayName);
  configData.value = arr || []
  // 判断是否启用次轴
  hasSecondaryAxis.value = arr && arr.some(item => item.showSub);
  if (hasSecondaryAxis.value) {
      // 设置次轴名称 
      const secondaryNames = arr.filter(item => item.showSub).map(item => item.name).join('/');
      secondaryName.value = formatYAxisName(secondaryNames);
      // 设置主轴名称
      const primaryNames = arr.filter(item => !item.showSub).map(item => item.name).join('/');
      primaryName.value = formatYAxisName(primaryNames);
    } else {
      secondaryName.value = '';
      if (arr && arr.length) {
        const allNames = arr.map(item => item.name).join('/');
        primaryName.value = formatYAxisName(allNames);
      } else {
        primaryName.value = '';
      }
  }

  // 构建 series 数据
  let commonSeries;
  if (arr && arr.length) {
    commonSeries = data.map((item, index) => {
      const seriesItem: any = {
        name: item.name,
        data: item.values,
        displayType: item.displayType,
        type: arr[index].type,
        barWidth: arr[index].type === 'bar' ? 40 : '',
        label: {
          show: props.showlabel
        }
      };
      // 分配到不同 Y 轴
      if (hasSecondaryAxis.value && arr[index].showSub) {
        seriesItem.yAxisIndex = 1;
      }
      return seriesItem;
    });
  } else {
    commonSeries = data.map(item => {
      return {
        name: item.name,
        data: item.values,
        displayType: item.displayType,
        type: 'line',
        label: {
          show: props.showlabel
        }
      };
    });
  }

  if (echartType.value === 'trend' && line && line.length > 0) {
    lineSetList.value = line;
  }

  if (echartType.value === 'stacked') {
    ySeries.value = data.map(series => {
      return {
        name: series.name,
        data: series.values,
        displayType: series.displayType,
        type: 'line',
        label: {
          show: props.showlabel
        },
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        }
      };
    });
  } else {
    ySeries.value = commonSeries;
  }

  selected.value = indicators.value;
  checkedAll.value = true;
  ySeriesData.value = cloneDeep(ySeries.value);
  handleChartSeries();
};

const freshTotalData = () => {

  axisPointerType.value = 'line'
  // xAxis.value = props.eventData.x
  formatterXData()
  const yLength = props.eventData?.y.length
  const data = [];
  props.eventData.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if ( yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if ( yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      }else {
        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        // name:item.displayName,
        displayType:item.displayType,
        values:el.values
      });
    });
  });
  ySeries.value = data.map(item => {
    return {
      name: item.name,
      displayType:item.displayType,
      data: calculateCumulativeSums(item.values),
      type: 'line',
      label: {
        show: props.showlabel
      }
    }
  });
  indicators.value = props.eventData?.y.map(item => item.displayName)
  legendData.value = data.map(item => item.name);
  
  selected.value = indicators.value;
  checkedAll.value = true;
  ySeriesData.value = cloneDeep(ySeries.value)
};

const freshDistributionData = () => {
  axisPointerType.value = 'shadow'
  const data = [];
  props.eventData.y.forEach(item => {
    item.yData.forEach(el => {
      data.push({
        displayName:item.displayName,
        displayType:item.displayType,
        groupName:el.group.map(value => value === null ? 'null' : value).join(','),
        values:calculateSum(el.values),
        valuesCompared: el.valuesCompared ? el.valuesCompared.map(val => calculateSum(val)) : []
      });
    });
  });

  const uniqueGroups = [...new Set(data.map(item => item.groupName))];
  // 为每个 groupName 创建一个 series 对象
  ySeries.value = uniqueGroups.map(groupName => {
      const groupData = data.filter(item => item.groupName === groupName);
      return {
          name: groupName,
          data: groupData.map(item => item.values),
          type: 'bar',
          barWidth: uniqueGroups.length > 10 ? 'auto' : 40,
          label: {
              show: props.showlabel
          },
          large: true
      };
  });
  percentNameList.value = Array.from(
      new Map(
          data.filter(item => item.displayType.type === 'percent') // 筛选出 type 为 percent 的项
              .map(item => [item.displayName, { displayName: item.displayName, displayType: item.displayType }]) // 创建以 displayName 为键的 Map
      ).values() // 获取 Map 的值
  );

  indicators.value = props.eventData?.y.map(item => item.displayName)

  xAxis.value = props.eventData?.y.map(item => item.displayName)
  legendData.value = groups.value.map(item => item);
  
  
  selected.value = indicators.value;
  checkedAll.value = true;
  xAxisData.value = cloneDeep(xAxis.value)
  ySeriesData.value = cloneDeep(ySeries.value)
  
};

const carouselCurrent = ref(1)
// 把数组等分
const chunkArray = (array, chunkSize) => {
  const result = [];
  const size = array.length < chunkSize ? array.length : chunkSize
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  carouselCurrent.value = 1
  return result;
}
// 饼图
const pieData = ref<any>([])
const pieDataCopy = ref<any>([])
const pieLimitData = ref<any>([])
const freshPieData = () => {
  axisPointerType.value = 'line'
  pieData.value = []
  props.eventData.y.forEach(item => {
    const seriesData = item.yData.map(el => {
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      return {
        name:processedGroup.join('.') || '',
        value: calculateSum(el.values),
      };
    });

    // 按值大小降序排序
    seriesData.sort((a, b) => b.value - a.value);
    
    // 切割前 groupNum 项，后面的项统一为 '其它'
    const displayedData = seriesData.slice(0, props.groupNum);
    const otherData = seriesData.slice(props.groupNum);
    
    if (otherData.length > 0) {
      const otherTotalValue = otherData.reduce((sum, el) => sum + el.value, 0);
      displayedData.push({
        name: '其它',
        value: otherTotalValue,
      });
    }

    pieData.value.push({
      displayName: item.displayName,
      seriesData: displayedData,
    });
  });

  legendData.value = pieData.value.map(item => item.displayName);
  pieDataCopy.value = cloneDeep(pieData.value)
  pieLimitData.value = chunkArray(pieData.value, props.limitScreen)
  selected.value = legendData.value;
};
    
watch(() => props.groupNum, (newValue, oldValue) => {
  if (echartType.value === 'pieTrend' && newValue !== oldValue) {
    freshPieData();
  }
}, { immediate: false });
const changeType = (type:string)=>{
  echartType.value = type
  switch (type){
    case 'trend':
      freshData(configData.value)
      break;
    case 'stacked':
      freshData()
      break;
    case 'total':
      freshTotalData()
      break;
    case 'distribution':
      freshDistributionData()
      break;
    case 'pieTrend':
      freshPieData()
      break;
    default:
      break;
  }
}

changeType(props.chartType)

// 全选
const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    selected.value = echartType.value === 'pieTrend' ? cloneDeep(legendData.value) : cloneDeep(indicators.value)
  } else {
    checkedAll.value = false;
    selected.value = []
  }
}
const handleChange = (values) => {
  const indicatorLength = echartType.value === 'pieTrend' ? legendData.value.length : indicators.value.length
  if (values.length === indicatorLength) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
}

const handleChangeGroups = (values) => {
  checkedSessionGroups.value = values
}

const handleBarChart = () => {
  ySeries.value = cloneDeep(ySeriesData.value);
  // 先过滤 xAxis，只保留选中的指标
  xAxis.value = xAxisData.value.filter(range => {
    return selected.value.some(item => item === range);
  });
  if (xAxis.value.length > 0) {
    // 获取选中指标在原始 xAxisData 中的索引
    const selectedIndices = [];
    xAxisData.value.forEach((item, index) => {
      if (selected.value.some(sel => sel === item)) {
        selectedIndices.push(index);
      }
    });
    const newSeries = [];
    for (const series of ySeries.value) {
      // 只保留选中的分组
      if (selectedGroups.value.some(group => group === series.name) || groups.value.length === 0) {
        const newSeriesItem = cloneDeep(series);
        // 创建新的 data 数组，只包含选中指标索引对应的数据
        const newData = [];
        selectedIndices.forEach(index => {
          if (index < series.data.length) {
            newData.push(series.data[index]);
          }
        });
        newSeriesItem.data = newData;
        newSeries.push(newSeriesItem);
      }
    }
    ySeries.value = newSeries;
    // 如果 ySeries 为空，则 xAxis 也设为空
    if (ySeries.value.length === 0) {
      xAxis.value = [];
    }
  } else {
    ySeries.value = [];
  }
}
const updateYAxisSeriesGroups = () => {
  if(echartType.value === 'distribution'){
    handleBarChart()
  }else{
    handleChartSeries()
  }
};
const updateYAxisSeries = () => {
  if(echartType.value === 'distribution'){
    handleBarChart()
  }else{
    handleChartSeries()
    pieData.value = pieDataCopy.value.filter(range => selected.value.includes(range.displayName))
    pieLimitData.value = chunkArray(pieData.value, props.limitScreen)
  }
};

watch(selectedGroups, (newValue:any,oldValue:any) => {
  if(newValue){
    updateYAxisSeriesGroups();
  }
},{immediate:true,deep:true})

watch(selected, (newValue:any,oldValue:any) => {
  if(newValue){
    updateYAxisSeries();
  }
})

watch(() => props.showlabel,(newValue:any,oldValue:any) => {
  ySeries.value.forEach((item:any,index:number)=>{
    item.label = {
      show: props.showlabel
    }
  })
},{immediate:true})

// 默认选中的分组数设置
const showSide = ref(false)
const limitCustom = ref(10)
const limitType = ref('less')
const sureType = ref('less')
const limitGroupNumber = ref(10)
const handleDefaultGroupNum = () => {
  limitType.value = sureType.value
  showSide.value = !showSide.value
}
const limitChange = (v) => {

}
const checkedChangeDefault = (value) => {
  if(value){
    const copyGroups = cloneDeep(groups.value);
    checkedDefault.value = true;
    selectedGroups.value = copyGroups.slice(0, limitGroupNumber.value);
  }else{
    checkedDefault.value = false
    selectedGroups.value = []
  }
}
const limitCancel = () => {
  showSide.value = false
}
const limitSure = () => {
  sureType.value = limitType.value
  if(sureType.value === 'less') {
    limitGroupNumber.value = 10
    limitCustom.value = 10
  }else if(sureType.value ==='middle'){
    limitGroupNumber.value = 20
    limitCustom.value = 20
  }else if(sureType.value ==='more'){
    limitGroupNumber.value = 30
    limitCustom.value = 30
  }else{
    limitCustom.value = echartType.value === 'distribution' ? limitCustom.value : Math.min(limitCustom.value, 50);
    limitGroupNumber.value = limitCustom.value
  }
  groups.value = props.eventData?.groups?.map(item => {
      return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  selectedGroups.value = groups.value.slice(0, limitGroupNumber.value);
  changeType(echartType.value)
  showSide.value = false

}
const popChange = () => {
  showSide.value = false
}


const filteredIndicators = computed(() => {
  const str = indicatorsInput.value.trim().toLowerCase();
  return indicators.value.filter(item => item.toLowerCase().includes(str));
});

const filteredGroups = computed(() => {
  const str = groupsInput.value.trim().toLowerCase();
  return groups.value.filter(item => item.toLowerCase().includes(str));
});

defineExpose({
  changeType,
  freshData,  
})

</script>

<template>
  <div class="chart-content">
    <a-spin  style="width: 100%;height: 500px;" :style="heightFull?'height: 100%':''">
      <div style="display: flex;justify-content: space-between;margin-top: 5px;" :style="optionStyle">
        <div>
        </div>
        <div style="display: flex;">
          <!-- 指标 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true">
            <div v-if="indicators.length>1" style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">指标( {{selected.length}}/{{indicators.length}})<icon-down/></div>
            <template #content>
                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                  <a-input v-model="indicatorsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                    <template #prefix>
                      <icon-search />
                    </template>
                  </a-input>
                </div>
                <a-checkbox-group v-if="filteredIndicators.length" v-model="selected" direction="vertical" style="width: 100%;" @change="handleChange">
                    <a-checkbox v-for="(item,index) in filteredIndicators" :key="index" :value="item">
                      <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                    </a-checkbox>
                </a-checkbox-group>
                <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);">
                <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" style="width: 100%;" @change="handleChangeAll">全选</a-checkbox>
              </div>
            </template>
          </a-dropdown>
          <!-- 分组项 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true" @popup-visible-change="popChange">
            <div v-if="groups.length>0 && echartType !== 'pieTrend'" style="min-height: 32px;margin-left: 16px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">分组( {{ selectedGroups.length }}/{{groups.length}})<icon-down/></div>
            <template #content>
                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                  <a-input v-model:model-value="groupsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                    <template #prefix>
                      <icon-search />
                    </template>
                  </a-input>
                </div>
                <a-checkbox-group v-if="filteredGroups.length" v-model="selectedGroups" direction="vertical" style="width: 100%;" @change="handleChangeGroups">
                    <a-checkbox v-for="(item,index) in filteredGroups" :key="index" :value="item">
                      <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                    </a-checkbox>
                </a-checkbox-group>
                <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);display: flex;align-items: center;">
                <a-checkbox :model-value="checkedDefault" style="width: 100%;" @change="checkedChangeDefault">默认</a-checkbox>
                <div class="icon-set" @click="handleDefaultGroupNum">
                  <icon-settings />
                </div>
              </div>
              <!-- 默认设置 -->
              <div v-if="showSide" class="ta-default-group-side-left">
                <div class="group-side">
                  <p class="group-side-head">默认选中的分组数</p>
                  <div class="group-side-body">
                    <a-radio-group v-model:model-value="limitType" direction="vertical" style="width: 100%;" @change="limitChange">
                      <a-radio value="less">
                        <div class="radio-desc">
                          <span>少</span>
                          <span class="limit-set">10</span>
                        </div>
                      </a-radio>
                      <a-radio value="middle">
                        <div class="radio-desc">
                          <span>中</span>
                          <span class="limit-set">20</span>
                        </div>
                      </a-radio>
                      <a-radio value="more">
                        <div class="radio-desc">
                          <span>多</span>
                          <span class="limit-set">30</span>
                        </div>
                      </a-radio>
                      <a-radio value="custom">
                        <div class="radio-desc">
                          <span>自定义分组</span>
                          <a-input-number v-model:model-value="limitCustom" :disabled="limitType !== 'custom'" :style="{width:'66px',height:'24px'}" :precision="0" :min="1" :hide-button="true"/>
                        </div>
                      </a-radio>
                    </a-radio-group>
                    <div class="group-info-wrap">
                      <p class="group-info">默认选中前<b class="bold">{{limitGroupNumber}}</b>个分组项</p>
                      <p class="group-info">分组项与当前排序设置一致</p>
                      <p class="group-info">仅柱图支持 50 个分组项以上的展示</p>
                    </div>
                  </div>
                  <div class="group-side-foot">
                    <a-button class="cancel" @click="limitCancel">取消</a-button>
                    <a-button type="primary" @click="limitSure">确定</a-button>
                  </div>
                </div>
              </div>
            </template>
          </a-dropdown>
          <!-- 数据量排序 -->
          <dataSortBtn v-if="pieData.length>=1000"/>
        </div>
      </div>
      <!-- <a-alert v-if="pieData.length>=1000">因数据条数过多，优先展示前1000个分组值的数据。建议修改分组项减少数据条数，或点击<span class="download-txt">数据下载</span>可获取完整数据。</a-alert> -->
      <a-carousel
        v-if="echartType === 'pieTrend'"
        v-model:current="carouselCurrent"
        :style="{
          width: '100%',
          height: '376px',
        }"
        indicator-type="never"
      >
        <a-carousel-item v-for="(item,index) in pieLimitData" :key="index">
          <div style="display: flex;justify-content: space-between">
            <div v-for="(el,elIndex) in item" :key="elIndex" :style="{ width: `${100 / item.length}%`, height: '376px' }">
              <div class="pie-name">{{el.displayName}}</div>
              <pieChart :series-data="el.seriesData" :showlabel="showlabel" :show-rate="showRate"/>
            </div>
          </div>
        </a-carousel-item>
      </a-carousel>
      <Chart v-else :option="chartOption"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
:deep(.arco-dropdown-option){
  padding: 0;
}
:deep(.arco-dropdown-option-content){
  max-width: 200px;
}
:deep(.arco-checkbox){
  padding: 0 12px;
  &:hover{
    background-color: var(--color-fill-2);
  }
}
.pie-name{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header5-medium);
  margin-top: 24px;
}
:deep(.arco-carousel-arrow > div){
  z-index: 9999;
  &:hover{
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}
:deep(.arco-carousel-arrow > div>svg){
  color: var(--tant-text-gray-color-text1-2);
}
.download-txt {
    margin: 0 4px;
    color: var(--tant-primary-color-primary-default);
    cursor: pointer;
}
.icon-set{
  padding-right: 12px;
  cursor: pointer;
  color: var(--tant-primary-color-primary-hover);
}
.cancel {
  background: transparent;
  margin-right: 8px;
  &:hover {
    background-color: var(--color-secondary);
  }
}
.ta-default-group-side-left{
  position: absolute;
  top: 0;
  left: -285px;
  width: 280px;
  
  height: 0;
  .group-side{
    width: 100%;
    padding: 16px 16px 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: none;
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-bottom);
    .group-side-head {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 30px;
        margin-bottom: 16px;
        padding-left: 0;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }
    .group-side-body {
        min-height: 220px;
        padding-left: 0;
        .radio-desc{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .limit-set {
              color: var(--tant-text-gray-color-text1-3);
          }
        }
        :deep(.arco-radio-label){
          width: 100%;
        }
        .group-info-wrap {
          margin-top: 24px;
          margin-bottom: 24px;
          .group-info {
            width: 100%;
            margin-bottom: 8px;
            margin-left: 0;
            color: var(--tant-text-gray-color-text1-3);
            font-size: 14px;
            line-height: 22px;
            .bold {
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
            }
          }
          .group-info:before {
              position: relative;
              top: 3px;
              padding-right: 10px;
              font-weight: 500;
              font-size: 20px;
              content: "\b7";
          }
      }
    }
    .group-side-foot{
      margin-right: -16px;
      margin-left: -16px;
      padding: 8px 16px 0;
      text-align: right;
      border-top: 1px solid var(--tant-border-color-border1-1);
      button{
        border-radius: 4px;
      }
    }
  }
}
</style>
