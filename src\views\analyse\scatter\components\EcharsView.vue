<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {computed, ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import {cloneDeep} from "lodash"
import {useSessionStorage} from '@vueuse/core';
import { TimeParticleSize } from '@/api/enum';

interface Props {
  /**
   * 报表数据
   */
  eventData: any;
  graphType: string;
}

const props = defineProps<Props>()


const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([]);

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])
const chartType = ref(props.graphType);
const showlabel = ref(false)
const indicatorData = props.eventData?.indicator?.eventList?.[0]
const groupsDesc = ref<any>([])
const groupsInput = ref('') // 分组搜索
const groups = ref<any>([])
const checkedSessionGroup = useSessionStorage('checkedSessionGroup', '总体')
const selectGroupName = ref('总体')

groups.value = props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
}).filter(item => item !== '总体') || [];
groupsDesc.value = props.eventData?.groupsDesc || []

const initGroup = () => {
  if(groups.value.includes(checkedSessionGroup.value)){
    selectGroupName.value = checkedSessionGroup.value
  }
}
initGroup()
function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
// 格式化时间列
const formatTimeColumn = (dateString:string) => {
  const {timeParticleSize}  = props.eventData
  if(timeParticleSize === TimeParticleSize.DAY1){
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if(timeParticleSize === TimeParticleSize.WEEK1){
    return `${dateString}当周`;
  }
  if(timeParticleSize === TimeParticleSize.MONTH1){
    return `${dateString}月`;
  }
  return `${dateString}`;
}
const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '6%',
      right: '2.6%',
      top: '60',
      bottom: '60',
    },
    legend: {
      data: legendData.value,
      bottom: '10',
      type: 'scroll', // 设置图例为滚动类型
      orient: 'horizontal', // 横向显示图例

    },
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: chartType.value === 'stackedRate' ? '{value} %' : '{value}'
      },
      max:chartType.value === 'stackedRate' ? 100 : null,
      splitLine:{
        lineStyle:{
          type:'dashed'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
      axisPointer: {
        type: axisPointerType.value
      },
      enterable: true,
      confine: true,
      extraCssText: 'max-height: 300px; overflow-y: auto;width: 180px;',
      formatter: (params) => {
        const date = params[0].axisValue;
        const formattedDate = formatTimeColumn(date);
        let result = `${formattedDate}<br/>`;
        params.forEach(param => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          const value = chartType.value === 'stackedRate' ? `${param.value}%` : param.value;
          result += `${marker} ${seriesName}: ${formatNumber(value)}<br/>`;
        });
        return result;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});


const freshData = () => {
  axisPointerType.value = 'line'
  const { scatterQueryResult,stages } = props.eventData
  const secondaryData = chartType.value ==='trend' ? scatterQueryResult?.[1] : scatterQueryResult?.[0]
  xAxis.value = secondaryData.map(item => item.eventDate)
  legendData.value = stages
  // 处理每个阶段的数据
  const commonSeries = stages.map((stageName, index) => {
    // 根据是否选择总体来决定使用哪个数据源
    const getData = (item) => {
      let value;
      if (selectGroupName.value === '总体') {
        value = item.summaryData[index]
      } else {
        // 在 groupData 中查找匹配的分组
        const groupItem = item.groupData.find(g => g.group.join(',') === selectGroupName.value)
        value = groupItem ? groupItem.values[index] : 0
      }
      
      // 如果是堆叠百分比图，计算该值占总和的百分比
      if (chartType.value === 'stackedRate') {
        const total = selectGroupName.value === '总体' 
          ? item.summaryDataSum
          : item.groupData.find(g => g.group.join(',') === selectGroupName.value)?.valueSum || 0
        return total ? Number(((value / total) * 100).toFixed(2)) : 0
      }
      return value
    }
    return {
      name: stageName,
      type: 'line',
      data: secondaryData.map(item => getData(item)),
      label: {
        show: showlabel.value,
        formatter: chartType.value === 'stackedRate' ? '{c}%' : '{c}'
      }
    }
  })
  if (chartType.value === 'stacked' || chartType.value === 'stackedRate') {
    ySeries.value = commonSeries.map(series => ({
      ...series,
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      }
    }))
  } else {
    ySeries.value = commonSeries
  }
  ySeriesData.value = cloneDeep(ySeries.value)
};
const freshDistributionData = () => {
  legendData.value = []
  axisPointerType.value = 'shadow'
  const { scatterQueryResult,stages } = props.eventData
  const secondaryData = chartType.value ==='distribution' ? scatterQueryResult?.[1] : scatterQueryResult?.[0]
  xAxis.value = stages
  const getSeriesData = (data) => {
      if (selectGroupName.value === '总体') {
        return data.summaryData
      }
      // 在 groupData 中查找匹配的分组
      const groupItem = data.groupData.find(g => g.group.join(',') === selectGroupName.value)
      return groupItem ? groupItem.values : []
    }
  const seriesData = getSeriesData(secondaryData[0])
  ySeries.value = [
    {
      name: `${indicatorData.eventDisplayName}.${indicatorData.eventAttrName}`,
      type: 'bar',
      data: seriesData,
      barWidth: chartType.value === 'zfTrend'? null : 40,
      barCategoryGap:chartType.value === 'zfTrend' ? 5 : null,
      label: {
        show: showlabel.value,
        position: 'insideTop'
      }
    }
  ]
}

const changeType = (type:string)=>{
  groups.value = props.eventData?.groups?.map(item => {
      return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  groupsDesc.value = props.eventData?.groupsDesc || []
  chartType.value = type
  switch (type){
    case 'trend':
      freshData()
      break;
    case 'zfTrend':
      freshDistributionData()
      break;
    case 'distribution':
      freshDistributionData()
      break;
    case 'stacked':
      freshData()
      break;
    case 'stackedRate':
      freshData()
      break;
    default:
      break;
  }
}
changeType(chartType.value)
// freshData()
const groupSelectChange = (value:any) => {
  selectGroupName.value = value
  checkedSessionGroup.value = value
  switch (chartType.value){
    case 'trend':
      freshData()
      break;
    case 'zfTrend':
      freshDistributionData()
      break;
    case 'distribution':
      freshDistributionData()
      break;
    case 'stacked':
      freshData()
      break;
    case 'stackedRate':
      freshData()
      break;
    default:
      break;
  }
}
watch(() => showlabel.value,(newValue:any,oldValue:any) => {
  ySeries.value.forEach((item:any,index:number)=>{
    item.label = {
      show: showlabel.value,
      formatter: (params) => formatNumber(params.value)
    }
  })
  
},{immediate:true})
const filteredGroups = computed(() => {
  // 将总体添加到分组列表的开头
  const list = ['总体', ...groups.value]
  const str = groupsInput.value.trim().toLowerCase()
  return list.filter(item => item.toLowerCase().includes(str));
});
defineExpose({
  changeType
})

</script>

<template>
  <div class="chart-content">
    <a-spin style="flex: 1 1 0; display: flex; flex-direction: column; height: 100%;">
      <div style="display: flex;justify-content: space-between;">
        <div>
        </div>
        <div style="display: flex;align-items: center;">
          <a-switch v-model:model-value="showlabel" size="small" style="margin-right: 4px;"/>
          显示数值
          <!-- 分组项 -->
          <a-dropdown :update-at-scroll="true" @select="groupSelectChange">
            <div v-if="groupsDesc.length>0" style="min-height: 32px;margin-left: 16px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">分组:{{ selectGroupName }}<icon-down/></div>
            <template #content>
              <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input v-model:model-value="groupsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                  <template #prefix>
                    <icon-search />
                  </template>
                </a-input>
              </div>
              <a-doption v-for="(item,index) in filteredGroups" :key="index" :value="item" style="min-width: 160px;">{{ item }}</a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
      <div v-if="chartType ==='stacked'" class="title">
        用户 的 {{ indicatorData.eventDisplayName }}.{{indicatorData.eventAttrName}}，分布情况
        <span v-if="selectGroupName != '总体'">({{ selectGroupName }})</span>
      </div>
      <div v-if="chartType ==='stackedRate'" class="title">
        用户 的 {{ indicatorData.eventDisplayName }}.{{indicatorData.eventAttrName}}，百分比分布情况
        <span v-if="selectGroupName != '总体'">({{ selectGroupName }})</span>
      </div>
      <Chart :option="chartOption" style="flex: 1 1 0; height: 100%;"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.title{
  color: var(--tant-text-gray-color-text1-2);
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
}
</style>