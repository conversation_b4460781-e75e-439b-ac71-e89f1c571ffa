<template>
  <div class="guide">
    <div v-show="gameUserCluster.condition.singleConditionExpressions.length||gameUserCluster.condition.sequenceConditionExpressions.length ||gameUserCluster.condition.userCondition?.filters?.length" class="event-filter-box">
      <div class="action-row">
        <div class="row-filters">
          <div class="relation-editor">
            <div
              v-if="gameUserCluster.condition.singleConditionExpressions.length||
              gameUserCluster.condition.sequenceConditionExpressions.length"
              class="relation-relation">
              <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
              <div v-if="getTotalConditionCount" :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="logicalChange">
                <span v-if="gameUserCluster.condition.logicalOperation == 'and'">且</span>
                <span v-else>或</span>
              </div>
            </div>

            <div style="display: flex;flex-direction: column;">
              <div class="relation-editor">
                <div
                  v-if="gameUserCluster.condition.sequenceConditionExpressions.length >1 ||
                  gameUserCluster.condition.singleConditionExpressions.length >1 ||
                  gameUserCluster.condition.sequenceConditionExpressions.length + gameUserCluster.condition.singleConditionExpressions.length>1"
                  class="relation-relation">
                  <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
                  <div 
                    v-if="gameUserCluster.condition.sequenceConditionExpressions.length >1 ||
                    gameUserCluster.condition.singleConditionExpressions.length >1 ||
                    gameUserCluster.condition.sequenceConditionExpressions.length +gameUserCluster.condition.singleConditionExpressions.length >1"
                    :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'"
                    @click="() => {
                      if(!props.disabled) {
                        gameUserCluster.condition.eventIsAnd = gameUserCluster.condition.eventIsAnd === 'and' ? 'or' : 'and'
                      }
                    }">
                    <span v-if="gameUserCluster.condition.eventIsAnd == 'and'">且</span>
                    <span v-else>或</span>
                  </div>
                </div>
                <div style="display: flex;flex-direction: column;">
              <!-- 做没做过的事件 -->
              <div class="relation-main">
                <div v-for="(item, index) in gameUserCluster.condition.singleConditionExpressions" :key="index" class="relation-row">
                  <div class="multi-filter-condition">
                    <div class="sub-action-row">
                      <div class="sub-action-left">
                        <a-space wrap size="small" style="width: 100%;">
                          <div v-if="!props.disabled">
                            <a-trigger
                              v-model:popup-visible="doSelectVisible[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="doSelectVisible[index] = !doSelectVisible[index];">
                              <div class="filter-btn" style="width: 60px;margin-left:4px; ">
                                <span class="filter-label">{{ item.isDone ? '做过' : '没做过' }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible[index] = false; }">
                                    <div>
                                      做过
                                    </div>
                                  </div>
                                  <div class="filter-select-item" @click="() => { item.isDone = false; doSelectVisible[index] = false; }">
                                    <div>
                                      没做过
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-else>
                            <div class="filter-btn-disabled" style="width: 60px;margin-left:4px; ">
                              <span class="filter-label">{{ item.isDone ? '做过' : '没做过' }}</span>
                            </div>
                          </div>
                          <div style="width: 100%;">
                            <EventIndicatorSelect
                              :disabled="props.disabled"
                              :panel-data="item"
                              @analysis-index-change="(event)=>{
                                if(event.type === 'indicator'){
                                  item.indicatorCode = event.indicatorCode;
                                  item.indicatorDisplayName = event.indicatorDisplayName;
                                  item.indicatorName = event.indicatorName;
                                  item.eventType = event.eventType;
                                }else{
                                  item.eventCode = event.eventCode;
                                  item.eventDisplayName = event.eventDisplayName;
                                  item.eventName = event.eventName;
                                  item.eventType = event.eventType;
                                }
                            }"/>
                          </div>
                          <div v-if="item.isDone" class="itemIndexTitle">
                            的
                          </div>
                          <div v-if="item.isDone">
                            <EventAttrSelect
                              :disabled="props.disabled"
                              :panel-data="item"
                              @sub-change="(event) => {
                              item.eventAttrName = event.eventAttrName;
                              item.eventAttrCode = event.eventAttrCode;
                              item.eventAttrDisplayName = event.eventAttrDisplayName;
                              item.statisticalType = event?.statisticalType || undefined;
                              item.objectType = event.objectType
                              }
                              "/>
                          </div>
                          <div v-if="item.isDone && !props.disabled">
                            <a-trigger
                              v-model:popup-visible="symbolSelectVisible[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="symbolSelectVisible[index] = !symbolSelectVisible[index];">
                              <div class="filter-btn " style="width: 75px;">
                                <span class="filter-label">{{ transformSymbol(item.calcuSymbol) }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.GREATER_THAN, symbolSelectVisible[index] = false;}">
                                    <div>大于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.GREATER_THAN_OR_EQUAL, symbolSelectVisible[index] = false;}">
                                    <div>大于等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.LESS_THAN, symbolSelectVisible[index] = false; }">
                                    <div>小于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => {item.calcuSymbol = CalculateSymbol.LESS_THAN_OR_EQUAL, symbolSelectVisible[index] = false;}">
                                    <div>小于等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item" @click="() => { item.calcuSymbol = CalculateSymbol.EQUAL, symbolSelectVisible[index] = false; }">
                                    <div>等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item" @click="() => { item.calcuSymbol = CalculateSymbol.NOT_EQUAL, symbolSelectVisible[index] = false; }">
                                    <div>不等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.SCOPE, symbolSelectVisible[index] = false;}">
                                    <a-tooltip position="left" :popup-translate="[-5, 0]" mini>
                                      <template #content>
                                        区间：属性值在设置的数值区间范 <br> 围内(左闭右闭)
                                      </template>
                                      <div style="display: flex;justify-content: space-between;">
                                        区间
                                        <icon-info-circle/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-if="item.isDone && props.disabled">
                            <div class="filter-btn-disabled" style="width: 75px;">
                                <span class="filter-label">{{ transformSymbol(item.calcuSymbol) }}</span>
                              </div>
                          </div>
                          <div v-show="item.isDone"  v-if="item.calcuSymbol !== CalculateSymbol.SCOPE">
                            <a-input-number v-model="item.thresholds[0]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => firsetThresholdChange(index, val)"/>
                          </div>
                          <div v-show="item.isDone" v-else style="display: flex;flex-direction: row;width: 100%;">
                            <a-input-number v-model="item.thresholds[0]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => handleThresholdChange(index, val)"/>
                            <div class="itemIndexTitle">
                              与
                            </div>
                            <a-input-number v-model="item.thresholds[1]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => handleThresholdChange(index, val)"/>
                            <div class="itemIndexTitle">
                              之间
                            </div>
                          </div>
                          <div style="color: var(--tant-text-gray-color-text1-3);line-height: 27px;width: 30px;">
                            ，在
                          </div>
                          <div v-if="!showUnifiedCycle">
                            <date-picker disabled-after-today :date-range="item.dateRange" @date-pick="date => {item.dateRange = date;}"/>
                          </div>
                          <div v-else>
                            <button class="filter-btn" :disabled="props.disabled">
                              {{ unifiedDateRange.dateText}}
                              <icon-calendar/>
                            </button>
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" :size="2">
                          <a-tooltip content="添加筛选条件" position="top">
                            <a-button class="btn-bg btn-26" style="margin-left: 8px;" @click="addFilters(index)">
                              <template #icon>
                                <icon-filter/>
                              </template>
                            </a-button>
                          </a-tooltip>
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>
                    <filterComponent :ref="item => queryFilterRefs[index] = item" :filter="item.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...item }]" @query-filters-change="queryFiltersChange(index,$event)"/>
                  </div>
                </div>
              </div>

              <!-- 依次做过事件 -->
              <div class="relation-main">
                <div v-for="(item, index) in gameUserCluster.condition.sequenceConditionExpressions" :key="index" class="relation-row">
                  <div class="multi-filter-condition">
                    <div class="sub-action-row">
                      <div class="sub-action-left">
                        <a-space wrap size="small" style="width: 100%;">
                          <div v-if="!props.disabled">
                            <a-trigger
                              v-model:popup-visible="doSelectVisible2[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="doSelectVisible2[index] = !doSelectVisible2[index];">
                              <div class="filter-btn" style="margin-left: 4px;">
                                <span class="filter-label">{{ item.isDone ? '依次做过' : '依次没做过' }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible2[index] = false; }">
                                    <div>
                                      依次做过
                                    </div>
                                  </div>
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible2[index] = false; }">
                                    <div>
                                      依次没做过
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-else>
                            <div class="filter-btn-disabled" style="margin-left: 4px;">
                              <span class="filter-label">{{ item.isDone ? '依次做过' : '依次没做过' }}</span>
                            </div>
                          </div>
                          <div class="itemIndexTitle">
                            以下事件，在
                          </div>
                          <date-picker :date-range="item.dateRange" disabled-after-today @date-pick="date => {item.dateRange = date;}"/>
                          <button v-if="showUnifiedCycle" class="filter-btn" :disabled="props.disabled">
                            {{ unifiedDateRange.dateText}}
                            <icon-calendar/>
                          </button>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <div class="sub-action-row">
                          <a-space align="center" :size="2">
                            <a-dropdown trigger="hover">
                              <a-button class="btn-bg btn-26" style="margin-left: 8px;">
                                <template #icon>
                                  <icon-more-vertical/>
                                </template>
                              </a-button>
                              <template #content>
                                <a-tooltip content="最多可添加5个关联属性">
                                  <a-doption v-show="item.sameEventAttr.length >= 5" disabled>添加关联属性</a-doption>
                                </a-tooltip>
                                <a-doption v-show="item.sameEventAttr.length < 5" @click="addItemSameEvent(index)">添加关联属性
                                </a-doption>
                                <a-doption v-show="item.timeWindow && Object.keys(item.timeWindow).length == 0" @click="addTimeWindow(index)">添加时间窗口
                                </a-doption>
                                <a-doption v-show="item.timeWindow && Object.keys(item.timeWindow).length !== 0" @click="deleteTimeWindow(index)">删除时间窗口
                                </a-doption>
                              </template>
                            </a-dropdown>
                            <a-tooltip content="添加事件" position="top">
                              <a-button v-show="item.eventList.length < 20" class="btn-bg btn-26" @click="addSeqEvent( index)">
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="最多可添加20个事件" position="top">
                              <a-button v-show="item.eventList.length >= 20" class="btn-bg btn-26" disabled>
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="删除" position="top">
                              <a-button class="btn-bg-delete btn-26" @click="deleteDone(index)">
                                <template #icon>
                                  <icon-close-circle/>
                                </template>
                              </a-button>
                            </a-tooltip>
                          </a-space>
                        </div>
                      </div>
                    </div>
                    <div v-for="(Same, SameIndex) in item.sameEventAttr" :key="SameIndex" class="sub-action-row">
                      <div class="sub-action-left" style="padding-left: 80px;">
                        <a-space align="center" size="small" style="width: 100%;">
                          <div class="itemIndexTitle">
                            且
                          </div>
                          <div>
                            <EventIndicatorSelect
                              :disabled="props.disabled"
                              :panel-data="panelData"
                              @analysis-index-change="(event)=>{
                                if(event.type === 'indicator'){
                                  Same.attributeCode = event.indicatorCode;
                                  Same.displayName = event.indicatorDisplayName;
                                  Same.attributeName = event.indicatorName;
                                  Same.attributeType = event.eventType;
                                }else{
                                  Same.attributeCode = event.eventCode;
                                  Same.displayName = event.eventDisplayName;
                                  Same.attributeName = event.eventName;
                                  Same.attributeType = event.eventType;
                                }
                            }"/>
                          </div>
                          <div class="itemIndexTitle">
                            相同
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" size="mini">
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteSameEvent(index, SameIndex)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>

                    <div v-if="item.timeWindow && Object.keys(item.timeWindow).length !== 0" class="sub-action-row">
                      <div class="sub-action-left" style="padding-left: 80px;">
                        <a-space align="center" size="small" style="width: 100%;">
                          <div class="itemIndexTitle">
                            且全部在
                          </div>
                          <div>
                            <DateTimeDropdown @change="(date) => { item.timeWindow.value = date.value;item.timeWindow.unit = date.unit}"/>
                          </div>
                          <div class="itemIndexTitle">
                            内完成
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" size="mini">
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteTimeWindow(index)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>

                    <div v-for="(Same, SameIndex) in item.eventList" :key="SameIndex">
                      <div class="sub-action-row">
                        <div class="sub-action-left" style="padding-left: 80px;">
                          <a-space align="center" size="small" style="width: 100%;">
                            <div class="cluster-condition-indexName">
                              {{ SameIndex + 1 }}
                            </div>
                            <div>
                              <EventIndicatorSelect
                                :disabled="props.disabled"
                                :panel-data="Same" @analysis-index-change="(event)=>{
                                if(event.type === 'indicator'){
                                  Same.indicatorCode = event.indicatorCode;
                                  Same.indicatorDisplayName = event.indicatorDisplayName;
                                  Same.indicatorName = event.indicatorName;
                                  Same.eventType = event.eventType;
                                }else{
                                  Same.eventCode = event.eventCode;
                                  Same.eventDisplayName = event.eventDisplayName;
                                  Same.eventName = event.eventName;
                                  Same.eventType = event.eventType;
                                }
                              }"/>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              ，在
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && !props.disabled" class="itemIndexTitle">
                              <a-trigger
                                v-model:popup-visible="followIndexVisible[SameIndex]"
                                trigger="click"
                                :unmount-on-close="false"
                                position="bl"
                                :update-at-scroll="true"
                                @click="followIndexVisible[SameIndex] = !followIndexVisible[SameIndex];">
                                <div class="filter-btn" style="width: 60px;margin-left:4px; ">
                                  <span class="filter-label">{{ Same.timeWindow.afterIndex }}</span>
                                </div>
                                <template #content>
                                  <div class="filter-select">
                                    <div v-show="SameIndex !== 1" class="filter-select-item" @click="() => { Same.timeWindow.afterIndex = 1; followIndexVisible[SameIndex] = false; }">
                                      <div>
                                        1
                                      </div>
                                    </div>
                                    <div class="filter-select-item" @click="() => { Same.timeWindow.afterIndex = SameIndex; followIndexVisible[SameIndex] = false; }">
                                      <div>
                                        {{ SameIndex }}
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && props.disabled" class="itemIndexTitle">
                              <div class="filter-btn-diabled" style="width: 60px;margin-left:4px; ">
                                <span class="filter-label">{{ Same.timeWindow.afterIndex }}</span>
                              </div>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              后
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              <DateTimeDropdown disabled-pop @change="(date) => { Same.timeWindow.value = date.value;Same.timeWindow.unit = date.unit }"/>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              内
                            </div>
                          </a-space>
                        </div>
                        <div v-if="!props.disabled" class="sub-action-right">
                          <a-space align="center" :size="2">
                            <a-dropdown trigger="hover">
                              <a-button class="btn-bg btn-26" style="margin-left: 8px;">
                                <template #icon>
                                  <icon-more-vertical/>
                                </template>
                              </a-button>
                              <template #content>
                                <a-doption v-show="Same.timeWindow && Object.keys(Same.timeWindow).length == 0&&SameIndex > 0" @click="addSeqTimeWindow(index, SameIndex)">添加时间窗口
                                </a-doption>
                                <a-doption v-show="SameIndex+1 < item.eventList.length" @click="addItemNeverEvent(index, SameIndex)">添加之间没做过
                                </a-doption>
                                <a-doption v-show="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && SameIndex > 0" @click="delSeqTimeWindow(index, SameIndex)">删除时间窗口
                                </a-doption>
                              </template>
                            </a-dropdown>
                            <a-tooltip content="添加事件" position="top">
                              <a-button v-show="item.eventList.length <20" class="btn-bg btn-26" @click="addSeqEvent(index)">
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="最多可添加20个事件" position="top">
                              <a-button v-show="item.eventList.length >= 20" class="btn-bg btn-26" style="margin-left: 8px;" disabled>
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="添加筛选" position="top">
                              <a-button class="btn-bg btn-26" @click="addSeqFilters(SameIndex)">
                                <template #icon>
                                  <icon-filter/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="删除" position="top">
                              <a-button v-show="SameIndex > 1" class="btn-bg-delete btn-26" @click="deleteSeqEvent(index, SameIndex)">
                                <template #icon>
                                  <icon-close-circle/>
                                </template>
                              </a-button>
                            </a-tooltip>
                          </a-space>
                        </div>
                      </div>
                      <div class="sequence-content">
                        <filterComponent :ref="Same => sameFilterRefs[SameIndex] = Same" :filter="Same.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...Same }]" @query-filters-change="sameFiltersChange(index,SameIndex,$event)"/>
                      </div>
                      <div v-show="Same.neverDoneList && Object.keys(Same.neverDoneList).length !== 0" class="relation-editor" style="padding-left: 80px;">
                        <div class="relation-relation">
                          <em class="relation-relation-line"></em>
                        </div>
                        <div class="relation-main">
                          <div style="display: flex;flex-direction: row;" class="sub-action-row">
                            <div class="cluster-condition-indexName">
                              {{ SameIndex + 1 }}
                            </div>
                            <div class="cluster-condition-indexName">
                              {{ SameIndex + 2 }}
                            </div>
                            <div class="itemIndexTitle">
                              之间，没做过
                            </div>
                          </div>
                          <div v-for="(Never, NeverIndex) in Same.neverDoneList.eventList" :key="NeverIndex">
                            <div class="sub-action-row">
                              <div class="sub-action-left">
                                <a-space align="center" size="small" style="width: 100%;">
                                  <div>
                                  <EventIndicatorSelect
                                      :disabled="props.disabled"
                                      :panel-data="panelData" @analysis-index-change="(event)=>{
                                      if(event.type === 'indicator'){
                                        Same.indicatorCode = event.indicatorCode;
                                        Same.displayName = event.indicatorDisplayName;
                                        Same.indicatorName = event.indicatorName;
                                        Same.eventType = event.eventType;
                                      }else{
                                        Same.eventCode = event.eventCode;
                                        Same.displayName = event.eventDisplayName;
                                        Same.eventName = event.eventName;
                                        Same.eventType = event.eventType;
                                      }
                                  }"/>
                                  </div>
                                </a-space>
                              </div>
                              <div v-if="!props.disabled" class="sub-action-right">
                                <a-space align="center" :size="2">
                                  <a-tooltip content="添加事件" position="top">
                                    <a-button v-show="Same.neverDoneList.eventList.length < 5" class="btn-bg btn-26" style="margin-left: 8px;" @click="addNeverEvent(index, SameIndex)">
                                      <template #icon>
                                        <icon-plus/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip position="top">
                                    <template #content>
                                      事件间最多可添加5个之间“没做 <br> 过事件”
                                    </template>
                                    <a-button v-show="Same.neverDoneList.eventList.length >= 5" class="btn-bg btn-26" style="margin-left: 8px;" disabled>
                                      <template #icon>
                                        <icon-plus/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip content="添加筛选" position="top">
                                    <a-button class="btn-bg btn-26" @click="addNeverFilters(NeverIndex)">
                                      <template #icon>
                                        <icon-filter/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip content="删除" position="top">
                                    <a-button class="btn-bg-delete btn-26" @click="delNeverEvent(index, SameIndex, NeverIndex)">
                                      <template #icon>
                                        <icon-close-circle/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                </a-space>
                              </div>
                            </div>
                            <div class="user-content">
                              <filterComponent :ref="Never => neverFiltersRefs[NeverIndex] = Never" :filter="Never.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...Never}]" @query-filters-change="neverFiltersChange(index,SameIndex,NeverIndex,$event)"/>
                            </div>

                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
                </div>
              </div>
              <!-- 用户属性满足 -->
               <div class="user-content">
                  <filterComponent
                    ref="userFilterRef"
                    :filter="gameUserCluster.condition.userCondition"
                    :disabled="props.disabled"
                    :only-user="true"
                    :show-detail-filter="true"
                    @query-filters-change="userFiltersChange"/>
               </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import {debounce} from 'lodash'
import {Message} from "@arco-design/web-vue";
import DatePicker from "@/components/date-picker/index.vue";
import {CalculateSymbol, LogicalOperationType} from "@/api/enum";
import {DateRange, EventQueryFilter} from "@/api/analyse/type";
import EventAttrSelect from "@/views/analyse/components/userFilterComponent/EventAttrSelect.vue";
import DateTimeDropdown from "@/views/analyse/components/userFilterComponent/DateTimeDropdown.vue";
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import filterComponent from "@/views/analyse/components/AttrCategoryFilter.vue"
import {analyseStore} from '@/store';


const analyseData = analyseStore()
// const attrBus = useEventBus('attrList');
const emits = defineEmits(['change'])
const doSelectVisible = ref<boolean[]>([])
const doSelectVisible2 = ref<boolean[]>([])
const symbolSelectVisible = ref<boolean[]>([])
const followIndexVisible = ref<boolean[]>([])
const props = defineProps({
  excludeEvent: {
    type: Boolean,
    default: false
  },
  modelLists:{
      type:Array,
      default:() => []
  },
  // 用户属性筛选本不需要nameList,传入为了便于缓存筛选
  codeList:{
      type:Array,
      default:() => []
  },
  // 回显传参
  userFilter:{
    type:Object,
    default:() => {}
  },
  // 禁用只读
  disabled:{
    type: Boolean,
    default: false
  }
})


const panelData = reactive({
  eventAttrCode: "",
  eventAttrName: "次数",
  eventDisplayName: "",
  eventName: "",
  eventType: "",
  eventCode:'',
  type:''
})
const firstEvent = ref()
watch(() => props.modelLists,() => {
  // console.log(props.modelLists,'props.modelLists');
  if(props.modelLists.length === 0){
    return
  }
  const [firstEventValue] = props.modelLists
  firstEvent.value = firstEventValue
  // console.log(firstEvent.value,'firstEvent.value');
  
  panelData.eventDisplayName = firstEvent.value.displayName
  panelData.eventName = firstEvent.value.name
  panelData.eventType = firstEvent.value.type
  panelData.type = firstEvent.value.objectType
  panelData.eventCode = firstEvent.value.code
},{immediate:true,deep:true})
interface Formatter {
  name: string // 标签值
  desc: string // 说明
  condition: {
    logicalOperation: string // 事件条件与用户条件且/或
    eventIsAnd: string // 事件条件且/或
    singleConditionExpressions: {
      isDone: boolean, // true 做过 false 未做
      eventName: string, // 事件名称
      eventCode: string, // 事件编码
      eventType: string, // 事件类型
      eventDisplayName: string,
      eventAttrName: string, // 事件属性名称
      eventAttrCode: string, // 事件属性编码
      eventAttrDisplayName: string, // 事件属性展示名称
      logicalOperation: string
      filter: EventQueryFilter,
      calcuSymbol: CalculateSymbol, // 计算符号
      dateRange: DateRange, // 时间范围
      thresholds: (string | number)[], // 阈值数组
      statisticalType: (string | undefined), // 统计类型
      objectType:(string | undefined), // 属性类型
    }[]
    sequenceConditionExpressions: {
      isDone: boolean, // true 做过 false 未做
      logicalOperation: string
      eventList: {
        eventName: string, // 事件名称
        eventCode: string, // 事件编码
        eventType: string, // 事件类型
        eventDisplayName: string,
        timeWindow: { // 时间窗口
          afterIndex: (string | number), // 跟随index
          value: number // 时间值
          unit: string // 时间单位
        },
        neverDoneList: { // 之前未做过的事件列表
          afterIndex: (string | number), // 跟随index
          eventList: {
            eventName: string, // 事件名称
            eventCode: string, // 事件编码
            eventType: string, // 事件类型
            eventDisplayName: string,
            filter: EventQueryFilter
          }[]
        },
        filter: EventQueryFilter,
      }[]
      filter: EventQueryFilter,
      calcuSymbol: CalculateSymbol, // 计算符号
      dateRange: DateRange, // 时间范围
      timeWindow: { // 时间窗口
        value: number // 时间值
        unit: string // 时间单位
      },
      sameEventAttr: { // 关联相同属性
        attributeName: string, // 属性名称
        attributeCode: string, // 属性编码
        attributeType: string, // 事件类型
        displayName: string, // 展示名称
      }[]
    }[]
    userCondition: EventQueryFilter
  }
}

const gameUserCluster = ref<Formatter>({
  name: "示例标签",
  desc: "这是一个示例说明",
  condition: {
    logicalOperation: 'and', // 事件条件与用户条件且/或
    eventIsAnd: 'and', // 事件条件且/或
    singleConditionExpressions: [],
    sequenceConditionExpressions: [],
    userCondition: {logicalOperation: LogicalOperationType.AND, filters: []}
  }
})

interface params {
  logicalOperation: string,
  filters: { objectName: string, objectType: string, objectId: string, filterType: string }[],
  subFilters: {
    logicalOperation: string,
    filters: { objectName: string, objectType: string, objectId: string, filterType: string }[]
  }[]
}

const eventQueryfilter = reactive<params>(
    {
      logicalOperation: 'and',
      filters: [],
      subFilters: []
    }
)

const transformSymbol = (symbol: string) => {
  switch (symbol) {
    case CalculateSymbol.EQUAL:
      return '等于'
    case CalculateSymbol.NOT_EQUAL:
      return '不等于'
    case CalculateSymbol.GREATER_THAN:
      return '大于'
    case CalculateSymbol.LESS_THAN:
      return '小于'
    case CalculateSymbol.GREATER_THAN_OR_EQUAL:
      return '大于等于'
    case CalculateSymbol.LESS_THAN_OR_EQUAL:
      return '小于等于'
    case CalculateSymbol.SCOPE:
      return '区间'
    default:
      return '自定义'
  }
}

const logicalChange = () => {
  if(!props.disabled){
    const newValue = gameUserCluster.value.condition.logicalOperation === 'and' ? 'or' : 'and';
    gameUserCluster.value.condition.logicalOperation = newValue;
    eventQueryfilter.logicalOperation = newValue;
  }
}

const add = () => {
  gameUserCluster.value.condition.singleConditionExpressions.push({
    isDone: true,
    eventName: firstEvent.value.name, // 事件名称
    eventCode: firstEvent.value.code, // 事件编码
    eventType: firstEvent.value.type, // 事件类型
    eventDisplayName: firstEvent.value.displayName,
    eventAttrName: '', // 事件属性名称
    eventAttrCode: '', // 事件属性编码
    eventAttrDisplayName: '次数', // 事件属性展示名称
    logicalOperation: 'and',
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []},
    calcuSymbol: CalculateSymbol.GREATER_THAN, // 计算符号
    dateRange: {
      dateText: '最近7天',
      recentStartDate: 6,
      recentEndDate: 0
    }, // 时间范围
    thresholds: [0, 0] // 阈值数组
  } as never)
}

const firsetThresholdChange = (index, value: number) => {
  gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1] = value;
}
// 处理阈值变化
const handleThresholdChange = (index: number, value: number) => {
  const num1 = gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[0]
  const num2 = gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1]
  if ( num1 > num2) {
    gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[0] = value
    gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1] = value
  }
}
const addDone = () => {
  if(gameUserCluster.value.condition.sequenceConditionExpressions.length>4){
    Message.info('最多添加5个')
    return
  }

  gameUserCluster.value.condition.sequenceConditionExpressions.push({
    isDone: true, // 依次做过
    eventName: "",
    eventCode: "",
    eventType: "",
    logicalOperation: 'and',
    eventList: [{
      eventName: firstEvent.value.name, // 事件名称
      eventCode: firstEvent.value.code, // 事件编码
      eventType: firstEvent.value.type, // 事件类型
      eventDisplayName: firstEvent.value.displayName,
      timeWindow: {},
      neverDoneList: [],
      filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
    }, {
      eventName: firstEvent.value.name, // 事件名称
      eventCode: firstEvent.value.code, // 事件编码
      eventType: firstEvent.value.type, // 事件类型
      eventDisplayName: firstEvent.value.displayName,
      timeWindow: {},
      neverDoneList: [],
      filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
    }],
    sameEventAttr: [],
    timeWindow: {},
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []},
    calcuSymbol: CalculateSymbol.GREATER_THAN,
    dateRange: {
      dateText: '最近7天',
      recentStartDate: 6,
      recentEndDate: 0
    }, // 时间范围
  } as never)
  // console.log(doneList.value, 'doneList.value');
}

// 添加并列条件
const queryFilterRefs = ref<any>([])
const addFilters = (index: number) => {
    // console.log(queryFilterRefs.value,'bbbbbb');
    queryFilterRefs.value[index].add()
}
const queryFiltersChange = (index:number,v) => {
  gameUserCluster.value.condition.singleConditionExpressions[index].filter = v;
  // console.log(v,'queryFiltersChange///////////');
}
const userFilterRef = ref()
const userLists = ref<any>([])
const addUser = async () => {
  // if(!analyseData.userLists.length){
  //   userLists.value = await analyseData.fetchUserInfo()
  // }
  // console.log(analyseData.userLists,'analyseData.userLists--index');
  userFilterRef.value.add()
}
// 提供用户属性满足组件回显数据
const userConditionData = ref()
const userFiltersChange = (v) => {
  gameUserCluster.value.condition.userCondition = v;
  // console.log(v,'userFiltersChange///////////');
}
const sameFilterRefs = ref<any>([])
const sameFiltersChange = (index:number,eventIndex:number,v) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].filter = v
}

const neverFiltersRefs  = ref<any>([])
const neverFiltersChange = (index:number,eventIndex:number,neverIndex:number,v) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList.eventList[neverIndex].filter = v
}

const getTotalConditionCount = computed(() => {
  const { singleConditionExpressions, sequenceConditionExpressions, userCondition } = gameUserCluster.value.condition;
  const conditions = [
    singleConditionExpressions.length > 0,
    sequenceConditionExpressions.length > 0,
    (userCondition?.filters?.length || 0) > 0
  ];
  // 计算有多少个条件组存在数据
  return conditions.filter(Boolean).length >= 2;
});
const addSeqEvent = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.push({
    eventName: firstEvent.value.name, // 事件名称
    eventCode: firstEvent.value.code, // 事件编码
    eventType: firstEvent.value.type, // 事件类型
    eventDisplayName: firstEvent.value.displayName,
    timeWindow: {},
    neverDoneList: [],
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
  })
}

const addSeqFilters = (eventIndex: number) => {
  sameFilterRefs.value[eventIndex].add()
}

const deleteSeqEvent = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.splice(eventIndex, 1)
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.forEach((item: any) => {
    if (item.timeWindow.afterIndex > eventIndex) {
      item.timeWindow.afterIndex -= 1
    }
  })
}

const deleteFilters = (index: number) => {
  gameUserCluster.value.condition.singleConditionExpressions.splice(index, 1)
}

const deleteDone = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions.splice(index, 1)
}

const addItemSameEvent = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].sameEventAttr.push({
    attributeName: '', // 属性名称
    attributeCode: '', // 属性编码
    attributeType: '', // 事件类型
    displayName: '', // 展示名称
  })
}
const addTimeWindow = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].timeWindow = {
    value: 1, // 时间值
    unit: 'day' // 时间单位
  }
}

const addSeqTimeWindow = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].timeWindow = {
    value: 1, // 时间值
    unit: 'day', // 时间单位
    afterIndex: eventIndex
  }
}
const addItemNeverEvent = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList = {
    afterIndex: eventIndex, // 跟随index
    eventList: [{
      eventName: '', // 事件名称
      eventCode: '', // 事件编码
      eventType: '', // 事件类型
      filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
    }]
  }
}

const addNeverEvent = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList.eventList.push({
    eventName: '', // 事件名称
    eventCode: '', // 事件编码
    eventType: '', // 事件类型
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
  })
}

const addNeverFilters = (neverIndex: number) => {
  neverFiltersRefs.value[neverIndex].add()
}

// 删除之间未做过事件列表
const delNeverEvent = (index: number, eventIndex: number, NeverIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList.eventList.splice(NeverIndex, 1)
  if (gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList.eventList.length === 0) {
    gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].neverDoneList = {}
  }
}
// 删除时间窗口
const delSeqTimeWindow = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].timeWindow = {}
}
// 删除相同事件
const deleteSameEvent = (index: number, num: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].sameEventAttr.splice(num, 1)
}
// 删除时间窗口
const deleteTimeWindow = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].timeWindow = {}
}


// 导入已有条件
const originFilters = (v: any, b?: any) => {
  gameUserCluster.value.condition = v
}

// 创建一个防抖的emit函数
const debouncedEmit = debounce((value) => {
  const filterParams = {
    logicalOperation: value.logicalOperation,
    eventCondition: {
      logicalOperation: value.eventIsAnd,
      singleConditionExpressions: value.singleConditionExpressions.map(item => ({
        ...item,
        thresholds: item.calcuSymbol !== 'scope' ? [item.thresholds[0]] : item.thresholds
      })),
      sequenceConditionExpressions: value.sequenceConditionExpressions,
    },
    userCondition: value.userCondition,
  }
  emits('change', filterParams)
}, 500)

// 修改 watch
watch(() => gameUserCluster.value.condition, (newValue) => {
  debouncedEmit(newValue)
}, { deep: true })
// 监听props.userFilter回显
watch(() => props.userFilter, (newValue) => {
  // console.log(props.userFilter,'newValue');
  if (props.userFilter && Object.keys(props.userFilter)?.length) {
    gameUserCluster.value.condition = {
      logicalOperation:  props.userFilter?.logicalOperation || 'and',
      eventIsAnd:  props.userFilter.eventCondition?.logicalOperation || 'and',
      singleConditionExpressions: props.userFilter.eventCondition?.singleConditionExpressions.map(item => ({
        ...item,
        thresholds: item.calcuSymbol!=='scope'? [item.thresholds[0],item.thresholds[0]] : item.thresholds
      })) || [],
      sequenceConditionExpressions:props.userFilter?.sequenceConditionExpressions || [],
      userCondition:  props.userFilter?.userCondition || {logicalOperation: LogicalOperationType.AND, filters: []},
    };
    // userConditionData.value = props.userFilter?.userCondition || {logicalOperation: LogicalOperationType.AND, filters: []};
  }
}, { immediate: true, deep: true })

// 显示统一筛选时间
const showUnifiedCycle = ref(false)
const unifiedDateRange = ref<DateRange>({
  dateText: '最近7天',
  recentStartDate: 6,
  recentEndDate: 0
})
// 控制显示统一时间，不改变原数据
const changeShowCycle = (flag) => {
  showUnifiedCycle.value = flag
}
const handleUnifiedCycle = (date:any) => {
  unifiedDateRange.value = date
}
// 改变原数据dateRange
// const changeDateRange = (date:any) => {
//   gameUserCluster.value.condition.singleConditionExpressions.forEach(item => {
//     item.dateRange.dateText = date.dateText
//     item.dateRange.startDate = date.startDate
//     item.dateRange.endDate = date.endDate
//   })
//   gameUserCluster.value.condition.sequenceConditionExpressions.forEach(item => {
//     item.dateRange.dateText = date.dateText
//     item.dateRange.startDate = date.startDate
//     item.dateRange.endDate = date.endDate
//   })
// }
defineExpose({
  add, originFilters, addDone, addUser,handleUnifiedCycle,changeShowCycle
})
</script>

<style scoped lang="less">
:deep(.arco-space-item){
  margin-top: 2px !important;
  margin-bottom: 2px !important;
  // margin: 4px 0!important;
}
.user-content{
  :deep(.action-row){
    padding-left: 0!important;
  }
}
.sequence-content{
  :deep(.action-row){
    padding-left: 80px!important;
  }
}
.filter-btn, .filter-btn-disabled {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;
  margin: 2px 0;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  .un-filter-label {
    color: var(--tant-text-gray-color-text1-4);
  }

  .virtual-sign {
    color: var(--tant-primary-color-primary-default);
  }
}

.filter-btn {
  cursor: pointer;

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.filter-input {
  all: unset;
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 100px;
  height: 26px;
  padding: 0 8px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;

  &:focus {
    border-color: var(--tant-primary-color-primary-default);
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {
    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 110px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);
        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }
        :deep(.select-btn) {
          background: #fff;
        }
        .action-right {
          opacity: 1;
        }
      }
    }

    .row-filters {
      // padding-left: 38px;
      .relation-editor {
        box-sizing: border-box;
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;

        .relation-relation {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          width: 24px;
          margin-right: 8px;
          flex-shrink: 0;

          .relation-relation-line,.relation-relation-disabled-line {
            position: absolute;
            left: 12px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
            top: 6px;
            height: calc(100% - 12px);
          }
          
          .relation-relation-value,.relation-relation-disabled-value{
            position: absolute;
            text-transform: uppercase;
            top: 50%;
            left: 50%;
            transform: translate(-50%);
            height: 24px;
            padding: 0 5px;
            margin-top: -12px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            line-height: 22px;
            text-align: center;
            background-color: #fff;
            border: 1px solid var(--tant-border-color-border1-2);
            border-radius: 4px;
            word-break: keep-all;
            transition: all .3s;
          }
          .relation-relation-value{
              cursor: pointer;
          }
          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }
        }

        .relation-main {
          flex: 1 1;

          .relation-row {
            box-sizing: border-box;

            .multi-filter-condition {
              .sub-action-row {
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;

                .sub-action-left {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  line-height: 27px;
                }

                .sub-action-right {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  display: flex;
                  align-items: center;
                  opacity: 0;
                  transition: opacity .3s;
                  line-height: 27px;
                }

                &:hover .sub-action-right {
                  opacity: 1;
                }
              }

              &:hover {
                // background-color: #f6f6f9;
              }
            }
          }
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.filter-select {
  width: 200px;
  min-width: 46px;
  left: 0px;
  top: 5px;
  max-width: 240px;
  background: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  box-shadow: var(--tant-small-shadow-small-overall);
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, .85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: "tnum", "tnum";
  position: absolute;
  z-index: 1050;
  display: block;
  height: auto;
}

.filter-select-item {
  position: relative;
  width: 100%;
  height: 42px;
  padding: 4px 4px 0;
  cursor: default;

  div {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    border-radius: 4px;

    &:hover {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }
  }

}

.cluster-condition-indexName {
  background-color: var(--tant-disabled-color-disabled-fill);
  border-radius: 4px;
  width: 24px;
  height: 24px;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-description-font-description-medium);
  line-height: 24px;
  text-align: center;
}

.itemIndexTitle {
  color: var(--tant-text-gray-color-text1-3);
  line-height: 27px;

}
</style>