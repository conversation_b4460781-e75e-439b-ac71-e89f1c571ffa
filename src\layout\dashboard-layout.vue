<template>
  <div>
    <a-layout class="layout" :class="{ mobile: appStore.hideMenu }">
      <div class="layout-navbar">
        <NavBar/>
      </div>
      <a-layout>
        <a-layout-sider
            v-if="renderMenu"
            class="layout-sider"
            :collapsed="collapsed"
            :collapsible="true"
            :width="menuWidth"
            :style="{ paddingTop: navbar ? '48px' : '' }"
            @collapse="setCollapsed"
        >
          <!--收起-->
          <div v-show="collapsed" class="layout-sider-icon">
            <a-button class="layout-sider-search" @click="closeCollapsed">
              <div>
                <icon-search size="15"/>
              </div>
            </a-button>
            <a-dropdown
                position="bl"
                trigger="hover"
                @select="(value) => addEvent(value)"
            >
              <a-button>
                <template #icon>
                  <icon-plus size="15"/>
                </template>
              </a-button>
              <template #content>
                <a-doption value="addDashboard">新建看板</a-doption>
                <a-doption value="addFolder">新建文件夹</a-doption>
                <a-doption value="addSpace">新建空间</a-doption>
                <a-divider :margin="2"/>
                <a-doption>导入看板</a-doption>
                <a-doption>导出看板</a-doption>
              </template>
            </a-dropdown>
          </div>
          <!--展开菜单-->
          <div v-show="!collapsed" class="menu-wrapper">
            <treeMenu ref="treeMenuRef" @handleCancel="sidebar"/>
          </div>
          <template #trigger="{ collapsed }">
            <a-tooltip mini v-if="collapsed" content="点击展开">
              <icon-double-right/>
            </a-tooltip>
            <div v-else style="margin-left: 250px">
              <a-tooltip mini content="点击收起">
                <icon-double-left/>
              </a-tooltip>
            </div>
          </template>
        </a-layout-sider>
        <a-drawer
            :visible="drawerVisible"
            placement="left"
            :footer="false"
            mask-closable
            :closable="false"
            @cancel="drawerCancel"
        >
          <Menu/>
        </a-drawer>
        <a-layout v-show="!treeMenuRef?.loading" class="layout-content" :style="paddingStyle">
          <TabBar v-if="appStore.tabBar"/>
          <a-layout-content>
            <PageLayout/>
          </a-layout-content>
        </a-layout>
      </a-layout>

    </a-layout>
    <div>
      <add-space v-model:visible="showAddSpace"/>
      <add-folder
          v-model:visible="showAddFolder"
          :my-space="mySpace"
          :project-spaces="projectSpaces"
          :add-in-space="addInSpace"
      />
      <add-dashboard
          v-model:visible="showAddDashboard"
          :my-space="mySpace"
          :project-spaces="projectSpaces"
          :add-in-space="addInSpace"
          :add-in-folder="addInFolder"
      />
    </div>
  </div>

</template>

<script lang="ts" setup>
import {computed, onMounted, provide, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {SpaceDto} from '@/api/space/type';

import {useAppStore, useDashboardStore, useUserStore} from '@/store';
import AddSpace from '@/components/tree-menu/components/modal/add/add-space.vue';
import AddFolder from '@/components/tree-menu/components/modal/add/add-folder.vue';
import AddDashboard from '@/components/tree-menu/components/modal/add/add-dashboard.vue';
import NavBar from '@/components/navbar/index.vue';
import treeMenu from '@/components/tree-menu/index.vue';
import TabBar from '@/components/tab-bar/index.vue';
import usePermission from '@/hooks/permission';
import useResponsive from '@/hooks/responsive';
import PageLayout from './page-layout.vue';
import {ROUTE_NAME} from "@/router/constants";

/**
 * 我的看板
 */
const mySpace = ref<SpaceDto>();
/**
 * 项目空间
 */
const treeMenuRef = ref(null);
const projectSpaces = ref<SpaceDto[]>();
const showAddSpace = ref<boolean>(false);
const showAddFolder = ref<boolean>(false);
const showAddDashboard = ref<boolean>(false);
const addInFolder = ref<string>();
const addInSpace = ref<string>();
const isInit = ref(false);
const appStore = useAppStore();
const userStore = useUserStore();
const dashboardStore = useDashboardStore()
const router = useRouter();
const route = useRoute();
const permission = usePermission();
useResponsive(true);
const collapsed = ref(false)
const drawerVisible = ref(false);
const navbarHeight = `48px`;
const navbar = computed(() => appStore.navbar);
const renderMenu = computed(() => appStore.menu && !appStore.topMenu);
// const hideMenu = computed(() => appStore.hideMenu);
const footer = computed(() => appStore.footer);
const menuWidth = computed(() => {
  return collapsed.value ? 48 : 286;
});

const paddingStyle = computed(() => {
  const paddingLeft =
      !collapsed.value
          ? {marginLeft: `286px`}
          : {marginLeft: '48px'};
  const paddingTop = navbar.value ? {paddingTop: navbarHeight} : {};
  return {...paddingLeft, ...paddingTop};
});
// watch([renderMenu,hideMenu],(newVal1,newVal2)=>{
//   console.log(newVal1,newVal2)
// })
const setCollapsed = (val: boolean) => {
  collapsed.value = val
}
const closeCollapsed = () => {
  setCollapsed(!collapsed.value);
  treeMenuRef.value?.focusInput();
}

function addEvent(addType: string, spaceId?: string, folderId?: string) {
  switch (addType) {
    case 'addDashboard':
      showAddDashboard.value = true;
      addInSpace.value = spaceId;
      addInFolder.value = folderId;
      break;
    case 'addFolder':
      showAddFolder.value = true;
      addInSpace.value = spaceId;
      addInFolder.value = undefined;
      break;
    case 'addSpace':
      showAddSpace.value = true;
      break;
    default:
  }
}

watch(
    () => userStore.role,
    (roleValue) => {
      if (roleValue && !permission.accessRouter(route))
        router.push({name: ROUTE_NAME.NOT_FOUND});
    }
);
const drawerCancel = () => {
  drawerVisible.value = false;
};

provide('toggleDrawerMenu', () => {
  drawerVisible.value = !drawerVisible.value;
});
onMounted(() => {
  isInit.value = true;
});
</script>

<style scoped lang="less">

.layout {
  width: 100%;
  height: 100%;
}

.layout-navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: var(--nav-height);
}

.layout-sider {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  height: 100%;
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    display: block;
    width: 1px;
    height: 100%;
    background-color: var(--color-border);
    content: '';
  }

  > :deep(.arco-layout-sider-children) {
    overflow-y: hidden;
  }

  .layout-sider-icon {
    padding-top: 20px;
    padding-left: 8px;
    height: 100%;
    border-bottom: 1px solid var(--color-neutral-3);

    .layout-sider-search {
      background-color: var(--tant-bg-white-color-bg1-1);
      display: flex;
      justify-content: center;
      align-items: center;
      width: 32px;
      height: 32px;
      margin-bottom: 10px;
    }

    .layout-sider-search:hover {
      color: var(--color-text-2);
      background-color: var(--color-secondary-hover);
      border-color: transparent;
    }
  }
}

.menu-wrapper {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  border-bottom: 1px solid var(--color-neutral-3);

  :deep(.arco-menu) {
    ::-webkit-scrollbar {
      width: 12px;
      height: 4px;
    }

    ::-webkit-scrollbar-thumb {
      border: 4px solid transparent;
      background-clip: padding-box;
      border-radius: 7px;
      background-color: var(--color-text-4);
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-text-3);
    }
  }


}

.layout-content {
  min-height: 100vh;
  overflow-y: hidden;
  background-color: var(--color-fill-2);
  transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.spin {
  margin-top: 300px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .spin-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .spin-tip {
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--tant-slate-slate-50);
    }
  }

}
</style>
