<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {getDays} from "@/utils/dateUtil";
import _ from "lodash";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 新增
   */
  newUserCount: number[]

  /**
   * 活跃
   */
  activeUserCount: number[]
}

const props = defineProps<Props>()

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '36',
      top: '10',
      // bottom: '24',
      bottom: '48'
    },
    legend: {
      data: ['新增', '活跃'],
      bottom: '0',
      type: 'scroll'
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      className: 'echarts-tooltip-diy',
      extraCssText: 'max-height: 400px; overflow-y: auto;',
    },
    xAxis: {
      type: 'category',
      data: getDays(props.date[0], props.date[1]),
    },
    yAxis: [{
      type: 'value',
      name: '活跃',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`; // 转换为 k 单位
          }
          if (value >= 1000) {
            return `${value / 1000}k`; // 转换为 k 单位
          }
          return value;
        }
      }
    }, {
      type: 'value',
      name: '新增',
      max: (_.max(props.newUserCount) || 0) * 2,
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`; // 转换为 k 单位
          }
          if (value >= 1000) {
            return `${value / 1000}k`; // 转换为 k 单位
          }
          return value;
        }
      }
    }],
    series: [
      {
        name: '活跃',
        yAxisIndex: 0,
        type: 'bar',
        data: props.activeUserCount,
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter(params) {
            if (!params.value || typeof params.value !== 'number') {
              return params.value;
            }
            if (params.value >= 1000000) {
              return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
            }
            if (params.value >= 1000) {
              return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
            }
            return Math.round(params.value); // 保留整数
          }
        }
      },
      {
        name: '新增',
        type: 'line',
        color: '#91cc75',
        yAxisIndex: 1,
        data: props.newUserCount,
        label: {
          show: true,
          position: 'top',
          formatter(params) {
            if (!params.value || typeof params.value !== 'number') {
              return params.value;
            }
            if (params.value >= 1000000) {
              return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
            }
            if (params.value >= 1000) {
              return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
            }
            return Math.round(params.value); // 保留整数
          }
        }
      }
    ]
  };
});


</script>

<template>
  <Chart :option="chartOption"/>
</template>

<style scoped lang="less">

</style>