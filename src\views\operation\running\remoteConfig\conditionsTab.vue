<template>
    <div class="conditions-content">
        <div class="params-content">
            <div class="params-title"></div>
            <div class="config-content">
                <a-button class="button" type="primary" style="margin-right: 30px" @click="handleClick">
                    <icon-plus style="margin-right: 3px"/>
                    添加条件
                </a-button>
                <a-button class="button" type="primary" @click="openGroup">
                    <icon-folder-delete style="margin-right: 3px"/>
                    创建新群组
                </a-button>
            </div>
        </div>
        <div class="action-content">
            <div class="filter">
            </div>
            <div class="search">
                <a-input v-model:model-value="conditionText" style="width: 300px;background-color: #FFF;" placeholder="请输入...">
                    <template #prefix>
                        <icon-search/>
                    </template>
                </a-input>
            </div>
        </div>
        <div class="card-content">
            <draggable :list="filteredList" handle=".drag-handle">
                <template #item="{ element,index }">
                    <div class="condition-item" :style="{background: element.color}">
                        <div class="condition-content">
                            <div class="reorder-controls">
                                <div class="drag-handle">
                                    <icon-drag-dot-vertical size="24"/>
                                </div>
                            </div>
                            <div class="condition-title">
                                <h2>{{ element.name }}</h2>
                            </div>
                            <div class="condition-summary">
                                <div v-for="el in element.summaryList" :key="el.code" class="summary-row">
                                    <div class="summary-title">{{ el.summaryTitle }}</div>
                                    <div class="summary-body">
                                        <span>{{ el.summaryDesc }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="entity-footer">
                            <div class="modified">
                                上次发布：
                                <span v-if="element.creator.email" class="version-info">{{ element.creator.email }},</span>
                                <span class="version-info">{{ dayjs(element.createTime).format('YYYY年M月DD日') }}</span>
                            </div>
                            <div class="affected-parameters">
                                <!-- 影响参数 -->
                                <span class="parameter-name">{{ element.note }}</span>
                            </div>
                        </div>
                        <div class="controls">
                            <div class="btn" @click="editCondition(element,'edit')">
                                <icon-pen size="24"/>
                            </div>
                            <a-dropdown >
                                <div class="btn">
                                    <icon-more-vertical size="24"/>
                                </div>
                                <template #content>
                                    <a-doption value="edit" @click="editCondition(element,'copy')">复制</a-doption>
                                    <a-doption value="delete" @click.stop="deleteItem(element)">删除</a-doption>
                                </template>
                            </a-dropdown>
                        </div>
                    </div>
                </template>
            </draggable>
            <a-empty v-if="!filteredList.length"/>
        </div>

        <createGroupModal ref="groupModalRef"/>
        <createConditionModal ref="conditionModalRef" :condition-list="conditionList" @condition-change="conditionChange"/>
    </div>
</template>

<script setup lang="ts">
import {ref,watch,computed} from "vue";
import draggable from 'vuedraggable'
import {getConditionList,deleteConditionItem} from "@/api/marketing/api"
import dayjs from "dayjs";
import {Message} from '@arco-design/web-vue';
import createGroupModal from "./components/createGroupModal.vue";
import createConditionModal from "./components/createConditionModal.vue";

const props = defineProps({
    appId:{
        type:String,
        default:''
    }
})
const conditionText = ref('')
const conditionModalRef = ref()
const conditionList = ref([
    // {
    //     conditionName:'2000-Variant A',
    //     color:'rgba(67, 160, 71, .24)',
    //     summaryList: [
    //         {
    //             code:'1',
    //             summaryTitle:'应用',
    //             summaryDesc:'com.blockpuzzle.comboblastmaster.freegame'
    //         }
    //     ],
    //     email:'<EMAIL>',
    //     releaseDate:'2024年1月22日',
    //     parameterName:'isUse2000SimpleMode'
    // },
    // {
    //     conditionName:'2000-Variant B',
    //     color:'rgba(57, 73, 171, .24)',
    //     summaryList: [
    //         {
    //             code:'2',
    //             summaryTitle:'应用1',
    //             summaryDesc:'com.blockpuzzle'
    //         }
    //     ],
    //     email:'<EMAIL>',
    //     releaseDate:'2024年1月22日',
    //     parameterName:'isUse2000SimpleMode'
    // }
])
const handleClick = () => {
    conditionModalRef.value.openModal()
}
const groupModalRef = ref()
const openGroup = () => {
    groupModalRef.value.openModal()
}

const editCondition = (item,type) => {
    conditionModalRef.value.openModal(item,type)
}
// 获取条件列表
const getTabList = async () => {
  const data = {
    appId:props.appId,
    conditionScope:'app_specific'
  }
  await getConditionList(data).then(res => {
    conditionList.value = res.items || []
    // console.log(conditionList.value,'conditionList.value');

  })
}
const conditionChange = (v) => {
    getTabList()
}
const deleteItem = (item) => {
  const data = {
    appId:props.appId,
    // paramName:'default_json',
    conditionCode:item.code,
  }
  deleteConditionItem(data).then(res => {
    if(res){
      Message.success('删除成功')
      getTabList()
    }
  })
}
const filteredList = computed(() => {
  const str = conditionText.value.trim();
  if(conditionList.value.length>0){
    return conditionList.value.filter(item => item?.name.includes(str));
  }
  return []
});
watch(() => props.appId,(newValue,oldValue) => {
    if(props.appId){
        getTabList()
    }
},{deep:true})
defineExpose({
    getTabList
})
</script>

<style scoped lang="less">

.params-content{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.config-content {
  display: flex;
  justify-content: flex-end;
  .button {
    border: none;
    border-radius: 5px;
  }
}
.action-content{
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 56px;
  padding: 0 8px 0 16px;
  border-radius: 8px 8px 0 0;
  border: 1px solid #f7f6f6;
  border-bottom: none;
}
.card-content{
    border: 1px solid #f7f6f6;
    border-top: none;
    width: 100%;
    height: calc(100vh - 300px);
    overflow-y: auto;
    padding: 24px;
    margin-bottom: 24px;
    .condition-item{
        border-radius: 2px;
        margin-bottom: 24px;
        position: relative;
        .condition-content{
            display: flex;
            align-items: center;
            padding: 16px;
            .drag-handle{
                cursor: grab;
            }
            .condition-title{
                flex: 1;
                margin-left: 16px;
                margin-right: 16px;
                max-width: 30%;
            }
            .condition-summary{
                display: flex;
                flex: 1;
                margin-right: 80px;
                .summary-row{
                    display: flex;
                    .summary-title{
                        flex-shrink: 0;
                        padding-right: 16px;
                        text-align: right;
                        width: 160px;
                    }
                    .summary-body{
                        word-break: break-word;
                    }
                }
            }
        }
        .entity-footer{
            display: flex;
            font-size: 13px;
            justify-content: space-between;
            line-height: 16px;
            padding: 8px;
            background-color: rgba(0, 0, 0, 0.1);
            .modified{
                max-width: 50%;
                .version-info{
                    padding-left: 4px;
                }
            }
            .affected-parameters {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 50%;
                .parameter-name{
                    white-space: nowrap;
                }
            }
        }
        .controls{
            position: absolute;
            right: 8px;
            top: 8px;
            opacity: 0;
            display: flex;
            align-items: center;
            .btn{
                width: 42px;
                height: 42px;
                flex-shrink: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 100%;
                cursor: pointer;
                &:hover{
                    background-color: rgba(0, 0, 0, 0.1);
                }
            }
        }
        &:hover{
            .controls{
                opacity: 1;
            }
        }
    }
}
</style>
