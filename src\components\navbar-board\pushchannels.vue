<template>
  <div class="pushChannel">
    <div>
      <span class="ico">*</span>
      <span class="boardPush-span">推送渠道</span>
    </div>
    <div class="pushConfig">
      <div v-for="(config, configIndex) in pushConfigs" :key="config.channelType" class="pushConfigSelected">
        <div class="PushConfigInput">
          <div class="selectCss">
            <a-select v-model="config.channelType" :style="{width:'140px'}">
              <a-option v-for="channelType in channelTypeOptions" :key="channelType" :value="channelType"
                        :disabled="_.includes(alreadySelectedChannel, channelType)">
                {{ channelType }}
              </a-option>
            </a-select>
          </div>
          <div class="inputCss">
            <div v-if="config.channelType === PushChannelType.MAIL">
              <div class="inputWrapper">
                <a-input :value="config.config.email" disabled placeholder="你还没有在TE系统绑定邮箱"/>
                <div class="actionButtons">
                  <button class="-button-css"><span class="font-span">去绑定</span></button>
                  <a-tooltip content="删除" position="top" mini>
                    <button class="-button-css" @click="removeInput(configIndex, inputIndex)">
                      <icon-minus-circle/>
                    </button>
                  </a-tooltip>
                </div>
              </div>
            </div>
            <div v-else>
              <div v-for="(wxUrl, inputIndex) in config.config.wxUrls" :key="inputIndex" class="inputWrapper">
                <input
                    :value="wxUrl"
                    placeholder="请输入Webhook地址"
                    class="a-input"
                    @input="event => wxUrlInput(event, inputIndex, config.channelType)"/>
                <div class="actionButtons">
                  <div v-if="inputIndex === 0">
                    <a-tooltip content="配置" position="top" mini>
                      <button class="-button-css" @click="skipWeb(config.channelType)">
                        <img src="/icon/config.svg"/>
                      </button>
                    </a-tooltip>
                  </div>
                  <a-tooltip content="添加" position="top" mini>
                    <button class="-button-css" @click="addInput(configIndex)">
                      <icon-plus-circle/>
                    </button>
                  </a-tooltip>
                  <a-tooltip content="删除" position="top" mini>
                    <button class="-button-css" @click="removeInput(configIndex, inputIndex)">
                      <icon-minus-circle/>
                    </button>
                  </a-tooltip>
                </div>
              </div>
            </div>
            <div v-if="config.channelType === PushChannelType.FEISHU" class="feishuInput">
              <a-input :value="config.config.appid" placeholder="APP ID"/>
              <a-input :value="config.config.appSecret" placeholder="APP SECRET"/>
            </div>
          </div>
        </div>
      </div>
      <div>
        <a-button type="text" class="boardPush-pushway" :disabled="disAddPushChannel" @click="addPushChannel">
          + 推送渠道
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import _ from "lodash";
import {computed, ref} from 'vue';

interface PushConfig {
  channelType: PushChannelType,
  config: {
    appid?: string,
    appSecret?: string,
    email?: string,
    wxUrls?: Array<string>,
  }
}

enum PushChannelType {
  MAIL = "邮件",
  WECHAT = "企业微信",
  DINGDING = "钉钉",
  FEISHU = "飞书",
  SLACK = "slack"
}

const disAddPushChannel = ref<boolean>(false);
const channelNumbers = ref<number>(1);
const channelTypeOptions = ref<PushChannelType[]>([
  PushChannelType.MAIL, PushChannelType.WECHAT, PushChannelType.DINGDING, PushChannelType.FEISHU, PushChannelType.SLACK
]);

// push
const pushConfigs = ref<PushConfig[]>([{
  channelType: channelTypeOptions.value[0],
  config: {
    wxUrls: [''],
    appid: '',
    appSecret: '',
    email: '',
  }
}]);

const alreadySelectedChannel = computed(() => {
  return pushConfigs.value?.map(config => config.channelType);
})

const addPushChannel = () => {
  if (channelNumbers.value < 5) {
    pushConfigs.value.push({
      channelType: channelTypeOptions.value.filter(item => !alreadySelectedChannel.value?.includes(item))[0],
      config: {
        wxUrls: [''],
        appid: '',
        appSecret: '',
        email: '',
      }
    })
    channelNumbers.value += 1;
  }
  if (channelNumbers.value === 5) {
    disAddPushChannel.value = true;
  }
}

const removePushChannel = (index) => {
  if (pushConfigs.value.length > 1) {
    pushConfigs.value.splice(index, 1);
    channelNumbers.value -= 1;
    disAddPushChannel.value = false;
  } else {
    disAddPushChannel.value = false;
  }
}

const addInput = (configIndex) => {
  const urls = pushConfigs.value[configIndex].config.wxUrls;
  if (urls.length < 5) {
    urls.push('');
  }
}

const removeInput = (configIndex, inputIndex) => {
  const urls = pushConfigs.value[configIndex].config.wxUrls;
  if (urls.length > 1) {
    urls.splice(inputIndex, 1);
  } else {
    removePushChannel(configIndex);
  }
}

const wxUrlInput = (event, inputIndex, channelType) => {
  const value = event.target.value;
  const config = pushConfigs.value.find(config => config.channelType === channelType);
  if (config && config.config.wxUrls) {
    config.config.wxUrls[inputIndex] = value;
  }
}
const skipWeb = (v) => {
  if (v === '企业微信') {
    window.open("https://open.work.weixin.qq.com/help2/pc/14931?person_id=1&is_tencent", '_blank');
  }
  if (v === '钉钉') {
    window.open("https://open.dingtalk.com/document/orgapp/custom-robot-access#title-jfe-yo9-jl2", '_blank')
  }
  if (v === '飞书') {
    window.open("https://thinkingdata.feishu.cn/docs/doccnF2uHIo7kV5nWyAxSOuwQrg", '_blank')
  }
  if (v === 'slack') {
    window.open("https://api.slack.com/messaging/webhooks", '_blank')
  }
}

</script>

<style lang="less" scoped>
.pushChannel {
  display: flex;
  flex-direction: column;

  .boardPush-span {
    color: #333;
    font-size: 14px;
    font-style: normal;
    margin-right: 10px;
  }

  .pushConfig {
    display: flex;
    flex-direction: column;

    .pushConfigSelected {
      display: flex;
      flex-direction: column;

      .PushConfigInput {
        display: flex;
        flex-direction: row;
        margin: 0 0 12px;
        align-items: flex-start;
        justify-items: flex-start;

        .selectCss {
          width: 160px;
          margin-right: 10px;
        }

        .inputCss {
          width: 100%;
          padding: 0 8px;
          margin: 0 8px 0 0;

          .a-input {
            height: 32px;
            padding: 0 8px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            line-height: 30px;
            background-color: transparent;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: var(--tant-border-radius-medium);
            width: 385.42px;
            outline: none;
            box-shadow: none;
          }

          .a-input:hover {
            background-color: var(--tant-bg-white-color-bg1-1);
            border: 1px solid var(--tant-primary-color-primary-hover);
          }

          .inputWrapper {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            justify-content: space-between;
          }


        }
      }
    }
  }
}

.actionButtons {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-top: 7px;

  .-button-css {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    background-color: var(--tant-bg-white-color-bg1-1);
    margin-right: 5px;
    white-space: nowrap;

    .font-span {
      color: var(--tant-decorative-blue-color-decorative1-1);
    }
  }
}

.feishuInput {
  display: flex;
  width: 385.42px;
}

.ico {
  margin-right: 4px;
  color: var(--tant-status-danger-color-danger-default);

}

.boardPush-pushway {
  font-size: 14px;
  text-align: start;
}
</style>
