<template>
  <div class="page-content">
    <div class="detail-content">
        <div class="video">
            <div class="video-box">
                <video width="100%" height="100%" controls loop>
                    <source src="https://xmp-material.mobvista.com/video/69/69a97d8995984d7686899718a919b30d.mp4" type="video/mp4">
                </video>
            </div>
        </div>
        <div class="info">
            <a-form ref="formRef" :model="form" label-align="left" style="width: 100%;">
                <a-form-item field="name" label="素材名称">
                    {{ form.name }}
                </a-form-item>
                <a-form-item field="id" label="素材ID">
                    {{ form.id }}
                </a-form-item>
                <a-form-item field="md5" label="MD5">
                    {{ form.md5 }}
                </a-form-item>
                <a-form-item label="设计师">
                    -
                </a-form-item>
                <a-form-item label="创意人">
                    -
                </a-form-item>
                <a-form-item label="标签">
                    -
                </a-form-item>
                <a-form-item label="所属文件夹">
                    Unknow
                </a-form-item>
                <a-form-item label="类型">
                    视频
                </a-form-item>
                <a-form-item label="形状">
                    竖版
                </a-form-item>
                <a-form-item label="尺寸">
                    1080x1920
                </a-form-item>
                <a-form-item label="时长(秒)">
                    60.03
                </a-form-item>
                <a-form-item label="创建时间">
                    -
                </a-form-item>
            </a-form>
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";

const form = reactive({
    name: '2761_sy_20241219_重复动作.mp4.mp4',
    id:'69a97d8995984d7686899718a919b30d',
    md5:'69a97d8995984d7686899718a919b30d'
})
</script>

<style scoped lang="less">
.detail-content{
    display: flex;
    .video{
        margin-bottom: 10px;
        position: relative;
        max-width: 40%;
        min-width: 404px;
        margin-right: 20px;
        .video-box{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 404px;
            height: 404px;
            background-color: #f5f6f7;
        }
    }
    .info{
        flex: 1;
        max-width: 60%;
    }
}
</style>