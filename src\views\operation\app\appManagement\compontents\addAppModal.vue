
<template>
    <a-modal v-model:visible="modalVisible" :width="560" title-align="start" title="添加应用" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="osType" label="选择平台" validate-trigger="change">
                <a-radio-group v-model:model-value="form.osType" @change="osNameChange">
                    <a-radio value="android">安卓</a-radio>
                    <a-radio value="ios">IOS</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item field="status" label="选择上架状态" validate-trigger="change">
                <a-radio-group v-model:model-value="form.status">
                    <a-radio :value="1">已上架</a-radio>
                    <a-radio :value="0">未上架</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item v-if="form.status" field="platform" label="选择应用商店" validate-trigger="change">
                <a-select v-model:model-value="form.platform" placeholder="请选择选择应用商店" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option v-for="item in storeList" :key="item.code" :value="item.code">{{item.name}}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item v-if="form.status" field="platformLink" label="应用商店URL" validate-trigger="blur">
              <a-input v-model="form.platformLink" placeholder="请输入应用商店URL"/>
            </a-form-item>
            <a-form-item field="appId" label="应用ID" validate-trigger="blur">
              <a-input v-model="form.appId" placeholder="请输入应用ID"/>
            </a-form-item>
            <a-form-item field="name" label="应用名称" validate-trigger="blur">
              <a-input v-model="form.name" placeholder="请输入应用名称"/>
            </a-form-item>
            <a-form-item field="package" label="应用包名：" validate-trigger="blur">
                <a-input v-model="form.package" placeholder="请输入应用包名"/>
            </a-form-item>
            <a-form-item v-if="form.osType === 'ios'" field="storeId" label="appStoreID" validate-trigger="blur">
              <a-input v-model="form.storeId" placeholder="请输入Appstoreid"/>
            </a-form-item>
            <a-form-item field="icon" label="应用ICON" validate-trigger="blur">
                <a-input v-model="form.icon" placeholder="请输入应用ICON-URL"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {getStoreList, postAddApp} from "@/api/marketing/api";

const modalVisible = ref(false)
const storeList = ref()
const form = reactive({
    osType:'android',
    status:1,
    platform:'',
    platformLink:'',
    appId: '',
    name:'',
    storeId: '',
    package:'',
    icon:'',
})
const rules = {
    osType: [
        {
            required: true,
            message:'请选择平台'
        }
    ],
    status: [
        {
            required: true,
            message:'请选择上架状态'
        }
    ],
    platform: [
        {
            required: true,
            message:'请选择应用商店'
        }
    ],
    platformLink: [
        {
            required: true,
            message:'请填写应用商店URL'
        }
    ],
    appId: [
        {
            required: true,
            message:'请填写应用ID'
        },
        {
            match: /^[a-zA-Z0-9_]+$/,
            message: '应用ID支持数字、字母和下划线'
        }
    ],
    name: [
        {
            required: true,
            message:'请填写应用名称'
        }
    ],
    storeId: [
        {
            required: true,
            message:'请填写appStoreId'
        }
    ],
    icon: [
        {
            required: true,
            message:'请上传图标'
        }
    ],
    package:[
        {
            required: true,
            message:'请填写应用包名'
        }
    ]
}
const formRef = ref()

const emits = defineEmits(['updateData']);

// 商店平台列表
const getStore = async () => {
    await getStoreList(form.osType || 'android').then(res => {
        storeList.value= res
    })
}
const osNameChange = (v) => {
    form.platform = ''
    getStore()
}
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    await getStore()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try{
                postAddApp(form).then(res => {
                    Message.success('创建成功')
                    modalVisible.value = false
                    emits('updateData')
                })
            } catch (error) {
                console.log(error);
            } finally{
                loading.value = false
            }
        
        }else{
            // Message.warning('请填写必要数据')
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>