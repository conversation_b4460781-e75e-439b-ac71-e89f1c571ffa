<template>
  <div class="container">
    <div class="logo">
    </div>
    <!-- <LoginBanner /> -->
    <div class="content">
      <div class="left-banner">
        <LoginBanner />
      </div>
      <div>
        <div class="login-card">
          <LoginForm />
        </div>
      </div>
      <!-- <div class="footer">
        <Footer />
      </div> -->
    </div>
    <Footer />
  </div>
</template>

<script lang="ts" setup>
import {onMounted, onUnmounted} from "vue";
import {getToken} from "@/utils/auth";
import Footer from '@/components/footer/index.vue';
import {useRouter} from "vue-router";
import LoginBanner from './components/banner.vue';
import LoginForm from './components/login-form.vue';

const router = useRouter();

const checkAndRedirect = () => {
  const token = getToken();
  const { redirect, ...othersQuery } = router.currentRoute.value.query;
  const routePath = redirect && redirect !== "/" ? (redirect as string) : "/"; // 默认跳转到根路径

  if (token) {
    router.push({
      path: routePath,
      query: {
        ...othersQuery,
      },
    });
  }
};

let intervalId: number | null = null;

onMounted(() => {
  checkAndRedirect(); // 立即执行一次
  intervalId = setInterval(checkAndRedirect, 4000); // 每4秒执行一次
});

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId); // 组件销毁时清除定时器，避免内存泄漏
  }
});
</script>

<style lang="less" scoped>
  .container{
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 100vh;
    background-image: url('/image/loginBanner.jpeg');
  }
  .logo{
    height: 60px;
    padding-top: 24px;
  }
  .content{
    align-items: center;
    box-sizing: border-box;
    display: flex;
    height: calc(100vh - 125px);
    justify-content: space-between;
    margin: 0 auto;
    max-width: 1200px;
    min-height: 650px;
    // padding: 0 40px;
    .left-banner{
      align-items: center;
      display: flex;
      flex: 1 1;
      height: 100%;
      position: relative;
    }
    .login-card{
      display: flex;
      flex-direction: column;
      margin-bottom: 30px;
      padding: 48px 43px 32px;
      background: #fff;
      border-radius: 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, .05);
      box-sizing: border-box;
      min-height: 510px;
      position: relative;
      width: 476px;
    }
  }
  // .container {
  //   display: flex;
  //   height: 100vh;

  //   .banner {
  //     width: 70%;
  //     background: linear-gradient(163.85deg, #1d2129 0%, #00308f 100%);
  //   }

  //   .content {
  //     position: relative;
  //     display: flex;
  //     flex: 1;
  //     align-items: center;
  //     justify-content: center;
  //     padding-bottom: 40px;
  //     width: 30%;
  //   }

  //   .footer {
  //     position: absolute;
  //     right: 0;
  //     bottom: 0;
  //     width: 100%;
  //   }
  // }

  // .logo {
  //   position: fixed;
  //   top: 24px;
  //   left: 22px;
  //   z-index: 1;
  //   display: inline-flex;
  //   align-items: center;

  //   &-text {
  //     margin-right: 4px;
  //     margin-left: 4px;
  //     color: var(--color-fill-1);
  //     font-size: 20px;
  //   }
  // }
</style>

<style lang="less" scoped>
  // responsive
  @media (max-width: @screen-lg) {
    .container {
      .banner {
        width: 25%;
      }
    }
  }
</style>
