<!-- 分析主体下拉选组件 -->
<template>
  <a-select v-model="params.subject" :disabled="disabled" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="selectChange">
    <a-optgroup v-for="(item,index) in barData" :key="index" :label="item.group">
      <a-trigger v-for="child in item.items" :key="child.code" :trigger="['hover']" position="right" :update-at-scroll="true">
        <a-option style="width: 140px;" :style="optionStyle" :value="child.code">{{ child.name }}</a-option>
        <template #content>
          <div class="trigger-box">
            <div class="card-header">
              <div class="card-header-container">
                <div class="header-icon">
                  <IconFont type="icon-crosshair-2-fill" :size="14"/>
                </div>
                <div class="header-title">
                  <div class="name">{{ child.name }}</div>
                </div>
                <div class="header-type">自定义分析主体</div>
              </div>
            </div>
            <div class="card-desc">
              <div class="span-desc">{{ child.description == undefined || child.description == '' ? '暂无描述' : child.description }}</div>
            </div>
          </div>
        </template>
      </a-trigger>
    </a-optgroup>

  </a-select>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import {analyseStore} from '@/store';

const analyseData = analyseStore()
const props = defineProps({
  subjectData: {
    type: Object,
    default: () => ({
      subject: '',
      subjectName: ''
    })
  },
  disabled: {
    type: Boolean,
    default: () => false
  },
  optionStyle: {
    type: Object,
    required: false
  }
})
const params = reactive({
  subject: '',
  subjectName: ''
})
const barData = ref<any>()
const emits = defineEmits(['subjectChange', 'subjectInit'])

const selectChange = async () => {
  await analyseData.fetchOperationFilterLists(params.subject)
  await analyseData.fetchOperationGroupLists(params.subject)
  emits('subjectChange', params)
}

onMounted(async () => {
  const data = analyseData.$state.subjectLists.length > 0 ? analyseData.$state.subjectLists : await analyseData.fetchSubjectLists()
  const filteredData = [{
    group: '应用',
    items: data?.filter(item => ['application'].includes(item.source))
  }];
  barData.value = filteredData;
  const {code, name} = filteredData[0]?.items?.[0]
  params.subject = props.subjectData.subject ? props.subjectData.subject : code
  params.subjectName = props.subjectData.subjectName ? props.subjectData.subjectName : name
  emits('subjectInit', params)
})
// watch(() => params.subject, async (newData,oldData) => {
//   if(newData){
//     await analyseData.fetchOperationFilterLists(params.subject)
//     await analyseData.fetchOperationGroupLists(params.subject)
//   }
// },{deep:true})

</script>

<style lang="less" scoped>
.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);

  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;
      align-items: center;

      .header-icon {
        margin-bottom: 1px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }

      .header-type {
        flex-shrink: 0;
        // min-width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }
}
</style>