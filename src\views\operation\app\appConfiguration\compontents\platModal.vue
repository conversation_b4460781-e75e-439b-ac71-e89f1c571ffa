<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="osName" label="选择平台" validate-trigger="change">
                <a-radio-group v-model:model-value="form.osName">
                    <a-radio value="android">安卓</a-radio>
                    <a-radio value="ios">IOS</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item field="name" label="显示名">
                <a-input v-model="form.name"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addStoreList,} from "@/api/marketing/api";

const modalVisible = ref(false)
const modalTitle = ref('新增应用商店')
const form = reactive({
    name: '',
    osName:'android'
})
const rules = {
    name: [
        {
            required: true,
            message:'请填写显示名',
        }
    ],
    osName: [
        {
            required: true,
            message:'请选择平台',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);
const openModal = () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            try {
                await addStoreList(form);
                Message.success('创建成功');
                modalVisible.value = false;
                emits('updateData', modalTitle.value);
            } catch (error) {
                console.error('创建失败:', error);
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>