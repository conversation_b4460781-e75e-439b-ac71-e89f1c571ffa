<template>
  <div>
    <div class="search">
      <a-input v-model:model-value="pageParams.text" style="background-color: #fff; width: 300px; margin-right: 12px" placeholder="搜索项目组" @press-enter="init">
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
      <a-button type="primary" @click="addList">
        <template #icon>
          <icon-plus />
        </template>
        添加项目组
      </a-button>
    </div>
    <a-table :columns="columns" :loading="loading" :data="tableData" size="small" :hoverable="true" sticky-header :pagination="pagination" :table-layout-fixed="true" :filter-icon-align-left="true" :column-resizable="true" :scroll="scroll" :scrollbar="scrollbar" @page-size-change="pageSizeChange">
      <template #name="{ record }">
        <span class="href-name">{{ record.team }}</span>
      </template>
      <template #profit="{ record }">
        {{ formatNumber(record.profit) }}
      </template>
      <template #roas="{ record }">
        {{ formatNumber(record.roas) }}
      </template>
      <template #retention="{ record }">
        {{ formatNumber(record.retention) }}
      </template>
      <template #profitTarget="{ record }">
        <div class="hover-box">
          <span>{{ formatNumber(record.profitTarget) }}</span>
          <a-tooltip content="编辑">
            <icon-edit class="add-group" size="16px" @click="editProfit(record)" />
          </a-tooltip>
        </div>
      </template>
      <template #reachPeriod="{ record }">
        <span class="href-value" @click="openCycle">{{ record.reachPeriod }}</span>
      </template>
    </a-table>
    <!-- <div class="pagination">
          <a-pagination :total="total" show-total @change="pageChange"/>
        </div> -->
    <handleProfitTarget ref="profitRef" label="team" @update-data="init" />
    <cycleModal ref="cycleRef" />
    <HandleProject ref="handleRef" :table-data="tableData" @update-data="init" />
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive, ref, inject } from 'vue';
  import { getProfitTargetList } from '@/api/analyse/api';
  import handleProfitTarget from './components/handleProfitTarget.vue';
  import cycleModal from './components/cycleModal.vue';
  import HandleProject from './components/HandleProject.vue';

  // 模拟table数据
  const loading = ref(false);
  const year = ref(inject('year'));
  const tableData = ref<any>([]);
  const total = ref(0);
  const pageParams = reactive({
    text: '',
  });
  const pagination = ref<any>({
    pageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    showPageSize: 'true',
    showTotal: 'true',
  });
  const pageSizeChange = (size: number) => {
    pagination.value.pageSize = size;
  };
  const init = async () => {
    loading.value = true;
    const data = {
      label: 'team',
      year: year.value,
      ...pageParams,
    };
    try {
      await getProfitTargetList(data).then((res) => {
        if (res) {
          tableData.value = res;
        }
      });
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      loading.value = false;
    }
  };
  init();
  const pageChange = (v) => {
    pageParams.current = v;
    init();
  };
  const formatNumber = (num) => {
    return Math.round(num * 100) / 100;
  };
  // 图标改变
  const scrollbar = ref(true);

  const columns = ref<any>([
    {
      title: '项目组',
      dataIndex: 'team',
      ellipsis: 'true',
      tooltip: true,
      slotName: 'name',
      minWidth: 120,
    },
    {
      title: '利润目标',
      dataIndex: 'profitTarget',
      ellipsis: 'true',
      tooltip: true,
      slotName: 'profitTarget',
      sortable: {
        sortDirections: ['ascend', 'descend'],
      },
      minWidth: 120,
    },
    {
      title: '达成周期',
      dataIndex: 'reachPeriod',
      slotName: 'reachPeriod',
      ellipsis: 'true',
    },
    {
      title: '当前利润',
      dataIndex: 'profit',
      slotName: 'profit',
      sortable: {
        sortDirections: ['ascend', 'descend'],
      },
      ellipsis: 'true',
    },
    {
      title: 'ROAS',
      dataIndex: 'roas',
      slotName: 'roas',
      sortable: {
        sortDirections: ['ascend', 'descend'],
      },
      ellipsis: 'true',
    },
    {
      title: '近7天次留',
      dataIndex: 'retention',
      slotName: 'retention',
      ellipsis: 'true',
    },
  ]);
  // Table页面滑动
  const scroll = {
    y: 'calc(100vh - 330px)',
  };

  const profitRef = ref();

  const editProfit = (record) => {
    profitRef.value.openModal(record);
  };

  const cycleRef = ref();
  const openCycle = () => {
    cycleRef.value.openModal();
  };
  const handleRef = ref();
  const addList = () => {
    handleRef.value.openModal();
  };
  defineExpose({
    init,
  });
</script>

<style scoped lang="less">
  .pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
  }
  .search {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;
  }
  .href-name {
    text-decoration: underline;
    color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    //flex: 1;
    overflow: hidden;
    width: auto;
    cursor: pointer;
  }
  .href-value {
    text-decoration: underline;
    //   color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: auto;
    cursor: pointer;

    &:hover {
      color: #8d0088;
    }
  }
  .hover-box {
    display: flex;
    align-items: center;
    &:hover {
      .add-group {
        opacity: 1;
      }
    }
    .add-group {
      cursor: pointer;
      opacity: 0;
      margin-left: 5px;
      // &:hover{
      //   color: #8d0088;
      // }
    }
  }
  .setting {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    cursor: pointer;
  }

  .setting:hover {
    background-color: var(--tant-dblue-dblue-20);
    border-radius: 3px;
  }

  .delete {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
  }

  .delete:hover {
    background-color: var(--tant-red-red-20);
    color: var(--tant-red-red-50);
    border-radius: 3px;
  }
</style>
