<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  v-model="filterParams.subChannelIds"
                  placeholder="请选择子渠道ID"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="subChannelOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleSubChannelSearch"
                  @dropdown-reach-bottom="loadMoreSubChannels"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in subChannelOptions" 
                    :key="item.subChannelId" 
                    :value="item.subChannelId" 
                    :label="item.subChannelName">
                    {{ item.subChannelName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.adUnitIds"
                  placeholder="请选择广告单元"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adUnitOptionsLoading"
                  :filter-option="false"
                  allow-search
                  :tag-nowrap="true"
                  @search="handleAdUnitSearch"
                  @dropdown-reach-bottom="loadMoreAdUntis"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告账户-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adUnitOptions" 
                    :key="item.adUnitId" 
                    :value="item.adUnitId" 
                    :label="item.adUnitName">
                    {{ item.adUnitName }}
                  </a-option>
              </a-select>
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption :disabled="!selectedKeys.length">修改出价</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditTag">编辑标签</a-doption>
                    <a-doption @click="excelBidEdit">Excel出价编辑</a-doption>
                    <a-doption @click="excelBlockEdit">Excel屏蔽子渠道</a-doption>
                </template>
            </a-dropdown>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="subChannelId"
        >
          <template #effectiveStatus="{ record }">
            <a-switch v-model="record.effectiveStatus" checked-value="ACTIVE" unchecked-value="PAUSED" size="small" :before-change="() => statusChange(record)"/>
          </template>
          <template #accountStatus="{ record }">
              <a-badge :status="record.accountStatus === 1 ? 'success' : 'normal'" :text="record.accountStatus === 1 ? 'ACTIVE' : 'DISABLED'"/>
          </template>
           <template #status="{ record }">
              <a-badge :status="record.status === 'ACTIVE' ? 'success' : 'normal'" :text="record.status"/>
          </template>
          <template #adUnitName="{ record,rowIndex }">
            <div class="cell-content">
              <div class="text">{{ record.adUnitName }}</div>
              <div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </div>
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'effectiveStatus'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 批量编辑标签 -->
      <BatchEditTags ref="tagRef"/>
      <!-- excel批量编辑 -->
      <ExcelBidEdit ref="excelRef"/>
      <!-- excel批量屏蔽子渠道 -->
      <ExcelBlockSub ref="excelBlockRef"/>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject, watch } from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import {mintegralDrillList} from "@/views/launch/promotion/components/promotionData"
import BatchEditTags from "@/views/launch/promotion/components/BatchEditTags.vue";
import DrillDrawer from "../components/DrillDrawer.vue";
import ExcelBidEdit from "../components/ExcelBidEdit.vue";
import ExcelBlockSub from "../components/ExcelBlockSub.vue";


// 设置数据
const filterParams = reactive({
  adUnitIds:[],
  subChannelIds:[]
})
const dateTime = ref(inject('dateTime') as any[])
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
// 模拟table数据
const loading = ref(false)
const dropdownList = mintegralDrillList.ad
const tableData = ref<any>([])
const noSumColumns = ref([
  'adUnitName',
  'formatCountries',
  'subChannelId',
  'subChannelBid',
])
const columns = ref<any>([
  { title: '', dataIndex: 'effectiveStatus',slotName:'effectiveStatus',width:100,fixed:'left' },
  { title: '广告单元名称', dataIndex: 'adUnitName',slotName:'adUnitName',width:250,fixed:'left', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '地区', dataIndex: 'formatCountries',width:180,fixed:'left',slotName:'formatCountries',tooltip:true,ellipsis:true },
  { title: '子渠道ID', dataIndex: 'subChannelId',width:180,fixed:'left' },
  { title: '子渠道出价类型', dataIndex: 'subChannelBid',minWidth:180 },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])
const subChannelOptions = ref<any>([])
const subChannelOptionsLoading = ref(false);
const subChannelPagination = reactive({
  current: 1,
  pageSize: 20,
});

const adUnitOptions = ref<any>([])
const adUnitOptionsLoading = ref(false);
const adUnitPagination = reactive({
  current: 1,
  pageSize: 20,
});
// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};

const confirmVisible = ref(false)
const confirmType = ref('')
const statusChange = (record) => {
  confirmType.value = record.authStatus ? 'pause' : 'open'
  confirmVisible.value = true
  return new Promise((resolve, reject) => {
    if(confirmVisible.value){
      resolve(false);
    }
    resolve(true);
  });
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'ad',value:val,recordData:record})
}

// 开启
const batchOpen = () => {
  confirmVisible.value = true
  confirmType.value = 'open'
}
// 暂停
const batchPause = () => {
  confirmVisible.value = true
  confirmType.value = 'pause'
}
// 定时开关
const timeRef = ref()
const batchTime = () => {
  timeRef.value.openModal()
}
// 批量编辑标签
const tagRef = ref()
const batchEditTag = () => {
  tagRef.value.openModal('ad')
}
// excel出价编辑
const excelRef = ref()
const excelBidEdit = () => {
  excelRef.value.openModal('subChannel')
}
// excel屏蔽子渠道
const excelBlockRef = ref()
const excelBlockEdit = () => {
  excelBlockRef.value.openModal('subChannel')
}
// 加载子渠道选项
const loadSubChannelOptions = async () => {
  subChannelOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:subChannelPagination.pageSize,
      current:subChannelPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (subChannelPagination.current === 1) {
      subChannelOptions.value = items;
    } else {
      subChannelOptions.value = [...subChannelOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告选项失败:', error);
  } finally {
    subChannelOptionsLoading.value = false;
  }
}
const handleSubChannelSearch = (v) => {
}
const loadMoreSubChannels = async () => {
  subChannelPagination.current += 1;
  // 获取更多子渠道数据
  await loadSubChannelOptions();
}

// 加载广告单元选项
const loadAdUnitOptions = async () => {
  adUnitOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adUnitPagination.pageSize,
      current:adUnitPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    if (adUnitPagination.current === 1) {
      adUnitOptions.value = items;
    } else {
      adUnitOptions.value = [...adUnitOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    adUnitOptionsLoading.value = false;
  }
};   
const handleAdUnitSearch = (v) => {
}
const loadMoreAdUntis = async () => {
  adUnitPagination.current += 1;
  // 获取更多广告单元数据
  await loadAdUnitOptions();
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      subChannelOptions.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
}
const init = (filter?:any) => {
  getList()
//   loadAdUnitOptions()
}
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>
