<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";

// 模拟table数据
const originalData = reactive([
  {
    key: '1',
    name: '先用户登录，后用户登录的用户留存分析',
    type: '留存分析',
    apply: 'jojo测试看板',
    creator: 'IG测试',
    remake: '',
    updateUser: 'zz',
    authority: '可编辑',
    updateTime: '2023-08-23 15：06：12',
    operate: '',
  },
  {
    key: '2',
    name: '自定义指标',
    type: '事件分析',
    apply: '11112（1）',
    creator: '随身',
    remake: '',
    updateUser: 'huolieniao_demo',
    authority: '可编辑',
    updateTime: '2023-08-23 18：06：12',
    operate: '',
  },
  {
    key: '3',
    name: '当前活跃人数',
    type: 'SQL分析',
    apply: '测试看板1',
    creator: '测试用户1',
    remake: '测试中。。。',
    updateUser: '123ccc',
    authority: '仅查看',
    updateTime: '2023-08-23 21：06：12',
    operate: '',
  },
  {
    key: '4',
    name: '在线人数',
    type: 'SQL分析',
    apply: '测试看板2',
    creator: '测试用户2',
    remake: '测试中。。。',
    updateUser: 'zz',
    authority: '可编辑',
    updateTime: '2023-08-25 15：06：12',
    operate: '',
  },
  {
    key: '5',
    name: '用户登录总次数',
    type: '事件分析',
    apply: '测试看板3',
    creator: '测试用户3',
    remake: '测试中。。。',
    updateUser: 'zz',
    authority: '可编辑',
    updateTime: '2023-08-21 15：06：12',
    operate: '',
  },
  {
    key: '6',
    name: '报表1',
    type: '事件分析',
    apply: '11112（1）',
    creator: 'IG测试',
    remake: '测试中。。。',
    updateUser: 'huolieniao_demo',
    authority: '可编辑',
    updateTime: '2023-02-25 15：06：12',
    operate: '',
  },
  {
    key: '7',
    name: '过去30天累充排行榜',
    type: 'SQL分析',
    apply: 'jojo测试看板',
    creator: '随身',
    remake: '测试中。。。',
    updateUser: '-',
    authority: '可编辑',
    updateTime: '2023-10-25 15：06：12',
    operate: '',
  },
  {
    key: '8',
    name: '过去30天累充排行榜',
    type: 'SQL分析',
    apply: 'jojo测试看板',
    creator: '随身',
    remake: '测试中。。。',
    updateUser: '-',
    authority: '可编辑',
    updateTime: '2023-10-25 15：06：12',
    operate: '',
  },
  {
    key: '9',
    name: '过去30天累充排行榜',
    type: 'SQL分析',
    apply: 'jojo测试看板',
    creator: '随身',
    remake: '测试中。。。',
    updateUser: '-',
    authority: '可编辑',
    updateTime: '2023-10-25 15：06：12',
    operate: '',
  },
  {
    key: '10',
    name: '过去30天累充排行榜',
    type: 'SQL分析',
    apply: 'jojo测试看板',
    creator: '随身',
    remake: '测试中。。。',
    updateUser: '-',
    authority: '可编辑',
    updateTime: '2023-10-25 15：06：12',
    operate: '',
  },
  {
    key: '11',
    name: '过去30天累充排行榜',
    type: 'SQL分析',
    apply: 'jojo测试看板',
    creator: '随身',
    remake: '测试中。。。',
    updateUser: '-',
    authority: '可编辑',
    updateTime: '2023-10-25 15：06：12',
    operate: '',
  },
])
const tableDate = ref([...originalData]);
// name
// name-编辑
const Edit = ref(Array(tableDate.value.length).fill(false));
const EditVisible = ref(Array(tableDate.value.length).fill(false));
const originalNames = ref<string[]>([])
const showEdit = (index: number, status: boolean) => {
  if (tableDate.value[index].authority === '可编辑') {
    Edit.value[index] = status;
  }
};
const showEditVisible = (index: number, status: boolean) => {
  EditVisible.value[index] = status
  originalNames.value[index] = tableDate.value[index].name;
}
const saveEdit = (index: number, value: string) => {
  tableDate.value[index].name = value; // 保存编辑的值
  EditVisible.value[index] = false; // 退出编辑模式
};
const closeEdit = (index: number) => {
  tableDate.value[index].name = originalNames.value[index];
  EditVisible.value[index] = false;
}
// type
// type-options
const selectOptions = [
  '事件分析',
  '留存分析',
  '漏斗分析',
  '属性分析',
  '路径分析',
  '分布分析',
  'SQL分析',
  '间隔分析',
  '归因分析',
  '热力地图',
  '排行榜',
]
const typeVisible = ref(false)
const typeClose = () => {
  typeVisible.value = !typeVisible.value;
}
// apply
// apply-options
const checkOptions = [
  'jojo测试看板',
  '11112（1）',
  '测试看板1',
  '测试看板2',
  '测试看板3',
  '测试看板4',
  '测试看板5',
  '测试看板6']
const applyVisible = ref(false)
const applyClose = () => {
  applyVisible.value = !applyVisible.value;
}
// creator
// creator-options
const creatorOptions = [
  'IG测试',
  '随身',
  '老王',
  '长期活跃用户',
  '测试用户1',
  '测试用户2',
  '测试用户3',
  '测试用户4',
  '测试用户5',
]
const creatorVisible = ref(false)
const creatorClose = () => {
  creatorVisible.value = !creatorVisible.value;
}
// updateUser
// updateUser-options
const updateUserOptions = [
  'huolieniao_demo',
  '123ccc',
  'qweaaa',
  '管理员',
  'zz',
]
const updateUserVisible = ref(false)
const updateUserClose = () => {
  updateUserVisible.value = !updateUserVisible.value
}
// remake
const remakeEdit = ref(Array(tableDate.value.length).fill(false));
const RemakeVisible = ref(Array(tableDate.value.length).fill(false));
const remakeShowEdit = (index: number, status: boolean) => {
  if (tableDate.value[index].authority === '可编辑') {
    remakeEdit.value[index] = status;
  }
};
const showRemakeVisible = (index: number, status: boolean) => {
  originalNames.value[index] = tableDate.value[index].remake;
  RemakeVisible.value[index] = status
}
const remakeSaveEdit = (index: number, value: string) => {
  tableDate.value[index].remake = value; // 保存编辑的值
  RemakeVisible.value[index] = false; // 退出编辑模式
};
const remakeCloseEdit = (index: number) => {
  tableDate.value[index].remake = originalNames.value[index];
  RemakeVisible.value[index] = false;
}
// authority
// authority-options
const authority = [
  '可编辑',
  '仅查看',
]
// inputSearch
const applySearchQuery = ref('');
const creatorSearchQuery = ref('');
const updateUserSearchQuery = ref('');
// checkbox-group
const ApplycheckDate = ref<string[]>()
const CreatorcheckDate = ref<string[]>()
const UpdateUsercheckDate = ref<string[]>()
// 半选
const applyIndeterminate = ref(false)
const creatorIndeterminate = ref(false)
const updateUserIndeterminate = ref(false)
// 全选
const ApplyCheckedAll = ref(false)
const CreatorCheckedAll = ref(false)
const UpdateUserCheckAll = ref(false)
const CreatorHandleChangeAll = (value: boolean) => {
  creatorIndeterminate.value = false;
  if (value) {
    CreatorCheckedAll.value = true;
    CreatorcheckDate.value = [...creatorOptions]
  } else {
    CreatorCheckedAll.value = false;
    CreatorcheckDate.value = []
  }
  return CreatorcheckDate.value
}
const ApplyHandleChangeAll = (value: boolean) => {
  applyIndeterminate.value = false;
  if (value) {
    ApplyCheckedAll.value = true;
    ApplycheckDate.value = [...checkOptions]
  } else {
    ApplyCheckedAll.value = false;
    ApplycheckDate.value = []
  }
  return ApplycheckDate.value
}
const UpdateUserHandleChangeAll = (value: boolean) => {
  updateUserIndeterminate.value = false;
  if (value) {
    UpdateUserCheckAll.value = true;
    UpdateUsercheckDate.value = [...updateUserOptions]
  } else {
    UpdateUserCheckAll.value = false;
    UpdateUsercheckDate.value = []
  }
  return UpdateUsercheckDate.value
}
// 仅选择-hidden
const ApplyShowOnly = ref(Array(checkOptions.length).fill(false));
const CreatorShowOnly = ref(Array(creatorOptions.length).fill(false));
const UpdateUserShowOnly = ref(Array(updateUserOptions.length).fill(false));

// 仅选择-show
const showIcon = (index: number, status: boolean) => {
  ApplyShowOnly.value[index] = status;
};
const CreatorShowIcon = (index: number, status: boolean) => {
  CreatorShowOnly.value[index] = status;
};
const UpdateUserShowIcon = (index: number, status: boolean) => {
  UpdateUserShowOnly.value[index] = status;
};
// 仅选择-choose
const ApplyHandleOnly = (values: string) => {
  ApplycheckDate.value = [values];
  return ApplycheckDate.value;
}
const CreatorHandleOnly = (values: string) => {
  CreatorcheckDate.value = [values];
  return CreatorcheckDate.value;
}
const UpdateUserHandleOnly = (values: string) => {
  UpdateUsercheckDate.value = [values];
  return UpdateUsercheckDate.value;
}

// filter函数
function filterItems(options: string[], searchQuery: string) {
  const query = searchQuery.trim().toLowerCase();
  if (!query) {
    return options;
  }

  const queries = query.split(/\s+/); // 按空格分割成多个关键词

  return options.filter(item => {
    // eslint-disable-next-line no-shadow
    return queries.every(query => {
      const regex = new RegExp(query.split('').join('.*'), 'i');
      return regex.test(item.toLowerCase());
    });
  });
}

// 过滤数据
const ApplyFilteredItems = computed(() => filterItems(checkOptions, applySearchQuery.value));
const CreatorFilteredItems = computed(() => filterItems(creatorOptions, creatorSearchQuery.value));
const UpdateUserFilteredItems = computed(() => filterItems(updateUserOptions, updateUserSearchQuery.value));

// checkbox-group-选择
const ApplyHandleChange = (values: []) => {
  if (values.length === ApplyFilteredItems.value.length) {
    ApplyCheckedAll.value = true
    applyIndeterminate.value = false;
  } else if (values.length === 0) {
    ApplyCheckedAll.value = false
    applyIndeterminate.value = false;
  } else {
    ApplyCheckedAll.value = false
    applyIndeterminate.value = true;
  }
  return ApplycheckDate.value

}
const CreatorHandleChange = (values: []) => {
  if (values.length === ApplyFilteredItems.value.length) {
    CreatorCheckedAll.value = true
    creatorIndeterminate.value = false;
  } else if (values.length === 0) {
    CreatorCheckedAll.value = false
    creatorIndeterminate.value = false;
  } else {
    CreatorCheckedAll.value = false
    creatorIndeterminate.value = true;
  }
  return CreatorcheckDate.value

}
const UpdateUserHandleChange = (values: []) => {
  if (values.length === UpdateUserFilteredItems.value.length) {
    UpdateUserCheckAll.value = true
    UpdateUserFilteredItems.value = false;
  } else if (values.length === 0) {
    UpdateUserCheckAll.value = false
    UpdateUserFilteredItems.value = false;
  } else {
    UpdateUserCheckAll.value = false
    UpdateUserFilteredItems.value = true;
  }
  return UpdateUsercheckDate.value

}

// 排序
const handleSort = (dataIndex: string, order: string) => {
  const sortedData = [...tableDate.value];

  if (dataIndex === 'name') {
    sortedData.sort((a, b) => {
      if (order === 'ascend') {
        return a.name.localeCompare(b.name);
      }
      if (order === 'descend') {
        return b.name.localeCompare(a.name);
      }
      return a.key.localeCompare(b.key);
    });
  }
  if (dataIndex === 'updateTime') {
    sortedData.sort((a, b) => {
      if (order === 'ascend') {
        return a.updateTime.localeCompare(b.updateTime);
      }
      if (order === 'descend') {
        return b.updateTime.localeCompare(a.updateTime);
      }
      return a.key.localeCompare(b.key);

    });
  }


  // 更新已筛选的数据
  tableDate.value = sortedData;
};
// 过滤
let currentFilteredData = [...originalData]; // 保存当前过滤后的数据
const appliedFilters: string[] = []; // 保存所有已应用的过滤条件

const handleFilter = (dataIndex: string, filteredValues: string | string[]) => {
  // 更新过滤条件
  if (filteredValues.length === 0) {
    // 如果该列的过滤条件为空，则删除该列的过滤条件
    delete appliedFilters[dataIndex];
  } else {
    // 更新该列的过滤条件
    appliedFilters[dataIndex] = filteredValues;
  }
  // 重新开始过滤
  currentFilteredData = [...originalData];

  // 应用所有过滤条件
  // eslint-disable-next-line guard-for-in,no-restricted-syntax
  for (const key in appliedFilters) {
    const values = appliedFilters[key];
    currentFilteredData = currentFilteredData.filter(item => {
      return values.includes(item[key]);
    });
  }

  // 更新表格数据
  tableDate.value = currentFilteredData;
};


const columns = [
  {
    title: '报表名称',
    dataIndex: 'name',
    ellipsis: "true",
    slotName: 's-name',
    sortable: {
      sortDirections: ['ascend', 'descend'],
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    filterable: {
      slotName: 'type-filter',
      triggerProps: {
        PopupVisible: typeVisible,
        onClick: typeClose,
      }
    }
  },
  {
    title: '应用看板',
    dataIndex: 'apply',
    slotName: 's-apply',
    filterable: {
      slotName: 'apply-filter',
      triggerProps: {
        popupVisible: applyVisible,
        onClick: applyClose,
      }
    }
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    filterable: {
      slotName: 'creator-filter',
      triggerProps: {
        popupVisible: creatorVisible,
        onClick: creatorClose,
      }
    }
  },
  {
    title: '备注',
    dataIndex: 'remake',
    slotName: 's-remake',
  },
  {
    title: '最近更新人',
    dataIndex: 'updateUser',
    filterable: {
      slotName: 'updateUser-filter',
      triggerProps: {
        popupVisible: updateUserVisible,
        onClick: updateUserClose,
      }
    }
  },
  {
    title: '操作权限',
    dataIndex: 'authority',
    slotName: 's-authority',
    filterable: {
      slotName: 'authority-filter',
      triggerProps: {
        PopupVisible: typeVisible,
        onClick: typeClose,
      }
    }
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    sortable: {
      sortDirections: ['ascend', 'descend'],
    }
  },
  {
    title: '操作',
    dataIndex: 'operate',
    slotName: 's-operate',
  },
]
const scrollbar = ref(true);
const scroll = {
  y: 'calc(100vh - 300px)'
};
const pageOptions=[
 10,20,50,100
]
const totalPages = computed(() => {
  return tableDate.value.length;
});
const pagination = reactive({
  total: totalPages.value,  // 使用过滤后的数据长度
  PageSize: pageOptions[0],
  showPageSize: true,
  pageSizeOptions: pageOptions,
  showJumper: true,
  hideOnSinglePage:true,
  autoAdjust:true,
});
watch(totalPages, (newTotal) => {
  pagination.total = newTotal;
});

</script>

<template>
  <a-table
      :columns="columns"
      :data="tableDate"
      :bordered="false"
      :hoverable="true"
      sticky-header
      :table-layout-fixed="true"
      :filter-icon-align-left="true"
      :scroll="scroll"
      :scrollbar="scrollbar"
      :pagination="pagination"
       @sorter-change="handleSort"
      @filter-change="handleFilter"

  >
    <template #s-name="{ rowIndex}">
      <div class="name-cell" @mouseover="showEdit(rowIndex, true)" @mouseleave="showEdit(rowIndex, false)">
        <template v-if="EditVisible[rowIndex]">
          <a-input
              v-model="tableDate[rowIndex].name"
              style="width: 80px"/>
          <button class="InputClose" @click="closeEdit(rowIndex)">
            <icon-close/>
          </button>
          <button class="InputSave" @click="saveEdit(rowIndex,tableDate[rowIndex].name)">
            <icon-check/>
          </button>
        </template>
        <template v-else>
          <a class="href-name" href="">{{ tableDate[rowIndex].name }}</a>
          <button v-show="Edit[rowIndex]" class="edit-btn">
            <icon-edit @click="showEditVisible(rowIndex, true)"/>
          </button>
        </template>
      </div>
    </template>
    <template #type-filter="{ setFilterValue, handleFilterConfirm,handleFilterReset }">
      <div class="dropdown">
        <div class="dropdown-item" @click="()=>{handleFilterReset();}">
          <div class="dropdown-label">
            全部
          </div>
        </div>
        <div v-for="item in selectOptions" :key="item" class="dropdown-item">
          <div class="dropdown-label" @click="()=>{setFilterValue(item);handleFilterConfirm();}">
            {{ item }}
          </div>
        </div>
      </div>
    </template>
    <template #apply-filter="{ setFilterValue, handleFilterConfirm }">
      <div class="apply-filter">
        <div class="dropdown-search">
          <a-input v-model:model-value="applySearchQuery" class="apply-input" placeholder="请输入搜索">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <a-checkbox-group
            v-model="ApplycheckDate" direction="vertical" class="checkbox-group"
            @change="()=>{setFilterValue(ApplyHandleChange(ApplycheckDate))}">
          <div
              v-for="(item,index) in ApplyFilteredItems" :key="item" class="checkbox-group-items"
              @mouseover="showIcon(index, true)"
              @mouseleave="showIcon(index, false)">
            <div class="checkbox-label">
              <a-checkbox
                  :value="item">
                <div class="items-text">
                  {{ item }}
                </div>
              </a-checkbox>
              <div v-show="ApplyShowOnly[index]" class="check-showText"
                   @click="()=>{setFilterValue(ApplyHandleOnly(item))}">
                只看此项
              </div>
            </div>
          </div>
        </a-checkbox-group>
        <div class="checkbox-all">
          <a-checkbox
              v-model:model-value="ApplyCheckedAll"
              class="check-all"
              :indeterminate="applyIndeterminate"
              @change="()=>{setFilterValue(ApplyHandleChangeAll(ApplyCheckedAll))}">
            <span class="check-all-text">全选 </span>
          </a-checkbox>
          <a-button class="apply" @click="()=>{applyVisible=false;handleFilterConfirm()}">应用</a-button>
        </div>
      </div>
    </template>
    <template #s-apply="{rowIndex}">
      <div style="cursor: pointer">
        {{ tableDate[rowIndex].apply }}
      </div>
    </template>
    <template #creator-filter="{ setFilterValue, handleFilterConfirm }">
      <div class="apply-filter">
        <div class="dropdown-search">
          <a-input v-model:model-value="creatorSearchQuery" class="apply-input" placeholder="请输入搜索">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <a-checkbox-group
            v-model="CreatorcheckDate" direction="vertical" class="checkbox-group"
            @change="()=>{setFilterValue(CreatorHandleChange(CreatorcheckDate))}">
          <div
              v-for="(item,index) in CreatorFilteredItems" :key="item" class="checkbox-group-items"
              @mouseover="CreatorShowIcon(index, true)"
              @mouseleave="CreatorShowIcon(index, false)">
            <div class="checkbox-label">
              <a-checkbox
                  :value="item">
                <div class="items-text">
                  {{ item }}
                </div>
              </a-checkbox>
              <div
                  v-show="CreatorShowOnly[index]" class="check-showText"
                  @click="()=>{setFilterValue(CreatorHandleOnly(item))}">
                只看此项
              </div>
            </div>
          </div>
        </a-checkbox-group>
        <div class="checkbox-all">
          <a-checkbox
              v-model:model-value="CreatorCheckedAll"
              class="check-all"
              :indeterminate="creatorIndeterminate"
              @change="()=>{setFilterValue(CreatorHandleChangeAll(CreatorCheckedAll))}">
            <span class="check-all-text">全选 </span>
          </a-checkbox>
          <a-button class="apply" @click="()=>{creatorVisible=false;handleFilterConfirm()}">应用</a-button>
        </div>
      </div>
    </template>
    <template #s-remake="{rowIndex}">
      <div class="name-cell" @mouseover="remakeShowEdit(rowIndex, true)" @mouseleave="remakeShowEdit(rowIndex, false)">
        <template v-if="RemakeVisible[rowIndex]">
          <a-input
              v-model:model-value="tableDate[rowIndex].remake"
              style="width: 80px"/>
          <button class="InputClose" @click="remakeCloseEdit(rowIndex)">
            <icon-close/>
          </button>
          <button class="InputSave" @click="remakeSaveEdit(rowIndex,tableDate[rowIndex].remake)">
            <icon-check/>
          </button>
        </template>
        <template v-else>
          <div class="remake-flow ">
            {{ tableDate[rowIndex].remake }}
          </div>
          <button v-show="remakeEdit[rowIndex] && tableDate[rowIndex].remake" class="edit-btn">
            <icon-edit @click="showRemakeVisible(rowIndex, true)"/>
          </button>
          <button v-show=" !tableDate[rowIndex].remake" class="edit-btn">
            <icon-plus @click="showRemakeVisible(rowIndex, true)"/>
          </button>
        </template>
      </div>
    </template>
    <template #updateUser-filter="{ setFilterValue, handleFilterConfirm }">
      <div class="apply-filter">
        <div class="dropdown-search">
          <a-input v-model:model-value="updateUserSearchQuery" class="apply-input" placeholder="请输入搜索">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <a-checkbox-group
            v-model="UpdateUsercheckDate" direction="vertical" class="checkbox-group"
            @change="()=>{setFilterValue(UpdateUserHandleChange(UpdateUsercheckDate))}">
          <div
              v-for="(item,index) in UpdateUserFilteredItems" :key="item" class="checkbox-group-items"
              @mouseover="UpdateUserShowIcon(index, true)"
              @mouseleave="UpdateUserShowIcon(index, false)">
            <div class="checkbox-label">
              <a-checkbox
                  :value="item">
                <div class="items-text">
                  {{ item }}
                </div>
              </a-checkbox>
              <div
                  v-show="UpdateUserShowOnly[index]" class="check-showText"
                  @click="()=>{setFilterValue(UpdateUserHandleOnly(item))}">
                只看此项
              </div>
            </div>
          </div>
        </a-checkbox-group>
        <div class="checkbox-all">
          <a-checkbox
              v-model:model-value="UpdateUserCheckAll"
              class="check-all"
              :indeterminate="updateUserIndeterminate"
              @change="()=>{setFilterValue(UpdateUserHandleChangeAll(UpdateUserCheckAll))}">
            <span class="check-all-text">全选 </span>
          </a-checkbox>
          <a-button class="apply" @click="()=>{updateUserVisible=false;handleFilterConfirm()}">应用</a-button>
        </div>
      </div>
    </template>
    <template #authority-filter="{ setFilterValue, handleFilterConfirm,handleFilterReset }">
      <div class="dropdown" style="height:138px ;width:auto;overflow-y: auto">
        <div class="dropdown-item" @click="()=>{handleFilterReset();}">
          <div class="dropdown-label">
            全部
          </div>
        </div>
        <div v-for="item in authority" :key="item" class="dropdown-item">
          <div class="dropdown-label" @click="()=>{setFilterValue(item);handleFilterConfirm();}">
            <div>
              {{ item }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #s-authority="{rowIndex}">
      <div v-if="tableDate[rowIndex].authority==='可编辑'" class="authority-text">
        {{ tableDate[rowIndex].authority }}
      </div>
      <div v-else class="unauthority-text">
        {{ tableDate[rowIndex].authority }}
      </div>
    </template>
    <template #s-operate="{rowIndex}">
      <div v-if="tableDate[rowIndex].authority==='可编辑'" class="operate">
        <a-tooltip content="编辑">
          <icon-edit class="operate-text"/>
        </a-tooltip>
      </div>
      <div v-else class="unoperate">
        <a-tooltip content="查看">
          <icon-eye class="operate-text"/>
        </a-tooltip>
      </div>

    </template>
    <template #pagination-left>
      <div class="pagination-left">
        共{{tableDate.length}}条记录
      </div>
    </template>

  </a-table>
</template>

<style scoped lang="less">

.dropdown {
  box-shadow: var(--tant-small-shadow-small-overall);
  width: 100px;
  height: 256px;
  overflow-y: scroll;
  margin-top: 10px;
  margin-left: 20px;
  border-radius: 4px;
  background-color: var(--tant-bg-white-color-bg1-1);
  padding: 5px;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;

    .dropdown-label {
      padding: 4px 4px 0;
    }
  }

  .dropdown-item:hover {
    background-color: var(--tant-bg-gray-color-bg2-1);
  }
}

.apply-filter {
  display: flex;
  flex-direction: column;
  background-color: var(--tant-bg-white-color-bg1-1);
  box-shadow: var(--tant-small-shadow-small-overall);
  border-radius: 4px;
  max-width: 240px;
  background: var(--tant-bg-white-color-bg1-1);


  .dropdown-search {
    height: 38px;
    line-height: 38px;
    border-bottom: 1px solid var(--tant-border-color-border1-1);

    .apply-input {
      margin-top: 5px;
      margin-bottom: 3px;
      padding: 4px 8px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 30px;
      background-color: transparent;
      border: none;
      border-radius: var(--tant-border-radius-medium);
      outline: none;
    }

    :deep(.arco-input-prefix)   {
      padding-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .checkbox-group {
    height: 100%;
    max-height: 256px;
    overflow-y: scroll;
    padding: 4px 4px 0;
    cursor: default;

    .checkbox-group-items {
      display: flex;
      align-items: center;
      height: 42px;
      border-radius: 5px;

      .checkbox-label {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .items-text {
          padding: 2px;
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          border-radius: 4px;
          width: 100px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .check-showText {
          color: var(--tant-primary-color-primary-hover);
          padding-right: 5px;
        }
      }


    }

    .checkbox-group-items:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
    }


  }

  .checkbox-all {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: var(--tant-fill-color-fill1-1);
    border-top: 1px solid var(--tant-border-color-border1-1);
    padding: 4px 4px 0;
    cursor: default;

    .apply {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--tant-status-info-color-info-hover);
      color: white;
      border-radius: 4px;
      height: 25px;
      width: 50px;
    }

    .check-all {
      display: flex;
      align-items: center;
      height: 32px;

      .check-all-text {
        padding: 2px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        border-radius: 4px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }


}

:deep(.arco-checkbox-icon)   {
  width: 16px;
  height: 16px;
}

.name-cell {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  .InputClose {
    border: none;
    color: red;
    background-color: transparent;
    font-size: 16px;
    border-radius: 5px;
  }

  .InputClose:hover {
    background-color: var(--tant-red-red-20);
  }

  .InputSave {
    border: none;
    color: var(--tant-green-green-50);
    background-color: transparent;
    font-size: 16px;
    border-radius: 5px;
  }

  .InputSave:hover {
    background-color: var(--tant-green-green-20);
  }

  .href-name {
    text-decoration: underline;
    color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    flex: 1;
    overflow: hidden;
    width: 120px;
  }

  .remake-flow {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .edit-btn {
    display: flex;
    border: none;
    background-color: transparent;
  }

  .edit-btn:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);

    border-radius: 5px;
  }

}

.authority-text {
  width: 58px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: var(--tant-status-info-color-info-hover);
  background-color: var(--tant-blue-blue-20);
}

.unauthority-text {
  width: 58px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: var(--tant-text-gray-color-text1-1);
  background-color: var(--tant-dblue-dblue-20);
}

.operate {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: start;
  padding-left: 5px;
}

.unoperate {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: start;
  padding-left: 5px;
}
.operate-text{
  width: 24px;
  height: 24px;
  font-size: 14px;
  padding: 5px;
  border-radius: 4px;
}
.operate-text:hover{
  background-color: var(--tant-dblue-dblue-20);
}
:deep(.arco-table-tr)  {
  height: 66px;
}
.pagination-left{
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}
:deep(.arco-pagination-jumper)  > span{
  color: var(--tant-text-gray-color-text1-1);
}
</style>