<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import {deleteAdItem, getAdFormatList, getAdList, getOpMediaSourceList} from "@/api/marketing/api";
import _ from "lodash";
import {Message} from '@arco-design/web-vue';
import advertiseModal from "./advertiseModal.vue";

const columns = [
  {
    title: '变现平台',
    dataIndex: 'adChannel',
    ellipsis: "true",
    slotName: 'adChannel',
    minWidth: 200,
    fixed: 'left'
  },
  {
    title: '广告ID',
    dataIndex: 'adId',
    ellipsis: "true",
    tooltip: true,
    slotName: 'adId',
    minWidth: 240,
  },
  {
    title: '广告名称',
    dataIndex: 'adName',
    ellipsis: "true",
    tooltip: true,
    slotName: 'adName',
    minWidth: 240,
  },
  {
    title: '广告类型',
    dataIndex: 'adLabel',
    ellipsis: "true",
    slotName: 'adLabel',
    minWidth: 120,
  },
  {
    title: '广告状态',
    dataIndex: 'adStatus',
    ellipsis: "true",
    slotName: 'adStatus',
    minWidth: 80,
  },
  {
    title: '是否授权CP',
    dataIndex: 'cpViewEnabled',
    ellipsis: "true",
    slotName: 'cpViewEnabled',
    minWidth: 80,
  },
  {
    title: 'CP授权比例',
    dataIndex: 'cpViewRate',
    ellipsis: "true",
    slotName: 'cpViewRate',
    minWidth: 80,
  },
  {
    title: '',
    dataIndex: 'action',
    ellipsis: "true",
    slotName: 'action',
    minWidth: 50,
    fixed: 'right',
    align: 'right',
  },
]
const tableData = ref()


const pageParams = reactive({
  text: '',
  adChannel: ''
})
const loading = ref(false)
const getData = async () => {
  loading.value = true
  const appId = sessionStorage.getItem('app-id') || ''
  if (appId) {
    const params = {
      appId,
      ...pageParams
    }
    await getAdList(params).then((res) => {
      // console.log(res,'广告单元list请求')
      tableData.value = _.cloneDeep(res)
    })
  }
  loading.value = false
}
const sourceList = ref()
const adFormatList = ref()
// 获取变现平台
const getSourceList = () => {
  getOpMediaSourceList('revenue').then(res => {
    sourceList.value = res
  })
}
// 获取广告类型
const getAdTypeList = () => {
  getAdFormatList().then(res => {
    adFormatList.value = res
  })
}
onMounted(() => {
  // getData()
  getSourceList()
  getAdTypeList()
})
const visibleDel = ref(false)
const advertiseRef = ref()
const openModal = (type: string) => {
  advertiseRef.value.openModal(type)
}
const adItemId = ref('')
const handleSelect = (v, record) => {
  if (v === 'edit') {
    advertiseRef.value.openModal(v, record)
  } else {
    // 
    adItemId.value = record.id
    visibleDel.value = true
  }
}
const deleteItem = async () => {
  try {
    await deleteAdItem(adItemId.value).then(res => {
      getData()
      visibleDel.value = false
      Message.success('删除成功')
    })
  } catch (error) {
    console.log(error);

  }

}
const updateData = () => {
  getData()
}
defineExpose({getData})
</script>

<template>
  <div class="pageCss">
    <div class="head">
      <a-input v-model:model-value="pageParams.text" style="width: 300px;background-color: #FFF" placeholder="搜索" @press-enter="getData">
        <template #prefix>
          <icon-search/>
        </template>
      </a-input>
      <div class="head-right">
        <a-select v-model="pageParams.adChannel" style="width:180px;border-radius: 4px;" placeholder="变现平台" allow-clear @change="getData">
          <template #label="{ data }">
            <span>变现平台-{{ data?.label }}</span>
          </template>
          <a-option v-for="item in sourceList" :key="item.code" :value="item.name">{{ item.name }}</a-option>
        </a-select>
        <a-button class="button" type="primary" @click="openModal('add')">
          <icon-plus style="margin-right: 3px"/>
          添加广告映射
        </a-button>
      </div>
    </div>
    <div class="body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true">
        <template #adStatus="{ record }">
          <a-switch v-model="record.adStatus" :checked-value="1" :unchecked-value="0" size="small"/>
        </template>
        <template #cpViewEnabled="{ record }">
          <a-tag v-if="record.cpViewEnabled" color="blue">是</a-tag>
          <a-tag v-else color="gray">否</a-tag>
        </template>
        <template #cpViewRate="{ record }">
          {{ record.cpViewRate }}%
        </template>
        <template #action="{ record }">
          <a-dropdown position="bl" @select="handleSelect($event,record)">
            <a-button class="setting">
              <template #icon>
                <icon-more-vertical/>
              </template>
            </a-button>
            <template #content>
              <a-doption value="edit">
                <icon-settings class="mr8"/>
                编辑
              </a-doption>
              <a-doption value="delete">
                <icon-delete class="mr8"/>
                删除
              </a-doption>
            </template>
          </a-dropdown>
        </template>
      </a-table>
    </div>
    <a-modal v-model:visible="visibleDel" width="300px" :hide-title="true" @cancel="visibleDel = false">
      <div>确认删除？该操作不可恢复</div>
      <template #footer>
        <a-button style="border-radius: 4px;" @click="visibleDel = false">关闭</a-button>
        <a-button style="border-radius: 4px;" type="primary" status="danger" @click="deleteItem">删除</a-button>
      </template>
    </a-modal>
    <advertiseModal ref="advertiseRef" :source-list="sourceList" :ad-format-list="adFormatList" @update-data="updateData"/>
  </div>
</template>

<style scoped lang="less">
.mr8 {
  margin-right: 8px;
}

.setting {
  width: 20px;
  height: 20px;
  background: transparent;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;
}

.pageCss {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .head {
    display: flex;
    min-height: 50px;
    align-items: center;
    justify-content: space-between;

    .head-right {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .button {
        border: none;
        border-radius: 5px;
        margin-left: 12px;
      }
    }
  }

  .body {
    height: 100%;
    width: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);
  }
}
</style>