<template>
    <!-- 更改主页 -->
    <a-modal v-model:visible="modalVisible" :width="500" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" :auto-label-width="true">
            <a-form-item field="fbId" label="FB个人号">
                <a-select v-model="form.fbId" placeholder="请选择">
                </a-select>
            </a-form-item>
            <a-form-item field="homepage" label="选择主页">
                <a-select v-model="form.homepage" placeholder="请选择">
                </a-select>
            </a-form-item>
            <a-form-item field="instagramId" label="Instagram 帐户">
                <a-select v-model="form.instagramId" placeholder="请选择">
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('更改主页')
const form = reactive({
    fbId:'',
    homepage:'',
    instagramId:''
})
const rules = {
    fbId: [
        {
            required: true,
            message:'请选择FB个人号',
        }
    ],
    homepage: [
        {
            required: true,
            message:'请选择主页',
        }
    ],
    instagramId: [
        {
            required: true,
            message:'请选择Instagram 帐户',
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>