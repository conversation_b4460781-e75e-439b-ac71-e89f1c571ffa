<template>
  <div class="banner">
    <div class="banner-inner">
      <a-carousel
          class="carousel"
          indicator-type="never"
          show-arrow="never"
          :arrow-class="arrowCss"
          animation-name="slide">
        <a-carousel-item v-for="item in carouselItem" :key="item.slogan">
          <div :key="item.slogan" class="carousel-item">
            <div class="carousel-title">{{ item.slogan }}</div>
            <div class="carousel-sub-title">{{ item.subSlogan }}</div>
            <img class="carousel-image" :src="item.image"  alt=""/>
          </div>
        </a-carousel-item>
      </a-carousel>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed} from 'vue';
import {useI18n} from 'vue-i18n';
import bannerImageThree from '@/assets/images/login-banner-three.webp';

const arrowCss={
    color:'black',
  }
  const { t } = useI18n();
  const carouselItem = computed(() => [
    {
      slogan: '数据洞察，增长飞轮',
      subSlogan: '以数据分析 + A/B测试为驱动，AI 赋能投流与运营，实现增长飞轮',
      image: [bannerImageThree]
    },
  ]);
</script>

<style lang="less" scoped>
  .banner {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &-inner {
      flex: 1;
      height: 100%;
    }
  }

  .carousel {
    height: 100%;

    &-item {
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;
      height: 100%;
    }

    &-title {
      color: #0c0d0e;
      font-size: 24px;
      font-weight: 500;
      // font-size: 20px;
      line-height: 32px;
    }

    &-sub-title {
      margin-top: 8px;
      color: var(--color-text-3);
      font-size: 14px;
      line-height: 22px;
    }

    &-image {
      width: 600px;
      margin-top: 30px;
    }
  }
</style>
