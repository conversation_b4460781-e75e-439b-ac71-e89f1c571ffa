import { App,Directive  } from 'vue';
import permission from './permission';

const viewport: Directive = {
  mounted(el, binding) {
    const cb = typeof binding.value === 'function' ? binding.value : () => {};
    const observer = new IntersectionObserver(
      (entries) => cb(entries[0].isIntersecting),
      { root: null, threshold: 0.1, rootMargin: '100px' }
    );
    (el as any).__observer = observer;
    observer.observe(el);
  },
  unmounted(el) {
    (el as any).__observer?.disconnect();
  },
};

export default {
  install(Vue: App) {
    Vue.directive('permission', permission);
    Vue.directive('viewport', viewport);
  },
};
