<template>
    <!-- 修改竞价策略 -->
    <a-modal v-model:visible="modalVisible" :width="640" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-tabs v-model:active-key="activeName">
            <a-tab-pane key="bidStrategy" title="竞价策略(1)">
                <a-form ref="formRef" :model="form" :auto-label-width="true" style="height: 420px;overflow: auto;">
                    <a-form-item field="nums" label="新竞价策略">
                        <a-select v-model="form.newBidStrategy" placeholder="请选择" allow-clear style="margin-right: 12px;width: 200px;" @change="handleBidStrategyChange">
                            <a-option value="1">最高数量</a-option>
                            <a-option value="2">竞价上限</a-option>
                        </a-select>
                        <a-input v-if="form.newBidStrategy === '2'" v-model="form.nums" :min="0" style="width: 200px;">
                            <template #append>
                                USD
                            </template>
                        </a-input>
                    </a-form-item>
                    <a-form-item field="tableData" :label-col-props="{ span: 0 }">
                        <a-table
                            :columns="columns"
                            :data="form.tableData"
                            :hoverable="true"
                            sticky-header
                            :table-layout-fixed="true"
                            :column-resizable="true"
                            :pagination="false"
                        >
                            <template #bidStrategy="{ rowIndex }">
                                <div style="display: flex;align-items: center;">
                                    <a-select v-model="form.tableData[rowIndex].bidStrategy" placeholder="请选择" style="margin-right: 12px;min-width: 168px;">
                                        <a-option value="1">最高数量</a-option>
                                        <a-option value="2">竞价上限</a-option>
                                    </a-select>
                                    <a-input v-if="form.tableData[rowIndex].bidStrategy === '2'" v-model="form.tableData[rowIndex].nums" allow-clear style="width: 168px;flex-shrink: 0;">
                                        <template #append>
                                            USD
                                        </template>
                                    </a-input>
                                </div>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="unable" title="不能修改(1)">
                <a-table
                    :columns="unableColumns"
                    :data="unableTableData"
                    :hoverable="true"
                    sticky-header
                    :table-layout-fixed="true"
                    :column-resizable="true"
                    :pagination="false"
                    style="height: 420px;"
                >
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改竞价策略')
const activeName = ref('bidStrategy')
const form = reactive({
    newBidStrategy:'1',
    nums:'',
    tableData:[
        {
            adsetName:'10093_MTG_ColorBlock_20241011',
            bidStrategy:'1',
        },
    ]
})
const columns = ref<any>([
    { title: '广告组名称', dataIndex: 'adsetName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '竞价策略', dataIndex: 'bidStrategy',slotName:'bidStrategy',width:400 },
])

const unableColumns = ref<any>([
    { title: '广告组名称', dataIndex: 'adsetName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '不可修改原因', dataIndex: 'reason',minWidth:180 },
])
const unableTableData = ref<any>([
    {
        adsetName:'10093_MTG_ColorBlock_20241011',
        reason:'广告单元包含已暂停的广告',
    },
])
const formRef = ref()

const emits = defineEmits(['updateData']);

const handleBidStrategyChange = (v) => {
    form.tableData.forEach(item => {
        item.bidStrategy = v
    })
}
const openModal = async () => {
    formRef.value.resetFields()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.tip{
    white-space: pre;
    text-align: right;
}
</style>