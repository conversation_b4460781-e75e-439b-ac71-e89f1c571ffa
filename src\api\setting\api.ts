import {AxiosPromise} from "axios";
import {getRequest, postRequest, uploadRequest} from "@/api/request";
import {EventAttrConfig, tableDateConfig, UserAttrConfig} from '@/api/setting/type'


// 保存菜单配置
export function saveMenuFilterConfig(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/menu/config/save', {...params});
}
// 获取菜单配置
export function getMenuFilterConfig(menuCode: string, urlParams?: string): AxiosPromise<any> {
  const requestParams: any = { menu_code: menuCode };
  if (urlParams) {
    requestParams.url_params = urlParams;
  }
  return getRequest<any>('/api/menu/config', requestParams);
}
export function getEventList(params: any): AxiosPromise<tableDateConfig> {
  return getRequest<any>('/api/data/event/list', params);
}

export function getEventDetail(code: string): AxiosPromise<tableDateConfig> {
  return getRequest<any>('/api/data/event/detail', {code});
}

// 元事件更新
export function saveEventList(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/event/save', {...config});
}

export function getEventAttrList(params: any): AxiosPromise<EventAttrConfig> {
  return getRequest<any>('/api/data/event/attribute/list', params);
}

export function getEventAttrDetail(code: string): AxiosPromise<EventAttrConfig> {
  return getRequest<any>('/api/data/event/attribute/detail', {code});
}

// 创建自定义事件
export function addCustomEvent(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/event/add', {...config});
}

// 事件属性保存
export function saveEventAttrList(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/event/attribute/save', {...config});
}

export function getUserAttrList(params): AxiosPromise<UserAttrConfig> {
  return getRequest<any>('/api/data/user/attribute/list', params);
}

// 用户属性保存
export function saveUserAttrList(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/attribute/save', {...config});
}
export function getUserAttrDetail(code: string): AxiosPromise<EventAttrConfig> {
  return getRequest<any>('/api/data/user/attribute/detail', {code});
}
export function getIndicatorList(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/data/indicator/list', params);
}

// 事件属性关联事件
export function getEventAttrLinkList(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/event/attribute/related_event', {code});
}

// 指标保存
export function saveIndicatorData(object: object): AxiosPromise<string> {
  return postRequest<string>('/api/data/indicator/save', object);
}

// 指标详情
export function getIndicatorDetail(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/indicator/detail', {code});
}

// 指标删除
export function removeIndicatorItem(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/indicator/remove', {code});
}

// 事件属性新增
export function eventAttrAdd(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/event/attribute/add', {...config});
}

// 用户属性新增
export function userAttrAdd(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/attribute/add', {...config});
}

// 用户属性聚合方式
export function getAggregateList(): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/attribute/aggregate');
}

// 用户分群条件新增
export function userGroupAdd(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/cluster/add', {...config});
}

// 用户分群条件更新
export function updateUserGroupItem(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/cluster/update', {...config});
}

// 用户分群列表
export function getUserGroupList(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/cluster/list', params);
}

// 删除用户分群单个
export function delUserGroupItem(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/cluster/remove', {code});
}

// 用户分群单个
export function getUserGroupDetail(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/cluster/detail', {code});
}

// 分析主体列表
export function getAnalyzeSubjectList(sources?:any): AxiosPromise<any> {
  const params = new URLSearchParams();
  sources?.forEach(source => {
    params.append('source', source);
  });
  return getRequest<string>(`/api/data/analyze/subject/list?${params.toString()}`, {});
}

// 用户标签条件新增
export function userTagAdd(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/tag/add', {...config});
}

// 用户标签条件更新
export function updateUserTagItem(config: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/tag/update', {...config});
}

// 用户标签列表
export function getUserTagList(): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/tag/list');
}

// 用户标签单个
export function getUserTagDetail(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/tag/detail', {code});
}

// 删除用户标签单个
export function delUserTagItem(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/user/tag/remove', {code});
}

// 获取系统日志
export function getSystemAccessLog(pageParams: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/access_log/page/list', pageParams);
}

// 获取系统菜单
export function getSystemMenu(params?: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/menu/list', params);
}

// 保存系统菜单
export function saveSystemMenu(menuData: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/menu/save', menuData);
}

// 删除系统菜单
export function removeSystemMenu(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/menu/remove', {code: code});
}

// 获取系统角色
export function getSystemRole(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/role/list', params);
}

// 保存系统角色
export function saveSystemRole(roleData: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/role/save', roleData);
}

// 删除系统角色
export function removeSystemRole(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/role/remove', {code: code});
}

// 获取系统应用分组
export function getSystemMemberGroup(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/member/group/list', params);
}

// 获取系统应用分组（树状）
export function getSystemMemberGroupTree(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/member/group/tree', params);
}

// 保存系统应用分组
export function saveSystemMemberGroup(groupData: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/member/group/save', groupData);
}

// 删除系统应用分组
export function removeSystemMemberGroup(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/member/group/remove', {code: code});
}

// 获取系统用户
export function getSystemMember(params: any): AxiosPromise<any> {
  return getRequest<any>('/api/sys/member/list', params);
}

// 保存系统用户
export function saveSystemMember(data: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/member/save', data);
}

// 删除系统用户
export function removeSystemMember(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/member/remove', {code: code});
}

export function uploadMetadata(type: string, file: any): AxiosPromise<any> {
  const formData = new FormData();
  formData.append('file', file.file);  // 添加文件
  return uploadRequest<any>('/api/data/event/upload?upload_type=' + type, formData);
}


// Flink作业列表
export function getOceanusList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/service/oceanus/list', {});
}
// Flink作业详情
export function getOceanusInfo(id:string): AxiosPromise<any> {
  return getRequest<any>(`/api/sys/service/oceanus/info?job_id=${id}`, {});
}
// Flink作业新增 ｜ 更新
export function saveOceanusData(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/service/oceanus/save', params);
}
// 事件删除
export function deleteEventList(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/data/event/del?code=${code}`, {});
}
// 事件属性删除
export function deleteEvtAttrList(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/data/event/attribute/del?code=${code}`, {});
}
// 用户属性删除
export function deleteUserAttrList(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/data/user/attribute/del?code=${code}`, {});
}
// 账户授权列表
export function getAppsflyerList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/appsflyer/list', {});
}


// Appsflyer授权相关接口
// 新增账户授权
export function addAppsflyer(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/appsflyer/add', params);
}
// 更新账户授权
export function updateAppsflyer(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/appsflyer/update', params);
}
// 删除账户授权
export function deleteAppsflyer(account: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/appsflyer/delete', {account: account});
}
// 账户授权详情
export function getAppsflyerInfo(account: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/appsflyer/info', {account: account});
}
// 授权应用列表
export function getAppsflyerAppList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/appsflyer/app/list', {});
}
// 更新应用状态
export function updateAppsflyerApp(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/appsflyer/app/update', params);
}
// ga4账户授权列表 
export function getGaList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/ga4/list', {});
}
export function addGa(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/ga4/add', params);
}
// 更新ga4账户授权
export function updateGa(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/ga4/update', params);
}
// 删除ga4账户授权
export function deleteGa(email: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/ga4/delete', {clientEmail: email});
}
// ga4账户授权详情
export function getGaInfo(email: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/ga4/info', {clientEmail: email});
}
// ga4账户应用列表 
export function getGaAppList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/auth/mmp/ga4/app/list', {});
}
// ga4更新应用状态
export function updateGaApp(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/auth/mmp/ga4/app/update', params);
}

// 维度表列表
export function getDimensionList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/data/dimension/list', {});
}
// 维度表保存
export function saveDimension(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/data/dimension/save', params);
}
// 维度表删除
export function deleteDimension(code: string): AxiosPromise<any> {
  return getRequest<any>(`/api/sys/data/dimension/remove?table_code=${code}`, {});
}
// 维度表属性列表
export function getDimensionAttrList(code: string): AxiosPromise<any> {
  return getRequest<any>(`/api/sys/data/dimension/attribute/list?table_code=${code}`, {});
}
// 维度表属性保存
export function saveDimensionAttr(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/sys/data/dimension/attribute/save', params);
}
// 维度表属性删除
export function deleteDimensionAttr(code: string): AxiosPromise<any> {
  return getRequest<any>(`/api/sys/data/dimension/attribute/remove?code=${code}`, {});
}
// 维度表数据列表
export function getDimensionDataList(code: string): AxiosPromise<any> {
  return getRequest<any>(`/api/sys/data/dimension/data?table_code=${code}`, {});
}

// 用户信息详情请求
export function getUserInfoList(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/info/list', params);
}

// 用户信息详情请求
export function getUserInfoDetail(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/info/detail', params);
}

// 用户信息详情请求
export function getUserInfoEventList(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/user/info/event/list', params);
}
// 用户id列表
export function getUserInfoIdList(code: string): AxiosPromise<any> {
  return getRequest<any>(`/api/data/user/info/id/list?code=${code}`, {});
}
// meta
// 获取fb授权登录地址
export function getAuthFbUrl(): AxiosPromise<any> {
  return getRequest<any>('/api/placement/auth/facebook/login', {});
}
// 获取用户可授权的广告账户列表
export function getAuthFbAccountList(userId:string): AxiosPromise<any> {
  return getRequest<any>(`/api/placement/auth/facebook/all_ad_accounts?facebook_user_id=${userId}`, {});
}
// 更新用户广告账户状态
export function updateAuthFbAccount(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/placement/auth/facebook/modify/ad_accounts', params);
}
// 获取所有授权用户
export function getAllAuthFbUserList(): AxiosPromise<any> {
  return getRequest<any>('/api/placement/auth/facebook/users', {});
}
// 获取所有授权广告账户
export function getAllAuthFbAccountList(): AxiosPromise<any> {
  return getRequest<any>('/api/placement/auth/facebook/ad_accounts', {});
}