<script setup lang="ts">
import {computed, ref, watch} from "vue";
import draggable from 'vuedraggable'

interface screenConfig {

  name: string,
  attr?: string,
  type?: string,
  groupType?: string
  id: string
  collectType: boolean
}

// 模拟数据
const EventLists = ref<screenConfig[]>([
  {name: '活动类型', type: '文本', id: '1', collectType: false},
  {name: 'app版本', type: '文本', id: '2', collectType: false},
  {name: '关卡ID', type: '数值', id: '3', collectType: false},
  {name: '资源_关卡ID', attr: '维度表属性', type: '数值', id: '3-1', collectType: false},
  {name: '应用包名', type: '文本', id: '4', collectType: false},
  {name: '出场卡牌ID', type: '数值', id: '5', collectType: false},
  {name: '活动类型-分支', type: '文本', id: '1-1', collectType: false},
  {name: 'app版本-分支', type: '文本', id: '2-1', collectType: false},
  {name: '质量_关卡ID', attr: '维度表属性', type: '数值', id: '3-2', collectType: false},
  {name: '应用包名-分支', type: '文本', id: '4-1', collectType: false},
  {name: '出场卡牌ID-分支', type: '数值', id: '5-1', collectType: false},
])
const UserLists = ref<screenConfig[]>([
  {name: '账户ID', type: '文本', id: '1', collectType: false},
  {name: '广告ID', type: '文本', id: '2', collectType: false},
  {name: '广告组ID', type: '文本', id: '3', collectType: false},
  {name: '广告Campaign', attr: '维度表属性', type: '文本', id: '3-1', collectType: false},
  {name: '来源渠道', type: '文本', id: '4', collectType: false},
  {name: '当前等级', type: '数值', id: '5', collectType: false},
  {name: '访客ID', type: '文本', id: '1-1', collectType: false},
  {name: '首次登陆等级', type: '文本', id: '2-1', collectType: false},
  {name: '首次付费等级', type: '数值', id: '3-2', collectType: false},
  {name: '最后登录时间', type: '时间', id: '4-1', collectType: false},
  {name: '当前VIP等级', type: '数值', id: '5-1', collectType: false},

])
const UserGroup = ref<screenConfig[]>([
  {name: '分群20230814_112034', id: '1', groupType: 'ID分群', collectType: false},
  {name: 'llll000', id: '2', groupType: '条件分群', collectType: false},
  {name: '分群好好好', id: '3', groupType: '条件分群', collectType: false},
  {name: '妇女群20230717', id: '3-1', groupType: 'ID分群', collectType: false},
  {name: '做过复位', id: '4', groupType: '条件分群', collectType: false},
  {name: '分群20240103', id: '5', groupType: 'ID分群', collectType: false},
  {name: '符合分群条件人群', id: '1-1', groupType: '条件分群', collectType: false},
  {name: '高粘性', id: '2-1', groupType: '条件分群', collectType: false},
  {name: '有过首胜', id: '3-2', groupType: '条件分群', collectType: false},
  {name: '注册次日未登录', id: '4-1', groupType: '条件分群', collectType: false},
  {name: '长期登录', id: '5-1', groupType: '条件分群', collectType: false},
])
const UserTag = ref<screenConfig[]>([
  {name: '付费RMF标签', type: '文本', id: '1', collectType: false},
  {name: '用户的付费分层标签', type: '文本', id: '2', collectType: false},
  {name: 'facebook末次成本事件', type: '时间', id: '3', collectType: false},
  {name: '标签-付费时间时间', type: '时间', id: '3-1', collectType: false},
  {name: '创角8~14天内登录次数', type: '数值', id: '4', collectType: false},
  {name: '创角7天内登陆天数', type: '数值', id: '5', collectType: false},
  {name: '最后参与的核心玩法', type: '文本', id: '1-1', collectType: false},
  {name: '首次付费天数', type: '数值', id: '2-1', collectType: false},
  {name: '活跃天数', type: '数值', id: '3-2', collectType: false},
  {name: '近30天充值总额', type: '数值', id: '4-1', collectType: false},
  {name: '自定义标签', type: '文本', id: '5-1', collectType: false},
])
const ChooseLists = ref([])
const collectLists = ref([])
// 铆钉
const AllRef = ref()
const UserTagRef = ref()
const UserGroupRef = ref()
const EventListsRef = ref();
const UserListsRef = ref();
const searchQuery = ref('')


function isMatch(item, query) {
  const lowerCaseQuery = query.toLowerCase();
  // 将查询字符串转换为正则表达式模式，以便匹配不相连的字符
  const regexPattern = lowerCaseQuery.split('').join('.*?');
  const regex = new RegExp(regexPattern);
  return regex.test(item.name.toLowerCase());
}

const filteredEventLists = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  // 如果查询为空，返回原始数据
  if (!query) {
    return EventLists.value;
  }
  return EventLists.value.filter(item => isMatch(item, query));
});

const filteredUserLists = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  if (!query) {
    return UserLists.value;
  }
  return UserLists.value.filter(item => isMatch(item, query));
});

const filteredUserGroup = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  if (!query) {
    return UserGroup.value;
  }
  return UserGroup.value.filter(item => isMatch(item, query));
});

const filteredUserTag = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  if (!query) {
    return UserTag.value;
  }
  return UserTag.value.filter(item => isMatch(item, query));
});

const triggerVisible = ref<boolean>(false)
const screenVisible = defineModel<boolean>('visible')
const handleCancel = () => {
  screenVisible.value = false
}
const handleOk = () => {
  screenVisible.value = false
}

const handleTriggerVisible = () => {
  triggerVisible.value = !triggerVisible.value;
}
// 移除
const removeAt = (idx: number) => {
  ChooseLists.value.splice(idx, 1);
}
// 增加
const handleEvent = (v: string) => {

  ChooseLists.value.push(v)
  triggerVisible.value = false

}
// 收藏
const collectEvent = (e) => {
  collectLists.value.push(e)
  e.collectType = true
}
// 取消收藏
const uncollectEvent = (e) => {
  e.collectType = false
  const a = collectLists.value.indexOf(e)
  collectLists.value.splice(a, 1)
}
</script>

<template>
  <a-modal
      :width="582"
      title-align="start"
      :visible="screenVisible"
      ok-text="应用至空间"
      :mask-closable="false"
      @ok="handleOk"
      @cancel="handleCancel">

    <template #title>
      <div class="screen-header">
        <div>设置空间页面筛选</div>
        <a-tooltip
            content="设置后可在空间下的所有看板使用，更改一个看板上空间页面筛选的条件时，也会对空间下其他看板生效"
            position="right">
          <icon-exclamation-circle/>
        </a-tooltip>
      </div>
    </template>
    <div class="screen-body">
      <a-trigger
          v-model:popup-visible="triggerVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          @click="handleTriggerVisible">
        <button class="screen-bt">+ 添加筛选字段</button>
        <template #content>
          <div class="screen-content">
            <div class="screen-content-header">
              <div>
                <icon-search :size="17"/>
              </div>
              <div style="width: 100%">
                <input
                    v-model="searchQuery"
                    placeholder="请输入搜索"
                    class="screen-input"/>
              </div>
            </div>
            <div>
              <a-tabs
                  :destroy-on-hide="true"
                  default-active-key="1" size="mini" class="screen-content-body">
                <a-tab-pane key="1" title="全部">
                  <div ref="AllRef" class="screen-tabHeight">
                    <a-affix
                        :offset-top="0"
                        :target="AllRef">
                      <div class="affix-eventName">
                        <img src="/icon/collect.svg">
                        收藏
                      </div>
                    </a-affix>
                    <div v-if="collectLists.length>0">
                      <div v-for="(event,index) in collectLists" :key="index">
                        <a-doption :disabled='ChooseLists.includes(event.name)'>
                          <div class="screen-option">
                            <div @click="handleEvent(event.name)"
                                 class="screen-option">
                              <div class="screen-option-name">
                                {{ event.name }}
                              </div>
                              <div class="screen-option-attr">
                                {{ event.attr }}
                              </div>
                              <div class="screen-option-type">
                                <div v-if="event.type==='文本'" class="type-text">
                                  <img src="/icon/text.svg">
                                  {{ event.type }}
                                </div>
                                <div v-else-if="event.type==='数值'" class="type-text">
                                  <img src="/icon/number.svg">
                                  {{ event.type }}
                                </div>
                                <div v-else-if="event.type==='布尔'" class="type-text">
                                  <img src="/icon/boolean.svg">
                                  {{ event.type }}
                                </div>
                                <div v-else-if="event.type==='时间'" class="type-text">
                                  <img src="/icon/time.svg">
                                  {{ event.type }}
                                </div>
                              </div>
                            </div>
                            <div class="screen-option-collect">
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </a-doption>
                      </div>
                    </div>
                    <div v-else style="color:var(--tant-text-gray-color-text1-4) " class="affix-eventName">
                      <span>点击选项右侧</span>
                      <img src="/icon/black-star.svg" alt="">
                      <span>添加收藏</span>
                    </div>

                    <a-affix
                        :offset-top="0"
                        :target="AllRef">
                      <div class="affix-eventName">
                        <img src="/icon/eventAttr.svg">
                        事件属性
                      </div>
                    </a-affix>
                    <div v-for="(event,index) in filteredEventLists" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div class="screen-option" @click="handleEvent(event.name)">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                    <a-affix
                        :offset-top="0"
                        :target="AllRef">
                      <div class="affix-eventName">
                        <img src="/icon/userAttr.svg">
                        用户属性
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserLists" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div @click="handleEvent(event.name)" class="screen-option">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                    <a-affix
                        :offset-top="0"
                        :target="AllRef">
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户分群
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserGroup" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div @click="handleEvent(event.name)" class="screen-option">

                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-group">
                              {{ event.groupType }}
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>

                      </a-doption>
                    </div>
                    <a-affix
                        :offset-top="0"
                        :target="AllRef">
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户标签
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserTag" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div class="screen-option" @click="handleEvent(event.name)">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="2" title="事件属性">
                  <div ref="EventListsRef" class="screen-tabHeight">
                    <a-affix
                        :offset-top="0"
                        :target="EventListsRef">
                      <div class="affix-eventName">
                        <img src="/icon/eventAttr.svg">
                        事件属性
                      </div>
                    </a-affix>
                    <div v-for="event in filteredEventLists" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div @click="handleEvent(event.name)" class="screen-option">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="3" title="用户属性">
                  <div ref="UserListsRef" class="screen-tabHeight">
                    <a-affix
                        :offset-top="0"
                        :target="UserListsRef">
                      <div class="affix-eventName">
                        <img src="/icon/userAttr.svg">
                        用户属性
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserLists" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div @click="handleEvent(event.name)" class="screen-option">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="4" title="用户分群">
                  <div ref="UserGroupRef" class="screen-tabHeight">
                    <a-affix
                        :offset-top="0"
                        :target="UserGroupRef">
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户分群
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserGroup" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div @click="handleEvent(event.name)" class="screen-option">
                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-group">
                              {{ event.groupType }}
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="5" title="用户标签">
                  <div ref="UserTagRef" class="screen-tabHeight">
                    <a-affix
                        :offset-top="0"
                        :target="UserTagRef">
                      <div class="affix-eventName">
                        <img src="/icon/usergroup.svg">
                        用户标签
                      </div>
                    </a-affix>
                    <div v-for="event in filteredUserTag" :key="event.id">
                      <a-doption :disabled='ChooseLists.includes(event.name)'>
                        <div class="screen-option">
                          <div class="screen-option" @click="handleEvent(event.name)">

                            <div class="screen-option-name">
                              {{ event.name }}
                            </div>
                            <div class="screen-option-attr">
                              {{ event.attr }}
                            </div>
                            <div class="screen-option-type">
                              <div v-if="event.type==='文本'" class="type-text">
                                <img src="/icon/text.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='数值'" class="type-text">
                                <img src="/icon/number.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='布尔'" class="type-text">
                                <img src="/icon/boolean.svg">
                                {{ event.type }}
                              </div>
                              <div v-else-if="event.type==='时间'" class="type-text">
                                <img src="/icon/time.svg">
                                {{ event.type }}
                              </div>
                            </div>
                          </div>
                          <div class="screen-option-collect">
                            <div v-if="!event.collectType">
                              <a-tooltip content="点击收藏" position="top" mini>
                                <img src="/icon/uncollect.svg" class="uncollect-button"
                                     @click="collectEvent(event)">
                              </a-tooltip>
                            </div>
                            <div v-else>
                              <a-tooltip content="取消收藏" position="top" mini>
                                <img src="/icon/collect.svg" class="collect-button"
                                     @click="uncollectEvent(event)">
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-doption>
                    </div>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
        </template>
      </a-trigger>
      <div class="screen-menu">
        <div class="screen-menuin">
          <draggable
              tag="ul"
              :list="ChooseLists"
              handle=".items-img"
              item-key="name"
          >
            <template #item="{ element, index }">
              <li class="items">
                <div class="items-img">
                  <img src="/icon/drag.svg">
                </div>
                <div class="items-text">
                  空间
                </div>
                <div class="items-name">
                  {{ element }}
                </div>
                <div class="items-input">
                  <a-select
                      allow-clear
                      :style="{width:'240px'}"
                      placeholder="不限">
                    <a-option>属于分群</a-option>
                    <a-option>不属于分群</a-option>
                  </a-select>
                </div>
                <div class="items-delete">
                  <a-tooltip content="删除" position="top" mini>
                    <button class="delete-button" @click="removeAt(index)">
                      <icon-delete/>
                    </button>
                  </a-tooltip>
                </div>
              </li>
            </template>
          </draggable>
        </div>

      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.screen-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.screen-body {

  .screen-menu {
    background-color: var(--tant-bg-gray-color-bg2-1);
    height: 376px;
    margin-top: 26px;
    overflow: auto;

    .screen-menuin {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      height: max-content;
      min-height: 376px;
      padding-top: 8px;
      background-color: var(--tant-fill-color-fill1-2);
      border-radius: 4px;

      .items {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 470px;
        height: 48px;
        margin-top: 16px;
        margin-bottom: 16px;
        background: var(--tant-bg-white-color-bg1-1);
        border-radius: 2px;

        .items-content {
          display: flex;
          flex-direction: row;
          align-self: center;
          width: 90%;
          align-items: center;
        }

        .items-img {
          display: flex;
          margin-left: 12px;
          cursor: move;
          align-items: center;

        }

        .items-text {
          margin-left: 8px;
          background-color: var(--tant-status-info-color-info-fill);
          color: var(--tant-status-info-color-info-default);
          font: var(--tant-description-font-description-regular);
          padding: 0 4px;
          height: 18px;
          display: inline-block;
          line-height: 18px;
        }

        .items-name {
          width: max-content;
          max-width: 101px;
          margin-left: 8px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .items-input {
          display: flex;
          align-items: center;
          width: 240px;
          margin-left: 8px;
        }

        .items-delete {
          margin-left: auto;
          padding: 0;

          .delete-button {
            display: none;
            border: none;
            background-color: var(--tant-bg-white-color-bg1-1);
            padding: 8px;
          }

          .delete-button:hover {
            color: var(--tant-status-danger-color-danger-default);
            background-color: var(--tant-status-danger-color-danger-fill-hover);
            transition: .3s;

          }
        }

      }

      .items:hover {
        .delete-button {
          display: inline-block;
          border: none;
          background-color: var(--tant-bg-white-color-bg1-1);
          padding: 8px;
          margin-right: 5px;
        }
      }
    }
  }
}

.screen-bt {
  color: var(--tant-text-gray-color-text1-2);
  text-shadow: none;
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
  box-shadow: none;
  font-size: 14px;
  padding: 5px 16px;
}

.screen-bt:hover {
  cursor: pointer;
  background-color: var(--tant-bg-gray-color-bg2-1);
  transition: .3s;
}


.screen-content {
  width: 360px;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  .screen-content-header {
    display: flex;
    height: 40px;
    width: 100%;
    align-items: center;
    padding: 0 8px;
    gap: 7px;
    border-bottom: solid 1px var(--tant-border-color-border1-1);

    .screen-input {
      height: 30px;
      padding: 0;
      width: 100%;
      color: var(--tant-text-gray-color-text1-2);
      line-height: 28px;
      text-overflow: ellipsis;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
      outline: none;
      box-shadow: none;
    }
  }

  .screen-content-body {
    margin: 0 8px;

    .screen-tabHeight {
      height: 350px;
      overflow: auto
    }

    .screen-option {
      display: flex;

      .screen-option-group {
        display: inline-block;
        padding-left: 60px;
        width: 130px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 10px;

      }

      .screen-option-name {
        display: inline-block;
        width: 150px;
        text-align: left;
      }

      .screen-option-attr {
        display: inline-block;
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
      }

      .screen-option-type {
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;

        .type-text {
          display: flex;
          align-self: center;
        }
      }

      .screen-option-collect {
        display: flex;
        align-items: center;
        width: 30px;
        padding: 0;

        .uncollect-button {
          display: none;

        }

        .collect-button {
          display: flex;
          align-self: center;
          border: none;
          margin-right: 5px;
          color: var(--tant-text-gray-color-text1-3);
        }
      }
    }

    .screen-option:hover {
      .uncollect-button {
        display: flex;
        align-self: center;
        border: none;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-3);

      }
    }
  }

}

.affix-eventName {
  display: flex;
  align-self: center;
  background-color: var(--tant-bg-white-color-bg1-1);
  color: var(--tant-text-black-color-text1-1);
  padding: 12px;
  width: 310px;
}


</style>