<script setup lang="ts">
import {ref, watch} from "vue";
import {DashboardDto} from "@/api/dashboard/type";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import _ from "lodash";
import {CacheEventBus, DeleteEvent, RefreshEvent, TreeMenuEventBus, UpdateReportCountEvent} from "@/types/event-bus";
import {removeDashboard, saveDashboard} from "@/api/dashboard/api";
import {Message} from "@arco-design/web-vue";
import {useDashboardStore} from "@/store";
import boardShare from "@/components/navbar-board/board-share.vue"
import router from '@/router';
import {useRoute} from 'vue-router';
import moveDashboard from "./move-dashboard.vue"
import copyDashboard from "./copy-dashboard.vue";


interface Props {

  /**
   * 所属空间
   */
  spaceId?: string

  /**
   * 所属文件夹
   */
  folderId?: string

  /**
   * 看板
   */
  dashboard?: DashboardDto
  allowAdd?: boolean | undefined
}


const props = defineProps<Props>()
const route = useRoute();
const emit = defineEmits(['add']);

const reportCount = ref(props.dashboard?.reportCount)
const selected = ref<boolean>(false)
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: "",authority:null})
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const dashboardStore = useDashboardStore()
const cacheEventBus = useEventBus(CacheEventBus)
const updateCountBus = useEventBus(UpdateReportCountEvent)
// 监听store的看板选择来决定loading
watch(() => dashboardSelected, (newVal) => {
      selected.value = newVal.value && newVal.value.dashboardId === props.dashboard?.dashboardId && newVal.value.spaceId === props.spaceId
    },
    {immediate: true,deep:true}
);


const selectDashboard = () => {
  if (!props.dashboard) {
    return
  }
  dashboardSelected.value = {
    spaceId: props.spaceId,
    folderId: props.folderId,
    dashboardId: props.dashboard.dashboardId,
    authority: props.dashboard?.authority
  }
  dashboardStore.dashboardSelected = {
    spaceId: props.spaceId,
    folderId: props.folderId,
    dashboardId: props.dashboard.dashboardId
  }
  selected.value = true
  // 过滤掉空值
  const filteredParams: any = {};
  Object.keys(dashboardSelected.value).forEach((key) => {
    const value = (dashboardSelected.value as any)[key];
    if (value !== null && value !== undefined && value !== '') {
      filteredParams[key] = value;
    }
  });
  router.push({
    name: route.name as string,
    query: filteredParams,
  });
}
/**
 * 删除\重命名\复制\移动操作
 */

const myRenameSelected = ref<string|undefined>(props.dashboard?.name)
const moveModalShow = ref(false)
const copyModalShow = ref(false)
const deleteModalShow = ref(false);
const renameModalShow = ref(false);
/**
 * 看板操作
 */
const shareRefs = ref()
const handleDashboardOperation = (value: string) => {
  switch (value) {
    case "share":
      shareRefs.value.openModal(props.dashboard?.dashboardId)
      break
    case "move":
      moveModalShow.value = true
      break
    case "copy":
      copyModalShow.value = true
      break
    case "rename":
      renameModalShow.value = true
      break
    case "delete":
      deleteModalShow.value = true
      break
    default:
  }
}
const deleteCache = (dashboardId: string) => {
  dashboardStore.spaceList.some(space => {
    // 在 folders 中查找并删除目标 dashboard
    const foundInFolder = space.folders.some((folder: any) => {
      const dashboardIndex = folder.dashboards.findIndex(
          (dashboard: any) => dashboard.dashboardId === dashboardId
      );
      if (dashboardIndex !== -1) {
        // 找到并从 folder 中删除 dashboard
        folder.dashboards.splice(dashboardIndex, 1);
        return true; // 找到并删除后退出内部 some 循环
      }
      return false; // 未找到则继续
    });

    // 如果在 folders 中未找到，再检查 space 顶层的 dashboards
    if (!foundInFolder) {
      const dashboardIndex = space.dashboards.findIndex(
          (dashboard: any) => dashboard.dashboardId === dashboardId
      );
      if (dashboardIndex !== -1) {
        // 找到并从 space 顶层删除 dashboard
        space.dashboards.splice(dashboardIndex, 1);
        return true; // 找到并删除后退出循环
      }
    }

    return false; // 未找到则继续
  });

}
cacheEventBus.on((event, {type, dashboardId,}) => {
  if (event === DeleteEvent && type === 'dashboard')
    deleteCache(dashboardId)
  }
)
updateCountBus.on((event, { dashboardId, count}) => {
  if (event === UpdateReportCountEvent && dashboardId === props.dashboard?.dashboardId) {
    reportCount.value = count;
  }
})
const deleteConfirm = () => {

  if (props.dashboard?.dashboardId) {
    removeDashboard([props.dashboard?.dashboardId]).then((resp) => {
      if (dashboardStore.dashboardSelected?.dashboardId === props.dashboard?.dashboardId) {
        treeMenuEventBus.emit(RefreshEvent)
      } else {
        // 缓存删除
        cacheEventBus.emit('delete-event', {
          type: 'dashboard',
          dashboardId: props.dashboard?.dashboardId,
        });
      }
    }).catch((e) => {
      Message.error("删除看板失败！", e)
    })
  }
  deleteModalShow.value = false;
};

const deleteCancel = () => {
  deleteModalShow.value = false;
}

const renameConfirm = () => {
  if (props.dashboard?.dashboardId) {
    if (_.isEmpty(myRenameSelected.value)) {
      Message.warning('看板名称不能为空');
      return;
    }
    saveDashboard({name: myRenameSelected.value, dashboardId: props.dashboard.dashboardId})
        .then((resp) => {
          // 缓存重命名
          cacheEventBus.emit('rename-event', {
            type: 'dashboard',
            dashboardId: props.dashboard?.dashboardId,
            name: myRenameSelected.value
          });
          Message.success('编辑成功')
        })
        .catch((e) => {
          Message.error("重命名失败！", e);
        });
  } else {
    Message.error(' ID 无效');
  }
  renameModalShow.value = false;

};
// 监听缓存数据重命名修改
watch(() => dashboardStore.spaceList, () => {
  dashboardStore.spaceList.some(space => {
    // 在 folders 中查找目标 dashboard 并重命名
    const foundInFolder = space.folders.some((folder: any) => {
      const remakeDashboard = folder.dashboards.find(
          (dashboard: any) => dashboard.dashboardId === props.dashboard?.dashboardId
      );
      if (remakeDashboard) {
        // 找到后更新名称
        myRenameSelected.value = remakeDashboard.name;
        return true; // 找到后退出内部 some 循环
      }
      return false; // 未找到则继续
    });

    // 如果在 folders 中未找到，再检查 space 顶层的 dashboards
    if (!foundInFolder) {
      const remakeDashboard = space.dashboards.find(
          (dashboard: any) => dashboard.dashboardId === props.dashboard?.dashboardId
      );
      if (remakeDashboard) {
        // 找到后更新名称
        myRenameSelected.value = remakeDashboard.name;
        return true; // 找到后退出循环
      }
    }

    return false; // 未找到则继续
  });

}, {deep: true})
const renameCancel = () => {
  myRenameSelected.value= props.dashboard?.name
  renameModalShow.value = false;
}
const showIcon = ref(false)
const popupVisibleChange = (v: boolean) => {
  showIcon.value = v
}

</script>

<template>
  <div class="container">
    <div
        :class="{
        'dashboard': true,
        'dashboard-selected': selected,
        'dashboard-myspace': props.dashboard?.space.authority===1}"
        :style="{paddingLeft: '56px',backgroundColor:showIcon?'var(--tant-secondary-color-secondary-fill-hover)':''}"
        @click="selectDashboard">
      <div class="name">
        <div class="icon">
          <img v-if="selected" src="/icon/file-selected.svg" alt=""/>
          <img v-else src="/icon/file.svg" alt=""/>
        </div>
        <div class="dashboard-name">
          {{ dashboard?.name }}
          <span v-if="reportCount === 0 || reportCount === null" style="color:#ccc">(空)</span>
        </div>
      </div>
      <div class="operation" :style="showIcon?'visibility: visible;':''">
        <a-dropdown
            class="dropdown" position="bl" @click.stop @popup-visible-change="popupVisibleChange"
            @select='handleDashboardOperation'>
          <a-button
              type="text" class="create-btn"
              :style="showIcon?'background-color: var(--tant-secondary-color-secondary-transp-hover);':''">
            <template #icon>
              <icon-more-vertical size="15"/>
            </template>
          </a-button>
          <template #content>
            <a-doption v-if="props.dashboard?.authority !==4" value="share">分享</a-doption>
            <a-doption v-if="props.dashboard?.authority !==4" value="rename">重命名</a-doption>
            <a-doption v-if="props.dashboard?.authority !==4 && props?.allowAdd !== false" value="move">移动至</a-doption>
            <a-doption value="copy">复制</a-doption>
            <a-doption v-if="props.dashboard?.authority !==4 && props?.allowAdd !== false" class="delete-option" value="delete">删除</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <move-dashboard
        v-if="moveModalShow"
        v-model:visible="moveModalShow"
        :dashboard="props.dashboard"
    />
    <copy-dashboard
        v-if="copyModalShow"
        v-model:visible="copyModalShow"
        :copy-name="dashboard?.name"
        :copy-id="dashboard?.dashboardId"/>
    <a-modal
        :visible="renameModalShow"
        width="452px"
        :mask-closable="false"
        ok-text="重命名" title-align="start"
        @ok="renameConfirm"
        @cancel="renameCancel">
      <template #title>
        重命名看板
      </template>
      <div class="rename-input">
        <a-input
            v-model="myRenameSelected"
            :style="{width:'388px'}"
            placeholder="请输入看板名称"
            allow-clear/>
      </div>
    </a-modal>

    <a-modal
        v-model:visible="deleteModalShow"
        width="auto"
        :mask-closable="false"
        :ok-button-props="{status:'danger'}"
        :closable="true" ok-text="删除" title-align="start"
        @ok="deleteConfirm"
        @cancel="deleteCancel">
      <template #title>
        删除看板
      </template>
      确认删除【{{ dashboard?.name }}】？ 该操作不可恢复。
    </a-modal>
    <boardShare ref="shareRefs"/>
  </div>
</template>

<style scoped lang="less">
.dashboard-myspace {
  padding: 0 8px 0 40px!important;
}

.dashboard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 8px 0 56px;
  border-radius: 4px;
  cursor: pointer;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
    flex: 1;

    .icon {
      height: 16px;
      margin-right: 8px;
    }
    .dashboard-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      flex: 1;  // 让文本区域占据剩余空间
    }
  }

  .operation {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-right: 8px;
    visibility: hidden;

    .create-btn {
      height: 24px;
      width: 24px;
      color: var(--tant-secondary-color-secondary-default);
      font: var(--tant-body-font-body-regular);
      text-transform: capitalize;
      border-radius: var(--tant-border-radius-medium);
      margin-left: 8px;
    }
  }
}

.dashboard-selected {
  background-color: var(--tant-primary-color-primary-fill) !important;
}

.dashboard:hover {
  background-color: var(--tant-secondary-color-secondary-fill-hover);
}

.create-btn:hover {
  background-color: var(--tant-secondary-color-secondary-transp-hover);
}

.dashboard:hover .operation {
  visibility: visible;
}

.delete-option:hover {
  background-color: var(--tant-status-danger-color-danger-fill);
  color: var(--tant-status-danger-color-danger-default);
}

.rename-input {
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-body {
  padding: 8px 32px 32px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;

  .copy-body-name {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    //padding-bottom: 16px;
    .copy-body-text {
      flex: 0 0 auto;
      margin-right: 16px;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-body-font-body-regular);
      text-align: right;
    }

    .copy-input {
      flex: 1 0 auto;
      width: 316px;
      max-width: 316px;
    }
  }

  .copy-body-place {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 48px;
    //padding-bottom: 16px;
    .copy-body-name {
      flex: 0 0 auto;
      margin-right: 16px;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-body-font-body-regular);
      text-align: right;
    }

    .copy-body-radio {
      flex: 1 0 auto;
      width: 316px;
      max-width: 316px;

      .radio-group {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        color: rgba(0, 0, 0, .85);
        font-size: 14px;
        font-variant: tabular-nums;
        line-height: 1.5715;
        list-style: none;
        font-feature-settings: "tnum", "tnum";
        display: inline-block;

      }
    }
  }

  .copy-body-choose {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 48px;

    .choose-select {
      flex: 1 0 auto;
      width: 316px;
      max-width: 316px;
    }
  }

}

</style>