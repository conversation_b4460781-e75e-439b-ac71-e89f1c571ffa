<template>
  <div class="page-container">
    <a-page-header
        class="header"
        title="创建分群"
        @back="back"
    >
      <template #extra>
        <a-space>
          <a-button @click="back">取消</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="content">
      <div class="title">
        选择你想要创建的分群类型
      </div>
      <div class="card-list">
        <div class="card-item" @click="gotoCreateConditionGroup">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-screen.png">
            <div class="card-left">
              <div class="card-title">
                条件分群
              </div>
              <div class="card-desc">
                配置用户的事件和属性筛选条件，并将其定义为分群，支持更新定时。
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-id.png">
            <div class="card-left">
              <div class="card-title">
                ID分群
              </div>
              <div class="card-desc">
                上传用户 ID 或属性值的集合文件，并将其中可关联的用户定义为分群，支持多次上传。
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-sql.png">
            <div class="card-left">
              <div class="card-title">
                SQL分群
              </div>
              <div class="card-desc">
                自定义 SQL 脚本，使用查询结果快速创建 SQL 分群
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useRouter} from 'vue-router'
import {ROUTE_NAME} from "@/router/constants";

const router = useRouter()

function back() {
  router.back()
}

function gotoCreateConditionGroup() {
  router.push({name: ROUTE_NAME.USER_GROUP_CONDITION})
}
</script>

<style scoped lang="less">
@import '../../style/create.less';

</style>