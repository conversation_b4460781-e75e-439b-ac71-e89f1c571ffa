<template>
  <div class="guide">
    <div class="stickyBar">
      <div class="modal" style="width: 100%;">
        <img src="/icon/analysis/performAnalysis.svg" alt="">
        <span class="title">分析条件</span>
      </div>
    </div>
    <div class="event-filter-box">
      <div class="subTitle">参与分析的事件</div>
      <handleAnalyseList :handle-list="analyzeData"/>
      <div class="subTitle" style="margin-top: 16px;">分析路径以</div>
      <handleFilterList :handle-list="traceData"/>
      <div class="subTitle" style="margin-top: 16px;">会话间隔时长</div>
      <div class="sub-container">
        <a-input-number v-model:model-value="intervalValue" style="height:26px;width: 50px;margin-right: 4px;" :hide-button="true" :precision="0" :default-value="1"/>
        <a-select v-model:model-value="intervalUnit" style="height:26px;width: 50px;margin-right: 4px;" size="small" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
          <template #arrow-icon>
          </template>
          <a-option value="s" style="width: 60px;">秒</a-option>
          <a-option value="m">分钟</a-option>
          <a-option value="h">小时</a-option>
        </a-select>
        <a-tooltip content="即路径中相邻事件的最大间隔时间" position="right">
          <icon-info-circle/>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useEventBus} from '@vueuse/core';
import handleFilterList from "./handleFilterList.vue";
import handleAnalyseList from "./handleAnalyseList.vue";

const eventBus = useEventBus('eventList');

const intervalValue = ref(30)
const intervalUnit = ref('m')
const analyzeData = ref<any>([
  {
    name: '用户登录',
    type: 'event',
    displayType: {
      type: 'default',
      decimalNum: 2,
      thousandSep: 1
    },
    displayName: '',
    rename: false,
    unitName: '',
    eventList: [
      {
        eventName: '用户登录',
        eventDisplayName: '用户登录',
        type: 'event',
        eventAttrCode: '',
        eventAttrName: '总次数',
        statisticalType: '',
        statisticalName: '',
        filter: {}
      },
    ],
    filtersList: []
  }
])

const traceData = ref<any>([
  {
    name: '用户登录',
    type: 'event',
    displayType: {
      type: 'default',
      decimalNum: 2,
      thousandSep: 1
    },
    displayName: '',
    rename: false,
    unitName: '',
    eventList: [
      {
        eventName: '用户登录',
        type: 'event',
        eventAttrCode: '',
        eventAttrName: '总次数',
        statisticalType: '',
        statisticalName: '',
        filter: {}
      },
    ],
    filtersList: []
  }
])

</script>

<style scoped lang="less">
.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        font-weight: 600;
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .subTitle {
      padding-left: 24px;
      color: var(--tant-text-gray-color-text1-3);
      font: var(--tant-description-font-description-medium);
      line-height: 20px;
    }

    .relevance-box {
      padding-left: 24px;
      color: var(--tant-text-gray-color-text1-3);
      line-height: 38px;

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        :deep(.filter-btn) {
          background: #fff;
        }
      }
    }

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            // font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }

            :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
              font-weight: 600 !important;
            }

            :deep(.arco-input-wrapper) {
              border: none;
              background-color: transparent;
              font-weight: 600;

              &:hover {
                border: none;
                background-color: transparent;
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;
          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 24px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .action-left .row-content :deep(.filter-icon) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }
      }
    }

    .row-foot {
      margin: 0;
      padding-left: 34px;
      transition: all .3s;

      .ta-filter-button {
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-hover);
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.sub-container {
  display: flex;
  align-items: center;
  padding: 6px 24px;

  &:hover {
    background-color: var(--tant-fill-color-fill1-2);
  }
}
</style>