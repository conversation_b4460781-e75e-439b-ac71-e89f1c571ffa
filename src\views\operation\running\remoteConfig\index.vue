<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-tabs type="text" @change="tabsChange">
        <a-tab-pane key="1" title="参数">
          <div class="params-content">
            <div class="params-title">配置</div>
            <div class="config-content">
              <a-button class="button" type="primary" style="margin-right: 30px" @click="handleClick">
                <icon-plus style="margin-right: 3px"/>
                添加参数
              </a-button>
              <!-- <a-button class="button" type="primary" @click="openGroup">
                <icon-folder-delete style="margin-right: 3px"/>
                创建新群组
              </a-button> -->
            </div>
          </div>
          <div class="action-content">
            <div class="filter">
              <filterConditionBtn/>
            </div>
            <div class="search">
              <a-input v-model:model-value="pageParams.text" style="width: 300px;background-color: #FFF;" placeholder="请输入..." @press-enter="getList">
                <template #prefix>
                  <icon-search/>
                </template>
              </a-input>
            </div>
          </div>
          <a-table
              :loading="loading"
              :columns="columns"
              :data="tableData"
              :bordered="false"
              sticky-header
              :pagination="false"
              :table-layout-fixed="true"
               row-key="name"
              :row-selection="rowSelection"
              :scrollbar="scrollbar"
              :expandable="expandable"
              :scroll="scroll">
              <template #expand-icon="{expanded,record}">
                <icon-right v-if="!expanded"/>
                <icon-down v-else/>
              </template>
              <template #conditionName="{ record }">
                <div style="display: flex;align-items: center;">
                  <svg height="24px" viewBox="0 0 24 24" width="24px" fit="" preserveAspectRatio="xMidYMid meet" focusable="false">
                      <path :d="filterIcon(record.valueType)" fill-rule="evenodd"></path>
                  </svg>
                  <span style="margin-left: 8px;">{{ record.name }}</span>
                </div>
                <span style="color: var(--color-text-2);font-size: 14px;">{{ record.description }}</span>
              </template>
              <template #condition="{ record }">
                <span v-if="(record.conditions.length === 1 &&record.conditions[0].code ==='default') || !record.conditions.length">默认值</span>
                <a-button v-else shape="round" @click.stop="handleExpand(record)">{{ record.conditions.length }}个条件</a-button>
              </template>
              <template #configValue="{ record }">
                <span v-if="record.conditions.length>1">多个值</span>
                <span v-if="record.conditions.length === 1">{{ record.conditions[0].value }}</span>
                <span v-if="!record.conditions.length">空字符串</span>
              </template>
              <template #creator="{ record }">
                <p>{{ record.creator?.name }}</p>
                <span>{{ dayjs(record.createTime).format('YYYY年M月DD日') }}</span>
              </template>
              <template #extractionPercent>100%</template>
              <template #action="{ record }">
                <a-tooltip content="修改" position="bottom">
                  <a-button class="setting" style="margin-right: 12px;" @click="editParam(record)">
                    <template #icon>
                      <icon-pen/>
                    </template>
                  </a-button>
                </a-tooltip>
                <a-dropdown @select="handleSelect($event,record)">
                  <a-button class="setting">
                    <template #icon>
                      <icon-more-vertical/>
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption value="copy">复制</a-doption>
                    <a-doption value="delete">删除</a-doption>
                  </template>
                </a-dropdown>
              </template>
          </a-table>
          <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" title="条件">
          <conditionsTab ref="conditionTabRef" :app-id="appId"/>
          <!-- <a-empty style="width: auto;height: auto;"/> -->
        </a-tab-pane>
        <!-- <a-tab-pane key="3" title="A/B测试">
          <abTestTab/>
          <a-empty style="width: auto;height: auto;"/>
        </a-tab-pane> -->
      </a-tabs>
    </div>
    <drawer ref="drawerRef" :app-id="appId" :params-list="tableData" @update-data="getList"/>
    <createGroupModal ref="groupModalRef"/>
  </div>
</template>

<script setup lang="ts">
import {h, provide, reactive, ref} from "vue";
import selectApp from "@/components/selected-game-app/index.vue"
import {getConfigParamList, removeParam} from "@/api/marketing/api"
import {Message} from '@arco-design/web-vue';
import dayjs from "dayjs";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {useRoute} from 'vue-router';
import drawer from "./components/drawer.vue";
import filterConditionBtn from "./components/filterConditionBtn.vue";
import createGroupModal from "./components/createGroupModal.vue";
import conditionsTab from "./conditionsTab.vue";

const route = useRoute();
const appId = ref(useSessionStorage('app-id', '')?.value)
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const total = ref(0)
const loading = ref(true)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
  text: '',
})
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: "true",
    slotName: 'conditionName',
    minWidth: 200,
  },
  {
    title: '条件',
    dataIndex: 'condition',
    ellipsis: "true",
    slotName: 'condition',
    minWidth: 200,
  },
  {
    title: '值',
    dataIndex: 'configValue',
    ellipsis: "true",
    slotName: 'configValue',
    minWidth: 200,
  },
  {
    title: '提取百分比',
    dataIndex: 'extractionPercent',
    ellipsis: "true",
    slotName: 'extractionPercent',
    minWidth: 200,
  },
  {
    title: '上次发布者',
    dataIndex: 'creator',
    ellipsis: "true",
    slotName: 'creator',
    minWidth: 200,
  },
  {
    title: '',
    dataIndex: 'action',
    ellipsis: "true",
    slotName: 'action',
    width: 100,
    align: 'right',
    fixed: 'right'
  },
]
const tableData = ref<any>([
  // {
  //   name: '1234',
  //   expandDate: {
  //     name: '1234444'
  //   }
  // }
])
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const scrollbar = ref(true)
const scroll = {y: 'calc(100vh - 400px)', x: 1000}

const expandable = reactive({
  // title: 'Expand',
  // width: 80,
  expandedRowRender: (record) => {
    if (record.conditions && record.conditions.length > 0) {
      return h('div', { class: 'expand-content' }, [
        h('div', {}, [
          ...record.conditions.map((condition, index) => 
            h('div', 
            { key: index,
              style: { width: '520px', marginBottom: '12px', }
            }, [
              h('div',{style: { display: 'flex', fontSize: '13px'}},[
                h('div', 
                  {
                    style:{
                      padding: '0 12px',
                      borderRadius: '24px',
                      textAlign: 'center',
                      minWidth: '120px',
                      maxWidth:'180px',
                      background: condition.color ? condition.color : 'var(--color-secondary)',
                      lineHeight: '32px',
                      height:'32px',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis'
                    }
                  }, 
                  condition.name
                ),
                h('div',
                  { 
                    style: {
                      height: '2px',
                      minWidth: '120px',
                      flex: '1',
                      borderTop: '2px dashed #888686',
                      margin: '14px 8px 0'
                    }
                  },
                  {}
                ),
                h('div', { style: { lineHeight: '28px'}}, condition.value),
              ]),
              index < record.conditions.length - 1 ? h('div', { style: { width: '2px', minHeight: '24px', borderLeft: '2px dashed #888686', marginTop: '8px' , marginLeft: '20px' } }, {}) : null
            ]),
          ),
        ]),
      ]);
    }
  }
});
const handleExpand = (v) => {
  console.log(v,'mm');
  
}

const getList = async () => {
  loading.value = true
  const data = {
    appId:appId.value,
    ...pageParams
  }
  try{
    await getConfigParamList(data).then(res => {
      tableData.value = res.items
      total.value = res.total
      // tableData.value.push({name:'1234',conditions:[],valueType:'boolean'})
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
  
}
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
const drawerRef = ref()
const conditionTabRef = ref()
const tabsChange = (v) => {
  console.log(v,'vvv');
  if(v === '2') {
    conditionTabRef.value.getTabList()
  }
}
const handleClick = () => {
  drawerRef.value.handleDrawer()
};
const handleSelect = async (v,record) => {
  if (v !== 'delete') {
    drawerRef.value.handleDrawer(record,v)
  }else{
    const data = {
      name:record.name,
      appId:appId.value
    }
    try{
      await removeParam(data).then(res => {
        Message.success('删除成功')
        getList()
      })
    }catch (error) {
      console.error('失败:', error);
    }
    
  }
}
const editParam = (record) => {
  drawerRef.value.handleDrawer(record,'edit')
}
const groupModalRef = ref()
const openGroup = () => {
  groupModalRef.value.openModal()
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    appId.value = value
    getList()
  }
})

provide('appId', appId);
const init = () => {
  getList()
}
init()
const filterIcon = (v) => {
  const dataTypeList = [
      {
          value:'string',
          label:'字符串',
          svgPath:'M 6 7 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 8 H 6 v -2 H 4 v 2 H 2 V 9 c 0 -0.553 0.195 -1.025 0.585 -1.415 C 2.975 7.195 3.447 7 4 7 h 2 Z M 4 9 v 4 h 2 V 9 H 4 Z m 11 1.5 c 0 0.413 -0.147 0.767 -0.44 1.06 c -0.293 0.293 -0.647 0.44 -1.06 0.44 c 0.413 0 0.767 0.147 1.06 0.44 c 0.293 0.293 0.44 0.647 0.44 1.06 V 15 c 0 0.553 -0.195 1.025 -0.585 1.415 c -0.39 0.39 -0.862 0.585 -1.415 0.585 H 9 V 7 h 4 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 1.5 Z M 11 13 v 2 h 2 v -2 h -2 Z m 0 -4 v 2 h 2 V 9 h -2 Z m 5 6 V 9 c 0 -0.553 0.195 -1.025 0.585 -1.415 c 0.39 -0.39 0.862 -0.585 1.415 -0.585 h 2 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 1.5 h -2 V 9 h -2 v 6 h 2 v -1.5 h 2 V 15 c 0 0.547 -0.197 1.017 -0.59 1.41 c -0.393 0.393 -0.863 0.59 -1.41 0.59 h -2 a 1.924 1.924 0 0 1 -1.41 -0.59 A 1.924 1.924 0 0 1 16 15 Z")'
      },
      {
        value:'int',
        label:'整数',
        svgPath:'M3 9V7h4v10H5V9H3zm5-2h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v2c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-2v2h4v2H8v-4c0-.553.195-1.025.585-1.415.39-.39.862-.585 1.415-.585h2V9H8V7zm7 0h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v1.5c0 .413-.147.767-.44 1.06-.293.293-.647.44-1.06.44.413 0 .767.147 1.06.44.293.293.44.647.44 1.06V15c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-4v-2h4v-2h-2v-2h2V9h-4V7z',
      },
      {
          value:'float',
          label:'小数',
          svgPath:'M3 9V7h4v10H5V9H3zm5-2h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v2c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-2v2h4v2H8v-4c0-.553.195-1.025.585-1.415.39-.39.862-.585 1.415-.585h2V9H8V7zm7 0h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v1.5c0 .413-.147.767-.44 1.06-.293.293-.647.44-1.06.44.413 0 .767.147 1.06.44.293.293.44.647.44 1.06V15c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-4v-2h4v-2h-2v-2h2V9h-4V7z',
      },
      {
          value:'boolean',
          label:'布尔值',
          svgPath:'M 7 7 h 10 a 5 5 0 0 1 0 10 H 7 A 5 5 0 0 1 7 7 Z m 10 8 a 3 3 0 1 0 0 -6 a 3 3 0 0 0 0 6 Z'
      },
      {
          value:'json',
          label:'JSON',
          svgPath:'M 8 8 v 1.68 c 0 0.4 -0.075 0.795 -0.225 1.185 a 3.62 3.62 0 0 1 -0.625 1.055 c 0.567 0.58 0.85 1.273 0.85 2.08 v 2 c 0 0.273 0.098 0.508 0.295 0.705 A 0.962 0.962 0 0 0 9 17 h 1 v 2 H 9 a 2.89 2.89 0 0 1 -2.12 -0.88 A 2.89 2.89 0 0 1 6 16 v -2 a 0.962 0.962 0 0 0 -0.295 -0.705 A 0.962 0.962 0 0 0 5 13 H 4 v -2 h 1 c 0.26 0 0.492 -0.147 0.695 -0.44 c 0.203 -0.293 0.305 -0.587 0.305 -0.88 V 8 c 0 -0.827 0.293 -1.533 0.88 -2.12 A 2.89 2.89 0 0 1 9 5 h 1 v 2 H 9 a 0.962 0.962 0 0 0 -0.705 0.295 A 0.962 0.962 0 0 0 8 8 Z m 11 3 h 1 v 2 h -1 a 0.962 0.962 0 0 0 -0.705 0.295 A 0.962 0.962 0 0 0 18 14 v 2 a 2.89 2.89 0 0 1 -0.88 2.12 A 2.89 2.89 0 0 1 15 19 h -1 v -2 h 1 a 0.962 0.962 0 0 0 0.705 -0.295 A 0.962 0.962 0 0 0 16 16 v -2 c 0 -0.807 0.283 -1.5 0.85 -2.08 a 3.62 3.62 0 0 1 -0.625 -1.055 A 3.277 3.277 0 0 1 16 9.68 V 8 a 1.008 1.008 0 0 0 -1 -1 h -1 V 5 h 1 a 2.89 2.89 0 0 1 2.12 0.88 A 2.89 2.89 0 0 1 18 8 v 1.68 c 0 0.293 0.102 0.587 0.305 0.88 c 0.203 0.293 0.435 0.44 0.695 0.44 Z'
      }
  ]
  const filteredObjects = dataTypeList.find(item => item.value === v);
  return filteredObjects?.svgPath || ''
}
</script>

<style scoped lang="less">
.setting {
  width: 20px;
  height: 20px;
  background: transparent;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;
}

.params-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.config-content {
  display: flex;
  justify-content: flex-end;

  .button {
    border: none;
    border-radius: 5px;
  }
}

.action-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 56px;
  padding: 0 8px 0 16px;
  border-radius: 8px 8px 0 0;
  border: 1px solid #f7f6f6;
  border-bottom: none;
}
.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
:deep(.arco-table-tr-expand .arco-table-td){
  background-color: #fff;
}
.expand-content {
 height: 200px!important;
  background-color: #fff;
  margin-bottom: 12px;
  border-top: 2px dashed #888686;
}
</style>