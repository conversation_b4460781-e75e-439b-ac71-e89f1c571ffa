<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="refresh">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="()=> {
            router.push({
              name: ROUTE_NAME.USER_TAG_CREATE,
            })
          }">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #user_number="{record}">
          <a-button
              v-if="record.userNumber !== null && record.userNumber !== undefined"
              type="text"
              @click="goToUserList(record)">
            {{ record.userNumber }}
          </a-button>
          <div v-else>
            --
          </div>
        </template>
        <template #sourceChannel="{record}">
          <a-tag v-for="item in record.sourceChannel">
            {{ getUserAttributeSourceChannelName(item) }}
          </a-tag>
        </template>
        <template #optional="{ record }">
          <a-button
              type="text"
              @click="changeStatus(record)">
            {{ record.status? '禁用' : '启用' }}
          </a-button>
          <a-button
              type="text"
              @click="handleEdit(record)">
            编辑
          </a-button>
          <a-popconfirm :content="`确认删除“${record.name}”?`" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {delUserTagItem, getUserTagList, updateUserTagItem} from "@/api/setting/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {getUserAttributeSourceChannelName, getUserTagTypeComment} from "@/api/enum";
import router from "@/router";
import {useRoute} from "vue-router";
import selectApp from "@/components/selected-game-app/index.vue"
import {formatTimestamp} from "@/utils/dateUtil";
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {ROUTE_NAME} from "@/router/constants";
import {USER_LIST_SCENE} from "@/views/data/user/info/constants";

const localStorageEventBus = useEventBus(LocalStorageEventBus);
const route = useRoute();
const data = ref<any>([]);
const loading = ref<boolean>(true);
const searchName = ref<string>(route.query.text as string || '');
const columns = ref<any>([
  {
    title: '标签名',
    dataIndex: 'displayName',
    fixed: 'left',
    width: 200,
  },
  {
    title: '标签类型',
    dataIndex: 'type',
    render: (value) => {
      const {record} = value;
      return getUserTagTypeComment(record.type)
    },
    width: 180,
    align: 'center',
  },
  {
    title: '最后更新时间',
    dataIndex: 'lastTriggerTime',
    render: (value) => {
      const {record} = value;
      return record.lastTriggerTime ? formatTimestamp(record.lastTriggerTime, record.timeZone) : '--'
    },
    width: 180,
    align: 'center',
  },
  {
    title: '人数',
    slotName: 'user_number',
    width: 180,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'note',
    render: (value) => {
      const {record} = value;
      return _.isEmpty(record.note) ? '--' : record.note
    },
    width: 180,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '启用状态',
    dataIndex: 'status',
    render: (value) => {
      const {record} = value;
      return record.status ? '启用' : '禁用'
    },
    width: 120,
  },
  {
    title: '最后更新人',
    dataIndex: 'creator',
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
    width: 120,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    render: (value) => {
      const {record} = value;
      return record.createTime ? formatTimestamp(record.createTime) : '--'
    },
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 240
  },
]);

const refresh = () => {
  const params = {
    name: searchName.value,
  };
  loading.value = true
  getUserTagList(params).then(res => {
    data.value = _.isEmpty(searchName.value) ? res : res?.filter(item => {
      return item.name?.includes(searchName.value) || item.displayName?.includes(searchName.value)
    })
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  refresh()
})

const searchData = () => {
  
  // 更新路由参数
  const query = { ...route.query };
  if (searchName.value) {
    query.text = searchName.value;
  } else {
    delete query.text;
  }
  router.replace({ query });
}
// 更改状态
const changeStatus = (record: any) => {
  const {code, status} = record; 
  const params = {
    code,
    status: status === 1 ? 0 : 1,
  };
  updateUserTagItem(params).then(res => {
    Message.success("标签状态修改成功！")
    refresh() 
  })
}
const handleEdit = (record: any) => {
  let routeName = '';
  switch (record.type) {
    case 1:
      routeName = ROUTE_NAME.USER_TAG_CONDITION;
      break;
    // case 2:
    //   routeName = ROUTE_NAME.USER_TAG_ID;
    //   break;
    // case 3:
    //   routeName = ROUTE_NAME.USER_TAG_SQL;
    //   break;
    case 4:
      routeName = ROUTE_NAME.USER_TAG_INDICATOR;
      break;
    default:
      routeName = ROUTE_NAME.USER_TAG_CONDITION;
  }
  router.push({
    name: routeName,
    query: {
      code: record.code,
      type: 'edit'
    }
  });
};
const deleteData = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除用户标签编码不能为空！")
  }
  delUserTagItem(code).then(res => {
    Message.info("用户标签删除成功！")
    refresh()
  }).catch(e => {
    Message.error("用户标签删除失败！", e)
  })
}

const goToUserList = (record: any) => {
  router.push({
    name: ROUTE_NAME.USER_LIST,
    query: {
      scene: USER_LIST_SCENE.USER_TAG,
      tagCode: record.code
    },
  });
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") refresh()
})
</script>

<style scoped lang="less">

</style>