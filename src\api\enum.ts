import {NumberDisplayConfig} from "@/api/analyse/type";

/**
 * 对象权限枚举
 */
export enum ObjectPermissionType {

  /**
   * 无权限
   */
  NONE = 0,

  /**
   * 拥有者
   */
  OWNER = 1,

  /**
   * 管理者
   */
  MANAGER,

  /**
   * 协作者
   */
  MEMBER,

  /**
   * 查看者
   */
  GUEST
}


/**
 * 性别枚举
 */
export enum Gender {

  /**
   * 未知
   */
  NONE = 0,

  /**
   * 男
   */
  MALE = 1,

  /**
   * 女
   */
  FEMALE = 2
}

/**
 * 更新类型枚举
 */
export enum RefreshType {

  /**
   * 实时
   */
  REAL_TIME = 0,

  /**
   * 每 N 年更新一次
   */
  YEAR = 1,

  /**
   * 每 N 月更新一次
   */
  MONTH = 2,

  /**
   * 每 N 周更新一次
   */
  WEEK = 3,

  /**
   * 每 N 日更新一次
   */
  DAY = 4,

  /**
   * 每 N 小时更新一次
   */
  HOUR = 5,

  /**
   * 每 N 分钟更新一次
   */
  MINUTE = 6,

  /**
   * 每 N 秒更新一次
   */
  SECOND = 7,

  /**
   * 每 N 毫秒更新一次
   */
  MILLISECONDS = 8,

  /**
   * 固定时间
   *
   * 支持时间格式：二十四小时制 hh:mm
   */
  FIXED_TIME = 10,
}


/**
 * 图表类型
 */
export enum ChartType {
  /**
   * 趋势图
   */
  TREND = 'trend',

  /**
   * 堆积图
   */
  STACK = 'stack',

  /**
   * 累计图
   */
  TOTAL = 'total',

  /**
   * 分布图
   */
  DISTRIBUTION = 'distribution',

  /**
   * 表格
   */
  TABLE = 'table',

  /**
   * 饼图
   */
  PIE = 'pie',
}

/**
 * 计算符号
 */
export enum CalculateSymbol {
  /**
   * 等于
   */
  EQUAL = 'eq',
  /**
   * 不等于
   */
  NOT_EQUAL = 'neq',
  /**
   * 小于
   */
  LESS_THAN = 'lt',
  /**
   *小于等于
   */
  LESS_THAN_OR_EQUAL = 'lteq',
  /**
   * 大于
   */
  GREATER_THAN = 'gt',
  /**
   *大于等于
   */
  GREATER_THAN_OR_EQUAL = 'gteq',
  /**
   * 区间范围
   */
  SCOPE = 'scope',
  /**
   * 存在/有值
   */
  EXIST = 'ex',
  /**
   * 不存在/无值
   */
  NOT_EXIST = 'nex',
  /**
   * 包含
   */
  CONTAIN = 'con',
  /**
   * 不包含
   */
  NOT_CONTAIN = 'ncon',
  /**
   * 模糊包含
   */
  FUZZY_CONTAIN = 'fcon',
  /**
   * 模糊不包含
   */
  NOT_FUZZY_CONTAIN = 'nfcon',
  /**
   * 正则匹配
   */
  REGEX_MATCH = 'rm',
  /**
   * 正则不匹配
   */
  NOT_REGEX_MATCH = 'nrm'
}

/**
 * 与或
 */
export enum LogicalOperationType {
  /**
   * 与
   */
  AND = 'and',

  /**
   * 或
   */
  OR = 'or',

}

/**
 * QueryFilterType枚举类型说明
 */
export enum QueryFilterType {
  /**
   * 事件属性
   */
  EVENT = 'event',

  /**
   * 用户属性
   */
  USER = 'user',
  /**
   * 用户分群
   */
  CLUSTER = 'cluster',
  /**
   * 用户标签
   */
  TAG = 'tag',
}

/**
 * QueryAggregateType枚举类型说明
 */
export enum QueryAggregateType {
  /**
   * 事件属性
   */
  EVENT = 'event',

  /**
   * 用户属性
   */
  USER = 'user',
  /**
   * 用户分群
   */
  CLUSTER = 'cluster',
  /**
   * 用户标签
   */
  TAG = 'tag',
}

/**
 * TimeParticleSize
 */
export enum TimeParticleSize {
  /**
   * 1分钟
   */
  MINUTE1 = 'm1',
  /**
   * 5分钟
   */
  MINUTE5 = 'm5',
  /**
   * 10分钟
   */
  MINUTE10 = 'm10',
  /**
   * 1小时
   */
  HOUR1 = 'h1',
  /**
   * 1天
   */
  DAY1 = 'D1',
  /**
   * 1周
   */
  WEEK1 = 'W1',
  /**
   * 1月
   */
  MONTH1 = 'M1',
  /**
   * 1季度
   */
  QUARTER1 = 'Q1',
  /**
   * 1年
   */
  YEAR1 = 'Y1',
  /**
   * 合计
   */
  TOTAL = 'T0',
}

/**
 * 指标类型
 */
export enum IndicatorType {
  /**
   * 事件分析指标
   */
  EVENT = 'event',

  /**
   * 留存分析指标
   */
  RETENTION = 'retention',
  /**
   * 应用分析指标
   */
  OPERATION = 'operation',
  /**
   * 群组分析指标
   */
  GROUP = 'group',
  /**
   * 归因分析指标
   */
  TRAFFIC = 'traffic',
}

// 获取 IndicatorType 中文描述
export function getIndicatorTypeName(value: IndicatorType): string {
  switch (value) {
    case IndicatorType.EVENT:
      return '事件分析指标';
    case IndicatorType.RETENTION:
      return '留存分析指标';
    case IndicatorType.OPERATION:
      return '应用分析指标';
    case IndicatorType.GROUP:
      return '群组分析指标';
    case IndicatorType.TRAFFIC:
      return '归因分析指标';
    default:
      return '未知指标类型';
  }
}

/**
 * StatisticalType枚举类型说明
 */
export enum StatisticalType {
  /**
   * 合计
   */
  SUM = 'sum',
  /**
   * 均值
   */
  AVG = 'avg',
  /**
   * 人均值
   */
  PER_CAPITA = 'perCapita',
  /**
   * 中位数
   */
  MEDIAN = 'median',
  /**
   * 最大值
   */
  MAX = 'max',
  /**
   * 最小值
   */
  MIN = 'min',
  /**
   * 取重数
   */
  MODE = 'mode',
  /**
   * 方差
   */
  VARIANCE = 'variance',
  /**
   * 标准差
   */
  STD_DEV = 'stdDev',
  /**
   * 99分位数
   */
  PCT_99 = 'pct_99',
  /**
   * 90分位数
   */
  PCT_90 = 'pct_90',
  /**
   * 80分位数
   */
  PCT_80 = 'pct_80',
  /**
   * 75分位数
   */
  PCT_75 = 'pct_75',
  /**
   * 70分位数
   */
  PCT_70 = 'pct_70',
  /**
   * 60分位数
   */
  PCT_60 = 'pct_60',
  /**
   * 40分位数
   */
  PCT_40 = 'pct_40',
  /**
   * 30分位数
   */
  PCT_30 = 'pct_30',
  /**
   * 25分位数
   */
  PCT_25 = 'pct_25',
  /**
   * 20分位数
   */
  PCT_20 = 'pct_20',
  /**
   * 10分位数
   */
  PCT_10 = 'pct_10',
  /**
   * 5分位数
   */
  PCT_5 = 'pct_5'
}

/**
 * NumberSummaryType
 */
export enum NumberSummaryType {
  /**
   * 12等分
   */
  ST12 = 'st12',

  /**
   * 留存分析指标
   */
  DISCRETE = 'discrete',
  /**
   * 自定义区间
   */
  CUSTOM = 'custom',
}

// 事件枚举
// 预置 自定义 虚拟  维度
export enum EventType {
  /**
   * 预置事件
   */
  PREDEFINED = 1,

  /**
   * 自定义事件
   */
  CUSTOM = 2,
  /**
   * 虚拟事件
   */
  VIRTUAL = 3,
}

// 获取 EventType 中文描述的函数
export function getEventTypeName(value: EventType): string {
  switch (value) {
    case EventType.PREDEFINED:
      return '预置事件';
    case EventType.CUSTOM:
      return '自定义事件';
    case EventType.VIRTUAL:
      return '虚拟事件';
    default:
      return '未知事件类型';
  }
}

export enum EventAttributesType {
  /**
   * 预置属性
   */
  PREDEFINED = 1,

  /**
   * 自定属性
   */
  CUSTOM = 2,
  /**
   * 虚拟属性
   */
  VIRTUAL = 3,
  /**
   * 维度表属性
   */
  DIMENSION = 4,
}

// 获取 EventAttributesType 中文描述的函数
export function getEventAttributesTypeName(value: EventAttributesType): string {
  switch (value) {
    case EventAttributesType.PREDEFINED:
      return '预置属性';
    case EventAttributesType.CUSTOM:
      return '自定义属性';
    case EventAttributesType.VIRTUAL:
      return '虚拟属性';
    case EventAttributesType.DIMENSION:
      return '维度表属性';
    default:
      return '未知属性类型';
  }
}


// 事件使用状态枚举
//  可用 不可用
export enum EventUseOFStateType {
  /**
   * 可用
   */
  USABLE = 1,
  /**
   * 不可用
   */
  UNAVAILABLE = 2,
}

// 属性使用状态枚举
// 使用中 可用 不可用
export enum EventAttrUseOFStateType {
  /**
   * 使用中
   */
  INUSE = 3,
  /**
   * 属性可用
   */
  USABLE = 1,
  /**
   * 不可用
   */
  UNAVAILABLE = 2,
}

// 用户属性类型
export enum UserAttributeType {
  /**
   * 预置
   */
  PRESET = 1,
  /**
   * 自定义
   */
  CUSTOM = 2,
}

export function getUserAttributeTypeName(value: number): string {
  switch (value) {
    case UserAttributeType.PRESET:
      return '预置属性';
    case UserAttributeType.CUSTOM:
      return '自定义属性';
    default:
      return '未知属性类型';
  }
}

// 用户属性整合方式类型
export enum UserAttributeAggregateType {
  /**
   * 最新值
   */
  REPLACE = 'replace',
  /**
   * 最大值
   */
  SUM = 'sum',
  /**
   * 最小值
   */
  MAX = 'max',
  /**
   * 累计值
   */
  MIN = 'min',
}

export function getUserAttributeAggregateType(value: UserAttributeAggregateType): string {
  switch (value) {
    case UserAttributeAggregateType.REPLACE:
      return '最新值';
    case UserAttributeAggregateType.SUM:
      return '最大值';
    case UserAttributeAggregateType.MAX:
      return '最小值';
    case UserAttributeAggregateType.MIN:
      return '累计值';
    default:
      return '未知';
  }
}


// 用户属性来源渠道
export enum UserAttributeSourceChannel {
  /**
   * 埋点事件
   */
  EVENT = 'event',
  /**
   * appsflyer推送
   */
  APPSFLYER = 'appsflyer'
}

// 获取 UseAttributeSourceChannel 中文描述的函数
export function getUserAttributeSourceChannelName(value: UserAttributeSourceChannel): string {
  switch (value) {
    case UserAttributeSourceChannel.EVENT:
      return '埋点事件';
    case UserAttributeSourceChannel.APPSFLYER:
      return 'appsflyer推送';
    default:
      return '未知';
  }
}

/**
 * 时间单位
 */
export enum TimeUnit {
  /** 秒 */
  SECOND = 'second',
  /** 分钟 */
  MINUTE = 'minute',
  /** 小时 */
  HOUR = 'hour',
  /** 天 */
  DAY = 'day',
  /** 周 */
  WEEK = 'week',
  /** 月 */
  MONTH = 'month',
  /** 季度 */
  QUARTER = 'quarter',
  /** 年 */
  YEAR = 'year'
}

// 获取 TimeUnit 的中文描述
export function getTimeUnitComment(value: TimeUnit): string {
  switch (value) {
    case TimeUnit.SECOND:
      return '秒';
    case TimeUnit.MINUTE:
      return '分钟';
    case TimeUnit.HOUR:
      return '小时';
    case TimeUnit.DAY:
      return '天';
    case TimeUnit.WEEK:
      return '周';
    case TimeUnit.MONTH:
      return '月';
    case TimeUnit.QUARTER:
      return '季度';
    case TimeUnit.YEAR:
      return '年';
    default:
      return '未知时间单位';
  }
}

/**
 * 用户分群类型
 */
export enum UserGroupType {
  /** 条件分群 */
  CONDITION = 1,
  /** 用户 ID 分群 */
  USER_ID = 2,
  /** SQL 分群 */
  SQL = 3
}

// 获取 UserGroupType 的中文描述
export function getUserGroupTypeComment(value: UserGroupType): string {
  switch (value) {
    case UserGroupType.CONDITION:
      return '条件分群';
    case UserGroupType.USER_ID:
      return '用户ID分群';
    case UserGroupType.SQL:
      return 'SQL分群';
    default:
      return '未知用户群组类型';
  }
}
/**
 * 用户标签类型
 */
export enum UserTagType {
  /** 条件标签 */
  CONDITION = 1,
  /** 用户 ID 标签 */
  USER_ID = 2,
  /** SQL 标签 */
  SQL = 3,
  /** 指标标签 */
  INDICATOR = 4,
}

// 获取 UserTagType 的中文描述
export function getUserTagTypeComment(value: UserTagType): string {
  switch (value) {
    case UserTagType.CONDITION:
      return '条件标签';
    case UserTagType.USER_ID:
      return '用户ID标签';
    case UserTagType.SQL:
      return 'SQL标签';
    case UserTagType.INDICATOR:
      return '指标标签';
    default:
      return '未知用户标签类型';
  }
}

/**
 * 数值展示类型
 */
export const enum NumberDisplayType {
  OneDecimal = "1位小数",
  TwoDecimal = "2位小数",
  ThreeDecimal = "3位小数",
  FourDecimal = "4位小数",
  Integer = "取整",
  TwoPercent = "2位百分数",
  ThreePercent = "3位百分数",
  FourPercent = "4位百分数"
}

const NumberDisplayTypeConfigMap: Record<NumberDisplayType, NumberDisplayConfig> = {
  [NumberDisplayType.OneDecimal]: {type: "default", decimalNum: 1, thousandSep: 1},
  [NumberDisplayType.TwoDecimal]: {type: "default", decimalNum: 2, thousandSep: 1},
  [NumberDisplayType.ThreeDecimal]: {type: "default", decimalNum: 3, thousandSep: 1},
  [NumberDisplayType.Integer]: {type: "default", decimalNum: 0, thousandSep: 1},
  [NumberDisplayType.FourDecimal]: {type: "default", decimalNum: 4, thousandSep: 1},
  [NumberDisplayType.TwoPercent]: {type: "percent", decimalNum: 2, thousandSep: 1},
  [NumberDisplayType.ThreePercent]: {type: "percent", decimalNum: 3, thousandSep: 1},
  [NumberDisplayType.FourPercent]: {type: "percent", decimalNum: 4, thousandSep: 1},
};

/**
 * 获取数值展示类型对应的配置
 */
export function getNumberDisplayConfig(type: NumberDisplayType): NumberDisplayConfig {
  return NumberDisplayTypeConfigMap[type];
}

/**
 * 根据配置对象获取对应的 NumberDisplayType
 */
export function getNumberDisplayType(config: NumberDisplayConfig | undefined): NumberDisplayType | undefined {
  return (Object.entries(NumberDisplayTypeConfigMap) as [NumberDisplayType, NumberDisplayConfig][])
    .find(([, value]) =>
      value.type === config?.type &&
      value.decimalNum === config?.decimalNum &&
      value.thousandSep === config?.thousandSep
    )?.[0] || NumberDisplayType.TwoDecimal;
}

/**
 * 属性配置-属性值的输入方式
 */
export enum AttributionInputType {
  /** 手动输入 */
  MANUAL_INPUT = 'manual_input',
  /** 下拉选择 */
  DROPDOWN_SELECTION = 'dropdown_selection',
}

// 获取 AttributionInputType 的中文描述
export function getAttributionInputTypeComment(value: AttributionInputType): string {
  switch (value) {
    case AttributionInputType.MANUAL_INPUT:
      return '手动输入';
    case AttributionInputType.DROPDOWN_SELECTION:
      return '下拉选择';
    default:
      return '未知方式';
  }
}

