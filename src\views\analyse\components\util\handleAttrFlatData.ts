/**
 * 处理属性分类数据
 * @param allAttrList 原始属性列表数据
 * @param eventCodes 事件指标code数组
 * @returns 处理后的属性分类列表
 */
export const handleAttrFlatData = (allAttrList: any[], codeList: any[]) => {
    const eventCodes = [...new Set(codeList.map(item => item.eventCode || item.indicatorCode))];
    if (!allAttrList || !Array.isArray(allAttrList)) {
        return [];
    }
    return allAttrList.map((category) => {
        let items = [];
        if (category.items?.length > 0) {
            if (category.category === '事件属性') {
                if (eventCodes.length === 0) {
                    // eventCodes为空时，返回第一组事件属性的所有属性
                    const firstItem = category.items[0] || {};
                    items = Object.values(firstItem).flat();
                } else {
                    const allEventAttributes = category.items
                        .map(item => {
                            // 获取所有匹配的事件属性
                            return Object.entries(item)
                                .filter(([key]) => eventCodes.includes(key))
                                .map(([_, value]) => value)
                            .flat();
                        })
                        .filter(attrs => attrs.length > 0);
                    const commonCodes = allEventAttributes
                        .map(arr => new Set(arr.map(item => item.code)))
                        .reduce((acc, currentSet) => {
                            return new Set([...acc].filter(code => currentSet.has(code)));
                        }, allEventAttributes.length > 0 ? new Set(allEventAttributes[0].map(item => item.code)) : new Set());
                    // 3. 获取所有交集的元素（取第一个匹配的）
                    items = Array.from(commonCodes).map(code => {
                        // 在所有事件属性数组中找到第一个匹配该 code 的对象
                        for (let arr of allEventAttributes) {
                            const found = arr.find(attr => attr.code === code);
                            if (found) return found;
                        }
                        return null;
                    }).filter(Boolean);
                }
            }else {
                items = category.items;
            }
        }
        return {
            categoryName: category.category,
            itemData: items
        };
    });
};