<!-- 团队目标 -->
 <template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <a-radio-group v-model:model-value="params.pageType" type="button">
            <a-radio value="team">团队</a-radio>
            <a-radio value="app">应用</a-radio>
          </a-radio-group>
        </div>
        <div v-show="params.pageType === 'team'" class="filter-item">
            <a-select
                v-model:model-value="params.team"
                :loading="loading"
                allow-search
                :style="{width:'320px',borderRadius:'4px',marginLeft:'12px'}"
                @change="teamChange">
                <template #header>
                    <div style="padding: 6px 12px;" >
                        <a-button type="text" @click="addTeam">
                            <template #icon>
                                <icon-plus />
                            </template>
                            新建团队
                        </a-button>
                    </div>
                </template>
                <a-option v-for="item in teamList" :key="item.code" :value="item.code">
                    <div class="team-item" style="width: 100%;">
                        <span class="name">{{ item.name }}</span>
                        <div class="extra">
                            <icon-edit class="icon" style="margin-right: 8px;" @click.stop="editTeamItem(item)"/>
                            <icon-delete class="icon" @click.stop="removeTeamItem(item)"/>
                        </div>
                    </div>
                </a-option>
            </a-select>
        </div>
        <div v-show="params.pageType === 'app'" class="filter-item">
          <select-app/>
        </div>
      </div>
    </div>
    <div class="page-body">
        <BasicCards ref="basicRefs" :params="params" />
        <ChannelCostCards ref="costRefs" :params="params"/>
        <ChannelRevenueCards ref="revenueRefs" :params="params"/>
        <TableCards ref="tableRefs" :params="params" :team-list="teamList"/>
    </div>
    <handleModal ref="handleRefs" :groups-list="teamList" @update-data="init"/>
  </div>
</template>
  
<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from "vue-router";
import selectApp from "@/components/selected-game-app/index.vue"
import {Message, Modal} from '@arco-design/web-vue'
import {getTeamList} from "@/api/analyse/api";
import {deleteTeam} from "@/api/marketing/api";
import {usePageFilter} from '@/utils/filterConfigUtil';
import BasicCards from "./components/BasicCards.vue"
import ChannelCostCards from "./components/ChannelCostCards.vue";
import ChannelRevenueCards from "./components/ChannelRevenueCards.vue";
import TableCards from "./components/TableCards.vue";
import handleModal from "./components/handleModal.vue";

const localStorageEventBus = useEventBus(LocalStorageEventBus);
const route = useRoute();
const appIdRef = useSessionStorage('app-id', '');

const params = reactive({
    pageType:'team',
    team:'',
    appId:appIdRef,
})
// 筛选条件保存工具
const { saveFilter, saveFilterDebounced } = usePageFilter(params);
const teamList = ref<any>([])
const loading = ref(false)

const teamChange = () => {
}
const handleRefs = ref()
const addTeam = () => {
    handleRefs.value.openModal()
}
// 编辑团队列表
const editTeamItem = (record) => {
    handleRefs.value.openModal(record)
}
const getTeams = async () => {
    loading.value = true
    try {
        const res = await getTeamList()
        teamList.value = res
        params.team = res[0]?.code || ''
    }catch (e) {
        console.error(e)
    }finally {
        loading.value = false
    }
}
// 删除团队列表
const removeTeamItem = (record) => {
    Modal.confirm({
        title: '确认删除',
        titleAlign: 'start',
        content: `确定要删除团队"${record.name}"吗？`,
        modalClass: 'custom-confirm-modal',
        onOk: async () => {
            await deleteTeam(record.code)
            const index = teamList.value.findIndex(item => item.code === record.code);
            if (index > -1) {
                teamList.value.splice(index, 1);
            }
            if (params.team === record.code) {
                params.team = teamList.value[0]?.code || '';
            }
            Message.success('删除成功');
        }
    });
}

const init = async () => {
    await getTeams()
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, {...savedConfig});
    }
}
init()
localStorageEventBus.on((name, value) => {
  if (name === "app-id"){
    params.appId = value
    init()
  }
})
// 参数变化时自动保存（防抖）
watch(params, () => {
  saveFilterDebounced();
}, { deep: true });
</script>
  
<style scoped lang="less">
:deep(.arco-select-option-content){
    width: 100%;
}
.team-item{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .name{
        max-width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .extra{
        .icon{
            cursor: pointer;
        }
    }
}
.page-body{
    padding: 0;
    scrollbar-width: none;
    background-color: var(--tant-bg-gray-color-bg2-1);
}
:global(.custom-confirm-modal .arco-modal-footer) {
    text-align: right;
}
</style>