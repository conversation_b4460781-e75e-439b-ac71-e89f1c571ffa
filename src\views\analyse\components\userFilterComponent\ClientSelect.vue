<template>
    <!-- 客户端参数选择 -->
    <div class="guide">
      <div v-if="predicateList.length" class="event-filter-box">
        <div class="action-row">
          <div class="row-filters">
            <div class="relation-editor">
              <div v-if="predicateList.length" class="relation-relation">
                <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
                <div v-if="predicateList.length>1" :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="logicalChange">
                  <span v-if="isAnd">且</span>
                  <span v-else>或</span>
                </div>
              </div>
              <div class="relation-main">
                <div v-for="(pre,num) in predicateList" :key="num" class="predicate">
                    <span style="line-height: 26px;padding-right: 12px;flex-shrink: 0;">客户端参数</span>
                    <a-dropdown :disabled="props.disabled" @select="conditionSelect($event,num)">
                        <div class="select-btn" style="justify-content: flex-start;min-width: 180px;">
                            <span v-if="pre.name" class="name-text">{{pre.name}}</span>
                            <span v-else style="color: var(--color-text-2);">选择...</span>
                        </div>
                        <template #content>
                            <template v-for="item in selectList" :key="item.code">
                            <!-- 有子选项的情况 -->
                            <a-dsubmenu v-if="item.children?.length" :value="item.code"  trigger="hover" >
                                <template #default>
                                    <div class="option-content">
                                        <div class="option-title">{{ item.name }}</div>
                                        <div v-if="item.desc" class="option-desc">{{ item.desc }}</div>
                                    </div>
                                </template>
                                <template #content>
                                    <a-doption  v-for="child in item.children" :key="child.code" :value="child.code" :disabled="disableItem(child.code)" style="width: 130px;">
                                        {{ child.name }}
                                    </a-doption>
                                </template>
                            </a-dsubmenu>
                            <!-- 没有子选项的情况 -->
                            <a-doption v-else :value="item.code" :disabled="disableItem(item.code)" style="width: 180px;" >
                                <div class="option-content">
                                    <div class="option-title">{{ item.name }}</div>
                                    <div v-if="item.desc" class="option-desc">{{ item.desc }}</div>
                                </div>
                            </a-doption>
                            </template>
                        </template>
                    </a-dropdown>
                    <div v-if="pre.code" class="input-content">
                        <a-select
                            v-model:model-value="pre.symbol"
                             :disabled="props.disabled"
                            style="height: 28px;"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            @change="symbolChange($event,num)">
                            <a-option v-for="sym in getSymbol(pre.valueType)" :key="sym.value" :value="sym.value">{{ sym.label }}</a-option>
                        </a-select>
                    </div>
                    <!-- 非int,非有值，无值，input -->
                    <a-input
                        v-if="
                        pre.valueType !=='int' &&
                        !['ex', 'nex'].includes(pre.symbol) &&
                        pre.obtainMethod === 'manual_input'"
                        v-model:model-value="pre.inputValue"
                        placeholder="值"
                        class="input-content"
                         style="height: 28px;"
                        :disabled="props.disabled"
                        @input="inputChange($event,num)"
                    />
                    <!-- 非int,正则, select -->
                    <a-input
                        v-if="
                        pre.valueType !=='int' &&
                        ['rm','nrm'].includes(pre.symbol) &&
                        pre.obtainMethod === 'dropdown_selection'"
                        v-model:model-value="pre.inputValue"
                        placeholder="值"
                        class="input-content"
                         style="height: 28px;"
                        :disabled="props.disabled"
                        @input="inputChange($event,num)"
                    />
                    <!--包括,不包括,小于，小于等于，大于，大于等于 单选 select-->
                    <div v-if="['con','ncon','lt','lteq','gt','gteq'].includes(pre.symbol) && pre.obtainMethod === 'dropdown_selection'" class="input-content">
                        <a-select
                            v-model:model-value="pre.inputValue"
                            :loading="pre.attrLoading"
                            allow-search
                            :placeholder="`选择${pre.name}`"
                             style="height: 28px;"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            :disabled="props.disabled"
                            @popup-visible-change="selectPopup($event,pre.code)"
                            @change="singleSelectChange($event,num)">
                            <a-option v-for="li in pre.attrList" :key="li.code" :value="li.code">
                                {{ li.name }}
                            </a-option>
                        </a-select>
                    </div>
                    <!--等于 不等于 多选 select-->
                    <div v-if="['eq','neq'].includes(pre.symbol) && pre.obtainMethod === 'dropdown_selection'" class="input-content">
                        <a-select
                            v-if="!props.disabled"
                            v-model:model-value="pre.multipleValue"
                            allow-search
                            :loading="pre.attrLoading"
                             style="height: 28px;"
                            multiple
                            :tag-nowrap="true"
                            :max-tag-count="1"
                            :scrollbar="true"
                            :placeholder="`选择${pre.name}`"
                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                            @popup-visible-change="selectPopup($event,pre.code)"
                            @change="multipleSelectChange($event,num)">
                            <a-option v-for="li in pre.attrList" :key="li.code" :value="li.code">
                                {{ li.name }}
                            </a-option>
                        </a-select>
                        <a-tooltip position="top" :content="formatMultipleValueDisplay(pre)">
                            <a-select
                                v-if="props.disabled"
                                v-model:model-value="pre.multipleValue"
                                style="height: 28px;"
                                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                multiple
                                :tag-nowrap="true"
                                :max-tag-count="1"
                                disabled>
                                <a-option v-for="li in pre.attrList" :key="li.code" :value="li.code">
                                    {{ li.name }}
                                </a-option>
                            </a-select>
                        </a-tooltip>
                    </div>
                    <!-- int 非有值，非无值，input -->
                    <div v-if="pre.valueType ==='int' && !['ex','nex'].includes(pre.symbol) && pre.obtainMethod === 'manual_input'" class="next-input">
                        <div class="next-input-number">
                            <a-input-number v-model="pre.number1" :disabled="props.disabled" style="height: 28px;" @blur="value1Change($event,num)"/>
                        </div>
                        <span v-if="pre.symbol === 'scope'" class="word">与</span>
                        <div v-if="pre.symbol === 'scope'" class="next-input-number">
                            <a-input-number v-model="pre.number2" :disabled="props.disabled" style="height: 28px;" @blur="value2Change($event,num)"/>
                        </div>
                        <span v-if="pre.symbol === 'scope'" class="word">之间</span>
                    </div>
                    <div v-if="!props.disabled" class="sub-action-right">
                        <a-button class="btn-bg-delete btn-26" @click="deleteItem(num)">
                            <template #icon>
                                <icon-close-circle/>
                            </template>
                        </a-button>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {Message} from '@arco-design/web-vue';
import {getRemoteConfigAttrList, getRemoteConfigAttrValue} from "@/api/marketing/api";
import {booleanRangeList, dateRangeList, itemRangeList, textRangeList, valueRangeList} from '@/views/operation/pack/packData/components/listJson'

const isAnd = ref(true)
const props = defineProps({
    // 回显参数
    clientParams:{
        type:Object,
        default: () => {}
    },
    // 禁用/只读
    disabled:{
        type:Boolean,
        default: false
    },
})
const appCode = ref('')
appCode.value = sessionStorage.getItem('app-id') || ''
const getSymbol = (type:string) => {
  // data是传入calcuSymbo值，用以筛选calcuSymbolName显示值
  const rangeLists = {
    'string': textRangeList,
    'int': valueRangeList,
    'float': valueRangeList,
    'array': itemRangeList,
    'date': dateRangeList,
    'datetime': dateRangeList,
    'boolean': booleanRangeList,
    'variant': itemRangeList,
  };
  return rangeLists[type];
};
const selectList = ref<any>([])
const predicateList = ref<any>([])
const emits = defineEmits(['clientChange'])
const handleParams = () => {
    const ruleList = predicateList.value.map(item => {
        return {
            name: item.code,
            symbol: item.symbol,
            thresholds: item.thresholds,
            valueType: item.valueType,
            obtainMethod: item.obtainMethod
        }
    });
    const ruleObject = ruleList.reduce((acc, item) => {
        acc[item.name] = item;
        return acc; // 确保返回累积对象 acc
    }, {});
    const params = {
        logicalOperation: isAnd.value ? 'and' : 'or',
        rules: ruleObject
    }
    
    emits('clientChange',params)
}
const disableItem = (code) => {
    return predicateList.value.some(item => item.code === code)
}
const symbolChange = (e,index) => {
    predicateList.value[index].thresholds = []
    handleParams()
}

const inputChange = (e,index) => {
    predicateList.value[index].thresholds = [e]
    handleParams()
}
const value1Change = (e,index) => {
  const num1 = predicateList.value[index].number1
  const num2 = predicateList.value[index].number2
  predicateList.value[index].thresholds = [num1]
  if (num1 !== undefined && num2 !== undefined &&num1 >= 0) {
    if(num1 > num2){
        predicateList.value[index].number2 = num1
    }
    predicateList.value[index].thresholds = [num1,num2]
  }
  handleParams()
}

const value2Change = (e,index) => {
  const num1 = predicateList.value[index].number1
  const num2 = predicateList.value[index].number2
  if (num1 !== undefined && num2 !== undefined) {
    if(num2 < num1){
        predicateList.value[index].number1 = num2
    }
    predicateList.value[index].thresholds = [num1,num2]
  }
  handleParams()

}
const singleSelectChange = (e,index) => {
    predicateList.value[index].thresholds = [e]
    handleParams()
}
const multipleSelectChange = (e,index) => {
    predicateList.value[index].thresholds = e
    handleParams()
}
const getList = async () => {
    await getRemoteConfigAttrList({appId:appCode.value}).then(res => {
        // 只取 code 为 client_param 的 children
        const clientParam = (res || []).find(item => item.code === 'client_param');
        selectList.value = clientParam && clientParam.children ? clientParam.children : [];
    })
}
getList()
const formatInputValue = (pre) => {
    if(pre.valueType !=='int' &&['rm','nrm'].includes(pre.symbol) &&pre.obtainMethod === 'dropdown_selection'){
        return pre.thresholds[0]
    }
    if(pre.valueType !=='int' &&!['ex', 'nex'].includes(pre.symbol) &&pre.obtainMethod === 'manual_input'){
        return pre.thresholds[0]
    }
    if(['con','ncon','lt','lteq','gt','gteq'].includes(pre.symbol) &&pre.obtainMethod === 'dropdown_selection'){
        return pre.thresholds[0]
    }
    return ''
}
const formatMultipleValue = (pre) => {
    if(['eq','neq'].includes(pre.symbol) &&pre.obtainMethod === 'dropdown_selection'){
        return pre.thresholds
    }
    return []
}
const formatNumber1 = (pre) => {
    if(pre.valueType ==='int' && !['ex','nex'].includes(pre.symbol) && pre.obtainMethod === 'manual_input'){
        return pre.thresholds[0]
    }
    return undefined
}
const formatNumber2 = (pre) => {
    if(pre.valueType ==='int' && ['scope'].includes(pre.symbol) && pre.obtainMethod === 'manual_input'){
        return pre.thresholds[1]
    }
    return undefined
}
const formatAttrList = async (pre) => {
    let list = [] as any
    const data = {
        appId:appCode.value,
        attribute:pre.name
    }
    if(pre.obtainMethod === 'dropdown_selection'){
        await getRemoteConfigAttrValue(data).then(res => {
            list = res || []
        })
    }
    return list
}
const findItemByCode = (list, code) => {
    const foundItem = list.find(item => item.code === code);
    if (foundItem) {
        return foundItem;
    }
    const foundInChildren = list.flatMap(item => item.children || []).find(child => child.code === code) || null;
    return foundInChildren;
};
const formatRules = async (arr:any) => {
    if(selectList.value.length === 0){
        await getList()
    }
    const list = await Promise.all(arr.map(async (item) => {
        return {
            name: findItemByCode(selectList.value, item.name)?.name,
            code: item.name,
            valueType: item.valueType,
            obtainMethod: item.obtainMethod,
            thresholds: item.thresholds,
            symbol: item.symbol,
            attrLoading: false,
            attrList: await formatAttrList(item),
            inputValue: formatInputValue(item),
            multipleValue: formatMultipleValue(item),
            number1: formatNumber1(item),
            number2: formatNumber2(item),
        }
    }));
    return list;
}
// 格式化禁用状态多选值悬浮显示
const formatMultipleValueDisplay = (pre) => {
  if (!pre.multipleValue || !pre.multipleValue.length) return;
  // 将选中的值转换为对应的名称
  const selectedNames = pre.multipleValue.map(value => {
    const option = pre.attrList?.find(item => item.code === value);
    return option ? option.name : value;
  });
  
  // 返回格式化后的字符串
  return selectedNames.join(', ');
};
// 添加条件
const addItem = () => {
    // 找到第一个未被选中的参数
    const usedCodes = predicateList.value.map(item => item.code);
    const nextItem = selectList.value.find(item => !usedCodes.includes(item.code));
    if (!nextItem) {
        Message.warning('已无可选客户端参数');
        return;
    }
    const symbolValue = nextItem.code === 'country' ? 'con' : 'eq';
    predicateList.value.push({
        code: nextItem.code,
        name: nextItem.name,
        valueType: nextItem.valueType,
        obtainMethod: nextItem.obtainMethod,
        inputValue: '',
        symbol: symbolValue,
        multipleValue: [],
        number1: undefined,
        number2: undefined,
        thresholds: [],
        attrList: [],
        attrLoading: false
    });
    handleParams();
}
// 删除条件
const deleteItem = (index:number) => {
    predicateList.value.splice(index,1)
    handleParams()
}
const selectPopup = async (v,code) => {
    if(v){
        const index = predicateList.value.findIndex(item => item.code === code)
        predicateList.value[index].attrLoading = true
        const data = {
            appId:appCode.value,
            attribute:code
        }
        await getRemoteConfigAttrValue(data).then(res => {
            predicateList.value[index].attrList = res || []
        })
        predicateList.value[index].attrLoading = false
    }
}
const logicalChange = () => {
    if(!props.disabled){
        isAnd.value = !isAnd.value
    }
}
const conditionSelect = (v,index) => {
    const obj = findItemByCode(selectList.value,v)
    predicateList.value[index].code = obj?.code
    predicateList.value[index].name = obj?.name
    predicateList.value[index].valueType = obj?.valueType
    predicateList.value[index].obtainMethod = obj?.obtainMethod
    if(['country'].includes(v)){
        predicateList.value[index].symbol = 'con'
    }else{
        predicateList.value[index].symbol = 'eq'
    }
    predicateList.value[index].inputValue = ''
    predicateList.value[index].number1 = undefined
    predicateList.value[index].number2 = undefined
    predicateList.value[index].thresholds = []
    predicateList.value[index].attrList = []
    handleParams()
}

// watch(predicateList,() => {
//     const ruleList = predicateList.value.map(item => {
//         return {
//             name:item.code,
//             symbol:item.symbol,
//             thresholds:item.thresholds,
//             valueType:item.valueType,
//             obtainMethod:item.obtainMethod
//         }
//     })
//     const ruleObject = ruleList.reduce((acc, item) => {
//         acc[item.name] = item;
//         return acc;
//     }, {});
//     emits('clientChange',ruleObject)
// },{deep:true})
watch(() => props.clientParams,async () => {
    if(props.clientParams){
        isAnd.value = props.clientParams.logicalOperation === 'and'
        if(props.clientParams.rules && Object.keys(props.clientParams.rules).length){
            const arr = Object.entries(props.clientParams.rules).map(([key, value]) => ({
                ...value
            }));
            predicateList.value = await formatRules(arr)
        }else{
            predicateList.value = []
        }
    }
},{immediate:true,deep:true})
defineExpose({
  addItem
})
</script>

<style lang="less" scoped>
.select-btn {
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;

    .btn-icon {
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }

    .filter-label {
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }

    &:hover {
        border-color: var(--tant-primary-color-primary-hover);
    }
}
.predicate{
    display: flex;
    align-items: center;
    padding: 4px 0;
    .input-content{
        // width: 230px;
        min-width: 120px;
        margin: 0 4px;
        height: 100%;
        :global(.arco-select-view-input){
            height: 100%!important;
        }
    }
    :deep(.arco-select-view-value){
        min-height: 28px!important;
    }
    :deep(.arco-select-view-input){
        padding-bottom: 4px!important;
    }
    :deep(.arco-select-view-tag){
        white-space: nowrap!important;;
    }
    .sub-action-right {
        align-items: flex-start;
        height: auto;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
        margin-left: 8px;
    }

    &:hover .sub-action-right {
        opacity: 1;
    }
}
.btn-26 {
    width: 26px;
    height: 26px;
    border-radius: 4px;
}

.btn-bg-delete {
    background-color: transparent;
    &:hover {
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
.guide {
    width: 100%;
    // margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .event-filter-box {

        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;

        .action-row {
        position: relative;
        height: auto;
        width: 100%;
        min-height: 24px;
        line-height: 24px;
        padding-right: 24px;
        padding-left: 24px;

        .select-btn {
            display: inline-flex;
            align-items: center;
            min-width: 40px;
            max-width: 200px;
            height: 26px;
            padding: 0 8px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: top;
            background-color: var(--tant-secondary-color-secondary-fill);
            border: 1px solid transparent;
            border-radius: 4px;
            cursor: pointer;
            transition: all .3s;
            box-sizing: border-box;

            .btn-icon {
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            margin-right: 5px;
            }

            .filter-label {
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
            }

            &:hover {
            border-color: var(--tant-primary-color-primary-hover);
            }
        }

        &:hover {
            background-color: var(--tant-fill-color-fill1-2);
            :deep(.select-btn){
                background: #fff;
            }
            .action-right {
                opacity: 1;
            }
        }
        }

        .row-filters {
            // padding-left: 38px;
            .relation-editor {
                box-sizing: border-box;
                position: relative;
                display: flex;
                width: 100%;
                height: 100%;

                .relation-relation {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
                width: 24px;
                margin-right: 8px;
                flex-shrink: 0;

                .relation-relation-line,.relation-relation-disabled-line {
                    position: absolute;
                    left: 12px;
                    width: 1px;
                    background-color: var(--tant-border-color-border1-2);
                    transition: all .3s;
                    top: 6px;
                    height: calc(100% - 12px);
                }
                .relation-relation-value,.relation-relation-disabled-value{
                    position: absolute;
                    text-transform: uppercase;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%);
                    height: 24px;
                    padding: 0 5px;
                    margin-top: -12px;
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 12px;
                    line-height: 22px;
                    text-align: center;
                    background-color: #fff;
                    border: 1px solid var(--tant-border-color-border1-2);
                    border-radius: 4px;
                    word-break: keep-all;
                    transition: all .3s;
                }
                &:hover .relation-relation-line{
                    background-color: var(--tant-primary-color-primary-default);
                }
                &:hover .relation-relation-value{
                    color: var(--tant-primary-color-primary-default);
                    border-color: var(--tant-primary-color-primary-default);
                }
                .relation-relation-value{
                    cursor: pointer;
                }
                &:hover .relation-relation-line{
                    background-color: var(--tant-primary-color-primary-default);
                }
                &:hover .relation-relation-value{
                    color: var(--tant-primary-color-primary-default);
                    border-color: var(--tant-primary-color-primary-default);
                }
                }

                .relation-main {
                flex: 1 1;

                .relation-row {
                    box-sizing: border-box;

                    .multi-filter-condition {
                    .sub-action-row {
                        padding: 4px 0;
                        align-items: center;
                        display: flex;
                        height: auto;

                        .sub-action-left {
                        align-items: flex-start;
                        height: auto;
                        display: flex;
                        flex-wrap: wrap;
                        }
                    }
                    }
                }
                }
            }
        }
    }
}
.option-content{
    padding-bottom: 4px;
    .option-title{
        list-style: 16px;
    }
    .option-desc{
        font-size: 12px;
        color: var(--color-text-2);
        line-height: 16px;
    }
}
.tip-content{
    border: 1px solid var(--color-secondary);
    border-radius: 8px;
    color: rgb(var(--primary-5));
    margin-top: 8px;
    padding: 16px;
}
.word {
    margin-right: 6px;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;
    flex-shrink: 0;
}

.next-input {
    display: inline-flex;
    align-items: center;
    vertical-align: top;
    margin-left: 8px;
    flex: 1;
}

.next-input-number {
    // width: 60px;
    margin-right: 8px;
}
.name-text{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>
