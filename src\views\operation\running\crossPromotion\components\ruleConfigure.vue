<template>
    <a-modal v-model:visible="modalVisible" :width="900" title-align="start" @cancel="handleCancel">
        <template #title>
            Racing3D 规则配置
        </template>
        <a-spin :loading="loading" class="configure-content">
            <div class="json-box">
                <div class="label">JSON DATA</div>
                <json-editor-vue v-model="config" class="editor" style="height: 100%; position: relative" @update:modelValue="updataModel" @validationError="editError"/>
            </div>
            <div class="list-box">
                <div class="remark">
                    <span>备注：可拖拽排序</span>
                    <div title="添加" class="btn-icon" @click="addDetailList"><icon-plus /></div>
                </div>
                <div v-if="addDetailInputList.length>0" class="add-box">
                    <div v-for="(value,valueIndex) in addDetailInputList" :key="valueIndex" class="input-list">
                        <a-input v-model:model-value="value.appId" @press-enter="detailInputEnter(value,valueIndex)"/>
                        <div class="delete-icon" @click="deleteDetailInputItem(valueIndex)">
                        <icon-close-circle />
                        </div>
                    </div>
                </div>
                <a-list v-if="detailList.length>0">
                    <draggable :list="detailList" handle=".drag-handle">
                        <template #item="{ element,index }">
                            <a-list-item style="padding: 8px;">
                                <a-list-item-meta :description="`${element.appId}_${element.name}`" >
                                <template #avatar>
                                    <div style="display: flex;align-items: center;">
                                        <div class="drag-handle">
                                            <icon-drag-dot-vertical size="16"/>
                                        </div>
                                        <a-avatar shape="square">
                                            <img
                                                alt="avatar"
                                                :src="element.icon ? element.icon :'/src/assets/images/default.png'"
                                            />
                                        </a-avatar>
                                    </div>
                                </template>
                                </a-list-item-meta>
                                <template #actions>
                                    <icon-delete @click="deleteDetailItem(index)"/>
                                </template>
                            </a-list-item>
                        </template>
                    </draggable>
                    
                </a-list>
            </div>
        </a-spin>
        <template #footer>
            <div class="footer">
                <a-button style="margin-right: 10px;" class="cancel" @click="handleCancel">close</a-button>
                <a-button type="primary" :loading="saveLoading" @click="handleOk">
                    保存
                </a-button>
            </div>
        </template>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import JsonEditorVue from 'json-editor-vue3'
import draggable from 'vuedraggable'
import {appSearch, getCrossDetail, saveCrossPromotion} from "@/api/marketing/api";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const loading = ref(false)
const saveLoading = ref(false)
const config = ref<any>({})
const detailList = ref<any>([])
// 是否存在错误
const errors = ref(0);
// 错误行数
const line = ref();
// 数据更新时触发
const updataModel = (val: any) => {
    config.value = val;
};
// 数据错误时触发
const editError = (a: any, error: any) => {
  errors.value = error.length;
  console.log(errors.value,'errors.value');
  
  if (error[0]) {
    line.value = error[0].line;
  }
}
interface InputData {
    appId: string;
}
  // 添加详情列表
const addDetailInputList = ref<InputData[]>([])
const addDetailList = () => {
    addDetailInputList.value.push({
        appId:''
    })
}
// 删除detail-input
const deleteDetailInputItem = (index:number) => {
    addDetailInputList.value.splice(index,1)
}
// detail-input回车操作
const detailInputEnter = (value,index) => {
    if(value.appId){
        try{
            appSearch(value.appId).then(res => {
                detailList.value.push(res)
                addDetailInputList.value.splice(index,1)
            })
        } catch (error){
            console.error(error)
        }
    }
}
const deleteDetailItem = (index:number) =>{
    detailList.value.splice(index,1)
}
const parentsData = reactive({
    category:'',
    name:''
})
const openModal = async (params) => {
    modalVisible.value = true
    loading.value = true
    const {category,name} = params
    parentsData.category = category
    parentsData.name = name
    try {
        await getCrossDetail(category,name).then(res => {
            if(res){
                detailList.value = res?.appsInfo
            }
        })
    } catch (error) {
      console.error('初始化数据失败:', error)
    } finally {
      loading.value = false
    }
    
}
const handleOk = () => {
    console.log(detailList.value,'detailList.value');
    saveLoading.value = true
    try{
        const regular = detailList.value.map(item => item.appId).join(',');
        const paramsData = {
            category:parentsData.category,
            name:parentsData.name,
            regularData:config.value,
            regularApps:regular
        }
        saveCrossPromotion(paramsData).then(res => {
            Message.success('创建成功')
            modalVisible.value = false;
        })
    } catch (error) {
      console.error( error)
    } finally {
        saveLoading.value = false
    }
    // modalVisible.value = false;
};
const handleCancel = () => {
    addDetailInputList.value = []
    modalVisible.value = false
}
defineExpose({openModal})
</script>

<style scoped lang="less">
.drag-handle{
    cursor: grab;
    margin-right: 8px;
}
.configure-content{
    height: 100%;
    width: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);
    display: flex;
    .json-box{
        width: 50%;
        border-right: 1px solid var(--color-secondary);
        height: 660px;
        overflow-y: scroll;
        .label{
            font-weight: 700;
            padding: 10px 15px;
            background-color: var(--tant-fill-color-fill1-2);
            width: 100%;
        }
    }
    .list-box{
        width: 50%;
        max-height: 660px;
        overflow-y: scroll;
        padding-left: 12px;
        .remark{
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: var(--tant-border-radius-medium);
            padding: 10px 15px;
            position: relative;
            margin-top: 15px;
            margin-bottom: 8px;
            width: 100%;
            background-color: var(--tant-fill-color-fill1-2);
            .btn-icon{
                position: absolute;
                right: 12px;
                top: 8px;
                cursor: pointer;
            }
        }
    }
}
:deep(.jsoneditor-statusbar){
    display: none;
}
:deep(.jsoneditor-menu){
    display: none;
}
:deep(.jsoneditor){
    border: none;
}
:deep(.full-screen){
    display: none !important;
}
.add-box{
    background: #fff;
    .input-list{
        margin-bottom: 12px;
        position: relative;
    }
    .delete-icon{
        position: absolute;
        right: 12px;
        top: 4px;
        cursor: pointer;
    }
}
.footer{
    display: flex;
    justify-content: flex-end;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>