<script setup lang="ts">
import {computed, h, onMounted, ref} from "vue";
import {SpaceDto} from "@/api/space/type";
import cubeImg from '/public/icon/cube-open.svg';
import _ from "lodash";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {ObjectPermissionType} from "@/api/enum";
import {listSpace} from "@/api/space/api";
import {FolderDto} from "@/api/folder/type";
import {Message} from "@arco-design/web-vue";
import {useDashboardStore} from "@/store";
import {moveFolder} from "@/api/folder/api";

interface Props {


  /**
   * 看板
   */
  folder?: FolderDto
}

/*
 *我的看板
 */

const mySpace = ref<SpaceDto>();
/*
 *项目空间
 */
const props = defineProps<Props>()
const projectSpaces = ref<SpaceDto[]>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const visible = defineModel<boolean>("visible", {default: false});
const dashboardPosition = ref(props.folder?.space.name === '我的看板' ? 'other' : 'my');
const projectPositionSelected = ref<string>();
const dashboardStore = useDashboardStore()
const cacheEventBus = useEventBus(CacheEventBus)
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})


// const projectPosition = computed(() => {
//   return projectSpaces.value?.map(space => {
//     return {
//       'key': `space-${space.spaceId}`,
//       'title': space.name,
//       'icon': () => h(cubeImg),
//     } as TreeNodeData
//   }) || []
// })
const projectPosition = computed(() => {
  return projectSpaces.value?.map(space => {
    return {
      'key': `${space.spaceId}`,
      'title': space.name,
      'icon': () => h(cubeImg),
    }

  })
})
const handleOk = () => {
  const folderID = props.folder?.folderId
  const spaceID = dashboardPosition.value === 'my' ? mySpace.value?.spaceId : projectPositionSelected.value
  if (spaceID) {
    moveFolder({folderId: folderID, spaceId: spaceID})
        .then((resp) => {

          if (folderID === dashboardStore.dashboardSelected?.folderId) {
            treeMenuEventBus.emit(RefreshEvent);
          } else {
            cacheEventBus.emit('move-event', {
              type: 'folder',
              folderId: folderID,
              spaceId: spaceID
            });
            visible.value = false;
          }

        })
        .catch((e) => {
          Message.error("重命名失败！", e);
        });
    if (props.folder?.dashboards?.find(item => item.dashboardId === dashboardSelected.value.dashboardId)) {
      dashboardSelected.value = {
        ...dashboardSelected.value,
        spaceId: spaceID
      };
    }

  } else {
    Message.warning('请选择移动至位置')
  }


}

const handleCancel = () => {
  visible.value = false;
}
/**
 * 刷新菜单
 */
const refreshMenu = () => {
  listSpace(true).then((resp: SpaceDto[]) => {
    const spaces = resp || []
    const mySpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority === ObjectPermissionType.OWNER
    })

    const projectSpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority !== ObjectPermissionType.OWNER
    })

    if (!_.isEmpty(mySpaceList) && mySpaceList.length === 1) {
      // eslint-disable-next-line prefer-destructuring
      mySpace.value = mySpaceList[0];
    }

    if (!_.isEmpty(projectSpaceList) && projectSpaceList.length > 0) {
      projectSpaces.value = projectSpaceList;
    }
  })
}

treeMenuEventBus.on((event) => {
  if (event === RefreshEvent) {
    refreshMenu()
  }
})
const disabledMove = computed(() => projectSpaces.value?.length === 1)
/**
 * 初始化
 */
onMounted(() => {
  refreshMenu()
  if (props.folder?.space.name !== '我的看板') {
    projectPositionSelected.value = props.folder?.space?.spaceId
  }
})

</script>

<template>
  <a-modal
      :visible="visible"
      :mask-closable="false"
      :width="392"
      title-align="start"
      ok-text="移动"
      title="移动至"
      @ok="handleOk"
      @cancel="handleCancel">

    <a-row class="item" align="center">
      <a-col :span="24">
        <a-radio-group
            v-model:model-value="dashboardPosition" type="button"
            @change="value => {dashboardPosition=value}">
          <a-radio :disabled="props.folder?.space.name==='我的看板'" value="my" class="disabled-radio">我的看板
          </a-radio>
          <a-radio value="other">项目空间</a-radio>
        </a-radio-group>
      </a-col>
    </a-row>
    <a-row v-if="dashboardPosition==='my'" class="item" align="center">
      <a-col :span="24">
        <span>
          移动后，将取消空间成员的共享设置
        </span>
      </a-col>
      <a-col :span="24">
         <span>
        文件夹中他人创建的看板将自动移动至空间根目录
        </span>
      </a-col>
    </a-row>
    <a-row v-if=" dashboardPosition==='other'" class="item" align="center">
      <a-col :span="24" style="padding-bottom: 16px">
        <a-select
            v-model="projectPositionSelected"
            placeholder="请选择添加位置">
          <div v-for="item in projectPosition" :key="item.key" class="select-options">
            <a-option :value="item.key">
              <div class="option-item">
                <img src="/icon/cube-open.svg" alt="看板空间"/>{{ item.title }}
              </div>
            </a-option>
          </div>
        </a-select>
      </a-col>
      <a-col :span="24">
        <span>
          移动后，对目标空间成员共享该文件夹
        </span>
      </a-col>
      <a-col :span="24">
         <span v-if="props.folder?.space.authority===1">
        文件夹中他人创建的看板将移动至[分享给我的]文件夹
        </span>
        <span v-else>文件夹中他人创建的看板将移动至当前空间根目录</span>
      </a-col>
    </a-row>
  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}

.space-select-option {
  display: flex;
  align-items: center;
  justify-items: start;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

.select-options {
  width: 100%;
  padding: 5px;

  .option-item {
    display: flex;
    gap: 5px;
    align-items: center;
  }
}
</style>