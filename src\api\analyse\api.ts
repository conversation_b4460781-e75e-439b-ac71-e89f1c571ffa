// import axios from 'axios';
// import type { TableData } from '@arco-design/web-vue/es/table/interface';
import {AxiosPromise} from "axios";
import {useSessionStorage} from '@vueuse/core'
import {getRequest, postRequest} from "@/api/request";
import {ReportAnalyseModel} from "@/api/analyse/type";
import {ReportDataRequest, ReportQueryResponse} from "@/api/type";

export function getAnalyseList(): AxiosPromise<any> {
  return getRequest<any>('/api/sys/table/list', {});
}

export function getAnalyseColumns(catalog: string, schema: string, table: string): AxiosPromise<any> {
  return getRequest<any>('/api/sys/table/columns/list', {catalog, schema, table});
}

export function getEventAttributeBatch(params): AxiosPromise<any> {
  return postRequest<any>('/api/evt/event/attribute/intersection', {...params});
}
export function getAnalyseEvent(params): AxiosPromise<any> {
  return postRequest<any>('/api/evt/event/attribute/list', {...params});
}
export function getAttributeEnum(params:any): AxiosPromise<any> {
  return postRequest<any>('/api/evt/event/attribute/enum', {...params});
}

export function getAnalyseEventList(params): AxiosPromise<any> {
  return getRequest<any>(`/api/evt/event/list?types=${params?.types}&in_app=${params?.inApp}`, {});
}
export function getAnalyseIndicatorList(data): AxiosPromise<any> {
  const params = new URLSearchParams();
  data?.types.forEach(type => {
      params.append('types', type);
  });
  // 处理 inApp
  if (data?.inApp) {
    params.append('in_app', data.inApp);
  }

  // 处理 subjectCode
  if (data?.subjectCode) {
    params.append('subject_code', data.subjectCode);
  }

  return getRequest<any>(`/api/evt/indicator/list?${params.toString()}`, {});
}
export function getAnalyseUserList(): AxiosPromise<any> {
  return getRequest<any>('/api/evt/user/attribute/list', {});
}
export function getAnalyseClusterList(): AxiosPromise<any> {
  return getRequest<any>('/api/evt/user/cluster/list', {});
}
export function getAnalyseTagList(): AxiosPromise<any> {
  return getRequest<any>('/api/evt/user/tag/list', {});
}
export function getAnalyseHistoryList(): AxiosPromise<any> {
  return getRequest<any>('/api/analyse/query/log/list', {});
}

export function getAnalyseFavoriteList(): AxiosPromise<any> {
  return getRequest<any>('/api/analyse/query/favorite/list', {});
}

export function delAnalyseFavoriteList(favoriteCode: string): AxiosPromise<any> {
  return getRequest<any>('/api/analyse/query/favorite/remove', {favoriteCode});
}

export function saveAnalyseFavoriteList(name: string, sql: string): AxiosPromise<string> {
  return postRequest<string>('/api/analyse/query/favorite/save', {'name': name, 'query_param': {sql}});
}

export function getAnalyseReportList(): AxiosPromise<string> {
  return getRequest<string>('/api/analyse/report/list', {});
}

// 应用分析过滤属性
export function getOperationFilterList(analyzeSubjectCode:string): AxiosPromise<string> {
  return getRequest<string>('/api/op/operation_attribute/filter/list', {analyzeSubjectCode});
}
// 应用分析分组
export function getOperationGroupList(analyzeSubjectCode:string): AxiosPromise<string> {
  return getRequest<string>('/api/op/operation_attribute/group/list', {analyzeSubjectCode});
}

export function saveAnalyseReportList(model: ReportAnalyseModel, reportId?: string,dashboardId?: string, name?: string, description?: string, queryParam?: any,chartParams?:any, chartType?: string): AxiosPromise<string> {
  return postRequest<string>('/api/analyse/report/save', {
    model,
    reportId,
    dashboardId,
    name,
    description,
    queryParam,
    chartParams,
    chartType,
    appId:sessionStorage.getItem('app-id')
  });
}

export function delAnalyseReportList(reportId:string): AxiosPromise<string> {
  return getRequest<string>('/api/analyse/report/remove', {reportId});}

// 报表管理请求我的报表
export function getManageMyList(): AxiosPromise<any> {
  return getRequest<any>('/api/analyse/report/manage/list', {});
}
export function getSelectList(url:string): AxiosPromise<any> {
  return getRequest<any>(url, {});
}

export function reportAnalyseRequest(requestData: ReportDataRequest): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/query', requestData);
}
// 看板列表
export function getAppDashboardList(type:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analyse/dashboard/app/list?dashboard_type=${type}`, {});
}
// 应用看板保存
export function appDashboardSave(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analyse/dashboard/app/save', {...params});
}
// 设置默认应用看板
export function setDefaultAppDashboard(params): AxiosPromise<any> {
  return postRequest<any>('/api/analyse/dashboard/app/default', {...params});
}
// 应用看板分享
export function appDashboardShare(params): AxiosPromise<any> {
  return postRequest<any>('/api/analyse/dashboard/app/share', {...params});
}
// 应用看板分享详情
export function getAppDashboardShareDetail(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analyse/dashboard/app/share/detail?dashboard_code=${code}`);
}
// 获取渠道筛选
export function getMediaSourceList(params): AxiosPromise<string> {
  return getRequest<string>('/api/placement/media_source', {...params});
}
// 获取代理筛选
export function getAgencyList(params): AxiosPromise<string> {
  return getRequest<string>('/api/placement/agency', {...params});
}
// 广告成效接口
export function getPlacementRoi(params): AxiosPromise<string> {
  return postRequest<string>('/api/placement/roi', {...params});
}
// 广告留存接口
export function getPlacementRetention(params): AxiosPromise<string> {
  return postRequest<string>('/api/placement/retention', {...params});
}
// 变现分平台(分版位)ECPM
export function getPlacementEcpm(params): AxiosPromise<string> {
  return getRequest<string>('/api/placement/ecpm', {...params});
}
// 预测趋势接口
export function getRoiPredictTrend(params): AxiosPromise<string> {
  return postRequest<string>('/api/placement/roi_predict_trend', {...params});
}
// 指标组视图列表
export function getAnalyticsViewList(params?:any): AxiosPromise<string> {
  return getRequest<string>('/api/analytics/view/list', {...params});
}
// 指标组视图详情
export function getAnalyticsViewDetail(code:string): AxiosPromise<string> {
  return getRequest<string>(`/api/analytics/view/detail?code=${code}`, {});
}
// 指标组视图新建
export function analyticsViewAdd(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/view/add', {...params});
}
// 指标组视图更新
export function analyticsViewUpdate(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/view/update', {...params});
}
// 媒体分析视图新增/更新
export function saveMediaAnalysisView(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/view/media_update', {...params});
}
// 媒体分析视图详情
export function getMediaAnalysisViewDetail(name:string): AxiosPromise<ReportQueryResponse> {
  return getRequest<ReportQueryResponse>(`/api/analytics/view/media_detail?name=${name}`, {});
}
// 目标列表
export function getProfitTargetList(params?:any): AxiosPromise<string> {
  return getRequest<string>('/api/analytics/profit/target/list', {...params});
}
// 目标保存
export function saveProfitTarget(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/profit/target/save', {...params});
}
// 广告成效获取应用列表
export function getPlacementAppList(): AxiosPromise<string> {
  return getRequest<string>('/api/placement/app/list', {});
}
// 事件指标分类列表
export function getEvtCategoryList(params?: any): AxiosPromise<string> {
  const queryParams = new URLSearchParams();

  if (params?.inApp !== undefined) {
    queryParams.append('in_app', params.inApp);
  }
  if (params?.objectType !== undefined) {
    queryParams.append('object_type', params.objectType);
  }
  if (params?.isGroup !== undefined) {
    queryParams.append('is_group', params.isGroup);
  }
  if (params?.subjectCode !== undefined) {
    queryParams.append('subject_code', params.subjectCode);
  }
  if (params?.eventTypes?.length) {
    params.eventTypes.forEach((type: string) => {
      queryParams.append('event_types', type);
    });
  }
  if (params?.indicatorTypes?.length) {
    params.indicatorTypes.forEach((type: string) => {
      queryParams.append('indicator_types', type);
    });
  }

  return getRequest<string>(`/api/evt/category/list?${queryParams.toString()}`, {});
}
// 游戏收藏添加
export function saveFavorite(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/evt/favorite/save', {...params});
}
// 游戏收藏取消
export function cancelFavorite(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/evt/favorite/cancel?code=${code}`, {});
}
// 筛选分类列表
export function getFilterCategoryList(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/evt/filter/category/list', {...params});
}

// 策略分析分组统计信息
export function getStrategyGroupData(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/strategy/group/data', {...params});
}
// 策略分析策略统计信息
export function getStrategyGamewayData(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/strategy/gameway/data', {...params});
}
// 策略分析分组列表
export function getStrategyGroupList(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/strategy/group/list', {...params});
}
// 策略分析版本号列表
export function getStrategyVersionList(params): AxiosPromise<ReportQueryResponse> {
  return postRequest<ReportQueryResponse>('/api/analytics/strategy/app_version/list', {...params});
}
// 分析数据全量下载
export function getAnalyticsDataDownload(model: any, queryParam?: any): AxiosPromise<ReportQueryResponse> {
  const appId = useSessionStorage('app-id', '')?.value;
  return postRequest<ReportQueryResponse>('/api/analytics/download', {
    appId,
    model,
    queryParam,
  });
}

// 归因分析
// 获取视图列表
export function getAttributionViews(viewCode:string,dashboardCode?:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analytics/traffic/attribution/view/list?subject_code=${viewCode}${dashboardCode ? `&dashboard_code=${dashboardCode}` : ''}`);
}
// 获取视图详情
export function getAttributionViewDetail(code:string,dashboardCode?:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analytics/traffic/attribution/view/detail?view_code=${code}${dashboardCode ? `&dashboard_code=${dashboardCode}` : ''}`);
}
// 视图保存
export function saveAttributionView(data:any): AxiosPromise<any> {
  return postRequest<any>('/api/analytics/traffic/attribution/view/save', {...data});
}
// 视图删除
export function deleteAttributionView(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analytics/traffic/attribution/view/remove?view_code=${code}`);
}
// 设置默认视图
export function setDefaultAttributionView(data): AxiosPromise<any> {
  return postRequest<any>('/api/analytics/traffic/attribution/view/default', {...data});
}
// 分享视图
export function shareAttributionView(data): AxiosPromise<any> {
  return postRequest<any>('/api/analytics/traffic/attribution/view/share', {...data});
}
// 视图分享详情
export function getAttributionViewShareDetail(code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/analytics/traffic/attribution/view/share/detail?view_code=${code}`);
}
// 团队列表
export function getTeamList(): AxiosPromise<any> {
  return getRequest<any>('/api/analytics/team/list');
}
// 团队概览
export function getTeamOverview(params: { teamCode?: string; appId?: string }): AxiosPromise<any> {
  const urlParams = new URLSearchParams();
  if (params.teamCode) {
    urlParams.append('team_code', params.teamCode);
  }
  if (params.appId) {
    urlParams.append('app_id', params.appId);
  }
  const queryString = urlParams.toString();
  return getRequest<any>(`/api/analytics/team/overview${queryString ? `?${queryString}` : ''}`);
}
// 团队花费
export function getTeamCost(params: { teamCode?: string; appId?: string }): AxiosPromise<any> {
  const urlParams = new URLSearchParams();
  if (params.teamCode) {
    urlParams.append('team_code', params.teamCode);
  }
  if (params.appId) {
    urlParams.append('app_id', params.appId);
  }
  urlParams.append('media_source_type', 'spend');
  const queryString = urlParams.toString();
  return getRequest<any>(`/api/analytics/team/spend${queryString ? `?${queryString}` : ''}`);
}
// 团队变现
export function getTeamRevence(params: { teamCode?: string; appId?: string }): AxiosPromise<any> {
  const urlParams = new URLSearchParams();
  if (params.teamCode) {
    urlParams.append('team_code', params.teamCode);
  }
  if (params.appId) {
    urlParams.append('app_id', params.appId);
  }
  urlParams.append('media_source_type', 'revenue');
  const queryString = urlParams.toString();
  return getRequest<any>(`/api/analytics/team/revenue${queryString ? `?${queryString}` : ''}`);
}
// 团队总体数据
export function getTeamBasicTable(data): AxiosPromise<any> {
  return postRequest<any>('/api/analytics/team/basic_table', {...data});
}
// 团队年度累计利润
export function getTeamYearProfit(params): AxiosPromise<any> {
  const urlParams = new URLSearchParams();
  if (params.year) {
    urlParams.append('year', params.year);
  }
  if (params.teamCode) {
    urlParams.append('team_code', params.teamCode);
  }
  if (params.appId) {
    urlParams.append('app_id', params.appId);
  }
  const queryString = urlParams.toString();
  return getRequest<any>(`/api/analytics/team/annual_cumulative_profit${queryString ? `?${queryString}` : ''}`);
}

// 助攻分析查询接口
export function getContributorAnalysisData(params): AxiosPromise<any> {
  return postRequest<any>('/api/analytics/contributor/query', {...params});
}
// ASA 分版本
export function getAsaPlacement(params): AxiosPromise<any>{
  return postRequest<any>('/api/analytics/traffic/channel/asa/placement', {...params});
}
// ASA聚合数据
export function getAsaAggregation(params): AxiosPromise<any>{
  return postRequest<any>('/api/analytics/traffic/channel/asa/aggregate', {...params});
}
// ASA分组列表
export function getAsaGroup(): AxiosPromise<any> {
  return getRequest<any>(`/api/analytics/traffic/channel/asa/group`, {});
}
