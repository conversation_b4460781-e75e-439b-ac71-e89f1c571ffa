

<template>
    <div v-for="(item,index) in eventListData" :key="index" class="action-row">
        <div class="filter-row-eventRow">
            <div class="action-left">
                <div class="row-content">
                    <div>
                        <div v-for="(event,eventIndex) in  item.eventList" :key="eventIndex" class="event-item">
                            <panelCheckBox :panel-data="event" @analysis-index-change="panelSelectChange(index,eventIndex,$event)"/>
                        </div>
                    </div>
                    <div v-if="item.filtersList.length === 0" class="row-foot">
                        <div class="ta-filter-button" @click="add(index)">
                            <svg class="action" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M9.536 13H7.329a3 3 0 110-2h2.207L13 5h3.17a3.001 3.001 0 11.001 2h-2.017l-2.886 4.999L14.155 17h2.016a3.001 3.001 0 110 2H13l-3.464-6zM19 17a1 1 0 100 2 1 1 0 000-2zM4.5 11a1 1 0 100 2 1 1 0 000-2zM19 5a1 1 0 100 2 1 1 0 000-2z"></path></svg></svg>
                            <span class="label">事件拆分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <eventQueryFilter :ref="el => queryFilterRefs[index] = el" :filter="item.filter" :code-list="item.eventList" :show-detail-filter="true" :only-event="true" @query-filters-change="queryFiltersChange(index,$event)"/>
        <div v-if="item.filtersList&&item.filtersList.length" class="row-foot">
            <div class="ta-filter-button" @click="add(index)">
                <icon-plus class="action"/>
                <span class="label">拆分项</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import {useEventBus} from '@vueuse/core';
import {Indicator} from "@/api/analyse/type";
import panelCheckBox from "@/views/analyse/components/panelCheckBox.vue"
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import {cloneDeep} from "lodash"


const props = defineProps({
  handleList:{
    type:Array,
    default:() => []
  },
  showSub:{
    type:Boolean,
    default:false
  },
})
const eventBus = useEventBus('eventList');

const eventDropName = ref('用户登录')
const eventDropType = ref('初始事件')
const eventDropSelect = (v) => {
    eventDropName.value = v
}
const eventTypeSelect = (v) => {
    eventDropType.value = v === 'begin' ? '初始事件' : '结束事件'
}

const emits = defineEmits(['traceChange'])

const indicator = ref<Indicator[]>([])

const eventListData = ref<any>([])
eventListData.value = cloneDeep(props.handleList)
const queryFilterRefs = ref<any>([])

const panelSelectChange = (index:number,eventIndex:number,e:any) => {
    const {eventName,type} = e
    const filter = { ...eventListData.value[index].eventList[eventIndex] };
    filter.eventName = eventName;
    filter.type = type;
    eventListData.value[index].eventList[eventIndex] = filter;
    eventListData.value[index].name = eventName
}


// 添加并列条件
const add = (index1:number) => {
    queryFilterRefs.value[index1].add()
}
const queryFiltersChange = (index:number,v) => {
  eventListData.value[index].filter = v;
}



watch(eventListData.value, (newValue, oldValue) => {
    indicator.value = newValue.map(item => {
        return {
            displayName: item.displayName,
            displayType:item.displayType,
            name: item.name,
            type: item.type,
            eventList: item.eventList,
            filter: item.filter
        }
    })
    emits('traceChange',indicator.value)

},{immediate:true})
</script>

<style scoped lang="less">
.action-row{
    position: relative;
    height: auto;
    width: 100%;
    min-height: 24px;
    line-height: 24px;
    padding-right: 24px;
    padding-left: 24px;
    .action-left{
        align-items: flex-start;
        height: auto;
        display: flex;
        .row-content{
            flex-grow: 1;
            box-sizing: border-box;
            padding: 4px 0;
            .rename{
                min-width: 80px;
                max-width: calc(100% - 50px);
                height: 24px;
                padding: 0;
                line-height: 24px;
                background: inherit;
                margin-bottom: 6px;
                // font-weight: 600;
                font-size: 14px;
                display: flex;
                align-items: center;
                .placeholder{
                    max-width: 260px;
                    display: inline-block;
                    height: 32px;
                    line-height: 32px;
                    padding: 0 10px;
                    overflow: hidden;
                    font-size: 14px;
                    // white-space: pre;
                    // vertical-align: middle;
                    &:hover{
                        color: var(--tant-primary-color-primary-default);
                    }
                }
                :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                    font-weight: 600!important;
                }
                :deep(.arco-input-wrapper){
                    border: none;
                    background-color: transparent;
                    font-weight: 600;
                    &:hover{
                        border: none;
                        background-color: transparent;
                        color: var(--tant-primary-color-primary-default);
                    }
                }
            }
            .event-item{
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                padding: 4px 0;
                overflow: hidden;
                line-height: 32px;
                white-space: normal;
            }
        }
    }
    .action-right{
        position: absolute;
        top: 0;
        right: 24px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
    }
    .filter-btn{
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;
        .btn-icon{
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            margin-right: 5px;
        }
        .filter-label{
            display: inline-block;
            max-width: 300px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            white-space: nowrap;
            text-overflow: ellipsis;
            vertical-align: top;
        }

        &:hover{
            border-color: var(--tant-primary-color-primary-hover);
        }
    }
    .row-word{
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
    }
    &:hover{
    background-color: var(--tant-fill-color-fill1-2);
    .action-left .row-content :deep(.filter-btn){
        background: #fff;
    }
    .action-left .row-content :deep(.filter-icon){
        background: #fff;
    }
    .sub-action-left :deep(.filter-btn){
        background: #fff;
    }
    .action-right{
        opacity: 1;
    }
}
}

.row-foot{
    margin: 0;
    // padding-left: 34px;
    transition: all .3s;
    .ta-filter-button{
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;
        .action{
            border-radius: 4px;
            background-color: var(--tant-primary-color-primary-fill);
            color: var(--tant-primary-color-primary-default);
            margin-right: 8px;
            padding: 3px;
            font-size: 18px;
        }
        .label{
            color: var(--tant-primary-color-primary-default);
        }
        &:hover .action{
            background-color: var(--tant-primary-color-primary-fill-hover);
            color: var(--tant-primary-color-primary-hover);
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
.word{
    margin: 0 8px;
    color: var(--tant-text-gray-color-text1-3);
    font-size: 14px
}
</style>