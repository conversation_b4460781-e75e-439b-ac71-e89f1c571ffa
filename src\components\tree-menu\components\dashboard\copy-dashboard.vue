<script setup lang="ts">
import {computed, h, onMounted, ref} from "vue";
import {SpaceDto} from "@/api/space/type";
import {Message, TreeNodeData} from "@arco-design/web-vue";
import cubeImg from '/public/icon/cube-open.svg';
import folderImg from '/public/icon/folder.svg';
import _ from "lodash";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {useEventBus} from "@vueuse/core";
import {ObjectPermissionType} from "@/api/enum";
import {listSpace} from "@/api/space/api";
import {copyDashboard} from "@/api/dashboard/api";
import {useDashboardStore} from "@/store";


/*
 *我的看板
 */

const mySpace = ref<SpaceDto>();
/*
 *项目空间
 */
const projectSpaces = ref<SpaceDto[]>();
const props = defineProps({
  copyName: String,
  copyId: String
})
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const dashboardStore = useDashboardStore()
const cacheEventBus = useEventBus(CacheEventBus)

const visible = defineModel<boolean>("visible", {default: false});
const dashboardName = ref<string>();
const dashboardPosition = ref<string>('my');
const projectPositionSelected = ref<string>();
const myPositionSelected = ref<string>();
const copyWay = ref(false)
const projectPosition = computed(() => {
  return projectSpaces.value?.map(space => {
    return {
      'key': `${space.spaceId}`,
      'title': space.name,
      'icon': () => h(cubeImg),
      'children': space.folders?.map(folder => {
        return {
          'key': `${folder.folderId}`,
          'title': folder.name,
          'icon': () => h(folderImg)
        }
      })
    } as TreeNodeData
  }) || []
})

const myPosition = computed(() => {
  return mySpace.value?.folders?.filter(folder => folder?.allowAdd !== false || folder.isDeletable).map(folder => {
    return {
      'key': folder.folderId,
      'title': folder.name,
      'icon': () => h(folderImg),
    } as TreeNodeData
  }) || []
})

const handleOk = () => {
  let copyName
  let copySpaceId: any
  let copyFolderId: any
  const reportCopy = copyWay.value
  // name
  if (dashboardName.value) {
    copyName = dashboardName.value
  } else copyName = props.copyName
  // Id
  if (dashboardPosition.value === 'my') {
    copySpaceId = mySpace.value?.spaceId
    copyFolderId = myPositionSelected.value
  }
  if (dashboardPosition.value === 'other') {
    if (projectPositionSelected.value?.startsWith('space')) {
      copySpaceId = projectPositionSelected.value
      copyFolderId = undefined
    }
    if (projectPositionSelected.value?.startsWith('folder')) {
      copySpaceId = projectSpaces.value?.find(space => {
        return space.folders?.some(folder => folder.folderId === projectPositionSelected.value);
      })?.spaceId;
      copyFolderId = projectPositionSelected.value
    }
  }
  copyDashboard({reportCopy, spaceId: copySpaceId, name: copyName, dashboardId: props.copyId, folderId: copyFolderId})
      .then((resp) => {
        cacheEventBus.emit('copy-event', {
          type: 'dashboard',
          dashboard: resp,
          spaceId: copySpaceId,
          folderId: copyFolderId,
        });
      }).catch((e) => {
    Message.error("复制看板失败！", e)
  })
  visible.value = false;
}
const handleCancel = () => {
  visible.value = false;
}
/**
 * 刷新菜单
 */
const refreshMenu = () => {
  listSpace(true).then((resp: SpaceDto[]) => {
    const spaces = resp || []
    const mySpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority === ObjectPermissionType.OWNER
    })

    const projectSpaceList = spaces?.filter((spaceItem) => {
      return spaceItem.authority !== ObjectPermissionType.OWNER
    })

    if (!_.isEmpty(mySpaceList) && mySpaceList.length === 1) {
      // eslint-disable-next-line prefer-destructuring
      mySpace.value = mySpaceList[0];
    }

    if (!_.isEmpty(projectSpaceList) && projectSpaceList.length > 0) {
      projectSpaces.value = projectSpaceList;
    }
  })
}

treeMenuEventBus.on((event) => {
  if (event === RefreshEvent) {
    refreshMenu()
  }
})

/**
 * 初始化
 */
onMounted(() => {
  refreshMenu()
})

</script>

<template>
  <a-modal

      :visible="visible"
      :mask-closable="false"
      :width="500"
      title-align="start"
      ok-text="复制"
      title="复制看板"
      @ok="handleOk"
      @cancel="handleCancel">
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          看板名称
          <a-tooltip
              content="如果输入为空，则默认为当前看板名称"
              position="right">
            <icon-exclamation-circle/>
          </a-tooltip>
        </div>

      </a-col>
      <a-col :span="18" :offset="1">
        <a-input
            v-model:model-value="dashboardName"
            :default-value="props.copyName"
            placeholder="请输入看板名称"/>
      </a-col>
    </a-row>
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          复制到
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-radio-group type="button" :model-value="dashboardPosition" @change="value => {dashboardPosition=value}">
          <a-radio value="my">我的看板</a-radio>
          <a-radio value="other">项目空间</a-radio>
        </a-radio-group>
      </a-col>
    </a-row>
    <a-row v-if="dashboardPosition==='my'" class="item" align="center">
      <a-col :span="5">
      </a-col>
      <a-col :span="18" :offset="1">
        <a-tree-select
            v-model="myPositionSelected"
            :data="myPosition"
            placeholder="请选择添加位置">
          <template #label="{ data }">
            <div class="space-select-option">
              <img class="icon" src="/icon/folder.svg" alt=""/>
              <div class="label">
                {{ data.label }}
              </div>
            </div>
          </template>
        </a-tree-select>
      </a-col>
    </a-row>
    <a-row v-if=" dashboardPosition==='other'" class="item" align="center">
      <a-col :span="5">
      </a-col>
      <a-col :span="18" :offset="1">
        <a-tree-select
            v-model="projectPositionSelected"
            :data="projectPosition"
            placeholder="请选择添加位置">
          <template #label="{ data }">
            <div class="space-select-option">
              <img
                  v-if="projectPositionSelected?.split('-')[0]==='space'" class="icon" src="/icon/cube-open.svg"
                  alt=""/>
              <img v-else class="icon" src="/icon/folder.svg" alt=""/>
              <div class="label">
                {{ data.label }}
              </div>
            </div>
          </template>
        </a-tree-select>
      </a-col>
    </a-row>
    <a-row class="item" align="center">
      <a-col :span="5">
        <div class="label">
          看板内报表
          <a-tooltip
              content="选择引用不新建报表，选择复制则同时新建报表"
              position="right">
            <icon-exclamation-circle/>
          </a-tooltip>
        </div>
      </a-col>
      <a-col :span="18" :offset="1">
        <a-radio-group v-model:model-value="copyWay">
          <a-radio value=false>引用报表</a-radio>
          <a-radio value=true>复制报表</a-radio>
        </a-radio-group>
      </a-col>
    </a-row>

  </a-modal>
</template>

<style scoped lang="less">
.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }
}

.space-select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

</style>