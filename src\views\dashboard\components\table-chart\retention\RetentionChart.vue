<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import useLoading from '@/hooks/loading';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import {cloneDeep} from "lodash";

interface Props {
  // 留存类型
  retentionType:string
  /**
   * 报表数据
   */
  reportAnalysisData: object;
}

const props = defineProps<Props>()

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const {loading, setLoading} = useLoading(true);
const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([]);
const groups = ref<any>([]) // 分组
const chartTypeValue = ref('dTrend')
const rateOrNum = ref('rate') 
const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
// 千分位
const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
function renderChart(wsResultData: any) {
  if (!wsResultData) {
    return
  }
  if(!wsResultData?.retentionQueryResult?.length){
    setLoading(false)
    return
  }
  groups.value = wsResultData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  const timeSpanNumber = wsResultData?.timeSpan?.number
  const retentionRateData = wsResultData?.retentionQueryResult.find(item => item.type === `${props.retentionType}_rate`)?.resultData;
  const retentionData = wsResultData?.retentionQueryResult.find(item => item.type === `${props.retentionType}_num`)?.resultData;
  const handleData = rateOrNum.value === 'rate' ? cloneDeep(retentionRateData) : cloneDeep(retentionData)

  const unit = wsResultData?.timeSpan?.unit
  let unitName = '' // 单位
  if(unit === 'DAY'){
    unitName = '日'
  }else if(unit === 'WEEK'){
    unitName = '周'
  }else{
    unitName = '月'
  }
  let dateList = []
  if(props.retentionType==='churn'){
    dateList = Array.from({ length: timeSpanNumber }, (_, index) => `${index + 1}${unitName}`);
  }else{
    dateList = Array.from({ length: timeSpanNumber+1 }, (_, index) => {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
    });
  }
  const result = [] as any;
  // 从 index=1 开始遍历数据  取第timeSpanNumber天数据
  handleData.slice(1).forEach(item => {
      const nValue = rateOrNum.value === 'rate' ? Number((item.summaryData[timeSpanNumber] * 100).toFixed(2)) : item.summaryData[timeSpanNumber];
      if (nValue !== undefined && nValue !== null && !Number.isNaN(nValue)) {
          result.push({
              date: item.date,
              values: nValue,
              groupData:item.groupData
          });
      }
  });
  xAxis.value = dateList
  ySeries.value = []
  const startIndex = rateOrNum.value === 'rate' ? 0 : 1;
  if(groups.value.length>0){
    const sumData = [{
      name: '总体',
      data: handleData[0]?.summaryData.slice(startIndex).map(item => rateOrNum.value === 'rate' ? Number((item * 100).toFixed(2)) : item),
      type: 'line',
    }]
    const groupSeries = handleData[0]?.groupData.map(item => {
      return {
        name: item.group.join(','),
        data: item.values.slice(startIndex).map(ele =>rateOrNum.value === 'rate' ? Number((ele * 100).toFixed(2)) : ele),
        type: 'line',
      }
    })
    ySeries.value = sumData.concat(groupSeries)
  }else{
    ySeries.value = handleData.map(item => {
      return {
        name:item.date,
        data:item.summaryData.slice(startIndex).map(ele => rateOrNum.value === 'rate' ? Number((ele * 100).toFixed(2)) : ele),
        type: 'line',
      }
    })
  }
  if(ySeries.value.length>1){
    legendData.value = ySeries.value.map(item => item.name)
  }
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData, oldData) => {
  renderChart(newData);
})
watch(() => props.retentionType, async (newData, oldData) => {
  renderChart(props.reportAnalysisData);
})
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '1%',
      top: '20',
      bottom: '50',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      type: 'scroll',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      data: xAxis.value
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: rateOrNum.value === 'rate' ? '{value} %' : '{value}'
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine:true,
      axisPointer: {
        type: 'line'
      },
      // valueFormatter: (value) => `${value}%`,
      formatter: (params) => {
          const seriesInfo = params.map((param, index) => {
            let valueDisplay;
            if (param.value!== undefined && param.value!== null) {
                if (rateOrNum.value === 'rate') {
                    valueDisplay = `${param.value}%`;
                } else {
                    valueDisplay = formatNumber(param.value);
                }
            } else {
                valueDisplay = '-';
            }
            return `
                  <div style="display: flex; align-items: center; margin-bottom: 5px;">
                    <span style="flex-shrink: 0;">${param.marker}</span>
                    <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                    <span style="flex-shrink: 0;">${valueDisplay}</span>
                  </div>
              `;
          }).join('');
          const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${chartTypeValue.value === 'nTrend' ? formatNDate(params[0].axisValue) : params[0].axisValue}</div>
              ${seriesInfo}
            </div>
          `;
          return tooltipHtml;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

</script>

<style scoped lang="less">
.chart-content {
  width: 100%;
  height: 100%;
}
</style>
