<template>
  <a-modal v-model:visible="modalVisible" title="自定义指标列" :width="720" title-align="start" style="z-index: 998" @cancel="handleCancel">
    <div class="index-content">
      <div class="left">
        <div class="left-header">
          <a-input v-model="searchText" placeholder="搜索指标" allow-clear>
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </div>
        <div class="search-content">
          <div
            v-for="el in filteredIndicatorList"
            :key="el.code"
            class="list-box"
            :class="{ 'has-select': isAble(el) }"
            @click="addIndexItem(el)"
          >
            <span>{{ el.name }}</span>
            <icon-arrow-right class="select-icon" />
          </div>
        </div>
      </div>
      <div class="right">
        <div class="title">
          <div>已选择：支持排序</div>
          <a @click="checkList = []">清除</a>
        </div>
        <a-divider style="margin: 8px auto" />
        <a-list v-if="checkList.length > 0" :max-height="530">
          <draggable :list="checkList" handle=".drag-handle">
            <template #item="{ element, index }">
              <a-list-item style="padding: 8px">
                <a-list-item-meta :description="element.name">
                  <template #avatar>
                    <div style="display: flex; align-items: center">
                      <div class="drag-handle">
                        <icon-drag-dot-vertical size="16" />
                      </div>
                    </div>
                  </template>
                </a-list-item-meta>
                <template #actions>
                  <icon-delete @click="deleteDetailItem(index)" />
                </template>
              </a-list-item>
            </template>
          </draggable>
        </a-list>
        <a-empty v-else>
          <template #description>
            请从左侧选择指标
          </template>
        </a-empty>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="left"> </div>
        <div class="right">
          <a-button style="margin-right: 8px" class="cancel" @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="saveLoading" style="margin-right: 8px" @click="handleOk"> 保存 </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import {computed, ref, watch} from 'vue';
import draggable from 'vuedraggable';
import {Message} from '@arco-design/web-vue';
import {cloneDeep} from 'lodash';
import {saveMediaAnalysisView} from '@/api/analyse/api';

interface Props {
    indicatorList: any; // 指标列表
    selectedIndicators?: any[]; // 已选中的指标（用于回显）
    tabName: string;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    selectedIndicators: () => []
  });
  
  const emits = defineEmits<{
    'update:selectedIndicators': [value: any[]]
    'save': [value: any[]]
  }>();
  
  const modalVisible = ref(false);
  const saveLoading = ref(false);
  const checkList = ref<any>([]);
  const searchText = ref('');
  
  // 搜索过滤后的指标列表
  const filteredIndicatorList = computed(() => {
    const str = searchText.value.trim();
    if (!str) return props.indicatorList || [];
    
    return props.indicatorList?.filter(
      (indicator) => 
        indicator?.name?.includes(str) || 
        indicator?.code?.includes(str)
    ) || [];
  });
  
  // 初始化回显数据
  const initializeCheckList = () => {
    if (props.selectedIndicators?.length > 0) {
      // 根据indicatorList处理回显项
      const selectedItems = [];
      props.selectedIndicators.forEach(selectedCode => {
        // 在indicatorList中查找对应的指标
        const foundItem = props.indicatorList?.find(item => 
          typeof selectedCode === 'string' ? item.code === selectedCode : item.code === selectedCode.code
        );
        if (foundItem && !selectedItems.some(item => item.code === foundItem.code)) {
          selectedItems.push(cloneDeep(foundItem));
        }
      });
      checkList.value = selectedItems;
    } else {
      // 默认选中所有indicatorList
      checkList.value = cloneDeep(props.indicatorList || []);
    }
  };
  
  // 添加指标到已选列表
  const addIndexItem = (indicator: any) => {
    if (!checkList.value.some((item) => item.code === indicator.code)) {
      checkList.value.push(cloneDeep(indicator));
    }
  };
  
  // 检查指标是否已选中
  const isAble = (indicator: any) => {
    return checkList.value.some((item) => item.code === indicator.code);
  };
  
  // 删除已选指标
  const deleteDetailItem = (index: number) => {
    checkList.value.splice(index, 1);
  };
  
  // 取消操作
  const handleCancel = () => {
    modalVisible.value = false;
    // 重置为初始状态
    initializeCheckList();
  };
  
  // 保存操作
  const handleOk = async () => {
    if (!checkList.value.length) {
      Message.error('请至少选择一条指标');
      return;
    }
    
    saveLoading.value = true;
    try {
      // 向父组件传递数据
      const params = {
        type:12,
        name:props.tabName,
        indicator:checkList.value
      }
      await saveMediaAnalysisView(params);
      const selectedData = cloneDeep(checkList.value);
      emits('save', selectedData);
      modalVisible.value = false;
      Message.success('保存成功');
    } catch (error) {
      Message.error('保存失败');
    } finally {
      saveLoading.value = false;
    }
  };
  
  // 打开模态框
  const openModal = async () => {
    initializeCheckList();
    modalVisible.value = true;
  };
  
  let isInternalUpdate = false;
  watch(
    () => props.selectedIndicators,
    (newVal) => {
      if (!isInternalUpdate && !modalVisible.value) {
        initializeCheckList();
      }
    },
    { deep: true }
  );
  
  watch(
    checkList,
    (newVal) => {
      if (!modalVisible.value) {
        isInternalUpdate = true;
        emits('update:selectedIndicators', cloneDeep(newVal));
        setTimeout(() => {
          isInternalUpdate = false;
        }, 0);
      }
    },
    { deep: true }
  );
  
  defineExpose({
    openModal,
  });
</script>

<style scoped lang="less">
  .footer {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
    }
    .right {
      display: flex;
      justify-content: flex-end;
      button {
        border-radius: 4px;
      }
    }
  }
  .cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
      background-color: var(--color-secondary);
    }
  }
  .index-content {
    border: 1px solid var(--color-secondary);
    width: 100%;
    height: 100%;
    display: flex;
    
    .left{
     width: 50%;
     padding: 12px;
     height: 600px;
     border-right: 1px solid var(--color-secondary);
     overflow-y: scroll;
     .left-header{
       display: flex;
       button{
         border-radius: 4px;
       }
     }
     .search-content{
       width: 100%;
       padding: 8px;
       margin-top: 12px;
       background-color: var(--color-fill-1);
       .type-title{
         color: var(--color-text-1);
         font-size: 14px;
         line-height: 24px;
         padding-top: 8px;
         padding-bottom: 8px;
         overflow: hidden;
       }
     }
     .list-box {
         margin: 0 8px 8px;
         padding:0 12px;
         border-radius: 4px;
         color: var(--tant-text-gray-color-text1-2);
         font-size: 14px;
         line-height: 28px;
         height: 30px;
         background: #fff;
         cursor: pointer;
         display: flex;
         justify-content: space-between;
         align-items: center;
         .select-icon{
           opacity: 0;
         }
         &:hover .select-icon{
           opacity: 1;
         }
       }
     .has-select{
       cursor:no-drop;
       background: var(--color-secondary);
       &:hover .select-icon{
         opacity: 0;
       }
     }
   }
    
    .right {
      flex: 1;
      max-height: 600px;
      overflow: hidden;
      width: 100%;
      padding: 12px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        a {
          color: #0b75ff;
          cursor: pointer;
        }
      }
    }
  }
  .drag-handle {
    cursor: grab;
    margin-right: 8px;
  }
  .button-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
