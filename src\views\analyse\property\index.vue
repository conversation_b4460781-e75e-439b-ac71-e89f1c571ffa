<template>
  <div id="propertyRoot">
    <div class="analyse-content">
      <analyse-header :header-info="headerInfo"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <div class="box">
                <div class="query-condition">
                  <!-- 分析指标 -->
                  <analysisIndex @indicators-change="indicatorsChange"/>
                  <!-- 全局筛选 -->
                  <globalFilter :exclude-event="true" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem @attribute-change="attributeChange" @crowd-change="crowdChange"/>
                </div>
                <div class="left-footer">
                  <a-button @click="() => saveVisible = true">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
                <!-- 保存弹窗 -->
                <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
                  <template #title>
                    <div class="modal-title">保存报表</div>
                  </template>
                  <a-form ref="saveFormRef" :model="form">
                    <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
                      <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
                    </a-form-item>
                    <a-form-item field="dashboard" label="保存至看板">
                      <dashboard-select v-model:selected="form.dashboard" type="dashboard"/>
                    </a-form-item>
                    <a-form-item field="description" label="备注">
                      <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5 }"/>
                    </a-form-item>
                  </a-form>
                  <template #footer>
                    <a-button @click.stop="handleSaveCancel">取消</a-button>
                    <a-button type="primary" @click="saveReport">保存</a-button>
                  </template>
                </a-modal>
              </div>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <div class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                    </a-space>
                    <div>
                      <a-button-group style="background-color: #fff;margin-right: 20px;">
                        <a-popover title="柱状分布" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('distribution')" @click="selectChart('distribution')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示指标在各个属性/人群下的分布情况 <br>
                            <img :src="distribution" alt="" style="width: 100%;height: 90%;"/>
                          </template>
                        </a-popover>
                        <a-popover title="饼状分布" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('pieTrend')" @click="selectChart('pieTrend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/pie.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示指标在各个属性/人群下的占比情况 <br>
                            <img :src="pieTrend" alt="" style="width: 100%;height: 90%;"/> <br>
                            *按人群或属性进行分组统计时可用
                          </template>
                        </a-popover>

                      </a-button-group>
                      <a-tooltip content="可视化配置">
                        <a-button @click="onCollapse">
                          <template #icon>
                            <icon-settings/>
                          </template>
                        </a-button>
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="right-content">
                    <a-layout style="height: 490px;width: 100%" :style="showDrawer?'':''">
                      <a-layout-content>
                        <event-echars-vue ref="eventechars" :event-data="eventData" :showlabel="showlabel" :show-rate="showRate"/>
                      </a-layout-content>
                      <a-layout-footer>
                        <event-table-vue ref="eventtable" :event-data="eventData"/>
                      </a-layout-footer>
                    </a-layout>
                  </div>
                </div>
              </div>
              <div class="drawer" :style="showDrawer ? {width: '320px'} : {width: '0px'}">
                <div class="drawer-header">
                  <div class="title">可视化配置</div>
                  <div class="icon">
                    <a-button @click="() => showDrawer = false">
                      <template #icon>
                        <icon-double-right/>
                      </template>
                    </a-button>
                  </div>
                </div>
                <a-collapse :default-active-key="['1']" expand-icon-position="right" class="customStyle" :bordered="false" @change="handleChangeCollapse">
                  <a-collapse-item key="1">
                    <template #header>
                      <icon-settings/>
                      通用配置
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示数值</span>
                          <a-tooltip content="对柱状图，饼图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                        <div class="title">
                          <span>横坐标</span>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showlabel" size="small"/>
                        </div>
                        <div class="title">
                          <a-select default-value="今日登陆" :style="{minWidth:'90px'}" @change="xValueChange">
                            <a-option>今日登陆</a-option>
                            <a-option>test</a-option>
                          </a-select>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                  <a-collapse-item key="2">
                    <template #header>
                      <div style="display: flex;align-items: center;">
                        <img class="option-icon" src="/icon/pie.svg" alt=""/>
                        饼状分布
                      </div>
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示百分比</span>
                        </div>
                        <div class="title">
                          <span>分组</span>
                          <a-tooltip content="未被选中的分组值将被计入“其他”中" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showRate" size="small"/>
                        </div>
                        <div class="title">
                          <a-select default-value="10" :style="{width:'90px'}">
                            <a-option value="10">前10项</a-option>
                            <a-option value="15">前15项</a-option>
                            <a-option value="20">前20项</a-option>
                            <a-option value="30">前30项</a-option>
                            <a-option value="50">前50项</a-option>
                          </a-select>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                </a-collapse>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {onMounted, reactive, ref} from "vue";
import {useEventBus, useLocalStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {distribution, pieTrend} from '@/views/dashboard/components/img';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {ChartType} from "@/api/enum";
import _ from "lodash";
import {ReportAnalyseModel} from "@/api/analyse/type";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import analysisIndex from "./components/analysisIndex.vue"
import globalFilter from "../components/globalFilter.vue"
import groupItem from "./components/groupItem.vue"
import eventTableVue from "./components/eventTable.vue";
import eventEcharsVue from "./components/eventEchars.vue";
import analyseHeader from "../components/analyseHeader.vue"

const headerInfo = reactive({
  title: '属性分析',
  img: '/icon/topMenu/property.svg',
  tips: '分析当前状态下，指定用户的用户属性分布，构建用户画像',
  root: '#propertyRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true
})
const eventData = ref({
  y: [],
  groupsDesc: []
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// ------left start
const loading = ref(false)
// 传参数组
const queryParam = reactive({
  indicators: [], // 查询指标
  filter: [], // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: [], // 查询日期范围
  timeParticleSize: '', // 时间粒度
  chartType: '', // 图标类型
})
const eventBus = useEventBus('eventList');

// 分析指标传参
const indicatorsChange = (v) => {
  queryParam.indicators = v
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
}
// 分组项 属性传参
const attributeChange = (v) => {
  queryParam.aggregates = v
}
// 分组项 人群传参
const crowdChange = (v) => {
}
// 计算
const computedData = () => {
  loading.value = true
}
const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
onMounted(() => {
  eventBus.on((event: any) => {
    form.value.name = `${event[0]?.displayName}等${event.length}个`
  });
})

const handleSaveCancel = () => {
  saveVisible.value = false;
}
// 保存报表
const saveReport = async () => {
  if (!form.value.name) {
    Message.error('报表名称未填写')
    return
  }

  await saveAnalyseReportList(ReportAnalyseModel.EVENT, undefined, form.value.dashboard, form.value.name, form.value.description, queryParam, undefined, ChartType.TABLE).then(res => {
    Message.info(_.isEmpty(form.value.dashboard) ? "报表已保存" : "报表已保存并添加至看板")
  })
  saveVisible.value = false;
}


// ---left end

// ------right start


const showDrawer = ref<boolean>(false)
const eventtable = ref()
const eventechars = ref()
const isChartType = ref<string>('distribution')
const showlabel = ref<boolean>(false)
const showRate = ref<boolean>(false)

const xValueChange = (v) => {
  eventechars.value.handleXAxis(v)
}
const handleChangeCollapse = (key: any) => {
  if (key.includes('2')) {
    eventechars.value.changeType('pieTrend')
  }
}

const onCollapse = () => {
  showDrawer.value = !showDrawer.value;
};
const selectChart = (type: string) => {
  isChartType.value = type
  if (type === 'pieTrend') {
    showlabel.value = true
    showRate.value = true
  }
  eventechars.value.changeType(type)
}

// ---right end


const showBodyLeft = ref(true)
</script>

<style scoped lang="less">
#propertyRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;

}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  text-align: right;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  //padding-bottom: 18px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-content::before, .right-content::after {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-content::after {
  right: -24px;
  left: auto;
}

.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}

</style>