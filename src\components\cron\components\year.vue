<template>
  <div :val="computedValue">
    <div class="mb-8">
      <a-radio v-model="type" value="1" size="small">每年</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="5" size="small">不指定</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="2" size="small">周期</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">从</span>
      <a-input-number v-model="cycle.start" :min="2000" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">至</span>
      <a-input-number v-model="cycle.end" :min="2000" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
      年
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '*'
  }
})

const emit = defineEmits(['update:modelValue'])

const year = new Date().getFullYear()
const type = ref('1') // 类型
const cycle = ref({ // 周期
  start: year,
  end: year + 1
})

const computedValue = computed(() => {
  let result = ''
  switch (type.value) {
    case '1': // 每年
      result = '*'
      break
    case '2': // 周期
      result = `${cycle.value.start}-${cycle.value.end}`
      break
    case '5': // 不指定
      result = ''
      break
    default:
      result = '*'
      break
  }
  emit('update:modelValue', result)
  return result
})

watch(() => props.modelValue, (val) => {
  if (!val) {
    val = '*'
  }
  if (val === '*') {
    type.value = '1'
  } else if (val === '') {
    type.value = '5'
  } else if (val.indexOf('-') !== -1) { // 2周期
    if (val.split('-').length === 2) {
      type.value = '2'
      cycle.value.start = parseInt(val.split('-')[0])
      cycle.value.end = parseInt(val.split('-')[1])
    }
  } else {
    type.value = '1'
  }
}, { immediate: true })
</script>

<style lang="less" scoped>
.mb-8 {
  margin-bottom: 8px;
}
</style>