
<template>
    <!-- 创建广告 -->
    <a-modal v-model:visible="modalVisible" :width="800" :hide-title="true" :footer="false" @cancel="closeModal">
        <a-tabs v-model:active-key="activeName">
            <template #extra>
                <icon-close style="margin-bottom: 24px;cursor: pointer;" @click="closeModal"/>
            </template>
            <a-tab-pane key="1" title="新建广告">
                <a-form ref="formRef" :model="form" style="height: 260px;">
                    <a-form-item field="template" label="使用模板">
                        <a-button type="primary" style="margin-right: 12px;">选择模板</a-button>
                        <a-button>选择历史任务</a-button>
                    </a-form-item>
                    <a-form-item field="target" label="广告目标">
                        <a-radio-group v-model="form.target" type="button" @change="handleTargetChange">
                            <a-radio value="1">应用推广</a-radio>
                            <a-radio value="2">销量</a-radio>
                            <a-radio value="3">潜在客户</a-radio>
                            <a-radio value="4">互动</a-radio>
                            <a-radio value="5">流量</a-radio>
                            <a-radio value="6">知名度</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item v-if="form.target === '1'" field="isAdvancedApp" label="进阶赋能型应用广告">
                        <a-switch v-model="form.isAdvancedApp" :checked-value="1" :unchecked-value="0" size="small"/>
                    </a-form-item>
                    <a-form-item v-if="form.target === '2'" field="isAdvancedShopping" label="进阶赋能型购物广告">
                        <a-switch v-model="form.isAdvancedShopping" :checked-value="1" :unchecked-value="0" size="small"/>
                    </a-form-item>
                    <a-form-item v-if="form.target === '2' && !form.isAdvancedShopping" field="isAdvancedList" label="进阶赋能型目录广告">
                        <a-switch v-model="form.isAdvancedList" :checked-value="1" :unchecked-value="0" size="small"/>
                    </a-form-item>
                    <a-form-item v-if="form.target !== '1' && form.target !== '6'" field="position" label="转化发生位置">
                        <a-radio-group v-model="form.position" type="button">
                            <a-radio v-for="option in positionOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </a-radio>
                        </a-radio-group>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="2" title="选择已有">
                <a-form ref="hasRef" :model="hasform" style="height: 260px;">
                    <a-form-item field="account" label="广告账户">
                        <a-select v-model="hasform.account" placeholder="请选择广告账户" allow-clear style="width: 220px">
                            <a-option value="1">111</a-option>
                            <a-option value="2">222</a-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
        </a-tabs>
        
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref, computed, watch} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const activeName = ref('1')
const form = reactive({
    target:'1',
    isAdvancedApp:0,
    isAdvancedShopping:0,
    isAdvancedList:0,
    position:''
})
const formRef = ref()
const hasform = reactive({
    account:''
})
const hasRef = ref()

const emits = defineEmits(['updateData']);
const positionOptionsMap = {
    '2': [
        { value: 'app', label: '应用' },
        { value: 'website', label: '网站' },
        { value: 'messenger', label: '消息应用' }
    ],
    '3': [
        { value: 'instantForm', label: '即时表单' },
        { value: 'app', label: '应用' },
        { value: 'website', label: '网站' },
        { value: 'messenger', label: 'Messenger' },
        { value: 'instagram', label: 'Instagram' }
    ],
    '4': [
        { value: 'app', label: '应用' },
        { value: 'website', label: '网站' },
        { value: 'facebookPage', label: 'Facebook 公共主页' },
        { value: 'messenger', label: '消息应用' }
    ],
    '5': [
        { value: 'app', label: '应用' },
        { value: 'website', label: '网站' },
        { value: 'messenger', label: '消息应用' }
    ]
}
const positionOptions = computed(() => {
    // 当target为2且isAdvancedShopping或isAdvancedList为1时，只返回网站选项
    if (form.target === '2' && (form.isAdvancedShopping === 1 || form.isAdvancedList === 1)) {
        return [{ value: 'website', label: '网站' }];
    }
    // 否则返回完整的选项列表
    return positionOptionsMap[form.target] || [];
})
const handleTargetChange = () => {
    const options = positionOptionsMap[form.target] || [];
    form.position = options.length > 0 ? options[0].value : '';
}
const openModal = async (channel?:string) => {
    formRef.value.resetFields()
    hasRef.value.resetFields()
    activeName.value = '1'
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)

// 监听isAdvancedShopping和isAdvancedList的变化
watch([() => form.isAdvancedShopping, () => form.isAdvancedList], () => {
    if (form.target === '2') {
        // 如果开启了进阶赋能，则position只能是website
        if (form.isAdvancedShopping === 1 || form.isAdvancedList === 1) {
            form.position = 'website';
        } else {
            // 否则重新获取可用选项，并选择第一个
            const options = positionOptionsMap[form.target] || [];
            form.position = options.length > 0 ? options[0].value : '';
        }
    }
})
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>
