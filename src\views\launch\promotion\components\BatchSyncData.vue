
<template>
    <!-- 批量同步数据 -->
    <a-modal v-model:visible="modalVisible" :width="750" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div class="sync-container">
            <!-- 左侧广告账户选择区域 -->
            <a-spin :loading="leftLoading" class="left-container">
                <div class="header">
                    <div class="title">广告账户</div>
                    <a-checkbox :model-value="isAllSelected" :indeterminate="isIndeterminate" class="f12" @change="handleSelectAll">全选</a-checkbox>
                </div>
                <div class="search-box">
                    <a-input-search v-model="searchKeyword" placeholder="搜索广告账户名称或ID" allow-clear />
                </div>
                <a-scrollbar style="height: 300px; overflow: auto;" @scroll="scrollChange">
                    <div class="account-list">
                        <div v-for="account in filteredAccounts" :key="account.accountId" class="account-item">
                            <a-checkbox v-model="account.checked">
                                <div class="account-name">{{ account.accountName }}</div>
                                <div class="account-update-time">最后更新时间：{{ dayjs(account.updateTime).format('YYYY-MM-DD HH:mm:ss') || '-' }}</div>
                            </a-checkbox>
                        </div>
                    </div>
                </a-scrollbar>
            </a-spin>

            <!-- 右侧已选区域 -->
            <div class="right-container">
                <div class="header">
                    <div class="title">已选 ({{ selectedAccounts.length }}/50)</div>
                    <a-link class="f12" @click="handleClearAll">清除</a-link>
                </div>
                <a-scrollbar style="height: 340px; overflow: auto;">
                    <div class="selected-list">
                        <div v-for="account in selectedAccounts" :key="account.accountId" class="selected-item">
                            <div class="selected-name">
                                <div class="no-warp">{{ account.accountName }}</div>
                                <div class="selected-id">{{ account.accountId }}</div>
                            </div>
                            <a-button class="remove-btn" type="text" @click="handleRemoveAccount(account)">
                                <template #icon>
                                    <icon-close />
                                </template>
                            </a-button>
                        </div>
                    </div>
                </a-scrollbar>
            </div>
        </div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                确定
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref, computed,inject } from "vue";
import { Message } from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import dayjs from "dayjs";

const modalVisible = ref(false);
const modalTitle = ref('同步数据');
const loading = ref(false);
const leftLoading = ref(false);
const searchKeyword = ref('');
const props = defineProps({
    accountData: {
        type: Array,
        default: () => []
    }
})
const dateTime = ref(inject('dateTime') as any[])
const pageParams = reactive({
  current: 1,
  pageSize: 10,
  pages:0
})
// 广告账户数据
const accounts = ref<any>([]);

// 计算属性：过滤后的账户列表
const filteredAccounts = computed(() => {
    if (!searchKeyword.value) return accounts.value;
    
    return accounts.value.filter(account => 
        account.accountName.toLowerCase().includes(searchKeyword.value.toLowerCase()) || 
        account.accountId.includes(searchKeyword.value)
    );
});

// 计算属性：已选账户列表
const selectedAccounts = computed(() => {
    return accounts.value.filter(account => account.checked);
});

// 计算属性：是否全选
const isAllSelected = computed(() => {
    return filteredAccounts.value.length > 0 && filteredAccounts.value.every(account => account.checked);
});

// 计算属性：是否部分选中
const isIndeterminate = computed(() => {
    const selectedCount = filteredAccounts.value.filter(account => account.checked).length;
    return selectedCount > 0 && selectedCount < filteredAccounts.value.length;
});

// 全选/取消全选
const handleSelectAll = (checked) => {
    filteredAccounts.value.forEach(account => {
        account.checked = checked;
    });
};

// 移除单个账户
const handleRemoveAccount = (account) => {
    account.checked = false;
};

// 清除所有选中
const handleClearAll = () => {
    accounts.value.forEach(account => {
        account.checked = false;
    });
};
const loadMoreData = async () => {
    // 获取更多广告账户数据
    try {
        const params = {
        channel:'facebook',
        groupLabel:'account',
        startDate:dateTime.value[0],
        endDate:dateTime.value[1],
        pageSize:pageParams.pageSize,
        current:pageParams.current,
        };
        const response = await getAdChannelList(params);
        const { items } = response;
        const itemsList = items.map(item => ({
            accountId: item.accountId,
            accountName: item.accountName,
            checked: false,
            updateTime: item.authTime
        }))
        if (pageParams.current === 1) {
            accounts.value = itemsList;
        } else {
            accounts.value = [...accounts.value, ...itemsList];
        }
    } catch (error) {
        console.error('加载广告账户选项失败:', error);
    }
}
// 滚动事件
const scrollChange = (event) => {
    const { scrollTop, scrollHeight, clientHeight } = event.target;
    // 检测是否滚动到底部
    if (scrollHeight - scrollTop - clientHeight === 0) {
        // 触底加载更多数据
        pageParams.current += 1;
        if(pageParams.current <= pageParams.pages){
            loadMoreData();
        }
    }
};
// 打开模态框
const openModal = async () => {
    // 重置搜索关键词
    searchKeyword.value = '';
    pageParams.current = 1
    leftLoading.value = true
    try{
        const params = {
            channel:'facebook',
            groupLabel:'account',
            startDate:dateTime.value[0],
            endDate:dateTime.value[1],
            pageSize:pageParams.pageSize,
            current:pageParams.current,
        }
        await getAdChannelList(params).then(res => {
            accounts.value = res.items.map(item => ({
                accountId: item.accountId,
                accountName: item.accountName,
                checked: false,
                updateTime: item.authTime
            }))
            pageParams.pages = res.pages
        })
    }catch (error) {
        console.error('失败:', error);
    }finally{
        leftLoading.value = false
    }
    modalVisible.value = true;
};

// 关闭模态框
const closeModal = () => {
    modalVisible.value = false;
};

// 保存数据
const saveData = async () => {
    if (selectedAccounts.value.length === 0) {
        Message.warning('请至少选择一个广告账户');
        return;
    }
    loading.value = true;
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        // 获取选中的账户ID
        const selectedIds = selectedAccounts.value.map(account => account.accountId);
        // 发出更新事件
        emits('updateData', selectedIds);
        Message.success('同步数据成功');
        closeModal();
    } catch (error) {
        Message.error('同步数据失败');
    } finally {
        loading.value = false;
    }
};

const emits = defineEmits(['updateData']);

defineExpose({
    openModal
});
</script>

<style scoped lang="less">
.sync-container {
    display: flex;
    gap: 20px;
}

.left-container, .right-container {
    flex: 1;
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding:6px 10px;
    background-color: #f5f6f7;
    border-bottom: 1px solid var(--color-neutral-3);
}

.title {
    font-weight: 500;
}

.search-box {
    padding: 12px 16px;
}

.account-item {
    padding: 5px 10px;
    .account-name{
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &:hover{
        background-color: #f9f9f9;
    }
}

.account-update-time {
    font-size: 12px;
    color: var(--color-text-3);
}
.selected-id{
    font-size: 12px;
    color: var(--color-text-3);
}
.f12{
    font-size: 12px;
}
.selected-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    .no-warp{
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &:hover{
        background-color: #f9f9f9;
    }
}

.remove-btn {
    color: var(--color-text-3);
}

.footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}

.cancel {
    background: transparent;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>
