<template>
    <div style="display: flex;width: 100%;height: 100%">
        <div class="group-box">
            <div class="data-configure">
                <div class="json-version-list">
                    <div class="label-text">应用版本</div>
                    <div class="edit-select">
                        <a-select
                            v-model:model-value="appVersionsSelect"
                            :options="appVersions"
                            placeholder="应用版本"
                            multiple
                            :max-tag-count="2"
                            allow-clear
                            allow-search
                            :scrollbar="true">
                        </a-select>
                    </div>
                    <div class="label-text">数据版本列表</div>
                    <div class="handle-box">
                        <a-input v-model:model-value="conditionJsonText" allow-clear style="background-color: #FFF" placeholder="搜索">
                            <template #prefix>
                                <icon-search/>
                            </template>
                        </a-input>
                        <a-tooltip content="添加数据版本">
                            <a-button type="primary" style="margin-left: 12px;flex-shrink: 0;" @click="addData('新增数据版本')">
                                <template #icon>
                                    <icon-plus/>
                                </template>
                            </a-button>
                        </a-tooltip>
                    </div>
                    <div :style="{ maxHeight: 'calc(100% - 105px)', overflowY: 'auto' }">
                        <a-list :bordered="false">
                            <a-list-item v-for="(item,index) in filteredJsonList" :key="index" :style="item.name === form.dataVersion ?{backgroundColor: '#f3f3f3'} : ''">
                                <a-tooltip :content="`${item.name} (${formatCallTime(item.lastCallTime)})`">
                                <a-list-item-meta @click="selectJsonVersion($event, item)">
                                    <template #description>
                                        <span :style="item.name === form.dataVersion ?{color: 'rgb(var(--primary-6))'} : ''">{{ item.name }}</span>
                                        <span :style="item.name === form.dataVersion ?{color: 'rgb(var(--primary-6))'} : ''" style="padding-left: 5px;font-size: 11px">({{formatCallTime(item.lastCallTime)}})</span>
                                    </template>
                                </a-list-item-meta>
                                </a-tooltip>
                                <template #actions>
                                    <a-tooltip content="删除">
                                        <a-popconfirm :content="`删除后不可恢复，确定删除数据版本${item.name}?`" type="warning" position="rt" @ok="deleteVersion(item)">
                                            <icon-delete/>
                                        </a-popconfirm>
                                    </a-tooltip>
                                </template>
                            </a-list-item>
                        </a-list>
                    </div>
                </div>
                <addModal ref="addRef" @update-data="updateListData"/>
            </div>
        </div>
        <div class="group-detail">
            <div class="data-configure">
                <div class="condition-list">
                    <div class="label-text">条件列表</div>
                    <div class="handle-box">
                        <a-input v-model:model-value="conditionText" allow-clear style="background-color: #FFF" placeholder="搜索">
                            <template #prefix>
                                <icon-search/>
                            </template>
                        </a-input>
                        <a-tooltip content="添加条件">
                            <a-button type="primary" style="margin-left: 12px;flex-shrink: 0;" @click="conditionSelectShow=true">
                                <template #icon>
                                    <icon-plus/>
                                </template>
                            </a-button>
                        </a-tooltip>
                    </div>
                    <div :style="{ height: 'calc(100% - 70px)', overflowY: 'auto' }">
                        <a-table :data="filteredList" :pagination="false" :bordered="false" :draggable="{}"
                                 @change="dragChange" :loading="dragLoading" :show-header="false"
                                 :row-class="(record, rowIndex) => selectCondition.code === record.code ? 'highlight-row' : ''">
                            <template #columns>
                                <a-table-column>
                                    <template #cell="{ record, column, rowIndex }">
                                        <a-list-item :key="rowIndex" @click="selectItem(record)">
                                            <a-list-item-meta>
                                                <template #description>
                                                    <div style="display: flex;">
                                                        <div style="display: flex;align-items: center">
                                                            <a-switch v-model="record.status" :disabled="record.code === 'default'" :checked-value="1" :unchecked-value="0"  size="small" style="margin-right: 8px;" @change="statusChange($event,record)"/>
                                                            <span style="white-space: nowrap" :style="selectCondition.code === record.code ?{color: 'rgb(var(--primary-6))'} : ''">{{ record.name }}</span>
                                                        </div>
                                                        <div class="condition-simple-info" v-html="record.condition_format"></div>
                                                    </div>
                                                </template>
                                            </a-list-item-meta>
                                            <template #actions>
                                                <a-tooltip content="编辑">
                                                    <icon-pen v-if="record.code !== 'default'" @click.stop="editItem(record, 'edit')"/>
                                                </a-tooltip>
                                                <a-tooltip content="删除" v-if="record.code !== 'default'">
                                                    <a-popconfirm :content="`删除后不可恢复，确定删除条件配置[${record.name}]？`" type="warning" @ok="deleteItem(record)">
                                                        <icon-delete />
                                                    </a-popconfirm>
                                                </a-tooltip>
                                            </template>
                                        </a-list-item>
                                    </template>
                                </a-table-column>
                            </template>
                        </a-table>
                    </div>
                </div>
                <a-spin :loading="configLoading" class="configure-body">
                    <div class="top">
                        <div class="tips">使用条件，以便在满足条件时提供不同的参数值</div>
                        <div class="handle-btn">
                            <a-button class="cancel" @click="resetData">重置</a-button>
                            <a-button type="primary" :loading="saveLoading" :disabled="!form.code" @click="saveConfirmShow=true">
                                保存
                            </a-button>
                            <a-modal
                                v-model:visible="saveConfirmShow"
                                title-align="start"
                                :mask-closable="false"
                                :top="150"
                                @ok="saveData" @cancel="saveConfirmShow=false">
                                <template #title>
                                    警告
                                </template>
                                <div>确认保存下发配置的修改？</div>
                            </a-modal>
                        </div>
                    </div>
                    <div class="detail">
                        <div class="json-content">
                            <div class="action-content">
                                <div class="label">DATA</div>
                                <div class="btns">
                                    <a-button type="primary" status="warning" size="small" @click="formatConfig">格式化</a-button>
                                    <a-button type="outline" status="warning" size="small" @click="compressConfig">压缩</a-button>
                                    <a-button type="outline" size="small" @click="copyConfig">复制</a-button>
                                    <a-button type="primary" size="small" status="danger" @click="clearConfig">清空</a-button>
                                </div>
                            </div>
                            <json-editor-vue v-model="config" class="editor" @update:modelValue="updataModel" @validationError="editError"/>
                        </div>
                    </div>

                </a-spin>
                <createConditionModal ref="conditionModalRef" :condition-list="conditionFilterList" @condition-change="getTabList"/>
            </div>
        </div>
        <a-modal v-model:visible="conditionSelectShow" :width="450" title-align="start" title="添加条件" @ok="conditionRelated" @cancel="conditionSelected=''">
            <a-select v-model="conditionSelected" :loading="conditionAvailableLoading">
                <div v-if="availableConditions.length > 0" style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                    <a-input v-model="conditionInput" placeholder="请输入搜索" style="border: none;height: 40px;" @click="focusIn">
                        <template #prefix>
                            <icon-search />
                        </template>
                    </a-input>
                </div>
                <div class="create-condition" @click="addCondition"><icon-plus style="margin-right: 12px;"/>创建条件</div>
                <a-option v-if="filteredConditionList.length === 0"></a-option>
                <a-option v-for="(el,elIndex) in filteredConditionList" :key="elIndex" :value="el.conditionCode">{{ el.name }}</a-option>
            </a-select>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import JsonEditorVue from 'json-editor-vue3'
import {Message} from '@arco-design/web-vue';
import {
  countrySearch,
  deleteConditionItem,
  deleteDataVersion,
  getAdSourceList,
  getAppVersionDataVersion,
  getAppVersionList,
  getConditionList,
  getCountryList,
  getConditionAvailable,
  getRemoteConfigAttrList,
  saveCondition,
  saveConditionRelated,
  sortCondition
} from "@/api/marketing/api";
import addModal from "./addModal.vue";
import createConditionModal from "./createConditionModal.vue";
import {convertKeysToSnakeCase} from "@/utils/strUtil";
import {booleanRangeList, dateRangeList, itemRangeList, textRangeList, valueRangeList} from "@/views/operation/pack/packData/components/listJson";

const conditionJsonText = ref('')
const conditionText = ref('')
// const conditionIndex = ref(0)
const addInputList = ref<any>([])
const conditionList = ref<any>([])
const conditionFilterList = ref<any>([])
const conditionModalRef = ref()
const selectCondition = ref()
const appVersions = ref([])
const appVersionsSelect = ref([])
const appVersionDataVersion = ref({})
const form = reactive({
  code:'',
  name: '',
  status:0,
  dataVersion: '',
  sysVersion:undefined,
  appVersion:undefined,
  country:[],
  adSource: '',
  minMem:undefined,
  maxMem:undefined,
  // conditionNote: ''
  network:'',
  opt:''
})
const countryList = ref<any>([])
const config = ref<any>({})
const rules = {}
const configLoading = ref(false)
const conditionSelectShow = ref(false)
const conditionAvailableLoading = ref(false)
const availableConditions = ref([])

const versionList = ref<any>([])
const sourceList = ref<any>([])
const attrList = ref<any>([])
const saveConfirmShow = ref(false)
const conditionInput = ref('')
const conditionSelected = ref()

const filteredConditionList = computed(() => {
    const str = conditionInput.value.trim();
    const existingNames = conditionList.value.map(item => item.name);
    return availableConditions.value.filter(item => item.name.includes(str) && !existingNames.includes(item.name));
});
const focusIn = (e) => {e.target.focus()}
const conditionRelated = () => {
    let payload = {
        appId: appCode.value,
        valueType: "json",
        paramName: "default_json",
        conditionCode: conditionSelected.value,
        dataVersion: form.dataVersion
    }
    saveConditionRelated(payload).then(res => {
        conditionInput.value = ''
        conditionSelected.value = ''
        getAvailableCondition()
        selectJsonVersion(null, {name: form.dataVersion})
    })
}

// 获取应用下发数据可用买量来源渠道列表
const getSourceList = async () => {
  await getAdSourceList().then(res => {
    sourceList.value = res
  })
}
// 获取应用下发数据可用数据版本列表
const getVersionList = async () => {
    await loadDataVersion()
}

const loadDataVersion = async () => {
    await getAppVersionList({appId: appCode.value}).then(res => {
        versionList.value = res
    })
}

// 获取条件列表
const getTabList = async () => {
  const data = {
    appId: appCode.value,
    paramName:'default_json',
    conditionScope:'app_param_specific',
    dataVersion:form.dataVersion,
    page_size: 100
  }
  await getAvailableCondition()
  await getConditionList(data).then(res => {
    conditionList.value = res.items || []
    let itemSel = conditionList.value.length ? conditionList.value[0]  : null;
    conditionList.value.forEach(item => {
      item.value = item.value ? JSON.parse(item.value) : null;
      item.condition_format = conditionFormat(item.rules)
      if (selectCondition.value && selectCondition.value.code === item.code) {
          itemSel = item
      }
    })

    selectItem(itemSel)
  })
}
const filteredJsonList = computed(() => {
  const str = conditionJsonText.value.trim();
  if (appVersionsSelect.value.length) {
      let list = []
      appVersionsSelect.value.forEach(v => {
          appVersionDataVersion.value[v].forEach(vv => {
              if (!list.includes(vv)) {
                  list.push(vv)
              }
          })
      })

      const vList = []
      list.forEach(v => {
          versionList.value.some(vv => {
              if (v == vv.name && vv.name.includes(str)) {
                  vList.push(vv)
              }
          })
      })

      return vList
  } else {
      return versionList.value.filter(item => item?.name.includes(str));
  }
});
const filteredList = computed(() => {
  const str = conditionText.value.trim();
  return conditionList.value.filter(item => item?.name.includes(str));
});

const getCountry = async () => {
    await getCountryList().then(res => {
        countryList.value = res?.filter(item => {return item.code !== 'global'})
    })
}

const conditionFormat = (item: object) => {
    let formatContent = ''
    item = convertKeysToSnakeCase(item)
    const rangeLists = {
        'string': textRangeList,
        'int': valueRangeList,
        'float': valueRangeList,
        'array': itemRangeList,
        'date': dateRangeList,
        'datetime': dateRangeList,
        'boolean': booleanRangeList,
        'variant': itemRangeList,
    };

    Object.keys(item).forEach(k => {
        let label = attrList.value[k] ? attrList.value[k] : k
        if (k.startsWith('user_attribute')) {
            label = attrList.value['user_attribute']
        }
        formatContent += '<div class="condition-info">' + label + ':'
        let symbolLabel = item[k]['symbol']
        if (rangeLists[item[k]['value_type']]) {
            rangeLists[item[k]['value_type']].some(v => {
                if (v.value === item[k]['symbol']) {
                    symbolLabel = v.label
                    return
                }
            })
        }
        formatContent += ' ' + symbolLabel + ' '
        switch (k) {
            case 'country':
                let countryNames = ''
                item.country['thresholds'].forEach(c => {
                    countryList.value.some(v => {
                        if (v.code === c) {
                            countryNames += (countryNames ? '/' : '') + v.name;
                            return true
                        }
                    })
                })
                formatContent += countryNames
                break
            default:
                formatContent += item[k]['thresholds'].join('/')
                break
        }
        formatContent += '</div>'
    })
    return `<div>${formatContent}</div>`
}
const addCondition = () => {
    conditionModalRef.value.openModal(null, null, form.dataVersion)
    conditionSelectShow.value = false
  // addInputList.value.push({
  //   name: ''
  // })
}// 删除input
const deleteInputItem = (index: number) => {
  addInputList.value.splice(index, 1)
}

// 获取基本信息
const getInfo = async () => {
  configLoading.value = true
  await getTabList()
  config.value = conditionList.value[0]?.value
  form.code = conditionList.value[0]?.code
  form.status = conditionList.value[0]?.status
  configLoading.value = false
}

const getAppVersionDataVersionList = async () => {
    await getAppVersionDataVersion({appId: appCode.value}).then(res => {
        const resData = JSON.parse(res.data)
        appVersions.value = resData.app_versions
        appVersionDataVersion.value = resData.app_versions_data_versions
        appVersionsSelect.value = []
        if (resData.app_versions.length) {
            appVersionsSelect.value = [resData.app_versions[0]]
        }
    })
}

const getAttrList = async () => {
    await getRemoteConfigAttrList({appId: appCode.value}).then(res => {
        attrList.value = {}
        if (res) {
            res.forEach(v => {attrList.value[v.code] = v.name})
        }
    })
}

const getAvailableCondition = async () => {
    conditionAvailableLoading.value = true
    getConditionAvailable({appId: appCode.value}).then(res => {
        availableConditions.value = res
        conditionAvailableLoading.value = false
    })
}

const init = async () => {
  try {
    await Promise.all([
      getSourceList(),
      getCountry(),
    ]);
  } catch (error) {
    console.error('初始化列表失败:', error);
  }
}
init()

const appCode = ref('')
const refresh = async (appId: string, showAdd: boolean = false) => {
    try {
        appCode.value = appId
        form.dataVersion = ''
        selectCondition.value = ''
        config.value = {}
        conditionList.value = []
        conditionModalRef.value.appId = appId
        await getAvailableCondition()
        await Promise.all([
            getVersionList(),
            getAppVersionDataVersionList(),
            getAttrList()
        ]);

        if (filteredJsonList.value.length) {
            await selectJsonVersion(null, filteredJsonList.value[0])
        }

        if (showAdd) {
            addData('新增数据版本')
        }
    } catch (error) {
        console.error('初始化列表失败:', error);
    }
}


// 新增modal
const addRef = ref()
const addData = (value: string) => {
  addRef.value.openModal(value)
}
const updateListData = async (v) => {
  const operationMap = {
    '新增广告源': getSourceList,
    '新增数据版本': getVersionList
  };
  const addFunction = operationMap[v.desc];
  try {
    await addFunction(appCode.value);
    selectCondition.value = ''
    filteredJsonList.value.some(d => {
        if (v.name === d.name) {
            selectJsonVersion(null, d)
            return true
        }
    })

  } catch (error) {
    console.error('更新失败:', error);
  }
}
// 国家搜索
const searchCountry = (v) => {
  const data = {
    selectCountryCode:form.country,
    content:v
  }
  countrySearch(data).then(res => {
    countryList.value = []
    countryList.value = res?.filter(item => {return item.code !== 'global'});
  })
}
// 国家切换
const countryChange = async () => {
  if(!form.country.length){
    searchCountry('')
  }
}
// 点击条件列表
const selectItem = (item) => {
    selectCondition.value = item
  const {code,value,status,rules,note} = item
  form.code = code
  config.value = value
  form.status = status
  if(item.code !== 'default'){
    form.country = rules?.country?.thresholds
    form.appVersion = rules?.appVersion?.thresholds[0]
    form.sysVersion = rules?.osVersion?.thresholds[0]
    form.minMem = rules?.osRams?.thresholds[0]
    form.maxMem = rules?.osRams?.thresholds[1]
    form.adSource = rules?.mediaSources?.thresholds.join(',')
    // form.conditionNote = note
  }
}
// 数据版本change
const versionChange = async () => {
  // todo

}
const selectJsonVersion = async (e, item) => {
    form.dataVersion = item.name
    configLoading.value = true
    await getTabList()
    selectItem(conditionList.value[0])
    configLoading.value = false
}
const statusChange = async (e,obj) => {
  const params = {
    appId: appCode.value,
    paramName:'default_json',
    conditionCode:obj.code,
    conditionStatus:obj.status,
    rules: obj.rules
  }
  try {
    await saveCondition(params).then(async res => {
      Message.success('更改成功')
      configLoading.value = true
      await getTabList()
      configLoading.value = false
    })
  } catch (error) {
    console.log(error);
  }
}

const editItem = (item, type) => {
    selectItem(item)
    conditionModalRef.value.openModal(item,type, form.dataVersion)
}

const deleteItem = (item) => {
  const data = {
    appId: appCode.value,
    paramName:'default_json',
    conditionCode:item.code,
    dataVersion:form.dataVersion
  }
  deleteConditionItem(data).then(async res => {
    if(res){
      Message.success('删除成功')
      configLoading.value = true
      await getTabList()
      const {code,value,status,rules,note} = selectCondition.value
      form.code = code
      config.value = value
      form.status = status
      if(code !== 'default'){
        form.country = rules?.countries?.thresholds
        form.appVersion = rules?.appVersion?.thresholds[0]
        form.sysVersion = rules?.osVersion?.thresholds[0]
        form.minMem = rules?.osRams?.thresholds[0]
        form.maxMem = rules?.osRams?.thresholds[1]
        form.adSource = rules?.mediaSources?.thresholds.join(',')
        form.network = rules?.network?.thresholds[0]
        form.opt = rules?.opt?.thresholds[0]
        // form.conditionNote = note
      }
      configLoading.value = false
    }
  })
}
// 是否存在错误
const errors = ref(0);
// 错误行数
const line = ref();
// 数据更新时触发
const updataModel = (val: any) => {
  config.value = val;
};
// 数据错误时触发
const editError = (a: any, error: any) => {
  errors.value = error.length;
  if (error[0]) {
    line.value = error[0].line;
  }
}


// 格式化
const formatConfig = () => {
  if (typeof config.value === 'string') {
    config.value = JSON.parse(config.value);
  }
}
// 压缩
const compressConfig = () => {
  if (typeof config.value === 'object') {
    const compressed = JSON.stringify(config.value);
    config.value = compressed;
  }
}

// 复制
const copyConfig = () => {
  navigator.clipboard.writeText(JSON.stringify(config.value)).then(() => {
    Message.success('复制成功')
  }).catch(err => {
    console.error('复制失败:', err);
  });
}
// 清空
const clearConfig = () => {
  config.value = {}
}
const value1Change = () => {
  const num1 = form.minMem
  const num2 = form.maxMem
  if (num1 !== undefined && num2 !== undefined &&num1 >= 0 && num1 > num2) {
    form.maxMem = num1
  }
}

const value2Change = () => {
  const num1 = form.minMem
  const num2 = form.maxMem
  if (num1 !== undefined && num2 !== undefined && num2 < num1) {
    form.minMem = num2
  }
}
// 保存
const formRef = ref()
const saveLoading = ref(false)
const saveData = async() => {
  if(!config.value){
    Message.error('请输入json')
    return
  }
  saveLoading.value = true
  let status = form.status
  if (!config.value || Object.keys(config.value).length == 0 || (Object.keys(config.value).length == 2 &&
      Object.keys(config.value).includes('appid') && Object.keys(config.value).includes('v_api'))) {
      status = 0
  }
  const params = {
      appId: appCode.value,
      value: JSON.stringify(config.value),
      paramName: 'default_json',
      paramScope:'app_specific',
      dataVersion:form.dataVersion,
      conditionCode: form.code,
      conditionStatus: status,
      conditionScope: 'app_param_specific',
      valueType: 'json',
      // conditionNote: form.conditionNote,
      rules: selectCondition.value.rules
  };
  try {
    await saveCondition(params).then(async res => {
      Message.success('保存成功')
      configLoading.value = true
      await getTabList()
      configLoading.value = false
    })
  } catch (error) {
    console.log(error);

  } finally {
    saveLoading.value = false
  }

}
// 重置
const resetData = () => {
  config.value = {}
}

const dragLoading = ref(false)
const dragChange = async (data, extra, currentData) => {
    if (extra.type && extra.type=='drag') {
        let sorts = {}
        data.forEach((v, k) => {
            sorts[v.code] = k + 1
        })

        const reqData = {
            appId: appCode.value,
            dataVersion: form.dataVersion,
            sorts: JSON.stringify(sorts)
        }

        try{
            dragLoading.value = true
            await sortCondition(reqData).then(async res => {
                if (res) {
                    Message.success('排序成功')
                    await getTabList()
                }
            })
        }catch(error){
            console.log(error);

        }finally{
            dragLoading.value = false
        }
    }
}

const deleteVersion = (dataVersion) => {
    deleteDataVersion({dataCode: dataVersion.code}).then(res => {
        Message.success(`数据版本${dataVersion.name}删除成功`)
        refresh(appCode.value)
    }).catch(e => {
        console.error(e)
        Message.error("删除失败")
    }).finally(() => {

    })
}

const formatCallTime = (lastCallTime: number) => {
    if (lastCallTime == 0) {
        return '未调用'
    }

    const timeDuration = Date.now() - lastCallTime
    if (timeDuration < 60 * 1000) {
        return '刚刚'
    } else if (timeDuration < 5 * 60 * 1000) {
        return '1分钟前'
    } else if (timeDuration < 10 * 60 * 1000) {
        return '5分钟前'
    } else if (timeDuration < 30 * 60 * 1000) {
        return '10分钟前'
    } else if (timeDuration < 60 * 60 * 1000) {
        return '半小时前'
    } else if (timeDuration < 3 * 60 * 60 * 1000) {
        return '1小时前'
    } else if (timeDuration < 5 * 60 * 60 * 1000) {
        return '3小时前'
    } else if (timeDuration < 12 * 60 * 60 * 1000) {
        return '5小时前'
    } else if (timeDuration < 24 * 60 * 60 * 1000) {
        return '12小时前'
    } else if (timeDuration < 30 * 60 * 60 * 1000) {
        return '1天前'
    } else if (timeDuration < 180 * 60 * 60 * 1000) {
        return '30天前'
    } else if (timeDuration < 365 * 60 * 60 * 1000) {
        return '180天前'
    } else {
        return '1年前'
    }
}

defineExpose({refresh})
</script>

<style scoped lang="less">

// :deep(.arco-form-item:last-of-type) {
//   margin-bottom: 0;
// }

.group-box {
    width: 20%;
    height: 100%;
    border-right: 1px solid var(--color-secondary);

    .json-version-list{
        width: 100%;
        height: 100%;
        padding-right: 12px;
        .handle-box{
            display: flex;
            margin-bottom: 12px;
        }
    }
}

.group-detail {
    padding-left: 16px;
    overflow-y: auto;
    width: 100%;
    :deep(.condition-info){
        max-width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    :deep(.highlight-row){
        .arco-table-td {
            background-color: #f7f7f7 !important;
            .arco-switch[disabled]{
                background-color: var(--color-neutral-4);
            }
            .arco-switch[disabled].arco-switch-checked {
                background-color: var(--color-primary-light-3);
            }
        }
    }
    .condition-simple-info{
        font-size: 10px;
        padding-left: 8px;
        display: flex;
        align-items: center
    }
}

.detail-content {
  display: flex;
  align-items: stretch;

  .line {
    border-left: 2px dashed #ccc;
    margin-left: 24px;
    margin-right: 24px;
    width: 2px;
  }

  .form-content {
    flex: 1;
  }
}

.data-configure {
  width: 100%;
  height: 100%;
  padding: 15px 0;
  display: flex;
  .condition-list{
    width: 35%;
    height: 100%;
    padding-right: 12px;
    .handle-box{
      display: flex;
      margin-bottom: 12px;
    }
  }
  .configure-body {
    width: 100%;
    height: 100%;
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    padding: 12px;
    overflow-y: auto;
    .top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      .handle-btn{
        button {
            margin-left: 8px;
            border-radius: 4px;
          }
      }
    }
    .detail{
      display: flex;
      width: 100%;
      height: calc(100% - 44px);
    }

    .form-content {
      width: 33%;
      height: 100%;
      overflow-y: auto;
    }

    .json-content {
      flex: 1;
      padding-left: 8px;
      height: 100%;

      .action-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--tant-fill-color-fill1-2);

        .label {
          font-weight: 700;
        }

        .btns {
          button {
            margin-left: 8px;
            border-radius: 4px;
          }
        }
      }

      .editor {
        height: calc(100% - 56px);
        // height: 100%;
      }
    }
  }

}

.configure-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  height: 50px;
  margin-top: 16px;
  padding: 0 24px;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

:deep(.full-screen) {
  display: none !important;
}

:deep(.jsoneditor-poweredBy) {
  display: none !important;
}

:deep(.jsoneditor-modes) {
  display: none !important;
}

.edit-select {
  width: 100%;
  position: relative;

  .edit {
    position: absolute;
    right: 30px;
    top: 8px;
    opacity: 0;
    cursor: pointer;
  }

  &:hover {
    .edit {
      opacity: 1;
    }
  }
}
.men-content{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .to{
    margin: 0 12px;
  }
}
:deep(.arco-list-item){
  padding: 8px;
  cursor: pointer;
}
.add-box {
  background: #fff;

  .input-list {
    margin-bottom: 12px;
    position: relative;
  }

  .delete-icon {
    position: absolute;
    right: 12px;
    top: 4px;
    cursor: pointer;
  }
}
.label-text{
  color: var(--color-text-2);
  font-size: 14px;
  white-space: normal;
  line-height: 28px;
}
.create-condition{
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    padding: 0 12px;
    color: var(--color-text-1);
    font-size: 14px;
    line-height: 36px;
    text-align: left;
    background-color: var(--color-bg-popup);
    cursor: pointer;
}
</style>
