<template>
  <div class="feedback-container">
    <div class="feedback-header">
      <div class="feedback-header-top">
        <h3>问题反馈</h3>
        <a-button type="primary" @click="showFeedbackModal = true">
          <icon-plus />
          新增反馈
        </a-button>
      </div>
    </div>
    
    <!-- 筛选区域 - 添加反馈人选项 -->
    <div class="feedback-filters-container" @mousedown.stop>
      <div class="feedback-filters" @mousedown.stop>
        <div class="filter-item" @mousedown.stop>
          <label>对象：</label>
          <a-select v-model="filters.feedbackTarget" placeholder="请选择反馈对象" allow-clear style="width: 180px;" @mousedown.stop @change="applyFilters">
            <a-option value="GrowthX">GrowthX</a-option>
            <a-option disabled value="SDK">SDK</a-option>
          </a-select>
        </div>
        <div class="filter-item" @mousedown.stop>
          <label>版本：</label>
          <a-select v-model="filters.version" placeholder="请选择版本" allow-clear style="width: 180px;" @mousedown.stop @change="applyFilters">
            <a-option v-for="version in versionList" :key="version.versionCode" :value="version.versionCode">{{ version.versionCode }}
</a-option>
          </a-select>
        </div>
        <div class="filter-item" @mousedown.stop>
          <label>状态：</label>
          <a-select v-model="filters.status" placeholder="请选择状态" allow-clear style="width: 180px;" @mousedown.stop @change="applyFilters">
            <a-option value="ready">待处理</a-option>
            <a-option value="processing">处理中</a-option>
            <a-option value="processed">已处理</a-option>
            <a-option value="closed">已关闭</a-option>
          </a-select>
        </div>
        <div class="filter-item" @mousedown.stop>
          <label>日期：</label>
          <a-range-picker v-model="filters.dateRange" style="width: 220px;" @mousedown.stop @change="applyFilters" />
        </div>
        <div class="filter-actions" @mousedown.stop>
          <a-button @click="resetFilters" @mousedown.stop>重置</a-button>
        </div>
      </div>
    </div>

    <div class="feedback-content">
      <!-- 左侧反馈列表 -->
      <div class="feedback-list">
        <a-list>
          <a-list-item v-for="item in displayedFeedbackList" :key="item.id"
                       :class="{ 'active': selectedFeedback?.id === item.id }"
                       @click="selectFeedback(item)">
            <div class="feedback-item">
              <div class="feedback-title">
                <span class="feedback-status-badge" :class="getStatusClass(item.status)">
                  <!-- 使用不同形状的圆形图标表示状态 -->
                  <span v-if="item.status === '待处理'" class="status-icon empty-circle"></span>
                  <span v-else-if="item.status === '处理中'" class="status-icon rotating-circle"></span>
                  <span v-else-if="item.status === '已处理'" class="status-icon full-circle"></span>
                </span>
                {{ item.title }}

              </div>
              <div class="feedback-meta">
                <div class="feedback-meta-item">
                  <span>{{ item.creator }}
</span>
                </div>
                <div class="feedback-meta-item">
                  <span>@</span>
                </div>
                <div class="feedback-meta-item">
                  <span>{{ item.assignee }}
</span>
                </div>
                <div class="feedback-meta-item">
                  <span>{{ item.time }}
</span>
                </div>
                <div class="feedback-meta-item">
                  <span class="feedback-type" :class="getTypeClass(item.type)">{{ getTypeText(item.type) }}
</span>
                </div>
              </div>
            </div>
          </a-list-item>
        </a-list>

        <!-- 分页 -->
        <div class="feedback-pagination">
          <a-pagination
            :total="total"
            :page-size="pageSize"
            :current="currentPage"
            @change="handlePageChange"
            show-total
            show-jumper
          />
        </div>
      </div>

      <!-- 右侧反馈详情 -->
      <div class="feedback-detail">
        <div v-if="selectedFeedback" class="feedback-detail-content">
          <div class="feedback-detail-header">
            <h4>{{ selectedFeedback.title }}
</h4>
            <div class="feedback-detail-status">
              <span class="feedback-status" :class="getStatusClass(selectedFeedback.status)">
                <!-- 使用不同形状的圆形图标表示状态 -->
                <span v-if="selectedFeedback.status === '待处理'" class="status-icon empty-circle"></span>
                <span v-else-if="selectedFeedback.status === '处理中'" class="status-icon rotating-circle"></span>
                <span v-else-if="selectedFeedback.status === '已处理'" class="status-icon full-circle"></span>
                {{ selectedFeedback.status }}

              </span>
              <!-- 添加修改状态按钮 -->
              <a-button type="text" size="small" @click="showStatusModal = true">修改状态</a-button>
            </div>
          </div>

          <div class="feedback-detail-meta">
            <div class="feedback-detail-meta-item">
              <span class="meta-label">反馈人：</span>
              <span>{{ selectedFeedback.creator }}
</span>
            </div>
            <div class="feedback-detail-meta-item">
              <span class="meta-label">处理人：</span>
              <span>{{ selectedFeedback.assignee }}
</span>
            </div>
            <div class="feedback-detail-meta-item">
              <span class="meta-label">创建时间：</span>
              <span>{{ selectedFeedback.time }}
</span>
            </div>
            <div class="feedback-detail-meta-item">
              <span class="meta-label">反馈类型：</span>
              <span class="feedback-type" :class="getTypeClass(selectedFeedback.type)">{{ getTypeText(selectedFeedback.type) }}
</span>
            </div>
          </div>

          <div class="feedback-description">
            <h5>问题描述</h5>
            <div class="description-content" v-html="selectedFeedback.description"></div>
          </div>

          <div class="feedback-replies">
            <h5>回复记录 ({{ selectedFeedback.replies.length }})</h5>
            <div v-for="reply in selectedFeedback.replies" :key="reply.id" class="reply-item">
              <div class="reply-header">
                <span class="reply-author">{{ reply.author }}</span>
                <span class="reply-time">{{ reply.time }}</span>
              </div>
              <div class="reply-content" v-html="reply.content"></div>
            </div>

            <!-- 回复输入框 -->
            <div class="reply-input" @mousedown.stop>
              <div id="reply-ckeditor" style="width: 100%; height: 300px; position: relative;" @mousedown.stop>
                <ckeditor
                    ref="replyEditorRef"
                    v-model="replyContent"
                    :editor="ClassicEditor"
                    :config="editorConfig"
                    placeholder="添加回复..."
                    @ready="onEditorReady"
                    @mousedown.stop
                    style="height: 100%; width: 100%; display: block; position: absolute; top: 0; left: 0;"
                ></ckeditor>
              </div>
              <!-- 添加一个提示信息，让用户知道这是可编辑区域 -->
              <div class="reply-actions" @mousedown.stop>
                <a-button type="primary" @click="addReply" @mousedown.stop>回复</a-button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="feedback-detail-empty">
          <p>请选择一个反馈项查看详细信息</p>
        </div>
      </div>
    </div>

    <!-- 新增反馈弹窗 -->
    <a-modal v-model:visible="showFeedbackModal" title="新增反馈" @ok="submitFeedback" @cancel="resetFeedbackForm" width="800px" @mousedown.stop>
      <a-form :model="feedbackForm" @mousedown.stop>
        <a-form-item label="标题" required @mousedown.stop>
          <a-input v-model="feedbackForm.title" placeholder="请输入标题" @mousedown.stop />
        </a-form-item>
        <a-form-item label="对象" @mousedown.stop>
          <a-select v-model="feedbackForm.feedbackTarget" placeholder="请选择反馈对象" @mousedown.stop>
            <a-option value="GrowthX">GrowthX</a-option>
            <a-option disabled value="SDK">SDK</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="版本" @mousedown.stop>
          <a-select v-model="feedbackForm.version" placeholder="请选择版本" @mousedown.stop>
            <a-option v-for="version in versionList" :key="version.versionCode" :value="version.versionCode">{{ version.versionCode }}
</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述" required @mousedown.stop>
          <div id="ckeditor" style="width: 100%; height: 450px; position: relative;" @mousedown.stop>
            <ckeditor
                v-model="feedbackForm.description"
                :editor="ClassicEditor"
                :config="editorConfig"
                placeholder="请详细描述您的问题或建议"
                @ready="onEditorReady"
                @mousedown.stop
                style="height: 100%; width: 100%; display: block; position: absolute; top: 0; left: 0;"
            ></ckeditor>
          </div>
          <!-- 添加一个提示信息，让用户知道这是可编辑区域 -->

        </a-form-item>
        <a-form-item label="类型" @mousedown.stop>
          <a-select v-model="feedbackForm.type" placeholder="请选择反馈类型" @mousedown.stop>
            <a-option value="bug">Bug</a-option>
            <a-option value="feature">功能建议</a-option>
            <a-option value="other">其他</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="处理人" @mousedown.stop>
          <a-select v-model="feedbackForm.assignee" placeholder="请选择处理人" @mousedown.stop>
            <a-option v-for="handler in handlerList" :key="handler.code" :value="handler.code">{{ handler.name }}
</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 修改状态弹窗 -->
    <a-modal v-model:visible="showStatusModal" title="修改反馈状态" @ok="updateFeedbackStatus" @cancel="cancelStatusUpdate" width="400px" @mousedown.stop>
      <a-form :model="statusForm" @mousedown.stop>
        <a-form-item label="当前状态" @mousedown.stop>
          <a-tag :color="getStatusTagColor(selectedFeedback?.status)" @mousedown.stop>{{ selectedFeedback?.status }}
</a-tag>
        </a-form-item>
        <a-form-item label="新状态" @mousedown.stop>
          <a-select v-model="statusForm.newStatus" placeholder="请选择新状态" @mousedown.stop>
            <a-option value="ready">待处理</a-option>
            <a-option value="processing">处理中</a-option>
            <a-option value="processed">已处理</a-option>
            <a-option value="closed">已关闭</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, onUnmounted, reactive, ref, watch} from 'vue';
import {Message} from '@arco-design/web-vue';
import {Alignment, Bold, ClassicEditor, Essentials, Font, Heading, ImageBlock, ImageInline, ImageUpload, Italic, Link, List, Undo} from 'ckeditor5';
import {createFeedbackRepliedMessage, createFeedbackStatusChangedMessage, createNewFeedbackMessage, FeedbackMessageData, sendMessageToUser} from '@/api/feedback/message';
import {
  FeedbackDetailResponseData,
  FeedbackListItem,
  FeedbackListParams,
  FeedbackListResponseData,
  FeedbackReply,
  FeedbackReplySaveParams,
  FeedbackReplySaveResponseData,
  FeedbackSaveParams,
  getFeedbackDetail,
  getFeedbackList,
  getHandlerList,
  getVersionList,
  HandlerInfo,
  saveFeedback,
  saveFeedbackReply,
  uploadImage,
  VersionInfo
} from '@/api/feedback/api';

// 反馈数据相关状态（添加版本信息、类型信息和反馈对象信息）
const feedbackList = ref<any[]>([]);

const selectedFeedback = ref<any>(null); // 默认不选中任何反馈

// 添加筛选相关的状态（添加反馈人和搜索文本选项）
const filters = ref({
  status: undefined,
  version: undefined,
  feedbackTarget: 'GrowthX',
  dateRange: [] as string[]
});

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

// 回复输入内容
const replyContent = ref('');

// 添加筛选后的新反馈列表
const filteredFeedbackList = ref<any[]>([]);

// 处理人列表
const handlerList = ref<HandlerInfo[]>([]);

// 版本列表
const versionList = ref<VersionInfo[]>([]);

const showFeedbackModal = ref(false);
const showStatusModal = ref(false);
const newStatus = ref('');

const feedbackForm = reactive({
  title: '',
  feedbackTarget: 'GrowthX', // 新增反馈对象字段
  version: '', // 新增版本字段
  description: '',
  type: 'bug',
  assignee: ''
});

// 状态表单
const statusForm = reactive({
  newStatus: ''
});

// CKEditor 配置
const editorConfig = reactive({
  language: 'zh-cn',
  plugins: [Bold, Essentials, Italic, Undo, Font, Heading, Link, List, Alignment, ImageUpload, ImageInline, ImageBlock],
  toolbar: {
    items: ['undo', 'redo', '|', 'heading', 'bold', 'italic', 'fontColor', '|', 'alignment', 'bulletedList', 'numberedList', 'link', 'imageUpload'],
    shouldNotGroupWhenFull: true
  },
  // 图片上传配置
  image: {
    toolbar: ['imageTextAlternative'],
    upload: {
      types: ['jpeg', 'png', 'gif', 'bmp', 'webp']
    }
  },
  // 确保编辑器有足够空间
  height: '100%',
  // 编辑器主体配置
  ckeditor: {
    main: {
      height: '100%'
    }
  },
  // 可编辑区域配置
  editing: {
    minHeight: '400px'
  }
});

// 图片上传适配器
const MyUploadAdapter = class {
  loader: any;

  constructor(loader: any) {
    this.loader = loader;
  }

  upload() {
    console.log('开始上传图片:', this.loader.file);
    // 直接返回一个Promise，避免使用.then链
    return this.loader.file.then((file: File) => {
      // 调用上传接口
      return uploadImage(file)
        .then((response: any) => {
          console.log('图片上传响应:', response);
          // 检查响应数据是否存在
          if (!response || !response.url) {
            console.error('图片上传响应数据格式不正确:', response);
            throw new Error('图片上传响应数据格式不正确');
          }
          return { default: response.url };
        })
        .catch((error: any) => {
          console.error('图片上传失败:', error);
          Message.error('图片上传失败: ' + (error.message || '请稍后重试'));
          throw error;
        });
    }).catch((error: any) => {
      console.error('文件加载失败:', error);
      throw error;
    });
  }

  abort() {
    // 支持中止上传操作
    console.log('中止上传操作');
  }
};

// 回复编辑器引用
const replyEditorRef = ref(null);

// 图片上传处理方法
const handleImageUpload = (file: File) => {
  return new Promise((resolve, reject) => {
    // 模拟图片上传过程
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      // 模拟上传成功，返回图片URL
      const imageUrl = e.target?.result;
      if (imageUrl) {
        resolve({
          default: imageUrl
        });
      } else {
        reject(new Error('图片上传失败'));
      }
    };
    reader.onerror = () => {
      reject(new Error('图片读取失败'));
    };
    reader.readAsDataURL(file);
  });
};

// 编辑器就绪回调
const onEditorReady = (editor: any) => {
  // 设置自定义图片上传适配器
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    return new MyUploadAdapter(loader);
  };
};

// 计算分页后的反馈列表
const displayedFeedbackList = computed(() => {
  // 当使用服务端分页时，filteredFeedbackList已经包含了当前页的数据，直接返回即可
  return filteredFeedbackList.value;
});

// 设置默认日期范围为最近7天
const setDefaultDateRange = () => {
  const end = new Date();
  const start = new Date();
  start.setDate(start.getDate() - 7);

  filters.value.dateRange = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ];
};

// 获取处理人列表
const loadHandlerList = async () => {
  try {
    const response = await getHandlerList();
    handlerList.value = response as unknown as HandlerInfo[];
  } catch (error: any) {
    console.error('获取处理人列表失败:', error);
    Message.error('获取处理人列表失败: ' + (error.message || '请稍后重试'));
  }
};

// 获取版本列表
const loadVersionList = async (objectCode: string) => {
  try {
    const response = await getVersionList(objectCode);
    versionList.value = response as unknown as VersionInfo[];

    // 如果版本列表不为空且当前未选择版本，则默认选中第一个
    if (versionList.value.length > 0 && !feedbackForm.version) {
      feedbackForm.version = versionList.value[0].versionCode;
    }
  } catch (error: any) {
    console.error('获取版本列表失败:', error);
    Message.error('获取版本列表失败: ' + (error.message || '请稍后重试'));
  }
};

// 监听反馈对象变化，动态加载对应的版本列表
watch(() => feedbackForm.feedbackTarget, (newObjectCode) => {
  if (newObjectCode) {
    loadVersionList(newObjectCode);
  } else {
    versionList.value = [];
  }
});

// 获取反馈列表
const loadFeedbackList = async () => {
  try {
    const params: FeedbackListParams = {
      current: currentPage.value,
      pageSize: pageSize.value,
      statusCode: filters.value.status,
      objectCode: filters.value.feedbackTarget
    };
    // 如果有日期范围筛选，添加到参数中
    if (filters.value.dateRange.length === 2) {
      params.startDate = filters.value.dateRange[0];
      params.endDate = filters.value.dateRange[1];
    }

    // 如果有版本筛选，添加到参数中
    if (filters.value.version) {
      params.versionCode = filters.value.version;
    }

    // 注意：axios拦截器会自动处理响应结构，直接返回data部分
    const response = await getFeedbackList(params);

    // 检查响应数据是否存在
    if (!response) {
      throw new Error('响应数据为空');
    }

    // 类型断言，因为axios拦截器已经处理了响应结构
    const data = response as unknown as FeedbackListResponseData;

    // 转换API返回的数据格式以匹配组件中的使用
    const convertedList = data.items.map((item: FeedbackListItem) => ({
      id: item.id,
      code: item.code,
      title: item.title,
      status: getStatusText(item.statusCode), // 转换状态码为文本
      creator: item.creator?.name || '未知用户',
      assignee: item.handler?.name || '未分配',
      version: item.versionCode || '未知版本',
      type: item.typeCode || 'other',
      feedbackTarget: item.objectCode || '未知对象',
      time: item.feedbackTime ? formatDateTime(item.feedbackTime) : '未知时间', // 使用精确到秒的时间格式
      description: item.description,
      replies: item.replies.map((reply: FeedbackReply) => ({
        id: reply.id,
        code: reply.code,
        author: reply.creator?.name || '未知用户',
        content: reply.content,
        time: reply.createTime ? formatDateTime(reply.createTime) : '未知时间' // 使用精确到秒的时间格式
      }))
    }));

    // 对于分页，我们直接使用API返回的当前页数据
    feedbackList.value = convertedList;
    filteredFeedbackList.value = convertedList;
    total.value = data.total;

    // 每次加载新页面数据时，选中当前页的第一条反馈
    if (convertedList.length > 0) {
      selectedFeedback.value = convertedList[0];
    } else {
      selectedFeedback.value = null;
    }
  } catch (error: any) {
    console.error('获取反馈列表失败:', error);
    Message.error('获取反馈列表失败: ' + (error.message || '请稍后重试'));
  }
};

// 状态码转文本
const getStatusText = (statusCode: string) => {
  switch (statusCode) {
    case 'ready':
      return '待处理';
    case 'processing':
      return '处理中';
    case 'processed':
      return '已处理';
    case 'closed':
      return '已关闭';
    default:
      return '待处理';
  }
};

// 获取状态码对应的英文标识
const getStatusCode = (statusText: string) => {
  switch (statusText) {
    case '待处理':
      return 'ready';
    case '处理中':
      return 'processing';
    case '已处理':
      return 'processed';
    case '已关闭':
      return 'closed';
    default:
      return 'ready';
  }
};

// 筛选方法
const applyFilters = () => {
  loadFeedbackList();
};

// 重置筛选
const resetFilters = () => {
  filters.value = {
    status: '',
    version: '',
    feedbackTarget: '',
    dateRange: []
  };
  setDefaultDateRange(); // 重置后设置默认日期范围
  currentPage.value = 1;
  loadFeedbackList();
};

// 分页改变
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadFeedbackList();
};

// 格式化时间戳为 YYYY-MM-DD HH:mm:ss 格式
const formatDateTime = (timestamp: number) => {
  if (!timestamp) return '未知时间';
  const date = new Date(timestamp);
  return date.getFullYear() + '-' +
    String(date.getMonth() + 1).padStart(2, '0') + '-' +
    String(date.getDate()).padStart(2, '0') + ' ' +
    String(date.getHours()).padStart(2, '0') + ':' +
    String(date.getMinutes()).padStart(2, '0') + ':' +
    String(date.getSeconds()).padStart(2, '0');
};

// 反馈功能相关方法
const selectFeedback = async (feedback: any) => {
  // 如果选中的反馈与当前选中的一致，则不重新加载
  if (selectedFeedback.value && selectedFeedback.value.code === feedback.code) {
    return;
  }

  try {
    // 调用反馈详情接口获取完整的反馈信息和回复列表
    const response = await getFeedbackDetail(feedback.code);
    const detailData = response as unknown as FeedbackDetailResponseData;

    // 转换API返回的数据格式以匹配组件中的使用
    const convertedFeedback = {
      id: detailData.id,
      code: detailData.code,
      title: detailData.title,
      status: getStatusText(detailData.statusCode), // 转换状态码为文本
      creator: detailData.creator?.name || '未知用户',
      assignee: detailData.handler?.name || '未分配',
      version: detailData.versionCode || '未知版本',
      type: detailData.typeCode || 'other',
      feedbackTarget: detailData.objectCode || '未知对象',
      time: detailData.feedbackTime ? formatDateTime(detailData.feedbackTime) : '未知时间', // 使用精确到秒的时间格式
      description: detailData.description,
      replies: detailData.replies.map((reply: FeedbackReply) => ({
        id: reply.id,
        code: reply.code,
        author: reply.creator?.name || '未知用户',
        content: reply.content,
        time: reply.createTime ? formatDateTime(reply.createTime) : '未知时间' // 使用精确到秒的时间格式
      }))
    };

    selectedFeedback.value = convertedFeedback;
  } catch (error: any) {
    console.error('获取反馈详情失败:', error);
    Message.error('获取反馈详情失败: ' + (error.message || '请稍后重试'));
    // 如果获取详情失败，仍然选择反馈项
    selectedFeedback.value = feedback;
  }
};

// 获取状态对应的样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '待处理':
      return 'status-pending';
    case '处理中':
      return 'status-processing';
    case '已处理':
      return 'status-completed';
    case '已关闭':
      return 'status-closed';
    default:
      return 'status-pending';
  }
};

// 获取类型对应的文本
const getTypeText = (type: string) => {
  switch (type) {
    case 'bug':
      return 'Bug';
    case 'feature':
      return '功能建议';
    case 'other':
      return '其他';
    default:
      return '其他';
  }
};

// 获取类型对应的样式类
const getTypeClass = (type: string) => {
  switch (type) {
    case 'bug':
      return 'type-bug';
    case 'feature':
      return 'type-feature';
    case 'other':
      return 'type-other';
    default:
      return 'type-other';
  }
};

const submitFeedback = async () => {
  if (!feedbackForm.title || !feedbackForm.description) {
    Message.error('请填写反馈标题和描述');
    return;
  }

  try {
    // 准备提交数据
    const feedbackData: FeedbackSaveParams = {
      title: feedbackForm.title,
      description: feedbackForm.description,
      objectCode: feedbackForm.feedbackTarget, // 反馈对象作为对象编号
      versionCode: feedbackForm.version,       // 版本作为版本编号
      typeCode: feedbackForm.type,             // 类型作为类型编号
      handlerCode: feedbackForm.assignee,      // 处理人作为处理人编号
      statusCode: 'ready'                      // 默认状态为待处理
    };

    // 调用API保存反馈
    const response = await saveFeedback(feedbackData);

    // 由于axios拦截器已经处理了通用响应结构，直接使用响应数据
    Message.success('反馈提交成功');

    // 从响应中获取反馈详情数据（现在save接口返回完整的反馈详情）
    let detailData = response as unknown as FeedbackDetailResponseData;
    // 转换API返回的数据格式以匹配组件中的使用
    const newFeedback = {
      id: detailData.id,
      code: detailData.code,
      title: detailData.title,
      status: getStatusText(detailData.statusCode), // 转换状态码为文本
      creator: detailData.creator?.name || '未知用户',
      assignee: detailData.handler?.name || '未分配',
      version: detailData.versionCode || '未知版本',
      type: detailData.typeCode || 'other',
      feedbackTarget: detailData.objectCode || '未知对象',
      time: detailData.feedbackTime ? formatDateTime(detailData.feedbackTime) : '未知时间', // 使用精确到秒的时间格式
      description: detailData.description,
      replies: detailData.replies.map((reply: FeedbackReply) => ({
        id: reply.id,
        code: reply.code,
        author: reply.creator?.name || '未知用户',
        content: reply.content,
        time: reply.createTime ? formatDateTime(reply.createTime) : '未知时间' // 使用精确到秒的时间格式
      }))
    };

    // 处理分页问题
    if (currentPage.value === 1) {
      // 如果在第一页，直接添加到列表开头
      feedbackList.value.unshift(newFeedback);
      // 更新筛选后的列表
      filteredFeedbackList.value = [...feedbackList.value];
      // 选中新创建的反馈
      selectedFeedback.value = newFeedback;

      total.value = feedbackList.value.length;

    } else {
      // 如果不在第一页，切换到第一页以显示新反馈
      currentPage.value = 1;
      // 重新加载第一页数据以确保分页一致性
      await loadFeedbackList();
    }
    resetFeedbackForm();
    showFeedbackModal.value = false;
    // 发送新反馈消息通知给处理人
    if (feedbackForm.assignee) {
      const messageData: FeedbackMessageData = {
        feedbackId: newFeedback.id,
        feedbackTitle: newFeedback.title,
        feedbackType: newFeedback.type,
        feedbackCreator: newFeedback.creator,
        feedbackAssignee: newFeedback.assignee,
        feedbackStatus: newFeedback.status
      };

      const message = createNewFeedbackMessage(messageData);
      // 这里应该根据实际用户系统获取处理人ID
      const assigneeId = `user_${newFeedback.assignee}`;
      await sendMessageToUser(message, assigneeId);
    }
  } catch (error: any) {
    console.error('提交反馈时出错:', error);
    Message.error('反馈提交失败: ' + (error.message || '请稍后重试'));
  }
};

const resetFeedbackForm = () => {
  feedbackForm.title = '';
  feedbackForm.feedbackTarget = 'GrowthX'; // 重置反馈对象
  feedbackForm.version = ''; // 重置版本
  feedbackForm.description = '';
  feedbackForm.type = 'bug';
  feedbackForm.assignee = '';
};

// 添加回复
const addReply = async () => {
  if (!replyContent.value.trim()) {
    Message.error('请输入回复内容');
    return;
  }

  if (selectedFeedback.value) {
    try {
      // 准备保存回复的数据
      const replyData: FeedbackReplySaveParams = {
        feedbackCode: selectedFeedback.value.code, // 反馈编号
        content: replyContent.value // 回复内容
      };

      // 调用API保存回复
      const response = await saveFeedbackReply(replyData);
      const replyResponse = response as unknown as FeedbackReplySaveResponseData;

      // 使用API返回的回复编号创建新的回复对象
      const newReply = {
        id: Date.now(), // 临时ID，实际应该使用API返回的ID
        code: replyResponse.code, // 使用API返回的回复编号
        author: '当前用户', // 实际应该从用户信息中获取
        content: replyContent.value,
        time: new Date().toISOString().split('T')[0]
      };

      selectedFeedback.value.replies.push(newReply);
      replyContent.value = '';
      Message.success('回复成功');

      // 发送回复消息通知给反馈创建人
      const messageData: FeedbackMessageData = {
        feedbackId: selectedFeedback.value.id,
        feedbackTitle: selectedFeedback.value.title,
        feedbackType: selectedFeedback.value.type,
        feedbackCreator: selectedFeedback.value.creator,
        feedbackAssignee: selectedFeedback.value.assignee,
        feedbackStatus: selectedFeedback.value.status,
        replyContent: newReply.content,
        replyAuthor: newReply.author
      };

      const message = createFeedbackRepliedMessage(messageData);
      // 这里应该根据实际用户系统获取反馈创建人ID
      const creatorId = `user_${selectedFeedback.value.creator}`;
      await sendMessageToUser(message, creatorId);
    } catch (error: any) {
      console.error('保存回复时出错:', error);
      Message.error('回复保存失败: ' + (error.message || '请稍后重试'));
    }
  }
};

// 获取状态对应的标签颜色
const getStatusTagColor = (status: string) => {
  switch (status) {
    case '待处理':
      return 'orange';
    case '处理中':
      return 'blue';
    case '已处理':
      return 'green';
    case '已关闭':
      return 'gray';
    default:
      return 'gray';
  }
};

// 更新反馈状态
const updateFeedbackStatus = async () => {
  if (!statusForm.newStatus) {
    Message.error('请选择新状态');
    return;
  }

  if (selectedFeedback.value) {
    try {
      // 准备更新状态的数据
      const feedbackData: FeedbackSaveParams = {
        code: selectedFeedback.value.code,  // 反馈编号
        statusCode: statusForm.newStatus  // 状态编号
      };

      // 调用保存反馈接口更新状态
      await saveFeedback(feedbackData);

      // 将英文状态码转换为中文显示文本
      const statusText = getStatusText(statusForm.newStatus);

      // 更新选中反馈的状态
      selectedFeedback.value.status = statusText;

      // 更新反馈列表中的状态
      const feedback = feedbackList.value.find(item => item.id === selectedFeedback.value.id);
      if (feedback) {
        feedback.status = statusText;
      }

      // 更新筛选后的列表
      filteredFeedbackList.value = [...feedbackList.value];

      // 重置状态并关闭弹窗
      statusForm.newStatus = '';
      showStatusModal.value = false;
      Message.success('状态更新成功');

      // 发送状态变更消息通知相关人员
      const messageData: FeedbackMessageData = {
        feedbackId: selectedFeedback.value.id,
        feedbackTitle: selectedFeedback.value.title,
        feedbackType: selectedFeedback.value.type,
        feedbackCreator: selectedFeedback.value.creator,
        feedbackAssignee: selectedFeedback.value.assignee,
        feedbackStatus: statusText // 使用转换后的中文状态文本
      };

      const message = createFeedbackStatusChangedMessage(messageData);
      // 通知反馈创建人
      const creatorId = `user_${selectedFeedback.value.creator}`;
      await sendMessageToUser(message, creatorId);

      // 如果处理人不是创建人，也通知处理人
      if (selectedFeedback.value.assignee !== selectedFeedback.value.creator) {
        const assigneeId = `user_${selectedFeedback.value.assignee}`;
        await sendMessageToUser(message, assigneeId);
      }
    } catch (error: any) {
      console.error('更新反馈状态失败:', error);
      Message.error('状态更新失败: ' + (error.message || '请稍后重试'));
    }
  }
};

// 取消状态更新
const cancelStatusUpdate = () => {
  newStatus.value = '';
  showStatusModal.value = false;
};

// 处理反馈消息点击事件
const handleFeedbackMessageClick = (event: CustomEvent) => {
  const { feedbackId } = event.detail;
  // 查找对应的反馈项
  const feedback = feedbackList.value.find(item => item.id === feedbackId);
  if (feedback) {
    // 选择该反馈项
    selectFeedback(feedback);
    // 如果有分页，跳转到对应页面
    const index = feedbackList.value.findIndex(item => item.id === feedbackId);
    if (index >= 0) {
      const page = Math.floor(index / pageSize.value) + 1;
      currentPage.value = page;
    }
    // 显示反馈面板（如果当前是隐藏状态）
    // 这里可能需要通过父组件来控制显示状态
    console.log('定位到反馈:', feedback);
  } else {
    Message.warning('未找到对应的反馈项');
  }
};

// 组件挂载时设置默认日期范围
onMounted(() => {
  setDefaultDateRange();
  loadFeedbackList(); // 加载反馈列表
  loadHandlerList(); // 加载处理人列表
  loadVersionList(feedbackForm.feedbackTarget); // 加载默认对象的版本列表

  // 添加消息事件监听器
  window.addEventListener('feedbackMessageClicked', handleFeedbackMessageClick as EventListener);
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('feedbackMessageClicked', handleFeedbackMessageClick as EventListener);
});
</script>

<style lang="less" scoped>
.feedback-container {
  width: 1200px;
  height: 700px;
  display: flex;
  flex-direction: column;
}

.feedback-header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);

  .feedback-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.feedback-filters-container {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-fill-1);
}

.feedback-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  .filter-item {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      white-space: nowrap;
    }
  }

  .filter-actions {
    display: flex;
    gap: 8px;
    margin-left: auto;
  }
}

.feedback-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.feedback-list {
  width: 500px;
  border-right: 1px solid var(--color-border);
  padding: 16px;
  display: flex;
  flex-direction: column;

  .feedback-item {
    padding: 12px 8px;
    border-bottom: 1px solid var(--color-border-1);
    cursor: pointer;

    &:hover {
      background-color: var(--color-fill-1);
    }

    .feedback-title {
      font-weight: 500;
      margin-bottom: 8px;
      display: flex;
      align-items: center;

      .feedback-status-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        margin-right: 8px;

        .status-icon {
          display: inline-block;
          width: 12px;
          height: 12px;

          &.empty-circle {
            border: 2px solid #999;
            border-radius: 50%;
          }

          &.rotating-circle {
            border: 2px solid #4096ff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: rotate 1s linear infinite;
          }

          &.full-circle {
            background-color: #00b42a;
            border-radius: 50%;
          }
        }
      }
    }

    .feedback-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 12px;
      color: var(--color-text-3);

      .feedback-meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .feedback-type {
        padding: 2px 6px;
        border-radius: 2px;
        font-size: 12px;

        &.type-bug {
          background-color: #ff4d4f;
          color: white;
        }

        &.type-feature {
          background-color: #1890ff;
          color: white;
        }

        &.type-other {
          background-color: #999;
          color: white;
        }
      }
    }
  }

  .active {
    background-color: var(--color-primary-light-1);
  }

  .feedback-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

.feedback-detail {
  flex: 1;
  padding: 16px;
  overflow-y: auto;

  .feedback-detail-content {
    .feedback-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      .feedback-detail-status {
        .feedback-status {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;

          .status-icon {
            display: inline-block;
            width: 12px;
            height: 12px;

            &.empty-circle {
              border: 2px solid #999;
              border-radius: 50%;
            }

            &.rotating-circle {
              border: 2px solid #4096ff;
              border-radius: 50%;
              border-top-color: transparent;
              animation: rotate 1s linear infinite;
            }

            &.full-circle {
              background-color: #00b42a;
              border-radius: 50%;
            }
          }
        }
      }
    }

    .feedback-detail-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      padding: 12px 0;
      border-top: 1px solid var(--color-border-1);
      border-bottom: 1px solid var(--color-border-1);
      margin-bottom: 24px;

      .feedback-detail-meta-item {
        display: flex;
        gap: 8px;

        .meta-label {
          font-weight: 500;
          color: var(--color-text-2);
        }

        .feedback-type {
          padding: 2px 6px;
          border-radius: 2px;
          font-size: 12px;

          &.type-bug {
            background-color: #ff4d4f;
            color: white;
          }

          &.type-feature {
            background-color: #1890ff;
            color: white;
          }

          &.type-other {
            background-color: #999;
            color: white;
          }
        }
      }
    }

    .feedback-description {
      margin-bottom: 24px;

      h5 {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
      }

      .description-content {
        line-height: 1.6;
        white-space: pre-wrap;
      }
    }

    .feedback-replies {
      h5 {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
      }

      .reply-item {
        padding: 16px;
        background-color: var(--color-fill-1);
        border-radius: 4px;
        margin-bottom: 16px;

        .reply-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;

          .reply-author {
            font-weight: 500;
          }

          .reply-time {
            font-size: 12px;
            color: var(--color-text-3);
          }
        }

        .reply-content {
          line-height: 1.6;
          white-space: pre-wrap;
        }
      }

      .reply-input {
        margin-top: 24px;

        .reply-actions {
          margin-top: 12px;
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .feedback-detail-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--color-text-3);
  }
}

// 状态样式类
.status-pending {
  background-color: #fff7e8;
  color: #ff7d00;
}

.status-processing {
  background-color: #e8f3ff;
  color: #4096ff;
}

.status-completed {
  background-color: #e8ffea;
  color: #00b42a;
}

.status-closed {
  background-color: #f2f3f5;
  color: #86909c;
}

// 类型样式类
.type-bug {
  background-color: #ff4d4f;
  color: white;
}

.type-feature {
  background-color: #1890ff;
  color: white;
}

.type-other {
  background-color: #999;
  color: white;
}

// 添加旋转动画
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 限制图片显示大小的样式
.feedback-description, .reply-content {
  :deep(img) {
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    margin: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

// CKEditor 样式
#ckeditor, #reply-ckeditor {
  .ck-editor__editable {
    min-height: 400px !important;
    height: 450px !important;  // 设置固定高度以确保足够的编辑空间
    max-height: 500px !important;

    // 确保可编辑区域有足够的内边距
    padding: 20px !important;

    // 确保可编辑区域可以正常滚动
    overflow-y: auto !important;

    // 确保可编辑区域有清晰的焦点状态
    &:focus {
      outline: 1px solid #4096ff !important;
      outline-offset: -1px !important;
    }
  }
}

// 确保CKEditor容器本身也有足够的高度
#ckeditor {
  height: 450px !important;
  min-height: 450px !important;

  :deep(.ck-editor) {
    height: 100% !important;
    min-height: 450px !important;

    .ck-editor__top {
      height: auto !important;
    }

    .ck-editor__main {
      height: calc(100% - 400px) !important; // 减去工具栏高度
      min-height: 410px !important;

      .ck-content {
        height: 100% !important;
        min-height: 410px !important;

        // 确保内容区域有足够空间
        padding: 20px !important;
        box-sizing: border-box !important;

        // 确保内容区域有清晰的焦点状态
        &:focus {
          outline: 1px solid #4096ff !important;
          outline-offset: -1px !important;
        }
      }
    }
  }
}

#reply-ckeditor {
  height: 300px !important;
  min-height: 300px !important;

  :deep(.ck-editor) {
    height: 100% !important;
    min-height: 300px !important;

    .ck-editor__top {
      height: auto !important;
    }

    .ck-editor__main {
      height: calc(100% - 40px) !important; // 减去工具栏高度
      min-height: 260px !important;

      .ck-content {
        height: 100% !important;
        min-height: 260px !important;

        // 确保内容区域有足够空间
        padding: 15px !important;
        box-sizing: border-box !important;

        // 确保内容区域有清晰的焦点状态
        &:focus {
          outline: 1px solid #4096ff !important;
          outline-offset: -1px !important;
        }
      }
    }
  }
}

// 添加全局样式覆盖以确保高度生效
:deep(.ck-editor__editable) {
  min-height: 400px !important;
  height: 450px !important;
  max-height: 500px !important;

  // 确保可编辑区域有足够的内边距
  padding: 20px !important;

  // 确保可编辑区域可以正常滚动
  overflow-y: auto !important;

  // 确保可编辑区域有清晰的焦点状态
  &:focus {
    outline: 1px solid #4096ff !important;
    outline-offset: -1px !important;
  }
}

:deep(.ck-content) {
  min-height: 400px !important;
  height: 450px !important;
  max-height: 500px !important;

  // 确保内容区域有足够空间
  padding: 20px !important;
  box-sizing: border-box !important;

  // 确保内容区域有清晰的焦点状态
  &:focus {
    outline: 1px solid #4096ff !important;
    outline-offset: -1px !important;
  }
}

// 确保CKEditor容器有清晰的边框
#ckeditor, #reply-ckeditor {
  border: 1px solid #ddd;
  border-radius: 4px;

  &:focus-within {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.2);
  }
}

// 限制图片显示大小的样式
.feedback-description, .reply-content {
  :deep(img) {
    max-width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    margin: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
}

// 图片上传按钮样式
:deep(.ck-file-dialog-button) {
  display: none;
}

</style>
