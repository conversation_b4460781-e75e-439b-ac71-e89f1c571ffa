<template>
    <!-- 修改广告组出价 -->
    <a-modal v-model:visible="modalVisible" :width="940" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-tabs v-model:active-key="activeName" @change="handleTabChange">
            <a-tab-pane key="bid" title="出价(1)">
                <a-form ref="formRef" :model="form" :auto-label-width="true" style="height: 420px;overflow: auto;">
                    <a-form-item field="newBudget" label="新日计算">
                        <a-radio-group v-model="form.newBudget" type="button">
                            <a-radio value="1">固定值</a-radio>
                            <a-radio value="2">增加</a-radio>
                            <a-radio value="3">减少</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item field="nums">
                        <a-select v-model="form.budgetType" placeholder="请选择" style="margin-right: 12px;width: 100px;">
                            <a-option value="number">数值</a-option>
                            <a-option v-if="form.newBudget !== '1'" value="percent">百分比</a-option>
                        </a-select>
                        <a-input-number v-model="form.nums" :min="0" style="width: 200px;"/>
                    </a-form-item>
                    <a-form-item field="tableData">
                        <a-table
                            :columns="bidColumns"
                            :data="form.tableData"
                            :hoverable="true"
                            sticky-header
                            :table-layout-fixed="true"
                            :column-resizable="true"
                            :pagination="true"
                        >
                            <template #newDailyBid="{ rowIndex }">
                                <a-input v-model="form.tableData[rowIndex].newDailyBid" allow-clear>
                                    <template #append>
                                        USD
                                    </template>
                                </a-input>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="target" title="广告花费回报目标(1)">
                <a-form ref="formRef" :model="form" :auto-label-width="true" style="height: 420px;overflow: auto;">
                    <a-form-item field="newBudget" label="新日计算">
                        <a-radio-group v-model="form.newBudget" type="button" disabled>
                            <a-radio value="1">固定值</a-radio>
                            <a-radio value="2">增加</a-radio>
                            <a-radio value="3">减少</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item field="nums">
                        <a-select v-model="form.budgetType" placeholder="请选择" style="margin-right: 12px;width: 100px;" disabled>
                            <a-option value="number">数值</a-option>
                            <a-option v-if="form.newBudget !== '1'" value="percent">百分比</a-option>
                        </a-select>
                        <a-input-number v-model="form.nums" :min="0" style="width: 200px;" disabled/>
                    </a-form-item>
                    <a-form-item field="tableData">
                        <a-table
                            :columns="targetColumns"
                            :data="form.tableData"
                            :hoverable="true"
                            sticky-header
                            :table-layout-fixed="true"
                            :column-resizable="true"
                            :pagination="true"
                        >
                            <template #newDailyBid="{ rowIndex }">
                                <a-input v-model="form.tableData[rowIndex].newDailyBid" allow-clear>
                                    <template #append>
                                        USD
                                    </template>
                                </a-input>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-form>
            </a-tab-pane>
            <a-tab-pane key="unable" title="不能修改(1)">
                <a-table
                    :columns="unableColumns"
                    :data="unableTableData"
                    :hoverable="true"
                    sticky-header
                    :table-layout-fixed="true"
                    :column-resizable="true"
                    :pagination="false"
                    style="height: 420px;"
                >
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <div class="footer">
            <div class="footer-left">
                <a-checkbox>
                    设置为定时任务
                    <a-tooltip placement="top" content="设置定时任务后，可在任务中心的批量编辑中找到相关任务">
                        <icon-question-circle style="margin-left: 8px;"/>
                    </a-tooltip>
                </a-checkbox>
            </div>
            <div class="footer-right">
                <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
                <a-button type="primary" :loading="loading" @click="saveData">
                    保存
                </a-button>
            </div>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('修改出价')
const activeName = ref('bid')
const form = reactive({
    newBudget:'1',
    budgetType:'number',
    nums:undefined,
    tableData:[
        {
            adsetName:'10093_MTG_ColorBlock_20241011',
            bidStrategy:'单次成效费用目标',
            newDailyBid:'',
        },
    ]
})
const bidColumns = ref<any>([
    { title: '广告组名称', dataIndex: 'adsetName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '竞价策略', dataIndex: 'bidStrategy',minWidth:180 },
    { title: '日出价', dataIndex: 'dailyBid',minWidth:180 },
    { title: '新日出价', dataIndex: 'newDailyBid',slotName:'newDailyBid',minWidth:254 },
])
const targetColumns = ref<any>([
    { title: '广告组名称', dataIndex: 'adsetName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '竞价策略', dataIndex: 'bidStrategy',minWidth:180 },
    { title: '广告花费回报目标', dataIndex: 'targetType',minWidth:180 },
    { title: '新广告花费回报目标', dataIndex: 'newTargetType',slotName:'newTargetType',minWidth:254 },
])
const unableColumns = ref<any>([
    { title: '广告组名称', dataIndex: 'adsetName',minWidth:180,ellipsis: true,tooltip: true },
    { title: '不可修改原因', dataIndex: 'reason',minWidth:180 },
])
const unableTableData = ref<any>([
    {
        adsetName:'10093_MTG_ColorBlock_20241011',
        reason:'广告单元包含已暂停的广告',
    },
])
const formRef = ref()

const emits = defineEmits(['updateData']);

const handleTabChange = (key) => {
    if (key === 'target') {
        form.newBudget = '1'
        form.budgetType = 'number'
        form.nums = undefined
    }
}
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.tip{
    white-space: pre;
    text-align: right;
}
</style>