<template>
  <!-- 事件多选 -->
  <a-trigger
      v-model:popup-visible="triggerVisible"
      trigger="click"
      :unmount-on-close="false"
      position="bl"
      :update-at-scroll="true"
      @click="handleTriggerVisible"
  >
    <div class="filter-btn">
      <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
        <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
          <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
        </svg>
      </svg>
      <span class="filter-label">事件（{{ checkDataNumber }}）</span>
    </div>
    <template #content>
      <div class="select-panel">
        <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
          <a-input v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div v-if="searchInput===''" class="list-container">
          <div class="list-category">
            <div class="category-container">
              <div
                  v-for="(item, index) in panelList"
                  :key="index"
                  class="category-item"
                  :class="{ 'item-active': activeIndex === index,}"
                  @click="categoryChange(item.categoryName, index)"
              >{{ item.categoryName }}
              </div
              >
            </div>
            <a-button>管理分组</a-button>
          </div>
          <div class="item-list">
            <div ref="selectListRef" class="select-list">
              <a-list ref="virtualList" :virtual-list-props="{ height: 350 }" :data="virtualPanelList" @scroll="handleScroll">
                <template #item="{ item, index }">
                  <a-list-item :key="index">
                    <div class="list-content">
                      <!-- 分类全选 暂未实现 -->
                      <!-- <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <a-checkbox :model-value="item.checkedAll" :indeterminate="item.indeterminate" :disabled="!virtualPanelList[index+1]?.eventName" @change="itemCheckAll(item,index,$event)">
                              <template #checkbox="{ checked }">
                                  <div :class="{ 'custom-checkbox-card-checked': checked }" style="display: flex;align-items: center;">
                                      <div className="custom-checkbox-card-mask">
                                          <div className="custom-checkbox-card-mask-dot" />
                                      </div>
                                      <icon-star v-if="index == 0" class="star" />
                                      <span>{{ item.categoryName }}</span>
                                  </div>
                              </template>
                          </a-checkbox>
                      </div> -->
                      <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                        <icon-star v-if="index == 0" class="star"/>
                        <span>{{ item.categoryName }}</span>
                      </div>
                      <div v-if="index == 0 && !panelList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                        点击选项右侧
                        <icon-star/>
                        添加收藏
                      </div>
                      <a-checkbox-group
                          v-if="item.eventDisplayName" v-model="checkData" :max="20" direction="vertical"
                          @change="checkBoxChange">
                        <a-checkbox :value="item">
                          <div
                              class="list-box"
                              :class="{ 'list-active': item.eventDisplayName == selectObject.eventDisplayName, }"
                              style="height: 30px; width:100% ">
                            <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" :popup-offset="20">
                              <div style="display: flex;align-items: center;">
                                <span class="desc">{{ item.eventDisplayName }}</span>
                                <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                  <div class="icon" @click.prevent="starClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                                <a-tooltip v-else content="取消收藏" position="top">
                                  <div class="isStar" @click.prevent="cancelClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                              </div>
                              <template #content>
                                <div class="trigger-box">
                                  <div class="card-header">
                                    <div class="card-header-container">
                                      <div class="header-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
                                          <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                            <path
                                                d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                          </svg>
                                        </svg>
                                      </div>
                                      <div class="header-title">
                                        <div class="name">{{ item.eventDisplayName }}</div>
                                      </div>
                                      <!-- <div class="header-type">自定义事件</div> -->
                                      <div v-if="item.eventType === 1" class="header-type">预置事件</div>
                                      <div v-if="item.eventType === 2" class="header-type">自定义事件</div>
                                      <div v-if="item.eventType === 3" class="header-type">虚拟事件</div>
                                    </div>
                                    <div class="title-sub">{{ item.eventName }}</div>
                                  </div>
                                  <div class="card-desc">
                                    <div class="span-desc">{{ item.eventNote }}</div>
                                  </div>
                                  <div class="card-footer">
                                    <div></div>
                                    <div class="action">
                                      <a-tooltip content="前往事件详情" position="top">
                                        <div class="action-icon" @click="toEventDetail">
                                          <icon-launch />
                                        </div>
                                      </a-tooltip>
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                        </a-checkbox>
                      </a-checkbox-group>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="item-list">
            <div ref="selectListRef" class="select-list">
              <a-list ref="virtualList" :virtual-list-props="{ height: 350 }" :data="filteredLists" @scroll="handleScroll">
                <template #item="{ item, index }">
                  <a-list-item :key="index">
                    <div class="list-content">
                      <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                        <icon-star v-if="index == 0" class="star"/>
                        <span>{{ item.categoryName }}</span>
                      </div>
                      <div v-if="index == 0 && !panelList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                        点击选项右侧
                        <icon-star/>
                        添加收藏
                      </div>
                      <a-checkbox-group v-if="item.eventDisplayName" v-model="checkData" style="display: flex" :max="20" direction="vertical" @change="checkBoxChange">
                        <a-checkbox :value="item" style="display: flex;width: 100%">
                          <div
                              class="list-box"
                              :class="{ 'list-active': item.eventDisplayName == selectObject.eventDisplayName, }"
                              style="display: flex;height: 30px; width:100% ">
                            <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" :popup-offset="20">
                              <div style="display: flex;align-items: center;justify-content: space-between">
                                <span class="desc" style="width: 240px;">{{ item.eventDisplayName }}</span>
                                <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                  <div class="icon" style="padding-left: 30px" @click.prevent="starClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                                <a-tooltip v-else content="取消收藏" position="top">
                                  <div class="isStar" style="padding-left: 30px" @click.prevent="cancelClick(item)">
                                    <icon-star/>
                                  </div>
                                </a-tooltip>
                              </div>
                              <template #content>
                                <div class="trigger-box">
                                  <div class="card-header">
                                    <div class="card-header-container">
                                      <div class="header-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                             viewBox="0 0 24 24" fill="currentColor">
                                          <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                            <path
                                                d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                          </svg>
                                        </svg>
                                      </div>
                                      <div class="header-title">
                                        <div class="name">{{ item.eventDisplayName }}</div>
                                      </div>
                                      <!-- <div class="header-type">自定义事件</div> -->
                                      <div v-if="item.eventType === 1" class="header-type">预置事件</div>
                                      <div v-if="item.eventType === 2" class="header-type">自定义事件</div>
                                      <div v-if="item.eventType === 3" class="header-type">虚拟事件</div>
                                    </div>
                                    <div class="title-sub">{{ item.eventName }}</div>
                                  </div>
                                  <div class="card-desc">
                                    <div class="span-desc">{{ item.eventNote }}</div>
                                  </div>
                                  <div class="card-footer">
                                    <div></div>
                                    <div class="action">
                                      <a-tooltip content="前往事件详情" position="top">
                                        <div class="action-icon" @click="toEventDetail">
                                          <icon-launch />
                                        </div>
                                      </a-tooltip>
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                        </a-checkbox>
                      </a-checkbox-group>
                    </div>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </div>
        <div class="list-footer">
          <div class="footer-alert"></div>
          <div class="foot-btn">
            <a-button class="cancel" @click="cancelApply">取消</a-button>
            <a-button type="primary" @click="checkedApply">应用</a-button>
          </div>
        </div>
      </div>
    </template>
  </a-trigger>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {analyseStore} from '@/store';
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

const analyseData = analyseStore();

const triggerVisible = ref(false);
const emits = defineEmits(['analysisIndexChange']);
const handleTriggerVisible = () => {
  triggerVisible.value = !triggerVisible.value;
};
const props = defineProps({
  panelData: {
    type: Object,
    default() {
      return {};
    },
  },
  clickAll: {
    type: Boolean,
    default: true
  }
});
// 前往事件详情
const toEventDetail = () =>{
  router.push({name:ROUTE_NAME.SETTING_ANALYSE_EVENT})
}
const selectObject = ref({
  eventName: props.panelData.eventName || '',
  eventDisplayName: props.panelData.eventName || '',
  type: props.panelData.type || 'event', // 事件 | 指标
  eventCode:props.panelData.eventCode || '',
  eventType: null, // 事件类型
  eventNote: '' // 备注
});
watch(
    () => props.panelData,
    (newVal) => {
      selectObject.value.eventName = newVal.eventName || '';
      selectObject.value.eventDisplayName = newVal.eventDisplayName || '';
      selectObject.value.eventType = newVal.eventType || '';
      selectObject.value.eventCode = newVal.eventCode || '';
    },
    {immediate: true}
);
const searchInput = ref('')
//   原 事件数组
const panelList = ref<any[]>([
  {
    categoryName: '收藏的事件',
    itemData: [],
  },
  {
    categoryName: '基础事件',
    itemData: [],
  }
]);

interface EventItem {
  eventName: string;
  eventDisplayName: string;
  eventType: number;
  sub: string;
  eventNote: string;
  isCollect: boolean;
}

interface CategoryItem {
  categoryName: string;
  indeterminate: boolean
  checkedAll: boolean
}

type PanelItem = CategoryItem | EventItem;

// 虚拟列表  事件数组
const virtualPanelList = ref<PanelItem[]>([]);
const getVirtualPanelList = (arr) => {
  virtualPanelList.value = arr.flatMap((category) => {
    const categoryItem: CategoryItem = {
      categoryName: category.categoryName,
      indeterminate: false,
      checkedAll: false
    };
    return [categoryItem, ...category.itemData];
  });
}
watch(virtualPanelList, (newVal) => {
})
const filteredLists = computed(() => {
  const str = searchInput.value.trim();
  const plist = virtualPanelList.value.map(item => {
    return {
      ...item,
      type: 'event'
    }
  })
  return plist.filter(item => {
    return item?.eventDisplayName?.includes(str) || item?.eventName?.includes(str) || item.categoryName
  });
});
watch(filteredLists, (newVal) => {
})
const getEventList = async () => {
  const data = analyseData.$state.evtLists.length > 0 ? analyseData.$state.evtLists : await analyseData.fetchEvtInfo();
  if (data && data.length) {
    panelList.value[1].itemData = data.map((item, index) => {
      return {
        eventCode:item.eventCode,
        eventName: item.eventName,
        eventDisplayName:item.eventDisplayName,
        eventType: item.eventType,
        eventNote: item.eventNote,
        isCollect: false,
      };
    });
  }
  getVirtualPanelList(panelList.value)
};
getEventList();

//   事件容器，左右点击滚动效果联动
const activeIndex = ref(0)
const virtualList = ref(null);
const selectListRef = ref(null);
const categoryChange = async (categoryName: string, index: number) => {
  await nextTick()
  activeIndex.value = index;
  const targetIndex = virtualPanelList.value.findIndex(item => item.categoryName === categoryName);
  if (targetIndex !== -1) {
    virtualList.value?.scrollIntoView({index: targetIndex});
  }
};
const handleScroll = () => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (!scrollContainer) return;

  const {scrollTop} = scrollContainer; // 获取 scrollTop
  const items = scrollContainer.querySelectorAll('.list-name');
  if (items && items.length > 0) {
    let sIndex = 0
    items.forEach((item, index) => {
      if (scrollTop + 60 >= item.offsetTop) {
        panelList.value.forEach((el, num) => {
          if (item.textContent?.trim() === el.categoryName) {
            sIndex = num
          }
        })
      }
    })
    activeIndex.value = sIndex
  }
};

onMounted(() => {
  // 初始化 categoryName
  if (panelList.value && panelList.value.length > 0) {
    activeIndex.value = 0
  }

  // 添加滚动事件监听
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    scrollContainer.addEventListener('scroll', handleScroll);
  }
});
// 在组件卸载时移除事件监听
onUnmounted(() => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    scrollContainer.removeEventListener('scroll', handleScroll);
  }
});
//   事件点击
const listChange = (el: object) => {
  selectObject.value.eventName = el.eventName;
  selectObject.value.eventNote = el.eventNote;
  selectObject.value.type = 'event';
  triggerVisible.value = false;
  emits('analysisIndexChange', selectObject.value);
};
// 事件收藏
const starClick = (item: object) => {
  const existingItem = panelList.value[0].itemData.find(
      (el) => el.eventCode === item.eventCode
  );
  if (!existingItem) {
    item.isCollect = true; // 标记为已收藏
    panelList.value[0].itemData.push(item); // 仅在不存在时添加
    getVirtualPanelList(panelList.value)
  }
};
// 取消事件收藏
const cancelClick = (item: object) => {
  item.isCollect = false;
  panelList.value[0].itemData = panelList.value[0].itemData.filter(
      (el: any) => el.eventCode !== item.eventCode
  );
  getVirtualPanelList(panelList.value)
};


const indeterminate = ref(false)
const checkedAll = ref(false)
const checkData = ref<any>([])

const handleChangeAll = (value: any) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    checkData.value = panelList.value.map(item => item.itemData).flat()
  } else {
    checkedAll.value = false;
    checkData.value = []
  }
}

const checkBoxChange = (values: any) => {
  if (values.length === panelList.value.map(item => item.itemData).flat().length) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
}
const uniqueArray = ref()
const oldCheckData = ref([])
const checkDataNumber = ref(0)
const checkedApply = () => {
  uniqueArray.value = checkData.value.filter(item => {
    return new Set(checkData.value.map(a => a.eventName)).has(item.eventName);
  });
  oldCheckData.value = checkData.value
  checkDataNumber.value = uniqueArray.value.length
  // checkData.value=[]
  triggerVisible.value = false
}
const cancelApply = () => {
  checkData.value = []

  triggerVisible.value = false

}
// const itemCheckAll = (item,index,e) => {
//     virtualPanelList.value[index].indeterminate = false;
//     if (e) {
//         virtualPanelList.value[index].checkedAll = true;
//         const targetIndex = panelList.value.findIndex(el => el.categoryName === item.categoryName);
//         checkData.value = panelList.value[targetIndex].itemData.map(item => item)
//         indeterminate.value = true;
//     } else {
//         virtualPanelList.value[index].checkedAll = false;
//         checkData.value = []
//     }
// }
defineExpose({oldCheckData, checkDataNumber, checkData})
</script>

<style scoped lang="less">
.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.select-panel {
  position: relative;
  width: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-bottom);
}

.header-select-all {
  padding: 4px 12px 0;
  line-height: 32px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.list-container {
  position: relative;
  display: flex;
  flex-direction: row;

  .list-category {
    height: 350px;
    position: relative;
    flex-shrink: 0;
    width: 100px;
    padding: 10px 0 0 10px;
    border-right: 1px solid var(--tant-border-color-border1-1);

    .category-container {
      height: calc(100% - 32px);
      overflow-y: auto;

      .category-item {
        width: 100%;
        margin-bottom: 10px;
        padding: 2px 0 2px 2px;
        color: var(--tant-text-gray-color-text1-3);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item-active {
        font-weight: 500;
        border-right: 2px solid var(--tant-primary-color-primary-default);
      }
    }

    .arco-btn {
      margin-left: -10px;
      width: 100px;
      border-radius: 0 0 0 4px;
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-2);
      text-shadow: none;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: 1px solid var(--tant-border-color-border1-1);
      box-shadow: none;
      border-left: none;
      border-bottom: none;
    }
  }

  .item-list {
    flex: 1 1;

    .select-list {
      // position: relative;
      //height: 350px;
      width: 260px;
      //overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0 0 0 8px;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }

    .list-group {
      width: 100%;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    .favorite {
      height: 30px;
      width: 100%;
      padding-left: 18px;
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
    }

    .list-name {
      border-top: 1px solid var(--tant-border-color-border1-1);
      margin-top: 8px;
      //   padding: 8px 20px 0 8px;
      padding: 8px 20px 0;

      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      height: 45px;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    :deep(.arco-checkbox) {
      margin-right: 0;
      padding: 0 12px;
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);

        .icon {
          opacity: 100;
          pointer-events: all;
        }
      }
    }

    .list-box {
      //   margin: 0 8px;
      // padding: 0 12px;
      // border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 28px;
      cursor: pointer;

      .desc {
        display: inline-block;
        width: 170px;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .icon {
        opacity: 0;
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
      }

      .icon:hover {
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }

      // &:hover {
      //   background-color: var(--tant-secondary-color-secondary-fill-hover);
      //   .icon {
      //     opacity: 100;
      //     pointer-events: all;
      //   }
      // }
      .isStar {
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
        opacity: 100;
        pointer-events: all;
      }
    }

    .list-active {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }

    .list-metric {
      position: relative;
      height: 350px;
      width: 360px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }
  }
}

.list-footer {
  min-height: 48px;
  padding: 8px 12px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-top: 1px solid var(--tant-border-color-border1-1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  text-align: right;
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);

  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;

      .header-icon {
        margin-top: 2px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }

      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }

  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;

    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}

// .custom-checkbox-card-mask {
//     margin-right: 8px;
//     height: 14px;
//     width: 14px;
//     display: inline-flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 2px;
//     border: 1px solid var(--color-border-2);
//     box-sizing: border-box;
//     }

// .custom-checkbox-card-mask-dot {
//   width: 8px;
//   height: 8px;
//   border-radius: 2px;
// }
// .custom-checkbox-card-checked .custom-checkbox-card-mask-dot {
//   background-color: rgb(var(--primary-6));
// }
.item-list {
  flex: 1 1;

  .select-list {

    will-change: transform;
    direction: ltr;

    :deep(.arco-list-item) {
      padding: 0 0 0 8px !important;
      border: none;
    }

    :deep(.arco-list-bordered) {
      border: none;
    }
  }

  .list-group {
    width: 100%;
    padding: 8px 20px 0;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 500;
    font-size: 14px;
    background-color: #fff;

    .star {
      margin-right: 5px;
      color: var(--tant-decorative-yellow-color-decorative6-3) !important;
    }
  }

  .favorite {
    height: 30px;
    width: 100%;
    padding-left: 18px;
    color: var(--tant-text-gray-color-text1-4);
    font-weight: 400;
  }

  .list-name {
    border-top: 1px solid var(--tant-border-color-border1-1);
    margin-top: 8px;
    //   padding: 8px 20px 0 8px;
    padding: 8px 20px 0;

    color: var(--tant-text-gray-color-text1-3);
    font-weight: 500;
    font-size: 14px;
    background-color: #fff;
    height: 45px;

    .star {
      margin-right: 5px;
      color: var(--tant-decorative-yellow-color-decorative6-3) !important;
    }
  }

  :deep(.arco-checkbox) {
    margin-right: 0;
    padding: 0 12px;
    border-radius: 4px;

    &:hover {
      background-color: var(--tant-secondary-color-secondary-fill-hover);

      .icon {
        opacity: 100;
        pointer-events: all;
      }
    }
  }

  .list-box {
    //   margin: 0 8px;
    // padding: 0 12px;
    // border-radius: 4px;
    display: flex;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    line-height: 28px;
    cursor: pointer;

    .desc1 {
      //display: flex;
      width: 100px;
      margin-right: 10px;
      color: inherit !important;
      vertical-align: top;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .desc2 {
      //display: flex;
      width: 130px;
      margin-right: 10px;
      color: inherit !important;
      vertical-align: top;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .icon {
      opacity: 0;
      pointer-events: none;
      float: right;
      display: flex;
      align-items: end;
      margin-left: auto;
      margin-right: 4px;
      line-height: 28px;
      cursor: pointer;
    }

    .icon:hover {
      color: var(--tant-decorative-yellow-color-decorative6-3) !important;
    }

    // &:hover {
    //   background-color: var(--tant-secondary-color-secondary-fill-hover);
    //   .icon {
    //     opacity: 100;
    //     pointer-events: all;
    //   }
    // }
    .isStar {
      display: flex;
      align-items: end;
      margin-left: auto;
      float: right;
      margin-right: 4px;
      line-height: 28px;
      cursor: pointer;
      color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      opacity: 100;
      pointer-events: all;
    }
  }

  .list-active {
    background-color: var(--tant-secondary-color-secondary-fill-hover);
  }

  .list-metric {
    position: relative;
    height: 350px;
    width: 360px;
    overflow: auto;
    will-change: transform;
    direction: ltr;

    :deep(.arco-list-item) {
      padding: 0;
      border: none;
    }

    :deep(.arco-list-bordered) {
      border: none;
    }
  }
}
</style>