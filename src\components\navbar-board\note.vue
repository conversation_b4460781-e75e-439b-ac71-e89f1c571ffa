<template>
  <div style="width: 550px">
    <div id="ckeditor" style="width: auto;padding-bottom:8px">
      <ckeditor
          ref="editorRef"
          v-model="editorDesign"
          :editor="editor"
          :config="editorConfig"
          placeholder="输入文本内容"
          @destroy="onEditorDestroy"
      ></ckeditor>
    </div>
    <div class="noteFooter">
      <a-button class="cancelButton" @click="cancel">取消</a-button>
      <a-button class="applyButton" @click="addNote">
        <div v-if="props.date">修改</div>
        <div v-else>添加</div>
      </a-button>
    </div>

  </div>
</template>

<script setup>
import {ref, reactive, watch} from 'vue'
import {
  ClassicEditor,
  Bold,
  Essentials,
  Italic,
  Undo,
  Font,
  Heading,
  Link,
  List,
  Alignment, ImageBlock, ImageInline, ImageUpload, FontBackgroundColor,
} from 'ckeditor5';
// eslint-disable-next-line import/extensions
import '@ckeditor/ckeditor5-build-classic/build/translations/zh-cn.js'

const props=defineProps({
  date:{
    type:Object,
    default:null
  },
  year:{
    type:String
  },
  month:{
    type:String
  },
  day:{
    type:Number
  },

})
const editor = ClassicEditor

const editorDesign = ref('')
if(props.date){
  editorDesign.value=props.date.content
}
const editorConfig = reactive({
  language: 'zh-cn',
  plugins: [Bold, Essentials, Italic, Undo, Font, Heading, Link,List,Alignment,ImageBlock,ImageInline,ImageUpload,FontBackgroundColor],
  toolbar: {
    items: ['undo', 'redo', '|', 'heading', 'bold', 'italic', 'fontColor', 'fontBackgroundColor', '|', 'alignment','bulletedList','imageUpload','numberedList', 'link',],
    shouldNotGroupWhenFull: true
  },
})

const emit = defineEmits([ 'dataSent','updateLog','cancelNote'])

const editorRef = ref(null)
const addNote=()=>{
  if(props.date){
    emit('updateLog',editorDesign.value,props.date,props.year,props.month,props.day)
  }else{
    emit('dataSent',editorDesign.value )
  }

}
const cancel=()=>{
  if(props.date){
    emit('cancelNote',false)
  }else{
    emit('dataSent',false)
  }

}
</script>

<style lang="less">
#ckeditor {
  .ck-editor__editable {
    min-height: 400px;
    max-height: 500px;
  }
}
.noteFooter{
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 48px;
  .cancelButton{
    color: var(--tant-text-gray-color-text1-2);
    background-color: transparent;
    border: none;
    margin-left: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 5px 16px;
    font: var(--tant-body-font-body-regular);
    text-transform: capitalize;
    border-radius: var(--tant-border-radius-medium);
    box-shadow: unset;
    &:hover{
      background-color: var(--tant-secondary-color-secondary-transp-hover);
      color: var(--tant-text-gray-color-text1-2);
      border: none;
  }
  }
  .applyButton{
    color: var(--tant-white-white-100);
    background-color: var(--tant-primary-color-primary-default);
    border: none;
    margin-left: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 5px 16px;
    font: var(--tant-body-font-body-regular);
    text-transform: capitalize;
    border-radius: var(--tant-border-radius-medium);
    box-shadow: unset;
    &:hover{
      background-color: var(--tant-primary-color-primary-hover);
      color: var(--tant-white-white-100);
      border: none;
  }
  }
}
</style>

