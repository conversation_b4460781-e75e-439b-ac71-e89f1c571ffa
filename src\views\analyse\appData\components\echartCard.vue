<template>
  <div ref="cardRef" class="card">
    <div class="drag-allow-from card-title">
      <div class="title">
        <div style="width: 240px;">
          <a-select
              v-model:model-value="params.indicator"
              v-model:popupVisible="selectOpen"
              multiple
              :max-tag-count="1"
              allow-search
              allow-clear
              :tag-nowrap="true"
              :trigger-props="{
                autoFitPopupMinWidth: true,
                updateAtScroll: true,
              }"
              placeholder="选择指标"
              @clear="getData"
              @remove="getData"
              @popup-visible-change="popupVisibleChange">
            <template #label="{ data }">
              <span>指标-{{ data?.label }}</span>
            </template>
            <!-- 已选中数据显示区域 -->
            <template #header>
              <div v-if="selectedIndicators.length > 0" class="selected-indicators">
                <div class="selected-header">已选择 ({{ selectedIndicators.length }})</div>
                <div class="selected-list">
                  <div v-for="item in selectedIndicators" :key="item.code" class="selected-item">
                    <a-checkbox 
                      :model-value="true" 
                      @change="removeIndicator(item.code)"
                      class="selected-checkbox">
                      {{ item.displayName }}
                    </a-checkbox>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="selectOptionLoaded">
              <a-optgroup v-for="(item,index) in props.indicatorList" :key="index" :label="item.type">
                <a-option v-for="el in item.items" :key="el.code" :value="el.code">{{ el.displayName }}</a-option>
              </a-optgroup>
            </template>
            <template v-else>
              <a-optgroup v-for="(item,index) in props.indicatorList" :key="index" :label="item.type">
                <a-option v-for="el in item.items.filter(i => params.indicator.includes(i.code))" :key="el.code" :value="el.code">{{ el.displayName }}</a-option>
              </a-optgroup>
            </template>
          </a-select>
        </div>
      </div>
      <div class="operation">
        <a-dropdown :popup-max-height="false" :popup-container="cardRef">
          <a-tooltip content="更多">
            <div class="operation-icon">
              <icon-more class="icon"/>
            </div>
          </a-tooltip>
          <template #content>
            <a-doption @click="resize('middle')">中图
              <icon-check v-if="size == 'middle'"/>
            </a-doption>
            <a-doption @click="resize('large')">大图
              <icon-check v-if="size == 'large'"/>
            </a-doption>
            <a-doption @click="deleteItem">删除</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <div class="card-filter">
      <a-dropdown :popup-offset="-40" :popup-max-height="false" :popup-container="cardRef">
          <span class="filter">
            <img class="option-icon" :src="selectedOption.icon" alt=""/>
            {{ selectedOption.label }}
          </span>
        <template #content>
          <a-doption v-for="(item,index) in chartOptions" :key="index" :value="item.value" @click="selectOption(item)">
            <div class="option-item">
              <img class="option-icon" :src="item.icon" alt="">
              {{ item.label }}
            </div>
          </a-doption>
        </template>
      </a-dropdown>
    </div>
    <div class="card-content">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <Chart v-if="totalNum>0" :option="chartOption"/>
        <a-empty v-else>
          无搜索结果<br/>请尝试不同的筛选组合
        </a-empty>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from 'vue';
import useChartOption from "@/hooks/chart-option";
import {isEqual} from "lodash"
import {TimeParticleSize} from '@/api/enum';
import {queryComprehensiveReportData, reportQueryResponseData} from "@/api/analyse/analyse";

interface Props {

  /**
   * 报表信息
   */
  report: any;
  groupList: any;
  indicatorList: any;
  filterParams: any
}

const props = defineProps<Props>();
const params = reactive({
  indicator: [],
  chartType: ''
})
const lastParams = ref({
  indicator: [],
})
const emits = defineEmits(['resize', 'deleteItem', 'updateParams']);
const selectedOption = reactive({icon: '/icon/trend-chart.svg', label: '折线图', value: 'trend'})
const chartOptions = [
  {icon: '/icon/trend-chart.svg', label: '折线图', value: 'trend'},
  {icon: '/icon/stack-chart.svg', label: '堆积图', value: 'stack'},
  {icon: '/icon/distribution-chart.svg', label: '柱状图', value: 'distribution'},
  {icon: '/icon/pie-chart.svg', label: '饼图', value: 'pie'}
]
const cardRef = ref<HTMLElement>();
const ySeries = ref<any>([])
const xAxis = ref<any>([])
const legendData = ref<any>([])
const percentNameList = ref<any>([])
const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
// 选择器状态
const selectOpen = ref<boolean>(false)
const selectOptionLoaded = ref(false);
watch(selectOpen, (v) => {
  if (v && !selectOptionLoaded.value) selectOptionLoaded.value = true;
});
const formatValue = (
    num: number | string | null | undefined,
    displayType?: { type: string; decimalNum: number }
): string => {
    if (num === null || num === undefined || num === '' || Number.isNaN(Number(num))) {
        return '';
    }
    const n = Number(num);
    if (Number.isNaN(n)) return '';
    let formattedValue = '';
    if (displayType?.type) {
        switch (displayType.type) {
            case 'default':
                formattedValue = formatNumber(n.toFixed(displayType.decimalNum));
                break;
            case 'percent':
                formattedValue = formatNumber((n * 100).toFixed(displayType.decimalNum));
                break;
            default:
                formattedValue = formatNumber(n.toFixed(2));
        }
    } else {
        formattedValue = formatNumber(n.toFixed(2));
    }
    return formattedValue;
};
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '2%',
      right: '0%',
      top: '40',
      bottom: '20',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      bottom: '0',
      type: 'scroll', // 设置图例为滚动类型
    },
    //
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        show: selectedOption.value !== 'pie'
      },
      axisLabel: {
        color: '#8E8E8E'
      },
      axisTick: {
        show: false
      },
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: selectedOption.value === 'pie' ? 'item' : 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      // formatter: selectedOption.value=== 'pie' ? '{b}: {c}({d}%)' : ''
      formatter: (params) => {

        if (selectedOption.value === 'distribution') {
          // 柱状图
          const seriesInfo = params.map((param, index) => {
            const matchingY = percentNameList.value.find(y => y.displayName === param.axisValueLabel);
            // 使用 formatValue 统一格式化
            const value = formatValue(
              typeof param.value === 'number' ? param.value : Number(param.value),
              matchingY?.displayType || { type: 'default', decimalNum: 2 }
            );
            return `
              <div style="display: flex; align-items: center; margin-bottom: 5px;">
                <span style="flex-shrink: 0;">${param.marker}</span>
                <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                <span style="flex-shrink: 0;">${value}${matchingY?.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
              </div>
            `;
          }).join('');
          const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${params[0].axisValue}</div>
              ${seriesInfo}
            </div>
          `;
          return tooltipHtml;
        }
        if (selectedOption.value === 'pie') {
          return `${params.name}: ${formatNumber(params.value)} (${params.percent}%)`;
        }
        // 线图
        const seriesInfo = params.map((param, index) => {
          const matchingY = ySeries.value.find(y => y.name === param.seriesName);
          if (matchingY) {
            return `
                    <div style="display: flex; align-items: center; margin-bottom: 5px;">
                      <span style="flex-shrink: 0;">${param.marker}</span>
                      <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                      <span style="flex-shrink: 0;">${formatValue(param.value, matchingY.displayType)}${matchingY.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
                    </div>
                `;
          }
          return '';
        }).join('');
        const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${params[0].axisValue}</div>
              ${seriesInfo}
            </div>
          `;
        return tooltipHtml;
      }
    },
    series: ySeries.value,
  };
});

interface AggregatesItem {
  aggregateType?: string;
  objectDisplayName?: string;
  objectName?: string;
  objectId?: string;
  objectType?: string;
}

interface IndicatorsItem {
  code?: string;
  name?: string;
  displayName?: string;
  type?: string;
}

const requestId = ref('')
const chartData = ref()
const totalNum = ref(0)
const loading = ref(false)
const timeOut = ref()


// 数组相加
const calculateSum = (array: any) => {
  return Number(array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0));
};

// 时间格式化x轴数据
const formatterXData = () => {
  const timeFormat = 'D1'
  const formatMap = {
    'm1': 'YYYY-MM-DD HH:mm:ss',
    'm5': 'YYYY-MM-DD HH:mm:ss',
    'm10': 'YYYY-MM-DD HH:mm:ss',
    'h1': 'YYYY-MM-DD HH:mm',
    'D1': 'YYYY-MM-DD',
    'W1': 'YYYY-MM-DD',
    'M1': 'YYYY/MM',
    'Q1': 'YYYY-M',
    'Y1': 'YYYY',
  };
  const format = formatMap[timeFormat] || null;

  if (format) {
    const list = chartData.value?.x?.map(item => item) || [];
    const suffixMap = {
      'W1': '当周',
      'M1': '月',
      'Q1': '季度',
      'Y1': '年'
    };

    if (timeFormat in suffixMap) {
      xAxis.value = list.map(item => `${item}${suffixMap[timeFormat]}`);
    } else {
      xAxis.value = list;
    }
  } else {
    xAxis.value = chartData.value.x;
  }
}
const freshData = () => {

  formatterXData()
  const data = [];
  const yLength = chartData.value?.y?.length

  chartData.value?.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if (yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {
        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        displayType: item.displayType,
        values: el.values,
      });
    });
  });

  legendData.value = data.map(item => item.name);
  const commonSeries = data.map(item => {
    return {
      name: item.name,
      data: item.values,
      displayType: item.displayType,
      type: 'line',
    }
  });
  if (selectedOption.value === 'stack') {
    ySeries.value = data.map(series => {
      return {
        name: series.name,
        data: series.values,
        displayType: series.displayType,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        }
      }
    });
  } else {
    ySeries.value = commonSeries;
  }
}
const freshDistributionData = () => {
  const groups = chartData.value?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  const data = [];
  chartData.value.y.forEach(item => {
    item.yData.forEach(el => {
      data.push({
        displayName: item.displayName,
        displayType: item.displayType,
        groupName: el.group.map(value => value === null ? 'null' : value).join(','),
        values: calculateSum(el.values),
        valuesCompared: el.valuesCompared ? el.valuesCompared.map(val => calculateSum(val)) : []
      });
    });
  });
  const uniqueGroups = [...new Set(data.map(item => item.groupName))];
  // 为每个 groupName 创建一个 series 对象
  ySeries.value = uniqueGroups.map(groupName => {
    const groupData = data.filter(item => item.groupName === groupName);
    return {
      name: groupName,
      data: groupData.map(item => item.values),
      type: 'bar',
      barWidth: 40,
      large: true
    };
  });
  percentNameList.value = Array.from(
      new Map(
          data.filter(item => item.displayType?.type && item.displayType.type === 'percent') // 筛选出 type 为 percent 的项
              .map(item => [item.displayName, { displayName: item.displayName, displayType: item.displayType }]) // 创建以 displayName 为键的 Map
      ).values() // 获取 Map 的值
  );
  xAxis.value = chartData.value?.y.map(item => item.displayName)
  legendData.value = groups.map(item => item);
}
const freshPieData = () => {
  xAxis.value = []
  const pieData = [] as any
  chartData.value.y.forEach(item => {
    const seriesData = item.yData.map(el => {
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      return {
        name: processedGroup.join('.') || '',
        value: calculateSum(el.values),
      };
    });

    // 切割前 groupNum 项，后面的项统一为 '其它'
    const displayedData = seriesData.slice(0, 5);
    const otherData = seriesData.slice(5);

    if (otherData.length > 0) {
      const otherTotalValue = otherData.reduce((sum, el) => sum + el.value, 0);
      displayedData.push({
        name: '其它',
        value: otherTotalValue,
      });
    }

    pieData.push({
      displayName: item.displayName,
      seriesData: displayedData,
    });
  });
  legendData.value = pieData.map(item => item.displayName);
  ySeries.value = [
    {
      type: 'pie',
      radius: ['35%', '58%'],
      center: ['50%', '50%'],
      bottom: '20',
      label: {
        formatter: '{b}'
      },
      labelLine: {
        normal: {
          show: true
        }
      },
      data: pieData[0].seriesData
    }
  ]
}
const computedData = () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
    }
  }, 20000)
  const aggregatesList = [] as any
  props.groupList.forEach(item => {
    props.filterParams.groupCodes.forEach(el => {
      if(item.code === el){
        aggregatesList.push(
          {
            aggregateType:"operation",
            objectDisplayName:item.displayName,
            objectName:item.name,
            objectId:item.code,
            objectType:"string"
          }
        )
      }
    })
  })
  const indicatorsItem: IndicatorsItem[] = []
  const indexList = props.indicatorList.flatMap(item => item.items)
  indexList.forEach(item => {
    if (params.indicator.includes(item.code)) {
      indicatorsItem.push({ displayName: item.displayName, name: item.name, code: item.code, type: 'operation' })
      
    }
  })
  const filterData = {
    logicalOperation: 'and',
    filters: [
      {
        calcuSymbol: "eq",
        displayName: "国家",
        filterType: "operation",
        logicalOperation: "and",
        objectId: "operation_attribute202412210011",
        objectName: "country",
        objectType: "string",
        subFilters: [],
        thresholds: props.filterParams.country
      }
    ]
  }
  const queryParams = {
    aggregates: aggregatesList,
    indicators: indicatorsItem,
    filter: props.filterParams?.country?.length ? filterData : {},
    subject: "application",
    timeParticleSize: TimeParticleSize.DAY1,
    dateRange: props.filterParams.date,
    teamCodes: props.filterParams.teamCodes,
  }
  requestId.value = queryComprehensiveReportData(queryParams);
}

// watch(() => props.filterParams, (newValue) => {
//   if (newValue && params.indicator?.length) {
//     computedData()
//   }
// }, {deep: true});
const getData = () => {
  if ( params.indicator?.length) {
    computedData()
  }
  emits('updateParams', props.report.i, params)
}
const popupVisibleChange = (v) => {
  if(!v){
    // 检查参数是否有变化
    const hasParamsChanged = !isEqual(
      { indicator: params.indicator},
      { indicator: lastParams.value.indicator}
    );
    if (hasParamsChanged) {
      // 更新上一次参数值
      lastParams.value = {
        indicator: [...params.indicator],
      };
      getData()
    }
  }
}
const handleChartOptions = () => {
  if ( params?.indicator?.length) {
    switch (selectedOption.value) {
      case 'trend':
        freshData()
        break;
      case 'stack':
        freshData()
        break;
      case 'distribution':
        freshDistributionData()
        break;
      case 'pie':
        freshPieData()
        break;
      default:
        break;
    }
  }

}
watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (timeOut.value) {
    clearTimeout(timeOut.value);
  }
  chartData.value = newData.result
  totalNum.value = newData?.result?.totalNum
  handleChartOptions()
  loading.value = false
})
const init = () => {
  params.chartType = props.report.configParams?.chartType || ''
  params.indicator = props.report.configParams?.indicator || []
  chartOptions.forEach(item => {
    if (item.value === params.chartType) {
      selectedOption.value = item.value
      selectedOption.icon = item.icon
      selectedOption.label = item.label
    }
  })
  // 初始化 lastParams 的值，确保 indicator 是数组
  lastParams.value = {
    indicator: Array.isArray(params.indicator) ? [...params.indicator] : []
  };
  if (params?.indicator?.length) {
    computedData()
  }
}
init()
const selectOption = (item) => {
  selectedOption.icon = item.icon
  selectedOption.value = item.value
  selectedOption.label = item.label
  params.chartType = item.value
  emits('updateParams', props.report.i, params)
  handleChartOptions()
}
const size = computed(() => {
  if (props.report.w === 5) {
    return 'small';
  }
  if (props.report.w === 10) {
    return 'middle';
  }
  return 'large'; // 默认返回
});
// 删除
const deleteItem = () => {
  const id = props.report.i
  emits('deleteItem', id)
}
// 小中大图
const resize = (sizeType: string) => {
  emits('resize', sizeType, props.report?.objectCode);
};

defineExpose({
  init
})

// 计算已选中的指标
const selectedIndicators = computed(() => {
  const selected = [];
  props.indicatorList?.forEach(group => {
    group.items?.forEach(item => {
      if (params.indicator.includes(item.code)) {
        selected.push(item);
      }
    });
  });
  return selected;
});

// 移除指标
const removeIndicator = (code: string) => {
  const index = params.indicator.indexOf(code);
  if (index > -1) {
    params.indicator.splice(index, 1);
    getData();
  }
};
</script>

<style scoped lang="less">
.card {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  transition: all 0.3s;
  background: #fff;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 46px;
  margin-bottom: 0;
  padding: 16px 24px 8px;
  line-height: 20px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom: none;
  cursor: grab;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  .title {
    display: flex;
    flex: 1;
  }

  .operation {
    display: none;
    align-items: center;
    transition: opacity 0.3s;
  }

  .operation-icon {
    padding: 4px;
    display: inline-block;
    margin-left: 8px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
    }

    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }
}

.card-filter {
  display: flex;
  justify-content: flex-end;
  padding: 0 12px;

  .filter {
    opacity: 0;
    display: flex;
    min-height: 24px;
    padding: 2px 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all .3s;
    cursor: pointer;

    &:hover {
      background-color: var(--tant-disabled-color-disabled-fill);
    }
  }
}

.card:hover .card-title .operation {
  display: flex;
  unicode-bidi: isolate;
}

.card:hover {
  box-shadow: var(--tant-medium-shadow-medium-overall);
  border: 1px solid var(--tant-primary-color-primary-default);
  border-radius: 4px;
}

.card:hover .card-filter .filter {
  opacity: 1;
}

.card-content {
  width: 100%;
  height: calc(100% - 74px);
  padding: 0 24px 16px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  box-sizing: border-box;
  overflow: hidden;
}

.option-icon {
  margin-right: 4px;
}

.option-item {
  display: flex;
  align-items: center;
}
:deep(.arco-select-view-tag) {
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}

.selected-indicators {
  padding: 8px 12px;
  .selected-header {
    color: var(--color-text-3);
    margin-bottom: 6px;
  }
  
  .selected-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  
  .selected-item {
    .selected-checkbox {
      :deep(.arco-checkbox-label) {
        padding-left: 4px;
      }
    }
  }
}
</style>
