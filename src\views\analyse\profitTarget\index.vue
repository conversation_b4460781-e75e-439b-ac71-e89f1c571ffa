

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <a-year-picker v-model="year" style="width: 200px;" @change="yearChange"/>
        </div>
      </div>
    </div>
    <div class="page-body">
        <a-tabs default-active-key="1">
            <a-tab-pane key="1" title="项目组">
               <projectTable ref="teamRef"/>
            </a-tab-pane>
            <a-tab-pane key="2" title="应用">
                <appTable ref="appRef"/>
            </a-tab-pane>
        </a-tabs>
    </div>
  </div>

</template>

<script setup lang="ts">
import {computed, reactive, ref, provide} from "vue";
import {useRoute} from 'vue-router';
import projectTable from "./projectTable.vue";
import appTable from "./appTable.vue";

const route = useRoute();
const year = ref('2025')

provide('year', year);

const teamRef = ref()
const appRef = ref()
const yearChange = () => {
  teamRef.value.init()
  appRef.value.init()
}
</script>

<style scoped lang="less">

</style>