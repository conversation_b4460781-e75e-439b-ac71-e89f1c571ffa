
<template>
    <!-- Excel编辑 -->
    <a-modal v-model:visible="modalVisible" :width="625" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <div style="font-size: 12px;">您可以通过上传Excel文件修改{{typeName}}名称</div>
        <div class="form-item">
            <div class="index-title">
                <span class="index">1</span>
                <span>下载文件，支持导出前1万行</span>
            </div>
            <div class="btn">
                <a-button>导出{{typeName}}</a-button>
            </div>
        </div>
        <div class="form-item">
            <div class="index-title">
                <span class="index">2</span>
                <span>上传文件，请确保提供{{typeName}}ID和{{typeName}}名称，提交任务后系统会自动过滤掉没有改名的对象</span>
            </div>
            <div class="btn">
                <a-upload draggable action="/" accept=".xlsx,.xls" :limit="1"/>
            </div>
        </div>
        <div class="tip">提示：点击确定后，可以在任务列表中查看执行结果</div>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('Excel编辑')
const typeName = ref('')


const emits = defineEmits(['updateData']);
const openModal = async (type:string) => {
    if(type === 'adset'){
       typeName.value = '广告组'
    }else if(type === 'ad'){
       typeName.value = '广告'
    }
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.form-item{
    position: relative;
    line-height: 32px;
    font-size: 12px;
    margin-bottom: 24px;
    .index{
        margin-right: 5px;
        border-radius: 50%;
        color: #0b75ff;
        background: #e6f1ff;
        font-size: 12px;
        width: 18px;
        height: 18px;
        display: inline-block;
        text-align: center;
        line-height: 18px;
    }
    .btn{
        margin-left: 25px;
    }
}
.tip{
    color: #999;
    margin: 0 25px 0;
    font-size: 12px;
}
</style>