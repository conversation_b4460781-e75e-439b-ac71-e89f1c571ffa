<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app />
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.dateRange" disabled-after-today @date-pick="datePick" />
        </div>
        <div class="filter-item">
          <dateSet :time-particle="{ timeParticleSize: params.timeParticleSize, firstDayDfWeek: params.firstDayDfWeek }" :no-mh="true" @time-change="timeChange" />
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-tabs v-model:active-key="activeView">
        <template #extra>
          <a-tooltip content="快捷输出展示份额数据，选中粒度后默认分组不可选，只支持天、周时间粒度"> 
            <span><icon-question-circle style="padding-right: 2px;"/>展示份额时间粒度：</span>
          </a-tooltip>
          <a-select
            v-model:model-value="impressionShareTimeParticleSize"
            :style="{ borderRadius: '4px', width: '80px', marginRight: '12px' }"
            allow-clear
            :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
            @change="shareTimeParticleSizeChange"
          >
            <a-option v-for="item in shareTimeList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
          </a-select>
          <span>分组：</span>
          <a-select
            v-model:model-value="groups"
            :style="{ borderRadius: '4px', width: '280px', marginRight: '12px' }"
            :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
            placeholder="选择分组"
            multiple
            :max-tag-count="2"
            :tag-nowrap="true"
            :scrollbar="true"
            :disabled="impressionShareTimeParticleSize !== ''"
            @clear="refreshMultiple"
            @remove="refreshMultiple"
            @popup-visible-change="groupChange"
          >
            <a-option v-for="item in groupList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
          </a-select>
          
          <a-button :loading="asaLoding" style="margin-right: 12px" size="small" @click="openCustomColumn">
            <template #icon>
              <icon-menu />
            </template>
            <template #default>自定义列</template>
          </a-button>
          <a-tooltip content="导出">
            <a-button size="small" @click="exportXlsx">
              <template #icon>
                <icon-download />
              </template>
            </a-button>
          </a-tooltip>
        </template>
        <a-tab-pane key="ASA" title="ASA">
          <AsaTable ref="asaTableRef" :loading="asaLoding" :table-data="asaResult.tableData" :groups="asaResult.groups" :indicators-formula="asaResult.indicatorsFormula" :indicator-list="indicatorList" :custom-column-data="customColumnData" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <!-- 自定义列 -->
    <CustomColumn ref="customColumnRef" :tab-name="activeView" :indicator-list="indicatorList" :selected-indicators="customColumnData" @save="updateSelectedIndicators" />
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch} from 'vue';
import {useRoute} from 'vue-router';
import DatePicker from '@/components/date-picker/index.vue';
import {getAsaAggregation, getAsaGroup, getMediaAnalysisViewDetail} from '@/api/analyse/api';
import {isEqual} from 'lodash';
import {usePageFilter} from '@/utils/filterConfigUtil';
import selectApp from '@/components/selected-game-app/index.vue';
import selectCountryList from '@/components/selected-country-list/index.vue';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {LocalStorageEventBus} from '@/types/event-bus';
import {TimeParticleSize} from '@/api/enum';
import dateSet from '@/views/analyse/components/dateSet.vue';
import AsaTable from './components/asaTable.vue';
import CustomColumn from './components/CustomColumn.vue';

const route = useRoute();
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const appIdRef = useSessionStorage('app-id', '');
  const params = reactive({
    appId: appIdRef,
    country: ['global'],
    dateRange: {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天',
    },
    timeParticleSize: TimeParticleSize.TOTAL,
    firstDayDfWeek: null,
  });
  const impressionShareTimeParticleSize = ref('');
  const shareTimeList = [
    { name: '天', code: TimeParticleSize.DAY1 },
    { name: '周', code: TimeParticleSize.WEEK1 },
  ];
  const groups = ref<any[]>([]);
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(params);
  // 添加用于存储上一次复选参数值的响应式变量
  const lastParams = ref<{
    country: string[];
    groups: string[];
  }>({
    country: ['global'],
    groups: [],
  });
  const indicatorList = ref<any>([]);
  const groupList = ref<any>([]);
  const activeView = ref('ASA');
  const asaLoding = ref(false);
  const asaResult = ref({
    tableData: [],
    groups: [],
    indicatorsFormula: {},
  });
  const customColumnData = ref<any>([]);

  interface objMap {
    [key: string]: string;
  }

  // 转换函数
  const transformMapList = (item: objMap): { code: string; name: string }[] => {
    return Object.entries(item).map(([code, name]) => ({
      code,
      name,
    }));
  };
  // 获取asa数据
  const getAsaData = async () => {
    asaLoding.value = true;
    const parmasObj = {
      ...params,
      impressionShareTimeParticleSize:impressionShareTimeParticleSize.value,
      groups: groups.value,
    }
    try {
      const res = await getAsaAggregation(parmasObj);
      asaResult.value.tableData = res.data;
      asaResult.value.groups = transformMapList(res.groups);
      indicatorList.value = transformMapList(res.indicators);
      asaResult.value.indicatorsFormula = res.indicatorsFormula;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      asaLoding.value = false;
    }
  };
  const getTabData = async () => { 
    if(activeView.value === 'ASA'){
      getAsaData();
    }
  };
  // 获取分组项数据
  const getGroupList = async () => {
    try {
      const res = await getAsaGroup();
      groupList.value = res;
    } catch (error) {
      console.error('获取分组数据失败:', error);
    }
  };
  // 获取保存视图指标
  const getViewIndicators = async () => { 
    try{
      const res = await getMediaAnalysisViewDetail(activeView.value);
      customColumnData.value = res?.indicator || [];
    } catch (error) {
      console.error('获取保存视图指标失败:', error);
    }
  };
  const init = async () => {
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, { ...savedConfig });
    }
    if(params.timeParticleSize === TimeParticleSize.DAY1 || params.timeParticleSize === TimeParticleSize.WEEK1){
      impressionShareTimeParticleSize.value = params.timeParticleSize;
    }
    await Promise.all([getViewIndicators(), getGroupList()]);
    groups.value = groupList.value.filter((item) => item.default).map((item) => item.code);
    lastParams.value = {
      groups: [...groups.value] as string[],
      country: [...params.country],
    };
    getTabData();
  };
  onMounted(() => {
    init();
  });
  const refreshMultiple = () => {
    // 检查参数是否有变化
    const hasParamsChanged = !isEqual({ country: params.country, groups: groups.value }, { country: lastParams.value.country, groups: lastParams.value.groups });
    // 只有当参数发生变化时才执行刷新
    if (hasParamsChanged) {
      // 更新上一次参数值
      lastParams.value = {
        country: [...params.country],
        groups: [...groups.value],
      };
      getTabData();
    }
  };
  const datePick = (date: any) => {
    params.dateRange = date;
    getTabData();
  };
  const countryChange = (v: any) => {
    params.country = v;
    refreshMultiple();
  };
  const groupChange = (v: any) => {
    if (!v) {
      refreshMultiple();
    }
  };
  // 在 timeChange 函数中添加联动逻辑
  const timeChange = (v: any) => {
    params.timeParticleSize = v.timeParticleSize;
    params.firstDayDfWeek = v.firstDayDfWeek;
    
    // 联动逻辑：当天、周选项时保持同步，其他情况置空impressionShareTimeParticleSize
    if (params.timeParticleSize === TimeParticleSize.DAY1 || params.timeParticleSize === TimeParticleSize.WEEK1) {
      impressionShareTimeParticleSize.value = params.timeParticleSize;
    } else {
      impressionShareTimeParticleSize.value = '';
    }
    
    getTabData();
  };
  const shareTimeParticleSizeChange = (v: any) => {
    if(v){
      params.timeParticleSize = v
    }
    getTabData();
  };
  const customColumnRef = ref();
  const openCustomColumn = () => {
    customColumnRef.value.openModal();
  };
  const updateSelectedIndicators = (selectedIndicators: any) => {
    customColumnData.value = selectedIndicators;
  };

  const asaTableRef = ref();
  const exportXlsx = () => {
    asaTableRef.value.exportXlsx(params.dateRange);
  };
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id') {
      params.appId = value;
      getTabData();
    }
  });
</script>

<style scoped lang="less"></style>
