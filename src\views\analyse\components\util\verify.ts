export const analyseParamsVerify = (params) => {
  const verifyFilter = (item: any) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };

  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取需要验证的列表
  const firstList = params.indicators
      .filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.indicators
      .flatMap(item =>
          item.eventList
              .filter(event => event.filter?.filters?.length > 0)
              .map(event => event.filter.filters)
      );
  // 添加对 userFilter 的校验
  const userFilterList = params?.userFilter?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.userFilter?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
          ...(item.filter?.filters || []),
          ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userFilter?.userCondition?.filters || [];
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList) &&
      verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};
export const verifyLeastIndicator = (params) => {
  const indicators = params.indicators || [];
  if (indicators.length === 0) {
    return false;
  }
  // 检查是否至少有一个指标的 ignoreCalc 不为 true
  return indicators.some(indicator => indicator.ignoreCalc !== true);
}

// 移除查询参数中的enum_list 属性
export const removeEnumList = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(item => removeEnumList(item));
  }
  if (typeof obj === 'object' && obj !== null) {
    const newObj = {...obj};
    delete newObj.enumList;
    Object.keys(newObj).forEach(key => {
      newObj[key] = removeEnumList(newObj[key]);
    });
    return newObj;
  }
  return obj;
};

// 获取默认事件的方法：优先获取 sdk_app_open，否则取第一项
export const getDefaultObj = (list: any[]) => {
  if (!Array.isArray(list) || list.length === 0) {
    return null;
  }
  const sdkAppOpenEvent = list.find(item => item.name === 'sdk_app_open');
  return sdkAppOpenEvent || list[0];
};
