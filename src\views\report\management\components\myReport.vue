<script setup lang="ts">
import {computed, onMounted, reactive, ref, watch} from "vue";
import {getManageMyList} from "@/api/analyse/api";
import {ReportAnalyseModel, ReportCreateWayParam} from "@/api/analyse/type";

const props = defineProps({
  reportValue: String,
})
const columns = [
  {
    title: '报表名称',
    dataIndex: 'name',
    ellipsis: true,
    slotName: 's-name',
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    fixed: 'left',
    minWidth: 150,
  },
  {
    title: '备注',
    width: 150,
    ellipsis: true,
    dataIndex: 'description',
    slotName: 's-description',
  },
  {
    title: '类型',
    width: 130,
    dataIndex: 'model',
    titleSlotName: 't-model',
    ellipsis: true,
    slotName: 's-model',
  },
  {
    title: '创建方式',
    dataIndex: 'createType',
    ellipsis: true,
    slotName: 's-createType',
    titleSlotName: 't-createType',
    width: 130,
  },
  {
    title: '应用看板数',
    dataIndex: 'dashboardArrLength',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    width: 150,
  },
  {
    title: '应用看板',
    dataIndex: 'dashboardArr',
    ellipsis: true,
    slotName: 's-dashboardArr',
    titleSlotName: 't-dashboardArr',
    width: 200,
  },
  {
    title: '最后更新人',
    dataIndex: 'lastUpdater',
    ellipsis: true,
    width: 180,
    titleSlotName: 't-lastUpdater',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    ellipsis: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
    },
    width: 180,

  },
  {
    title: '操作',
    dataIndex: 'operate',
    slotName: 's-operate',
    ellipsis: "true",
    minWidth: 180,
    fixed: 'right',

  },
]
const model = [
  {value: ReportAnalyseModel.EVENT, label: '事件分析'},
  {value: ReportAnalyseModel.RETENTION, label: '留存分析'},
  {value: ReportAnalyseModel.FUNNEL, label: '漏斗分析'},
  {value: ReportAnalyseModel.INTERVAL, label: '间隔分析'},
  {value: ReportAnalyseModel.SCATTER, label: '分布分析'},
  {value: ReportAnalyseModel.TRACE, label: '路径分析'},
  {value: ReportAnalyseModel.PROPERTY, label: '属性分析'},
  {value: ReportAnalyseModel.ATTRIBUTION, label: '归因分析'},
  {value: ReportAnalyseModel.CUSTOM, label: 'SQL分析'},
  {value: ReportAnalyseModel.RANKLIST, label: '排行榜'},
  {value: ReportAnalyseModel.HEATMAP, label: '热力图'},
]
const createType = [
  {value: ReportCreateWayParam.MANUAL, label: '手动创建'},
  {value: ReportCreateWayParam.COPY, label: '看板复制'},
  {value: ReportCreateWayParam.IMPORT, label: '配置导入'},
  {value: ReportCreateWayParam.TEMPLATE, label: '模版创建'},
]

const selectedKeys = (v: string[]) => {
  console.log(v)
}
const selectedAllKeys = (v: boolean) => {
  console.log(v)
}
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const tableDate = ref();
const modelCheckDate = ref()
const typeCheckDate = ref()
const arrCheckDate = ref<string[]>([])
const updateCheckDate = ref<string[]>([])
const ArrCheckedAll = ref()
const UpdateCheckedAll = ref()
const arrSearchQuery = ref('')
const updateSearchQuery = ref('')
const VisibleModel = ref(false)
const VisibleType = ref(false)
const VisibleArr = ref(false)
const VisibleUpdate = ref(false)
const scrollbar = ref(true);
const isChanged = ref(false);
const isTypeChanged = ref(false)
const isArrChanged = ref(false)
const isUpdateChanged = ref(false)
const pageNumber = ref(1)
const arrIndeterminate = ref(false)
const updateIndeterminate = ref(false)
const originalNames = ref<string[]>([])
const dashboardArr = ref([])
const updateUser = ref([])

function Match(searchTerm: string, text: string): boolean {
  let i = 0;
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();
  for (const char of lowerSearchTerm) {
    i = lowerText.indexOf(char, i);
    if (i === -1) {
      return false;
    }
    i++;
  }
  return true;
}

const filteredTableData = computed(() => {
  if (!tableDate.value || tableDate.value.length === 0) return []; // 避免为空时报错
  const filterItems = (items: any) => {
    return items.flatMap((item: any) => {

      const isMatch =
          Match(props.reportValue, item.name)
      const isArr =
          arrCheckDate.value?.length === 0 ||
          arrCheckDate.value.some(checkValue =>
              item.dashboardArr.some((value: any) => value.code.includes(checkValue.code))
          )
      const isModel =
          modelCheckDate.value === undefined ||
          modelCheckDate.value === item.model;

      const isType =
          typeCheckDate.value === undefined ||
          typeCheckDate.value === item.createType;

      // 递归检查子项

      // 确保父节点和子节点都符合条件
      if (isModel && isType && isArr && isMatch) {
        return [{...item}];
      }
      return [];
    });
  };

  return filterItems(tableDate.value);
});
const showNameTableData = ref([filteredTableData.value]);
const EditName = ref(Array(showNameTableData.value.length).fill(false));
const VisibleName = ref(Array(showNameTableData.value.length).fill(false));
const EditRemake = ref(Array(showNameTableData.value.length).fill(false));
const VisibleRemake = ref(Array(showNameTableData.value.length).fill(false));
const modelLabel = computed(() => {
  return (index: number) => {
    const item = model.find(
        option => option.value === filteredTableData.value[index]?.model
    );
    return item ? item.label : 'Unknown'; // 确保返回值安全
  }
})
const typeLabel = computed(() => {
  return (index: number) => {
    const item = createType.find(
        option => option.value === filteredTableData.value[index].createType
    );
    return item ? item.label : 'Unknown'; // 确保返回值安全
  }
})

// filter函数
function filterItems(options: string[], searchQuery: string) {
  const query = searchQuery.trim().toLowerCase();
  if (!query) {
    return options;
  }

  const queries = query.split(/\s+/); // 按空格分割成多个关键词

  return options.filter(item => {
    // eslint-disable-next-line no-shadow
    return queries.every(query => {
      const regex = new RegExp(query.split('').join('.*'), 'i');
      return regex.test(item.name.toLowerCase());
    });
  });
}

const arrFilteredItems = computed(() => filterItems(dashboardArr.value, arrSearchQuery.value));
const updateFilteredItems = computed(() => filterItems(updateUser.value, updateSearchQuery.value));

const ArrHandleChange = () => {
  if (arrCheckDate.value?.length === arrFilteredItems.value.length) {
    ArrCheckedAll.value = true
    arrIndeterminate.value = false;
  } else if (arrCheckDate.value?.length === 0) {
    ArrCheckedAll.value = false
    arrIndeterminate.value = false;
  } else {
    ArrCheckedAll.value = false
    arrIndeterminate.value = true;
  }
  return arrCheckDate.value
}
const UpdateHandleChange = () => {
  if (updateCheckDate.value?.length === updateFilteredItems.value.length) {
    UpdateCheckedAll.value = true
    updateIndeterminate.value = false;
  } else if (updateCheckDate.value?.length === 0) {
    UpdateCheckedAll.value = false
    updateIndeterminate.value = false;
  } else {
    UpdateCheckedAll.value = false
    updateIndeterminate.value = true;
  }
  return updateCheckDate.value
}
/**
 * 页数相关config
 */
const pageOptions = [10, 20, 50, 100]

const scroll = {y: 'calc(100vh - 260px)', x: 1000};
const totalPages = computed(() => {
  return filteredTableData.value.length;
});
const pagination = reactive({
  total: totalPages.value,  // 使用过滤后的数据长度
  PageSize: pageOptions[0],
  showPageSize: true,
  pageSizeOptions: pageOptions,
  showJumper: true,
  hideOnSinglePage: true,
  autoAdjust: true,
});
const pageChange = (v: number) => {
  pageNumber.value = v
}
const pageIndex = computed(() => {
  return (pageNumber.value - 1) * pagination.PageSize
});
// over
const mouseEnter = (e) => {
  const dateIndex = filteredTableData.value.findIndex((item: any) => {
    return item.code === e.code
  }) + pageIndex.value
  EditName.value = EditName.value.map((value, index) => {
    return index === dateIndex
  })
  EditRemake.value = EditRemake.value.map((value, index) => index === dateIndex)
}
const mouseLeave = (e) => {
  const index = filteredTableData.value.findIndex((item: any) => {
    return item.code === e.code
  }) + pageIndex.value
  EditName.value[index] = false;
  EditRemake.value[index] = false;
}
// name
const showVisibleName = (index: number, status: boolean) => {
  originalNames.value[index] = showNameTableData.value[index].name;
  VisibleName.value[index] = status
}
const SaveEditName = (index: number, value: string, code: string) => {
  showNameTableData.value[index].name = value; // 保存编辑的值
  // saveVirtualEventList({code, note: showNameTableData.value[index].name})
  //     .catch((e) => {
  //       Message.error("重命名失败！", e);
  //     });
  VisibleName.value[index] = false; // 退出编辑模式
};
const CloseEditName = (index: number) => {
  showNameTableData.value[index].name = originalNames.value[index];
  VisibleName.value[index] = false;
}

const showRemakeVisible = (index: number, status: boolean) => {
  originalNames.value[index] = showNameTableData.value[index].description;
  VisibleRemake.value[index] = status
}
const remakeSaveEdit = (index: number, value: string, code: string) => {
  showNameTableData.value[index].description = value; // 保存编辑的值
  // saveVirtualEventList({code, note: showNameTableData.value[index].description})
  //     .catch((e) => {
  //       Message.error("重命名失败！", e);
  //     });
  VisibleRemake.value[index] = false; // 退出编辑模式
};
const remakeCloseEdit = (index: number) => {
  showNameTableData.value[index].description = originalNames.value[index];
  VisibleRemake.value[index] = false;
}
const chooseModel = (v: string) => {
  modelCheckDate.value = v
  VisibleModel.value = false
}
const chooseModelAll = () => {
  modelCheckDate.value = undefined
  VisibleModel.value = false
}
const chooseType = (v: string) => {
  typeCheckDate.value = v
  VisibleType.value = false
}
const chooseTypeAll = () => {
  typeCheckDate.value = undefined
  VisibleType.value = false
}

const modelIcon = computed(() => {
  return !isChanged.value ? '/icon/filter.svg' : '/icon/filterBlue.svg'
})
const typeIcon = computed(() => {
  return !isTypeChanged.value ? '/icon/filter.svg' : '/icon/filterBlue.svg'
})
const arrIcon = computed(() => {
  return !isArrChanged.value ? '/icon/filter.svg' : '/icon/filterBlue.svg'
})
const updateIcon = computed(() => {
  return !isUpdateChanged.value ? '/icon/filter.svg' : '/icon/filterBlue.svg'
})
const ArrShowOnly = ref()
const UpdateShowOnly = ref()
const showIcon = (index: number, status: boolean) => {
  ArrShowOnly.value[index] = status;
};
const showUpdateIcon = (index: number, status: boolean) => {
  UpdateShowOnly.value[index] = status;
};
const arrHandleOnly = (values: any) => {
  arrCheckDate.value = [values];
  return arrCheckDate.value;
}
const updateHandleOnly = (values: any) => {
  updateCheckDate.value = [values];
  return updateCheckDate.value;
}
const arrHandleChangeAll = () => {
  arrIndeterminate.value = false;
  if (ArrCheckedAll.value) {
    arrCheckDate.value = [...arrFilteredItems.value]
  } else {
    arrCheckDate.value = []
  }
  return arrCheckDate.value
}
const updateHandleChangeAll = () => {
  updateIndeterminate.value = false;
  if (UpdateCheckedAll.value) {
    updateCheckDate.value = [...updateFilteredItems.value]
  } else {
    updateCheckDate.value = []
  }
  return updateCheckDate.value
}
watch(filteredTableData, (newDataType) => {
  showNameTableData.value = newDataType;
  EditName.value = Array(showNameTableData.value.length).fill(false)
  EditRemake.value = Array(showNameTableData.value.length).fill(false)
  VisibleName.value = Array(showNameTableData.value.length).fill(false)
  VisibleRemake.value = Array(showNameTableData.value.length).fill(false)

})
watch(modelCheckDate, (newTypeCheckDate) => {
  isChanged.value = newTypeCheckDate
});
watch(typeCheckDate, (newTypeCheckDate) => {
  isTypeChanged.value = newTypeCheckDate
});
watch(arrCheckDate, () => {
  isArrChanged.value = arrCheckDate.value?.length > 0;
});
watch(updateCheckDate, () => {
  isUpdateChanged.value = updateCheckDate.value?.length > 0;
});
watch(totalPages, (newTotal) => {
  pagination.total = newTotal;
});
onMounted(() => {
  getManageMyList().then((res) => {
    tableDate.value = structuredClone(res)
    dashboardArr.value = tableDate.value.flatMap((item: any) => item.dashboardArr).filter(
        (item: any, index: any, self: any) => self.findIndex((i: any) => i.code === item.code) === index
    );
    updateUser.value = tableDate.value
        .map((item: any) => item.lastUpdater)
        .filter((item: any, index: any, self: any) =>
            self.findIndex((i: any) => i === item) === index && item !== '-'
        );
    ArrShowOnly.value = Array(dashboardArr.value.length).fill(false)
    UpdateShowOnly.value = Array(updateUser.value.length).fill(false)

  })
})

</script>

<template id="aaa">
  <a-table
      row-key="code"
      :columns="columns"
      :data="filteredTableData"
      :bordered="false"
      :hoverable="true"
      sticky-header
      :row-selection="rowSelection"
      :table-layout-fixed="true"
      :filter-icon-align-left="true"
      :scroll="scroll"
      :column-resizable="true"
      :scrollbar="scrollbar"
      :pagination="pagination"
      @page-change="pageChange"
      @select="selectedKeys"
      @select-all="selectedAllKeys"
      @cell-mouse-enter="mouseEnter"
      @cell-mouse-leave="mouseLeave">
    <template #s-name="{ record,rowIndex}">
      <div class="name-cell">
        <template v-if="VisibleName[rowIndex+pageIndex]">
          <a-input
              v-model:model-value="showNameTableData[rowIndex+pageIndex].name"
              style="width: 80px"/>
          <button class="InputClose" @click="CloseEditName(rowIndex+pageIndex)">
            <icon-close/>
          </button>
          <button
              class="InputSave"
              @click="SaveEditName(rowIndex+pageIndex,showNameTableData[rowIndex+pageIndex].name,record.eventCode)">
            <icon-check/>
          </button>
        </template>
        <template v-else>
          <div style="display: flex;align-items: center;">
            <a class="href-name" @click="gotoItem('detailPage',record)">{{ record.name }}</a>
          </div>
          <transition name="fade">
            <button v-show="EditName[rowIndex+pageIndex]" class="edit-btn">
              <icon-edit @click="showVisibleName(rowIndex+pageIndex, true)"/>
            </button>
          </transition>
        </template>
      </div>
    </template>
    <template #s-description="{record,rowIndex}">
      <div class="name-cell">
        <template v-if="VisibleRemake[rowIndex+pageIndex]">
          <a-input
              v-model:model-value="showNameTableData[rowIndex+pageIndex].description"
              style="width: 80px"/>
          <button class="InputClose" @click="remakeCloseEdit(rowIndex+pageIndex)">
            <icon-close/>
          </button>
          <button
              class="InputSave"
              @click="remakeSaveEdit(rowIndex+pageIndex,showNameTableData[rowIndex+pageIndex].description,record.description)">
            <icon-check/>
          </button>
        </template>
        <template v-else>
          <template v-if="!record.description">
            <button v-show="EditRemake[rowIndex+pageIndex]" class="edit-btn">
              <icon-plus @click="showRemakeVisible(rowIndex+pageIndex, true)"/>
            </button>
          </template>
          <template v-else>
            <a-tooltip :content="record.description">
              <div class="remake-flow ">
                {{ record.description }}
              </div>
            </a-tooltip>
            <transition name="fade">
              <button v-show="EditRemake[rowIndex+pageIndex]" class="edit-btn">
                <icon-edit @click="showRemakeVisible(rowIndex+pageIndex, true)"/>
              </button>
            </transition>
          </template>


        </template>
      </div>
    </template>
    <template #t-model>
      <div style="display: flex;align-items: center;justify-content: center;gap: 3px">
        <div>类型</div>
        <a-trigger
            v-model:popup-visible="VisibleModel"
            trigger="click">
          <div class="state-tip">
            <img :src="modelIcon" alt="" style="padding-bottom: 3px;">
          </div>
          <template #content>
            <div class="dropdown" style="height:256px ;width:auto;overflow-y: auto">
              <div class="dropdown-item" @click="chooseModelAll">
                <div class="dropdown-label">
                  全部
                </div>
              </div>
              <div v-for="item in model" :key="item.value" class="dropdown-item">
                <div class="dropdown-label" @click="chooseModel(item.value)">
                  {{ item.label }}
                </div>
              </div>
            </div>
          </template>
        </a-trigger>
      </div>
    </template>
    <template #s-model="{rowIndex}">
      <div>{{ modelLabel(rowIndex + pageIndex) }}</div>
    </template>
    <template #t-createType>
      <div style="display: flex;align-items: center;justify-content: center;gap: 3px">
        <div>创建方式</div>
        <a-trigger
            v-model:popup-visible="VisibleType"
            trigger="click">
          <div class="state-tip">
            <img :src="typeIcon" alt="" style="padding-bottom: 3px;">
          </div>
          <template #content>
            <div class="dropdown" style="height:auto ;width:auto;overflow-y: auto">
              <div class="dropdown-item" @click="chooseTypeAll">
                <div class="dropdown-label">
                  全部
                </div>
              </div>
              <div v-for="item in createType" :key="item.value" class="dropdown-item">
                <div class="dropdown-label" @click="chooseType(item.value)">
                  {{ item.label }}
                </div>
              </div>
            </div>
          </template>
        </a-trigger>
      </div>
    </template>
    <template #s-createType="{rowIndex}">
      <div>{{ typeLabel(rowIndex + pageIndex) }}</div>
    </template>
    <template #t-dashboardArr>
      <div style="display: flex;align-items: center;justify-content: center;gap: 3px">
        <div>应用看板</div>
        <a-trigger
            v-model:popup-visible="VisibleArr"
            trigger="click">
          <div class="state-tip">
            <img :src="arrIcon" alt="" style="padding-bottom: 3px;">
          </div>
          <template #content>
            <div class="apply-filter">
              <div class="dropdown-search">
                <a-input v-model:model-value="arrSearchQuery" class="apply-input" placeholder="请输入搜索">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <a-checkbox-group
                  v-model="arrCheckDate" direction="vertical" class="checkbox-group"
                  @change="ArrHandleChange">
                <div
                    v-for="(item,index) in arrFilteredItems" :key="index" class="checkbox-group-items"
                    @mouseover="showIcon(index, true)"
                    @mouseleave="showIcon(index, false)">
                  <div class="checkbox-label">
                    <a-checkbox
                        :value="item">
                      <div class="items-text">
                        {{ item.name }}
                      </div>
                    </a-checkbox>
                    <div
                        v-show="ArrShowOnly[index]" class="check-showText"
                        @click="arrHandleOnly(item)">
                      只看此项
                    </div>
                  </div>
                </div>
              </a-checkbox-group>
              <div class="checkbox-all">
                <a-checkbox
                    v-model:model-value="ArrCheckedAll"
                    class="check-all"
                    :indeterminate="arrIndeterminate"
                    @change="arrHandleChangeAll"
                >
                  <span class="check-all-text">全选 </span>
                </a-checkbox>
              </div>
            </div>
          </template>
        </a-trigger>
      </div>

    </template>
    <template #s-dashboardArr="{record}">
      <div v-for="(item,index) in (record.dashboardArr)" :key="index">
        <div class="dashboardName">
          <a-tooltip :content="item.name">
            <div>
              {{ item.name }}
              <span v-if="index < record.dashboardArr.length - 1">,</span>
            </div>
          </a-tooltip>
        </div>
      </div>
    </template>
    <template #t-lastUpdater>
      <div style="display: flex;align-items: center;justify-content: center;gap: 3px">
        <div>最后更新人</div>
        <a-trigger
            v-model:popup-visible="VisibleUpdate"
            trigger="click">
          <div class="state-tip">
            <img :src="updateIcon" alt="" style="padding-bottom: 3px;">
          </div>
          <template #content>
            <div class="apply-filter">
              <div class="dropdown-search">
                <a-input v-model:model-value="updateSearchQuery" class="apply-input" placeholder="请输入搜索">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <a-checkbox-group
                  v-if="updateFilteredItems.length > 0"
                  v-model="updateCheckDate" direction="vertical" class="checkbox-group"
                  @change="UpdateHandleChange">
                <div
                    v-for="(item,index) in updateFilteredItems" :key="index" class="checkbox-group-items"
                    @mouseover="showUpdateIcon(index, true)"
                    @mouseleave="showUpdateIcon(index, false)"
                >
                  <div class="checkbox-label">
                    <a-checkbox
                        :value="item">
                      <div class="items-text">
                        {{ item }}
                      </div>
                    </a-checkbox>
                    <div
                        v-show="UpdateShowOnly[index]" class="check-showText"
                        @click="updateHandleOnly(item)">
                      只看此项
                    </div>
                  </div>
                </div>
              </a-checkbox-group>
              <div class="checkbox-all">
                <a-checkbox
                    v-model:model-value="UpdateCheckedAll"
                    class="check-all"
                    :indeterminate="updateIndeterminate"
                    @change="updateHandleChangeAll">
                  <span class="check-all-text">全选</span>
                </a-checkbox>
              </div>
            </div>

          </template>
        </a-trigger>
      </div>
    </template>
    <template #s-operate>
      <div style="display: flex; align-items: center">
        <a-tooltip content="编辑">
          <icon-edit class="operate-text"/>
        </a-tooltip>
        <a-tooltip content="移交">
          <img src="/icon/turnOver.svg" class="operate-text" alt="">
        </a-tooltip>
        <a-tooltip content="删除">
          <icon-close-circle class="operate-text-del"/>
        </a-tooltip>
      </div>

    </template>
  </a-table>
</template>

<style scoped lang="less">

:deep(.arco-checkbox-icon) {
  width: 16px;
  height: 16px;
}

:deep(.arco-table-tr) {
  height: 66px;
}

.pagination-left {
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}

:deep(.arco-pagination-jumper > span) {
  color: var(--tant-text-gray-color-text1-1);
}
:deep(.arco-table-td-content) {
  width: auto;
}

.href-name {
  text-decoration: underline;
  color: var(--tant-status-info-color-info-hover);
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
  overflow: hidden;
  cursor: pointer;
  min-width: 150px;
  width: auto;
}

.name-cell {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  .InputClose {
    border: none;
    color: red;
    background-color: transparent;
    font-size: 16px;
    border-radius: 5px;
  }

  .InputClose:hover {
    background-color: var(--tant-red-red-20);
  }

  .InputSave {
    border: none;
    color: var(--tant-green-green-50);
    background-color: transparent;
    font-size: 16px;
    border-radius: 5px;
  }

  .InputSave:hover {
    background-color: var(--tant-green-green-20);
  }

  .remake-flow {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .edit-btn {
    display: flex;
    border: none;
    background-color: transparent;

  }

  .edit-btn:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
    transition-delay: .3s; /* 添加离开时的延时 */
    transition: .3s;
    border-radius: 5px;
  }

}

.state-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--tant-secondary-color-secondary-hover);

}

.dropdown {
  box-shadow: var(--tant-small-shadow-small-overall);
  width: 100px;
  height: 120px;
  overflow-y: scroll;
  margin-top: 10px;
  margin-left: 20px;
  border-radius: 4px;
  background-color: var(--tant-bg-white-color-bg1-1);
  padding: 5px;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;

    .dropdown-label {
      padding: 4px 4px 0;
    }
  }

  .dropdown-item:hover {
    background-color: var(--tant-bg-gray-color-bg2-1);
  }
}

//.Arr-input {
//  width: 210px;
//  font-size: inherit;
//  border: none;
//  background-color: transparent;
//  outline: none;
//  &:hover{
//    background-color: transparent;
//    border: none;
//  }
//}
.apply-filter {
  display: flex;
  flex-direction: column;
  background-color: var(--tant-bg-white-color-bg1-1);
  box-shadow: var(--tant-small-shadow-small-overall);
  border-radius: 4px;
  max-width: 240px;
  background: var(--tant-bg-white-color-bg1-1);


  .dropdown-search {
    height: 38px;
    line-height: 38px;
    border-bottom: 1px solid var(--tant-border-color-border1-1);

    .apply-input {
      margin-top: 5px;
      margin-bottom: 3px;
      padding: 4px 8px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 30px;
      background-color: transparent;
      border: none;
      border-radius: var(--tant-border-radius-medium);
      outline: none;
    }

    :deep(.arco-input-prefix) {
      padding-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .checkbox-group {
    height: 100%;
    max-height: 256px;
    overflow-y: scroll;
    padding: 4px 4px 0;
    cursor: default;

    .checkbox-group-items {
      display: flex;
      align-items: center;
      height: 42px;
      border-radius: 5px;

      .checkbox-label {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .items-text {
          padding: 2px;
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          border-radius: 4px;
          width: 100px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .check-showText {
          color: var(--tant-primary-color-primary-hover);
          padding-right: 5px;
        }
      }


    }

    .checkbox-group-items:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
    }


  }

  .checkbox-all {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background-color: var(--tant-fill-color-fill1-1);
    border-top: 1px solid var(--tant-border-color-border1-1);
    padding: 4px 4px 0;
    cursor: default;

    .apply {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--tant-status-info-color-info-hover);
      color: white;
      border-radius: 4px;
      height: 25px;
      width: 50px;
    }

    .check-all {
      display: flex;
      align-items: center;
      height: 32px;

      .check-all-text {
        padding: 2px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        border-radius: 4px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }


}

.dashboardName {
  display: -webkit-box;
  width: 100%;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  word-break: break-all;
  border-bottom: 1px solid transparent;
  -webkit-box-orient: vertical;

  &:hover {
    border-bottom: 1px solid var(--color-gray-blue-7);
    cursor: pointer;
  }
}

.operate-text {
  width: 24px;
  height: 24px;
  font-size: 14px;
  padding: 5px;
  border-radius: 4px;
}

.operate-text:hover {
  background-color: var(--tant-dblue-dblue-20);
}

.operate-text-del {
  width: 24px;
  height: 24px;
  font-size: 14px;
  padding: 5px;
  border-radius: 4px;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>