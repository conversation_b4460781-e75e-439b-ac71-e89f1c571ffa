<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange"/>
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="pickDate"/>
        </div>
        <div class="filter-item">
          <a-select
              v-model:model-value="params.platform"
              placeholder="选择平台"
              :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
              @change="getDataList">
              <template #label="{ data }">
                <span>平台-{{ data?.label }}</span>
              </template>
              <a-option v-for="item in platformList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-button class="button" type="primary" style="margin-left: 16px" @click="openModal">
            配置修改
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-tabs type="text">
        <a-tab-pane key="1" title="登录">
          <a-table
              :loading="loading"
              :columns="columns"
              :data="loginData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :pagination="true"
              :table-layout-fixed="true"
              :scrollbar="scrollbar"
              :scroll="scroll">
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="2" title="验证">
          <a-table
              :loading="loading"
              :columns="columns"
              :data="validateData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :pagination="true"
              :table-layout-fixed="true"
              :scrollbar="scrollbar"
              :scroll="scroll">
          </a-table>
        </a-tab-pane>
      </a-tabs>
      
    </div>
    <a-modal v-model:visible="modalVisible" :width="520" title-align="start" title="登录配置" :footer="false" @cancel="closeModal">
      <a-form ref="basicFormRef" class="attr-form" :rules="basicRules" :model="basicForm" layout="vertical">
          <div class="form-label">{{ platformTitle }}:</div>
          <a-form-item field="oauthWebClientId" label="授权WEB客户端id" validate-trigger="blur">
            <a-input v-model="basicForm.oauthWebClientId" placeholder="请输入授权WEB客户端id"/>
          </a-form-item>
          <a-divider :margin="5"/>
          <div class="form-label">Facebook:</div>
          <a-form-item field="facebookAppId" label="应用id" validate-trigger="blur">
            <a-input v-model="basicForm.facebookAppId" placeholder="请输入应用id"/>
          </a-form-item>
          <a-form-item field="facebookAppSecret" label="应用密钥" validate-trigger="blur">
            <a-input v-model="basicForm.facebookAppSecret" placeholder="请输入应用密钥"/>
          </a-form-item>
          <a-divider :margin="5"/>
          <a-form-item field="deliverUrl" label="回调地址" validate-trigger="blur">
            <a-input v-model="basicForm.deliverUrl" placeholder="请输入回调地址"/>
          </a-form-item>
          <a-form-item field="deliverUrlSalt" label="回调地址盐值" validate-trigger="blur">
            <a-input v-model="basicForm.deliverUrlSalt" placeholder="请输入回调地址盐值"/>
          </a-form-item>
      </a-form>
      <div class="footer">
          <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
          <a-button type="primary" :loading="saveLoading" @click="saveData">
              保存
          </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">

import {reactive, ref} from "vue";
import {getLoginAppDetail, saveLoginAppInfo,getLoginAppStatistic,getLoginAppPlatformList} from "@/api/marketing/api";
import selectApp from "@/components/selected-game-app/index.vue"
import selectCountryList from "@/components/selected-country-list/index.vue"
import DatePicker from "@/components/date-picker/index.vue";
import {Message} from '@arco-design/web-vue';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from 'vue-router';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";

const route = useRoute();
const appId = ref(useSessionStorage('app-id', '')?.value)
const params = reactive({
  country: ['global'],
  date: {
    recentStartDate: 29,
    recentEndDate: 0,
    dateText: '最近30天',
  },
  platform: 'all',
})
const loginData = ref<any>([])
const validateData = ref<any>([])
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const scrollbar = ref(true)
const scroll = {x: 1000,y: 'calc(100vh - 300px)'}
const columns = [
  {
    title: '日期',
    dataIndex: 'rpDate',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '成功',
    dataIndex: 'success',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '失败',
    dataIndex: 'failed',
    ellipsis: true,
    tooltip: true,
    minWidth: 150,
  },
]
const loading = ref(false)


const getDataList = async () => {
  loading.value = true
  try {
    const data = {
      appId: appId.value,
      country: params.country.join(','),
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
      platform: params.platform,
    }
    const [loginRes, validateRes] = await Promise.all([
      getLoginAppStatistic({ ...data, label: 'login' }),
      getLoginAppStatistic({ ...data, label: 'validate' })
    ])
    loginData.value = loginRes
    validateData.value = validateRes
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
getDataList()
const platformList = ref<any>([])
// 获取平台列表
const getPlatformList = async () => {
  try {
    const res = await getLoginAppPlatformList()
    platformList.value = res
  } catch (error) {
    console.log(error)
  }
}
getPlatformList()
const countryChange = (value:any) => {
  getDataList()
}
const pickDate = (date: any) => {
  params.date = date
  getDataList()
};
const modalVisible = ref(false)
const platformTitle = ref('')
const basicFormRef = ref()
// 表单规则
const basicRules = {
  oauthWebClientId: [
    {
      required: true,
      message: '请输入授权WEB客户端id'
    }
  ],
  deliverUrl: [
    {
      required: false,
      message: '请输入回调地址'
    }
  ],
}

// 表单信息
const basicForm = reactive({
  oauthWebClientId:'',
  deliverUrl:'',
  facebookAppSecret:'',
  facebookAppId:'',
  deliverUrlSalt:''
});


// 获取基本信息
const getInfo = async () => {
  try {
    await getLoginAppDetail(appId.value).then(res => {
      basicForm.oauthWebClientId = res?.oauthWebClientId
      basicForm.deliverUrl = res?.deliverUrl
      basicForm.facebookAppSecret = res?.facebookAppSecret
      basicForm.facebookAppId = res?.facebookAppId
      basicForm.deliverUrlSalt = res?.deliverUrlSalt
    })
  } catch (error) {
    console.log(error, 'pay-init');
  }
}
const openModal = async () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveLoading = ref(false)
const saveData = () => {
  basicFormRef.value.validate(async (valid: any) => {
    saveLoading.value = true
    const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
    if (!valid) {
      try {
        const data = {
          appId:appId.value,
          osType:storedData?.osName,
          packageName:storedData?.package,
          oauthWebClientId: basicForm.oauthWebClientId,
          deliverUrl: basicForm.deliverUrl,
          facebookAppSecret: basicForm.facebookAppSecret,
          facebookAppId: basicForm.facebookAppId,
          deliverUrlSalt: basicForm.deliverUrlSalt,
        }
        await saveLoginAppInfo(data)
        Message.success('保存成功');
        modalVisible.value = false
        getInfo()
      } catch (error) {
        console.log(error);
      } finally {
        saveLoading.value = false;
      }
    } else {
      saveLoading.value = false;
    }
  })
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    appId.value = value
    getInfo().then(()=>{
      const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
      platformTitle.value = storedData.osName === 'ios' ? 'Apple' : 'Google'
    })
    getDataList()
  }
})
const init = () => {
  getInfo().then(()=>{
    const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
    platformTitle.value = storedData.osName === 'ios' ? 'Apple' : 'Google'
  })
}
init()
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

.form-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-label{
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  margin-bottom: 12px;
}
</style>