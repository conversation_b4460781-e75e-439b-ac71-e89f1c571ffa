<template>
  <div id="analyse-root">
    <div class="analyse-content">
      <analyse-header
        :model="ReportAnalyseModel.GROUP"
        :query-param="queryParam"
        :report-data="reportData"
        :header-info="headerInfo"
        @call-computed-data="computedData"
        @update-report-form="updateReportForm"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="dataLoading" class="box">
                <div v-if="!dataLoading" class="query-condition">
                  <!-- 分析指标 -->
                  <analysis-index
                      :analysis-index-data="analysisIndexData"
                      :full-event-lists="fullEventList"
                      @indicators-change="indicatorsChange"
                      @reset-params="reset"/>
                  <!-- 全局筛选 -->
                  <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem :group-item="groupData" @aggregates-change="aggregatesChange"/>
                  <div style="width: 100%;padding: 0 24px;">
                    <a-divider :margin="5" type="dashed"/>
                  </div>
                  <!-- 用户筛选、 -->
                  <userFilter :user-filter="userFilterData" @filters-change="userFiltersChange"/>
                </div>
                <div class="left-footer">
                  <div style="margin-right: auto">
                    <a-checkbox
                        :model-value="queryParam.isDataIntegrity"
                        style="margin-right: 8px;"
                        @change="v=>queryParam.isDataIntegrity=v">
                      完整数据
                    </a-checkbox>
                    <a-tooltip content="勾选后不显示部分的数据" position="top">
                      <icon-info-circle size="16"/>
                    </a-tooltip>
                  </div>
                  <a-button class="footer-btn" @click="toSave">保存</a-button>
                  <a-button class="footer-btn" type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="datePick"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        分析周期：
                        <DateDropdown :time-span-data="timeSpanData" @time-span-change="timeSpanChange"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        激活事件：
                        <single-event-select :event-code="activationEvent?.code" @event-change="activationEventChange"/>
                      </div>
                    </a-space>
                    <div style="display: flex;">
                      <!--                      <a-button-group style="background-color: #fff;margin-right: 16px;">-->
                      <!--                        <a-popover title="表格" position="bottom" :content-style="{width: '260px'}">-->
                      <!--                          <a-button :disabled="!eventData.groupQueryResult?.length" @click="selectChart('table')">-->
                      <!--                            <template #icon><img class="option-icon" src="/icon/table-chart.svg" alt=""/></template>-->
                      <!--                          </a-button>-->
                      <!--                          <template #content>-->
                      <!--                            <div>-->
                      <!--                              <p>展示分组多指标对比数据表格信息</p>-->
                      <!--                              <img :src="table" alt="" style="width: 90%;height: 90%;"/>-->
                      <!--                            </div>-->
                      <!--                          </template>-->
                      <!--                        </a-popover>-->
                      <!--                        <a-popover title="趋势图" position="bottom" :content-style="{width: '260px'}">-->
                      <!--                          <a-button :disabled="!eventData.groupQueryResult?.length" @click="selectChart('trend')">-->
                      <!--                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>-->
                      <!--                          </a-button>-->
                      <!--                          <template #content>-->
                      <!--                            对比不同分组的用户，在激活后续第N日的单指标数据趋势<br>-->
                      <!--                          </template>-->
                      <!--                        </a-popover>-->
                      <!--                      </a-button-group>-->
                      <a-button style="margin-left: 12px;" @click="importTable()">导出</a-button>
                    </div>
                  </div>
                  <div v-if="!loading && eventData.groupQueryResult?.length" class="right-content">
                    <a-alert v-if="eventData?.resultsExceedsLimit" style="margin-bottom: 5px;">
                      因数据条数过多，优先展示前1000条数据。建议修改分组项或添加更严格的过滤项以减少数据条数，或点击右上角下载全量数据。
                    </a-alert>
                    <div class="chart-item">
                      <trend-view :event-data="eventData"/>
                    </div>
                    <div class="chart-item">
                      <table-view ref="tableRef" :event-data="eventData"/>
                    </div>
                  </div>
                  <div v-if="!loading && !eventData.groupQueryResult?.length" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
      <!-- 保存弹窗 -->
      <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
        <template #title>
          <div class="modal-title">保存报表</div>
        </template>
        <a-form ref="saveFormRef" :model="form">
          <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
            <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
          </a-form-item>
          <a-form-item field="dashboard" label="保存至看板">
            <dashboard-select v-model:selected="form.dashboard" type="dashboard" @change="dashboardChange"/>
          </a-form-item>
          <a-form-item field="description" label="备注">
            <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" :loading="saveLoading" @click="saveReport">保存</a-button>
        </template>
      </a-modal>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {provide, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {ChartType, getNumberDisplayConfig, IndicatorType, NumberDisplayType} from "@/api/enum";
import {cloneDeep, debounce} from "lodash";
import DateDropdown from "@/views/analyse/components/dateDropdown.vue";
import {Indicator, ReportAnalyseModel} from "@/api/analyse/type";
import {queryGroupReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {analyseStore, toolStore} from '@/store';
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from 'vue-router';
import {detailReport} from "@/api/report/api";
import userFilter from "@/views/analyse/components/UserFilter.vue"
import {analyseParamsVerify, getDefaultObj, verifyLeastIndicator} from "@/views/analyse/components/util/verify";
import {reportSaveSuccessMessage} from "@/views/analyse/components/util/message";
import {compressDateRangeToEndDate} from "@/utils/dateUtil";
import {updateDisplayNames} from '@/views/analyse/components/util/mapNameChange';
import AnalysisIndex from "./components/AnalysisIndex.vue"
import globalFilter from "../components/globalFilter.vue"
import groupItem from "../components/groupItem.vue"
import TableView from "./components/TableView.vue";
import TrendView from "./components/TrendView.vue";
import analyseHeader from "../components/analyseHeader.vue"
import SingleEventSelect from "@/views/analyse/components/SingleEventSelect.vue";

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
const route = useRoute();
const reportId = ref(route.query.code);
const analyseData = analyseStore();
const toolData = toolStore();
const eventData = ref({
  startTime: 0,
  endTime: 0,
  groupsDesc: [],
  groups: [],
  newUserCount: [],
  timeSpan: {},
  dateRange: {},
  groupQueryResult: {},
})
const headerInfo = reactive({
  title: '群组分析',
  img: '/icon/topMenu/event.svg',
  tips: '群组分析',
  root: '#analyseRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true,
  showAppSelect: true
})
const timeSpanData = ref({
  firstDayDfWeek: 1,
  number: 7,
  unit: "DAY"
})
const activationEvent = ref()
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// 分析查询请求id
const requestId = ref()
const loading = ref(false)
const totalNum = ref(0)
const isReset = ref(false)
// 传参数组
const queryParam = reactive({
  indicators: [] as Indicator[], // 查询指标
  filter: {}, // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  }, // 查询日期范围
  timeSpan: timeSpanData.value,
  activationEvent: activationEvent.value,
  firstDayDfWeek: null, // 周几作为一周开始
  isDataIntegrity: false,
  userFilter: {},// 用户筛选条件
})
const dataLoading = ref(false)
const analysisIndexData = ref<any>([]) // 分析指标传参
const globalFilterData = ref({}) // 全局筛选传参
const groupData = ref<any>([]) // 分组项传参
const userFilterData = ref({}) // 用户筛选传参
const fullEventList = ref<any>([])
const eventBus = useEventBus('eventList');
const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const paramsDataStorage = useSessionStorage("group-params-data", {});
const saveFormRef = ref();
// const eventParamsList = ref() // 已有event事件列表传值
const handleAttrBus = debounce(async () => {
  const eventLists = queryParam.indicators.flatMap(item =>
      item.eventList
  );
  eventBus.emit(eventLists);
  form.value.name = `${queryParam.indicators[0]?.displayName}等${queryParam.indicators.length}个`
}, 300)

// 提供已选指标，用于筛选和分组
// provide('eventParamsList', eventParamsList);
// 提供分析模式
provide('analyseModel', ReportAnalyseModel.GROUP);
// 分析指标传参
const indicatorsChange = (v) => {
  console.log('indicatorsChange', v);
  
  queryParam.indicators = cloneDeep(v)
  handleAttrBus();
  paramsDataStorage.value = cloneDeep(queryParam);
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
  paramsDataStorage.value = queryParam
}
// 用户筛选传参
const userFiltersChange = (v) => {
  queryParam.userFilter = v
  paramsDataStorage.value = queryParam
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
  paramsDataStorage.value = queryParam
}
const timeOut = ref()
const tableRef = ref()

// 计算
const computedData = () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.warning('事件查询超时，请重试！')
    }
  }, 60 * 1000)
  if (!verifyLeastIndicator(queryParam)) {
    Message.error('请至少选中一个指标计算');
    loading.value = false;
    return;
  }
  if (analyseParamsVerify(queryParam)) {
    requestId.value = queryGroupReportData(queryParam);
  } else {
    Message.error('筛选条件参数错误')
    loading.value = false
  }
}

watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (newData.result === null) {
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }
  timeOut.value && clearTimeout(timeOut.value)
  eventData.value = newData.result
  paramsDataStorage.value = queryParam;
  totalNum.value = newData.result.totalNum
  loading.value = false
})

const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  saveVisible.value = false;
}
const saveLoading = ref(false)
// 报表更新传参对象
const editReportData = reactive({
  name: '',
  description: ''
})
const dashboardData = ref()
const dashboardChange = (v) => {
  dashboardData.value = v
}
// 保存报表
const saveReport = debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    return;
  }
  saveLoading.value = true;
  try {
    const saveParams = {
      model: ReportAnalyseModel.GROUP,
      reportId: reportId.value || undefined,
      dashboard: form.value.dashboard,
      name: editReportData?.name || form.value.name,
      description: editReportData?.description || form.value.description,
      queryParam,
      chartParams: [],
      chartType: ChartType.TREND
    };
    await saveAnalyseReportList(
        saveParams.model,
        saveParams.reportId,
        saveParams.dashboard,
        saveParams.name,
        saveParams.description,
        saveParams.queryParam,
        saveParams.chartParams,
        saveParams.chartType
    );
    dashboardSelected.value = { ...dashboardData.value }
    Message.success({
      content: () => reportSaveSuccessMessage(form.value.dashboard),
      duration: 3000
    });
    eventBus.emit('saveReport');
    if (!reportId.value) {
      saveVisible.value = false;
    }
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300);
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  saveReport()
}
const toSave = () => {
  if (reportId.value) {
    saveReport()
  } else {
    saveVisible.value = true
  }
}
const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});

provide('boardDate', boardDate);

const showDrawer = ref<boolean>(false)

// 按小时 只能选一天
const minutesVerification = () => {
  const timeType = queryParam.timeParticleSize
  if (timeType === 'm1' || timeType === 'm5' || timeType === 'm10') {
    Message.warning('按分钟查看，时间范围一次最多展示1天')
    const compressDateRange = compressDateRangeToEndDate(boardDate.value);
    boardDate.value = compressDateRange;
    queryParam.dateRange = compressDateRange;
  }
}

const datePick = (date: any) => {
  boardDate.value = date
  queryParam.dateRange = date;
  minutesVerification()
  computedData()
};

const importTable = (name?: string) => {
  tableRef.value.exportXlsx(queryParam.dateRange, name)
}
const timeSpanChange = (v) => {
  queryParam.timeSpan = v
  computedData()
}

const activationEventChange = (v) => {
  queryParam.activationEvent = v
  computedData()
}

const reportData = ref()
// 创建事件项的工具函数
const createEventItem = (eventData) => ({
  eventName: eventData.name,
  eventCode: eventData.code,
  eventDisplayName: eventData.displayName,
  type: eventData.objectType,
  eventType: eventData.type,
  eventAttrCode: '',
  eventAttrName: eventData.objectType === 'event' ? '总次数' : '',
  filter: {}
});
const init = async () => {
  if (dataLoading.value) {
    // 正在初始化中
    return
  }
  dataLoading.value = true
  checkedSessionGroups.value = []
  toolData.updateTemporaryList([])
  fullEventList.value = (await toolData.fetchGroupModalList()).flatMap(category => category.items || []);
  // 切换应用时重新请求全部属性
  if(toolData.totalEvtIndCodes.length){
    await toolData.fetchAllAttrList()
  }
  // const paramsData = paramsDataStorage.value;
  let paramsData = {} as any
  if (reportId.value) {
    await detailReport(reportId.value as string).then(res => {
      paramsData = res.queryParam
      paramsData.dateRange = boardDate.value
      reportData.value = res
      editReportData.name = res?.name
      editReportData.description = res?.description
    })
  }
  // 如果没有从报表获取到数据，则尝试从会话存储获取
  if (Object.keys(paramsData).length === 0) {
    paramsData = paramsDataStorage.value;
  }
  // 处理分析指标
  if (paramsData?.indicators && paramsData.indicators?.length > 0) {
    const defaultEvent = createEventItem(getDefaultObj(fullEventList.value));
    analysisIndexData.value = paramsData.indicators.map(item => {
      return {
        ...item,
        unitName: item.unitName,
        eventList: item.eventList?.length > 0 ? item.eventList : [defaultEvent],
      }
    })
  }
  // 更新可能被修改的displayName
  updateDisplayNames(analysisIndexData.value)
  if (!analysisIndexData.value?.length) {
    const firstEvent = getDefaultObj(fullEventList.value);
    const eventItem = createEventItem(firstEvent);
    analysisIndexData.value.push({
      name: firstEvent.name,
      type: IndicatorType.GROUP,
      isBasic: true,
      displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
      displayName: firstEvent.objectType === 'event'
          ? `${firstEvent.name}-${firstEvent.displayName}.总次数`
          : `${firstEvent.name}-${firstEvent.displayName}`,
      unitName: '',
      eventList: [eventItem],
      filter: {},
      isAddUp: false,
      ignoreCalc: false,
    });
  }
  queryParam.indicators = analysisIndexData.value.map(item => {
    return {
      displayName: item.displayName,
      displayType: item.displayType,
      isAddUp: item.isAddUp,
      name: item.name,
      type: item.type,
      isBasic: item.isBasic,
      eventList: item.eventList,
      filter: item.filter,
      formula: item?.formula,
      ignoreCalc: item.ignoreCalc
    }
  })
  // 处理默认激活事件
  let hasTrackEvent = false;
  fullEventList.value?.forEach(item => {
    if (item.name === 'start_track_event') {
      activationEvent.value = {
        code: item.code,
        name: item.name,
        displayName: item.displayName,
      }
      queryParam.activationEvent = {
        code: item.code,
        name: item.name,
        displayName: item.displayName,
      }
      hasTrackEvent = true
    }
    if (!hasTrackEvent && item.name === 'sdk_app_open') {
      activationEvent.value = {
        code: item.code,
        name: item.name,
        displayName: item.displayName,
      }
      queryParam.activationEvent = {
        code: item.code,
        name: item.name,
        displayName: item.displayName,
      }
    }
  })

  form.value.name = `${analysisIndexData.value[0]?.displayName}等${analysisIndexData.value?.length}个`;
  // 处理事件筛选
  if (paramsData.filter) {
    globalFilterData.value = paramsData.filter
    queryParam.filter = paramsData.filter
  }
  // 处理分组项
  if (paramsData.aggregates && paramsData.aggregates.length > 0) {
    groupData.value = cloneDeep(paramsData.aggregates)
    queryParam.aggregates = cloneDeep(paramsData.aggregates)
  }
  // 处理用户筛选条件
  if (paramsData.userFilter) {
    userFilterData.value = paramsData.userFilter
    queryParam.userFilter = paramsData.userFilter
  }
  // 处理时间
  if (paramsData.dateRange) {
    boardDate.value = paramsData.dateRange
    queryParam.dateRange = paramsData.dateRange
  } else {
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    boardDate.value = date
    queryParam.dateRange = date
  }
  // 处理timeSpan
  if (paramsData.timeSpan) {
    timeSpanData.value = paramsData.timeSpan
    queryParam.timeSpan = paramsData.timeSpan
  } else {
    const time = {
      firstDayDfWeek: 1,
      number: 7,
      unit: "DAY"
    }
    timeSpanData.value = time
    queryParam.timeSpan = time
  }
  // 处理激活事件
  if (paramsData.activationEvent) {
    activationEvent.value = paramsData.activationEvent
    queryParam.activationEvent = paramsData.activationEvent
  }
  dataLoading.value = false
  setTimeout(() => {
    computedData()
  }, 1000)
}

init()

localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    init()
  }
})
const reset = () => {
  isReset.value = true
  paramsDataStorage.value = {}
  analysisIndexData.value = []
  globalFilterData.value = {}
  queryParam.filter = {}
  groupData.value = []
  userFilterData.value = {}
  queryParam.userFilter = {}
  init()
}
</script>

<style scoped lang="less">
.box {
  width: 100%;
  height: 100%;
}

//.cancel {
//  background: transparent;
//  margin-right: 8px;
//
//  &:hover {
//    background-color: var(--color-secondary);
//  }
//}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}

.right-content {
  width: calc(100% - 24px);
  height: 100%;
  position: relative;
  margin: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .chart-item {
    margin-bottom: 10px;
    height: calc(100% - 24px);
  }
}

.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;

}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}


</style>