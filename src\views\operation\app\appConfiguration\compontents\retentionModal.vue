<template>
    <a-modal v-model:visible="modalVisible" :width="660" title-align="start" title="编辑留存模型" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="名称">
                <a-select v-model:model-value="form.name" allow-create :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="handleChange">
                    <a-option v-for="(item,index) in props.list" :key="index" :value="item.name">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <div class="data-content">
                <div v-for="(item,index) in dataList" :key="index" class="item">
                    <a-form-item :field="`num${index}`" :label="labelList[index]">
                        <a-input-number v-model:model-value="item.number"/>
                    </a-form-item>
                </div>
            </div>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addRetentionModel, updateRetentionModel,} from "@/api/marketing/api";

const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    }
})
const modalVisible = ref(false)
const form = reactive({
    name: '',
    code: '',
    data:[] as number[]
})

const labelList = ['1日','2日','3日','4日','5日','6日','7日','8日','9日','10日','11日','12日','30日','60日','90日']
const rules = {
    name: [
        {
            required: true,
            message:'请选择名称',
        }
    ],
}
const formRef = ref()

const openModal = (value:string) => {
    form.code = value
    props.list.forEach(item => {
        if(item.code === value) {
            form.name = item.name
            form.data = item.data
        }
    })
    modalVisible.value = true
}
const dataList = computed(() => {
    if(form.data.length){
        return form.data.map(item => ({ number: Number(item) }));
    }
    return [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
})
const closeModal = () => {
    modalVisible.value = false
    formRef.value.resetFields()
    formRef.value.clearValidate()
}
const handleChange = (v) => {
    const hasNameEqualToV = props.list.some(item => item.name === v);
    if(hasNameEqualToV){
        form.name = v
        props.list.forEach(item => {
            if(item.name === v) {
                form.code = item.code
                form.data = item.data
            }
        })
    }else{
        form.code = ''
        form.data = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]
        form.name = v
    }
    
}
const emits = defineEmits(['updateData']);
const saveData = () => {
    formRef.value.validate((valid:any) => {
        if (!valid) {
            // 
            console.log(form,'form');
            form.data = dataList.value.map(item => item.number)
            if(form.code){
                // 更新
                try {
                    updateRetentionModel(form).then(res => {
                        Message.success('更新成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('更新失败:', error);
                }
            }else{
                // 添加
                try {
                    addRetentionModel(form).then(res => {
                        Message.success('创建成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } catch (error) {
                    console.error('创建失败:', error);
                }
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.data-content{
    display: flex;
    flex-wrap: wrap;
    .item{
        // width: 33%;
        flex: 1 0 33.33%;
        box-sizing: border-box;
        padding: 0 10px;
    }
}
</style>