<template>
  <div class="content">
    <div class="header-sticky">
      <div class="title" style="width: calc(100% - 42px);" @click="addAnalysisIndex">
        <img src="/icon/analysis/performAnalysis.svg" alt="">
        <span class="text">分析指标</span>
        <div class="model-btn">
          <a-button>
            <template #icon>
              <icon-plus class="nav-icon"/>
            </template>
          </a-button>
        </div>
      </div>
      <a-tooltip content="重置" position="top">
        <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="resetParams">
          <template #icon>
            <icon-loop style="font-size: 16px;"/>
          </template>
        </a-button>
      </a-tooltip>
    </div>
    <div class="indicator-list-box">
      <draggable
          :list="eventListData || []"
          group="eventListDataDrag"
          item-key="index"
          handle=".left-handle"
          ghost-class="drag-ghost"
      >
        <template #item="{ element,index }">
          <div class="indicator-item-box">
            <div class="item-content">
              <i class="left-index">{{ index + 1 }}</i>
              <i class="left-handle">
                <icon-drag-dot-vertical/>
                <a-tooltip v-if="!element.ignoreCalc" content="忽略计算">
                  <icon-eye style="margin-top: 12px;cursor: pointer;" @click="() => element.ignoreCalc = true"/>
                </a-tooltip>
                <a-tooltip v-if="element.ignoreCalc" content="参与计算">
                  <icon-eye-invisible style="margin-top: 12px;cursor: pointer;" @click="() => element.ignoreCalc = false"/>
                </a-tooltip>
              </i>
              <div class="right-content">
                <div v-if="element.ignoreCalc" class="mask"></div>
                <div class="indicator-attribute">
                  <a-tooltip content="点击修改指标名称" position="top">
                    <div class="rename">
                      <div class="name-span">{{ element.displayName }}</div>
                      <a-input v-model="element.displayName" class="name-input"/>
                    </div>
                  </a-tooltip>
                  <display-type-select v-model:displayType="element.displayType"/>
                </div>
                <a-tooltip content='点击编辑自定义指标' position="tl">
                  <custom-indicator-display v-if="!element.isBasic && element.eventList?.length" :allow-hover="true" :indicator="element" @click="editCustomIndicator(index)"/>
                </a-tooltip>
                <div v-if="element.isBasic && element.eventList?.length" class="event-element">
                  <EventIndicatorSelect :ref="el => evtIndRefs[index] = el" :panel-data="element.eventList[0]" @analysis-index-change="basicIndicatorEventChange(index,0,$event)"/>
                  <span v-if="element.eventList[0].type == 'event'" class="row-word">的</span>
                  <sub-select v-if="element.eventList[0].type == 'event'" :panel-data="element.eventList[0]" @sub-change="statisticsChange(index,0,$event)"/>
                  <a-tooltip content="切换自定义公式" position="top">
                    <a-button class=" custom-edit-btn btn-26" @click="editCustomIndicator(index)">
                      <template #icon>
                        <icon-formula/>
                      </template>
                    </a-button>
                  </a-tooltip>
                </div>
              </div>
            </div>
            <div class="action-right">
              <a-space align="center">
                <a-tooltip content="添加筛选" position="top">
                  <a-button class="btn-bg btn-26" @click="addQueryFilter(index)">
                    <template #icon>
                      <icon-filter/>
                    </template>
                  </a-button>
                </a-tooltip>
                <a-tooltip content="复制" position="top">
                  <a-button class="btn-bg btn-26" @click="copyIndicator(index)">
                    <template #icon>
                      <icon-copy/>
                    </template>
                  </a-button>
                </a-tooltip>
                <a-dropdown trigger="hover" @select="handleIndicatorOperation(index,$event)">
                  <a-button class="btn-bg btn-26">
                    <template #icon>
                      <icon-more-vertical/>
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption value="create">
                      <template #icon>
                        <icon-save/>
                      </template>
                      <template #default>创建指标</template>
                    </a-doption>
                    <a-doption value="loop">
                      <template #icon>
                        <icon-loop/>
                      </template>
                      <template #default>重置</template>
                    </a-doption>
                    <a-doption v-if="eventListData.length>1" value="delete">
                      <template #icon>
                        <icon-close-circle/>
                      </template>
                      <template #default>删除</template>
                    </a-doption>
                  </template>
                </a-dropdown>
              </a-space>
            </div>
            <eventQueryFilter
                :ref="el => element.queryFilterRef = el"
                :filter="element.filter"
                :show-detail-filter="true"
                :code-list="element.eventList"
                @query-filters-change="queryFiltersChange(index, $event)"/>
            <div v-if="element.filter && element.filter.filters && element.filter.filters.length" class="row-foot">
              <div class="ta-filter-button" @click="addQueryFilter(index)">
                <icon-plus class="action"/>
                <span class="label">筛选条件</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
    <create-event-indicator-modal
        v-if="createIndicatorModalVisible"
        v-model:visible="createIndicatorModalVisible"
        :indicator="eventListData[operateIndicatorIndex]"
        @edit-indicator="editCustomIndicator(operateIndicatorIndex)"
    />
    <edit-custom-indicator-modal
        v-if="editCustomIndicatorVisible"
        v-model:visible="editCustomIndicatorVisible"
        :indicator="eventListData[operateIndicatorIndex]"
        :full-event-list="fullEventList"
        @confirm="editCustomIndicatorConfirm"/>
  </div>
</template>

<script setup lang="ts">
import {nextTick, ref, watch} from "vue";
import {cloneDeep, omit} from "lodash";
import SubSelect from "@/views/analyse/components/SubSelect.vue"
import {Message} from '@arco-design/web-vue'
import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import {getNumberDisplayConfig, IndicatorType, NumberDisplayType} from "@/api/enum";
import DisplayTypeSelect from "@/views/analyse/components/DisplayTypeSelect.vue";
import EditCustomIndicatorModal from "@/views/analyse/components/EditCustomIndicatorModal.vue";
import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import CreateEventIndicatorModal from "@/views/analyse/components/CreateEventIndicatorModal.vue";
import draggable from "vuedraggable";
import {getDefaultObj} from "@/views/analyse/components/util/verify";

const props = defineProps({
  analysisIndexData: {
    type: Array,
    default: () => []
  },
  fullEventLists: {
    type: Array,
    default: () => []
  },
})

const emits = defineEmits(['indicatorsChange', 'resetParams'])

// 指标事件列表数据
const eventListData = ref<any>([])
// 当前操作指标
const operateIndicatorIndex = ref<number>(0);

// 自定义指标编辑显示
const editCustomIndicatorVisible = ref<boolean>(false)

// 全量事件列表
const fullEventList = ref<any>([])
// 指标统计展示名称
const statisticsDisplayName = ref('')

// 指标创建弹窗
const createIndicatorModalVisible = ref(false)

// 监听传入事件列表
watch(() => props.fullEventLists, () => {
  fullEventList.value = cloneDeep(props.fullEventLists)
}, {immediate: true, deep: true})

watch(() => props.analysisIndexData, () => {
  eventListData.value = cloneDeep(props.analysisIndexData)
}, {immediate: true, deep: true})

/**
 * 指标变化事件通知
 */
watch(eventListData, (newValue, oldValue) => {
  const indicator = newValue.map(item => {
    return {
      ignoreCalc: item.ignoreCalc,
      displayName: item.displayName,
      displayType: item.displayType,
      name: item.name,
      type: item.type,
      isBasic: item.isBasic,
      formula: item.formula,
      eventList: item.eventList,
      filter: item.filter
    }
  })
  emits('indicatorsChange', indicator)
}, {immediate: true, deep: true})

/**
 * 参数校验
 */
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  const firstList = params.filter?.filters || [];
  const secondList = params.eventList.filter(event => event.filter?.filters?.length > 0)
      .map(event => event.filter.filters)
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList);
};

const evtIndRefs = ref<any>([])
// 添加事件指标
const addAnalysisIndex = () => {
  const firstEvent = getDefaultObj(fullEventList.value);
  const eventItem = {
    eventName: firstEvent.name,
    eventCode: firstEvent.code,
    eventDisplayName: firstEvent.displayName,
    type: firstEvent.objectType,
    eventType: firstEvent.type,
    eventAttrCode: '',
    eventAttrName: firstEvent.objectType === 'event' ? '总次数' : '',
    filter: {},
  };
  eventListData.value.push({
    name: firstEvent.name,
    type: IndicatorType.EVENT,
    isBasic: true,
    displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
    displayName: firstEvent.objectType === 'event'
        ? `${firstEvent.name}-${firstEvent.displayName}.总次数`
        : `${firstEvent.name}-${firstEvent.displayName}`,
    eventList: [eventItem],
    filter: {},
    ignoreCalc: false,
  });
  nextTick(() => {
    if (evtIndRefs.value[eventListData.value.length - 1]) {
      evtIndRefs.value[eventListData.value.length - 1].handleTriggerVisible();
    }
  });

}

/**
 * 添加指标筛选条件
 */
const addQueryFilter = (index: number) => {
  eventListData.value[index].queryFilterRef.add()
}

/**
 * 指标筛选条件变化
 */
const queryFiltersChange = (index: number, v) => {
  eventListData.value[index].filter = v;
}

/**
 * 指标统计变化
 */
const statisticsChange = (index: number, eventIndex: number, e: any) => {
  const {eventAttrName, eventAttrCode, eventAttrDisplayName, statisticalName, statisticalType, subjectName, subjectCode, objectType} = e
  const filter = {...eventListData.value[index].eventList[eventIndex]};
  filter.eventAttrName = eventAttrName;
  filter.eventAttrCode = eventAttrCode;
  filter.eventAttrDisplayName = eventAttrDisplayName
  if (statisticalName) {
    filter.statisticalName = statisticalName;
  } else {
    delete filter?.statisticalName;
  }
  if (statisticalType) {
    filter.statisticalType = statisticalType;
  } else {
    delete filter?.statisticalType;
  }
  filter.objectType = objectType;
  if (subjectName) {
    filter.subjectName = subjectName;
  } else {
    delete filter?.subjectName;
  }
  if (subjectCode) {
    filter.subjectCode = subjectCode;
  } else {
    delete filter?.subjectCode;
  }
  eventListData.value[index].eventList[eventIndex] = filter;
  if (filter.eventAttrName === '触发用户数' && subjectName) {
    statisticsDisplayName.value = `触发${subjectName}数`
  } else if (filter.eventAttrName === '人均次数' && subjectName) {
    statisticsDisplayName.value = `${subjectName}均次数`
  } else {
    statisticsDisplayName.value = filter.eventAttrDisplayName
  }
  eventListData.value[index].displayName = `${filter.eventName}-${filter.eventDisplayName}${filter.type === 'event' ? (statisticsDisplayName.value ? `.${statisticsDisplayName.value}` : '') + (statisticalName ? `.${statisticalName}` : '') : ''}`
}

/**
 * 指标操作
 *
 * @param index 指标id
 * @param operationType 操作类型
 */
const handleIndicatorOperation = (index: number, operationType) => {
  if (operationType === 'delete') {
    eventListData.value.splice(index, 1)
    eventListData.value.queryFilterRef.deleteAll()
  }
  if (operationType === 'loop') {
    const eventItem = getDefaultObj(fullEventList.value)
    eventListData.value[index] = {
      name: eventItem.name,
      type: IndicatorType.EVENT,
      isBasic: true,
      displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
      displayName: eventItem.objectType === 'event'
          ? `${eventItem.name}-${eventItem.displayName}.总次数`
          : `${eventItem.name}-${eventItem.displayName}`,
      eventList: [
          {
            eventName: eventItem.name,
            eventCode: eventItem.code,
            eventDisplayName: eventItem.displayName,
            type: eventItem.objectType,
            eventType: eventItem.type,
            eventAttrCode: "",
            eventAttrName: eventItem.objectType === 'event'? '总次数' : '',
            filter: {}
          }
      ],
      filter: {}
    };
  }
  if (operationType === 'create' && eventListData.value.length) {
    operateIndicatorIndex.value = index
    if (paramsVerify(eventListData.value[index])) {
      createIndicatorModalVisible.value = true
    } else {
      Message.error('筛选条件参数错误')
    }
  }
};

/**
 * 基础指标变化
 */
const basicIndicatorEventChange = (index: number, eventIndex: number, e: any) => {
  const filter = {
    ...eventListData.value[index].eventList[eventIndex],
    ...e
  }  
  const fieldsToRemove = e.type === 'indicator'
      ? ['eventCode', 'eventName', 'eventDisplayName']
      : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

  // 从filter中移除指定的字段，包括额外需要移除的属性
  const cleanedFilter = omit(filter, [
    ...fieldsToRemove,
    'eventAttrCode',
    'eventAttrDisplayName',
    'statisticalName',
    'statisticalType'
  ]);
  eventListData.value[index].eventList[eventIndex] = cleanedFilter;
  eventListData.value[index].name = e.eventName || e.indicatorName
  eventListData.value[index].eventList[eventIndex].eventAttrName = filter.type === 'event' ? '总次数' : ''
  eventListData.value[index].displayName = `${e.eventName || e.indicatorName}-${e.eventDisplayName || e.indicatorDisplayName}${filter.type === 'event' ? '.总次数' : ''}`
}

/**
 * 指标复制
 */
const copyIndicator = (index: number) => {
  const newItem = cloneDeep(eventListData.value[index]);
  eventListData.value.splice(index + 1, 0, newItem);
}

/**
 * 编辑自定义指标
 */
const editCustomIndicator = (index: number) => {
  operateIndicatorIndex.value = index
  editCustomIndicatorVisible.value = true
}

/**
 * 自定义指标编辑确认
 */
const editCustomIndicatorConfirm = (customIndicator) => {
  const {eventList, formula, displayType} = customIndicator;
  eventListData.value[operateIndicatorIndex.value].isBasic = formula === 'A'
  eventListData.value[operateIndicatorIndex.value].eventList = eventList
  eventListData.value[operateIndicatorIndex.value].formula = formula
  eventListData.value[operateIndicatorIndex.value].displayType = displayType
}
const resetParams = () => {
  emits('resetParams')
}
</script>

<style scoped lang="less">
.content {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .header-sticky {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .title {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .text {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .indicator-list-box {
    position: relative;
    box-sizing: border-box;
    min-height: 32px;
    transition: all .3s;

    .indicator-item-box {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .item-content {
        align-items: flex-start;
        height: auto;
        display: flex;
        width: 100%;

        .left-index {
          margin-top: 4px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
        }

        .left-handle {
          cursor: pointer;
          font-size: 14px;
          color: var(--tant-secondary-color-secondary-default);
          background-color: transparent;
          margin-top: 4px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          transition: all .3s;
          display: none;
        }

        .right-content {
          flex-grow: 1;
          padding: 4px 0;
          width: calc(100% - 36px);
          position: relative;

          .indicator-attribute {
            height: 24px;
            max-width: calc(100% - 40px);
            background: inherit;
            margin-bottom: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;

            .rename {
              position: relative;
              height: 100%;
              max-width: calc(100% - 120px);
              margin-right: 12px;
              display: flex;
              align-items: center;
              overflow: hidden;

              .name-span {
                // 仅用于占位
                flex: 0 1 auto; /* 不会强制扩展，占据内容宽度，但最大不能挤压右侧 */
                height: 100%;
                font-size: 14px;
                min-width: 40px;
                color: red;
                font-weight: 900;
                opacity: 0;
              }

              .name-input {
                position: absolute;
                flex: 0 1 auto; /* 不会强制扩展，占据内容宽度，但最大不能挤压右侧 */
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                padding: 0;
                opacity: 1;
              }
            }

            :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
              font-weight: 600 !important;
            }

            :deep(.arco-input-wrapper) {
              border: none;
              background-color: transparent;
              font-weight: 600;

              &:hover {
                border: none;
                background-color: transparent;
                color: var(--tant-primary-color-primary-default);
              }
            }

            .filter-btn {
              flex-shrink: 0;
              min-width: 40px;
              padding: 0 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: top;
              background-color: var(--tant-secondary-color-secondary-fill);
              border: 1px solid transparent;
              border-radius: 4px;
              cursor: pointer;
              transition: all .3s;

              .filter-label {
                display: inline-block;
                max-width: 300px;
                overflow: hidden;
                color: var(--tant-text-gray-color-text1-2);
                white-space: nowrap;
                text-overflow: ellipsis;
                vertical-align: top;
              }

              &:hover {
                border-color: var(--tant-primary-color-primary-hover);
              }
            }
          }

          .event-element {
            display: flex;
            align-items: center;
          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .action-left .row-content :deep(.filter-icon) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }

        .left-index {
          display: none;
        }

        .left-handle {
          display: block;
        }
      }
    }

    .drag-ghost {
      background-color: var(--tant-primary-color-primary-fill) !important;
    }

    .row-foot {
      margin: 0;
      padding-left: 34px;
      transition: all .3s;

      .ta-filter-button {
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-hover);
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.custom-edit-btn {
  background-color: transparent;

  &:hover {
    background-color: transparent;
    color: var(--tant-primary-color-primary-hover);
  }
}

:deep(.arco-textarea) {
  border: none;
}
</style>