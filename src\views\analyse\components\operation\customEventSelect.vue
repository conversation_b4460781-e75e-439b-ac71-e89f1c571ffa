<template>
  <!-- 自定义事件筛选 custom -->
  <div ref="handleRef" class="custom-event-container" :style="contentFocused ? {borderBottom: '1px solid var(--tant-primary-color-primary-default)'} : {}">
    <!--输入区域-->
    <div ref="editable" class="custom-item-box" role="textbox" aria-multiline="true" data-slate-editor="true" data-slate-node="value" @keydown="editAreaInput">
      <div v-for="(item,index) in formulaTextList" :key="index">
        <span v-if="formulaTextList.length==1" data-slate-node="text">
          <span
            :key="index"
            :data-key="index"
            data-slate-leaf="true"
            style="min-width: 2px; display: inline-block; height: 100%; vertical-align: top;outline: 0"
            contenteditable="true"
            @focus="() => contentFocused = true">
            {{ item }}
          </span>
        </span>
        <span v-if="index==1" :data-key="index-1" data-slate-node="text">
          <span
            :key="index-1"
            :data-key="index-1"
            data-slate-leaf="true"
            style="min-width: 2px; display: inline-block; height: 100%; vertical-align: top;outline: 0"
            contenteditable="true"
            @focus="() => contentFocused = true">
            {{ formulaTextList[index - 1] }}
          </span>
        </span>
        <span v-if="index>0" :key="index" data-slate-node="element" data-slate-inline="true" data-slate-void="true">
          <div v-if="eventList.length>0" :data-key="index" class="custom-item" @click.stop="() => contentFocused = false">
            <!-- <panelSelect :panel-data="eventList[index-1]" :indicator-list-store="indicatorListStore" @analysis-index-change="panelSelectChange(index-1,$event)"/> -->
            <EventIndicatorSelect :no-fetch-attr="true" :panel-data="eventList[index-1]" @analysis-index-change="panelSelectChange(index-1,$event)"/>
            <eventScreenPopup :info="eventList[index-1]" @event-screen="eventScreen(index-1,$event)"/>
          </div>
        </span>
        <span v-if="index>0" :key="index" data-slate-node="text">
          <span
            :data-key="index"
            data-slate-leaf="true"
            style="min-width: 2px; display: inline-block; height: 100%; vertical-align: top;outline: 0"
            contenteditable="true"
            @focus="() => contentFocused = true"
          >
            {{ item }}
          </span>
        </span>
      </div>
    </div>
    <!-- 键盘 -->
    <div class="handle-box" :style="contentFocused ? {height: '152px'} : {height: 0}" @mousedown.prevent @click.stop>
      <div class="key-board">
        <div class="numbers">
          <a-button v-for="num in '7894561230.'" :key="num" class="atom" :style="getGridArea(num)" @click="simulateKeyPress(num)">{{ num }}</a-button>
        </div>
        <div class="operators">
          <a-button v-for="op in '+-*/()'" :key="op" class="atom" :style="getGridArea(op)" @click="simulateKeyPress(op)">{{ op }}</a-button>
          <a-button class="atom" style="grid-area: Backspace; transition: 0.2s;" @click="simulateKeyPress('Backspace')">
            <icon-arrow-left/>
            <span style="margin-left: 10px;">del</span>
          </a-button>
        </div>
        <div class="hotkeys">
          <div class="editor-keyboard-hotkey">
            <div class="btn___wOGwu" @click="addEvent">
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor">
                <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M1 3a2 2 0 012-2h10a2 2 0 012 2v1h-1V3a1 1 0 00-1-1H3a1 1 0 00-1 1v10a1 1 0 001 1h10a1 1 0 001-1v-2h1v2a2 2 0 01-2 2H3a2 2 0 01-2-2V3zm5 1.5l3.6 8 1.35-4L15 6.722 6 4.5z"></path>
                </svg>
              </svg>
              <span style="margin-left: 2px;">插入指标</span>
            </div>
            <div class="editor-keyboard-code-tips">
                <span>Ctrl</span>
                <span>+E</span>
            </div>
          </div>
          <div class="editor-keyboard-hotkey">
            <div class="btn___wOGwu" @click="clearAll">
              <icon-delete/>
              <span style="margin-left: 2px;">清空</span>
            </div>
            <div class="editor-keyboard-code-tips">
              <span>Ctrl</span>
              <span>+D</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {cloneDeep, omit} from "lodash"
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import eventScreenPopup from "./eventScreenPopup.vue"

const props = defineProps({
  list: {
    type: Array,
    default: () => []
  },
  formula: {
    type: String,
     default: () => `\${0}`
  },
  // 只选事件
  onlyEvent:{
    type:Boolean,
    default:false
  },
})



/**
 * 事件列表
 */
const eventList = ref<any>([])

/**
 * 公式文本
 */
const formulaTextList = ref<string[]>([])

/**
 *
 */
const handleRef = ref()

/**
 * 编辑中
 */
const editable = ref(null);

/**
 * 内容聚焦
 */
const contentFocused = ref(false)

const closeHandle = () => {
  document.addEventListener('click', (e) => {
    if (handleRef.value && !handleRef.value.contains(e.target)) {
      contentFocused.value = false
    }
  })
}

watch(contentFocused, (newVal, oldValue) => {
  if (newVal) {
    closeHandle()
  }
})

const emits = defineEmits(['customFiltersChange','formulaValueChange'])
const panelSelectChange = (index:number,e:any) => {
  const filter = {
    ...eventList.value[index],
    ...e
  }
  const fieldsToRemove = e.type === 'indicator'
    ? ['eventCode', 'eventName', 'eventDisplayName']
    : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

  const cleanedFilter = omit(filter, fieldsToRemove);
  eventList.value[index] = cleanedFilter;
}
const eventScreen = (index: number, e: any) => {
  eventList.value[index].filter = e;
}
watch(formulaTextList, (newValue) => {
  // 确保 eventList 长度与 formulaTextList 长度匹配
  if (newValue.length < eventList.value.length) {
    eventList.value = eventList.value.slice(0, newValue.length);
  }
  const placeholders = Array.from({ length: eventList.value.length }, (_, index) => `\${${index}}`);
  if(eventList.value.length>0){
    const result = formulaTextList.value.map((item, index) => {
        if (index < placeholders.length) {
            return item + placeholders[index];
        }
        return item;
    }).join('');
    emits('formulaValueChange', result);
  }else{
    emits('formulaValueChange', props.formula || '');
  }
}, { deep: true });

// 修改 watch eventList 的逻辑
watch(eventList, (newValue) => {
  emits('customFiltersChange', newValue);
  // 确保 formulaTextList 长度正确
  if (formulaTextList.value.length !== newValue.length + 1) {
    const newFormulaTextList = [...formulaTextList.value];
    while (newFormulaTextList.length > newValue.length + 1) {
      newFormulaTextList.pop();
    }
    while (newFormulaTextList.length < newValue.length + 1) {
      newFormulaTextList.push('');
    }
    formulaTextList.value = newFormulaTextList;
  }
}, { deep: true });
const getGridArea = (char) => {
  const areas = {
    '7': 'x7', '8': 'x8', '9': 'x9',
    '4': 'x4', '5': 'x5', '6': 'x6',
    '1': 'x1', '2': 'x2', '3': 'x3',
    '0': 'x0', '.': 'xx',
    '+': 'add', '-': 'minus', '*': 'multiply', '/': 'divide',
    '(': 'left_bracket', ')': 'right_bracket'
  };
  return {gridArea: areas[char], transition: '0.2s'};
};


/**
 * 设置光标位置
 *
 * @param dataKey 数据列
 * @param position 光标位置
 */
 const setCursorPosition = (dataKey, position) => {
  const span = handleRef.value.querySelector(`span[data-key="${dataKey}"]`);
  const range = document.createRange();
  const selection = window.getSelection();

  // 确保 span 中的文本节点存在
  if (span.firstChild) {
    range.setStart(span.firstChild, position);
    range.collapse(true);

    // 清除任何现有的选区，然后添加我们设置的选区
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
};
/**
 * 键盘输入
 *
 * @param key 键入文本
 */
 const simulateKeyPress = (key) => {
  if (!editable.value) return;

  const selection = window.getSelection();
  const {startContainer} = selection.getRangeAt(0);
  const {startOffset} = selection.getRangeAt(0);

  let dataKey = startContainer?.parentElement?.getAttribute('data-key');
  if (dataKey === undefined || dataKey === null) {
    dataKey = startContainer?.getAttribute('data-key');
  }
  dataKey = Number(dataKey); // 确保 dataKey 是数字类型
  
  const {textContent} = startContainer;

  if (key === 'Backspace') {
    // 处理删除操作
    if (startOffset === 0) {
      // 当前位置在文本开头
      if (dataKey > 0) { // 确保不是第一个文本块
        // 合并当前文本和前一个文本
        const newFormulaTextList = [...formulaTextList.value];
        newFormulaTextList[dataKey - 1] = newFormulaTextList[dataKey - 1] + newFormulaTextList[dataKey];
        newFormulaTextList.splice(dataKey, 1);
        
        // 同步删除对应的事件
        const newEventList = [...eventList.value];
        if (dataKey <= newEventList.length) {
          newEventList.splice(dataKey - 1, 1);
        }
        
        // 更新状态
        formulaTextList.value = newFormulaTextList;
        eventList.value = newEventList;

        // 设置光标位置
        setTimeout(() => {
          setCursorPosition(dataKey - 1, newFormulaTextList[dataKey - 1].length);
        }, 0);
        return;
      }
    }
    // 普通的删除操作
    formulaTextList.value[dataKey] = textContent.slice(0, startOffset - 1) + textContent.slice(startOffset);
    setTimeout(() => {
      setCursorPosition(dataKey, startOffset - 1);
    }, 0);
  } else {
    // 其他按键的处理逻辑保持不变
    formulaTextList.value[dataKey] = textContent.slice(0, startOffset) + key + textContent.slice(startOffset);
    setTimeout(() => {
      setCursorPosition(dataKey, startOffset + 1);
    }, 0);
  }
};
/**
 * 拦截文本数据
 *
 * @param evnet 文本事件
 */
 const editAreaInput = (evnet) => {
  // 允许的按键: 数字、运算符、括号和删除键
  const allowedKeys = [
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    '+', '-', '*', '/', '(', ')'
  ];

  // 允许的控制按键（例如 Backspace、Tab、Arrow 键等）
  const controlKeys = [
    'ArrowLeft', 'ArrowRight'
  ];

  const deleteKeys = [
    'Backspace'
  ];

  // 如果按键不在允许的字符集合中，也不是控制键，就阻止默认行为
  const key = event.key;
  if (allowedKeys.includes(key) || deleteKeys.includes(key)) {
    simulateKeyPress(key)
  }
  if (!controlKeys.includes(key)) {
    event.preventDefault();
  }

}


/**
 * 添加事件
 */
const addEvent = () => {
  const selection = window.getSelection();
  const {startContainer} = selection.getRangeAt(0);

  let dataKey = startContainer?.parentElement?.getAttribute('data-key');
  if (dataKey === undefined || dataKey === null) {
    dataKey = startContainer?.getAttribute('data-key');
  }
  dataKey = Number(dataKey);
  const eventListValue = [...eventList.value];
  const textListValue = [...formulaTextList.value];
  const initData = cloneDeep(props.list[0])
  eventListValue.splice(dataKey + 1, 0, initData);
  textListValue.splice(dataKey + 1, 0, "");
  eventList.value = eventListValue;
  formulaTextList.value = textListValue;
  // 使用 setTimeout 确保文本更新完成后再设置光标位置
  setTimeout(() => {
    // eslint-disable-next-line no-use-before-define
    setCursorPosition(dataKey + 1, 0);
  }, 0);
}
// 
const clearAll = () => {
  eventList.value = []
  formulaTextList.value = ['']
}

/**
 * 页面初始化
 */
onMounted(() => {
  eventList.value = cloneDeep(props.list)
  // formulaTextList.value = props.formula.split(/\$\{\d+\}/)
})
watch(props.formula, ()=> {
  const formula = props.formula || `\${0}`
  formulaTextList.value = formula.split(/\$\{\d+\}/)
},{immediate:true})
</script>

<style scoped lang="less">
.row-word {
  display: inline-block;
  margin: 0 4px;
  color: var(--tant-text-gray-color-text1-2);
  vertical-align: top;
}

.custom-event-container {
  position: relative;
  line-height: 38px;
  border-bottom: none;

  .custom-item-box {
    position: relative;
    outline: none;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    min-height: auto;

    .custom-item {
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
      position: relative;
      top: -1px;
    }
  }

  .handle-box {
    position: absolute;
    top: calc(100% + 2px);
    left: 0px;
    min-width: 380px;
    width: 100%;
    background-color: rgb(255, 255, 255);
    box-shadow: rgba(10, 16, 50, 0.05) 0px 40px 120px, rgba(10, 16, 50, 0.03) 0px 30px 60px, rgba(10, 16, 50, 0.02) 0px 20px 40px;
    border-radius: 4px;
    transition: height 0.1s;
    height: 0px;
    overflow: hidden;
    z-index: 8888;

    .key-board {
      padding: 16px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      max-width: 480px;
      box-sizing: border-box;

      .atom {
        height: 24px;
        padding: 1px 8px;
        font: var(--tant-body-font-body-regular);
        border-radius: var(--tant-border-radius-medium);
        color: var(--tant-secondary-color-secondary-default);
        background-color: var(--tant-secondary-color-secondary-fill);
        border: none;
      }

      .atom:active {
        background-color: var(--tant-secondary-color-secondary-fill-active);
      }

      .numbers {
        display: grid;
        gap: 8px;
        grid-template-columns: 24px 24px 24px;
        grid-template-rows: 24px 24px 24px 24px;
        grid-template-areas:
                    "x7 x8 x9"
                    "x4 x5 x6"
                    "x1 x2 x3"
                    "x0 xx xx";
      }

      .operators {
        display: grid;
        gap: 8px;
        grid-template-columns: 24px 24px;
        grid-template-rows: 24px 24px 24px 24px;
        grid-template-areas:
                    "add minus"
                    "multiply divide"
                    "left_bracket right_bracket"
                    "Backspace Backspace";
      }

      .hotkeys {
        font-size: 14px;
        font-weight: 400;
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .editor-keyboard-hotkey {
          // display: flex;
          // align-items: center;
          margin-bottom: 8px;

          .btn___wOGwu {
            cursor: pointer;
            min-width: 96px;
            height: 32px;
            background: var(--tant-secondary-color-secondary-fill);
            border-radius: 4px;
            color: var(--tant-secondary-color-secondary-default);
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            white-space: nowrap;
          }

          .editor-keyboard-code-tips {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            height: 20px;
            color: var(--tant-text-gray-color-text1-4);
            font: var(--tant-description-font-description-regular);
          }
        }
      }
    }
  }
}


.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>