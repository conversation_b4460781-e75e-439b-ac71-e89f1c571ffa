<template>
  <div>
    <!-- 事件筛选弹窗 -->
    <a-popover v-if="!props.readOnly" trigger="click" position="tl" :popup-visible="popupVisible" style="z-index: 1002;">
      <a-tooltip content="事件筛选" position="top">
        <div class="filter-icon" @click="openPopup">
          <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"
               :fill="props.info.filter?.filters?.length > 0 ? 'var(--tant-primary-color-primary-hover)' : 'currentColor'">
            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 20h4v-4h-4v4zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
            </svg>
          </svg>
        </div>
      </a-tooltip>
      <template #content>
        <div class="ta-formula-filter-popup">
          <div class="ffp-head">添加筛选条件</div>
          <div class="ffp-body">
            <eventQueryFilter v-if="popupVisible" ref="queryFilterRef" :filter="eventQueryfilter" :show-detail-filter="true" @query-filters-change="queryFiltersChange"/>
            <div class="ffp-extra">
                        <span class="ffp-extra-add" @click="add">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path
                                d="M14 14v6l-4 2v-8L4 5V3h16v2l-6 9zM6.404 5L12 13.394 17.596 5H6.404z"></path></svg></svg>
                            添加条件
                        </span>
            </div>
          </div>
          <div class="ffp-foot">
            <a-button class="cancel" @click.stop="() => popupVisible = false">取消</a-button>
            <a-button type="primary" @click="filterDone">完成</a-button>
          </div>
        </div>
      </template>
    </a-popover>
    <a-popover v-if="props.readOnly && eventQueryfilter?.filters?.length>0" trigger="hover" position="tl" style="z-index: 1002;">
      <div class="filter-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor">
          <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 20h4v-4h-4v4zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
          </svg>
        </svg>
      </div>
      <template #content>
        <div class="ta-formula-filter-popup">
          <div class="ffp-head">筛选条件</div>
          <div class="ffp-body">
            <eventQueryFilter ref="queryFilterRef" :disabled="props.readOnly" :filter="eventQueryfilter" :show-detail-filter="true" @query-filters-change="queryFiltersChange"/>
          </div>
        </div>
      </template>
    </a-popover>
    <div class="fixed-modal" :style="{display: popupVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue"
import eventQueryFilter from "./eventQueryFilter.vue"

const props = defineProps({
    info: {
      type: Object,
      default() {
        return {};
      },
    },
    readOnly:{
        type:Boolean,
        default:false
    },
})
const popupVisible = ref(false)

const eventQueryfilter = ref(
    {
        logicalOperation:'and',
        filters:[],
    }
)

const openPopup = () => {
    eventQueryfilter.value = props.info.filter
    popupVisible.value = true
}
watch(() => props.info.filter,() => {
    eventQueryfilter.value = props.info.filter
},{immediate:true,deep:true})
const queryFilterRef = ref()
const add = () => {
    queryFilterRef.value.add()
}

const queryFiltersChange = (v) => {
  eventQueryfilter.value = v
}
const emits = defineEmits(['eventScreen'])
// 筛选条件完成
const filterDone = () => {
    emits('eventScreen',eventQueryfilter.value)
    popupVisible.value = false
}
</script>

<style scoped lang="less">
.fixed-modal{
  position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background-color: transparent;
    visibility: hidden;
}
.ta-formula-filter-popup{
    width: 520px;
    min-height: 156px;
    font-size: 14px;
    .ffp-head {
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        line-height: 20px;
    }
    .ffp-body{
        margin: 24px 0 20px;
    }
    .ffp-extra{
        margin-top: 8px;
        .ffp-extra-add{
            display: inline-block;
            margin-right: 16px;
            padding: 4px;
            color: var(--tant-primary-color-primary-default);
            line-height: 18px;
            border-radius: 2px;
            cursor: pointer;
            transition: all .3s;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
        }
    }
    .ffp-foot{
        padding: 12px 0 0;
        text-align: right;
        :deep(.arco-btn){
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
        }
        .cancel {
            background: transparent;
            margin-right: 8px;

            &:hover {
            background-color: var(--color-secondary);
            }
        }
    }
}
.filter-icon{
    font-size: 16px;
    height: 26px;
    width: 26px;
    text-align: center;
    line-height: 26px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--tant-text-gray-color-text1-3);
    transition: all .3s;
    border: 1px solid transparent;
    background-color: var(--tant-secondary-color-secondary-fill);
    margin-left: 8px;
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}

.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}
</style>