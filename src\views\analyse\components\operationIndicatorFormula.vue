<template>
  <div class="action-row">
    <div class="filter-row-eventRow">
      <div class="action-left">
        <div class="row-content">
          <customEventSelect
              :list="indicatorData.customList"
              :formula="indicatorData.formula"
              @custom-filters-change="customFiltersChange($event)"
              @formula-value-change="formulaValueChange($event)"/>
        </div>
      </div>
      <div class="action-right">
        <a-space align="center">
          <a-tooltip content="添加筛选" position="top">
            <a-button class="btn-bg btn-26" @click="add()">
              <template #icon>
                <icon-filter/>
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </div>
    </div>
    <eventQueryFilter
        :ref="el => queryFilterRefs = el"
        :filter="indicatorData.filter"
        :show-detail-filter="true"
        @query-filters-change="queryFiltersChange($event)"/>
  </div>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";
import _ from 'lodash';
import {Indicator} from "@/api/analyse/type";
import customEventSelect from "@/views/analyse/components/operation/customEventSelect.vue";
import eventQueryFilter from "@/views/analyse/components/operation/eventQueryFilter.vue"

const props = defineProps({
  indicatorListStore: {
    type: Array,
    default() {
      return [];
    },
  },
  initData: {
    type: Object,
  },
})

const indicatorData = ref<any>()

const queryFiltersChange = (v) => {
  indicatorData.value.filter = v;
}

const init = async () => {
  const indicatorList = _.cloneDeep(props.indicatorListStore);
  const defaultIndicator = {
    name: indicatorList[0]?.name,
    code: indicatorList[0]?.code,
    type: 'operation',
    displayType: {},
    displayName: `${indicatorList[0]?.displayName}`,
    unitName: '',
    customList: [
      {
        eventName: indicatorList[0]?.name,
        eventDisplayName: indicatorList[0]?.displayName,
        eventCode: indicatorList[0]?.code,
        type: 'indicator',
        filter: {}
      },
    ],
    filter: {}
  }
  // 
  if (props.initData?.eventList?.length > 0) {
    defaultIndicator.customList = _.cloneDeep(props.initData?.eventList).map(item => {
      return {
        eventName: item.indicatorName,
        eventDisplayName: item.indicatorDisplayName,
        eventCode: item.indicatorCode,
        type: item.type,
        filter: item.filter
      }
    })
  }
  indicatorData.value = _.cloneDeep(defaultIndicator)
}

init()

const emits = defineEmits(['indicatorsChange', 'deleteList', 'openEditModal'])

const indicator = ref<Indicator[]>([])

const customFiltersChange = (e: any) => {
  indicatorData.value.customList = e
}

const formulaValueChange = (e: any) => {
  indicatorData.value.formula = e
}

// 添加并列条件
const queryFilterRefs = ref<any>()
const add = () => {
  queryFilterRefs.value.add()
}

watch(indicatorData, (newValue, oldValue) => {
  indicator.value = {
    displayName: newValue.displayName,
    displayType: newValue.displayType,
    name: newValue.name,
    type: newValue.type,
    formula: newValue.formula,
    eventList: newValue.customList.map(item => {
      return {
        indicatorName: item.eventName,
        indicatorDisplayName: item.eventDisplayName,
        indicatorCode: item.eventCode,
        type: item.type,
        filter: item.filter
      }
    }),
    filter: newValue.filter
  }
  emits('indicatorsChange', indicator.value)
}, {deep: true, immediate: true})

</script>

<style scoped lang="less">
.action-row {
  position: relative;
  height: auto;
  width: 100%;
  min-height: 24px;
  line-height: 24px;
  padding-right: 24px;
  padding-left: 24px;

  .action-left {
    align-items: flex-start;
    height: auto;
    display: flex;

    .row-content {
      flex-grow: 1;
      box-sizing: border-box;
      padding: 4px 0;

      .rename {
        min-width: 80px;
        max-width: calc(100% - 50px);
        height: 24px;
        padding: 0;
        line-height: 24px;
        background: inherit;
        margin-bottom: 6px;
        // font-weight: 600;
        font-size: 14px;
        display: flex;
        align-items: center;

        .placeholder {
          max-width: 260px;
          display: inline-block;
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
          overflow: hidden;
          font-size: 14px;
          // white-space: pre;
          // vertical-align: middle;
          &:hover {
            color: var(--tant-primary-color-primary-default);
          }
        }

        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
          font-weight: 600 !important;
        }

        :deep(.arco-input-wrapper) {
          border: none;
          background-color: transparent;
          font-weight: 600;

          &:hover {
            border: none;
            background-color: transparent;
            color: var(--tant-primary-color-primary-default);
          }
        }
      }

      .event-item {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 4px 0;
        overflow: hidden;
        line-height: 32px;
        white-space: normal;
      }
    }
  }

  .action-right {
    position: absolute;
    top: 0;
    right: 24px;
    min-width: 40px;
    height: 36px;
    padding-top: 0 !important;
    display: flex;
    align-items: center;
    opacity: 0;
    transition: opacity .3s;
  }

  .filter-btn {
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;

    .btn-icon {
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      margin-right: 5px;
    }

    .filter-label {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }

    &:hover {
      border-color: var(--tant-primary-color-primary-hover);
    }
  }

  .row-word {
    display: inline-block;
    margin: 0 4px;
    color: var(--tant-text-gray-color-text1-2);
    vertical-align: top;
  }

  &:hover {
    background-color: var(--tant-fill-color-fill1-2);

    :deep(.filter-btn) {
      background: #fff;
    }

    :deep(.filter-icon) {
      background: #fff;
    }

    .sub-action-left :deep(.filter-btn) {
      background: #fff;
    }
    :deep(.select-btn) {
      background: #fff;
    }
    .action-right {
      opacity: 1;
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>