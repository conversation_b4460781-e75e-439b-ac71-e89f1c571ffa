import {UserDto} from "@/api/authorize/type";
import {FolderDto} from "@/api/folder/type";
import {DashboardDto} from "@/api/dashboard/type";
import {ObjectPermissionType} from "@/api/enum";

/**
 * 看板空间实体
 */
export interface SpaceDto {

  /**
   * 对象权限
   */
  authority: ObjectPermissionType;

  /**
   * 空间编码
   */
  spaceId: string;

  /**
   * 空间名称
   */
  name: string;

  /**
   * 空间图标
   */
  icon: string;

  /**
   * 筛选条件, 非必传
   * todo，筛选条件的类型
   */
  filter?: any;

  /**
   * 次序
   */
  order: number;

  /**
   * 创建人员
   */
  creator: UserDto;

  /**
   * 文件夹列表
   */
  folders?: FolderDto[];

  /**
   * 看板列表
   */
  dashboards?: DashboardDto[];

  /**
   * 是否允许删除
   */
  isDeletable: boolean;

  /**
   * 创建时间，毫秒时间戳
   */
  createTime: number;
}

/**
 * 看板空间保存实体
 */
export interface SpaceSaveDto {

  /**
   * 空间编码
   */
  spaceId?: string;

  /**
   * 空间名称
   */
  name: string;

  /**
   * 空间图标
   */
  icon?: string;

  /**
   * 筛选条件, 非必传
   * todo，筛选条件的类型
   */
  filter?: any;

  /**
   * 次序
   */
  order?: number;

  /**
   * 文件夹列表
   */
  folders?: string[];

  /**
   * 看板列表
   */
  dashboards?: string[];
}