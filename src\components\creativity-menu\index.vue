<script setup lang="ts">
import {ref} from "vue";

// const showItem1 = ref<boolean>(true);
// const showItem2 = ref<boolean>(true);
// const showItem3 = ref<boolean>(true);
const selectedMenu = ref('首页');
const selectMenu = (menuName: string) => {
  selectedMenu.value = menuName;
};
</script>

<template>
  <a-menu
      :style="{ width: '340px', height: '100%' }"
      :default-open-keys="['0']"
      :default-selected-keys="['0_2']"
      show-collapse-button
      breakpoint="xl"
  >
    <div class="title">
      <!--      <div class="items" @click="showItem1 = !showItem1">-->
      <div class="menu"  :class="{ 'on': selectedMenu === '首页' }">
        <div>
          <icon-home/>
        </div>
        <div
            class="menu-name"

            @click="selectMenu('首页')"
        >首页
        </div>

      </div>
      <!--      <div>-->
      <!--        <a-menu-item v-show="showItem1" key="0">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          游戏大盘-->
      <!--        </a-menu-item>-->
      <!--        <a-menu-item v-show="showItem1" key="1">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          整体数据-->
      <!--        </a-menu-item>-->
      <!--        <a-menu-item v-show="showItem1" key="2">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          财务数据-->
      <!--        </a-menu-item>-->
      <!--      </div>-->
<!--      <div class="menu" @click="showItem2 = !showItem2">-->
      <div class="menu"   :class="{ 'on': selectedMenu === '项目库' }">
        <div>
          <icon-layers/>
        </div>
        <div
            class="menu-name"

            @click="selectMenu('项目库')"
        >项目库
        </div>
      </div>
      <!--      <div>-->
      <!--        <a-menu-item v-show="showItem2" key="4">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          A项目-->
      <!--        </a-menu-item>-->
      <!--        <a-menu-item v-show="showItem2" key="5">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          B项目-->
      <!--        </a-menu-item>-->
      <!--        <a-menu-item v-show="showItem2" key="6">-->
      <!--          <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--          C项目-->
      <!--        </a-menu-item>-->
      <!--      </div>-->
<!--      <div class="menu" @click="showItem3 = !showItem3">-->
      <div class="menu" :class="{ 'on': selectedMenu === '审核管理' }">
        <div>
          <icon-storage/>
        </div>
        <div
            class="menu-name"

            @click="selectMenu('审核管理')"
        >审核管理</div>
        <div class="inform">新增权限通知</div>
      </div>
      <!--      <div>-->
      <!--      <a-menu-item v-show="showItem3" key="7">-->
      <!--        <template #icon><img src="/icon/file.svg" class="space-item" alt=""/></template>-->
      <!--        App审核-->
      <!--      </a-menu-item>-->
      <!--      </div>-->
    </div>
  </a-menu>
</template>

<style scoped lang="less">
.title {
  padding-left: 10px;
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: var(--tant-text-gray-color-text1-1);
  gap: 5px;

  .menu {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 0;
    gap: 4px;
    .menu-name{
      cursor: pointer;
      font-weight:bolder;
    }
    .inform {
      color: rgb(255, 163, 115);
      border-radius: 2px;
      background-color: rgba(253, 94, 0, 0.06);
      font-size: 11px;
      margin-left: 5px;
    }
  }
    .on {
      color: var(--tant-primary-color-primary-default);
    }

}
</style>