import {ChartType, ObjectPermissionType, RefreshType} from "@/api/enum";
import {UserDto} from "@/api/authorize/type";
import {FolderDto} from "@/api/folder/type";
import {SpaceDto} from "@/api/space/type";

/**
 * 看板内元素类型
 */
export enum LayoutConfigObjectType {

  /**
   * 便签
   */
  NOTE = 0,

  /**
   * 报表
   */
  REPORT = 1
}

export interface LayoutConfigParam {

}


export interface EventAnalyseLayoutConfigParam extends LayoutConfigParam {

  /**
   * 图表类型
   */
  chartType: ChartType;

}


/**
 * 看板内元素布局
 */
export interface LayoutConfig {

  /**
   * 对象类型
   */
  objectType: LayoutConfigObjectType;

  /**
   * 对象编码
   */
  objectCode: string;

  /**
   * 配置参数
   */
  configParams: LayoutConfigParam;

  /**
   * 横向位置
   */
  x: number;

  /**
   * 纵向位置
   */
  y: number;

  /**
   * 初始宽度
   */
  w: number;

  /**
   * 初始高度
   */
  h: number;

  /**
   * 允许拖拽
   */
  isDraggable: boolean;
}


/**
 * 看板实体
 */
export interface DashboardDto {

  /**
   * 对象权限
   */
  authority: ObjectPermissionType;

  /**
   * 看板编码
   */
  dashboardId: string;

  /**
   * 看板名称
   */
  name: string;

  /**
   * 看板描述
   */
  description: string;

  /**
   * 次序
   */
  order: number;

  /**
   * 标签
   */
  tag: string[]

  /**
   * 固定时区
   */
  timeZone: string;

  /**
   * 近似计算
   */
  approximateOn: boolean;

  /**
   * 更新周期类型
   */
  refreshType: RefreshType;

  /**
   * 更新频率，根据更新周期确定
   */
  refreshSchedule: string;

  /**
   * 数据更新时间，毫秒时间戳
   */
  refreshTime: number;

  /**
   * 所属目录
   */
  folder?: FolderDto;

  /**
   * 所属空间
   */
  space: SpaceDto;

  /**
   * 页面布局
   */
  uiConfig: LayoutConfig[];

  /**
   * 筛选条件, 非必传
   * todo，筛选条件的类型
   */
  filter?: any;

  /**
   * 查询条件, 非必传
   * todo，查询条件的类型
   */
  query?: any;

  /**
   * 创建人员
   */
  creator: UserDto;

  /**
   * 创建时间，毫秒时间戳
   */
  createTime: number;
}


/**
 * 看板保存实体
 */
export interface DashboardSaveDto {

  /**
   * 对象权限
   */
  authority?: ObjectPermissionType;

  /**
   * 看板编码
   */
  dashboardId?: string;

  /**
   * 看板名称
   */
  name?: string;

  /**
   * 看板描述
   */
  description?: string;

  /**
   * 次序
   */
  order?: number;

  /**
   * 标签
   */
  tag?: string[]

  /**
   * 固定时区
   */
  timeZone?: string;

  /**
   * 近似计算
   */
  approximateOn?: boolean;

  /**
   * 更新周期类型
   */
  refreshType?: RefreshType;

  /**
   * 更新频率，根据更新周期确定
   */
  refreshSchedule?: string;

  /**
   * 数据更新时间，毫秒时间戳
   */
  refreshTime?: number;

  /**
   * 所属目录
   */
  folderId?: string;

  /**
   * 所属空间
   */
  spaceId?: string;

  /**
   * 页面布局
   */
  uiConfig?: LayoutConfig[];

  /**
   * 筛选条件, 非必传
   * todo，筛选条件的类型
   */
  filter?: any;

  /**
   * 查询条件, 非必传
   * todo，查询条件的类型
   */
  query?: any;
}
// copy
export interface dashboardCopyRequest {
  /**
   * 看板id，要复制的看板
   */
  dashboardId: string|undefined;
  /**
   * 看板名称
   */
  name: string|undefined;
  /**
   * 看板目录id，复制到的看板目录id
   */
  folderId?: string|undefined;
  /**
   * 复制，true是复制 false引用
   */
  reportCopy: boolean;
  /**
   * 空间id，复制到的空间id
   */
  spaceId: string|undefined;
  [property: string]: any;
}