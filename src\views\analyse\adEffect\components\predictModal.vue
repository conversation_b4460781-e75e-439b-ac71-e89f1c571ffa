<template>
    <a-modal v-model:visible="modalVisible" :width="800" title-align="start" title="回本周期预测" :footer="false" @cancel="closeModal">
        <div class="handle-header">
            <div class="right">
                <a-radio-group v-model:model-value="detailParams.type" type="button">
                    <a-radio value="table">数据表</a-radio>
                    <a-radio value="line">折线图</a-radio>
                </a-radio-group>
            </div>
        </div>
        <a-spin :loading="loading" style="width: 100%;min-height: 400px;">
            <a-table v-if="detailParams.type === 'table'" :columns="columns" :data="tableData" :pagination="false" size="small" :scroll="scroll">
                <template #predict="{ record }">
                    <span v-if="record.predict === null" style="color: red;">无法回本</span>
                    <span v-if="record.predict === 0">无法预测</span>
                    <span v-if="record.predict >0">{{ record.predict }}</span>
                </template>
            </a-table>
            <div v-else class="chart-content">
                <Chart :option="chartOption"/>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import useChartOption from "@/hooks/chart-option";
import {getRoiPredictTrend} from "@/api/analyse/api"
import {useSessionStorage} from '@vueuse/core'

const appIds = useSessionStorage("app-id-list", []);
interface Props {
    filterParams:any
}
const props = defineProps<Props>();
const scroll = {
  y: '400px',
  x: '100%'
}
const detailParams = reactive({
    type:'table',
})
const modalVisible = ref(false)

const tableData = ref()

const ySeries = ref<any>([])
const xAxis = ref<any>([])
const legendData = ref<any>([])
const {chartOption} = useChartOption(() => {
  return {
      grid: {
        left: '0%',
        right: '0%',
        top: '40',
        bottom: '20',
        containLabel: true
      },
      legend: {
        data: legendData.value,
        bottom: '0',
        type: 'scroll', // 设置图例为滚动类型
      },
      //  
      xAxis: {
        type: 'category',
        data: xAxis.value,
        axisLine:{
          show:true
        },
        axisLabel:{
          color:'#8E8E8E'
        },
        axisTick:{
          show:false
        },
      },
      yAxis: {
        type: 'value',
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        enterable: true,
        confine:true,
        formatter:  (params) => {
            let tooltipContent = '';
            params.forEach(item => { 
                const dayValue = tableData.value[item.dataIndex] ? tableData.value[item.dataIndex]?.day : '';
                const logData = tableData.value[item.dataIndex] ? tableData.value[item.dataIndex]?.updateLog : null;
                const itemDataValue = item.data === null ? '-' : item.data;
                tooltipContent += `
                    <span>${item.axisValue} DAY${dayValue}</span>
                    <div style="display: flex; align-items: center;justify-content: space-between;">
                        <div>
                            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${item.color};margin-right:4px;"></span>
                            <span>回本周期</span>
                        </div>
                        <span>${logData ? item.data.value : itemDataValue}</span>
                    </div>
                `;
                if (logData) {
                    tooltipContent += `
                    <div style="min-width:200px">
                        <p>更新人：${logData.creator || ''}</p>
                        <p>更新版本：${logData.appVersion}</p>
                        <p>更新内容：</p>
                        ${logData.content}
                    </div>`;
                }
            });
            return tooltipContent;
        }
      },
      series:ySeries.value
    };
});
const loading = ref(false)

const columns = ref<any>([
    {
        title: '日期',
        dataIndex: 'date',
        minWidth: 140,
        slotName: 'date',
    },
    {
        title: '回本周期',
        dataIndex: 'predict',
        minWidth: 140,
        slotName: 'predict',
    },
])

const predictDate = ref('')
const groupValue = ref('')
const getData = async () => {
    loading.value = true
    const data = {
        appId:appIds.value.join(','),
        date:predictDate.value,
        country:props.filterParams.country.join(','),
        group:props.filterParams.group,
        adFilter:props.filterParams.adFilter,
        dateType:props.filterParams.dateType,
        groupValue:groupValue.value
    }
    try {
        await getRoiPredictTrend(data).then(res => {
            tableData.value = res
            xAxis.value = tableData.value.map(item => item.date)
            ySeries.value = [
                {
                    name:'回本周期',
                    type: 'line',
                    stack: 'Total',
                    data:tableData.value.map(item => 
                        item.updateLog ? { value: item.predict, itemStyle: { color: 'yellow' } } : item.predict
                    )
                }
            ]
        })
    } catch (error) {
        console.log(error);
    }finally {
        loading.value = false
    }
}

const openModal = async (obj) => {
    predictDate.value = obj.date
    const nameMap = {
        1: obj.channel,
        2: obj.campaign,
        3: obj.adset,
        4: obj.ad
    };
    const name = nameMap[props.filterParams.group]
    groupValue.value = name || ''
    detailParams.type = 'line'
    tableData.value = []
    modalVisible.value = true
    await getData()
}

const closeModal = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.handle-header{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 12px;
    // .right{
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    // }
}
.chart-content{
    width: 100%;
    height: 400px;
}
</style>