<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
                <div v-if="osName === 'android'" class="filter-item">
                    <a-space>
                        <a-button :loading="apkPackLoading" class="button" type="primary" @click="(ev) => buildApk(ev, false)">
                            打APK
                        </a-button>
                        <a-button :loading="aabPackLoading" class="button" type="primary" @click="(ev) => buildApk(ev, true)">
                            打AAB
                        </a-button>
                    </a-space>
                </div>
                <div v-else class="filter-item">
                    <a-space>
                        <a-button class="button" type="primary" :loading="applySettingLoading" @click="(ev) => iosFuncExec(ev, 'applySetting')">
                            配置SDK
                        </a-button>
                        <a-button class="button" type="primary" :loading="openXcodeLoading" @click="(ev) => iosFuncExec(ev, 'openXcode')">
                            打开XCODE
                        </a-button>
                    </a-space>
                </div>
            </div>
        </div>
        <div id="pack-body" v-if="osName==='android'" class="page-body ">
            <pack-android  ref="androidPackRef" @send-message="sendMessage" @update-loading="updateLoading"/>
        </div>
        <div id="pack-body" v-else class="page-body ">
            <pack-ios ref="iosPackRef" @send-message="sendMessage" @update-loading="updateLoading"/>
        </div>
    </div>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import {computed, nextTick, onMounted, reactive, ref} from "vue";
import selectApp from "@/components/selected-game-app/index.vue"
import packAndroid from "@/views/operation/pack/packCenter/components/packAndroid.vue"
import packIos from "@/views/operation/pack/packCenter/components/packIos.vue"
import {useEventBus, useWebSocket} from '@vueuse/core'
import {Message} from '@arco-design/web-vue';
import {LocalStorageEventBus} from "@/types/event-bus";

const route = useRoute();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const baseWsURL = process.env?.BASE_PACK_URL
const token = localStorage.getItem('token');
const retriesNum = process.env.NODE_ENV === 'development' ? 1 : -1

const {status, data, send, open, close} = useWebSocket(`${baseWsURL}/api/pack/ws?token=${token}&server_type=`, {
    autoReconnect: {
        retries:retriesNum,
        delay: 2000,
        onFailed() {
            console.error('重试后无法连接 WebSocket！')
        },
    },
    onConnected(ws) {
        console.debug('websocket 连接成功！')
    },
    onDisconnected(ws, event) {
        console.warn('websocket 连接断开连接！', ws)
    },
    onError(ws, event) {
        console.error('websocket 连接错误！', ws)
    },
    onMessage(ws, event) {
        if (event.type === 'message') {
            if (event.data instanceof Blob) {
                downloadFile(event.data)
            } else {
                const message = JSON.parse(event.data)
                if (osName.value === 'android') {
                    androidPackRef.value.handleMessage(message)
                } else {
                    iosPackRef.value.handleMessage(message)
                }
            }
        }
    }
})
const file_ws = useWebSocket(`${baseWsURL}/api/pack/ws?token=${token}&clb_type=file&server_type=`, {
    autoReconnect: {
        retries: retriesNum,
        delay: 2000,
        onFailed() {
            console.error('重试后无法连接 WebSocket！')
        },
    },
    onConnected(ws) {
        console.debug('websocket 连接成功！')
    },
    onDisconnected(ws, event) {
        console.warn('websocket 连接断开连接！', ws)
    },
    onError(ws, event) {
        console.error('websocket 连接错误！', ws)
    },
    onMessage(ws, event) {
        if (event.type === 'message') {
            if (event.data instanceof Blob) {
                downloadFile(event.data)
            } else {
                const message = JSON.parse(event.data)
                if (osName.value === 'android') {
                    androidPackRef.value.handleMessage(message)
                } else {
                    iosPackRef.value.handleMessage(message)
                }
            }
        }
    }
})
const log_ws = useWebSocket(`${baseWsURL}/api/pack/ws?token=${token}&clb_type=log&server_type=`, {
    autoReconnect: {
        retries: retriesNum,
        delay: 2000,
        onFailed() {
            console.error('重试后无法连接 WebSocket！')
        },
    },
    onConnected(ws) {
        console.debug('websocket 连接成功！')
    },
    onDisconnected(ws, event) {
        console.warn('websocket 连接断开连接！', ws)
    },
    onError(ws, event) {
        console.error('websocket 连接错误！', ws)
    },
    onMessage(ws, event) {
        if (event.type === 'message') {
            if (event.data instanceof Blob) {
                downloadFile(event.data)
            } else {
                const message = JSON.parse(event.data)
                if (osName.value === 'android') {
                    androidPackRef.value.handleMessage(message)
                } else {
                    iosPackRef.value.handleMessage(message)
                }
            }
        }
    }
})
const osName = ref('')
const androidPackRef = ref()
const iosPackRef = ref()

const tmpFile = ref({})

const loading = ref(false)
const apkPackLoading = ref(false)
const aabPackLoading = ref(false)
const applySettingLoading = ref(false)
const openXcodeLoading = ref(false)

const configIndex = ref(0)
const cpuLibCheckedAll = ref(true)
const cpuLibIndeterminate = ref(false)
const androidPermissionsOptions = ref<Array<object>>([])
const drawerTab = ref('')
const drawerName = ref('')
const androidSdkVerOptions = ref([])
const uploadFileModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    method: '',
    tips: '',
    accept: ''
})
const contentShowModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    content: ''
})

onMounted(() => {
    const appData = JSON.parse(sessionStorage.getItem('app-data') || '{}')
    osName.value = appData?.osName
    loadSubPage(appData)
})

localStorageEventBus.on((name, value) => {
    if (name === 'app-data') {
        osName.value = value?.osName
        loadSubPage(value)
    }
})

const loadSubPage = (appData: object) => {
    nextTick(() => {
        if (osName.value === 'android') {
            if (androidPackRef.value) {
                androidPackRef.value.refreshPage(appData)
            }
        } else {
            if (iosPackRef.value) {
                iosPackRef.value.refreshPage(appData)
            }
        }
    })
}

const downloadFile = async (msg: Blob) => {
    // 将 Blob 转换为 ArrayBuffer
    const arrayBuffer = await msg.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    // 使用 TextDecoder 解码为字符串
    const decoder = new TextDecoder('utf-8');
    const decodedString = decoder.decode(uint8Array);

    // 寻找 JSON 结束位置
    const splitPos = decodedString.indexOf('}') + 1;
    if (splitPos === 0) {
        console.error("未找到有效的JSON结束符");
        return;
    }

    // 提取头部信息
    const headerJson = decodedString.substring(0, splitPos);
    let header;
    try {
        header = JSON.parse(headerJson);
    } catch (e) {
        console.error("解析JSON头部失败", e);
        return;
    }
    if (!header["file_name"]) {
        console.error("未找到文件名");
        return;
    }
    if (!header["chunk"]) {
        console.error("未找到内容序号");
        return;
    }
    if (!header["total_chunks"]) {
        console.error("未找到内容总序号");
        return;
    }
    if (!tmpFile.value.hasOwnProperty(header["file_name"])) {
        tmpFile.value[header["file_name"]] = {}
    }
    // 文件数据部分
    const fileData = uint8Array.slice(splitPos);
    console.log(fileData.length)
    tmpFile.value[header["file_name"]][header["chunk"]-1] = fileData

    if (header.params) {
        if (header["chunk"] < header["total_chunks"]) {
            const reqData = JSON.parse(atob(header.params))
            reqData["payload"]["chunkIndex"] = header["chunk"]
            send(JSON.stringify(reqData))
        }
    }

    // 如果所有块都已接收，则重组文件并下载
    if (Object.keys(tmpFile.value[header["file_name"]]).length === header["total_chunks"]) {
        const finalBuffer = concatUint8Arrays(tmpFile.value[header["file_name"]]);
        delete tmpFile.value[header["file_name"]]; // 清理缓存

        // 创建 Blob 并下载
        try {
            const blob = new Blob([finalBuffer], { type: 'application/octet-stream' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = header["file_name"];

            // 触发下载
            document.body.appendChild(a);
            a.click();

            // 清理
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (e) {
            console.error(e)
        }
    }
}

const concatUint8Arrays = (arrays: any): Uint8Array => {
    // 计算总长度
    let totalLength = 0;
    for (const arr of Object.keys(arrays)) {
        totalLength += arrays[arr].length;
    }

    // 创建新的 Uint8Array 并复制数据
    const result = new Uint8Array(totalLength);
    let currentOffset = 0;
    for (let i = 0; i < Object.keys(arrays).length; i++) {
        result.set(arrays[i], currentOffset);
        currentOffset += arrays[i].length;
    }
    return result;
}

const buildApk = (event: MouseEvent, isAab: boolean = false) => {
    androidPackRef.value.buildApk(event, isAab)
}

const iosFuncExec = (event: MouseEvent, funcName: string) => {
    iosPackRef.value.iosFuncExec(event, funcName)
}

const sendMessage = (data: any) => {
    send(data)
}

const updateLoading = (keyName: string, value: boolean) => {
    switch (keyName) {
        case 'apkPackLoading':
            apkPackLoading.value = value
            break
        case 'aabPackLoading':
            aabPackLoading.value = value
            break
        case 'applySettingLoading':
            applySettingLoading.value = value
            break
        case 'openXcodeLoading':
            openXcodeLoading.value = value
            break
    }
}
</script>

<style scoped lang="less">
.page {
    .page-body {
        padding: 24px 16px;
        position: relative;
    }
    :deep(.arco-drawer-mask) {
        background-color: var(--color-mask-bg);
    }

    .border-mini{
        border-radius: 2px;
    }
}
</style>
