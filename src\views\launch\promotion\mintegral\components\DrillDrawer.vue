<template>
    <a-drawer width="90%" :visible="modalVisible" title="数据下钻" unmountOnClose :footer="false" class="drill-drawer" @cancel="() => modalVisible = false">
        <template #title>
            {{ modalTitle }}
            <DateRangePicker v-model="dateTime" />
        </template>
        <a-spin :loading="loading" style="width: 100%;height: 100%;display: flex;flex-direction: column;">
            <!-- <div class="filter-box">
                <div class="text">筛选</div>
                <DateRangePicker v-model="dateTime" />
            </div> -->
            <div class="filter-box">
                <div class="text">未指定<span style="margin: 0 4px;">></span></div>
                <a-select
                    v-model="selectValue"
                    allow-clear
                    style="width: 180px;">
                    <a-option v-for="item in dropdownList" :key="item.value" :value="item.value">{{ item.label }}</a-option>
                </a-select>
            </div>
            <div class="data-content">
                <div class="table-wrap">
                    <div class="wrap-left">
                        <div class="text">详细数据</div>
                    </div>
                    <div class="wrap-right">
                        <a-button class="br4" style="margin-left: 12px;">
                            <template #icon>
                                <icon-refresh />
                            </template>
                            <template #default>刷新</template>
                        </a-button>
                        <a-button class="br4" style="margin-left: 12px;">
                            <template #icon>
                                <icon-download />
                            </template>
                            <template #default>导出</template>
                        </a-button>
                    </div>
                </div>
                <div class="table-area">
                    <a-table
                        :columns="columns"
                        :data="tableData"
                        :hoverable="true"
                        :table-layout-fixed="true"
                        :column-resizable="true"
                        :scroll="scroll"
                        :pagination="false"
                        :summary="true"
                    >
                        <template #dirllName="{ record,rowIndex }">
                            <div class="cell-content">
                                <div class="text">{{ record.dirllName }}</div>
                                <div>
                                    <a-trigger trigger="hover" position="rt">
                                        <icon-down-circle style="cursor: pointer;"/>
                                        <template #content>
                                            <div class="trigger-content">
                                                <div class="tooltip-icon-list-header">
                                                数据下钻
                                                </div>
                                                <div class="dropdown-list-content">
                                                    <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                                                        <div class="dropdown-list-item">{{ item.label }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </a-trigger>
                                </div>
                            </div>
                        </template>
                        <template #summary-cell="{ column,record,rowIndex }">
                            <div v-if="column.dataIndex === 'dirllName'">
                                <div class="text">汇总</div>
                            </div>
                            <div v-else>{{record[column.dataIndex]}}</div>
                        </template>
                    </a-table>
                    <div class="pagination">
                        <a-pagination :total="total" show-total @change="pageChange"/>
                    </div>
                </div>
            </div>
        </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import {reactive, ref,watch,inject} from "vue";
import dayjs from 'dayjs';
import DateRangePicker from "@/components/date-quick-picker/index.vue";
import {mintegralDrillList} from "@/views/launch/promotion/components/promotionData"

const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref('数据下钻')

const today = dayjs();
const dateTime = ref(inject('dateTime') as any[])
const selectValue = ref('')
const dropdownList = ref<any>([])
const total = ref(0)
const tableData = ref<any>([
    {
        dirllName: 'mockName1',
        cost: 1000,
        impressions: 1000,
        cpm: 1000,
        clicks: 1000,
        cpc: 1000,
        ctr: 1000,
        conversions: 1000,
    },
    {
        dirllName: 'mockName2',
        cost: 1000,
        impressions: 1000,
        cpm: 1000,
        clicks: 1000,
        cpc: 1000,
        ctr: 1000,
        conversions: 1000,
    },
])
const columns = ref<any>([
    { title: '产品', dataIndex: 'dirllName',slotName:'dirllName',width:250,fixed:'left' },
    { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '千次展示成本', dataIndex: 'cpm',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
    { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} }
])
// Table页面滑动
const scroll = {
    y: 'calc(100% - 56px)'
};

const pageChange = () => {
}
const openModal = async (obj?:any) => {
    dropdownList.value = mintegralDrillList[obj?.tab]
    selectValue.value = obj?.value
    // loading.value = true
    modalVisible.value = true
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
:global(.drill-drawer .arco-drawer-title) {
    width: 100%!important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.date-box{
    width: 100%;
    text-align: right;
}
.filter-box{
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 16px;
    .text{
        flex-shrink: 0;
        width: 60px;
    }
}
:deep(.page-content){
    height: 100%;
}
.data-content{
    flex: 1 1 0%;
    min-height: 0;
    display: flex;
    flex-direction: column;
}
.table-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    .wrap-left{
        .text{
        color: rgb(var(--gray-10));
        font-weight: 500;
        }
    }
    .wrap-right{
    }
}
.table-area {
    flex: 1 1 0%;
    min-height: 0;
    display: flex;
    flex-direction: column;
}
.br4{
    border-radius: 4px;
}
.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
}
.cell-content{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .text{
        color: #0b75ff;
        background: transparent;
        cursor: pointer;
        transition: color .2s ease;
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
        text-overflow: ellipsis;
    }
}
.trigger-content{
    background: #fff;
    color: #000;
    border-radius: 6px;
    border: none !important;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2) !important;
    .tooltip-icon-list-header{
        padding: 7px 16px;
        font-size: 14px;
        font-weight: 600;
    }
    .dropdown-list-content{
        padding: 0;
        .dropdown-list-item-wrapper{
        margin: 0;
        padding: 0;
        &:hover{
            background-color: #f9f9f9;
        }
        .dropdown-list-item{
            cursor: pointer;
            min-width: 100px;
            padding: 6px 15px !important;
        }
        }
    }
}
</style>