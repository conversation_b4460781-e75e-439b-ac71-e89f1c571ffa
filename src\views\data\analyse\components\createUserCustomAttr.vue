

<template>
  <div class="body">
    <div class="tant-next-commonHead">
      <div class="tant-next-commonHead-title">添加用户属性</div>
    </div>
    <a-form ref="formRef" :model="form" :rules="rules" class="form">
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="name" label="属性名" validate-trigger="blur" label-col-flex="100px">
              <a-input v-model="form.name" class="form-input" placeholder="请输入" style="width: 360px;"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="displayName" label="显示名" validate-trigger="blur" label-col-flex="100px">
              <a-input v-model="form.displayName" class="form-input" placeholder="请输入" style="width: 360px;"/>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="dataType" label="数据类型" label-col-flex="100px">
              <a-select v-model="form.dataType" placeholder="请选择" style="width: 360px;">
                <template v-if="form.dataType!==''" #prefix>
                  <img v-if="form.dataType==='string'" src="/icon/text.svg" alt="textIcon">
                  <img v-if="form.dataType==='int' || form.dataType==='float'" src="/icon/number.svg" alt="numberIcon">
                  <img v-if="form.dataType==='variant'" src="/icon/objectGroup.svg" alt="ObjectGroupIcon">
                  <img v-if="form.dataType==='boolean'" src="/icon/boolean.svg" alt="booleanIcon">
                  <img v-if="form.dataType==='array'" src="/icon/list.svg" alt="listIcon">
                  <img v-if="form.dataType==='date' || form.dataType==='datetime'" src="/icon/time.svg" alt="dateIcon">
                </template>
                <div v-for="item in dataOptions " :key="item.value">
                  <a-option class="optionDate" :value="item.value">
                    <img :src="getIconForItem(item.label)" alt="icon" class="filter-icon">
                    {{ item.label }}
                  </a-option>
                </div>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="aggregateType" label="更新方式" label-col-flex="100px">
              <a-select v-model:model-value="form.aggregateType" placeholder="请选择" style="width: 360px;">
                  <a-option v-for="(item,index) in updateList" :key="index" :value="item">{{ formatAggregateType(item) }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="attrTag" label="属性标签" label-col-flex="100px">
              <a-select v-model:model-value="form.attrTag" placeholder="请输入" multiple allow-create style="width: 360px;">
                  <a-option v-for="(item,index) in attrTagList" :key="index" :value="item.value">{{ item.name }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="20">
          <a-col :span="14">
            <a-form-item field="note" label="属性说明" label-col-flex="100px">
              <a-textarea v-model:model-value="form.note"  placeholder="请输入" class="form-input" :max-length="200" allow-clear show-word-limit style="height: 100px;width: 360px;border: none;"/>
            </a-form-item>
          </a-col>
        </a-row>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import {Message} from "@arco-design/web-vue";
import router from "@/router";
import {DateManageEventBus, SaveEvent} from "@/types/event-bus";
import {useEventBus} from "@vueuse/core";
import {getAggregateList, userAttrAdd} from '@/api/setting/api'
import {ROUTE_NAME} from "@/router/constants";

const form = reactive({
  name: '',
  displayName: '',
  note: '',
  dataType:'string',
  aggregateType:'',
  attrTag:[]
})
const rules = {
  name: [
    {
      required: true,
      message: '属性名不能为空'
    }
  ],
  displayName: [
    {
      required: true,
      message: '显示名不能为空'
    }
  ],
  select: [
    {
      required: true,
      message: '请选择'
    }
  ],
  dataType: [
    {
      required: true,
      message: '请选择'
    }
  ],
  aggregateType: [
    {
      required: true,
      message: '请选择'
    }
  ],
}

const dataOptions = [
  {value: 'int', label: '整数'},
  {value: 'float', label: '小数'},
  {value: 'string', label: '文本'},
  {value: 'date', label: '日期'},
  {value: 'datetime', label: '日期时间'},
  {value: 'boolean', label: '布尔'},
  {value: 'array', label: '列表'},
  {value: 'variant', label: '对象组'},
];


const getIconForItem = (item: string) => {
  const icons: { [key: string]: string } = {
    '文本': '/icon/text.svg',
    'string': '/icon/text.svg',
    '小数': '/icon/number.svg',
    'float': '/icon/number.svg',
    '整数': '/icon/number.svg',
    'int': '/icon/number.svg',
    '日期': '/icon/time.svg',
    'date': '/icon/time.svg',
    '日期时间': '/icon/time.svg',
    'datetime': '/icon/time.svg',
    '布尔': '/icon/boolean.svg',
    'boolean': '/icon/boolean.svg',
    '列表': '/icon/list.svg',
    'array': '/icon/list.svg',
    '对象组': '/icon/objectGroup.svg',
    'variant': '/icon/objectGroup.svg',
  };
  return icons[item] || null;
}
const updateList = ref<any>([])
const formatAggregateType = (v) => {
  const label = {
    'replace':'最新值',
    'max':'最大值',
    'min': '最小值',
    'sum':'累计值'
  }
  return label[v] || v
}
const init = async () => {
  await getAggregateList().then(res => {
    if(res) {
      updateList.value = res
      const [firstValue] = updateList.value;
      form.aggregateType = firstValue;
    }
  })
}
init()
const attrTagList = ref<any>([])
const formRef = ref(null)
const save = () => {
  if (formRef.value) {
    formRef.value.validate((errors:any) => {
      if (!errors) {
        userAttrAdd(form).then(res => {
          if(res){
            router.push({
              name: ROUTE_NAME.SETTING_ANALYSE_USER_ATTR,
            });
            Message.success('添加成功！')
          }else{
            Message.error('添加失败！')
          }
        })
      }
    });
  }
  
}
const saveEventBus = useEventBus(DateManageEventBus)
saveEventBus.on((event,route) => {
  if (event === SaveEvent&&route==='data.analyse.user.attr.create.custom') {
    save()
  }
})
const eventBus = useEventBus('eventList');
onMounted(() => {
  eventBus.emit('customAttr')
})
</script>

<style scoped lang="less">
.body {
  height: calc(100vh - 110px);
  padding-top: 24px;
}

.tant-next-commonHead{
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 16px 42px;
    .tant-next-commonHead-title {
      width: 100%;
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
  }
}
</style>