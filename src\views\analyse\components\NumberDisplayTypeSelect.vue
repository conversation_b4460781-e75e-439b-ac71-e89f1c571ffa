<script setup lang="ts">
import {getNumberDisplayConfig, getNumberDisplayType, NumberDisplayType} from "@/api/enum";
import {computed} from "vue";
import {NumberDisplayConfig} from "@/api/analyse/type";

// 使用 v-model 进行数据绑定
const value = defineModel<NumberDisplayConfig>("value");
const emits = defineEmits(['select'])

// 计算当前选中的类型
const selectedType = computed(() => getNumberDisplayType(value.value));

/**
 * 选中事件
 */
const selectType = (event, ev) => {
  value.value = event;
  emits('select', event)
}
</script>

<template>
  <a-dropdown @select="selectType">
    <div class="select-btn">
      <span class="btn-label">{{ selectedType }}</span>
    </div>
    <template #content>
      <a-doption
          v-for="type in Object.values(NumberDisplayType)"
          :key="type"
          :value="getNumberDisplayConfig(type)"
      >
        {{ type }}
      </a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped lang="less">
</style>