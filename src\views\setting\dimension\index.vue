

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <a-input v-model="searchValue" class="title-input" placeholder="请输入需要检索的名称">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="createIndex">
            <template #icon>
              <icon-plus/>
            </template>
            新增维度表
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :columns="columns"
          :loading="loading"
          :data="filteredTableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :scrollbar="scrollbar"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
      >
        <template #dataType="{record}">
          {{ getFieldTypeName(record.dataType) }}
        </template>
        <template #relatedAttribute="{record}">
          <div class="colRelatedAttributes">
            <div v-for="(item,index) in record.relatedAttribute" :key="index" class="attribute">
              <IconFont v-if="item.attributeType === 'event'" type="icon-icon-dianjifangkuang" :size="14"/>
              <IconFont v-else type="icon-quntishijian" :size="14"/>
              <span class="attributeName">{{ item.name }}</span>
            </div>
          </div>
        </template>
        <template #action="{record}">
          <a-button type="text" @click="editIndex(record)">编辑</a-button>
          <a-button type="text" @click="toAttr(record.tableCode)">属性</a-button>
          <a-button type="text" @click="toData(record.tableCode)">数据</a-button>
          <a-popconfirm :content="`确认删除数据表 ${record?.tableCode }？ 该操作不可恢复。`" type="error" @ok="deleteItem(record.tableCode)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ filteredTableData.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <edit-modal ref="editModalRef" @success="init"/>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from "vue";
import router from "@/router";
import {deleteDimension, getDimensionList} from "@/api/setting/api";
import {getFieldTypeName} from "@/api/type";
import {Message} from '@arco-design/web-vue';
import {useRoute} from 'vue-router';
import {ROUTE_NAME} from "@/router/constants";
import EditModal from './components/EditModal.vue';

const route = useRoute();
// 模拟table数据
const loading = ref(false);
const editModalRef = ref();
const tableData = ref<any>([]);

const init = async () => {
  loading.value = true
  try {
    await getDimensionList().then(res => {
      tableData.value = res;
    })
  } catch (err) {
    console.log(err)
  } finally {
    loading.value = false
  }

};
init();

// input搜索
const searchValue = ref('');

// 图标改变
const scrollbar = ref(true);

const filteredTableData = computed(() => {
  const str = searchValue.value.trim();
  const searchRegex = new RegExp(str, 'i');
  return tableData.value.filter(item => searchRegex.test(item?.tableCode));
});

const columns = ref<any>([
  {
    title: '数据表名',
    dataIndex: 'tableCode',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '维度名称',
    dataIndex: 'tableName',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '描述说明',
    dataIndex: 'tableDesc',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '主键属性',
    dataIndex: 'majorKey',
    ellipsis: true,
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '主键数据类型',
    dataIndex: 'dataType',
    slotName: 'dataType',
    ellipsis: true,
    minWidth: 100,
  },
  {
    title: '关联属性',
    dataIndex: 'relatedAttribute',
    ellipsis: true,
    slotName: 'relatedAttribute',
    width: 180,
  },
  {
    title: '更新事件',
    dataIndex: 'sourceEvent',
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '最后更新人',
    dataIndex: 'creator',
    slotName: 'creator',
    width: 120,
    align: 'center',
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    fixed: 'right',
    width: 280,
    slotName: 'action',
  }
]);

// Table页面滑动
const scroll = {
  y: 'calc(100vh - 186px)'
};


const createIndex = () => {
  editModalRef.value?.openModal();
};

const editIndex = (record: any) => {
  editModalRef.value?.openModal(record);
}
const toAttr = (tableCode: string) => {
  router.push({
      name: ROUTE_NAME.SETTING_DIMENSION_ATTR,
      query: {
        tableCode
      }
  });
}
const toData = (tableCode: string) => {
  router.push({
      name: ROUTE_NAME.SETTING_DIMENSION_DATA,
      query: {
        tableCode
      }
  });
}
const deleteItem = async (tableCode: string) => {
  if (!tableCode) return;
  try {
    await deleteDimension(tableCode);
    Message.success('删除成功');
    init();
  } catch (error) {
    Message.error('删除失败');
    console.error(error);
  }
};
</script>

<style scoped lang="less">

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  cursor: pointer;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;

}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.delete:hover {
  background-color: var(--tant-red-red-20);
  color: var(--tant-red-red-50);
  border-radius: 3px;

}



:deep(.arco-table-tr) {
  height: 66px;
}

.pagination-left {
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}


:deep(.arco-pagination-jumper > span) {
  color: var(--tant-text-gray-color-text1-1);
}

:deep(li.arco-dropdown-option) {
  line-height: 20px;
}
.colRelatedAttributes{
  display: flex;
  flex-wrap: wrap;
  .attribute{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    max-width: 160px;
    height: 32px;
    margin: 4px;
    padding: 6px 8px;
    color: var(--tant-text-gray-color-text1-2);
    background-color: var(--tant-secondary-color-secondary-fill);
    border-radius: 4px;
    .attributeName{
      margin-left: 2px;
      max-width: 128px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>