<script setup lang="ts">
import {onMounted, ref, watch} from "vue";
import {getOverviewSummary,getOverviewDetail} from "@/api/overview/api";
import {Message} from "@arco-design/web-vue";
import Income from "../chart/income.vue";


interface Props {
  /**
   * 日期
   */
  date: any

  /**
   * 查询参数
   */
  queryParams: any

  /**
   * 刷新触发器
   */
  refreshTrigger?: number
}

const props = defineProps<Props>()
const emits = defineEmits(['showFullScreen'])
const loading = ref<boolean>(true)
const total = ref<number[]>([])
const revenue = ref<number[]>([])
const purchase = ref<number[]>([])
const other = ref<number[]>([])
const diff = ref<number>();
const diffRate = ref<number>();

const refreshData = () => {
  loading.value = true
  const params = {
    type: 'income',
    startDate: props.date[0],
    endDate: props.date[1],
    country: props.queryParams.countrySelected,
    appIds:props.queryParams.appIds,
    team:props.queryParams.teamSelected
  }
  getOverviewDetail(params).then(res => {
    const data = res?.y?.[0] || [];
    total.value = data?.total;
    purchase.value = data?.detail[0] || [];
    revenue.value = data?.detail[1] || [];
    other.value = data?.detail[2] || [];
    diff.value = data?.totalDiff[data?.totalDiff.length - 1];
    diffRate.value = data?.totalDiffRate[data?.totalDiffRate.length - 1];
    loading.value = false
  }).catch(() => {
    Message.error("总收入查询错误！")
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  refreshData()
})

// 监听刷新触发器
watch(() => props.refreshTrigger, () => {
  if (props.refreshTrigger && props.refreshTrigger > 0) {
    refreshData();
  }
});
</script>

<template>
  <div class="report-card">
    <div class="report-card-header">
      <div class="report-card-title">
        总收入（单位：美元）
      </div>
      <div class="report-card-indicator">
        <div class="report-card-indicator-item">
          <div class="report-card-indicator-title">
          </div>
          <div v-if="diff ===0" class="report-card-indicator-data">
            -- ( -- %)
          </div>
          <div v-if="diff && diff >=0" class="report-card-indicator-data up">
            ↑ +{{ diff.toLocaleString() }} (+{{ diffRate }}%)
          </div>
          <div v-if="diff && diff < 0" class="report-card-indicator-data down">
            ↓ {{ diff.toLocaleString() }} ({{ diffRate }}%)
          </div>
        </div>
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="全屏">
          <div class="operation-icon" @click="()=>{emits('showFullScreen','overview-card-income')}">
            <icon-fullscreen class="icon"/>
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-content">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <income :date="props.date" :total="total" :revenue="revenue" :purchase="purchase" :other="other"/>
      </a-spin>
    </div>
  </div>

</template>

<style scoped lang="less">
@import './style.less';
</style>