import {mergeConfig} from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from "./plugin/compress";
import configVisualizerPlugin from "./plugin/visualizer";
import configImageminPlugin from "./plugin/imagemin";

export default mergeConfig({
    mode: 'development',
    plugins: [
      configCompressPlugin('gzip'),
      configVisualizerPlugin(),
      configImageminPlugin(),
    ],
    define: {
      'process.env': {
        "BASE_API_URL": "http://localhost:8888",
        "BASE_WS_URL": "ws://localhost:8888",
        "BASE_PACK_URL": "ws://localhost:8810",
      },
    }
  },
  baseConfig
);
