# Dashboard 滚动重复加载修复

## 问题描述
已经可视的卡片，滚动离开视口后再回来时，会重复请求加载数据。

## 问题原因
`v-viewport` 指令会在组件每次进入视口时触发，没有区分是首次加载还是重复进入。

## 解决方案

### 1. 添加加载状态映射
```javascript
// 已加载映射，用于跟踪组件是否已经加载过数据
const loadedMap = ref<Record<string, boolean>>({});
```

### 2. 修改可见性变化处理逻辑
```javascript
// 处理可见性变化
const handleVisibilityChange = (itemId: string, isVisible: boolean) => {
  // 只在第一次变为可见时才标记为可见（触发渲染和数据加载）
  if (isVisible && !loadedMap.value[itemId]) {
    visibleMap.value[itemId] = true;
    loadedMap.value[itemId] = true;
  }
};
```

### 3. 更新初始化逻辑
```javascript
const initVisibleComponents = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const gridItem = entry.target;
        const itemId = gridItem.getAttribute('data-item-id');
        // 只有未加载过的组件才标记为可见
        if (itemId && !loadedMap.value[itemId]) {
          visibleMap.value[itemId] = true;
          loadedMap.value[itemId] = true;
        }
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '100px'
  });
  // ...
};
```

### 4. 看板切换时重置状态
```javascript
// 看板数据加载完成后，重置可见性映射和加载状态映射
visibleMap.value = {};
loadedMap.value = {};
```

## 修复效果

### 修复前：
1. 卡片首次进入视口 → 加载数据 ✅
2. 卡片滚动离开视口 → 组件保持渲染
3. 卡片再次进入视口 → 重复加载数据 ❌

### 修复后：
1. 卡片首次进入视口 → 加载数据 ✅
2. 卡片滚动离开视口 → 组件保持渲染
3. 卡片再次进入视口 → 不重复加载 ✅
4. 看板切换 → 重置状态，新看板的卡片正常加载 ✅

## 核心逻辑

- **visibleMap**: 控制组件是否渲染（v-if 条件）
- **loadedMap**: 记录组件是否已经加载过数据
- **首次可见**: `!loadedMap[itemId]` 确保只在首次可见时触发加载
- **状态重置**: 看板切换时清空两个映射，确保新看板正常工作

这样既实现了懒加载的性能优化，又避免了重复请求的问题。
