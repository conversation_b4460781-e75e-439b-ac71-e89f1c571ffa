<template>
  <!-- 用户细查 -->
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          <select-app />
        </div>
        <div class="filter-item">
          <selectEventList :event-list="params.events" @events-change="eventsChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick" />
        </div>
        <div class="filter-item">
          <a-input v-model:model-value="userCode" :style="{ width: '240px' }" allow-clear placeholder="请输入用户ID查询" @change="searchData" />
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchData">查询</a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <DetailInfo ref="infoRef" :query-params="params" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { useRoute } from 'vue-router';
  import selectApp from '@/components/selected-game-app/index.vue';
  import selectEventList from '@/components/selected-event-list/index.vue';
  import { useEventBus } from '@vueuse/core';
  import { LocalStorageEventBus } from '@/types/event-bus';
  import { debounce,isEqual } from 'lodash';
  import DatePicker from '@/components/date-picker/index.vue';
  import DetailInfo from './DetailInfo.vue';

  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const route = useRoute();
  const userCode = ref<string>((route.query.text as string) || '');
  const params = reactive({
    date: {
      recentStartDate: 14,
      recentEndDate: 1,
      dateText: '过去14天',
    },
    events: [],
  });
  // 添加用于存储上一次复选参数值的响应式变量
  const lastParams = ref<{
    events: string[];
  }>({
    events: [],
  });
  const infoRef = ref();
  const searchData = debounce(() => {
    infoRef.value?.init(userCode.value);
  }, 300);
  const datePick = (date: any) => {
    params.date = date;
    infoRef.value.refreshEventList();
  };
  const eventsChange = (events: any) => {
    params.events = events;
    const hasParamsChanged = !isEqual({ events: params.events }, { events: lastParams.value.events });
    if (hasParamsChanged) {
      lastParams.value = {
        events: [...params.events],
      };
      infoRef.value.refreshEventList();
    }
  };

  localStorageEventBus.on((name, value) => {
    if (name === 'app-id') {
      searchData();
    }
  });
</script>

<style scoped lang="less"></style>
