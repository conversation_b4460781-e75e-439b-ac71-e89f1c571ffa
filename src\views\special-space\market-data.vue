<template>
  <div class="page">
    <div :class="isFullScreen && subHeaderVisible ? 'page-head-multiple-unfold' : 'page-head-multiple-fold'">
      <div class="title">总览</div>
      <div class="filter" style="border-radius: 9px 9px 0 9px">
        <div class="filter-item">
          <select-app-list @change="handleAppChange" />
        </div>
        <div class="filter-item">
          团队：
          <SelectedTeamList v-model:teams="queryParams.teamSelected" @change="handleTeamChange" />
        </div>
        <div class="filter-item">
          区域：
          <a-select v-model:model-value="queryParams.countrySelected" allow-search style="width: 120px; background-color: var(--tant-bg-white-color-bg1-1)">
            <a-option v-for="country in countryList" :key="country.code" :value="country.code">{{ country.name }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          概览日期（UTC）：
          <a-range-picker
            v-model:model-value="selectedDate"
            :allow-clear="false"
            shortcuts-position="left"
            :disabled-date="disableDate"
            style="width: 240px; background-color: var(--tant-bg-white-color-bg1-1)"
            :shortcuts="[
              {
                label: '过去一周',
                value: () => [dayjs(today).subtract(1, 'week'), dayjs(today)],
              },
              {
                label: '过去两周',
                value: () => [dayjs(today).subtract(2, 'week'), dayjs(today)],
              },
              {
                label: '过去一月',
                value: () => [dayjs(today).subtract(1, 'month'), dayjs(today)],
              },
              {
                label: '过去60天',
                value: () => [dayjs(today).subtract(60, 'day'), dayjs(today)],
              },
              {
                label: '过去90天',
                value: () => [dayjs(today).subtract(90, 'day'), dayjs(today)],
              },
              {
                label: '过去180天',
                value: () => [dayjs(today).subtract(180, 'day'), dayjs(today)],
              },
            ]"
            @select="onDateSelect"
          />
        </div>
        <div class="filter-item">
          <a-button :disabled="!isFullScreen" type="primary" class="btn" @click="subHeaderVisible = !subHeaderVisible">
            <icon-menu-unfold v-if="!subHeaderVisible" />
            <icon-menu-fold v-else />
          </a-button>
        </div>
      </div>
    </div>
    <div v-if="isFullScreen && subHeaderVisible" class="sub-header">
      <div class="filter-line" style="border-radius: 0 0 9px 9px">
        <div class="filter-item">
          时间粒度：
          <dateSet :time-particle="{ timeParticleSize: queryParams.timeParticleSize, firstDayDfWeek: queryParams.firstDayDfWeek }" :no-extend="true" @time-change="timeChange" />
        </div>
        <div class="filter-item">
          分组：
          <a-select
            v-model:model-value="queryParams.dataGroup"
            multiple
            allow-clear
            :allow-search="false"
            placeholder="请选择分组条件"
            style="width: 200px; background-color: var(--tant-bg-white-color-bg1-1)"
            @clear="handleDataGroupChange"
            @remove="handleDataGroupChange"
            @popup-visible-change="handleDataGroupPopupChange"
          >
            <template #arrow-icon></template>
            <a-option key="app_id" :disabled="queryParams.dataGroup.indexOf('team') > -1 || queryParams.dataGroup.indexOf('game_category') > -1" value="app_id">应用</a-option>
            <a-option key="team" :disabled="queryParams.dataGroup.indexOf('app_id') > -1 || queryParams.dataGroup.indexOf('game_category') > -1" value="team">团队</a-option>
            <a-option key="country" value="country">国家</a-option>
            <a-option key="game_category" :disabled="queryParams.dataGroup.indexOf('app_id') > -1 || queryParams.dataGroup.indexOf('team') > -1" value="game_category">游戏类别</a-option>
          </a-select>
        </div>
      </div>
    </div>
    <div v-if="isFullScreen" class="page-body">
      <overview-card-profit-full-screen v-if="fullScreenType === 'overview-card-profit'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <overview-card-income-full-screen v-if="fullScreenType === 'overview-card-income'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <overview-card-spend-full-screen v-if="fullScreenType === 'overview-card-spend'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <overview-card-daily-user-full-screen v-if="fullScreenType === 'overview-card-daily-user'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <overview-card-ad-display-full-screen v-if="fullScreenType === 'overview-card-ad-display'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <overview-card-app-count-full-screen v-if="fullScreenType === 'overview-card-app-count'" :date="date" :query-params="queryParams" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <pure-muti-line-card-full-screen v-if="fullScreenType === 'pure-muti-line-card-team'" :date="date" :query-params="queryParams" :data-name="teamNameFilter" :indicator="teamFilterSelected" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <pure-muti-line-card-full-screen v-if="fullScreenType === 'pure-muti-line-card-game-category'" :date="date" :data-name="teamNameFilter" :query-params="queryParams" :indicator="teamFilterSelected" :refresh-trigger="fullScreenRefreshTrigger" @exit-full-screen="exitFullScreen" />
      <ad-category-card-full-screen
        v-if="fullScreenType === 'ad-category-card'"
        :date="date"
        :data-name="adCategoryNameFilter"
        :title="adCategoryList[fullScreenValue?.dataIndex]"
        :query-params="queryParams"
        :indicator="adCategoryFilterSelected"
        :refresh-trigger="fullScreenRefreshTrigger"
        @exit-full-screen="exitFullScreen"
      />
      <country-ranking-card-full-screen
        v-if="fullScreenType === 'country-ranking-card'"
        :date="date"
        :data-name="countryTopFilterList[fullScreenValue?.dataIndex].name"
        :query-params="queryParams"
        :indicator="countryTopFilterList[fullScreenValue?.dataIndex].code"
        :refresh-trigger="fullScreenRefreshTrigger"
        @exit-full-screen="exitFullScreen"
      />
      <app-ranking-card-full-screen
        v-if="fullScreenType === 'app-ranking-card'"
        :date="date"
        :data-name="appTopFilterList[fullScreenValue?.dataIndex].name"
        :query-params="queryParams"
        :indicator="appTopFilterList[fullScreenValue?.dataIndex].code"
        :refresh-trigger="fullScreenRefreshTrigger"
        @exit-full-screen="exitFullScreen"
      />
    </div>
    <div v-show="!isFullScreen" class="page-body">
      <div class="report-card-container">
        <small-card title=" 总利润（单位：美元）" :total="overviewSummary.profit" :diff="overviewSummary.profitDiff" :diff-rate="overviewSummary.profitDiffRate" />
        <small-card title=" 总收入（单位：美元）" :total="overviewSummary.income" :diff="overviewSummary.incomeDiff" :diff-rate="overviewSummary.incomeDiffRate" />
        <small-card title=" 总投放（单位：美元）" :total="overviewSummary.spend" :diff="overviewSummary.spendDiff" :diff-rate="overviewSummary.spendDiffRate" />
        <small-card title=" 新增（单位：人）" :total="overviewSummary.newUserCount" :diff="overviewSummary.newUserCountDiff" :diff-rate="overviewSummary.newUserCountDiffRate" />
        <small-card title=" 活跃（单位：人）" :total="overviewSummary.activeUserCount" :diff="overviewSummary.activeUserCountDiff" :diff-rate="overviewSummary.activeUserCountDiffRate" />
        <small-card title=" 广告展示（单位：次）" :total="overviewSummary.adDisplay" :diff="overviewSummary.adDisplayDiff" :diff-rate="overviewSummary.adDisplayDiffRate" />
      </div>
      <div v-viewport="(v) => v && (visibleMap['overview-cards-container'] = true)" class="report-card-container">
        <overview-card-profit v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <overview-card-income v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <overview-card-spend v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <overview-card-daily-user v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <overview-card-ad-display v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <overview-card-app-count v-if="visibleMap['overview-cards-container']" :date="date" :query-params="queryParams" :refresh-trigger="refreshTrigger" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
      </div>
      <div class="title">
        <img class="title-icon" src="/public/icon/point.png" />
        <div class="title-text">项目组 & 游戏类别</div>
      </div>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div class="radio-scroll">
          <a-radio-group v-model="teamFilterSelected" type="button" class="radio-group-nowrap">
            <a-radio v-for="teamFilter in teamFilterList" :key="teamFilter.code" :value="teamFilter.code">
              {{ teamFilter.name }}
            </a-radio>
          </a-radio-group>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: center; flex-shrink: 0">
          <span>按应用：</span>
          <a-switch v-model="isAppId" :before-change="true" :unchecked-value="false" @change="refreshTeam" />
        </div>
      </div>
      <div v-viewport="(v) => v && (visibleMap['team-game-cards-container'] = true)" class="report-card-container">
        <pure-muti-line-card v-if="visibleMap['team-game-cards-container']" type="team" :date="date" :group="teamList" :data="teamData" :data-name="teamNameFilter" :loading="teamDataLoading" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <team-ranking-card v-if="visibleMap['team-game-cards-container']" :date="date" :loading="teamDataLoading" :group="teamList" :data="teamPieData" :data-name="teamNameFilter" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <pure-muti-line-card v-if="!isAppId && visibleMap['team-game-cards-container']" type="game-category" :date="date" :group="gameCategoryList" :data="gameCategoryData" :data-name="teamNameFilter" :loading="gameCategoryDataLoading" @show-full-screen="showFullScreen" />
        <div v-else-if="!isAppId" class="card-placeholder"></div>
        <team-ranking-card v-if="!isAppId && visibleMap['team-game-cards-container']" :date="date" :loading="gameCategoryDataLoading" :group="gameCategoryList" :data="gameCategoryPieData" :data-name="teamNameFilter" @show-full-screen="showFullScreen" />
        <div v-else-if="!isAppId" class="card-placeholder"></div>
      </div>
      <div class="report-card-container"> </div>
      <div class="title">
        <img class="title-icon" src="/public/icon/point.png" />
        <div class="title-text">广告类型</div>
      </div>
      <a-radio-group v-model="adCategoryFilterSelected" type="button">
        <a-radio v-for="adCategoryFilter in adCategoryFilterList" :key="adCategoryFilter.code" :value="adCategoryFilter.code">{{ adCategoryFilter.name }}</a-radio>
      </a-radio-group>
      <div v-viewport="(v) => v && (visibleMap['ad-category-cards-container'] = true)" class="report-card-container">
        <template v-for="(adCategory, index) in adCategoryList" :key="index">
          <ad-category-card
            v-if="visibleMap['ad-category-cards-container']"
            :title="adCategory"
            :data-name="adCategoryNameFilter"
            :date="date"
            :total="adCategoryData[index].total"
            :diff="adCategoryData[index].totalDiff"
            :diff-rate="adCategoryData[index].totalDiffRate"
            :detail="adCategoryData[index].detail"
            :detail-names="asCategoryDetailName"
            :loading="adCategoryDataLoading"
            :data-index="index"
            @show-full-screen="showFullScreen"
          />
          <div v-else class="card-placeholder"></div>
        </template>
      </div>
      <div class="title">
        <img class="title-icon" src="/public/icon/point.png" />
        <div class="title-text">国家排行</div>
      </div>
      <div v-viewport="(v) => v && (visibleMap['country-ranking-cards-container'] = true)" class="report-card-container">
        <template v-for="(countryTopFilter, index) in countryTopFilterList" :key="countryTopFilter.code">
          <country-ranking-card
            v-if="visibleMap['country-ranking-cards-container']"
            :date="date"
            :group="countryTopData[index]?.group"
            :loading="countryTopData[index]?.loading"
            :data="countryTopData[index]?.data"
            :data-name="countryTopFilter.name"
            :data-index="index"
            @show-full-screen="showFullScreen"
          />
          <div v-else class="card-placeholder"></div>
        </template>
      </div>
      <div class="title">
        <img class="title-icon" src="/public/icon/point.png" />
        <div class="title-text">TOP50应用</div>
      </div>
      <div v-viewport="(v) => v && (visibleMap['app-ranking-cards-container'] = true)" class="report-card-container">
        <template v-for="(appTopFilter, index) in appTopFilterList?.slice(0, 1)" :key="appTopFilter.code">
          <app-ranking-card v-if="visibleMap['app-ranking-cards-container']" :date="date" :group="appTopData[index]?.group" :loading="appTopData[index]?.loading" :data="appTopData[index]?.data" :data-name="appTopFilter.name" :data-index="index" @show-full-screen="showFullScreen" />
          <div v-else class="card-placeholder"></div>
        </template>
        <team-ranking-card v-if="visibleMap['app-ranking-cards-container']" :date="date" :loading="appTopData[0]?.loading" :group="appTopData[0]?.group?.slice(0, 50)" :data="appTopData[0]?.data?.slice(0, 50)" data-name="利润" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <template v-for="(appTopFilter, index) in appTopFilterList?.slice(1, 2)" :key="appTopFilter.code">
          <app-ranking-card v-if="visibleMap['app-ranking-cards-container']" :date="date" :group="appTopData[index + 1]?.group" :loading="appTopData[index + 1]?.loading" :data="appTopData[index + 1]?.data" :data-name="appTopFilter.name" :data-index="index + 1" @show-full-screen="showFullScreen" />
          <div v-else class="card-placeholder"></div>
        </template>
        <team-ranking-card v-if="visibleMap['app-ranking-cards-container']" :date="date" :loading="appTopData[1]?.loading" :group="appTopData[1]?.group?.slice(0, 50)" :data="appTopData[1]?.data?.slice(0, 50)" data-name="收入" @show-full-screen="showFullScreen" />
        <div v-else class="card-placeholder"></div>
        <template v-for="(appTopFilter, index) in appTopFilterList?.slice(2, 4)" :key="appTopFilter.code">
          <app-ranking-card v-if="visibleMap['app-ranking-cards-container']" :date="date" :group="appTopData[index + 2]?.group" :loading="appTopData[index + 2]?.loading" :data="appTopData[index + 2]?.data" :data-name="appTopFilter.name" :data-index="index + 2" @show-full-screen="showFullScreen" />
          <div v-else class="card-placeholder"></div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { isEqual, sum } from 'lodash';
  import OverviewCardProfit from '@/views/special-space/component/overview/card/overview-card-profit.vue';
  import OverviewCardIncome from '@/views/special-space/component/overview/card/overview-card-income.vue';
  import OverviewCardSpend from '@/views/special-space/component/overview/card/overview-card-spend.vue';
  import OverviewCardAppCount from '@/views/special-space/component/overview/card/overview-card-app-count.vue';
  import OverviewCardDailyUser from '@/views/special-space/component/overview/card/overview-card-daily-user.vue';
  import OverviewCardAdDisplay from '@/views/special-space/component/overview/card/overview-card-ad-display.vue';
  import AdCategoryCard from '@/views/special-space/component/overview/card/ad-category-card.vue';
  import CountryRankingCard from '@/views/special-space/component/overview/card/country-ranking-card.vue';
  import PureMutiLineCard from '@/views/special-space/component/overview/card/pure-muti-line-card.vue';
  import OverviewCardProfitFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-profit-full-screen.vue';
  import OverviewCardIncomeFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-income-full-screen.vue';
  import OverviewCardSpendFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-spend-full-screen.vue';
  import OverviewCardDailyUserFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-daily-user-full-screen.vue';
  import OverviewCardAdDisplayFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-ad-display-full-screen.vue';
  import OverviewCardAppCountFullScreen from '@/views/special-space/component/overview/full-screen/overview-card-app-count-full-screen.vue';
  import PureMutiLineCardFullScreen from '@/views/special-space/component/overview/full-screen/pure-muti-line-card-full-screen.vue';
  import AdCategoryCardFullScreen from '@/views/special-space/component/overview/full-screen/ad-category-card-full-screen.vue';
  import CountryRankingCardFullScreen from '@/views/special-space/component/overview/full-screen/country-ranking-card-full-screen.vue';
  import AppRankingCard from '@/views/special-space/component/overview/card/app-ranking-card.vue';
  import AppRankingCardFullScreen from '@/views/special-space/component/overview/full-screen/app-ranking-card-full-screen.vue';
  import { getCountryList } from '@/api/marketing/api';
  import { getOverviewDetail, getOverviewTopicDetail } from '@/api/overview/api';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import SmallCard from '@/views/special-space/component/overview/card/small-card.vue';
  import { getCurrentUTCDateISO } from '@/utils/dateUtil';
  import TeamRankingCard from '@/views/special-space/component/overview/card/team-ranking-card.vue';
  import { useEventBus, useLocalStorage, useSessionStorage } from '@vueuse/core';
  import dateSet from '@/views/analyse/components/dateSet.vue';
  import { TimeParticleSize } from '@/api/enum';
  import SelectedTeamList from '@/components/selected-team-list/index.vue';
  import { LocalStorageEventBus } from '@/types/event-bus';
  import { usePageFilter } from '@/utils/filterConfigUtil';
  import { useRoute } from 'vue-router';
  import selectAppList from './component/selected-game-app-list/index.vue';

  const route = useRoute();
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const isFullScreen = ref<boolean>(false);
  const fullScreenType = ref<string>('overview-card-profit');
  const fullScreenValue = ref<any>();
  const subHeaderVisible = useLocalStorage('overview-summary-sub-header-visible', false);
  const today = getCurrentUTCDateISO();
  const yesterday = dayjs(today).subtract(1, 'day').format('YYYY-MM-DD');
  const selectedDate = ref<string[]>([yesterday, yesterday]);

  const date = computed(() => {
    if (selectedDate.value[0] === selectedDate.value[1]) {
      const lastDay = new Date(selectedDate.value[0]);
      lastDay.setDate(lastDay.getDate() - 6);
      return [lastDay.toISOString().split('T')[0], selectedDate.value[1]];
    }
    return selectedDate.value;
  });
  const appIdsRef = useSessionStorage('overview-app-id-list', []);
  const queryParams = reactive({
    teamSelected: useSessionStorage('overview-summary-team-selected', []),
    timeParticleSize: TimeParticleSize.DAY1,
    firstDayDfWeek: null,
    appIds: appIdsRef,
    dataGroup: [],
    countrySelected: 'global',
  });
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(queryParams);
  // 添加用于存储上一次参数值的响应式变量（只存储多选参数）
  const lastParams = ref({
    appIds: [],
    teamSelected: [],
    dataGroup: [],
  });

  const dateLimit = ref<Date | undefined>();

  const countryList = ref<any[]>([]);

  // 总指标
  const overviewSummary = ref<any>({
    profit: undefined,
    profitDiff: undefined,
    profitDiffRate: undefined,
    income: undefined,
    incomeDiff: undefined,
    incomeDiffRate: undefined,
    spend: undefined,
    spendDiff: undefined,
    spendDiffRate: undefined,
  });

  const teamFilterSelected = ref<string>('profit');
  const teamFilterList = [
    { code: 'new_users', name: '新增' },
    { code: 'profit', name: '利润' },
    { code: 'revenue', name: '总收入' },
    { code: 'ma_revenue', name: '广告收入' },
    { code: 'iap_revenue', name: '内付收入' },
    { code: 'ap_spend', name: '投放' },
    { code: 'ma_k_arpu_total', name: '千活总收益' },
    { code: 'ma_k_arpu', name: '千活广告收益' },
    { code: 'ma_k_arpu_iap', name: '千活内购收益' },
    { code: 'inter_revenue', name: '插屏收入' },
    { code: 'video_revenue', name: '视频收入' },
    { code: 'banner_revenue', name: '横幅收入' },
    { code: 'inter_impressions', name: '插屏展示' },
    { code: 'video_impressions', name: '视频展示' },
    { code: 'banner_clicks', name: '横幅点击' },
    { code: 'cpi', name: 'CPI' },
    { code: 'cpi_7', name: 'CPI_7' },
  ];
  const teamNameFilter = computed(() => {
    return teamFilterList.find((item) => item.code === teamFilterSelected.value)?.name || '';
  });
  const adCategoryFilterSelected = ref<string>('revenue');
  const adCategoryFilterList = [
    { code: 'revenue', name: '收入' },
    { code: 'requests', name: '请求数' },
    { code: 'fill_count', name: '填充数' },
    { code: 'impressions', name: '展示数' },
    { code: 'clicks', name: '点击数' },
    { code: 'ecpm', name: 'ECPM' },
  ];
  const adCategoryNameFilter = computed(() => {
    return adCategoryFilterList.find((item) => item.code === adCategoryFilterSelected.value)?.name || '';
  });
  const countryTopFilterList = ref<any>([
    { code: 'profit', name: '利润' },
    { code: 'revenue', name: '收入' },
    { code: 'act_users', name: '日活' },
    { code: 'new_users', name: '新增' },
  ]);
  const appTopFilterList = ref<any>([
    { code: 'profit', name: '利润' },
    { code: 'revenue', name: '收入' },
    { code: 'act_users', name: '日活' },
    { code: 'new_users', name: '新增' },
  ]);

  // 项目组
  const teamList = ref<any>([]);
  const teamData = ref<number[][]>([]);
  const teamPieData = ref<any>([]);
  const teamDataLoading = ref<boolean>(true);

  // 游戏类别
  const gameCategoryList = ref<any>([]);
  const gameCategoryData = ref<number[][]>([]);
  const gameCategoryPieData = ref<any>([]);
  const gameCategoryDataLoading = ref<boolean>(true);

  const adCategoryList = ref<any>([]);
  const asCategoryDetailName = ref<any>([]);
  const adCategoryData = ref<any>([]);
  const adCategoryDataLoading = ref<boolean>(true);

  // 国家排行
  const countryTopData = ref<any[]>([]);

  // 应用排行
  const appTopData = ref<any[]>([]);

  // 可视加载映射表
  const visibleMap = ref<Record<string, boolean>>({});

  // 数据加载状态映射表
  const dataLoadedMap = ref<Record<string, boolean>>({});

  // 刷新触发器，用于通知子组件刷新数据
  const refreshTrigger = ref(0); // 普通卡片组件的刷新触发器
  const fullScreenRefreshTrigger = ref(0); // 全屏组件的刷新触发器

  // 触发普通卡片组件刷新的函数
  const triggerCardRefresh = () => {
    refreshTrigger.value += 1;
  };

  // 触发全屏组件刷新的函数
  const triggerFullScreenRefresh = () => {
    fullScreenRefreshTrigger.value += 1;
  };

  // 触发所有子组件刷新的函数
  const triggerAllChildRefresh = () => {
    triggerCardRefresh();
    triggerFullScreenRefresh();
  };
  const isAppId = ref(false);
  // 通用参数构建函数
  const buildCommonParams = (topic: string, indicator?: string) => ({
    topic,
    startDate: date.value[0],
    endDate: date.value[1],
    country: queryParams.countrySelected,
    appIds: queryParams.appIds,
    team: queryParams.teamSelected,
    indicator: indicator || teamFilterSelected.value,
  });
  const getTeamData = async () => {
    teamDataLoading.value = true;
    try {
      const params = buildCommonParams('team');
      let group = [] as string[];
      if (isAppId.value) {
        group = ['app_id'];
      }
      const res = await getOverviewTopicDetail({ ...params, group });
      teamList.value = res?.groups?.map((item: any) => item.join('-')) || [];
      teamData.value = res?.y || [];
      teamPieData.value =
        res?.y?.map((item: any) => {
          return {
            total: [Math.round(sum(item.total))],
            totalDiff: [item.totalDiff?.[0] || 0],
            totalDiffRate: [item.totalDiffRate?.[0] || 0],
          };
        }) || [];
    } catch (error) {
      Message.error(`项目组${teamFilterSelected.value}查询错误！`);
    } finally {
      teamDataLoading.value = false;
    }
  };
  const getGameCategoryData = async () => {
    gameCategoryDataLoading.value = true;
    try {
      const params = buildCommonParams('game_category');
      const res = await getOverviewTopicDetail(params);
      gameCategoryList.value = res?.groups?.map((item: any) => item.join('-')) || [];
      gameCategoryData.value = res?.y || [];
      gameCategoryPieData.value = res?.y?.map((item: any) => {
        return {
          total: [Math.round(sum(item.total))],
          totalDiff: [item.totalDiff[0]],
          totalDiffRate: [item.totalDiffRate[0]],
        };
      });
    } catch (error) {
      console.log(error, 'ee');
      Message.error(`游戏类别${teamFilterSelected.value}查询错误！`);
    } finally {
      gameCategoryDataLoading.value = false;
    }
  };
  const getAdCategoryData = async () => {
    adCategoryDataLoading.value = true;
    try {
      const params = buildCommonParams('ad_category', adCategoryFilterSelected.value);
      const res = await getOverviewTopicDetail(params);
      adCategoryList.value = res?.groups?.map((item: any) => item.join('-')) || [];
      asCategoryDetailName.value = res?.detailNames || [];
      adCategoryData.value = res?.y || [];
    } catch (error) {
      Message.error(`广告类别${adCategoryFilterSelected.value}查询错误！`);
    } finally {
      adCategoryDataLoading.value = false;
    }
  };
  const getCountryTopData = async () => {
    const promises = countryTopFilterList.value.map(async (item, index) => {
      countryTopData.value[index] = { loading: true };
      try {
        const params = buildCommonParams('country_rank', item.code);
        const res = await getOverviewTopicDetail({ ...params, timeParticleSize: TimeParticleSize.TOTAL });
        countryTopData.value[index] = {
          group: res?.groups?.map((groupItem: any) => groupItem.join('-')) || [],
          data: res?.y || [],
          loading: false,
        };
      } catch (error) {
        console.error(`国家排行${item.name}查询失败:`, error);
        Message.error(`国家排行${item.name}查询错误！`);
        countryTopData.value[index] = {
          group: [],
          data: [],
          loading: false,
          error: true,
        };
      }
    });
    await Promise.allSettled(promises);
  };
  const getAppRankData = async () => {
    const promises = appTopFilterList.value.map(async (item, index) => {
      appTopData.value[index] = { loading: true };
      try {
        const params = buildCommonParams('app_rank', item.code);
        const res = await getOverviewTopicDetail({ ...params, timeParticleSize: TimeParticleSize.TOTAL });
        appTopData.value[index] = {
          group: res?.groups?.map((groupItem: any) => groupItem.join('-')) || [],
          data: res?.y || [],
          loading: false,
        };
      } catch (error) {
        console.error(`应用排行${item.name}查询失败:`, error);
        Message.error(`应用排行${item.name}查询错误！`);
        appTopData.value[index] = {
          group: [],
          data: [],
          loading: false,
          error: true,
        };
      }
    });
    await Promise.allSettled(promises);
  };
  const getOverviewSummaryData = () => {
    const baseParams = {
      startDate: selectedDate.value[0],
      endDate: selectedDate.value[1],
      country: queryParams.countrySelected,
      aggregationType: 'sum',
      appIds: queryParams.appIds,
      team: queryParams.teamSelected,
    };
    getOverviewDetail({
      ...baseParams,
      type: 'profit',
    })
      .then((res) => {
        const data = res?.y?.[0] || [];
        overviewSummary.value.profit = data?.total?.[0];
        overviewSummary.value.profitDiff = data?.totalDiff?.[0];
        overviewSummary.value.profitDiffRate = data?.totalDiffRate?.[0];
        overviewSummary.value.income = data?.detail?.[0]?.[0];
        overviewSummary.value.incomeDiff = data?.detailDiff?.[0]?.[0];
        overviewSummary.value.incomeDiffRate = data?.detailDiffRate?.[0]?.[0];
        overviewSummary.value.spend = data?.detail?.[1]?.[0];
        overviewSummary.value.spendDiff = data?.detailDiff?.[1]?.[0];
        overviewSummary.value.spendDiffRate = data?.detailDiffRate?.[1]?.[0];
      })
      .catch(() => {
        Message.error('总利润查询错误！');
      });
    getOverviewDetail({
      ...baseParams,
      type: 'daily-user',
    })
      .then((res) => {
        const data = res?.y?.[0] || [];
        overviewSummary.value.newUserCount = data?.total?.[0];
        overviewSummary.value.newUserCountDiff = data?.totalDiff?.[0];
        overviewSummary.value.newUserCountDiffRate = data?.totalDiffRate?.[0];
        overviewSummary.value.activeUserCount = data?.detail?.[0]?.[0];
        overviewSummary.value.activeUserCountDiff = data?.detailDiff?.[0]?.[0];
        overviewSummary.value.activeUserCountDiffRate = data?.detailDiffRate?.[0]?.[0];
      })
      .catch(() => {
        Message.error('总览数据查询错误！');
      });
    getOverviewDetail({
      ...baseParams,
      type: 'ad-display',
    })
      .then((res) => {
        const data = res?.y?.[0] || [];
        overviewSummary.value.adDisplay = data?.total?.[0];
        overviewSummary.value.adDisplayDiff = data?.totalDiff?.[0];
        overviewSummary.value.adDisplayDiffRate = data?.totalDiffRate?.[0];
      })
      .catch(() => {
        Message.error('总览数据查询错误！');
      });
  };
  // 切换项目组&游戏分类应用
  const refreshTeam = () => {
    teamDataLoading.value = true;
    getTeamData();
  };
  // 重置数据加载状态的函数
  const resetDataLoadedState = (keys: string[]) => {
    keys.forEach((key) => {
      dataLoadedMap.value[key] = false;
    });
  };

  // 按需重新加载数据的函数
  const reloadVisibleData = () => {
    const currentVisibleMap = visibleMap.value;

    // 重新加载已可见的数据
    if (currentVisibleMap['overview-cards-container'] && !dataLoadedMap.value['overview-summary']) {
      dataLoadedMap.value['overview-summary'] = true;
      getOverviewSummaryData();
    }

    if (currentVisibleMap['team-game-cards-container'] && !dataLoadedMap.value['team-data']) {
      dataLoadedMap.value['team-data'] = true;
      getTeamData();
    }

    if (currentVisibleMap['team-game-cards-container'] && !dataLoadedMap.value['game-category-data']) {
      dataLoadedMap.value['game-category-data'] = true;
      getGameCategoryData();
    }

    if (currentVisibleMap['ad-category-cards-container'] && !dataLoadedMap.value['ad-category-data']) {
      dataLoadedMap.value['ad-category-data'] = true;
      getAdCategoryData();
    }

    if (currentVisibleMap['country-ranking-cards-container'] && !dataLoadedMap.value['country-top-data']) {
      dataLoadedMap.value['country-top-data'] = true;
      getCountryTopData();
    }

    if (currentVisibleMap['app-ranking-cards-container'] && !dataLoadedMap.value['app-rank-data']) {
      dataLoadedMap.value['app-rank-data'] = true;
      getAppRankData();
    }
  };

  // 保留日期和指标变化的监听
  watch(
    () => [date.value, teamFilterSelected.value, queryParams.countrySelected],
    () => {
      // 重置相关数据的加载状态
      resetDataLoadedState(['team-data', 'game-category-data', 'overview-summary']);
      // 按需重新加载可见的数据
      reloadVisibleData();
      triggerAllChildRefresh();
    },
    { deep: true }
  );

  watch(
    () => [date.value, adCategoryFilterSelected.value, queryParams.countrySelected],
    () => {
      // 重置广告类别数据的加载状态
      resetDataLoadedState(['ad-category-data']);
      // 按需重新加载可见的数据
      reloadVisibleData();
      triggerAllChildRefresh();
    },
    { deep: true }
  );

  watch(
    () => [queryParams.countrySelected, date],
    () => {
      // 重置相关数据的加载状态
      resetDataLoadedState(['country-top-data', 'app-rank-data', 'overview-summary']);
      // 按需重新加载可见的数据
      reloadVisibleData();
      triggerAllChildRefresh();
    },
    { deep: true }
  );

  // 检查参数变化并刷新数据
  const checkAndRefreshData = () => {
    // 检查应用和团队参数是否有变化
    const hasMainParamsChanged = !isEqual(
      {
        appIds: queryParams.appIds,
        teamSelected: queryParams.teamSelected,
      },
      {
        appIds: lastParams.value.appIds,
        teamSelected: lastParams.value.teamSelected,
      }
    );

    // 检查分组参数是否有变化（这些变化只需要触发全屏子组件刷新）
    const hasDataGroupChanged = !isEqual(queryParams.dataGroup, lastParams.value.dataGroup);
    // 只有当应用或团队参数发生变化时才执行当前文件的接口刷新
    if (hasMainParamsChanged) {
      // 更新上一次参数值
      lastParams.value = {
        appIds: [...queryParams.appIds],
        teamSelected: [...queryParams.teamSelected],
        dataGroup: [...queryParams.dataGroup],
      };
      // 重置所有数据的加载状态
      resetDataLoadedState(['team-data', 'game-category-data', 'ad-category-data', 'country-top-data', 'app-rank-data', 'overview-summary']);
      // 按需重新加载可见的数据
      reloadVisibleData();
      // 通知所有子组件刷新
      triggerAllChildRefresh();
    } else if (hasDataGroupChanged) {
      // 如果只有分组参数变化，只更新分组参数并通知全屏子组件刷新
      lastParams.value.dataGroup = [...queryParams.dataGroup];
      // 仅通知全屏子组件刷新，不触发当前文件的接口请求
      triggerFullScreenRefresh();
    }
  };

  // 应用选择变更处理
  const handleAppChange = (appIds: string[]) => {
    checkAndRefreshData();
  };

  // 团队选择变更处理
  const handleTeamChange = (teams: string[]) => {
    checkAndRefreshData();
  };

  // 仅触发子组件刷新的参数统一处理方法
  const handleFullChildParamsChange = (paramType: 'dataGroup' | 'timeParticle') => {
    // 更新对应的参数值
    if (paramType === 'dataGroup') {
      lastParams.value.dataGroup = [...queryParams.dataGroup];
    }
    // 仅全屏子组件刷新
    triggerFullScreenRefresh();
  };

  // 分组参数变更处理（立即触发子组件刷新）
  const handleDataGroupChange = () => {
    handleFullChildParamsChange('dataGroup');
  };

  // 分组选择变更处理（检测变化并仅触发子组件刷新）
  const handleDataGroupPopupChange = (visible: boolean) => {
    if (!visible) {
      // 检查分组参数是否有变化
      const hasDataGroupChanged = !isEqual(queryParams.dataGroup, lastParams.value.dataGroup);

      if (hasDataGroupChanged) {
        handleFullChildParamsChange('dataGroup');
      }
    }
  };

  localStorageEventBus.on((name, value) => {
    if (name === 'overview-app-id-list') {
      queryParams.appIds = value;
    }
  });

  // 日期选择处理
  const onDateSelect = (value: string[]) => {
    if (value.length > 1) {
      dateLimit.value = undefined;
      return;
    }
    dateLimit.value = new Date(value[0]);
  };

  // 禁用日期处理
  const disableDate = (current: Date) => {
    // 只禁用当前日期之后的日期
    return current.getTime() > new Date().getTime();
  };

  // 时间粒度变化处理（仅全屏子组件刷新）
  const timeChange = (v: any) => {
    queryParams.timeParticleSize = v.timeParticleSize;
    queryParams.firstDayDfWeek = v.firstDayDfWeek;
    handleFullChildParamsChange('timeParticle');
  };

  // 全屏显示处理
  const showFullScreen = (type: string, data?: any) => {
    isFullScreen.value = true;
    fullScreenType.value = type;
    fullScreenValue.value = data;
  };

  // 退出全屏处理
  const exitFullScreen = () => {
    isFullScreen.value = false;
  };

  // 监听可视状态变化，按需加载数据
  watch(
    () => visibleMap.value,
    (newVisibleMap) => {
      // 概览卡片数据加载
      if (newVisibleMap['overview-cards-container'] && !dataLoadedMap.value['overview-summary']) {
        dataLoadedMap.value['overview-summary'] = true;
        getOverviewSummaryData();
      }

      // 项目组和游戏类别数据加载
      if (newVisibleMap['team-game-cards-container'] && !dataLoadedMap.value['team-data']) {
        dataLoadedMap.value['team-data'] = true;
        getTeamData();
      }

      if (newVisibleMap['team-game-cards-container'] && !dataLoadedMap.value['game-category-data']) {
        dataLoadedMap.value['game-category-data'] = true;
        getGameCategoryData();
      }

      // 广告类别数据加载
      if (newVisibleMap['ad-category-cards-container'] && !dataLoadedMap.value['ad-category-data']) {
        dataLoadedMap.value['ad-category-data'] = true;
        getAdCategoryData();
      }

      // 国家排行数据加载
      if (newVisibleMap['country-ranking-cards-container'] && !dataLoadedMap.value['country-top-data']) {
        dataLoadedMap.value['country-top-data'] = true;
        getCountryTopData();
      }

      // 应用排行数据加载
      if (newVisibleMap['app-ranking-cards-container'] && !dataLoadedMap.value['app-rank-data']) {
        dataLoadedMap.value['app-rank-data'] = true;
        getAppRankData();
      }
    },
    { deep: true }
  );

  // 组件初始化
  onMounted(async () => {
    try {
      // 获取国家列表
      const countryRes = await getCountryList();
      countryList.value = countryRes || [];
      // 优先从路由配置中获取已保存的筛选条件
      const savedConfig = route.meta?.pageConfig;
      if (savedConfig) {
        // 应用保存的筛选条件
        Object.assign(queryParams, { ...savedConfig });
      }
      // 初始化上一次参数值
      lastParams.value = {
        appIds: [...queryParams.appIds],
        teamSelected: [...queryParams.teamSelected],
        dataGroup: [...queryParams.dataGroup],
      };
      // 不再初始化加载所有数据，改为按需加载
      // await Promise.allSettled([getTeamData(), getGameCategoryData(), getAdCategoryData(), getCountryTopData(), getAppRankData(), getOverviewSummaryData()]);
    } catch (error) {
      console.error('初始化失败:', error);
      Message.error('初始化失败！');
    }
  });
  // 参数变化时自动保存（防抖）
  watch(
    queryParams,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .page {
    .page-body {
      padding: 0;
      scrollbar-width: none;
      background-color: var(--tant-bg-gray-color-bg2-1);

      .title {
        display: flex;
        align-items: center;
        padding: 8px 0;

        .title-icon {
          width: 48px;
          height: 48px;
          margin: -10px -10px -8px;
        }

        .title-text {
          font-size: 16px;
          font-weight: 600;
          margin-left: 8px;
        }
      }

      .report-card-container {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }

      .card-placeholder {
        height: 400px;
        background: #f5f5f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 14px;
        flex: 1;
        min-width: 600px;
      }

      .card-placeholder::before {
        content: '加载中...';
      }
    }
  }
  .radio-scroll {
    width: 90%;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
  }
  .radio-scroll::-webkit-scrollbar {
    height: 2px; /* 悬停显示细滚动条 */
  }
  .radio-scroll::-webkit-scrollbar-track {
    background: transparent; /* 轨道透明 */
  }
  .radio-scroll::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2); /* 细且低对比度 */
    border-radius: 999px; /* 圆角 */
  }
  .radio-scroll:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.35); /* 悬停加深一点 */
  }

  /* 让滚动条不遮挡内容（小屏留出一点空间） */
  @media (max-width: 1280px) {
    .radio-scroll {
      padding-bottom: 2px;
    }
  }

  /* 保持按钮横向不换行 */
  .radio-group-nowrap {
    display: inline-flex;
    flex-wrap: nowrap;
    gap: 8px;
  }
  :deep(.arco-radio-button) {
    flex: 0 0 auto;
  }
</style>
