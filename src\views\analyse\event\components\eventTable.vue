<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';
import {cloneDeep} from "lodash";
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {createCustomSorter, getCustomStringLength} from "@/utils/strUtil";

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  // 时间格式化
  timeFormat:string;
  // 
  tableRadio:string;
  arrangeType:string
}

const props = defineProps<Props>()

const columns = ref<any>([]);
const tableData = ref<any>([]);
const visible = ref(false);
const showTotalValue = ref(false)

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])

const configColumns = ref<any>([
  {
    title: '分析指标',
    dataIndex: 'name',
    slotName: 'name',
    width:210,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '汇总方式',
    dataIndex: 'type',
    slotName: 'type'
  },
  {
    title: '剔除不完整日期',
    dataIndex: 'date',
    slotName: 'date'
  }
])
const configData = ref<any>([])

const calculateSum = (array:any) => {
  return Number(array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0));

};

// 判断周几
const getDayOfWeek = (dateString) => {
  if(props.timeFormat === 'D1'){
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if(props.timeFormat === 'W1'){
    return `${dateString}当周`;
  }
  if(props.timeFormat === 'M1'){
    return `${dateString}月`;
  }
  if(props.timeFormat === 'Q1'){
    return `${dateString}季度`;
  }
  if(props.timeFormat === 'Y1'){
    return `${dateString}年`;
  }
  return dateString;
}
// 时间格式化数据
const formatterData = computed(() => {
  const formatMap = {
    'm1': 'YYYY-MM-DD HH:mm:ss',
    'm5': 'YYYY-MM-DD HH:mm:ss',
    'm10': 'YYYY-MM-DD HH:mm:ss',
    'h1': 'YYYY-MM-DD HH:mm',
    'D1': 'YYYY-MM-DD',
    'W1':'YYYY-MM-DD',
    'M1':'YYYY/MM',
    'Q1':'YYYY-M',
    'Y1':'YYYY'
  };
  return formatMap[props.timeFormat]
})
// 千分位
const formatNumber = (num:number,displayType?:{ type: string; decimalNum: number }) => {
    if (num === null || num === undefined) return '0';
    let formattedValue = ''
    if(displayType && displayType.type){
      if (displayType.type === 'default') {
        formattedValue = num.toFixed(displayType.decimalNum);
      } else if (displayType.type === 'percent') {
          // 乘以 100
        formattedValue = (num * 100).toFixed(displayType.decimalNum);
      }
    }
    return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
const tableType = ref('')
const spliceList = ref<any>([])

// 处理radio切换时累计数据
const handleRadioData = () => {
  // 先判断是否显示阶段汇总
  let orangicData
  if (!showTotalValue.value){
    if(tableType.value !== 'event') {
      columns.value = copyColumns.value.filter(item => item.title !== '阶段汇总');
    }
    orangicData = copyTableData.value.filter(item => item.date !== '阶段汇总');
  }else{
    orangicData = cloneDeep(copyTableData.value)
    columns.value = copyColumns.value
  }
  if(props.tableRadio === 'lj'){
    // 创建新的数据副本进行计算
    const calculatedData = cloneDeep(orangicData);
    if (tableType.value === 'event') {
       // 按事件累加，需要按日期顺序处理
        const nonSummaryData = calculatedData.filter(item => item.date !== '阶段汇总');
        const summaryData = calculatedData.filter(item => item.date === '阶段汇总');
        
        // 对每个分组分别处理
        const groups = {};
        nonSummaryData.forEach(item => {
            if (!groups[item.groupValue]) {
                groups[item.groupValue] = [];
            }
            groups[item.groupValue].push(item);
        });

        // 对每个分组内的数据进行累加
        Object.values(groups).forEach(groupItems => {
            groupItems.forEach((item, index) => {
                if (index > 0) { // 跳过第一项，从第二项开始累加
                    const numKeys = Object.keys(item).filter(key => key.startsWith('num'));
                    numKeys.forEach(key => {
                        item[key] = groupItems[index - 1][key] + (item[key] || 0);
                    });
                }
            });
        });
        
        tableData.value = [...summaryData, ...nonSummaryData];
    } else {
        // 按日期累加
        calculatedData.forEach(item => {
            let sum = 0;
            const dateKeys = Object.keys(item)
                .filter(key => key.startsWith('date-'))
                .sort((a, b) => {
                    const numA = parseInt(a.split('-')[1]);
                    const numB = parseInt(b.split('-')[1]);
                    return numA - numB;
                });
                
            dateKeys.forEach(key => {
                sum += item[key];
                item[key] = sum;
            });
        });
    }
    tableData.value = calculatedData;
  }else{
    tableData.value = orangicData
  }
}
const freshData = (type?: string) => {
  tableType.value = type || 'event'
  tableData.value = []
  const data = props.eventData.y.flatMap(item =>
    item.yData.map(el => ({
      name: item.displayName,
      displayType:item.displayType,
      groupValue:el.group.map(value => value === null ? 'null' : value).join(','),
      groupList:el.group.map(value => value === null ? 'null' : value),
      values: type === 'dateDown' ? [...el.values].reverse() : el.values
    }))
  );
  const uniqueData = Array.from(new Set(data.map(item => item.name)))
    .map(name => data.find(item => item.name === name)); // 根据 name 去重
  const xDateList = type === 'dateDown' ? [...props.eventData.x].reverse() : props.eventData.x;
  const columnsDate = xDateList && xDateList.length > 0 ? xDateList.map(item => props.timeFormat !== 'T0' ? dayjs(item).format(formatterData.value) : item): []
  configData.value = uniqueData.map(item => {
    return {
      name:item.name,
      type:'sum'
    }
  })
  spliceList.value = []
  const groupsList =  props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }) || [];

  if (tableType.value === 'event') {
    columns.value = [
      {
        title: '日期',
        dataIndex: 'date',
        sortable: { sortDirections: ['ascend', 'descend'] }
      },
      ...uniqueData.map((item, index) => ({
        title: item.name,
        dataIndex: `num${index + 1}`,
        slotName:`num${index + 1}`,
        displayType:item.displayType,
        sortable: { sortDirections: ['ascend', 'descend'] }
      }))
    ];
    const summary = [] as any
    
    groupsList.forEach((val) => {
      const summaryItem = {
          date: '阶段汇总',
          groupValue: val,
          groupList: val.split(',')
      };

      // 根据 y 的长度动态添加 num1, num2, ...
      props.eventData.y.forEach((yy, index) => {
          summaryItem[`num${index + 1}`] = 0; // 初始化 num1, num2, ...
      });

      summary.push(summaryItem);
  });
    
    const result = [] as any
    const dates = columnsDate.map(item =>getDayOfWeek(item))
    const yLength = props.eventData.y.length;
    dates.forEach((date,dateIndex) => {
        groupsList.forEach(groupValue => {
            const entry = {
                date,
                groupValue,
                groupList: [],
                displayType:{}
            };

            // 初始化所有 num 值为 null
            for (let i = 0; i < yLength; i++) {
                entry[`num${i + 1}`] = null;
            }
            // 遍历数据,填充对应的 num 值
            data.forEach((item) => {
                if (item.groupValue === groupValue) {
                    for (let i = 0; i < yLength; i++) {
                        if (entry[`num${i + 1}`] === null) {
                            entry[`num${i + 1}`] = item.values[dateIndex];
                            entry.groupList = item.groupList;
                            entry.displayType = item.displayType
                            break; // 找到第一个为null的num并赋值后,跳出循环
                        }
                    }
                }
            });
            result.push(entry);
        });
    });

    summary.forEach(summaryItem => {
        const matchingItems = result.filter(dataItem => dataItem.groupValue === summaryItem.groupValue);
        // 遍历 summaryItem 的所有属性
        Object.keys(summaryItem).forEach(key => {
            if (key.startsWith('num')) { // 检查键名是否以 'num' 开头
                const index = parseInt(key.replace('num', '')) - 1; // 获取 num 的索引
                summaryItem[key] = matchingItems.reduce((sum, item) => sum + (item[`num${index + 1}`] || 0), 0); // 求和
            }
        });
    });

    tableData.value = [...summary, ...result];
    
    // 当有分组项的情况下
    if(props.eventData.groupsDesc && props.eventData.groupsDesc.length>0){
      props.eventData.groupsDesc.forEach((item,index) => {
          spliceList.value.push({
            title: item.name,
            dataIndex: `group-${index}`,
            sortable: {
              sortDirections: ['ascend', 'descend'],
              sorter: (a, b, extra) => {
                const { direction } = extra;
                const format = item.format;
                const valueA = a[`group-${index}`];
                const valueB = b[`group-${index}`];
                return createCustomSorter(format)(valueA, valueB, direction);
              }
            },
            filterable: {
              filter: (value, row) => {
                if (!value || value.length === 0) return true;
                return value.includes(row.groupList[index]);
              },
              slotName: `group-filter-${index}`, // 使用唯一的插槽名
              multiple: true,
            },
          })
          tableData.value.forEach(el => {
            el[`group-${index}`] = el.groupList[index]
          })
      })
      columns.value.splice(1, 0, ...spliceList.value);
    }

  } else {
    columns.value = [
      {
        title: '指标',
        dataIndex: 'name',
        sortable: { sortDirections: ['ascend', 'descend'] },
        filterable: {
          // 直接从 uniqueData 获取筛选选项，因为它包含了所有的指标名称
          filters: uniqueData.map(item => ({
            text: item.name,
            value: item.name
          })),
          filter: (value, row) => value.includes(row.name),
          multiple: true
        }
      },
      {
        title: '阶段汇总',
        dataIndex: 'sum',
        slotName:'sum',
        sortable: { sortDirections: ['ascend', 'descend'] }
      },
      ...columnsDate.map((item, index) => ({
        title: getDayOfWeek(item),
        dataIndex: `date-${index + 1}`,
        slotName:`date-${index + 1}`,
        sortable: { sortDirections: ['ascend', 'descend'] }
      }))
    ];

    tableData.value = data.map(item => ({
      name: item.name,
      groupValue:item.groupValue,
      groupList:item.groupList,
      displayType:item.displayType,
      sum: calculateSum(item.values),
      ...columnsDate.reduce((acc, _, index) => {
        acc[`date-${index + 1}`] = item.values[index] || 0;
        return acc;
      }, {})
    }));
    tableData.value.sort((a, b) => {
      if (a.groupValue === b.groupValue) {
        return 0; // 相同的 groupValue 保持原有顺序
      }
      return a.groupValue < b.groupValue ? -1 : 1; // 按 groupValue 排序
    });
    
    // 当有分组项的情况下
    if(props.eventData.groupsDesc && props.eventData.groupsDesc.length>0){
      props.eventData.groupsDesc.forEach((item,index) => {
          spliceList.value.push({
              title: item.name,
              dataIndex: `group-${index}`,
              sortable: {
                sortDirections: ['ascend', 'descend'],
                sorter: (a, b, extra) => {
                  const { direction } = extra;
                  const format = item.format;
                  const valueA = a[`group-${index}`];
                  const valueB = b[`group-${index}`];
                  return createCustomSorter(format)(valueA, valueB, direction);
                }
              },
              filterable: {
                filter: (value, row) => {
                  if (!value || value.length === 0) return true;
                  return value.includes(row.groupList[index]);
                },
                slotName: `group-filter-${index}`, // 使用唯一的插槽名
                multiple: true,
              },
          })
          tableData.value.forEach((el,elIndex) => {
              el[`group-${index}`] = el.groupList[index]
          })
      })
    }
    columns.value.unshift(...spliceList.value)
    
    
  }
  copyColumns.value = cloneDeep(columns.value)
  copyTableData.value = cloneDeep(tableData.value)
  handleRadioData()
  
};
watch(() => props.tableRadio, () => {
  handleRadioData()
});
watch(() => props.arrangeType, (newData, oldData) => {
  freshData(props.arrangeType)
},{immediate:true,deep:true})
const showTotal = ()=>{
  visible.value = true
}

const analyzeArray = (numbers) => {
  // 将字符串数组转换为数字数组
  const nums = numbers.map(Number);
  // 计算均值并保留两位小数
  const mean = Number((nums.reduce((sum, num) => sum + num, 0) / nums.length).toFixed(2));
  // 计算均值取整
  const meanInt = Math.round(parseFloat(mean));
  // 计算最大值和最小值
  const maxVal = Math.max(...nums);
  const minVal = Math.min(...nums);
  return [mean, meanInt, maxVal, minVal];
}
// 阶段汇总配置
const extractDateValues = (item) => {
    return Object.entries(item)
        .filter(([key]) => key.startsWith('date-'))
        .map(([_, value]) => value);
};
const calculateList = (data) => {
    const groupValues = [...new Set(data.map(item => item.groupValue))];
    groupValues.forEach(groupValue => {
        const summaryItem = data.find(item => item.date === '阶段汇总' && item.groupValue === groupValue);
        if (summaryItem) {
            const groupItems = data.filter(item => item.groupValue === groupValue && item.date !== '阶段汇总');
            // 获取所有 'num' 开头的键
            const numKeys = Object.keys(summaryItem).filter(key => key.startsWith('num'));
            numKeys.forEach(numKey => {
                const values = groupItems.map(item => item[numKey]).filter(val => val !== null && val !== undefined);
                summaryItem[numKey] = values.length > 0 ? values : null;
            });
        }
    });
    return data;
}
// 阶段汇总确定
const checkValue = () => {
  if(tableType.value !== 'event'){
    const numData = copyTableData.value.map(item => extractDateValues(item))

    copyTableData.value.forEach((item,index) => {
      configData.value.forEach(val => {
        if(item.name === val.name){
          const [mean, meanInt, maxVal, minVal] = analyzeArray(numData[index]);
          if(val.type ==='sum'){
            item.sum = calculateSum(numData[index])
          }else if(val.type === 'avg'){
            item.sum = mean
          }else if(val.type === 'avgRound'){
            item.sum = meanInt
          }else if(val.type === 'avgWeight'){
            item.sum = mean
          }else if(val.type === 'max'){
            item.sum = maxVal
          }else if(val.type === 'min'){
            item.sum = minVal
          }else if(val.type === 'total'){
            item.sum = calculateSum(numData[index])
          }
        }
      })
    })
  }else{
    const numData = calculateList(cloneDeep(copyTableData.value))
    numData.forEach(item => {
      configData.value.forEach((val,valIndex) => {
        if(item.date === '阶段汇总'){
          const [mean, meanInt, maxVal, minVal] = analyzeArray(item[`num${valIndex+1}`]);
          if(val.type ==='sum'){
            item[`num${valIndex+1}`] = calculateSum(item[`num${valIndex+1}`])
          }else if(val.type === 'avg'){
            item[`num${valIndex+1}`] = mean
          }else if(val.type === 'avgRound'){
            item[`num${valIndex+1}`] = meanInt
          }else if(val.type === 'avgWeight'){
            item[`num${valIndex+1}`] = mean
          }else if(val.type === 'max'){
            item[`num${valIndex+1}`] = maxVal
          }else if(val.type === 'min'){
            item[`num${valIndex+1}`] = minVal
          }else if(val.type === 'total'){
            item[`num${valIndex+1}`] = calculateSum(item[`num${valIndex+1}`])
          }
        }
      })
      
    })
    copyTableData.value = numData
  }
  
  // 处理是否显示汇总
  // if (!showTotalValue.value){
  //   if(tableType.value === 'event') {
  //     tableData.value = copyTableData.value.filter(item => item.date !== '阶段汇总');
  //   }else{
  //     columns.value = copyColumns.value.filter(item => item.title !== '阶段汇总');
  //   }
  // }else{
  //   tableData.value = copyTableData.value
  //   columns.value = copyColumns.value
  // }
  handleRadioData()
  visible.value = false
}
// freshData()

// 导出
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `事件分析${name ? `_${name}` : ''}_${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}.xlsx`);
};
defineExpose({
  showTotal,
  freshData,
  exportXlsx
})
const filteredTableData = computed(() => {
  
  return tableData.value;
})
const totalPages = computed(() => {
  return filteredTableData.value.length;
});
// 按日期时，只合并columns.value中title='指标'列，按事件时，合并title='日期'列
const spanMethod = ({ record, column, rowIndex, columnIndex }) => {
  
};
const pageOptions = [
  10, 20, 50, 100
]
const pagination = reactive({
  total: totalPages.value,  // 使用过滤后的数据长度
  PageSize: pageOptions[0],
  showPageSize: true,
  pageSizeOptions: pageOptions,
  showJumper: true,
  hideOnSinglePage: true,
  autoAdjust: true,
});
watch(totalPages, (newTotal) => {
  pagination.total = newTotal;
});

// 添加一个获取筛选选项的函数
const getFilterOptionsByIndex = (index: number) => {
  if (!tableData.value?.length) return [];
  return Array.from(new Set(tableData.value.map(row => row.groupList[index])))
    .filter(value => value !== undefined && value !== null)
    .map(value => ({
      label: value,
      value: value
    }));
};
</script>

<template>
  <div class="chart-content">
    <a-spin  style="width: 100%;height: 100%;padding-bottom: 24px;">
      <a-table :columns="columns" :data="filteredTableData" :pagination="true" size="small" column-resizable :bordered="{cell:true}" :span-method="spanMethod" :filter-icon-align-left="true" style="white-space: nowrap">
        <template v-for="(_, index) in props.eventData?.groupsDesc" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptionsByIndex(index)"
                :style="{ width: '200px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record }">
          {{ formatNumber(record[column.dataIndex], tableType === 'event' ? column.displayType : record.displayType) }}
          <span v-if="(tableType === 'event' ? column.displayType?.type === 'percent' : record.displayType?.type === 'percent')">%</span>
        </template>
      </a-table>
    </a-spin>
    <a-modal :visible="visible" width="600px" title="阶段汇总配置" title-align="start" unmount-on-close @cancel="visible = false">
      <div>
        <span style="font-size: 14px;color: #7e7f80;">除各时段的结果数据外，你还可以基于这批数据聚合得到阶段汇总，并为每个分析指标选择合适的计算方法，例如通过汇总每小时同时在线人数的最大值得到PCU (最高同时在线玩家人数)</span><br>
        <a-table :columns="configColumns" :pagination="false" size="small" :bordered="false" :data="configData" style="margin: 16px 0;">
          <template #name="{ rowIndex }">
            <i class="i">{{rowIndex+1}}</i>
            <span style="font-weight: 500">{{configData[rowIndex].name}}&nbsp;</span>
          </template>
          <template #type="{ rowIndex }">
            <a-select v-model="configData[rowIndex].type" :style="{width:'120px',borderRadius: '4px'}">
              <a-tooltip content="适用于总次数或总和类指标，基于不同时段的结果数据进行简单加和，不去重" position="right">
                <a-option value="sum">阶段总和</a-option>
              </a-tooltip>
              <a-tooltip content="适用于总和类的均值计算，基于不同时段的结果数据进行简单平均" position="right">
                <a-option value="avg">阶段均值</a-option>
              </a-tooltip>
              <a-option value="avgRound">阶段均值取整</a-option>
              <a-tooltip content="适用于比率或人均类指标，基于不同时段的分子和分母分别相加后再计算，分母越大则该时段权重越大" position="right">
                <a-option value="avgWeight">加权平均</a-option>
              </a-tooltip>
              <a-option value="max">最大值</a-option>
              <a-option value="min">最小值</a-option>
              <a-tooltip content="适用于去重类指标，基于所选时间范围（包括按周按月下补全的周和月）进行去重计算，性能开销较大，请谨慎选择" position="right">
                <a-option value="total">合计</a-option>
              </a-tooltip>
            </a-select>
          </template>
          <template #date="{ rowIndex }">
            <a-tooltip>
              <template #content>
                <div style="width: 220px;">
                  <div>什么是不完整日期？</div>
                <img src="/src/assets/images/incomplete-date.png" alt="" style="width: 100%;height: 100px;">
                <div>不完整日期指不早于此刻的时段，因为这些时段还未结束，指标数据不能代表实际表现，剔除后可以获得更准确的阶段汇总结果。</div>
                </div>
              </template>
              <a-switch v-model="configData[rowIndex].date" size="small"/>
            </a-tooltip>
          </template>
        </a-table>
      </div>
      <template #footer>
        <div style="display: flex;justify-content: space-between;">
        <div>
          <a-checkbox v-model="showTotalValue" value="1">显示阶段汇总</a-checkbox>
        </div>
        <div>
          <button style="margin-right: 10px;" class="btnQ" @click="visible = false">取消</button>
          <button style="" class="btnB" @click="checkValue">保存</button>
        </div>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
.btnQ{
  color: #333;
  background-color: transparent;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
  cursor: pointer;
}
.btnQ:hover{
  background-color: #f1f2f5;
}
.btnB{
  cursor: pointer;
  color: #fff;
  background-color: #1e76f0;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
}
.btnB:hover{
  background-color: #3583ef;
}
.i{
  display: inline-block;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: var(--tant-bg-white-color-bg1-1);
  font-size: 12px;
  font-style: normal;
  line-height: 24px;
  text-align: center;
  background-color: var(--tant-secondary-color-secondary-default);
  border-radius: 4px;
  transition: all .3s;
  margin-right: 12px;
}
</style>