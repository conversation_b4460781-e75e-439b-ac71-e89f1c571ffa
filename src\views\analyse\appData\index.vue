<template>
  <div ref="pageContainer" class="page">
    <div class="page-head-multiple-unfold">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <div class="edit-select">
            <a-select
                v-model:model-value="boardCode"
                :loading="loading"
                :style="{width:'240px',borderRadius:'4px',marginLeft:'12px'}"
                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                @change="boardChange">
              <template #label="{ data }">
                <span>面板-{{ data?.label }}</span>
              </template>
              <a-option v-for="item in boardViewList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
            </a-select>
            <a-tooltip content="编辑">
                <icon-edit v-if="!loading" size="16" class="edit" @click="editBoard"/>
            </a-tooltip>
          </div>
        </div>
        <div class="filter-item">
          <select-app-list @change="refresh"/>
        </div>
        <div class="filter-item">
          <SelectedTeamList v-model:teams="params.teamCodes" @change="refresh" />
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange"/>
        </div>
        <div class="filter-item">
          <div style="width: 200px;">
            <a-select
              v-model:model-value="params.groupCodes"
              :style="{borderRadius:'4px'}"
              :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
              placeholder="选择分组"
              multiple
              allow-clear
              :max-tag-count="1"
              :scrollbar="true"
              @clear="refresh"
              @remove="refresh"
              @popup-visible-change="groupPopupVisibleChange">
                <template #label="{ data }">
                    <span style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">分组-{{data?.label}}</span>
                </template>
                <a-option 
                  v-for="item in groupList" 
                  :key="item.code" 
                  :value="item.code"
                  :disabled="isGroupOptionDisabled(item)"
                >{{item.displayName}}</a-option>
            </a-select>
          </div>
        </div>
      </div>
    </div>
    <div class="sub-header">
      <div class="filter-line" style="border-radius: 0 0 9px 9px">
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick"/>
        </div>
        <div class="filter-item">
          <dateSet :time-particle="{ timeParticleSize: params.timeParticleSize, firstDayDfWeek: params.firstDayDfWeek }" :no-extend="true" @time-change="timeChange" />
        </div>
        <div class="filter-item"> 
          <a-button type="primary" @click="refreshAll">
            <template #icon>
              <icon-search class="nav-icon"/>
            </template>
            查询
          </a-button>
        </div>
        <div class="filter-item">
          <a-dropdown>
            <a-button :loading="handleLoading" type="primary">
              <template #icon>
                <icon-settings />
              </template>
              <template #default>操作</template>
            </a-button>
            <template #content>
              <a-doption style="width: 120px;" @click="saveCurrent">
                保存当前面板
              </a-doption>
              <a-doption style="width: 120px;" @click="saveAs">
                另存新面板
              </a-doption>
              <a-doption style="width: 120px;" @click="addNew">
                新增面板
              </a-doption>
            </template>
          </a-dropdown>
        </div>
        <div class="filter-item">
          <a-dropdown>
            <a-tooltip content="添加内容">
              <a-button type="primary">
                <template #icon>
                  <icon-plus/>
                </template>
              </a-button>
            </a-tooltip>
            <template #content>
              <a-doption v-for="(item,index) in options" :key="index" :value="item.value" style="width: 120px;" @click="selectOption(item)">
                {{ item.label }}
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div>
    <div class="page-body">
      <div v-if="loading" class="spin">
        <a-spin :loading="loading" class="spin-icon" dot>
          <template #tip>
            <span class="spin-tip">加载中...</span>
          </template>
        </a-spin>
      </div>
      <div v-if="!loading && layout.length" class="card-layout">
        <grid-layout
            v-model:layout="layout"
            :col-num="20"
            :row-height="30"
            :is-resizable="false"
            :margin="[15, 15]"
            @layout-updated="layoutUpdated">
          <grid-item
              v-for="item in layout"
              :key="item.i"
              v-viewport="(v)=> v && (visibleMap[item.i] = true)"
              :x="item.x"
              :y="item.y"
              :w="item.w"
              :h="item.h"
              :i="item.i"
              drag-allow-from=".drag-allow-from"
              drag-ignore-from=".no-drag"
              :min-w="4"
              :min-h="2"
              :is-draggable="item.isDraggable">
            <earningsCard
                v-if="item.objectType === 2 && visibleMap[item.i]"
                :ref="el => earningsCardRefs[layout.findIndex(l => l.i === item.i)] = el"
                :report="item"
                :indicator-list="indicatorList"
                :filter-params="params"
                @delete-item="deleteItem"
                @update-params="updateParams"/>
            <echartCard
                v-if="item.objectType === 3 && visibleMap[item.i]"
                :ref="el => echartCardRefs[layout.findIndex(l => l.i === item.i)] = el"
                :report="item"
                :group-list="groupList"
                :indicator-list="indicatorList"
                :filter-params="params"
                @resize="resize"
                @delete-item="deleteItem"
                @update-params="updateParams"/>
            <tableCard
                v-if="item.objectType === 4 && visibleMap[item.i]"
                :ref="el => tableCardRefs[layout.findIndex(l => l.i === item.i)] = el"
                :report="item"
                :group-list="groupList"
                :indicator-list="indicatorList"
                :filter-params="params"
                :view-data="handleViewData"
                :indicators-view-list="indicatorsViewList"
                @delete-item="deleteItem"
                @update-params="updateParams"
                @update-view="getViewList"
                @update-indicator="updateIndicatorList"
                @page-size-change="(ps, actualCount) => resize('large',item.objectCode,ps, actualCount)"/>
          </grid-item>
        </grid-layout>
      </div>
      <div v-if="!loading && !layout.length" class="empty">
        <div class="empty-body">
          <div style="box-sizing: border-box">
            <div class="empty-img">
              <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
            </div>
            <div class="empty-description">
              <div class="title">当前查询无数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <kpiSelect ref="kpiRef" @transfer-data="transferData"/>
    <!-- 保存弹窗 -->
    <a-modal v-model:visible="saveVisible" title-align="start" width="466px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">{{setType === 'saveAs' ? '另存为新面板' : '新增面板'}}</div>
      </template>
      <a-form ref="saveFormRef" :model="form" layout="vertical">
        <a-form-item field="name" required :rules="[{ required: true, message: '面板名称不能为空' }]" :hide-asterisk="true">
          <a-input v-model="form.name" placeholder="设置面板名称" :max-length="80"/>
        </a-form-item>
        <a-form-item field="isDefault">
          <a-checkbox v-model="form.isDefault">设为默认面板</a-checkbox>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="footer">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" :loading="saveLoading" @click="saveReport">保存</a-button>
        </div>
      </template>
    </a-modal>
    <BoardSelect ref="boardRef" :layout="layout" :board-view-data="handleBoardData" @update-board="updateBoard"/>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {GridItem, GridLayout, Layout} from 'grid-layout-plus';
import {analyseStore} from '@/store';
import {Message} from "@arco-design/web-vue";
import selectAppList from "@/components/selected-game-app-list/index.vue"
import selectCountryList from "@/components/selected-country-list/index.vue"
import DatePicker from "@/components/date-picker/index.vue";
import {debounce, isEqual} from "lodash"
import {detailDashboard, saveDashboardUI} from "@/api/dashboard/api";
import {appDashboardSave, getAppDashboardList, getAttributionViews} from "@/api/analyse/api"
import {useElementSize, useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from "vue-router";
import {TimeParticleSize} from '@/api/enum';
import dateSet from '@/views/analyse/components/dateSet.vue';
import {usePageFilter} from '@/utils/filterConfigUtil';
import SelectedTeamList from '@/components/selected-team-list/index.vue';
import earningsCard from "./components/earningsCard.vue";
import echartCard from "./components/echartCard.vue";
import tableCard from "./components/tableCard.vue";
import kpiSelect from "./components/kpiSelect.vue";
import BoardSelect from "./components/boardSelect/index.vue";

const pageContainer = ref<HTMLElement | null>(null)
const { width: containerWidth } = useElementSize(pageContainer)
const showMainHeaderActions = computed(() => containerWidth.value > 1280)
const loading = ref(false)
const analyseData = analyseStore();
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const route = useRoute();
const boardCode = ref('')
const isPublic = ref(1)
const boardViewList = ref()
const handleBoardData = ref()
const appIdList = useSessionStorage("app-id-list", [])
const params = reactive({
  appIds: appIdList,
  country: ['global'],
  teamCodes:[],
  date: {
    recentStartDate: 14,
    recentEndDate: 1,
    dateText: '过去14天'
  },
  groupCodes:[],
  timeParticleSize: TimeParticleSize.DAY1,
  firstDayDfWeek: null,
})

// 筛选条件保存工具
const { saveFilter, saveFilterDebounced } = usePageFilter(params);
// 添加用于存储上一次参数值的响应式变量
const lastParams = ref({
  appIds: [],
  country: [],
  groupCodes: [],
  teamCodes: []
});
localStorageEventBus.on((name, value) => {
  if (name === "app-id-list") params.appIds = value
})
const boardName = ref('')
const groupList = ref()
const indicatorList = ref()
const evtLists = ref()

// 判断分组选项是否应该被禁用
const isGroupOptionDisabled = (item: any) => {
  // 获取当前已选中的分组
  const selectedGroups = groupList.value.filter(group => params.groupCodes.includes(group.code));
  // 检查当前已选中的分组中是否有团队
  const hasSelectedTeam = selectedGroups.some(group => group.name === 'team');
  // 检查当前已选中的分组中是否有应用或国家
  const hasSelectedAppOrCountry = selectedGroups.some(group => 
    group.name === 'app_id' || group.name === 'country'
  );
  // 如果已经选中了团队，则禁用应用和国家选项
  if (hasSelectedTeam && (item.name === 'app_id' || item.name === 'country')) {
    return true;
  }
  // 如果已经选中了应用或国家，则禁用团队选项
  if (hasSelectedAppOrCountry && item.name === 'team') {
    return true;
  }
  return false;
};

const kpiRef = ref()
const options = [
  {
    value: 'table',
    label: '数据表'
  },
  {
    value: 'kpi',
    label: 'KPI指标'
  },
  {
    value: 'trend',
    label: '折线图'
  },
  {
    value: 'distribution',
    label: '柱状图'
  },
  {
    value: 'stack',
    label: '堆积图'
  },
  {
    value: 'pie',
    label: '饼图'
  },
]


const layout = ref<Layout>([])
const layoutUpdated = () => {

}
const layoutAdd = (type: any, data?: any, size?: string) => {
  const id = `app${Date.now()}`;
  // 偶发界面重叠
  let valueH = 10; // 默认值
  let valueW = 20; // 默认值
  if (type === 2) {
    valueH = 4;
    valueW = 5;
  } else if (type === 3) {
    switch (size) {
      case 'small':
        valueH = 5;
        valueW = 5;
        break;
      case 'middle':
        valueH = 10;
        valueW = 10;
        break;
      default:
        // 保持默认值
        break;
    }
  }
  const maxWidth = 20; // 一行的最大宽度
  let currentX = 0; // 当前行的 x 坐标
  let currentY = 0; // 当前行的 y 坐标

  // 遍历现有布局，计算新元素的 x 和 y
  layout.value.forEach(item => {
    // 如果当前元素的 y 坐标与新元素的 y 坐标相同
    if (item.y === currentY) {
      currentX += item.w; // 累加当前行的宽度
    } else {
      // 如果当前行的宽度加上新元素的宽度超过最大宽度，换行
      if (currentX + valueW > maxWidth) {
        currentX = 0; // 重置 x 坐标
        currentY += item.h; // 增加 y 坐标
      } else {
        currentY = item.y; // 更新当前行的 y 坐标
      }
      currentX += item.w; // 累加当前行的宽度
    }
  });

  // 如果当前行的宽度加上新元素的宽度超过最大宽度，换行
  if (currentX + valueW > maxWidth) {
    currentX = 0; // 重置 x 坐标
    currentY += valueH; // 增加 y 坐标
  }
  const newLayout = {
    objectType: type,
    objectCode: id,
    x: currentX,
    y: currentY,
    h: valueH,
    w: valueW,
    i: id,
    configParams: data,
    isDraggable: true,
    move: false
  };

  layout.value.push(newLayout);
}

const visibleMap = ref<Record<string, boolean>>({});

// 跟踪参数是否已经变化但还没有同步到 lastParams
const paramsChanged = ref(false);

const selectOption = (item) => {
  if (item.value === 'kpi') {
    kpiRef.value.openModal()
    return
  }
  if (item.value === 'table') {
    layoutAdd(4)
  } else {
    layoutAdd(3, {chartType: item.value}, 'middle')
  }
}
const transferData = (v) => {
  layoutAdd(2, {indicator: v.indicator})
}

const getDetail = async () => {
  await detailDashboard(boardCode.value).then((response:any) => {
    layout.value = response.uiConfig?.map(config => {
      return {
        ...config,
        i: config.objectCode
      }
    })
  }).catch(e => {
    console.error(e)
  })
}
const indicatorsViewList = ref()
const handleViewData = ref()
const getViewList = async () => {
  await getAttributionViews('application',boardCode.value).then(res => {
      indicatorsViewList.value = [
        ...(res.sys || []),
        ...(res.user || []),
        ...(res.share || [])
      ]
      handleViewData.value = res
  })
}
const boardChange = async (v) => {
  loading.value = true
  const obj = boardViewList.value.find(item => item.code === v)
  isPublic.value = obj.isPublic
  boardName.value = obj.name
  await getViewList()
  await getDetail()

  // 面板切换后，重置可见性映射
  visibleMap.value = {}

  loading.value = false
}
const getBoardList = async () => { 
  const res = await getAppDashboardList('application')
  handleBoardData.value = res
  // 扁平化处理，合并所有分组的数据
  boardViewList.value = [
    ...(res.sys || []),
    ...(res.user || []),
    ...(res.share || [])
  ]
}
// 设置面板
const boardRef = ref()
const editBoard = () => {
  boardRef.value.openModal(boardCode.value)
}
const updateBoard = async (v) => {
  boardCode.value = v ? v : boardCode.value
  await getBoardList()
  // 检查更新后的boardCode是否还在面板数组中
  const boardExists = boardViewList.value.find(item => item.code === boardCode.value)
  if (!boardExists) {
    // 如果不存在，使用默认面板或第一个面板
    const defaultObj = boardViewList.value.find(item => item.isDefault)
    boardCode.value = defaultObj?.code || boardViewList.value[0]?.code
    boardChange(boardCode.value)
  }
  if(v){
    boardChange(boardCode.value)
  }
}
const updateIndicatorList = async () => { 
  indicatorList.value = await analyseData.fetchOperationIndexInfo('application');
}
const init = async () => {
  loading.value = true
  try {
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, {...savedConfig});
    }
    const [groupListResult, indicatorListResult, evtListsResult] = await Promise.all([
      analyseData.fetchOperationGroupLists('application'),
      analyseData.fetchOperationIndexInfo('application'),
      analyseData.$state.evtLists.length > 0 
        ? Promise.resolve(analyseData.$state.evtLists) 
        : analyseData.fetchEvtInfo()
    ]);
    groupList.value = groupListResult;
    const appIdGroup = groupList.value.find(item => item.name === 'app_id');
    // 只有在没有保存配置时才设置默认分组
    if (!params.groupCodes.length) {
      params.groupCodes = appIdGroup ? [appIdGroup.code] : [];
    }
    indicatorList.value = indicatorListResult;
    evtLists.value = evtListsResult;
    await getBoardList();
    const defaultObj = boardViewList.value.find(item => item.isDefault);
    boardCode.value = defaultObj?.code || boardViewList.value[0]?.code;
    boardName.value = defaultObj?.name || boardViewList.value[0]?.name;
    isPublic.value = defaultObj?.isPublic || boardViewList.value[0]?.isPublic;
    await Promise.all([
      getViewList(),
      getDetail()
    ]);
    // 初始化 lastParams 的值
    lastParams.value = {
      appIds: [...params.appIds],
      country: [...params.country],
      groupCodes: [...params.groupCodes],
      teamCodes: [...params.teamCodes],
    };
  } catch (error) {
    console.error('初始化失败:', error);
  } finally {
    loading.value = false;
  }
}
onMounted(() => {
  init()
})

provide('isPublic', isPublic);
provide('indicatorList', indicatorList);
provide('evtLists', evtLists);
provide('dashboardCode', boardCode);

const resize = (size: string, code: number, pageSize: number | undefined, actualDataCount?: number) => {
  let width: number
  let height: number
  switch (size) {
    case 'small':
      width = 5
      height = 4
      break
    case 'middle':
      width = 10
      height = 10
      break
    case 'large':
      width = 20
      // 根据实际数据量和分页大小计算高度
      if (pageSize && actualDataCount !== undefined) {
        const effectiveRowCount = Math.min(actualDataCount, pageSize);
        // 基础高度(表头+分页器) + 每行数据的高度
        height = Math.max(effectiveRowCount * 0.67 + 4.25, 6); // 最小高度为6
      } else {
        height = pageSize ? pageSize * 0.67 + 4.25 : 10;
      }
      break
    default:
      width = 5
      height = 5
  }


  layout.value = layout.value?.map(item => {
    if (item.objectCode === code) {
      let xValue = item.x;
      if (xValue + width > 20) {
        xValue = 20 - width
      }
      return {
        ...item,
        w: width,
        h: height,
        x: xValue
      }
    }
    return item;
  })
};
const deleteItem = (id) => {
  const index = layout.value.findIndex(item => item.objectCode === id);
  if (index !== -1) {
    layout.value.splice(index, 1);
  }
}
const updateParams = (id: string, data: any) => {
  layout.value.forEach(item => {
    if (item.objectCode === id) {
      item.configParams = data
    }
  })
}
// 保存面板
const saveVisible = ref(false)
const saveFormRef = ref()
const saveLoading = ref(false)
const setType = ref('')
const form = reactive({
  name: '',
  isDefault: true
})
const handleLoading = ref(false)

// 保存当前面板
const saveCurrent = async() => {
  handleLoading.value = true
  try {
    const data = {
      name: boardName.value,
      code: boardCode.value,
      dashboardType: 'application',
    }
    await appDashboardSave(data)
    // 新布局保存
    await saveDashboardUI({
      dashboardId: boardCode.value,
      uiConfig: layout.value.map(config => {
        return {
          ...config,
          code: config.i,
          type: config.objectType,
          configParams: config.configParams
        }
      })
    })
    Message.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
    Message.error('保存失败')
  } finally {
    handleLoading.value = false
  }
}
// 另存新面板
const saveAs = () => { 
  form.name = ''
  setType.value = 'saveAs'
  saveVisible.value = true
}
// 新增面板
const addNew = () => {
  setType.value = 'addNew'
  form.name = ''
  saveVisible.value = true
}
const handleSaveCancel = () => {
  saveVisible.value = false
}
const saveReport = debounce(() => {
  saveFormRef.value.validate(async (valid: any) => {
    if (!valid) {
      const data = {
        name: form.name,
        dashboardType: 'application',
        isDefault: form.isDefault
      }
      await appDashboardSave(data).then(res => {
        if (form.isDefault) {
          boardCode.value = res.id
          boardName.value = form.name
        }
        if(setType.value === 'saveAs'){
          saveDashboardUI({
            dashboardId: res.id,
            uiConfig: layout.value.map(config => {
              return {
                ...config,
                code: config.i,
                type: config.objectType,
                configParams: config.configParams
              }
            })
          })
        }
        Message.success("创建成功");
      });
      await getBoardList()
      await getDetail()
      saveVisible.value = false;
      saveLoading.value = false
    }
  })
}, 300);
const earningsCardRefs = ref<any>([]);
const echartCardRefs = ref<any>([]);
const tableCardRefs = ref<any>([]);

const getComponentRef = (item, index) => {
  // 根据组件类型返回对应的ref
  if (item.objectType === 2) {
    return earningsCardRefs.value[index];
  } else if (item.objectType === 3) {
    return echartCardRefs.value[index];
  } else if (item.objectType === 4) {
    return tableCardRefs.value[index];
  }
  return null;
};
// 只刷新可见组件的函数
const refreshVisibleComponents = () => {
  layout.value.forEach((item, index) => {
    // 只刷新已经可见的组件
    if (visibleMap.value[item.i]) {
      const componentRef = getComponentRef(item, index);
      if (componentRef && typeof componentRef.init === 'function') {
        componentRef.init();
      }
    }
  });
};

const refresh = () => {
  // 检查参数是否有变化
  const hasParamsChanged = !isEqual(
    { appIds: params.appIds, country: params.country, groupCodes: params.groupCodes, teamCodes: params.teamCodes },
    { appIds: lastParams.value.appIds, country: lastParams.value.country, groupCodes: lastParams.value.groupCodes, teamCodes: lastParams.value.teamCodes }
  );
  // 只有当参数发生变化时才执行刷新
  if (hasParamsChanged) {
    // 标记参数已经变化
    paramsChanged.value = true;

    // 更新上一次参数值
    lastParams.value = {
      appIds: [...params.appIds],
      country: [...params.country],
      groupCodes: [...params.groupCodes],
      teamCodes: [...params.teamCodes]
    };

    // 只刷新可见的组件，而不是所有组件
    refreshVisibleComponents();

    // 重置参数变化标记
    paramsChanged.value = false;
  }
};

const refreshAll = () => {
  // 刷新所有组件（保留原有逻辑，用于特殊情况）
  layout.value.forEach((item, index) => {
    const componentRef = getComponentRef(item, index);
    if (componentRef && typeof componentRef.init === 'function') {
      componentRef.init();
    }
  });
};
const datePick = (date: any) => {
  params.date = date
  // 日期变化时只刷新可见的组件
  refreshVisibleComponents()
};
const timeChange = (v: any) => {
  params.timeParticleSize = v.timeParticleSize;
  params.firstDayDfWeek = v.firstDayDfWeek;
  // 只刷新可见的表格卡片组件
  layout.value.forEach((item, index) => {
    if (item.objectType === 4 && visibleMap.value[item.i]) { // 只处理可见的表格卡片 (objectType === 4)
      const componentRef = getComponentRef(item, index);
      if (componentRef && typeof componentRef.init === 'function') {
        componentRef.init();
      }
    }
  });
};
const countryChange = (v) => {
  params.country = v
  refresh()
}
const groupPopupVisibleChange = (v) => {
  if(!v){
    refresh()
  }
}

// 保存当前筛选条件
const saveCurrentFilter = async () => {
  try {
    await saveFilter();
    Message.success('筛选条件保存成功');
  } catch (error) {
    Message.error('筛选条件保存失败');
  }
}

// 监听组件可见性变化，当组件变为可见时检查是否需要刷新
watch(
  () => visibleMap.value,
  (newVisibleMap, oldVisibleMap) => {
    // 检查是否有新的组件变为可见
    Object.keys(newVisibleMap).forEach(itemId => {
      if (newVisibleMap[itemId] && !oldVisibleMap?.[itemId]) {
        // 组件刚变为可见，检查参数是否已经变化过
        const hasParamsChanged = !isEqual(
          { appIds: params.appIds, country: params.country, groupCodes: params.groupCodes, teamCodes: params.teamCodes },
          { appIds: lastParams.value.appIds, country: lastParams.value.country, groupCodes: lastParams.value.groupCodes, teamCodes: lastParams.value.teamCodes }
        );

        if (hasParamsChanged) {
          // 找到对应的组件并刷新
          const itemIndex = layout.value.findIndex(item => item.i === itemId);
          if (itemIndex !== -1) {
            const item = layout.value[itemIndex];
            const componentRef = getComponentRef(item, itemIndex);
            if (componentRef && typeof componentRef.init === 'function') {
              componentRef.init();
            }
          }
        }
      }
    });
  },
  { deep: true }
);

// 参数变化时自动保存（防抖）
watch(params, () => {
  saveFilterDebounced();
}, { deep: true });
</script>

<style scoped lang="less">
.page {
  width: auto;
  padding: 24px 0;
  margin: 0 24px;

  .page-body {
    background-color: var(--tant-bg-gray-color-bg2-1);
    margin: 24px 0  0 -14px;
    padding: 0;
    width: calc(100% + 28px);

    .card-layout {
      height: 100%;
      overflow: auto;
      .drag-allow-from {
        width: 100px;
      }

      .vgl-layout {
        //--vgl-placeholder-opacity: 0;
        margin-top: -14px;
        --vgl-placeholder-bg: green;
        height: 100% !important;
      }
    }
  }
}
.edit-select{
    width: 100%;
    position: relative;
    .edit{
        position: absolute;
        right: 30px;
        top: 8px;
        opacity: 0;
        cursor: pointer;
    }
    &:hover{
        .edit{
            opacity: 1;
        }
    }
}

.spin {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .spin-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .spin-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--tant-slate-slate-50);
    }
  }

}

.empty {
  height: 100%;
  width: 100%;
  min-height: 400px;
  // margin: 0 24px 32px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;

  .empty-body {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);

    .empty-img {
      width: 400px;
      height: 256px;
    }

    .empty-description {
      color: var(--tant-text-gray-color-text1-3);
      font-size: 12px;
      width: 400px;
      word-break: break-all;
      text-align: center;
      margin: 0 auto;

      .title {
        margin-bottom: 12px;
        font-weight: 500;
        font-size: 16px;
        color: var(--tant-text-gray-color-text1-2);
      }
    }

  }
}

.footer {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

:deep(.arco-empty) {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center
}
</style>
