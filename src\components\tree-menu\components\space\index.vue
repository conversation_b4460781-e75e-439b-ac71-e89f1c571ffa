<script setup lang="ts">
import {SpaceDto} from "@/api/space/type";
import draggable from "vuedraggable";
import spaceItem from "./item.vue"
import {useEventBus} from "@vueuse/core";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {moveFolder} from "@/api/folder/api";
import {Message} from "@arco-design/web-vue";
import {moveSpace} from "@/api/space/api";
import {useDashboardStore} from "@/store";

interface Props {
  /**
   * 空间列表
   */
  spaces?: SpaceDto[]
}

const props = defineProps<Props>()
const dashboardStore = useDashboardStore()
const emit = defineEmits(['add']);
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const cacheEventBus = useEventBus(CacheEventBus)
const draggableSpace = (e) => {
  const index=e.newIndex;
  moveSpace({spaceId:props.spaces[index].spaceId,order:index+1})
      .then((resp) => {
          cacheEventBus.emit('move-event', {
            type: 'space',
            spaceId: props.spaces[index].spaceId,
            order:index+1,
          });
      })
      .catch((e) => {
        Message.error("重命名失败！", e);
      });
}
</script>

<template>
  <div>
    <draggable
        :disabled="dashboardStore.dashboardSearchStore"
        :list="spaces || []"
        item-key="spaceId"
        @update="draggableSpace"
    >
      <template #item="{ element }">
        <space-item @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}" :space="element"/>
      </template>
    </draggable>
  </div>

</template>

<style scoped lang="less">


</style>