<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
            </div>
        </div>
        <div class="page-body ">
            <div class="cross-content">
                <sendDataConfigure ref="configRef"/>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import {onMounted, ref} from "vue";
import {useEventBus} from "@vueuse/core";
import selectApp from "@/components/selected-game-app/index.vue"
import sendDataConfigure from "./components/sendDataConfigure.vue";
import {LocalStorageEventBus} from "@/types/event-bus";

const route = useRoute();
const gameAppList = ref<any>([])
const localStorageEventBus = useEventBus(LocalStorageEventBus)

const loading = ref(true)
const configRef = ref()

localStorageEventBus.on((name, value) => {
    if (name === "app-id") {
        configRef.value.refresh(value)
    }
})

onMounted(() => {
    configRef.value.refresh(sessionStorage.getItem('app-id'))
})
</script>

<style scoped lang="less">
.page {
    .page-body {
        .cross-content {
            border: 1px solid var(--color-secondary);
            border-left: none;
            border-right: none;
            width: 100%;
            height: 100%;
            display: flex;
        }
    }

    .border-mini{
        border-radius: 2px;
    }
}
</style>
