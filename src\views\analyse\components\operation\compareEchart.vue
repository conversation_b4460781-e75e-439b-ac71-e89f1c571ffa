<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {computed, ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import dayjs from 'dayjs';
import {cloneDeep} from "lodash";

interface Props {
  /**
   * 展示label
   */
  chartType: string
  showlabel: boolean;
  showRate: boolean;
  // 分组值
  groupNum: number;
  /**
   * 报表数据
   */
  eventData: any;
  // y最大值
  yMax: number | null
  // y最小值
  yMin: number | null
  // 时间格式化
  timeFormat: string
  /**
   * 高度100%显示
   */
  heightFull?: boolean
  optionStyle?: any // 选项组样式
}

const props = defineProps<Props>()

const echartType = ref('')
echartType.value = props.chartType
const xAxis = ref<string[]>([]);
const xSeries = ref<any>([]);
const xSeriesData = ref<any>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([{selected: {}}]);
const selected = ref<string[]>([]);
const indeterminate = ref(false)
const checkedAll = ref(false)

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])
// const chartType = ref('trend')

const indicatorsInput = ref('') // 指标搜索
const indicators = ref<any>([]) // 指标
const groupsInput = ref('') // 分组搜索
const groups = ref<any>([]) // 分组
const selectedGroups = ref<string[]>([]);
const checkedDefault = ref(true)
groups.value = props.eventData?.groups?.map(item => {
  return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
}).filter(item => item !== '总体') || [];

// 对比阶段
const compareStage = ref<any>([])


const compareInput = ref('')
const compareSelected = ref<any>([])
const checkedCompareAll = ref(false)
const compareIndeterminate = ref(false)

const updateComparedDateList = (list) => {
  compareStage.value = list.map(item => item.dateText)
}

// 全选
const handleChangeCompareAll = (value) => {
  compareIndeterminate.value = false;
  if (value) {
    checkedCompareAll.value = true;
    compareSelected.value = compareStage.value
  } else {
    checkedCompareAll.value = false;
    compareSelected.value = []
  }
}
const handleCompareChange = (values) => {
  if (values.length === compareStage.value.length) {
    checkedCompareAll.value = true
    compareIndeterminate.value = false;
  } else if (values.length === 0) {
    checkedCompareAll.value = false
    compareIndeterminate.value = false;
  } else {
    checkedCompareAll.value = false
    compareIndeterminate.value = true;
  }
}
selectedGroups.value = groups.value.slice(0, 30) || []

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
const secondaryName = ref('')

const lineSetList = ref<any>([])

const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
const formatValue = (num: number, displayType?: { type: string; decimalNum: number }) => {
  if (displayType && displayType.type) {
    let formattedValue
    if (displayType.type === 'default') {
      if (num % 1 !== 0) {
        formattedValue = num.toFixed(displayType.decimalNum);
      } else {
        formattedValue = Math.floor(num).toString();
      }
    } else if (displayType.type === 'percent') {
      // 乘以 100
      formattedValue = (num * 100).toFixed(displayType.decimalNum);
    }
    return formattedValue;
  }
  return parseFloat(num.toFixed(2))
}
const percentNameList = ref<any>([])
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '0%',
      top: '40',
      bottom: '100',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      bottom: '60',
      type: 'scroll', // 设置图例为滚动类型
      orient: 'horizontal', // 横向显示图例
    },
    //
    xAxis: xSeries.value,
    yAxis: {
      type: 'value',
      max: props.yMax,
      min: props.yMin,
      name: secondaryName.value,
      nameLocation: 'left',
      // axisLabel: {
      //     formatter:props.eventData?.displayType?.type === 'percent' ? '{value} %' : '{value}'
      // }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      axisPointer: {
        type: axisPointerType.value
      },
      formatter: (params) => {
        // 创建一个对象来存储不同 axisIndex 的数据
        const groupedData = {};

        // 遍历 params，将数据按 axisIndex 分组
        params.forEach(param => {
          const index = param.axisIndex; // 获取 axisIndex
          if (!groupedData[index]) {
            groupedData[index] = []; // 初始化数组
          }
          groupedData[index].push(param); // 将当前 param 添加到对应的数组中
        });

        // 生成 tooltip 内容
        let tooltipHtml = '';

        // 遍历分组数据
        Object.keys(groupedData).forEach(index => {
          const seriesInfo = groupedData[index].map(param => {
            const matchingY = ySeries.value.find(y => y.name === param.seriesName);
            return `
                      <div style="display: flex; align-items: center; margin-bottom: 5px;">
                          <span style="flex-shrink: 0;">${param.marker}</span>
                          <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                          <span style="flex-shrink: 0;">${formatNumber(param.value)}${matchingY?.displayType && matchingY.displayType.type === 'percent' ? '%' : ''}</span>
                      </div>
                  `;
          }).join('');

          // 添加每个 axisIndex 的标题和内容
          tooltipHtml += `
                  <div style="margin-bottom: 10px;">
                      <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
                          <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold; margin-bottom: 10px; width: 100%;" title="${groupedData[index][0].axisValue}">${groupedData[index][0].axisValue}</div>
                          ${seriesInfo}
                      </div>
                  </div>
              `;
        });

        return tooltipHtml; // 返回生成的 tooltip HTML
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

// 数组累加
const calculateCumulativeSums = (array: any) => {
  const numericArray = array.map(Number);
  const result = [];

  for (let i = 0; i < numericArray.length; i++) {
    if (i === 0) {
      result.push(numericArray[i]);
    } else {
      const sum = numericArray[i] + result[i - 1];
      result.push(sum);
    }
  }
  return result;
};
// 数组相加
const calculateSum = (array: any) => {
  return Number(array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0));
};

// 时间格式化x轴数据
const formatterXData = () => {
  const formatMap = {
    'm1': 'YYYY-MM-DD HH:mm:ss',
    'm5': 'YYYY-MM-DD HH:mm:ss',
    'm10': 'YYYY-MM-DD HH:mm:ss',
    'h1': 'YYYY-MM-DD HH:mm',
    'D1': 'YYYY-MM-DD',
    'W1': 'YYYY-MM-DD',
    'M1': 'YYYY/MM',
    'Q1': 'YYYY-M',
    'Y1': 'YYYY',
  };

  const format = formatMap[props.timeFormat] || null;
  xSeries.value = []
  const lineItem = lineSetList.value
  const lineItemLength = lineSetList.value.length
  if (format) {
    // 非对比时间数据
    const xlist = props.eventData?.x?.map(item => props.timeFormat !== 'T0' ? dayjs(item).format(format) : item) || [];
    const suffixMap = {
      'W1': '当周',
      'M1': '月',
      'Q1': '季度',
      'Y1': '年'
    };

    xSeries.value.push(
        {
          type: 'category',
          data: props.timeFormat in suffixMap ? xlist.map((item: any) => `${item}${suffixMap[props.timeFormat]}`) : xlist,
          axisLabel: {
            color: '#8E8E8E'
          },
          axisTick: {
            show: false
          },
          name: lineItemLength > 0 && echartType.value === 'trend' ? `${lineItem[lineItemLength - 1].lineName}${lineItem[lineItemLength - 1].showValue ? `:${lineItem[lineItemLength - 1].lineValue}` : ''}` : '',
          nameGap: lineItemLength > 0 && echartType.value === 'trend' ? -36 : null,
          axisLine: lineItemLength > 0 && echartType.value === 'trend' ? {
            show: true,
            lineStyle: {
              color: lineItem[lineItemLength - 1].lineColor,
              type: lineItem[lineItemLength - 1].lineType
            }
          } : {
            show: false
          },
        }
    )
    // 有时间对比的情况下
    if (props.eventData?.xComparedList?.length) {
      props.eventData.xComparedList.forEach((item, index) => {
        xSeries.value.push(
            {
              type: 'category',
              data: props.timeFormat in suffixMap ? item.map((el: any) => `${el}${suffixMap[props.timeFormat]}`) : item,
              axisLabel: {
                color: '#8E8E8E'
              },
              offset: 20 * (index + 1),
              axisTick: {
                show: false
              },
              name: lineItemLength > 0 && echartType.value === 'trend' ? `${lineItem[lineItemLength - 1].lineName}${lineItem[lineItemLength - 1].showValue ? `:${lineItem[lineItemLength - 1].lineValue}` : ''}` : '',
              nameGap: lineItemLength > 0 && echartType.value === 'trend' ? -36 : null,
              axisLine: lineItemLength > 0 && echartType.value === 'trend' ? {
                show: true,
                lineStyle: {
                  color: lineItem[lineItemLength - 1].lineColor,
                  type: lineItem[lineItemLength - 1].lineType
                }
              } : {
                show: false
              },
              position: 'bottom'
            }
        )
      })
    }
  }
}

// 辅助线设置
const guideLineSet = () => {

}
// arr 指标显示设置
const freshData = (arr?: any, line?: any) => {
  axisPointerType.value = 'line'
  formatterXData()
  const data = [] as any;
  const yLength = props.eventData?.y.length
  props.eventData?.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if (yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {

        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        displayType: item.displayType,
        values: el.values.map(format => formatValue(format, item.displayType)),
        valuesCompared: el.valuesCompared
      });
    });
  });

  legendData.value = data.map(item => item.name);
  indicators.value = props.eventData?.y.map(item => item.displayName)
  let commonSeries
  if (arr && arr.length) {
    const nameString = arr.filter(item => item.showSub).map(item => item.name).join('/')
    secondaryName.value = nameString.split('').map(str => `${str}\n`).join('')
    commonSeries = data.flatMap((item, index) => {
      const seriesItems = [
        {
          name: item.name,
          data: item.values,
          displayType: item.displayType,
          type: arr[index].type,
          barWidth: arr[index].type === 'bar' ? 40 : '',
          label: {
            show: props.showlabel
          },
          xAxisIndex: 0
        }
      ];
      // 如果 valuesCompared 有值，添加相同结构
      if (item.valuesCompared && item.valuesCompared.length > 0) {
        const comparedItems = item.valuesCompared.map((comparedValues, comparedIndex) => {
          return {
            name: item.name, // 可以根据需要修改名称
            data: comparedValues.map(format => formatValue(format, item.displayType)), // 使用当前的 comparedValues
            displayType: item.displayType,
            type: arr[index].type,
            barWidth: arr[index].type === 'bar' ? 40 : '',
            label: {
              show: props.showlabel
            },
            xAxisIndex: comparedIndex + 1,
            lineStyle: {
              width: 2,
              type: 'dashed'
            }
          };
        });
        // 将比较项添加到 seriesItems 中
        seriesItems.push(...comparedItems);
      }

      return seriesItems; // 返回所有系列项
    });
  } else {
    commonSeries = data.flatMap((item, index) => {
      const seriesItems = [
        {
          name: item.name,
          data: item.values,
          displayType: item.displayType,
          type: 'line',
          label: {
            show: props.showlabel
          },
          xAxisIndex: 0
        }
      ];
      // 如果 valuesCompared 有值，添加相同结构
      if (item.valuesCompared && item.valuesCompared.length > 0) {
        const comparedItems = item.valuesCompared.map((comparedValues, comparedIndex) => {
          return {
            name: item.name, // 可以根据需要修改名称
            data: comparedValues.map(format => formatValue(format, item.displayType)), // 使用当前的 comparedValues
            displayType: item.displayType,
            type: 'line',
            label: {
              show: props.showlabel
            },
            xAxisIndex: comparedIndex + 1,
            lineStyle: {
              width: 2,
              type: 'dashed'
            }
          };
        });
        // 将比较项添加到 seriesItems 中
        seriesItems.push(...comparedItems);
      }
      return seriesItems; // 返回所有系列项
    });
  }
  if (echartType.value === 'trend' && line && line.length > 0) {
    lineSetList.value = line
  }
  ySeries.value = commonSeries;
  selected.value = indicators.value;
  checkedAll.value = true;
  compareSelected.value = compareStage.value;
  checkedCompareAll.value = true;
  ySeriesData.value = cloneDeep(ySeries.value)
  xSeriesData.value = cloneDeep(xSeries.value)
};

const freshTotalData = () => {
  axisPointerType.value = 'line'
  formatterXData()
  const yLength = props.eventData?.y.length
  const data = [] as any;
  props.eventData.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if (yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {
        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        // name:item.displayName,
        displayType: item.displayType,
        values: el.values.map(format => formatValue(format, item.displayType)),
        valuesCompared: el.valuesCompared
      });
    });
  });

  ySeries.value = data.flatMap((item, index) => {
    const seriesItems = [
      {
        name: item.name,
        data: calculateCumulativeSums(item.values),
        displayType: item.displayType,
        type: 'line',
        label: {
          show: props.showlabel
        },
        xAxisIndex: 0
      }
    ];
    // 如果 valuesCompared 有值，添加相同结构
    if (item.valuesCompared && item.valuesCompared.length > 0) {
      const comparedItems = item.valuesCompared.map((comparedValues, comparedIndex) => {
        return {
          name: item.name, // 可以根据需要修改名称
          data: calculateCumulativeSums(comparedValues), // 使用当前的 comparedValues
          displayType: item.displayType,
          type: 'line',
          label: {
            show: props.showlabel
          },
          xAxisIndex: comparedIndex + 1,
          lineStyle: {
            width: 2,
            type: 'dashed'
          }
        };
      });
      // 将比较项添加到 seriesItems 中
      seriesItems.push(...comparedItems);
    }
    return seriesItems; // 返回所有系列项
  });
  indicators.value = props.eventData?.y.map(item => item.displayName)
  legendData.value = data.map(item => item.name);

  selected.value = indicators.value;
  checkedAll.value = true;
  compareSelected.value = compareStage.value;
  checkedCompareAll.value = true;
  ySeriesData.value = cloneDeep(ySeries.value)
  xSeriesData.value = cloneDeep(xSeries.value)
};

const freshDistributionData = () => {
  axisPointerType.value = 'shadow'
  const data = [] as any;
  const yLength = props.eventData?.y.length
  props.eventData?.y.forEach(item => {
    item.yData.forEach(el => {
      let nameStr
      const processedGroup = el.group.map(value => value === null ? 'null' : value).filter(item => item !== '总体');
      if (yLength > 1 && processedGroup.length > 0) {
        nameStr = `${item.displayName}.(${processedGroup.join(',')})`;
      } else if (yLength <= 1 && processedGroup.length > 0) {
        nameStr = processedGroup.join(',');
      } else {

        nameStr = `${item.displayName}`;
      }
      data.push({
        name: nameStr,
        displayType: item.displayType,
        values: calculateSum(el.values.map(format => formatValue(format, item.displayType))),
        valuesCompared: el.valuesCompared ? el.valuesCompared.map(val => calculateSum(val)) : []
      });
    });
  });

  xSeries.value = [{
    type: 'category',
    data: data.map(item => item.name),
    axisLabel: {
      color: '#8E8E8E'
    },
    axisTick: {
      show: false
    }
  }]
  const compareParamsList = ['上一阶段']
  legendData.value = ['原始日期'].concat(compareParamsList)
  const dataSumList = data.map(el => [el.values].concat(el.valuesCompared))
  ySeries.value = legendData.value.map((item, index) => {
    return {
      name: item,
      data: dataSumList.map(val => val[index]),
      type: 'bar',
      barWidth: 40,
      label: {
        show: props.showlabel
      },
    }
  })
  indicators.value = props.eventData?.y.map(item => item.displayName)
  // legendData.value = groups.value.map(item => item);
  selected.value = indicators.value;
  checkedAll.value = true;
  compareSelected.value = compareStage.value;
  checkedCompareAll.value = true;
  ySeriesData.value = cloneDeep(ySeries.value)
  xSeriesData.value = cloneDeep(xSeries.value)
};


const changeType = (type: string) => {
  echartType.value = type
  switch (type) {
    case 'trend':
      freshData()
      break;
    case 'total':
      freshTotalData()
      break;
    case 'distribution':
      freshDistributionData()
      break;
    default:
      break;
  }
}

changeType(props.chartType)

// 全选
const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    selected.value = legendData.value
  } else {
    checkedAll.value = false;
    selected.value = []
  }
}
const handleChange = (values) => {
  if (values.length === legendData.value.length) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
}

const handleChangeGroups = (values) => {

}
const handleChartSeries = () => {
  const isIndicator = indicators.value.length > 1
  const isOverall = groups.value.length > 0;
  ySeries.value = ySeriesData.value.filter(range => {
    const matchesSelected = isIndicator && selected.value.some(group => range.name.includes(group));
    const matchesGroups = isOverall && selectedGroups.value.some(group => range.name.includes(group));
    if (isIndicator && !isOverall) {
      return matchesSelected; // 仅匹配 indicators
    }
    if (isIndicator && isOverall) {
      return matchesSelected && matchesGroups; // 同时匹配 indicators 和 groups
    }
    if (!isIndicator && isOverall) {
      return matchesGroups; // 仅匹配 groups
    }
    // 如果以上条件都不满足，则使用 selected.value 进行过滤
    return selected.value.includes(range.name);
  });
}
const handleBarChart = () => {
  const isOverall = groups.value.length > 0;
  let filterData = []
  filterData = xSeriesData.value[0]?.data.filter(item => {
    return selected.value.some(sel => item.includes(sel));
  });
  if (isOverall) {
    filterData = xSeriesData.value[0]?.data.filter(item => {
      return selected.value.some(sel => item.includes(sel)) && selectedGroups.value.some(sel => item.includes(sel));
    });
  }
  if (filterData.length === 0) {
    ySeries.value = [];
  } else {
    ySeries.value = ySeriesData.value
  }
  xSeries.value[0].data = filterData
}
const updateYAxisSeriesGroups = () => {
  if (echartType.value === 'distribution') {
    handleBarChart()
  } else {
    handleChartSeries()
  }
};
const updateYAxisSeries = () => {
  if (echartType.value === 'distribution') {
    handleBarChart()
  } else {
    handleChartSeries()
  }
};
const updateCompareSeries = () => {
  // 
}
// 分组
watch(selectedGroups, (newValue: any, oldValue: any) => {
  if (newValue) {
    updateYAxisSeriesGroups();
  }
})
// 指标
watch(selected, (newValue: any, oldValue: any) => {
  if (newValue) {
    updateYAxisSeries();
  }
})
// 对比阶段
watch(compareSelected, (newValue: any, oldValue: any) => {
  if (newValue) {
    updateCompareSeries()
  }
})

watch(() => props.showlabel, (newValue: any, oldValue: any) => {
  ySeries.value.forEach((item: any, index: number) => {
    item.label = {
      show: props.showlabel
    }
  })
}, {immediate: true})

// watch(() => props.comparedDateList,(newValue:any,oldValue:any) => {
//   compareStage.value = props.comparedDateList.map(item => item.dateText)
// },{immediate:true})

// 默认选中的分组数设置
const showSide = ref(false)
const limitCustom = ref(30)
const limitType = ref('more')
const limitGroupNumber = ref(30)
const limitChange = (v) => {
  if (v === 'less') {
    limitGroupNumber.value = 10
    limitCustom.value = 10
  } else if (v === 'middle') {
    limitGroupNumber.value = 20
    limitCustom.value = 20
  } else if (v === 'more') {
    limitGroupNumber.value = 30
    limitCustom.value = 30
  } else {
    limitGroupNumber.value = limitCustom.value
  }
}

const checkedChangeDefault = (value) => {
  if (value) {
    const copyGroups = cloneDeep(groups.value);
    checkedDefault.value = true;
    selectedGroups.value = copyGroups.slice(0, limitGroupNumber.value);
  } else {
    checkedDefault.value = false
    selectedGroups.value = []
  }
}
const limitCancel = () => {
  limitType.value = 'less'
  limitGroupNumber.value = 30
  showSide.value = false
}
const limitSure = () => {
  groups.value = props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  selectedGroups.value.splice(limitGroupNumber.value);
  changeType(echartType.value)
  showSide.value = false

}
const popChange = () => {
  showSide.value = false
}


const filteredIndicators = computed(() => {
  const str = indicatorsInput.value.trim();
  return indicators.value.filter(item => item.includes(str));
});
const filteredGroups = computed(() => {
  const str = groupsInput.value.trim()
  return groups.value.filter(item => item.includes(str));
});
const filteredCompareStage = computed(() => {
  const str = compareInput.value.trim();
  return compareStage.value.filter(item => item.includes(str));
});

defineExpose({
  changeType,
  freshData,
})

</script>

<template>
  <div class="chart-content">
    <a-spin style="width: 100%;height: 500px;" :style="heightFull?'height: 100%':''">
      <div style="display: flex;justify-content: space-between;margin-top: 5px;" :style="optionStyle">
        <div>
        </div>
        <div style="display: flex;">
          <!-- 对比阶段 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true">
            <div v-if="compareStage.length>0" style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">对比阶段(
              {{ compareSelected.length }}/{{ compareStage.length }})
              <icon-down/>
            </div>
            <template #content>
              <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input v-model="compareInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <a-checkbox-group v-if="filteredCompareStage.length" v-model="compareSelected" direction="vertical" style="width: 100%;" @change="handleCompareChange">
                <a-checkbox v-for="(item,index) in filteredCompareStage" :key="index" :value="item">
                  <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                </a-checkbox>
              </a-checkbox-group>
              <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);">
                <a-checkbox :model-value="checkedCompareAll" :indeterminate="compareIndeterminate" style="width: 100%;" @change="handleChangeCompareAll">全选</a-checkbox>
              </div>
            </template>
          </a-dropdown>
          <!-- 指标 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true">
            <div v-if="indicators.length>1" style="min-height: 32px;padding: 4px 8px;margin-left: 16px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">指标(
              {{ selected.length }}/{{ indicators.length }})
              <icon-down/>
            </div>
            <template #content>
              <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input v-model="indicatorsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <a-checkbox-group v-if="filteredIndicators.length" v-model="selected" direction="vertical" style="width: 100%;" @change="handleChange">
                <a-checkbox v-for="(item,index) in filteredIndicators" :key="index" :value="item">
                  <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                </a-checkbox>
              </a-checkbox-group>
              <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);">
                <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" style="width: 100%;" @change="handleChangeAll">全选</a-checkbox>
              </div>
            </template>
          </a-dropdown>
          <!-- 分组项 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true" @popup-visible-change="popChange">
            <div v-if="groups.length>0 && echartType !== 'pieTrend'"
                 style="min-height: 32px;margin-left: 16px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;">分组( {{
                selectedGroups.length
              }}/{{ groups.length }})
              <icon-down/>
            </div>
            <template #content>
              <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input v-model:model-value="groupsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <a-checkbox-group v-if="filteredGroups.length" v-model="selectedGroups" direction="vertical" style="width: 100%;" @change="handleChangeGroups">
                <a-checkbox v-for="(item,index) in filteredGroups" :key="index" :value="item">
                  <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                </a-checkbox>
              </a-checkbox-group>
              <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);display: flex;align-items: center;">
                <a-checkbox :model-value="checkedDefault" style="width: 100%;" @change="checkedChangeDefault">默认</a-checkbox>
                <div class="icon-set" @click="() => showSide = !showSide">
                  <icon-settings/>
                </div>
              </div>
              <!-- 默认设置 -->
              <div v-if="showSide" class="ta-default-group-side-left">
                <div class="group-side">
                  <p class="group-side-head">默认选中的分组数</p>
                  <div class="group-side-body">
                    <a-radio-group v-model:model-value="limitType" direction="vertical" style="width: 100%;" @change="limitChange">
                      <a-radio value="less">
                        <div class="radio-desc">
                          <span>少</span>
                          <span class="limit-set">10</span>
                        </div>
                      </a-radio>
                      <a-radio value="middle">
                        <div class="radio-desc">
                          <span>中</span>
                          <span class="limit-set">20</span>
                        </div>
                      </a-radio>
                      <a-radio value="more">
                        <div class="radio-desc">
                          <span>多</span>
                          <span class="limit-set">30</span>
                        </div>
                      </a-radio>
                      <a-radio value="custom">
                        <div class="radio-desc">
                          <span>自定义分组</span>
                          <a-input-number v-model:model-value="limitCustom" :disabled="limitType !== 'custom'" :style="{width:'66px',height:'24px'}" :precision="0" :min="1" :hide-button="true"/>
                        </div>
                      </a-radio>
                    </a-radio-group>
                    <div class="group-info-wrap">
                      <p class="group-info">默认选中前<b class="bold">{{ limitGroupNumber }}</b>个分组项</p>
                      <p class="group-info">分组项与当前排序设置一致</p>
                      <p class="group-info">仅柱图支持 50 个分组项以上的展示</p>
                    </div>
                  </div>
                  <div class="group-side-foot">
                    <a-button class="cancel" @click="limitCancel">取消</a-button>
                    <a-button type="primary" @click="limitSure">确定</a-button>
                  </div>
                </div>
              </div>
            </template>
          </a-dropdown>
        </div>
      </div>
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}

:deep(.arco-dropdown-option) {
  padding: 0;
}

:deep(.arco-dropdown-option-content) {
  max-width: 200px;
}

:deep(.arco-checkbox) {
  padding: 0 12px;

  &:hover {
    background-color: var(--color-fill-2);
  }
}

.pie-name {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-header-font-header5-medium);
  margin-top: 24px;
}

:deep(.arco-carousel-arrow > div) {
  z-index: 9999;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

:deep(.arco-carousel-arrow > div>svg) {
  color: var(--tant-text-gray-color-text1-2);
}

.download-txt {
  margin: 0 4px;
  color: var(--tant-primary-color-primary-default);
  cursor: pointer;
}

.icon-set {
  padding-right: 12px;
  cursor: pointer;
  color: var(--tant-primary-color-primary-hover);
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

.ta-default-group-side-left {
  position: absolute;
  top: 0;
  left: -285px;
  width: 280px;

  height: 0;

  .group-side {
    width: 100%;
    padding: 16px 16px 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: none;
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-bottom);

    .group-side-head {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 30px;
      margin-bottom: 16px;
      padding-left: 0;
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
    }

    .group-side-body {
      min-height: 220px;
      padding-left: 0;

      .radio-desc {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .limit-set {
          color: var(--tant-text-gray-color-text1-3);
        }
      }

      :deep(.arco-radio-label) {
        width: 100%;
      }

      .group-info-wrap {
        margin-top: 24px;
        margin-bottom: 24px;

        .group-info {
          width: 100%;
          margin-bottom: 8px;
          margin-left: 0;
          color: var(--tant-text-gray-color-text1-3);
          font-size: 14px;
          line-height: 22px;

          .bold {
            margin: 0 4px;
            color: var(--tant-text-gray-color-text1-2);
          }
        }

        .group-info:before {
          position: relative;
          top: 3px;
          padding-right: 10px;
          font-weight: 500;
          font-size: 20px;
          content: "\b7";
        }
      }
    }

    .group-side-foot {
      margin-right: -16px;
      margin-left: -16px;
      padding: 8px 16px 0;
      text-align: right;
      border-top: 1px solid var(--tant-border-color-border1-1);

      button {
        border-radius: 4px;
      }
    }
  }
}
</style>