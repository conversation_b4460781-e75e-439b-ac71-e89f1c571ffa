<script setup lang="ts">
import {ref} from 'vue';
import 'video.js/dist/video-js.css';

interface Img {
  src: string;
  id: string
  type: string
  name: string
}

const chooseType = ref([
  {type: '全部', id: 'ul1'},
  {type: '热门资源', id: 'ul2'},
  {type: '视频', id: 'ul3'},
  {type: '图片', id: 'ul4'},
  {type: '压缩文件', id: 'ul5'},
  {type: '文档类', id: 'ul6'},
  {type: '音频', id: 'ul7'},
  {type: 'mp4', id: 'ul8'},
  {type: 'png', id: 'ul9'},
  {type: 'psd', id: 'ul10'},
  {type: 'rar', id: 'ul11'}])
const resourceVideo = ref<Img[]>([
  {src: "http://vjs.zencdn.net/v/oceans.mp4", id: 'ul3', type: '视频', name: '模拟视频1.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频2.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频3.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频4.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频5.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频6.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频7.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频8.mp4'},
  {src: 'http://vjs.zencdn.net/v/oceans.mp4', id: 'ul3', type: '视频', name: '模拟视频9.mp4'},


])
const resourcePic = ref<Img[]>([
  {src: 'https://img95.699pic.com/photo/40250/3647.jpg_wh300.jpg', id: 'ul4', type: '图片', name: '模拟图片.1'},
  {src: 'https://th.bing.com/th/id/OIP.WufW6zjpR9s4z0Lwm1gFJwHaE7?rs=1&pid=ImgDetMain', id: 'ul4', type: '图片', name: '模拟图片.2'},
  {src: 'https://th.bing.com/th/id/OIP.Cq4khLPWJj0oPbx9P_dRbwAAAA?rs=1&pid=ImgDetMain', id: 'ul4', type: '图片', name: '模拟图片.3'},
  {src: 'https://pic.616pic.com/ys_bnew_img/00/46/22/Pl9yMjnFI9.jpg', id: 'ul4', type: '图片', name: '模拟图片.4'},
  {src: 'https://pic.616pic.com/ys_bnew_img/00/46/07/B6depJAwJc.jpg', id: 'ul4', type: '图片', name: '模拟图片.5'},
  {src: 'https://bpic.588ku.com/back_origin_min_pic/22/12/10/1d25679a2830101cdce00198fc965134.jpg!/fw/750/quality/99/unsharp/true/compress/true', id: 'ul4', type: '图片', name: '模拟图片.6'},
  {src: 'https://pic.616pic.com/ys_bnew_img/00/46/24/7isQ05T2Hv.jpg', id: 'ul4', type: '图片', name: '模拟图片.7'},
  {src: 'https://th.bing.com/th/id/OIP.f1gu-1kwVpytPbjapTkNIQHaE8?rs=1&pid=ImgDetMain', id: 'ul4', type: '图片', name: '模拟图片.8'},
  {src: 'https://img95.699pic.com/photo/50053/7402.jpg_wh860.jpg', id: 'ul4', type: '图片', name: '模拟图片.9'},

])
const resourceFile = ref<Img[]>([
  {src: '', id: 'ul5', type: '压缩文件', name: '模拟视频.mp4'},
  {src: '', id: 'ul5', type: '压缩文件', name: '模拟视频.mp4'},
])
const resourceWord = ref<Img[]>([
  {src: '', id: 'ul6', type: '文档类', name: '模拟视频.mp4'},
  {src: '', id: 'ul6', type: '文档类', name: '模拟视频.mp4'},
])
const resourceMusic = ref<Img[]>([
  {src: '', id: 'ul7', type: '音频', name: '模拟视频.mp4'},
  {src: '', id: 'ul7', type: '音频', name: '模拟视频.mp4'},
])
// 点击事件--选择类型

const selectedType = () => {
  document.querySelectorAll('.type').forEach(item => {
    item.addEventListener('click', function () {
      document.querySelectorAll('.type').forEach(el => el.classList.remove('on'));
      document.querySelectorAll('.ul-container').forEach(el => el.style.display = 'none');
      this.classList.add('on');
      const targetId = this.getAttribute('data-target');
      const targetUl = document.getElementById(targetId);
      if (targetUl) {
        targetUl.style.display = 'flex';
      }
    });
  });
}
const getDivStyle = (id: string) => {
  return id === 'ul1' ? 'flex' : 'none'; // 根据 id 设定显示方式
};
</script>

<template>
<div class="head">
  <div class="head-background">
    <div class="time-tag">
      统计时间截至：2022-9-30-至今
    </div>
    <div class="head-layout">
      <div class="items">
        <div class="icon-color">
          <icon-codepen/>
        </div>
        <div>
          <div>3D</div>
          <div>313</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-image/>
        </div>
        <div>
          <div>图片</div>
          <div>313</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-file-video/>
        </div>
        <div>
          <div>视频</div>
          <div>1231</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-file-audio/>
        </div>
        <div>
          <div>音频</div>
          <div>4141</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-scissor/>
        </div>
        <div>
          <div>后期工程</div>
          <div>312</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-delete/>
        </div>
        <div>
          <div>UE虚幻引擎</div>
          <div>3131</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-delete/>
        </div>
        <div>
          <div>Unity</div>
          <div>366</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-file-image/>
        </div>
        <div>
          <div>文档类</div>
          <div>7878</div>
        </div>
      </div>
      <div class="items">
        <div class="icon-color">
          <icon-file-image/>
        </div>
        <div>
          <div>压缩文件</div>
          <div>131</div>
        </div>
      </div>
    </div>
  </div>
</div>
  <div class="tabs-background">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" title="全部资源">
        <div class="search">
          <div class="search-input">
            <a-input class="input" placeholder="请输入搜索">
              <template #prefix>
                <icon-search />
              </template>
            </a-input>
            <div class="search-select">
              <a-select default-value="按更新时间逆序" :style="{width:'152px'}">
                <template #search-icon></template>
                <a-option>按更新时间逆序</a-option>
                <a-option>按更新时间顺序</a-option>
              </a-select>
            </div>
          </div>
        </div>
        <div class="choose">
          <div class="choose-type">
            <div
                v-for="items in chooseType" :key="items.id"
                :data-target=items.id
                :class="{'type':'true','on':items.type==='全部'}"
            >
              <div class="type-name " @click="selectedType">
                {{ items.type }}
              </div>
            </div>
          </div>
          <div class="resource">
            <div
                id="ul1"
                class="ul-container "
                :style="{ display: getDivStyle('ul1') }"
            >
              <ul
                  v-for="item in [...resourceVideo, ...resourcePic, ...resourceFile, ...resourceWord, ...resourceMusic]"
                  :key="item.src" class="ul-li">
                <li v-if="item.type === '视频'">
                  <video class="video-js vjs-default-skin vjs-big-play-centered" width="210" height="300" controls>
                    <source :src="item.src" type="video/mp4"/>
                  </video>
                  <div class="video-name">{{ item.name }}</div>
                </li>
                <li v-if="item.type === '图片'">
                  <img :src=item.src width="210" height="300" />
                  <div class="video-name">{{ item.name }}</div>

                </li>
<!--                <li v-if="item.type === '压缩文件'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === '文档类'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === '音频'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === 'mp4'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === 'png'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === 'psd'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
<!--                <li v-if="item.type === 'rar'">-->
<!--                  <div class="video-name">{{ item.name }}</div>-->

<!--                </li>-->
              </ul>
            </div>
            <div id="ul2" class="ul-container">
              <ul v-for="item in [...resourceVideo, ...resourcePic, ...resourceFile, ...resourceWord, ...resourceMusic]"
                  :key="item.src" class="ul-li">
                <li v-if="item.type === '视频'">
                  <video class="video-js vjs-default-skin vjs-big-play-centered" width="210" height="300" controls>
                    <source :src="item.src" type="video/mp4"/>
                  </video>
                  <div class="video-name">{{ item.name }}</div>
                </li>
              </ul>
            </div>
            <div id="ul3" class="ul-container">
              <ul v-for="items in resourceVideo" :key="items.id" class="ul-li">
                <li>
                  <video
                      ref="video"
                      class="video-js vjs-default-skin vjs-big-play-centered"
                      width="210" height='300' controls>
                    <source :src=items.src type="video/mp4"/>
                    <source :src=items.src type="video/WebM"/>
                  </video>
                  <div class="video-name">{{ items.name }}</div>
                </li>
              </ul>
            </div>
            <div id="ul4" class="ul-container">
              <ul v-for="items in resourcePic" :key="items.id" class="ul-li">
                <li>
                  <img :src=items.src width="210" height="300" />
                  <div>{{items.name}}</div>
                </li>
              </ul>
            </div>
            <div id="ul5" class="ul-container">
              <ul v-for="items in resourceFile" :key="items.id" class="ul-li">
                <li>{{ items.src }}</li>
              </ul>
            </div>
            <div id="ul6" class="ul-container">
              <ul v-for="items in resourceWord" :key="items.id" class="ul-li">
                <li>{{ items.src }}</li>
              </ul>
            </div>
            <div id="ul7" class="ul-container">
              <ul v-for="items in resourceMusic" :key="items.id" class="ul-li">
                <li>{{ items.src }}</li>
              </ul>
            </div>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" title="最近更新">
        Content of Tab Panel 2
      </a-tab-pane>
      <a-tab-pane key="3" title="项目动态">
        Content of Tab Panel 3
      </a-tab-pane>
      <a-tab-pane key="4" title="与我分享">
        Content of Tab Panel 4
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped lang="less">
.head{
  background-color: var(--tant-bg-white-color-bg1-1);
  height: 140px;
  .head-background {
    height: 140px;
    margin:0 14px;
    box-shadow: inset 4px 1px 100px rgba(0, 0, 0, 0.02);
    border-bottom: solid 1px var(--tant-fill-color-fill1-1);

    .time-tag {
      display: flex;
      justify-content: end;
      color: var(--tant-text-gray-color-text1-3);
      font-size: 11px;
      padding: 10px 10px 0 0;
      margin-right: 40px;
    }

    .head-layout {
      height: auto;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      padding-top: 20px;

      .items {
        display: flex;
        flex-direction: row;
        font-size: 15px;
        gap: 4px;
      }
    }
  }
}


.tabs-background {
  height: 100%;
  min-height: 400px;
  background-color: var(--tant-bg-white-color-bg1-1);

  .search {
    display: flex;
    flex-direction: column;

    .search-input {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-left:10px ;
      .input {
        width: 190px;
        margin-right: 8px;
        padding: 0 8px;
        background-color: var(--tant-bg-white-color-bg1-1);
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: var(--tant-border-radius-medium);
        line-height: 32px;
        height: 32px;

        .position-icon {
          position: relative;
          left: 180px;
        }

        .inputCss {
          outline: none;
          height: 32px;
          padding: 0 8px;
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          line-height: 30px;
          background-color: transparent;
          border: 1px solid var(--tant-border-color-border1-1);
          border-radius: var(--tant-border-radius-medium);
        }

        .inputCss:hover {
          background-color: var(--tant-bg-white-color-bg1-1);
          border: 1px solid var(--tant-primary-color-primary-hover);
        }

      }
      .input:hover {
        background-color: var(--tant-bg-white-color-bg1-1);
        border: 1px solid var(--tant-primary-color-primary-hover);
      }


      .search-select {
        display: flex;
        padding-right: 10px;

      }
    }
  }

  .choose {
    display: flex;
    flex-direction: column;

    .choose-type {
      display: flex;
      flex-direction: row;
      justify-content: start;
      align-items: center;
      gap: 10px;
      font-size: 12px;
      padding: 10px;
      margin-left: 8px;
      color: var(--tant-text-gray-color-text1-1);

      .type {
        padding-right: 5px;
        cursor: pointer;

        .type-name {
          font-size: 12px;
          font-weight: 500;
        }
      }

      .on {
        color: var(--tant-primary-color-primary-default);
      }

      .type:nth-child(1) {
        border-right: solid 1px var(--tant-border-color-border1-1);
      }

      .type:nth-child(2) {
        border-right: solid 1px var(--tant-border-color-border1-1);
      }

      .type:nth-child(7) {
        border-right: solid 1px var(--tant-border-color-border1-1);
      }
    }

    .resource {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .ul-container {
        display: none; /* Initially hidden */
        margin-left: 10px;
        padding: 0;
        list-style: none;
        flex-wrap: wrap;
        overflow-y: scroll;
        .ul-li {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          width: 220px;
          padding: 5px;
          gap: 20px;

          .video-name {
            display: flex;
            justify-content: center;
            align-self: center;
            overflow: hidden;
            font-size: 12px;
            padding: 5px;
          }
        }
      }
    }


  }
}

.icon-color {
  color: var(--tant-status-success-color-success-hover);
}

</style>