<script setup lang="ts">
import {ref,watch} from "vue";
import * as XLSX from 'xlsx';
import { useLocalStorage} from '@vueuse/core';
import {cloneDeep} from "lodash"
import {DateRange} from "@/api/analyse/type";
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {getCustomStringLength,createCustomSorter} from "@/utils/strUtil";

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  retentionType:string
}

const props = defineProps<Props>()

const columns = ref<any>([]);
const tableData = ref<any>([]);

const dateList = ref<any>([])

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])


// 判断周几
const getDayOfWeek = (dateString) => {
  const timeSpan = props.eventData?.timeSpan
  if(timeSpan.unit === 'DAY'){
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if(timeSpan.unit === 'WEEK'){
    return `${dateString}周`;
  }
  return `${dateString}月`;
}
// 处理表格颜色深浅
const getBackgroundColor = (value) => {
    // 如果没有值，返回白色
    if (value === null || value === undefined || value === '') {
        return 'rgb(255, 255, 255)'; // 白色
    }
   // 确保值是数字
   const numValue = Number(value);
    if (Number.isNaN(numValue)) {
        return 'rgb(255, 255, 255)';
    }
    // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
    const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
    return `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
}

const processData = (numType) => {
    const retentionNumData = props.eventData?.retentionQueryResult.find(item => item.type === `${numType}_num`).resultData;
    const retentionRateData = props.eventData?.retentionQueryResult.find(item => item.type === `${numType}_rate`).resultData;
    return retentionNumData.map((numData, index) => {
        const rateData = retentionRateData[index];
        // 获取格式化后的日期
        const formattedDate = numData.date === '阶段值' ? numData.date : getDayOfWeek(numData.date);
        // 创建对象
        const rowData = {
            date: formattedDate,
            userNum: numData?.summaryData?.[0], //  第一项
            groupData:numData.groupData,
            groupRateData:rateData.groupData
        };
        // 添加时间数据
        numData?.summaryData.forEach((value, idx) => {
            rowData[`time${idx}`] = value;
        });
        // 添加率数据
        rateData?.summaryData.forEach((value, idx) => {
            rowData[`rate${idx}`] = Number((value * 100).toFixed(2)); // 将率转换为百分比
        });
        return rowData;
    });
};
const groups = ref<any>([]) // 分组
const tipHeaderName = ref('')
groups.value = props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
}).filter(item => item !== '总体') || [];

// 生成基础列配置
const getBaseColumns = (eventName: string) => [
  {
    title: '日期',
    dataIndex: 'date',
    slotName: 'date',
    width: 140,
    fixed: 'left',
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title: eventName,
    dataIndex: 'userNum',
    slotName: 'userNum',
    width: 140,
    fixed: 'left',
    bodyCellStyle: { backgroundColor: 'rgb(249, 249, 251)' },
    sortable: { sortDirections: ['ascend', 'descend'] }
  }
];

// 生成日期列配置
const getDateColumns = (dates: string[], isRetention = false) => {
  return dates.map((item, index) => ({
    title: item,
    dataIndex: `time${isRetention ? index + 1 : index + 1}`,
    rateIndex: `rate${index}`,
    slotName: `time${isRetention ? index + 1 : index + 1}`,
    ...(isRetention && { titleSlotName: `timeHeader${index}` }),
    minWidth: 140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  }));
};

const freshData = (type?: string) => {
  const numType = type || props.retentionType;
  const timeSpan = props.eventData?.timeSpan;
  const eventName = props.eventData?.events?.[0]?.eventDisplayName;
  let unitName = '';
  
  if (timeSpan.unit === 'DAY') {
    unitName = '日';
    tipHeaderName.value = '当日';
  } else if (timeSpan.unit === 'WEEK') {
    unitName = '周';
    tipHeaderName.value = '当周';
  } else {
    unitName = '月';
    tipHeaderName.value = '当月';
  }

  const isRetention = numType !== 'churn';
  
  // 如果有分组项，直接显示分组数据
  if (props.eventData?.groupsDesc?.length) {
    const groupsDescList = props.eventData.groupsDesc;
    
    // 添加时间列
    const timeColumn = {
      title: '日期',
      dataIndex: 'date',
      slotName: 'date',
      width: 140,
      fixed: 'left',
      sortable: { sortDirections: ['ascend', 'descend'] }
    };
    
    // 生成分组列
    const newGroupColumns = groupsDescList.map((item, idx) => ({
        title: item.name,
        dataIndex: `group${idx}`,
        slotName: `group${idx}`,
        width: getCustomStringLength(item.name) + 80,
        fixed: 'left',
        sortable: {
            sortDirections: ['ascend', 'descend'],
            sorter: (a, b, extra) => {
                const { direction } = extra;
                const format = item.format;
                const valueA = a[`group${idx}`];
                const valueB = b[`group${idx}`];
                return createCustomSorter(format)(valueA, valueB, direction);
            }
        },
        filterable: {
            filter: (value, row) => !value?.length || value.includes(row[`group${idx}`] ?? null),
            slotName: `group-filter-${idx}`,
            multiple: true
        }
    }));

    const baseColumn = {
      title: eventName,
      dataIndex: 'userNum',
      slotName: 'userNum',
      minWidth: 240,
      sortable: { sortDirections: ['ascend', 'descend'] }
    };

    // 生成日期列表
    const length = isRetention ? timeSpan.number + 1 : timeSpan.number;
    dateList.value = Array.from({ length }, (_, index) => {
      if (isRetention) {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
      }
      return `${index + 1}${unitName}`;
    });

    // 生成日期列
    const dateColumns = dateList.value.map((item, index) => ({
      title: item,
      dataIndex: `time${index + 1}`,
      rateIndex: `rate${index}`,
      slotName: `time${index + 1}`,
      titleSlotName: `timeHeader${index + 1}`,
      minWidth: 140,
      sortable: { sortDirections: ['ascend', 'descend'] }
    }));

    // 设置列配置
    columns.value = [timeColumn, ...newGroupColumns, baseColumn, ...dateColumns];
    
    // 处理分组数据
    const processedData = processData(isRetention ? 'retention' : 'churn');
    const groupData = [];
    
    processedData.forEach(item => {
      if (item.groupData?.length) {
        item.groupData.forEach((numData, idx) => {
          const rateData = item.groupRateData[idx];
          groupData.push({
            date: item.date,
            userNum: numData.values?.[0],
            ...Object.fromEntries(groupsDescList.map((_, i) => [`group${i}`, numData.group[i]])),
            ...(numData.values?.length ? Object.fromEntries(numData.values.map((val, i) => [`time${i + 1}`, val])) : {}),
            ...(rateData.values?.length ? Object.fromEntries(rateData.values.map((val, i) => [`rate${i}`, Number((val * 100).toFixed(2))])) : {})
          });
        });
      }
    });
    
    tableData.value = groupData;
    // 按分组项拼接+日期排序，阶段值排在对应日期上面
    tableData.value = groupData.sort((a, b) => {
      // 拼接分组项
      const groupStrA = groupsDescList.map((_, i) => a[`group${i}`] || '').join(',');
      const groupStrB = groupsDescList.map((_, i) => b[`group${i}`] || '').join(',');
      
      // 先按分组项排序
      const groupCompare = groupStrA.localeCompare(groupStrB);
      if (groupCompare !== 0) return groupCompare;
      
      // 分组相同时，按日期排序，但阶段值优先
      const dateA = a.date;
      const dateB = b.date;
      
      // 如果其中一个是阶段值，阶段值排在前面
      if (dateA === '阶段值' && dateB !== '阶段值') return -1;
      if (dateA !== '阶段值' && dateB === '阶段值') return 1;
      if (dateA === '阶段值' && dateB === '阶段值') return 0;
      
      // 都不是阶段值时，正常日期排序
      return dateA.localeCompare(dateB);
    });
  } else {
    tableData.value = processData(isRetention ? 'retention' : 'churn');
    
    const length = isRetention ? timeSpan.number + 1 : timeSpan.number;
    dateList.value = Array.from({ length }, (_, index) => {
      if (isRetention) {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
      }
      return `${index + 1}${unitName}`;
    });

    columns.value = [
      ...getBaseColumns(eventName),
      ...getDateColumns(dateList.value, isRetention)
    ];
  }
  
  copyColumns.value = cloneDeep(columns.value);
  copyTableData.value = cloneDeep(tableData.value);
};

freshData()

// 导出
const exportXlsx = (date: DateRange, name?: string) => {
// 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `留存分析${name ? `_${name}` : ''}_${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}.xlsx`);
};
const createVisible = ref(false)
const form = ref({
  name: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  createVisible.value = false;
}
// 保存报表
const saveReport = async () => {
}

// 获取筛选选项
const getFilterOptions = (columnIndex: number) => {
  if (!tableData.value?.length) return [];
  return Array.from(new Set(tableData.value.map(row => row[`group${columnIndex}`])))
    .filter(value => value !== undefined)
    .map(value => ({
      label: value === null ? '(空)' : value,
      value: value === null ? null : value
    }));
};

// 监听视图宽度变化刷新table，否则横向滚动条会有拖动问题
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
const tableKey = ref(0)
watch(splitSize, (newSize, oldSize) => {
  if (newSize) {
    tableKey.value += 1
  }
})
// 千分位
const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
defineExpose({
  freshData,
  exportXlsx
})
</script>

<template>
  <div class="chart-content" style="width: 100%;height: 100%;margin-bottom: 24px;">
    <a-spin  style="width: 100%;height: 100%">
      <a-table 
        :key="tableKey" 
        :columns="columns" 
        :data="tableData" 
        :pagination="tableData.length > 50" 
        size="small" 
        column-resizable 
        :bordered="{cell:true}" 
        :scroll="{y:'100%'}" 
        :filter-icon-align-left="true">
        <template #timeHeader0>
            {{ tipHeaderName }}
            <a-tooltip :content="`【${tipHeaderName}】留存指触发初始事件的【${tipHeaderName}】，同时也触发回访事件（不区分两者的先后顺序）`" position="top">
                <icon-info-circle/>
            </a-tooltip>
        </template>
        <!-- 添加筛选器插槽 -->
        <template v-for="(_, idx) in props.eventData?.groupsDesc" :key="idx" #[`group-filter-${idx}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
            <div class="custom-filter">
            <a-space direction="vertical" :size="8">
                <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions(idx)"
                :style="{ width: '200px' }"
                :trigger-props="{
                    position: 'top',
                    autoFitPopupMinWidth: true,
                    updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
                </a-select>
                <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
                </div>
            </a-space>
            </div>
        </template>
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record,rowIndex }">
            <div class="cell-class" :style="props.eventData?.groupsDesc?.length ? (index > props.eventData.groupsDesc.length ? `background: ${getBackgroundColor(record[column.rateIndex])}` : '') : (index >1 ?`background: ${getBackgroundColor(record[column.rateIndex])}` : '')">
            <!-- 时间列 -->
            <div v-if="index === 0">
                <span>{{record[column.dataIndex]}}</span>
                <a-tooltip v-if="rowIndex === 0 && !props.eventData?.groupsDesc?.length" position="top">
                    <template #content>
                        <div v-if="props.retentionType === 'retention'">
                        {{ columns[props.eventData?.groupsDesc?.length ? props.eventData.groupsDesc.length + 1 : 1]?.title }}：阶段总和<br>
                        留存人数： 阶段总和<br>
                        留存率： 加权平均
                        </div>
                        <div v-else>
                        {{ columns[props.eventData?.groupsDesc?.length ? props.eventData.groupsDesc.length + 1 : 1]?.title }}阶段总和:<br>
                        流失人数： 阶段总和<br>
                        流失率： 加权平均
                        </div>
                    </template>
                    <icon-info-circle style="margin-left: 8px;"/>
                </a-tooltip>
            </div>
            
            <!-- 分组列 -->
            <div v-if="props.eventData?.groupsDesc?.length && index > 0 && index <= props.eventData.groupsDesc.length">
                <span>{{record[column.dataIndex]}}</span>
            </div>
            
            <!-- 数据列 -->
            <div v-if="props.eventData?.groupsDesc?.length ? (index > props.eventData.groupsDesc.length) : (index > 0)" class="hover-box">
                <!-- 第一行数据 -->
                <span v-if="rowIndex === 0 && (props.eventData?.groupsDesc?.length ? index === props.eventData.groupsDesc.length + 1 : index === 1)">
                    {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}
                </span>
                <a-tooltip
                    v-if="rowIndex === 0 &&
                    (props.eventData?.groupsDesc?.length ? index > props.eventData.groupsDesc.length + 1 : index > 1)"
                    :content="`${props.eventData?.groupsDesc?.length ? `${record.date}进行${props.eventData?.events?.[0]?.eventDisplayName}的 ${record['userNum'] || '-'}个用户中，有${record[column.dataIndex] || '-'}个在${column.title}进行了${props.eventData?.events?.[1]?.eventDisplayName}，占比${record[column.rateIndex] || '-'}%` : `在${tableData[1]?.date}到${tableData[tableData.length-1]?.date}期间，进行了${props.eventData?.events?.[0]?.eventDisplayName}的用户中，有${record[column.rateIndex] || '-'}%在${column.title}进行了${props.eventData?.events?.[1]?.eventDisplayName}`}`"
                    position="top">
                    <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
                </a-tooltip>
                
                <!-- 非第一行数据 -->
                <a-tooltip v-if="rowIndex > 0 && (props.eventData?.groupsDesc?.length ? index === props.eventData.groupsDesc.length + 1 : index === 1)" :content="`${record.date}有${record[column.dataIndex] || '-'}个用户参与了${props.eventData?.events?.[0]?.eventDisplayName}`" position="top">
                    <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
                </a-tooltip>
                <a-tooltip v-if="rowIndex > 0 && (props.eventData?.groupsDesc?.length ? index > props.eventData.groupsDesc.length + 1 : index > 1)" :content="`${record.date}进行${props.eventData?.events?.[0]?.eventDisplayName}的 ${record['userNum'] || '-'}个用户中，有${record[column.dataIndex] || '-'}个在${column.title}进行了${props.eventData?.events?.[1]?.eventDisplayName}，占比${record[column.rateIndex] || '-'}%`" position="top">
                    <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
                </a-tooltip>
                <svg v-if="rowIndex > 0 && props.retentionType === 'retention' && record[column.dataIndex]" class="add-group" @click="() => createVisible = true" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7 4.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-4 0a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z"></path>
                    <path d="M1 10a2 2 0 012-2h3a2 2 0 012 2v3H7v-3a1 1 0 00-1-1H3a1 1 0 00-1 1v3H1v-3z"></path>
                    <path d="M13 10h-1V8h-2V7h2V5h1v2h2v1h-2v2z"></path>
                </svg>
            </div>
            <div v-if="props.eventData?.groupsDesc?.length ? (index > props.eventData.groupsDesc.length + 1) : (index > 1)">
                {{ record[column.rateIndex] !== undefined && record[column.rateIndex] !== null ? `${record[column.rateIndex]}%` : '-' }}</div>
            </div>
        </template>
      </a-table>
    </a-spin>
    <!-- 创建结果分群弹窗 -->
    <a-modal v-model:visible="createVisible" :on-before-ok="validateForm" width="480px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">
          创建结果分群
          <a-tooltip content="选择计算结果保存为分群，可用于进一步下钻分析。" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <div class="form-label">分群显示名</div>
        <a-form-item field="name" :hide-label="true" :hide-asterisk="true" required :rules="[{ required: true, message: '分群显示名不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <div class="form-label">分群备注(选填)</div>
        <a-form-item field="description" :hide-label="true">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:3, maxRows:10}" :show-word-limit="true" :max-length="200"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="boe-foot">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" @click="saveReport">创建</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
.btnQ{
  color: #333;
  background-color: transparent;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
  cursor: pointer;
}
.btnQ:hover{
  background-color: #f1f2f5;
}
.btnB{
  cursor: pointer;
  color: #fff;
  background-color: #1e76f0;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  border-radius: 4px;
}
.btnB:hover{
  background-color: #3583ef;
  background: rgb(192, 215, 247);
}

:deep(tbody .arco-table-cell) {
  padding: 0;
}

.cell-class {
  padding: 5px 16px;
  height: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.hover-box{
  display: flex;
  align-items: center;
  &:hover{
    .add-group{
      opacity: 1;
    }
  }
  .add-group{
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;
    &:hover{
      color: #8d0088;
    }
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
.form-label{
  line-height: 32px;
  font-weight: 500;
}
.record-icon{
  margin-right: 5px;
  cursor: pointer;
  &:hover{
    color: var(--tant-primary-color-primary-hover);
  }
}
</style>
