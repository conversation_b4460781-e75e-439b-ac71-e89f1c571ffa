<template>
    <div class="content">
      <div v-if="eventListData.length" class="indicator-list-box">
        <div v-for="(element,index) in eventListData" :key="index" class="indicator-item-box">
          <div class="item-content">
            <!-- <i class="left-index">{{ index + 1 }}</i> -->
            <div class="right-content">
              <a-tooltip content='点击编辑自定义指标' position="tl">
                <custom-indicator-display v-if="!element.isBasic && element.eventList?.length" :allow-hover="true" :indicator="element" @click="editCustomIndicator(index)"/>
              </a-tooltip>
              <div v-if="element.isBasic && element.eventList?.length" class="event-element">
                <EventIndicatorSelect :ref="el => evtIndRefs[index] = el" :panel-data="element.eventList[0]" @analysis-index-change="basicIndicatorEventChange(index,0,$event)"/>
                <span v-if="element.eventList[0].type == 'event'" class="row-word">的</span>
                <sub-select v-if="element.eventList[0].type == 'event'" :panel-data="element.eventList[0]" @sub-change="statisticsChange(index,0,$event)"/>
                <a-tooltip content="切换自定义公式" position="top">
                  <a-button class="custom-edit-btn btn-26" style="margin-left: 8px;" @click="editCustomIndicator(index)">
                    <template #icon>
                        <icon-formula/>
                    </template>
                    </a-button>
                </a-tooltip>
                <div class="element-action">
                  <a-space align="center">
                    <a-tooltip content="添加筛选" position="top">
                      <a-button class="btn-bg btn-26" @click="addQueryFilter(index)">
                        <template #icon>
                          <icon-filter/>
                        </template>
                      </a-button>
                    </a-tooltip>
                    <a-tooltip content="删除" position="top">
                      <a-button v-if="eventListData.length>1" class="btn-bg-delete btn-26" @click="deleteItem(index)">
                          <template #icon>
                              <icon-close-circle/>
                          </template>
                      </a-button>
                    </a-tooltip>
                  </a-space>
                </div>
              </div>
            </div>
          </div>
          <eventQueryFilter
            :ref="el => queryFilterRefs[index] = el"
            :filter="element.filter"
            :show-detail-filter="true"
            :code-list="element.eventList"
            @query-filters-change="queryFiltersChange(index, $event)"/>
        </div>
      </div>
      <edit-custom-indicator-modal
          v-if="editCustomIndicatorVisible"
          v-model:visible="editCustomIndicatorVisible"
          :indicator="eventListData[operateIndicatorIndex]"
          @confirm="editCustomIndicatorConfirm"/>
    </div>
  </template>
  
  <script setup lang="ts">
  import {ref, watch} from "vue";
  import {cloneDeep, omit} from "lodash";
  import SubSelect from "@/views/analyse/components/SubSelect.vue"
  import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
  import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
  import {getNumberDisplayConfig, NumberDisplayType} from "@/api/enum";
  import {toolStore} from '@/store';
  import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
  import EditCustomIndicatorModal from "./EditCustomIndicatorModal.vue";


  const toolData = toolStore();
  const props = defineProps({
    analysisIndexData: {
      type: Array,
      default: () => []
    },
  })
  
  const emits = defineEmits(['indicatorsChange'])
  
  // 指标事件列表数据
  const eventListData = ref<any>([])
  // 当前操作指标
  const operateIndicatorIndex = ref<number>(0);
  
  // 自定义指标编辑显示
  const editCustomIndicatorVisible = ref<boolean>(false)
  
  // 指标统计展示名称
  const statisticsDisplayName = ref('')
  // 指标筛选条件引用
  const queryFilterRefs = ref<any>([])
  
  
  watch(() => props.analysisIndexData, () => {
    eventListData.value = cloneDeep(props.analysisIndexData)
  }, {immediate: true, deep: true})
  
  /**
   * 指标变化事件通知
   */
  watch(eventListData, (newValue, oldValue) => {
    const indicator = newValue.map(item => {
      return {
        displayName: item.displayName,
        displayType: item.displayType,
        name: item.name,
        type: item.type,
        isBasic: item.isBasic,
        formula: item.formula,
        eventList: item.eventList,
        filter: item.filter
      }
    })
    emits('indicatorsChange', indicator)
  }, {immediate: true, deep: true})
  
  const evtIndRefs = ref<any>([])
  // 添加事件指标
  const addItem = () => {
    const [firstEventValue] = toolData.toolModelList.flatMap(category => category.items || [])
    const eventItem = {
      eventName: firstEventValue.name,
      eventCode: firstEventValue.code,
      eventDisplayName: firstEventValue.displayName,
      type: firstEventValue.objectType,
      eventType: firstEventValue.type,
      eventAttrCode: '',
      eventAttrName: firstEventValue.objectType === 'event' ? '总次数' : '',
      filter: {}
    };
    eventListData.value.push({
      name: firstEventValue.name,
      type: 'event',
      isBasic: true,
      displayType: getNumberDisplayConfig(NumberDisplayType.TwoDecimal),
      displayName: firstEventValue.objectType === 'event'
          ? `${firstEventValue.name}-${firstEventValue.displayName}.总次数`
          : `${firstEventValue.name}-${firstEventValue.displayName}`,
      eventList: [eventItem],
      filter: {}
    });
  }
  const deleteItem = (index: number) => {
    eventListData.value.splice(index, 1)
 }
  /**
   * 添加指标筛选条件
   */
  const addQueryFilter = (index: number) => {
    queryFilterRefs.value[index].add()
  }
  /**
 * 指标筛选条件变化
 */
const queryFiltersChange = (index: number, v) => {
  eventListData.value[index].filter = v;
}
  /**
   * 指标统计变化
   */
  const statisticsChange = (index: number, eventIndex: number, e: any) => {
    const {eventAttrName, eventAttrCode, eventAttrDisplayName, statisticalName, statisticalType, subjectName, subjectCode, objectType} = e
    const filter = {...eventListData.value[index].eventList[eventIndex]};
    filter.eventAttrName = eventAttrName;
    filter.eventAttrCode = eventAttrCode;
    filter.eventAttrDisplayName = eventAttrDisplayName
    if (statisticalName) {
      filter.statisticalName = statisticalName;
    } else {
      delete filter?.statisticalName;
    }
    if (statisticalType) {
      filter.statisticalType = statisticalType;
    } else {
      delete filter?.statisticalType;
    }
    filter.objectType = objectType;
    if (subjectName) {
      filter.subjectName = subjectName;
    } else {
      delete filter?.subjectName;
    }
    if (subjectCode) {
      filter.subjectCode = subjectCode;
    } else {
      delete filter?.subjectCode;
    }
    eventListData.value[index].eventList[eventIndex] = filter;
    if (filter.eventAttrName === '触发用户数' && subjectName) {
      statisticsDisplayName.value = `触发${subjectName}数`
    } else if (filter.eventAttrName === '人均次数' && subjectName) {
      statisticsDisplayName.value = `${subjectName}均次数`
    } else {
      statisticsDisplayName.value = filter.eventAttrDisplayName
    }
    eventListData.value[index].displayName = `${filter.eventName}-${filter.eventDisplayName}${filter.type === 'event' ? (statisticsDisplayName.value ? `.${statisticsDisplayName.value}` : '') + (statisticalName ? `.${statisticalName}` : '') : ''}`
  }
  
  /**
   * 基础指标变化
   */
  const basicIndicatorEventChange = (index: number, eventIndex: number, e: any) => {
    const filter = {
      ...eventListData.value[index].eventList[eventIndex],
      ...e
    }
    const fieldsToRemove = e.type === 'indicator'
    ? ['eventCode', 'eventName', 'eventDisplayName']
    : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];
  
    const cleanedFilter = omit(filter, fieldsToRemove);
    eventListData.value[index].eventList[eventIndex] = cleanedFilter;
    eventListData.value[index].name = e.eventName || e.indicatorName
    eventListData.value[index].displayName = `${e.eventName || e.indicatorName}-${e.eventDisplayName || e.indicatorDisplayName}${filter.type === 'event' ? (filter.eventAttrName ? `.${filter.eventAttrName}` : '') + (filter.statisticalName ? `.${filter.statisticalName}` : '') : ''}`
  }
  /**
   * 编辑自定义指标
   */
  const editCustomIndicator = (index: number) => {
    operateIndicatorIndex.value = index
    editCustomIndicatorVisible.value = true
  }
  
  /**
   * 自定义指标编辑确认
   */
  const editCustomIndicatorConfirm = (customIndicator) => {
    const {eventList, formula, displayType} = customIndicator;
    eventListData.value[operateIndicatorIndex.value].isBasic = formula === 'A'
    eventListData.value[operateIndicatorIndex.value].eventList = eventList
    eventListData.value[operateIndicatorIndex.value].formula = formula
    eventListData.value[operateIndicatorIndex.value].displayType = displayType
  }
  defineExpose({
    addItem
  })
  </script>
  
  <style scoped lang="less">
  @import '@/views/analyse/css/analyse.less';
  .content {
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
  
    .header-sticky {
      position: sticky;
      top: 0;
      z-index: 999;
      width: 100% !important;
      margin: 0 !important;
      padding: 12px;
      background-color: var(--tant-bg-white-color-bg1-1);
  
      .title {
        width: 100%;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        padding: 8px 12px;
        font-size: 16px;
        line-height: 18px;
        vertical-align: top;
        background-color: var(--tant-text-white-color-text2-1);
        border-radius: 4px;
  
        &:hover {
          background-color: var(--tant-secondary-color-secondary-fill-hover);
        }
  
        img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
  
        .text {
          flex-grow: 1;
          font-weight: 600;
          max-width: calc(100% - 48px);
        }
  
        .model-btn {
          margin-left: auto;
  
          button {
            width: 24px;
            height: 24px;
            background-color: transparent;
          }
        }
      }
    }
  
    .indicator-list-box {
      box-sizing: border-box;
      min-height: 32px;
      transition: all .3s;
  
      .indicator-item-box {
        position: relative;
        height: auto;
        width: 100%;
        min-height: 24px;
        line-height: 24px;
        padding-right: 24px;
        padding-left: 24px;
  
        .item-content {
          align-items: flex-start;
          height: auto;
          display: flex;
          width: 100%;
  
          .left-index {
            margin-top: 5px;
            margin-right: 12px;
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            color: var(--tant-bg-white-color-bg1-1);
            font-size: 12px;
            font-style: normal;
            line-height: 24px;
            text-align: center;
            background-color: var(--tant-secondary-color-secondary-default);
            border-radius: 4px;
            transition: all .3s;
          }
  
          .right-content {
            flex-grow: 1;
            padding: 4px 0;
            width: calc(100% - 36px);
  
            .indicator-attribute {
              height: 24px;
              max-width: calc(100% - 40px);
              background: inherit;
              margin-bottom: 6px;
              font-size: 14px;
              display: flex;
              align-items: center;
  
              .rename {
                position: relative;
                height: 100%;
                max-width: calc(100% - 120px);
                margin-right: 12px;
                display: flex;
                align-items: center;
                overflow: hidden;
  
                .name-span {
                  // 仅用于占位
                  flex: 0 1 auto; /* 不会强制扩展，占据内容宽度，但最大不能挤压右侧 */
                  height: 100%;
                  font-size: 14px;
                  min-width: 40px;
                  color: red;
                  font-weight: 900;
                  opacity: 0;
                }
  
                .name-input {
                  position: absolute;
                  flex: 0 1 auto; /* 不会强制扩展，占据内容宽度，但最大不能挤压右侧 */
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  padding: 0;
                  opacity: 1;
                }
              }
  
              :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
                font-weight: 600 !important;
              }
  
              :deep(.arco-input-wrapper) {
                border: none;
                background-color: transparent;
                font-weight: 600;
  
                &:hover {
                  border: none;
                  background-color: transparent;
                  color: var(--tant-primary-color-primary-default);
                }
              }
  
              .filter-btn {
                flex-shrink: 0;
                min-width: 40px;
                padding: 0 8px;
                font-size: 14px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
  
                .filter-label {
                  display: inline-block;
                  max-width: 300px;
                  overflow: hidden;
                  color: var(--tant-text-gray-color-text1-2);
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  vertical-align: top;
                }
  
                &:hover {
                  border-color: var(--tant-primary-color-primary-hover);
                }
              }
            }
  
            .event-element {
              display: flex;
              align-items: center;
            }
          }
        }
  
        .action-right {
          position: absolute;
          top: 0;
          right: 4px;
          min-width: 40px;
          height: 36px;
          padding-top: 0 !important;
          display: flex;
          align-items: center;
          opacity: 0;
          transition: opacity .3s;
        }
        .element-action{
          min-width: 40px;
          height: 36px;
          padding-top: 0 !important;
          display: flex;
          align-items: center;
          opacity: 0;
          transition: opacity .3s;
          margin-left: 8px;
        }
  
        .row-word {
          display: inline-block;
          margin: 0 4px;
          color: var(--tant-text-gray-color-text1-2);
          vertical-align: top;
        }
  
        &:hover {
          background-color: var(--tant-fill-color-fill1-2);
  
          .action-left .row-content :deep(.filter-btn) {
            background: #fff;
          }
  
          .action-left .row-content :deep(.filter-icon) {
            background: #fff;
          }
  
          .sub-action-left :deep(.filter-btn) {
            background: #fff;
          }
          .action-right {
            opacity: 1;
          }
          .element-action{
            opacity: 1;
          }
        }
      }
  
      .row-foot {
        margin: 0;
        padding-left: 34px;
        transition: all .3s;
  
        .ta-filter-button {
          padding: 6px;
          display: inline-flex;
          align-items: center;
          cursor: pointer;
          font-size: 14px;
          transition: all .3s;
  
          .action {
            border-radius: 4px;
            background-color: var(--tant-primary-color-primary-fill);
            color: var(--tant-primary-color-primary-default);
            margin-right: 8px;
            padding: 3px;
            font-size: 18px;
          }
  
          .label {
            color: var(--tant-primary-color-primary-default);
          }
  
          &:hover .action {
            background-color: var(--tant-primary-color-primary-fill-hover);
            color: var(--tant-primary-color-primary-hover);
          }
        }
      }
    }
  }
  
  .btn-bg {
    background-color: transparent;
  
    &:hover {
      background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
  }
  
  .btn-26 {
    width: 26px;
    height: 26px;
    border-radius: 4px;
  }
  .btn-bg-delete {
    background-color: transparent;

    &:hover {
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
    }
  .custom-edit-btn {
    background-color: transparent;
  
    &:hover {
      background-color: transparent;
      color: var(--tant-primary-color-primary-hover);
    }
  }
  
  :deep(.arco-textarea) {
    border: none;
  }
  </style>