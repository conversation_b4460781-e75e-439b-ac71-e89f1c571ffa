<template>
  <div class="card" :style="{backgroundColor: backgroundColor}">
    <div class="drag-allow-from card-title" :style="{backgroundColor: backgroundColor}">
      <div class="operation" :style="isHovered ? 'opacity: 1;' : ''">
        <a-tooltip content="复制便签" position="top">
          <div class="operation-icon" @click="copyNote">
            <icon-copy class="icon"/>
          </div>
        </a-tooltip>
        <a-trigger v-model:popup-visible="showColorPicker" trigger="click" position="bottom" :unmount-on-close="false" :popup-hover-stay="true">
          <a-tooltip content="背景色" position="top">
            <div class="operation-icon">
              <icon-bg-colors class="icon"/>
            </div>
          </a-tooltip>
          <template #content>
            <div @mouseenter="isHovered = true" @mouseleave="isHovered = false">
              <a-color-picker v-model="ColorPickerValue" hide-trigger size="mini" show-preset :default-value="backgroundColor" @change="resetBackground"/>
            </div>
          </template>
        </a-trigger>

        <a-dropdown trigger="click" :popup-max-height="false">
          <a-tooltip content="更多" position="top">
            <div class="operation-icon">
              <icon-more class="icon"/>
            </div>
          </a-tooltip>
          <template #content>
            <div @mouseenter="isHovered = true" @mouseleave="isHovered = false">
              <a-doption style="width: 80px;" @click="resize('small')">小图
                <icon-check v-if="size == 'small'"/>
              </a-doption>
              <a-doption @click="resize('middle')">中图
                <icon-check v-if="size == 'middle'"/>
              </a-doption>
              <a-doption @click="resize('large')">大图
                <icon-check v-if="size == 'large'"/>
              </a-doption>
              <a-divider :margin="1"/>
              <a-doption @click="deleteNote">删除</a-doption>
            </div>
          </template>
        </a-dropdown>
      </div>
    </div>
    <div class="no-drag card-data" :style="{backgroundColor: backgroundColor}">
      <a-spin dot :loading="loading" :size="10" style="width: 100%;height: 100%">
        <div style="width: 100%;height: 100%">
          <note-small-card-content :note-analysis-data="noteDetail" :text-color="textColor" @change-note="changeNote"/>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import NoteSmallCardContent from '@/views/dashboard/components/report-card/note/note-small-card-content.vue';
import {computed, onMounted, ref} from 'vue';
import {DashboardEventBus, RefreshEvent} from "@/types/event-bus";
import {delNote, detailNote, saveNote} from "@/api/dashboard/api";
import {ReportQueryResponse} from "@/api/type";
import {Message} from "@arco-design/web-vue";
import {useEventBus} from "@vueuse/core";

interface Props {
  /**
   * 看板id
   */
  dashboardId: string;

  /**
   * 报表信息
   */
  report: any;
}

defineOptions({
  inheritAttrs: false,
});
const dashboardEventBus = useEventBus<string>(DashboardEventBus)
const props = defineProps<Props>();
const reportAnalysisData = ref<ReportQueryResponse>();
const noteDetail = ref();
const ColorPickerValue = ref<string>();
const showColorPicker = ref<boolean>(false);
const isHovered = ref(false)

const emit = defineEmits(['resize', 'addNote']);
const loading = ref<boolean>(true);
const size = computed(() => {
  if (props.report.w === 5) return 'small'; // 如果宽度为5，返回'small'
  if (props.report.w === 10) return 'middle'; // 如果宽度为10，返回'middle'
  if (props.report.w === 20) return 'large'; // 如果宽度为20，返回'large'
  return 'resizable'; // 其他情况
});


const backgroundColor = ref<string>('#fff');

const textColor = computed(() => {
  const color = backgroundColor.value;
  // 如果color未定义，直接返回黑色
  if (!color) {
    return '#000';
  }

  // 判断颜色是否是浅色 (包括白色和一些更亮的颜色)
  const isLight = color.startsWith('#fff') || color.startsWith('rgb(255, 255, 255)') ||
      (color.startsWith('#') && parseInt(color.slice(1), 16) > 0xffffff / 1.5);

  return isLight ? '#000' : '#fff';
});

const freshData = () => {
  detailNote(props.report.objectCode).then((resp) => {
    noteDetail.value = resp[0]
    backgroundColor.value = noteDetail.value?.type || '#fff';
    loading.value = false
  })
};

const resize = (sizeType: string) => {
  emit('resize', sizeType, props.report?.objectCode);
  freshData()
};

const changeNote = async (value: string) => {
  // emit('changeParams', value, props.report?.objectCode)
  const newNote = {
    noteId: noteDetail.value?.code,
    content: value,
    title: '',
    type: backgroundColor.value,
    dashboardId: props.dashboardId,
  }

  await saveNote(newNote).then(res => {
    Message.success('编辑成功')
  }).catch(e => {
    console.error(e)
  })
  freshData()
}

const resetBackground = async (value: string) => {
  backgroundColor.value = value;
  const newNote = {
    noteId: noteDetail.value?.code,
    content: noteDetail.value?.content,
    title: '',
    type: value,
    dashboardId: props.dashboardId,
  }

  await saveNote(newNote).then(res => {
  }).catch(e => {
    console.error(e)
  })
  freshData()
}

const copyNote = () => {
  const newNote = {
    content: noteDetail.value?.content,
    title: '',
    type: backgroundColor.value,
    dashboardId: props.dashboardId,
  }
  setTimeout(() => {
    saveNote(newNote).then(res => {
      emit('addNote', res.id, size.value)
      Message.success('复制成功')
    }).catch(e => {
      console.error(e)
    })

  }, 500)

}

const deleteNote = async () => {
  await delNote(props.dashboardId, noteDetail.value?.code).then(res => {
    Message.success('删除成功')
  }).catch(e => {
    console.error(e)
  })
  // freshData()
  dashboardEventBus.emit(RefreshEvent)
}

defineExpose({
  freshData
})

onMounted(() => {

  freshData();
})

</script>

<style scoped lang="less">
.card {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  transition: all 0.3s;
  border-radius: 4px;
  // position: relative;
}

.card:hover {
  box-shadow: var(--tant-medium-shadow-medium-overall);
  border: 1px solid var(--tant-primary-color-primary-default);
  border-radius: 4px;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  // height: 46px;
  // margin-bottom: 0;
  // padding: 16px 24px 8px;
  line-height: 20px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom: none;
  cursor: grab;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  position: relative;

  .operation {
    display: flex;
    align-items: center;
    transition: opacity 0.3s;
    height: 32px !important;
    opacity: 0;
    transition: opacity .3s;
    position: absolute;
    z-index: 1000;
    top: 0;
    right: 0;

  }

  .operation-icon {
    padding: 4px;
    display: inline-block;
    margin-left: 8px;
    cursor: pointer;
    color: var(--tant-text-color-text-secondary);
    transition: color 0.3s;
    border-radius: 4px;

    &:hover {
      color: var(--tant-primary-color-primary-default);
      //background-color: #fcfcfd;
    }

    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }
}

.card-data {
  display: flex;
  align-items: center;
  justify-content: center;
  // height: calc(100% - 46px);
  height: 100%;
  padding: 0 24px 16px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

.arco-icon {
  margin-left: 10px !important;
}

.card:hover .card-title .operation {
  opacity: 1;
  transition: opacity .3s;
  // unicode-bidi: isolate;
}
</style>
