<template>
  <div class="selected-content">
    <a-select
      v-model:model-value="appId"
      :loading="selectLoading"
      :style="{ width: props.width }"
      :show-extra-options="false"
      :filter-option="false"
      allow-search
      @search="handleSearch"
      @dropdown-reach-bottom="handleReachBottom"
      @focusin="selectFocusin"
      @change="handleChange"
      @focusout="selectFocusout"
    >
      <template #label="{ data }">
        <div class="game-select">
          <a-avatar shape="square" :size="20">
            <img :src="appData?.icon || '/image/game-default.svg'" alt="" />
          </a-avatar>
          <span class="game-name">{{ data?.label }}</span>
        </div>
      </template>
      <div v-show="!gameAppList?.some((item) => item.code === appId)" class="select-content">
        <a-option v-if="appData?.code" :key="appData.code" :value="appData.code">
          <div class="select-item">
            <a-avatar shape="square" :size="20">
              <img :src="appData?.icon || '/image/game-default.svg'" alt="" />
            </a-avatar>
            <span class="game-name">{{ appData?.code }}-{{ appData?.name }}</span>
          </div>
        </a-option>
      </div>
      <a-option v-for="(item, index) in gameAppList" :key="item.code" :value="item.code">
        <div class="select-item">
          <a-avatar shape="square" :size="20">
            <img :src="item?.icon || '/image/game-default.svg'" alt="" />
          </a-avatar>
          <span class="game-name">{{ item.code }}-{{ item.name }}</span>
        </div>
      </a-option>
      <div v-if="bottomLoading" style="width: 100%; display: flex; justify-content: center; align-items: center; margin-bottom: 10px">
        <a-spin :size="8" style="margin-top: 4px" dot />
      </div>
    </a-select>
  </div>
</template>

<script lang="ts" setup>
import {reactive, ref, watch} from 'vue';
import {getAppPageList} from '@/api/marketing/api';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {LocalStorageEventBus} from '@/types/event-bus';
import _ from 'lodash';

const props = defineProps({
    width: {
      type: String,
      default: '320px',
    },
  });

  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const appId = useSessionStorage('app-id', '');
  const appData = useSessionStorage('app-data', {});
  const focused = ref<boolean>(false);
  const pageParams = reactive({
    current: 1,
    pageSize: 10,
    text: '',
    appStatus: undefined,
    osName: undefined,
  });
  const bottomLoading = ref(false);
  const selectLoading = ref(false);
  const gameAppList = ref<any>([]);
  const getList = async (loading) => {
    loading.value = true;
    try {
      const res = await getAppPageList(pageParams);
      gameAppList.value = res?.items || [];
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = (v) => {
    pageParams.text = v;
    getList(selectLoading);
  };

  const handleReachBottom = () => {
    if (focused.value && pageParams.pageSize <= gameAppList.value.length) {
      pageParams.pageSize += 10;
      getList(bottomLoading);
    }
  };

  const handleChange = (v) => {
    appId.value = v;
    const appDataValue = gameAppList.value.find((item) => item.code === v) || {};
    appData.value = appDataValue;
    localStorage.setItem('app-id', v);
    localStorage.setItem('app-data', JSON.stringify(appDataValue));
    localStorageEventBus.emit('app-id', v);
    localStorageEventBus.emit('app-data', gameAppList.value.find((item) => item.code === v) || {});
  };

  const selectFocusin = () => {
    focused.value = true;
    // gameAppList为空则初始化数据，减少初始化的请求次数
    if (_.isEmpty(gameAppList.value)) {
      getList(selectLoading);
    }
  };

  const selectFocusout = () => {
    focused.value = false;
    setTimeout(() => {
      if (!_.isEmpty(pageParams.text)) {
        // 搜索值不为空时，重置搜索
        pageParams.text = '';
        pageParams.pageSize = 10;
        getList(bottomLoading);
      } else if (gameAppList.value.length > 6) {
        // 下拉过长时，减少选项
        gameAppList.value = gameAppList.value.slice(0, 6);
        pageParams.pageSize = 6;
      }
    }, 100);
  };

  const init = async () => {
    // 在appId存在时，appData比对获取
    if (!_.isEmpty(appId.value)) {
      // 检查appIdList和appDataList是否匹配
      const isMatch = appId.value === appData.value?.code;
      // 如果不匹配，需要重新获取数据
      if (!isMatch) {
        const params = { ...pageParams };
        params.text = appId.value;
        params.pageSize = 10;
        const res = await getAppPageList(params);
        appData.value = res?.items[0] || {};
        // 更新localStorage
        localStorage.setItem('app-id', appId.value);
        localStorage.setItem('app-data', JSON.stringify(appData.value));
        localStorageEventBus.emit('app-id', appId.value);
        localStorageEventBus.emit('app-data', appData.value);
      }
    }
    // 在appIdList不存在的情况下，初始化
    else if (_.isEmpty(appId.value)) {
      // 优先从localStorage中取值
      const localAppId = localStorage.getItem('app-id');
      const localAppData = localStorage.getItem('app-data');
      if (_.isEmpty(localAppId) || _.isEmpty(localAppData)) {
        await getList(selectLoading);
        appId.value = gameAppList.value[0].code;
        appData.value = gameAppList.value[0] || {};
      } else {
        appId.value = localAppId;
        appData.value = JSON.parse(localAppData);
      }
      localStorageEventBus.emit('app-id', gameAppList.value[0]?.code);
      localStorageEventBus.emit('app-data', gameAppList.value[0] || {});
    }
    // if (appId.value !== appData.value?.code) {
    //   const res = await getAppInfo(appId.value);
    //   appData.value = {...res, code: res.appId}
    //   localStorageEventBus.emit("app-data", gameAppList.value[0] || {})
    // }
  };

  init();
  watch(
    () => appId.value,
    (newAppId, oldAppId) => {
      // 只有当新的appId不为空且与旧的不同时才重新初始化
      if (!_.isEmpty(newAppId) && !_.isEqual(newAppId, oldAppId)) {
        const isMatch = newAppId === oldAppId;
        // 如果不匹配，重新初始化
        if (!isMatch) {
          init();
        }
      }
    }
  );
</script>

<style scoped lang="less">
  .game-name {
    padding-left: 8px;
    margin-bottom: 4px;
  }

  .select-content {
    padding-bottom: 8px;
    border-bottom: dashed 1px var(--color-neutral-4);
  }
</style>
