<template>
    <div class="table-card">
        <div class="header">
            <div class="title">{{ displayTitle }}</div>
            <div class="filter">
                <!-- <div v-if="params?.pageType === 'team'" class="filter-item">
                    <a-checkbox v-model:checked="queryParams.isGroupApp" @change="onGroupAppChange">应用分组</a-checkbox>
                </div> -->
                <!-- <div class="filter-item">
                    <dateSet :time-particle="queryParams.timeParticle" :no-extend="true" @time-change="timeChange"/>
                </div> -->
                <div class="filter-item">
                    <date-picker :date-range="queryParams.date" disabled-after-today @date-pick="datePick"/>
                </div>
                <div class="filter-item" style="line-height: 26px;">
                    <selectCountryList :default-codes="queryParams.country" @countrys-change="countryChange"/>
                </div>
                <div class="filter-item">
                    <icon-download class="icon" @click="exportXlsx"/>
                </div>
            </div>
        </div>
        <a-table :loading="dataLoading" :columns="displayColumns" :data="tableData" :pagination="pagination" size="small" column-resizable :filter-icon-align-left="true"style="white-space: nowrap" @page-size-change="pageSizeChange">
            <template v-for="(column, index) in displayColumns" :key="index" #[column.slotName]="{ record }">
                <span v-if="column.dataIndex === 'rpDate'" :style="isWeekend(record[column.dataIndex]) ? 'color: red' : ''">
                    {{ record[column.dataIndex] }}
                </span>
                <span v-else-if="column.dataIndex === 'appName'">
                    {{ record[column.dataIndex] }}
                </span>
                <span v-else>
                    {{ formatNumber(record[column.dataIndex]) }}
                </span>
            </template>
            
            <!-- 应用列筛选模板 -->
            <template #appName-filter="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
                <div class="custom-filter">
                    <a-space direction="vertical" :size="8">
                        <a-select
                            :model-value="filterValue"
                            placeholder="请选择应用"
                            allow-clear
                            allow-search
                            multiple
                            :options="appFilterOptions"
                            :style="{ width: '200px' }"
                            :trigger-props="{
                                position: 'top',
                                autoFitPopupMinWidth: true,
                                updateAtScroll: true
                            }"
                            @update:model-value="(val) => setFilterValue(val)">
                        </a-select>
                        <div class="custom-filter-footer">
                            <a-button size="mini" @click="handleFilterReset">重置</a-button>
                            <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
                        </div>
                    </a-space>
                </div>
            </template>
        </a-table>
    </div>
</template>

<script setup lang="ts"> 
import { reactive, watch, ref, computed } from 'vue'
import DatePicker from "@/components/date-picker/index.vue";
import selectCountryList from "@/components/selected-country-list/index.vue"
import * as XLSX from 'xlsx';
import { getDateRangeStartDate, getDateRangeEndDate } from "@/utils/dateUtil";
import { getTeamBasicTable } from '@/api/analyse/api'
import { TimeParticleSize } from '@/api/enum';
import dateSet from '@/views/analyse/components/dateSet.vue';

const props = defineProps({
    params: Object,
    teamList: Array,
})

const queryParams = reactive({
    country: ['global'],
    isGroupApp: false, // 改为boolean类型
    date: {
        recentStartDate: 7,
        recentEndDate: 1,
        dateText: '过去7天'
    },
    timeParticle: {
        timeParticleSize: TimeParticleSize.DAY1,
        firstDayDfWeek: null
    },
})
const pagination = ref<any>({
    pageSize: 10,
    pageSizeOptions: [30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
    showPageSize: 'true',
    showTotal: 'true',
});
const pageSizeChange = (size) => {
    pagination.value.pageSize = size;
};
// 应用筛选选项
const appFilterOptions = ref<any[]>([])

const displayTitle = computed(() => {
    const teamName = props.teamList?.find(item => item.code === props.params?.team)?.name;
    if (props.params?.pageType === 'app') {
        return `团队总体数据-应用${props.params.appId}`;
    }
    return `团队总体数据-${teamName}`;
});

const dataLoading = ref(false)

// 基础列定义
const baseColumns = ref<any>([
    {
        title: '日期',
        dataIndex: 'rpDate',
        slotName: 'rpDate',
        width: 120,
        sortable: { sortDirections: ['ascend', 'descend'], defaultSortOrder: 'descend' }
    },
    {
        title: '利润目标/天',
        dataIndex: 'avgAnnualProfitTarget',
        slotName: 'avgAnnualProfitTarget',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '收入目标/天',
        dataIndex: 'avgAnnualIncomeTarget',
        slotName: 'avgAnnualIncomeTarget',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '剩余利润目标/天',
        dataIndex: 'avgAnnualCumulativeProfit',
        slotName: 'avgAnnualCumulativeProfit',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '剩余收入目标/天',
        dataIndex: 'avgAnnualCumulativeIncome',
        slotName: 'avgAnnualCumulativeIncome',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '投停预估每日',
        dataIndex: 'avgLaunchStopPredictIncome',
        slotName: 'avgLaunchStopPredictIncome',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '利润',
        dataIndex: 'indProfit',
        slotName: 'indProfit',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '分成后流水',
        dataIndex: 'indDivideRevenue',
        slotName: 'indDivideRevenue',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '总投放',
        dataIndex: 'indApSpend',
        slotName: 'indApSpend',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '广告收入',
        dataIndex: 'indMaRevenue',
        slotName: 'indMaRevenue',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '内付收入',
        dataIndex: 'indIapRevenue',
        slotName: 'indIapRevenue',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '千活总收益',
        dataIndex: 'indKArpu',
        slotName: 'indKArpu',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '活跃',
        dataIndex: 'indActUsers',
        slotName: 'indActUsers',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '新增',
        dataIndex: 'indNewUsers',
        slotName: 'indNewUsers',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: 'video广告',
        dataIndex: 'indMaVideoImpressions',
        slotName: 'indMaVideoImpressions',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: 'inter广告',
        dataIndex: 'indMaInterImpressions',
        slotName: 'indMaInterImpressions',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: 'banner广告',
        dataIndex: 'indMaBannerImpressions',
        slotName: 'indMaBannerImpressions',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
    {
        title: '总流水',
        dataIndex: 'indTotalRevenue',
        slotName: 'indTotalRevenue',
        sortable: { sortDirections: ['ascend', 'descend'] }
    },
])

// 应用列定义
const appColumn = {
    title: '应用',
    dataIndex: 'appName',
    slotName: 'appName',
    sortable: { sortDirections: ['ascend', 'descend'] },
    filterable: {
        slotName: 'appName-filter',
        multiple: true,
    },
}

// 动态计算显示的列
const displayColumns = computed(() => {
	// 基础列
	let cols = [...baseColumns.value];
	const hasDate = queryParams.timeParticle?.timeParticleSize !== TimeParticleSize.TOTAL;
	// T0 不显示日期列
	if (!hasDate) {
		cols = cols.filter(col => col.dataIndex !== 'rpDate');
	}
	// 未勾选应用分组，直接返回
	if (!queryParams.isGroupApp) return cols;
	// 勾选应用分组时：
	// - 有日期：插在日期后面
	// - 无日期（T0）：放到第一列
	if (hasDate) {
		const idx = cols.findIndex(col => col.dataIndex === 'rpDate');
		if (idx !== -1) {
			return [...cols.slice(0, idx + 1), appColumn, ...cols.slice(idx + 1)];
		}
	}
	return [appColumn, ...cols];
});

const tableData = ref<any>([])

const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

// 判断是否为周末
const isWeekend = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDay();
    return day === 0 || day === 6; // 0是周日，6是周六
}

// 应用分组变化处理
const onGroupAppChange = (checked: boolean) => {
    queryParams.isGroupApp = checked
    // 更新应用筛选选项
    updateAppFilterOptions()
    getData()
}

// 更新应用筛选选项
const updateAppFilterOptions = () => {
    if (queryParams.isGroupApp && tableData.value.length > 0) {
        // 从表格数据中提取唯一的应用名称
        const appNames = [...new Set(tableData.value.map(item => item.appName).filter(Boolean))]
        appFilterOptions.value = appNames.map(name => ({
            label: name,
            value: name
        }))
    } else {
        appFilterOptions.value = []
    }
}

const exportXlsx = () => {
    // 获取表头
    const headers = displayColumns.value.map(item => item.title);
    const columnsList = [headers];
    tableData.value.forEach((item: any) => {
        const row = headers.map(header => {
            const dataIndex = displayColumns.value.find(col => col.title === header)?.dataIndex;
            return dataIndex ? item[dataIndex] : '';
        });
        columnsList.push(row);
    });

    const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
    const newWorkbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
    XLSX.writeFile(newWorkbook, `团队总体数据_${getDateRangeStartDate(queryParams.date)}-${getDateRangeEndDate(queryParams.date)}.xlsx`);
};

const getData = async () => {
    const data = {
        dateRange: queryParams.date,
        country: queryParams.country.length ? queryParams.country : ['global'],
        // timeParticleSize: queryParams.timeParticle.timeParticleSize,
        // isGroupApp: queryParams.isGroupApp ? 1 : 0,
    };
    if (props.params?.team && props.params.pageType === 'team') {
        data.teamCode = props.params.team;
    }
    if (props.params?.appId && props.params.pageType === 'app') {
        data.appId = props.params.appId;
    }
    dataLoading.value = true;
    try {
        const res = await getTeamBasicTable(data);
        tableData.value = res;
        // 更新应用筛选选项
        updateAppFilterOptions()
    } catch (error) {
        console.error('获取团队概览数据失败:', error);
        tableData.value = [];
        appFilterOptions.value = []
    } finally {
        dataLoading.value = false;
    }
}

const timeChange = (v) => {
    queryParams.timeParticle = v
    getData()
}

const datePick = (date) => { 
    queryParams.date = date
    getData()
}

const countryChange = () => {
    getData()
}

watch(() => props.params, () => {
    getData()
}, { deep: true })
</script>

<style lang="less" scoped>
.table-card{
    width: 100%;
    // height: 520px;
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    margin-top: 16px;
}
.header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 38px;
    margin-bottom: 16px;
    .title{
        font-size: 16px;
        font-weight: bold;
        color: #475466;
    }
    .filter{
        display: flex;
        align-items: center;
        .filter-item{
            margin-left: 12px;
        }
        .icon{
            cursor: pointer;
        }
    }
}

.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
</style>
