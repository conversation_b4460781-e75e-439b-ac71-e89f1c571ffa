import {AxiosPromise} from "axios";
import {getRequest, postRequest} from "@/api/request";
import {SystemReleaseVersionDto} from "@/api/system/type";

/**
 * 最新系统版本请求接口
 */
// export function getLatestReleaseVersion(platform?: string): AxiosPromise<SystemReleaseVersionDto> {
//   return getRequest<SystemReleaseVersionDto>('/api/sys/release_version/latest', { 'platform': platform || 'GrowthX_web'});
// }
export function getLatestReleaseVersion(systemName?: string,platform?: string): AxiosPromise<SystemReleaseVersionDto> {
  return getRequest<SystemReleaseVersionDto>('/api/sys/release_version/latest', {'systemName': systemName || 'GrowthX', 'platform': platform || 'web'});
}
/**
 * 系统版本列表请求接口
 */
export function getReleaseVersionList(params: any): AxiosPromise<SystemReleaseVersionDto[]> {
  return getRequest<SystemReleaseVersionDto[]>('/api/sys/release_version/list', params);
}
/**
 * 系统版本保存接口
 */
export function saveReleaseVersion(params: any): AxiosPromise<any> {
  return postRequest<SystemReleaseVersionDto[]>('/api/sys/release_version/save', params);
}
