<template>
    <div class="cross-content">
        <a-spin :loading="loading" class="group-box">
            <a-anchor
                class="pack-configs-nav"
                :change-hash="false"
                scroll-container="#config-detail"
                @change="configSelectChange">
                <a-anchor-link v-for="(v, k) in configsGroup" :class="configIndex === k ? 'active' : ''" :key="k" :href="v.link">{{v.name}}</a-anchor-link>
                <a-anchor-link v-if="formData.packMode==='pad'" :class="configIndex === configsGroup.length ? 'active' : ''" :key="configsGroup.length" href="#rootgradle">ROOT Gradle</a-anchor-link>
            </a-anchor>
        </a-spin>
        <div class="group-detail">
            <div :style="{'text-align': 'end', 'height': '32px'}">
                <a-space>
                    <a-button :loading="configLoading" class="button" @click="refreshConfig">刷新</a-button>
                    <a-button :loading="saveConfigLoading" class="button" type="primary" @click="saveConfig">保存</a-button>
                </a-space>
            </div>
            <div id="config-detail" ref="containerRef" :style="{'height': 'calc(100% - 10px)', 'overflow-y': 'auto'}">
                <a-form ref="formRef" :model="formData" layout="vertical" :loading="configLoading || saveConfigLoading">
<!--                    <a-spin>-->
                        <section id="jsonversion">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    打包选项
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="dataVersion" :rules="[{required:true,message:'请选择下发Json版本'}]" label="下发Json版本">
                                        <a-select
                                            :options="jsonVersionOptions"
                                            v-model:model-value="formData.dataVersion"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            :loading="jsonVersionLoading"
                                            allow-search>
                                            <template #header>
                                                <div style="padding: 0 12px;" >
                                                    <div class="create-condition" @click="(ev) => drawerTabChange(ev,'default', true)">
                                                        <icon-plus style="margin-right: 12px;"/>
                                                        创建数据版本
                                                    </div>
                                                </div>
                                            </template>
                                        </a-select>
                                    </a-form-item>
                                    <a-form-item field="packType" :rules="[{required:true,message:'请选择打包类型'}]" label="打包类型">
                                        <a-select
                                            :options="packTypeOptions"
                                            v-model:model-value="formData.packType"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            allow-search/>
                                    </a-form-item>
                                    <a-form-item field="packMode" :rules="[{required:true,message:'请选择打包模式'}]" label="打包模式">
                                        <a-select
                                            :options="packModeOptions"
                                            v-model:model-value="formData.packMode"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            allow-search/>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="sdkconfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div  class="section-area">
                                    SDK配置
                                </div>
                                <a-form-item field="sdkVer" :rules="[{required:true,message:'请选择sdk版本'}]" label="sdk版本">
                                    <a-select
                                        :options="sdkOptions"
                                        :style="{width:'200px'}"
                                        v-model:model-value="formData.sdkVer"
                                        @change="changeSdk"
                                        allow-search
                                        allow-create
                                    />
                                </a-form-item>
                                <a-form-item v-if="getAdPlatform.length" field="adPlatform" label="广告平台">
                                    <a-spin :loading="adPlatformLoading">
                                        <a-space direction="vertical">
                                            <div v-for="adp in getAdPlatform">
                                                <a-space direction="vertical">
                                                    <div>
                                                        <a-switch
                                                            size="small"
                                                            :model-value="formData.adMediation.includes(adp.value)"
                                                            @change="(value) => changeAdMediation(value, adp.value)"
                                                        /> {{adp.label}}</div>
                                                    <a-checkbox
                                                        v-if="formData.adMediation.includes(adp.value)"
                                                        :model-value="getCheckGroup[adp.value] === adp.children.length"
                                                        :indeterminate="getCheckGroup[adp.value] > 0 && getCheckGroup[adp.value] < adp.children.length"
                                                        @change="(value) => changeAllMediation(value, adp.value)">全选</a-checkbox>
                                                    <a-checkbox-group v-if="formData.adMediation.includes(adp.value)" v-model="formData.adPlatform">
                                                        <a-checkbox
                                                            v-for="item in adp.children"
                                                            :value="item.value"
                                                        >{{item.label}}</a-checkbox>
                                                    </a-checkbox-group>
                                                </a-space>
                                            </div>
                                        </a-space>
                                    </a-spin>
                                </a-form-item>
                                <a-form-item v-if="getSdkDependencies.length" field="adPlatform" label="sdk依赖">
                                    <a-spin :loading="adPlatformLoading">
                                        <a-space direction="vertical">
                                            <a-checkbox
                                                :model-value="formData.sdkDependencies.length === getSdkDependencies.length"
                                                :indeterminate="formData.sdkDependencies.length > 0 && formData.sdkDependencies.length < getSdkDependencies.length"
                                                @change="changeAllSdkDependencies">全选
                                            </a-checkbox>
                                            <a-checkbox-group v-model="formData.sdkDependencies">
                                                <a-checkbox v-for="item in getSdkDependencies" :value="item">{{item}}</a-checkbox>
                                            </a-checkbox-group>
                                        </a-space>
                                    </a-spin>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="appinfo">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    应用信息
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="name" :rules="[{required:true,message:'请填写应用名称'}]" label="应用名称">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.name" placeholder="please enter main path..." />
                                    </a-form-item>
                                    <a-form-item field="versionCode" :rules="[{required:true,message:'请填写版本code'}]" label="版本code">
                                        <a-input-number
                                            hide-button
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.versionCode"
                                            :min="1" :step="1"
                                            placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="versionName" :rules="[{required:true,message:'请填写版本号'}]" label="版本号">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.versionName" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="versionNameAuto" label="版本号自增">
                                        <a-select
                                            :options="[{label: '不自增', value: 0}, {label: '自增', value: 1}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.versionNameAuto"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="icon">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    ICON管理
                                </div>
                                <a-form-item field="iconUploaded" :rules="[{required:true,message:'请上传icon'}]" label="icon">
                                    <div v-if="formData.iconUploaded" :style="{width: '100%'}">
                                        <a-spin :loading="iconLoading" :style="{width: '100%'}">
                                            <a-image-preview-group infinite>
                                                <a-space :wrap="true">
                                                    <div v-for="image in iconUrlsContent" class="icon-box">
                                                        <p><a-image width="120" :style="{border: '1px solid #eee'}" :src="image.content" /></p>
                                                        <div class="icon-del-btn">
                                                          <a-tooltip content="删除该图标">
                                                            <icon-delete @click="delIcon(image.name)" :size="16"/>
                                                          </a-tooltip>
                                                        </div>
                                                        <label :style="{'font-size': '11px'}">{{image.name}}</label>
                                                    </div>
                                                </a-space>
                                            </a-image-preview-group>
                                        </a-spin>
                                    </div>
                                    <div v-if="formData.iconUploaded" :style="{'padding-top': '15px'}">
                                        <a-tooltip content="更新ICON">
                                            <a-link @click="() => uploadPackFile('icon')">
                                                更新ICON
                                                <template #icon>
                                                    <icon-edit />
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('icon')">上传ICON</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="names">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    应用名多语言
                                </div>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item
                                        v-for="(v,k) in formData.names"
                                        :label="k === 0 ? '应用名多语言' :''">
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-select
                                                :options="langOptions"
                                                :style="{'width':'200px'}"
                                                v-model:model-value="v.id"
                                                :field-names="{value: 'id', label: 'label'}"
                                                placeholder="请选择语言"
                                                allow-create/>
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="请输入语言对应的应用名" />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.names.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.names.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="git">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    代码仓库
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="url" :rules="[{required:true,message:'请填写代码仓库地址'}]" label="仓库地址">
                                        <a-input :style="{'width':'408px'}" v-model:model-value="formData.url" placeholder="please enter url..." />
                                    </a-form-item>
                                    <a-form-item v-if="formData && formData.url && formData.url.startsWith('git@')" field="branch" :rules="[{required:true,message:'请填写代码分支'}]" label="分支名称">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.branch" placeholder="please enter branch..." />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="cpulib">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    CPU LIB
                                </div>
                                <a-form-item field="cpuLibs" :rules="[{required:true,message:'请选择cpu架构'}]" label="cpu架构">
                                    <a-space>
                                        <a-checkbox
                                            :model-value="cpuLibCheckedAll"
                                            :indeterminate="cpuLibIndeterminate"
                                            @change="checkedAllCpuLib">Check All
                                        </a-checkbox>
                                        <a-checkbox-group v-model="formData.cpuLibs" @change="changeCpuLib">
                                            <a-checkbox v-for="(v, k) in cpuLibOptions" :key="k" :value="v">{{v}}</a-checkbox>
                                        </a-checkbox-group>
                                    </a-space>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="keystore">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    签名管理
                                </div>
                                <a-form-item field="ksUploaded" :rules="[{required:true,message:'请上传或生成签名文件'}]" label="签名">
                                    <div v-if="formData.ksUploaded">
                                        <a-tooltip content="更新签名">
                                            <a-link @click="() => uploadPackFile('keystore')">
                                                <template #icon>
                                                    <icon-edit />更新
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                        <a-tooltip content="点击查看内容详情">
                                            <a-link @click="(ev) => showContentModal('keystore')">{{formData.keystoreName}}.keystore</a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('keystore')">上传或生成签名</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="googleservice">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    FIREBASE
                                </div>
                                <a-form-item field="gsUploaded" :rules="[{required:true,message:'请上传google-services.json'}]" label="google-services.json">
                                    <div v-if="formData.gsUploaded">
                                        <a-tooltip content="更新google-services.json">
                                            <a-link @click="() => uploadPackFile('google_services')">
                                                <template #icon>
                                                    <icon-edit />更新
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                        <a-tooltip content="点击查看内容详情">
                                            <a-link @click="(ev) => showContentModal('google_services')">google-services.json</a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('google_services')">上传google-services.json</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="pluginversion">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    插件版本
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="ndkVersion" :rules="[{required:true,message:'请选择ndk版本'}]" label="ndk版本">
                                        <a-select
                                            :options="ndkVersionOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.ndkVersion"
                                            placeholder="Please select ..."
                                            allow-search
                                            allow-clear />
                                    </a-form-item>
                                    <a-form-item field="gradleVersion" :rules="[{required:true,message:'请选择gradle版本'}]" label="gradle版本">
                                        <a-select
                                            :options="gradleVersionOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.gradleVersion"
                                            allow-create/>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="buildconfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div  class="section-area">
                                    注包配置
                                </div>
                                <a-space :wrap="true">
<!--                                    <a-form-item field="sdkVer" :rules="[{required:true,message:'请选择sdk版本'}]" label="sdk版本">-->
<!--                                        <a-select-->
<!--                                            :options="sdkOptions"-->
<!--                                            :style="{width:'200px'}"-->
<!--                                            v-model:model-value="formData.sdkVer"-->
<!--                                            @change="changeSdk"-->
<!--                                            allow-search-->
<!--                                            allow-create-->
<!--                                        />-->
<!--                                    </a-form-item>-->
<!--                                    <a-form-item field="adPlatform" label="广告平台">-->
<!--                                        <a-cascader-->
<!--                                            :options="getAdPlatform"-->
<!--                                            :loading="adPlatformLoading"-->
<!--                                            :style="{width:'408px'}"-->
<!--                                            v-model:model-value="formData.adPlatform"-->
<!--                                            :max-tag-count="1"-->
<!--                                            tag-nowrap-->
<!--                                            placeholder="Please select ..."-->
<!--                                            @change="changeAdPlatform"-->
<!--                                            multiple/>-->
<!--                                    </a-form-item>-->
<!--                                    <a-form-item field="adPlatform" label="sdk依赖">-->
<!--                                        <a-select-->
<!--                                            :options="getSdkDependencies"-->
<!--                                            :loading="adPlatformLoading"-->
<!--                                            :style="{width:'200px'}"-->
<!--                                            v-model:model-value="formData.sdkDependencies"-->
<!--                                            :max-tag-count="1"-->
<!--                                            tag-nowrap-->
<!--                                            placeholder="Please select ..."-->
<!--                                            multiple/>-->
<!--                                    </a-form-item>-->
                                    <a-form-item field="keystoreVerify" label="签名验证">
                                        <a-select
                                            :options="['new', 'old']"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.keystoreVerify"/>
                                    </a-form-item>
                                    <a-form-item field="keystoreVerifyLog" label="签名验证日志">
                                        <a-select
                                            :options="[{label: '关闭', value: false}, {label: '启用', value: true}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.keystoreVerifyLog"/>
                                    </a-form-item>
                                    <a-form-item field="adaptiveIcon" label="图标自适应">
                                        <a-select
                                            :options="[{label: '不处理图标自适应', value: 0}, {label: '处理图标自适应', value: 1}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.adaptiveIcon"/>
                                    </a-form-item>
                                    <a-form-item field="resguard" label="资源混淆">
                                        <a-select
                                            :options="[{label: '不混淆资源', value: '0'}, {label: '混淆资源', value: '1'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.resguard"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="encryptDefaultJson" label="default.json加密">
                                        <a-select
                                            :options="[{label: '不加密default.json', value: 0}, {label: '加密default.json', value: 1}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.encryptDefaultJson"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="encryptMode" label="加密方式">
                                        <a-select
                                            :options="['NORMAL', 'RSA']"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.encryptMode"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="sha1" label="google签名sha1">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.sha1" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="maxAdReview" label="Max广告审查">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.maxAdReview" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="exportProject" label="导出注包项目">
                                        <a-select
                                            :options="[{label: '否', value: 0}, {label: '是', value: 1}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.exportProject"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="metadata">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    META数据
                                </div>
                                <a-form-item
                                    v-for="(v,k) in formData.metaConfigs"
                                    :label="'配置项'+k">
                                    <div :style="{width: '100%', display: 'flex'}">
<!--                                        <a-input :style="{width:'400px'}" v-model:model-value="v.id" placeholder="please enter key..." />-->
                                        <a-select
                                            :options="getMetaList"
                                            v-model:model-value="v.id"
                                            :style="{width:'400px'}"
                                            placeholder="select or create key..."
                                            allow-clear
                                            allow-create
                                            allow-search
                                        />
                                        <span :style="{width: '10px'}"></span>
                                        <div style="width: 100%;display: flex">
                                            <a-input :style="{'min-width':'100px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.metaConfigs.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </div>
                                </a-form-item>
                                <a-form-item>
                                    <a-button class="button" type="primary" @click="formData.metaConfigs.push({id: '', value: ''})">
                                        <icon-plus :style="{'margin-right': '3px'}"/>
                                        新增配置
                                    </a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="removepermissions">
                            <div :style="{'padding-bottom': '30px'}">
                                <div  class="section-area">
                                    权限移除
                                </div>
                                <a-transfer
                                    show-search
                                    one-way
                                    :title="['未选择', '已选择']"
                                    :data="androidPermissionsOptions"
                                    v-model:model-value="formData.removePermissions"
                                    :source-input-search-props="{
                                              placeholder:'source item search',
                                              'allow-clear': true
                                            }"
                                    :target-input-search-props="{
                                          placeholder:'target item search'
                                        }"
                                />
                            </div>
                        </section>
                        <section v-if="formData.packMode === 'pad'" id="globalgradle">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    GLOBAL GRADLE
                                </div>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item
                                        v-for="(v,k) in formData.globalDependencies"
                                        :label="k === 0 ? '依赖 dependencies' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-select
                                                :options="implementationTypeOptions"
                                                :style="{'width':'200px'}"
                                                v-model:model-value="v.type"
                                                :field-names="{value: 'id', label: 'label'}"
                                                allow-create/>
                                            <span :style="{width: '10px'}"></span>
                                            <a-input :style="{'min-width':'200px'}" v-model:model-value="v.url" placeholder="依赖内容" />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'100px'}" v-model:model-value="v.options" placeholder="(选填)exclude等内容 " />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalDependencies.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalDependencies.push({url: '', type: '', options: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalPlugin"
                                        :label="k === 0 ? '插件 plugins' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{'min-width':'400px'}" v-model:model-value="v.url" placeholder="please enter value..." />
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalPlugin.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalPlugin.push({'url': ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalBuildFeatures"
                                        :label="k === 0 ? '构建特性 buildFeatures' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalBuildFeatures.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalBuildFeatures.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalAaptOpts"
                                        :label="k === 0 ? 'AAPT选项 aaptOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalAaptOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalAaptOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalPackagingOpts"
                                        :label="k === 0 ? '打包选项 packagingOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalPackagingOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalPackagingOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalCompileOpts"
                                        :label="k === 0 ? '编译选项 compileOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalCompileOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalCompileOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalLintOpts"
                                        :label="k === 0 ? 'lint选项 lintOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalLintOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalLintOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                                <a-space :wrap="true">
                                    <a-form-item field="globalCompileVer" :rules="[{required:true,message:'请选择编译版本'}]" label="编译版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.globalCompileVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="globalMinVer" :rules="[{required:true,message:'请选择最低版本'}]" label="最低版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.globalMinVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="globalTargetVer" :rules="[{required:true,message:'请选择目标版本'}]" label="目标版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.globalTargetVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="globalNamesapce" label="命名空间">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.globalNamespace" placeholder="please enter value..." />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section v-else id="globalgradle">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    GLOBAL GRADLE
                                </div>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item class="group-title" label="maven地址:">
                                        <a-spin :loading="sdkLoading">
                                            <a-space direction="vertical">
                                                <a-list-item>google()</a-list-item>
                                                <a-list-item>mavenCentral()</a-list-item>
                                                <a-list-item>jcenter()</a-list-item>
                                                <a-list-item v-for="value in formData.repository">{{value}}</a-list-item>
                                            </a-space>
                                        </a-spin>
                                    </a-form-item>
                                    <a-form-item v-for="(v,k) in formData.mavenUrl">
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{'width':'100%'}" v-model:model-value="v.url" placeholder="please enter value..." />
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.mavenUrl.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.mavenUrl.push({'url': ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.globalDependencies"
                                        :label="k === 0 ? '依赖 dependencies' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-select
                                                :options="implementationTypeOptions"
                                                :style="{'width':'200px'}"
                                                v-model:model-value="v.type"
                                                :field-names="{value: 'id', label: 'label'}"
                                                allow-create/>
                                            <span :style="{width: '10px'}"></span>
                                            <a-input :style="{'min-width':'200px'}" v-model:model-value="v.url" placeholder="依赖内容" />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'100px'}" v-model:model-value="v.options" placeholder="(选填)exclude等内容 " />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.globalDependencies.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.globalDependencies.push({url: '', type: '', options: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="appgradle">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    APP GRADLE
                                </div>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item class="group-title" label="依赖 dependencies">
                                        <a-space direction="vertical">
                                            <a-list-item>implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])</a-list-item>
                                            <a-list-item v-for="value in getDependenciesShow">
                                                implementation {{value.type}}("{{value.url}}"){{value.options ? '{ ' + value.options + ' }' : ''}}
                                            </a-list-item>
                                        </a-space>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appDependencies">
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-select
                                                :options="implementationTypeOptions"
                                                :style="{'width':'200px'}"
                                                v-model:model-value="v.type"
                                                :field-names="{value: 'id', label: 'label'}"
                                                allow-create/>
                                            <span :style="{width: '10px'}"></span>
                                            <a-input :style="{'min-width':'200px'}" v-model:model-value="v.url" placeholder="依赖内容" />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'100px'}" v-model:model-value="v.options" placeholder="(选填)exclude等内容 " />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appDependencies.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appDependencies.push({url: '', type: '', options: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item
                                        v-for="(v,k) in formData.appPlugin"
                                        :label="k === 0 ? '插件 plugins' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{'min-width':'400px'}" v-model:model-value="v.url" placeholder="please enter value..." />
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appPlugin.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appPlugin.push({'url': ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appBuildFeatures"
                                        :label="k === 0 ? '构建特性 buildFeatures' : ''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appBuildFeatures.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appBuildFeatures.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appAaptOpts"
                                        :label="k === 0 ? 'AAPT选项 aaptOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appAaptOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appAaptOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appPackagingOpts"
                                        :label="k === 0 ? '打包选项 packagingOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appPackagingOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appPackagingOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appCompileOpts"
                                        :label="k === 0 ? '编译选项 compileOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appCompileOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appCompileOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-for="(v,k) in formData.appLintOpts"
                                        :label="k === 0 ? 'lint选项 lintOptions' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.appLintOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.appLintOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                    <a-form-item
                                        v-if="formData.packMode==='pad'"
                                        v-for="(v,k) in formData.bundleOpts"
                                        :label="k === 0 ? 'bundle配置' :''"
                                        :class="{'group-title': k === 0}"
                                    >
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-input :style="{width:'200px'}" v-model:model-value="v.id" placeholder="please enter key..." />
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.bundleOpts.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item v-if="formData.packMode==='pad'">
                                        <a-button class="button" type="primary" @click="formData.bundleOpts.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                                <a-space :wrap="true">
                                    <a-form-item field="appCompileVer" :rules="[{required:true,message:'请选择编译版本'}]" label="编译版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.appCompileVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="appMinVer" :rules="[{required:true,message:'请选择最低版本'}]" label="最低版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.appMinVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="appTargetVer" :rules="[{required:true,message:'请选择目标版本'}]" label="目标版本">
                                        <a-select
                                            :options="androidSdkVerOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.appTargetVer"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="multiDex" label="MultiDex支持">
                                        <a-select
                                            :options="[{label: '否', value: 'false'}, {label: '是', value: 'true'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.multiDex"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="debugMinifyEnabled" label="debug资源压缩">
                                        <a-select
                                            :options="[{label: '禁用', value: 'false'}, {label: '启用', value: 'true'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.debugMinifyEnabled"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="releaseMinifyEnabled" label="release资源压缩">
                                        <a-select
                                            :options="[{label: '禁用', value: 'false'}, {label: '启用', value: 'true'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.releaseMinifyEnabled"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="releaseShrinkResources" label="release资源缩减">
                                        <a-select
                                            :options="[{label: '禁用', value: 'false'}, {label: '启用', value: 'true'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.releaseShrinkResources"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="aidl" label="aidl目录">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.aidl" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="java" label="java目录">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.java" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="jniLibs" label="jniLibs目录">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.jniLibs" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="assets" label="assets目录">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.assets" placeholder="please enter value..." />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="rootgradle" v-if="formData.packMode === 'pad'">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    ROOT GRADLE
                                </div>
                                <a-form-item field="rootPlugins" :rules="[{required:true,message:'请选择插件'}]" label="插件">
                                    <a-spin :loading="adPlatformLoading">
                                        <a-space direction="vertical">
                                            <a-checkbox-group v-model="formData.rootPlugins">
                                                <a-checkbox v-for="item in getDependenciesPlugins" :disabled="item.require" :value="item.path">{{item.path +':'+ item.version}}</a-checkbox>
                                            </a-checkbox-group>
                                        </a-space>
                                    </a-spin>
                                </a-form-item>
                            </div>
                        </section>
<!--                    </a-spin>-->
                </a-form>
            </div>
        </div>
        <complete-log-modal ref="completeLogRef" />
        <a-drawer
            :style="{'z-index': 1002}"
            popup-container="#pack-body"
            :visible="drawerTab !== ''"
            :width="'80%'"
            @cancel="closeDraw"
            :footer="false"
        >
            <template #title> {{drawerName}} </template>
            <div v-if="drawerTab === 'task'">
                <a-tabs type="card-gutter" size="medium" @change="taskTabChange">
                    <a-tab-pane key="queue" title="打包队列">
                        <a-table :columns="taskColumns" :data="runWaitList">
                            <template #status="{record}">
                                <a-space>
                                    <a-tag :color="record.status === '打包中' ? 'red' : 'green'">{{record.status}}</a-tag>
                                    <a-tooltip content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => showLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="取消任务">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => cancelPack(ev, record)">
                                            <template #icon>
                                                <icon-close />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                    </a-tab-pane>
                    <a-tab-pane key="completed" title="已完成">
                        <a-table :loading="completeList.loading" :columns="taskColumns" :data="completeList.list" :pagination="false">
                            <template #status="{record}">
                                <a-space>
                                    <a-tooltip content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => getCompleteLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="下载文件">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => downloadPackage(ev, record)">{{record.resultType.toUpperCase()}}</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="再次打包">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => packAgain(ev, record)">
                                            <template #icon>
                                                <icon-refresh />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                        <div class="pagination">
                            <a-pagination :total="completeList.total" show-total @change="(current) => getHistoryLog(1, current, 10)"/>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="failed" title="失败">
                        <a-table :loading="failList.loading" :columns="taskColumns" :data="failList.list" :pagination="false">
                            <template #status="{record}">
                                <a-space>
                                    <a-tooltip content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => getCompleteLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="再次打包">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => packAgain(ev, record)">
                                            <template #icon>
                                                <icon-refresh />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                        <div class="pagination">
                            <a-pagination :total="failList.total" show-total @change="(current) => getHistoryLog(2, current, 10)"/>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div v-else-if="drawerTab === 'backup'">
                <div :style="{'text-align': 'end', 'padding': '5px 0 10px 0'}">
                    <a-button type="primary" :loading="backupLoading" @click="(ev) => backupOperate(1)">添加备份</a-button>
                </div>
                <a-table :columns="backupColumns" :data="backupList">
                    <template #optional="{record}">
                        <a-space>
                            <a-tooltip content="复制备份路径">
                                <a-link @click="(ev) => copyText(record.name)">
                                    <template #icon>
                                        <icon-copy size="15"/>
                                    </template>
                                </a-link>
                            </a-tooltip>
                            <a-tooltip v-if="deleteBackup.includes(record.name)" content="正在删除备份">
                                <a-link loading></a-link>
                            </a-tooltip>
                            <a-tooltip v-else content="删除备份">
                                <a-popconfirm content="确认删除吗" type="warning" @ok="backupOperate(3, record.name)">
                                    <a-link>
                                        <template #icon>
                                            <icon-close size="15"/>
                                        </template>
                                    </a-link>
                                </a-popconfirm>
                            </a-tooltip>
                        </a-space>
                    </template>
                </a-table>
            </div>
            <div v-else-if="drawerTab === 'log'">
                <a-textarea v-if="processLog" :style="{height: '650px','overflow-y': 'auto'}" v-model:model-value="processLog" auto-size></a-textarea>
                <a-empty v-else />
            </div>
            <div :style="{height: '100%'}" v-else-if="drawerTab === 'default'">
                <sendDataConfigure ref="configRef"/>
            </div>
        </a-drawer>
        <div class="sider-btn">
            <a-list>
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'task'}" @click="(ev) => drawerTabChange(ev,'task')">
                    任务记录
                </a-list-item>
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'backup'}" @click="(ev) => drawerTabChange(ev,'backup')">
                    备份列表
                </a-list-item>
                <!--                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'keystore'}" @click="(ev) => drawerTabChange(ev,'keystore')">-->
                <!--                    签名-->
                <!--                </a-list-item>-->
                <!--                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'log'}" @click="(ev) => drawerTabChange(ev,'log')">-->
                <!--                    日志-->
                <!--                </a-list-item>-->
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'default'}" @click="(ev) => drawerTabChange(ev,'default')">
                    default.json
                </a-list-item>
            </a-list>
        </div>

        <a-modal v-model:visible="contentShowModal.show" :width="700" :title="contentShowModal.title" title-align="start" :footer="false" @cancel="hideContentShow">
            <a-textarea ref="logRef" v-if="contentShowModal.type === 'log'" spellcheck="false" :style="{height: '500px', 'overflow-y': 'auto'}" v-model:model-value="getRunLog"></a-textarea>
            <a-spin v-else :style="{width: '100%'}" :loading="contentShowModal.loading">
                <div v-if="contentShowModal.content">
                    <a-textarea spellcheck="false" :style="{height: '500px', 'overflow-y': 'auto'}" v-model:model-value="contentShowModal.content" auto-size></a-textarea>
                    <a-space v-if="contentShowModal.type === 'keystore'">
                        <a-button :style="{'border-radius': '5px'}" :loading="exportKeystoreOldLoading" @click="(ev) => exportKeystore(ev)" type="primary">导出旧版google签名zip</a-button>
                        <a-button :style="{'border-radius': '5px'}" :loading="exportKeystoreNewLoading" @click="(ev) => exportKeystore(ev,true)" type="primary">导出新版google签名zip</a-button>
                    </a-space>
                </div>
                <a-empty v-else-if="!contentShowModal.loading" />
            </a-spin>
        </a-modal>
        <a-modal
            width="500px"
            :visible="true" v-for="(v,k) in packResult"
            :key="k" title="打包结果"
            title-align="start"
            :hide-cancel="true"
            @cancel="packResult.splice(k ,1)"
            @ok="packResult.splice(k ,1)">
            <div :style="{'text-align': 'center', 'word-break': 'break-word'}">
                {{v}}
            </div>
        </a-modal>
        <a-modal v-model:visible="uploadFileModal.show" :width="500" title-align="start" :title="uploadFileModal.title" :footer="false" @cancel="cancelUpload">
            <a-spin :loading="uploadFileModal.loading" :style="{width: '100%'}">
                <div :style="{'text-align': 'center'}">
                    <div v-if="uploadFileModal.type === 'keystore'">
                        <a-form layout="vertical">
                            <a-form-item field="keystoreName" :rules="[{required:true,message:'请填写签名名称'}]" label="签名名称">
                                <a-input v-model:model-value="formData.keystoreName" placeholder="please enter name..." allow-clear/>
                            </a-form-item>
                            <a-form-item field="keystorePass" :rules="[{required:true,message:'请填写签名pass'}]" label="签名pass">
                                <a-input v-model:model-value="formData.keystorePass" placeholder="please enter name..." allow-clear/>
                            </a-form-item>
                            <a-form-item field="keystoreAlias" :rules="[{required:true,message:'请填写签名别名'}]" label="签名别名">
                                <a-input v-model:model-value="formData.keystoreAlias" placeholder="please enter name..." allow-clear/>
                            </a-form-item>
                            <a-form-item field="keystoreKeyPass" :rules="[{required:true,message:'请填写签名keypass'}]" label="签名keypass">
                                <a-input v-model:model-value="formData.keystoreKeyPass" placeholder="please enter name..." allow-clear/>
                            </a-form-item>
                        </a-form>
                        <a-space>
                            <a-button @click="generateKeystore">生成签名</a-button>
                            <a-upload type="primary" :custom-request="handleUpload" :accept="uploadFileModal.accept" :show-file-list="false"/>
                        </a-space>
                    </div>
                    <div v-else>
                        <a-upload
                            draggable
                            :custom-request="handleUpload"
                            :show-file-list="false"
                            :accept="uploadFileModal.accept"
                        >
                        </a-upload>
                        <span style="padding: 5px 0;font-size: 11px;color: #ff5722">{{uploadFileModal.tips}}</span>
                    </div>
                </div>
            </a-spin>
        </a-modal>
        <a-modal v-model:visible="confirmModal.show" :width="500" title-align="start" :title="confirmModal.title" :footer="false" @cancel="confirmModal.cancelHandle">
            <div :style="{'text-align': 'center'}">
                <div style="padding: 5px 5px 30px 5px">
                    {{confirmModal.content}}
                </div>
                <div style="text-align: end">
                    <a-space>
                        <a-button @click="confirmModal.cancelHandle">取消</a-button>
                        <a-button type="primary" :loading="confirmModal.loading" @click="confirmModal.okHandle">确认</a-button>
                    </a-space>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {IconClose, IconMinus, IconPlus, IconRefresh} from '@arco-design/web-vue/es/icon';
import {computed, nextTick, onMounted, reactive, ref} from "vue";
import CompleteLogModal from "@/views/operation/pack/packCenter/components/completeLogModal.vue";
import sendDataConfigure from "@/views/operation/pack/packData/components/sendDataConfigure.vue";
import {useEventBus} from '@vueuse/core'
import {Message} from '@arco-design/web-vue';
import _ from "lodash";
import {useUserStore} from '@/store';
import {LocalStorageEventBus} from "@/types/event-bus";
import {
  androidPermissions,
  backupColumns,
  bundleOptions,
  configsGroup,
  cpuLibOptions,
  gradleVersionOptions,
  implementationTypeOptions,
  langOptions,
  metaDefaultConfigs, metaDefaultConfigsOld,
  packModeOptions,
  packTypeOptions,
  taskColumns
} from "@/views/operation/pack/packCenter/components/listJson";
import {getAppVersionList, getPackConfig, getPackHistory, getPackHistoryList, savePackConfig, savePackHistory} from "@/api/marketing/api";
import {formatTimestamp} from "@/utils/dateUtil";

const userStore = useUserStore();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const emits = defineEmits(['sendMessage', 'updateLoading']);
const osName = 'android'
const formRef = ref()
const formData = reactive({
  dataVersion: '',
  cpuLibs: ['arm64-v8a', 'armeabi-v7a'],
  packType: 'unity',
  packMode: 'normal',
  url: '',
  branch: 'master',
  name: '',
  versionNameAuto: 1,
  versionCode: 1,
  versionName: '1.0.0',
  gradleVersion: '8.9',
  ndkVersion: 11,
  sdkVer: '',
  keystoreVerify: 'old',
  keystoreVerifyLog: false,
  adaptiveIcon: 0,
  resguard: '0',
  encryptDefaultJson: 1,
  encryptMode: 'RSA',
  sha1: '',
  maxAdReview: '',
  exportProject: 0,
  adMediation: [],
  adPlatform: [],
  sdkDependencies: ['Core'],
  metaConfigs: [],
  removePermissions: [],
  keystoreName: '',
  keystorePass: '123456',
  keystoreAlias: '',
  keystoreKeyPass: '123456',
  names: [{id: '', value: ''}],
  mavenUrl: [],
  globalDependencies: [{url: '', type: '', options: ''}],
  globalPlugin: [{url: 'com.android.library'}],
  globalBuildFeatures: [{id: '' , value: ''}],
  globalAaptOpts: [{id: 'noCompress', value: "['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')"},{id: 'ignoreAssetsPattern', value: "\"!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~\""}],
  globalNdkOpts: [],
  globalPackagingOpts: [{id: 'doNotStrip', value: '*/armeabi-v7a/*.so'}, {id: 'doNotStrip', value: '*/arm64-v8a/*.so'}],
  globalLintOpts: [{id: 'abortOnError', value: 'false'}],
  globalCompileOpts: [{id: 'sourceCompatibility', value: 'JavaVersion.VERSION_11'},{id: 'targetCompatibility ', value: 'JavaVersion.VERSION_11'}],
  globalCompileVer: 34,
  globalMinVer: 21,
  globalTargetVer: 34,
  globalNamespace: 'com.unity3d.player',
  appPlugin: [{url: 'com.android.application'}, {url: 'com.google.gms.google-services'}],
  appDependencies: [{url: '', type: '', options: ''}],
  appBuildFeatures: [{id: '' , value: ''}],
  appAaptOpts: [{id: 'noCompress', value: "'.unity3d', '.ress', '.resource', '.obb'"}],
  appNdkOpts: [],
  appPackagingOpts: [{id: '', value: ''}],
  appLintOpts: [{id: 'abortOnError', value: 'false'}],
  appCompileOpts: [{id: 'sourceCompatibility', value: 'JavaVersion.VERSION_1_8'},{id: 'targetCompatibility ', value: 'JavaVersion.VERSION_1_8'}],
  appCompileVer: 34,
  appMinVer: 21,
  appTargetVer: 34,
  multiDex: 'true',
  debugMinifyEnabled: 'false',
  releaseMinifyEnabled: 'true',
  releaseShrinkResources: 'true',
  aidl: '',
  java: '',
  jniLibs: '',
  assets: '',
  bundleOpts: bundleOptions,
  rootPlugins: [],
  ksUploaded: 0,
  gsUploaded: 0,
  iconUploaded: 0
});
const projectChecked = ref(false)

const processLog = ref('')
const runningList = ref<Array<any>>([])
const waitingList = ref<Array<any>>([])
const backupList = ref<Array<any>>([])
const packResult = ref<Array<any>>([])
const uploadServer = ref('')
const configRef = ref()
const completeLogRef = ref()
const logRef = ref()

const loading = ref(false)
const jsonVersionLoading = ref(false)
const backupLoading = ref(false)
const checkProjectLoading = ref(false)
const sdkLoading = ref(false)
const iconLoading = ref(false)
const adPlatformLoading = ref(false)
const exportKeystoreOldLoading = ref(false)
const exportKeystoreNewLoading = ref(false)
const apkPackLoading = ref(false)
const aabPackLoading = ref(false)
const configLoading = ref(false)
const saveConfigLoading = ref(false)
const appConfigSaved = ref(true)
const globalGradleSaved = ref(true)

const configIndex = ref(0)
const cpuLibCheckedAll = ref(false)
const cpuLibIndeterminate = ref(true)
const jsonVersionOptions= ref<Array<object>>([])
const sdkOptions= ref<Array<string>>([])
const sdkContent= ref({})
const sdkDependenciesUrlOptions= ref({})
const sdkDependenciesPluginsOptions= ref({})
const ndkVersionOptions = ref<Array<number>>([])
const androidPermissionsOptions = ref<Array<object>>([])
const drawerTab = ref('')
const drawerName = ref('')
const androidSdkVerOptions = ref([])
const iconUrlsContent = ref([])
const imagesReqId = ref()
const uploadFileModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    method: '',
    tips: '',
    accept: ''
})
const contentShowModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    content: ''
})
const deleteBackup = ref<Array<string>>([])
const completeList = reactive({
    loading: false,
    total: 0,
    list: []
})
const failList = reactive({
    loading: false,
    total: 0,
    list: []
})
const confirmModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    content: '',
    cancelHandle: () => {},
    okHandle: () => {}
})

const props = defineProps({
    filter: {
        type: Object,
        default: () => {
        }
    }
})

const refreshPage = (appData: object) => {
    formData.appId = appData?.code || ''
    formData.package = appData?.package || ''
    formData.name = appData?.name || ''
    drawerTab.value = ''
    loadDataVersion(formData.appId)
    loadPackInfo()
}

const loadDataVersion = (appId: string) => {
    jsonVersionLoading.value = true
    getAppVersionList({appId: appId}).then(res => {
        jsonVersionOptions.value = [];
        if (res) {
            res.forEach(v => {
                jsonVersionOptions.value.push(
                    {id: v.name, label: v.name}
                );
            })
        }
    }).finally(() => {
        jsonVersionLoading.value = false
    })
}

const loadSdkList = () => {
    sdkLoading.value = true
    sendMessage(JSON.stringify({
        method: 'getSdkVerList',
        osName: osName,
        payload: {}
    }))
}

const loadIconContents = () => {
    if (formData.iconUploaded) {
        iconLoading.value = true
        imagesReqId.value = Date.now()
        iconUrlsContent.value = []
        sendMessage(JSON.stringify({
            method: 'getIconUrlContent',
            osName: osName,
            payload: {
                appId: formData.appId,
                imageReqId: Date.now()
            }
        }))
    }
}

const getAdPlatform = computed(() => {
    const options = []
    sdkDependenciesUrlOptions.value = {}
    if (formData.sdkVer && sdkContent.value && sdkContent.value["adPlatform"]) {
        const adPlatform = sdkContent.value["adPlatform"]
        const adPlatformKeys = Object.keys(adPlatform)
        adPlatformKeys.forEach(v => {
            //只能选择一个广告聚合，选择后其它广告聚合不可用
            let isDisabled = false
            if (formData.adPlatform && formData.adPlatform.length) {
                formData.adPlatform.forEach(adp => {
                    const adpl = adp.split('#')[0]
                    if (adPlatformKeys.includes(adpl) && adpl != v) {
                        isDisabled = true
                    }
                })
            }
            const item = {label: v, value: v, children: [], disabled: isDisabled}
            Object.keys(adPlatform[v]).forEach(_v => {
                const index = `${v}#${_v}`
                item["children"].push({label: _v, value: index, disabled: isDisabled})
                sdkDependenciesUrlOptions.value[index] = adPlatform[v][_v]
            })
            options.push(item)
        })
    }

    return options
})

const getSdkDependencies = computed(() => {
    let dependencies = []
    if (formData.sdkVer && sdkContent.value && sdkContent.value["dependencies"]) {
        const dps = sdkContent.value["dependencies"]
        Object.keys(dps).forEach(v => {
            dependencies.push(v)
            const index = `dependencies#${v}`
            sdkDependenciesUrlOptions.value[index] = dps[v]
        })
    }

    return dependencies
})

const getDependenciesPlugins = computed(() => {
    let plugins = []
    if (formData.sdkVer && sdkContent.value && sdkContent.value["plugins"]) {
        if (formData.packMode === 'pad') {
            plugins = sdkContent.value["plugins"]["pad"]
        } else {
            plugins = sdkContent.value["plugins"]["classic"]
        }

        plugins.forEach(v => {
            if (v.require && !formData.rootPlugins.includes(v.path)) {
                formData.rootPlugins.push(v.path)
            }

            sdkDependenciesPluginsOptions.value[v.path] = v
        })
    }

    return plugins
})

const getDependenciesShow = computed(() => {
    let dependencies = []

    if (formData.adPlatform && formData.adPlatform.length) {
        formData.adPlatform.forEach(v => {
            if (sdkDependenciesUrlOptions.value[v]) {
                dependencies.push(...sdkDependenciesUrlOptions.value[v])
            }
        })
    }

    if (formData.sdkDependencies && formData.sdkDependencies.length) {
        formData.sdkDependencies.forEach(v => {
            if (sdkDependenciesUrlOptions.value[`dependencies#${v}`]) {
                dependencies.push(...sdkDependenciesUrlOptions.value[`dependencies#${v}`])
            }
        })
    }

    return dependencies
})

const changeSdk = (value: string) => {
    if (sdkOptions.value.includes(value)) {
        adPlatformLoading.value = true
        sendMessage(JSON.stringify({
            method: 'getSdkContent',
            osName: osName,
            payload: {
                sdkVer: value
            }
        }))
    }
}

const changeAdMediation = (value, adm) => {
    const admCap = _.upperFirst(adm)
    if (value) {
        if (!formData.adMediation.includes(adm)) {
            formData.adMediation.push(adm)
        }
        if (!formData.sdkDependencies.includes(admCap)) {
            formData.sdkDependencies.push(admCap)
        }
    } else {
        if (formData.adMediation.includes(adm)) {
            formData.adMediation.splice(formData.adMediation.indexOf(adm), 1)
        }
        const adms = [];
        Object.keys(sdkContent.value['adPlatform']).forEach(v => adms.push(_.upperFirst(v)))
        formData.sdkDependencies.forEach((v, k) => {
            if (adms.includes(v) && !formData.adMediation.includes(v)) {
                formData.sdkDependencies.splice(k, 1)
            }
        })
        let tmp = []
        formData.adPlatform.forEach(v => {
            if (!v.startsWith(adm+'#')) {
                tmp.push(v)
            }
        })
        formData.adPlatform = tmp
    }
}

const getMetaList = computed(() => {
    const options = []

    if (!formData.sdkVer.startsWith('10.')) {
      if (metaDefaultConfigsOld && metaDefaultConfigsOld.length) {
        const usedConfigs = []
        if (formData.metaConfigs && formData.metaConfigs.length) {
          formData.metaConfigs.forEach(v => {
            usedConfigs.push(v.id)
          })
        }
        metaDefaultConfigsOld.forEach(v => {
          if (!usedConfigs.includes(v.id)) {
            options.push(v.id)
          }
        })
      }
    } else {
      if (metaDefaultConfigs && metaDefaultConfigs.length) {
        const usedConfigs = []
        if (formData.metaConfigs && formData.metaConfigs.length) {
          formData.metaConfigs.forEach(v => {
            usedConfigs.push(v.id)
          })
        }
        metaDefaultConfigs.forEach(v => {
          if (!usedConfigs.includes(v.id)) {
            options.push(v.id)
          }
        })
      }
    }

    return options
})

const getCheckGroup = computed(() => {
    const group = {}
    Object.keys(sdkContent.value['adPlatform']).forEach(v => {group[v] = 0})
    formData.adPlatform.forEach(v => {
        const adm = v.split('#')[0]
        if (group.hasOwnProperty(adm)) {
            group[adm] += 1
        }
    })

    return group
})

const changeAllMediation = (value, adp) => {
    if (value) {
        getAdPlatform.value.some(v => {
            if (v.value === adp) {
                v.children.forEach(_v => {
                    if (!formData.adPlatform.includes(_v.value)) {
                        formData.adPlatform.push(_v.value)
                    }
                })
                return true
            }
        })
    } else {
        const tmp = []
        formData.adPlatform.forEach(v => {
            if (!v.startsWith(adp+'#')) {
                tmp.push(v)
            }
        })
        formData.adPlatform = tmp
    }
}

const changeAllSdkDependencies = (value) => {
    formData.sdkDependencies = value ? getSdkDependencies.value : []
}

const runWaitList = computed(() => {
    let list = []
    list.push(...runningList.value, ...waitingList.value)
    return list
})

const loadPackInfo = () => {
    if (!formData.appId) {
        Message.warning('请选择打包应用')
        return
    }

    configLoading.value = true
    getPackConfig({app_id: formData.appId}).then(res => {
        projectChecked.value = res?.projectChecked == 1
        formatFormData(res)
        loadIconContents()
        loadSdkList()
    }).catch(e => {
        console.log(e)
    }).finally(() => {
        configLoading.value = false
    })

    backupOperate(2)
}

const formatFormData = (res) => {
    formData.dataVersion = res?.dataVersion || '';
    formData.cpuLibs = res?.cpuLibs || ['arm64-v8a', 'armeabi-v7a'];
    formData.packType = res?.packType || 'unity';
    formData.packMode = res?.packMode || 'normal';
    formData.url = res?.gitOpts?.url || '';
    formData.branch = res?.gitOpts?.branch || 'master';
    formData.name = res?.appOpts?.name || formData.name;
    formData.versionNameAuto = res?.appOpts?.hasOwnProperty('versionNameAuto') ? res.appOpts.versionNameAuto : 1;
    formData.versionCode = res?.appOpts?.versionCode || 1;
    formData.versionName = res?.appOpts?.versionName || '1.0.0';
    formData.gradleVersion = res?.pluginOpts?.gradleVersion || '8.9';
    formData.ndkVersion = res?.pluginOpts?.ndkVersion || 11;
    formData.sdkVer = res?.buildOpts?.sdkVer || '';
    formData.keystoreVerify = res?.buildOpts?.keystoreVerify || 'old';
    formData.keystoreVerifyLog = res?.buildOpts?.keystoreVerifyLog || false;
    formData.adaptiveIcon = res?.buildOpts?.adaptiveIcon || 0;
    formData.resguard = res?.buildOpts?.resguard || '0';
    formData.encryptDefaultJson = res?.buildOpts?.hasOwnProperty('encryptDefaultJson') ? res.buildOpts.encryptDefaultJson : 1;
    formData.encryptMode = res?.buildOpts?.encryptMode || 'RSA';
    formData.sha1 = res?.buildOpts?.sha1 || '';
    formData.maxAdReview = res?.buildOpts?.maxAdReview || '';
    formData.exportProject = res?.buildOpts?.exportProject || 0;
    formData.adMediation = res?.buildOpts?.adMediation || [];
    formData.adPlatform = res?.buildOpts?.adPlatform || [];
    formData.sdkDependencies = res?.buildOpts?.sdkDependencies || ['Core'];
    formData.metaConfigs = res?.metadataOpts || [];
    formData.removePermissions = res?.removePermissionOpts || [];
    formData.keystoreName = res?.keystoreOpts.name || formData.package;
    formData.keystorePass = res?.keystoreOpts.pass || '123456';
    formData.keystoreAlias = res?.keystoreOpts.alias || formData.package;
    formData.keystoreKeyPass = res?.keystoreOpts.keypass || '123456';
    formData.names = res?.names || [{id: '', value: ''}];
    formData.mavenUrl = res?.mavenUrl || [];
    formData.globalDependencies = res?.globalDependencies || [{url: '', type: '', options: ''}];
    formData.globalPlugin = res?.globalPlugin || [{url: 'com.android.library'}];
    formData.globalBuildFeatures = res?.globalBuildFeatures || [{id: '' , value: ''}];
    formData.globalAaptOpts = res?.globalAaptOpts || [{id: 'noCompress', value: "['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')"},{id: 'ignoreAssetsPattern', value: "\"!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~\""}];
    formData.globalNdkOpts = res?.globalNdkOpts || [];
    formData.globalPackagingOpts = res?.globalPackagingOpts || [{id: 'doNotStrip', value: '*/armeabi-v7a/*.so'}, {id: 'doNotStrip', value: '*/arm64-v8a/*.so'}];
    formData.globalLintOpts = res?.globalLintOpts  || [{id: 'abortOnError', value: 'false'}];
    formData.globalCompileOpts = res?.globalCompileOpts  || [{id: 'sourceCompatibility', value: 'JavaVersion.VERSION_11'},{id: 'targetCompatibility ', value: 'JavaVersion.VERSION_11'}];
    formData.globalCompileVer = res?.globalBuild?.compileVer || 34;
    formData.globalMinVer = res?.globalBuild?.minVer || 21;
    formData.globalTargetVer = res?.globalBuild.targetVer || 34;
    formData.globalNamespace = res?.globalBuild.namespace || 'com.unity3d.player';
    formData.appPlugin = res?.appPlugin || [{url: 'com.android.application'}, {url: 'com.google.gms.google-services'}];
    formData.appDependencies = res?.appDependencies || [{url: '', type: '', options: ''}];
    formData.appBuildFeatures = res?.appBuildFeatures || [{id: '' , value: ''}];
    formData.appAaptOpts = res?.appAaptOpts || [{id: 'noCompress', value: "'.unity3d', '.ress', '.resource', '.obb'"}];
    formData.appNdkOpts = res?.appNdkOpts || [];
    formData.appPackagingOpts = res?.appPackagingOpts || [{id: '', value: ''}];
    formData.appLintOpts = res?.appLintOpts  || [{id: 'abortOnError', value: 'false'}];
    formData.appCompileOpts = res?.appCompileOpts  || [{id: 'sourceCompatibility', value: 'JavaVersion.VERSION_1_8'},{id: 'targetCompatibility ', value: 'JavaVersion.VERSION_1_8'}];
    formData.appCompileVer = res?.appBuild?.compileVer || 34;
    formData.appMinVer = res?.appBuild?.minVer || 21;
    formData.appTargetVer = res?.appBuild.targetVer || 34;
    formData.multiDex = res?.appBuild.multiDex|| 'true';
    formData.debugMinifyEnabled = res?.appBuild.debugMinifyEnabled|| 'false';
    formData.releaseMinifyEnabled = res?.appBuild.releaseMinifyEnabled|| 'true';
    formData.releaseShrinkResources = res?.appBuild.releaseShrinkResources|| 'true';
    formData.aidl = res?.appSourcePath.aidl || '';
    formData.java = res?.appSourcePath.java || '';
    formData.jniLibs = res?.appSourcePath.jniLibs || '';
    formData.assets = res?.appSourcePath.assets || '';
    formData.bundleOpts = res?.bundleOpts|| bundleOptions;
    formData.rootPlugins = res?.rootPlugins|| [];
    formData.ksUploaded = res?.filesUploaded?.ksUploaded || 0;
    formData.gsUploaded = res?.filesUploaded?.gsUploaded || 0;
    formData.iconUploaded = res?.filesUploaded?.iconUploaded || 0;

    if (!formData.metaConfigs || formData.metaConfigs.length === 0) {
        formData.metaConfigs = JSON.parse(JSON.stringify(metaDefaultConfigs))
        formData.metaConfigs.forEach(v => {
            if (v.id === 'ivy_app_id') {
                v.value = formData.appId
            }
        })
    }

    if (formData.appAaptOpts.length == 0) {
        formData.appAaptOpts = [{id: '', value: ''}]
    }
    if (formData.appPackagingOpts.length == 0) {
        formData.appPackagingOpts = [{id: '', value: ''}]
    }
    if (formData.sdkDependencies.length == 0) {
        formData.sdkDependencies = ['Core']
    }
    if (formData.appBuildFeatures.length == 0) {
        formData.appBuildFeatures = [{id: '', value: ''}]
    }
    if (formData.globalBuildFeatures.length == 0) {
        formData.globalBuildFeatures = [{id: '', value: ''}]
    }
    if (formData.cpuLibs.length > 0) {
        if (formData.cpuLibs.length === cpuLibOptions.length) {
            cpuLibCheckedAll.value = true
            cpuLibIndeterminate.value = false
        } else {
            cpuLibCheckedAll.value = false
            cpuLibIndeterminate.value = true
        }
    } else {
        cpuLibCheckedAll.value = false
        cpuLibIndeterminate.value = false
    }

    if (!formData.globalPlugin || formData.globalPlugin.length == 0) {
        formData.globalPlugin = [{url: 'com.android.library'}]
    }

    if (!formData.globalAaptOpts || formData.globalAaptOpts.length == 0) {
        formData.globalAaptOpts = [
            {id: 'noCompress', value: "['.unity3d','.ress','.resource','.obb'] + unityStreamingAssets.tokenize(', ')"},
            {id: 'ignoreAssetsPattern', value: '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'}
        ]
    }

    if (!formData.globalPackagingOpts || formData.globalPackagingOpts.length == 0) {
        formData.globalPackagingOpts = [
            {id: 'doNotStrip', value: '*/armeabi-v7a/*.so'},
            {id: 'doNotStrip', value: '*/arm64-v8a/*.so'}
        ]
    }

    if (!formData.globalCompileOpts || formData.globalCompileOpts.length == 0) {
        formData.globalCompileOpts = [
            {id: 'sourceCompatibility', value: 'JavaVersion.VERSION_1_8'},
            {id: 'targetCompatibility ', value: 'JavaVersion.VERSION_1_8'}
        ]
    }

    if (!formData.globalLintOpts || formData.globalLintOpts.length == 0) {
        formData.globalLintOpts = [
            {id: 'abortOnError', value: 'false'},
            {id: 'checkReleaseBuilds', value: 'false'}
        ]
    }

    if (!formData.globalLintOpts || formData.globalLintOpts.length == 0) {
        formData.globalLintOpts = [
            {id: 'abortOnError', value: 'false'},
            {id: 'checkReleaseBuilds', value: 'false'}
        ]
    }

    if (!formData.bundleOpts || formData.bundleOpts.length == 0) {
        formData.bundleOpts = bundleOptions
    }
}

onMounted(() => {
    let appId = sessionStorage.getItem('app-id') || ''
    loadDataVersion(appId)

    for (let i = 9; i <= 30; i++) {
        ndkVersionOptions.value.push(i)
    }
    for (let i = 17; i <= 37; i++) {
        androidSdkVerOptions.value.push(i)
    }

    androidPermissions.forEach(v => {
        androidPermissionsOptions.value.push({
            value: `android.permission.${v.id}`,
            label: v.id,
            disabled: false
        })
    })
})

const checkedAllCpuLib = (value: boolean) => {
    cpuLibIndeterminate.value = false;
    if (value) {
        cpuLibCheckedAll.value = true;
        formData.cpuLibs = cpuLibOptions
    } else {
        cpuLibCheckedAll.value = false;
        formData.cpuLibs = []
    }
}

const changeCpuLib = (values: Array<string>) => {
    if (values.length === cpuLibOptions.length) {
        cpuLibCheckedAll.value = true
        cpuLibIndeterminate.value = false;
    } else if (values.length === 0) {
        cpuLibCheckedAll.value = false
        cpuLibIndeterminate.value = false;
    } else {
        cpuLibCheckedAll.value = false
        cpuLibIndeterminate.value = true;
    }
}


const configSelectChange = (hash: string) => {
    configsGroup.forEach((v, k) => {
        if (hash == v.link) {
            configIndex.value = k
        }
    })
}

const getKeystoreInfo = () => {
    sendMessage(JSON.stringify({
        method: "getKeystoreInfo",
        osName: osName,
        payload: {
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package,
            name: formData.keystoreName,
            pass: formData.keystorePass,
            alias: formData.keystoreAlias,
            keypass: formData.keystoreKeyPass,
            sha1: formData.sha1
        }
    }))
}

const getGoogleServices = () => {
    sendMessage(JSON.stringify({
        method: "getGoogleServices",
        osName: osName,
        payload: {
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package
        }
    }))
}

const exportKeystore = (event: MouseEvent, isNew: boolean = false) => {
    if (isNew) {
        exportKeystoreNewLoading.value = true
    } else {
        exportKeystoreOldLoading.value = true
    }

    sendMessage(JSON.stringify({
        method: "exportKeystore",
        osName: osName,
        payload: {
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package,
            name: formData.keystoreName,
            pass: formData.keystorePass,
            alias: formData.keystoreAlias,
            keypass: formData.keystoreKeyPass,
            isNew: isNew
        }
    }))
}

const generateKeystore = () => {
    if (!formData.keystoreName || !formData.keystorePass || !formData.keystoreAlias || !formData.keystoreKeyPass) {
        Message.warning('请填写完整的签名信息')
        return
    }

    uploadFileModal.loading = true;
    sendMessage(JSON.stringify({
        method: "generateKeystore",
        osName: osName,
        payload: {
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package,
            name: formData.keystoreName,
            pass: formData.keystorePass,
            alias: formData.keystoreAlias,
            keypass: formData.keystoreKeyPass,
        }
    }));
}

const handleUpload = (options: any) => {
    const file = options.fileItem.file
    if (!file) return;

    let filename = `tmpFile_${Date.now()}`
    if (uploadFileModal.type === 'keystore') {
        if (!formData.keystoreName || !formData.keystorePass || !formData.keystoreAlias || !formData.keystoreKeyPass) {
            Message.warning('请填写完整的签名信息')
            return;
        }

        if (file.name !== `${formData.keystoreName}.keystore`) {
            Message.warning('签名文件名称与填写名称不一致')
            return;
        }

        filename = file.name
    }

    const chunkSize = 1024 * 1024; // 每次发送1MB的数据块
    let start = 0;
    uploadFileModal.loading = true
    const sendNextChunk = () => {
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        const header = {
            to: uploadServer.value,
            method: uploadFileModal.method,
            osName: osName,
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package,
            fileName: filename,
            chunk: Math.floor(start / chunkSize),
            totalChunks: Math.ceil(file.size / chunkSize),
        };

        const reader = new FileReader();
        reader.readAsArrayBuffer(chunk);
        reader.onloadend = async (e) => {
            if (e.target && e.target.result) {
                const data = e.target.result as ArrayBuffer;
                const message = new Blob([JSON.stringify(header), '', new Uint8Array(data)]);
                message.arrayBuffer().then(res => {
                    sendMessage(res);
                    if (end < file.size) {
                        start = end;
                        sendNextChunk(); // 发送下一个数据块
                    }
                })
            }
        };
    };
    sendNextChunk()
}

const buildApk = (event: MouseEvent, isAab: boolean = false) => {
    let isValid = false
    formRef.value.validate((errors: any) => {
        isValid = !errors;
    }).then(() => {
      console.log(formatAppJson())
      console.log(formatRootGradle())
      console.log(formatGlobalGradle())
      console.log(formatAppGradle())
      console.log(formatSettingsGradle())
        if (!isValid) {
            Message.warning("请检查错误")
            return
        }

        if (sdkLoading.value || adPlatformLoading.value) {
            Message.warning("请等待sdk内容加载完成")
            return
        }

        if (!formData.metaConfigs.length) {
            Message.warning("检测到未配置metadata，请配置")
            return
        }

        //将配置组合成文件内容 保存记录到打包历史中
        const appJson = formatAppJson()
        const rootGradle = formatRootGradle()
        const globalGradle = formatGlobalGradle()
        const appGradle = formatAppGradle()
        const settingsGradle = formatSettingsGradle()

        let saveData = getPackConfigSaveFormat()
        saveData.appType = isAab ? 'aab' : 'apk'
        // 将记录id作为任务id 并将文件内容发送至打包后台进行打包
        if (isAab) {
            aabPackLoading.value = true
            updateLoading('aabPackLoading', true)
        } else {
            apkPackLoading.value = true
            updateLoading('apkPackLoading', true)
        }

        const payload = {
            buildType: formData.packType,
            packMode: formData.packMode,
            jsonVersion: formData.dataVersion,
            appId: formData.appId,
            packageName: formData.package,
            isBuildAab: isAab,
            cpuLib: formData.cpuLibs,
            keyName: formData.keystoreName,
            appJson: appJson,
            rootGradle: rootGradle,
            globalGradle: globalGradle,
            appGradle: appGradle,
            settingsGradle: settingsGradle,
            createTime: formatTimestamp(Date.now()),
            sdkVersion: formData.sdkVer,
            creator: userStore.name,
            saveData: saveData
        }

        if (!projectChecked.value) {
            Message.info('服务器正在检查源工程目录结构，请耐心等待')
            checkProjectLoading.value = true
            sendMessage(JSON.stringify({
                method: "checkProjectStruct",
                osName: osName,
                payload: payload
            }))
            return;
        }

        sendPack(payload)
    })
}

const sendPack = (payload) => {
    savePackHistory(payload['saveData']).then(res => {
        delete payload['saveData'];
        payload['taskId'] = `${res.code}`;
        sendMessage(JSON.stringify({
            method: "buildApk",
            osName: osName,
            payload: payload
        }));
    }).catch(e => {
        Message.error('打包任务添加失败', e)
        if (payload['isBuildAab']) {
            aabPackLoading.value = false
            updateLoading('aabPackLoading', false)
        } else {
            apkPackLoading.value = false
            updateLoading('apkPackLoading', false)
        }
    }).finally(() => {

    })
}

const packAgain = (event: MouseEvent, packRecord: object) => {
    drawerTab.value = ''
    configLoading.value = true
    getPackHistory({id: packRecord.id}).then(res => {
        formatFormData(res)
        Message.success('已恢复至历史配置')
    }).catch(e => {
        console.error(e)
    }).finally(() => {
        configLoading.value = false
    })
}

const getCompleteLog = (event: MouseEvent, packRecord: object) => {
    if (!formData.appId) {
        Message.warning("请选择包名")
        return
    }

    completeLogRef.value.openModal()
    sendMessage(JSON.stringify({
        method: "getCompleteLog",
        osName: osName,
        payload: {
            buildType: packRecord.buildType,
            appId: packRecord.appId,
            packageName: packRecord.packageName,
            logName: `${packRecord.id}`
        }
    }));
}

const selectedRunTask = ref()
const showLog = (event: MouseEvent, packRecord: object) => {
    selectedRunTask.value = packRecord
    contentShowModal.loading = false
    contentShowModal.show = true
    contentShowModal.type = 'log'
    contentShowModal.title = "日志"
    contentShowModal.content = ""
}

const getRunLog = computed(() => {
    let log = ''
    runningList.value.some(v => {
        if (v.id === selectedRunTask.value.id) {
            log = v.log
            return true
        }
    })
    nextTick(() => {
        if (logRef.value) {
            logRef.value.textareaRef.scrollTop = logRef.value.textareaRef.scrollHeight;
        }
    })
    return log
})

const cancelPack = (event: MouseEvent, packRecord: object) => {
    sendMessage(JSON.stringify({
        method: "cancelTask",
        osName: osName,
        payload: {
            robotId: packRecord.id,
            robotTime: packRecord.time,
            packageName: packRecord.packageName,
            status: packRecord.status === '打包中' ? 'run' : 'wait'
        }
    }));
}

const downloadPackage = (event: MouseEvent, packRecord: object) => {
    window.open(`https://10.80.1.16:1443/${packRecord.appId}_${packRecord.packageName}/${packRecord.resultType}/${packRecord.packageName}-${packRecord.versionName}.${packRecord.resultType}`)
}

const drawerTabChange = (event: MouseEvent, tabName: string, showAdd :boolean = false) => {
    if (!formData.appId) {
        Message.warning("请选择应用")
        return
    }

    drawerTab.value = tabName;
    switch (tabName) {
        case 'task':
            drawerName.value = '任务记录'
            break
        case 'backup':
            drawerName.value = '备份列表'
            break
        case 'default':
            drawerName.value = 'default.json'
            nextTick(() => {
                if (configRef.value) {
                    configRef.value.refresh(formData.appId, showAdd);
                } else {
                    console.error('configRef is undefined');
                }
            });
            break
    }
}

const formatAppJson = () => {
    let appJson = {};
    appJson['app'] = {
        packageName: formData?.package || '',
        name: formData?.name || '',
        versionCode: formData?.versionCode || 1,
        versionName: formData?.versionName || '1.0.0',
        versionNameAuto: formData?.versionNameAuto || 1,
        app_type: 'games'
    }

    appJson['build_config'] = {
        adaptiveIcon: formData?.adaptiveIcon || 0,
        encrypt_default_json: formData?.encryptDefaultJson || 0,
        encryptMode: formData?.encryptMode || '',
        keystoreVerify: formData?.keystoreVerify || 'new',
        keystoreVerifyLog: formData?.keystoreVerifyLog || false,
        resguard: formData?.resguard || '0',
        sdk_ver: formData?.sdkVer || '',
        maxAdReview: formData?.maxAdReview || '',
        exportProject: formData?.exportProject || 0
    }
    if (formData?.sha1) {
        appJson['build_config']["sha1"] = [formData.sha1]
    }

    appJson['git'] = {
        branch: formData?.branch || '',
        url: formData?.url || '',
    }

    appJson['keystore'] = {
        alias: formData?.keystoreAlias || formData.package,
        pass: formData?.keystorePass || '123456',
        name: formData?.keystoreName || formData.package,
        keypass: formData?.keystoreKeyPass || '123456'
    }

    const names = {}
    formData.names.forEach(v => {
        if (v.id && v.value) {
            names[v.id] = v.value
        }
    })
    if (Object.keys(names).length) {
        appJson['names'] = names;
    }

    const metadata = {}
    formData.metaConfigs.forEach(v => {
        if (v.id) {
            metadata[v.id] = v.value
        }
    })
    appJson['metadata'] = metadata

    appJson['plugin_version'] = {
        gradleVersion: formData?.gradleVersion || '8.9',
        ndk_version: formData?.ndkVersion || 11
    }

    appJson['remove_permissions'] = formData?.removePermissions || []

    return appJson
}

const formatRootGradle = () => {
    let plugins = '';
    formData.rootPlugins.forEach(v => {
        if (sdkDependenciesPluginsOptions.value[v]) {
            let info = sdkDependenciesPluginsOptions.value[v]
            plugins += `\tid '${info.path}' version '${info.version}' apply false\n`
        }
    })

    let rootGradle = `plugins {\n` +
        `${plugins}` +
        `//plugins depend replace line\n` +
        `}\n` +
        `task clean(type: Delete) {\n` +
        `\tdelete rootProject.buildDir\n` +
        `}`

    return rootGradle
}

const formatGlobalGradle = () => {
    if (formData.packMode === 'pad') {
        let dependencies = '\timplementation fileTree(dir: \'libs\', include: [\'*.jar\', \'*.aar\'])\n'
        formData.globalDependencies.forEach(v => {
            if (v.url) {
                if (v.type === 'fileTree') {
                    dependencies += `\timplementation ${v.type}(${v.url})\n`
                } else if (['platform', 'project'].includes(v.type)) {
                    dependencies += `\timplementation ${v.type}("${v.url.replace('$sdk_version', '$sdk_ver')}")\n`
                }else {
                    dependencies += `\timplementation ("${v.url.replace('$sdk_version', '$sdk_ver')}")` + (v.options ? `{\n\t\t${v.options}\n\t}\n` : '\n')
                }
            }
        })

        let ndkOptions = ''
        if (formData.globalNdkOpts) {
            let ndkContent = ''
            if (formData.packType === 'cocos') {
                ndkContent += `\n\t\t\tmoduleName "MyGame"`
            }
            if (formData.cpuLibs.length) {
                ndkContent += `\n\t\t\tabiFilters '${formData.cpuLibs.join("','")}'`
            }

            ndkOptions = `\t\tndk {${ndkContent}\n\t\t}`
        }

        let lintOptions = ''
        if (formData.globalLintOpts) {
            let lintContent = ''
            formData.globalLintOpts.forEach(v => {
                if (v.id && v.value) {
                    const valStr = ['true', 'false'].includes(v.value) ? v.value : `'${v.value}'`
                    lintContent += `\n\t\t${v.id} ${valStr}`
                }
            })
            lintOptions = `\tlintOptions {${lintContent}\n\t}`
        }

        let aaptOptions = ''
        if (formData.packType === 'unity' && formData.globalAaptOpts) {
            let aaptContent = ''
            formData.globalAaptOpts.forEach(v => {
                if (v.id && v.value) {
                    if (!v.value.startsWith('[')) {
                        aaptContent += `\n\t\t${v.id} ${v.value}`
                    } else {
                        aaptContent += `\n\t\t${v.id} = ${v.value}`
                    }
                }
            })
            if (aaptContent) {
                aaptOptions = `\taaptOptions {${aaptContent}\n\t}`
            }
        }

        let buildFeatures = ''
        if (formData.globalBuildFeatures) {
            let buildFeaturesContent = ''
            formData.globalBuildFeatures.forEach(v => {
                if (v.id && v.value) {
                    buildFeaturesContent += `\n\t\t${v.id} ${v.value}`
                }
            })
            if (buildFeaturesContent) {
                buildFeatures = `\tbuildFeatures {${buildFeaturesContent}\n\t}`
            }
        }

        let packagingOptions = ''
        if (formData.globalPackagingOpts) {
            let packagingContent = ''
            formData.globalPackagingOpts.forEach(v => {
                if (v.id && v.value) {
                    if (v.id === 'jniLibs') {
                        packagingContent += `\n\t\t${v.id}{ \n\t\t\t${v.value}\n\t\t}`
                    } else if (v.id === 'exclude' && v.value.startsWith('(')) {
                        packagingContent += `\n\t\t${v.id} ${v.value}`
                    } else {
                        packagingContent += `\n\t\t${v.id} '${v.value}'`
                    }
                }
            })
            if (packagingContent) {
                packagingOptions = `\tpackagingOptions {${packagingContent}\n\t}`
            }
        }

        let compileOptions = ''
        if (formData.globalCompileOpts) {
            let compileContent = ''
            formData.globalCompileOpts.forEach(v => {
                if (v.id && v.value) {
                    compileContent += `\n\t\t${v.id} ${v.value}`
                }
            })
            compileOptions = `\tcompileOptions {${compileContent}\n\t}`
        }

        let gradleContent = ''
        let pluginEnd = ''
        if (formData.globalPlugin.length) {
            formData.globalPlugin.forEach(v => {
                if (v["url"] !== 'com.android.library') {
                    pluginEnd += `\napply plugin: '${v["url"]}'`
                } else {
                    gradleContent += `apply plugin: '${v["url"]}'\n`
                }
            })
        }
        gradleContent += `dependencies {\n`+
            `${dependencies}`+
            `}\n` +
            `android {\n`+
            `    namespace  "${formData.globalNamespace}"\n`+
            `    compileSdkVersion ${formData.globalCompileVer}\n`+
            `\n`+
            `${compileOptions}\n`+
            `   defaultConfig {\n`+
            `        targetSdkVersion ${formData.appTargetVer}\n`+
            `        minSdkVersion ${formData.appMinVer}\n`+
            `\n`+
            `        versionCode 1\n`+
            `        versionName '0.1'\n`+
            `        consumerProguardFiles 'proguard-unity.txt', 'proguard-rules.pro'\n`+
            `\n`+
            `${ndkOptions}\n`+
            `   }\n`+

            `${lintOptions}\n`+
            `${aaptOptions}\n`+
            `${buildFeatures}\n`+
            `${packagingOptions}\n`+
            `}\n`+
            `${pluginEnd}`;

        return gradleContent
    } else {
        const buildMarven = getMavenContent()
        const projectMarven = getMavenContent()
        let dependencies = ''
        formData.globalDependencies.forEach(v => {
            if (v.url) {
                dependencies += `\n\t\t\tclasspath "${v.url}"`
            }
        })

        return `buildscript {` +
            `\n\trepositories {\n` +
            `${buildMarven}` +
            `\n\t}\n` +
            `\n\tdependencies {` +
            `${dependencies}` +
            `\n\t}` +
            `\n}` +
            `\nallprojects {` +
            `\n\trepositories {` +
            `${projectMarven}` +
            `\n\t}` +
            `\n}`
    }
}

const formatAppGradle = () => {
    let dependencies = '\timplementation fileTree(dir: \'libs\', include: [\'*.jar\', \'*.aar\'])\n'
    if (getDependenciesShow.value && getDependenciesShow.value.length) {
        getDependenciesShow.value.forEach(v => {
            if (v.type === 'fileTree') {
                dependencies += `\timplementation ${v.type}(${v.url})\n`
            } else if (['platform', 'project'].includes(v.type)) {
                dependencies += `\timplementation ${v.type}("${v.url.replace('$sdk_version', '$sdk_ver')}")\n`
            }else {
                dependencies += `\timplementation ("${v.url.replace('$sdk_version', '$sdk_ver')}")` + (v.options ? `{\n\t\t${v.options}\n\t}\n` : '\n')
            }
        })
    }
    formData.appDependencies.forEach(v => {
        if (v.url) {
            if (v.type === 'fileTree') {
                dependencies += `\timplementation ${v.type}(${v.url})\n`
            } else if (['platform', 'project'].includes(v.type)) {
                dependencies += `\timplementation ${v.type}("${v.url.replace('$sdk_version', '$sdk_ver')}")\n`
            }else if (v.type === '') {
                dependencies += `\timplementation ("${v.url.replace('$sdk_version', '$sdk_ver')}")` + (v.options ? `{\n\t\t${v.options}\n\t}\n` : '\n')
            } else {
                dependencies += `\t ${v.type}("${v.url.replace('$sdk_version', '$sdk_ver')}")\n`
            }
        }
    })

    dependencies += "//gradle position replace\n"

    let sourceSetsContent = ''
    if (formData.aidl) {
        sourceSetsContent += `\t\taidl.srcDir "${formData.aidl}"\n`
    }
    if (formData.java) {
        sourceSetsContent += `\t\tjava.srcDir "${formData.java}"\n`
    }
    if (formData.jniLibs) {
        sourceSetsContent += `\t\tjniLibs.srcDir "${formData.jniLibs}"\n`
    }
    if (formData.assets) {
        sourceSetsContent += `\t\tassets.srcDir "${formData.assets}"\n`
    }

    let ndkOptions = ''
    if (formData.appNdkOpts) {
        let ndkContent = ''
        if (formData.packType === 'cocos') {
            ndkContent += `\n\t\t\tmoduleName "MyGame"`
        }
        if (formData.cpuLibs.length) {
            ndkContent += `\n\t\t\tabiFilters '${formData.cpuLibs.join("','")}'`
        }

        ndkOptions = `\t\tndk {${ndkContent}\n\t\t}`
    }

    let lintOptions = ''
    if (formData.appLintOpts) {
        let lintContent = ''
        formData.appLintOpts.forEach(v => {
            if (v.id && v.value) {
                const valStr = ['true', 'false'].includes(v.value) ? v.value : `'${v.value}'`
                lintContent += `\n\t\t${v.id} ${valStr}`
            }
        })
        lintOptions = `\tlintOptions {${lintContent}\n\t}`
    }

    let aaptOptions = ''
    if (formData.packType === 'unity' && formData.appAaptOpts) {
        let aaptContent = ''
        formData.appAaptOpts.forEach(v => {
            if (v.id && v.value) {
                if (!v.value.startsWith('[')) {
                    aaptContent += `\n\t\t${v.id} ${v.value}`
                } else {
                    aaptContent += `\n\t\t${v.id} = ${v.value}`
                }
            }
        })
        if (aaptContent) {
            aaptOptions = `\taaptOptions {${aaptContent}\n\t}`
        }
    }

    let buildFeatures = ''
    if (formData.appBuildFeatures) {
        let buildFeaturesContent = ''
        formData.appBuildFeatures.forEach(v => {
            if (v.id && v.value) {
                buildFeaturesContent += `\n\t\t${v.id} ${v.value}`
            }
        })
        if (buildFeaturesContent) {
            buildFeatures = `\tbuildFeatures {${buildFeaturesContent}\n\t}`
        }
    }

    let packagingOptions = ''
    if (formData.appPackagingOpts) {
        let packagingContent = ''
        formData.appPackagingOpts.forEach(v => {
            if (v.id && v.value) {
                packagingContent += `\n\t\t${v.id} '${v.value}'`
            }
        })
        if (packagingContent) {
            packagingOptions = `\tpackagingOptions {${packagingContent}\n\t}`
        }
    }

    let compileOptions = ''
    if (formData.appCompileOpts) {
        let compileContent = ''
        formData.appCompileOpts.forEach(v => {
            if (v.id && v.value) {
                compileContent += `\n\t\t${v.id} ${v.value}`
            }
        })
        compileOptions = `\tcompileOptions {${compileContent}\n\t}`
    }

    let libOptions = ''
    if (formData.packType === 'cocos') {
        libOptions = '\nrepositories {\n' +
            '    flatDir {\n' +
            '        dirs \'libs\'\n' +
            '    }\n' +
            '}\n'
    }

    let bundleOptions = ''
    if (formData.packMode === 'pad' && formData.bundleOpts) {
        let bundleContent = ''
        formData.bundleOpts.forEach(v => {
            if (v.id && v.value) {
                bundleContent += `\n\t\t${v.id}{ \n\t\t\t${v.value}\n\t\t}`
            }
        })
        bundleOptions = `\n\tassetPacks = [":UnityDataAssetPack"]\n\tbundle {${bundleContent}\n\t}`
    }

    let buildTypeDebug = `\n\t\t\tsigningConfig signingConfigs.debug\n`
    if (formData.packMode === 'pad') {
        buildTypeDebug += `\n\t\t\tminifyEnabled ${formData.debugMinifyEnabled}\n`
        buildTypeDebug += `\n\t\t\tproguardFiles getDefaultProguardFile('proguard-android.txt')\n`
        buildTypeDebug += `\n\t\t\tjniDebuggable true\n`
    }

    let releaseShrink = ''
    if (formData.packMode !== 'pad') {
        releaseShrink = `\n\t\t\tshrinkResources ${formData.releaseShrinkResources}\n`
    }

    let gradleContent = ''
    let pluginEnd = ''
    if (formData.appPlugin.length) {
        let hasMax = false
        formData.appPlugin.forEach(v => {
            if (v["url"] !== 'com.android.application' && v["url"] !== 'applovin-quality-service') {
                pluginEnd += `\napply plugin: '${v["url"]}'`
            } else {
                gradleContent += `apply plugin: '${v["url"]}'\n`
            }
            if (!hasMax) {
                hasMax = v["url"] === 'applovin-quality-service'
            }
        })
        if (!hasMax && formData.maxAdReview) {
            gradleContent += `apply plugin: 'applovin-quality-service'\n`
        }
    }
    gradleContent += `def jsonFile = file('../configs/app.json')\n`+
        `def resultVersionCode = 1\n`+
        `def resultVersionName = "1.0.0"\n`+
        `def sdk_ver = "5.3.12"\n`+
        `def pkgName = "com.ivy.test"\n`+
        `def additionalMetas = new HashMap()\n`+
        `def keystore = null\n`+
        `def maxAdReview = ""\n`+
        `\n`+
        `if (jsonFile.exists()) {\n`+
        `    def appJson = new groovy.json.JsonSlurper().parseText(jsonFile.text)\n`+
        `    resultVersionCode = appJson.app.versionCode\n`+
        `    resultVersionName = appJson.app.versionName\n`+
        `    sdk_ver = appJson.build_config.sdk_ver\n`+
        `    pkgName = appJson.app.packageName\n`+
        `    additionalMetas = appJson.metadata\n`+
        `    keystore = appJson.keystore\n`+
        `    ${formData.maxAdReview ? 'maxAdReview = appJson.build_config.maxAdReview' : ''}\n`+
        `}\n`+
        `\n`+
        `${formData.maxAdReview ? 'applovin {\n' +
            '\tapiKey maxAdReview\n' +
            '}' : ''}\n`+
        `\n`+
        `android {\n`+
        `    compileSdkVersion ${formData.appCompileVer}\n`+
        `\n`+
        `    signingConfigs {\n`+
        `        debug {\n`+
        `            storeFile file('../configs/' + keystore.name + '.keystore')\n`+
        `            storePassword keystore.pass\n`+
        `            keyAlias keystore.alias\n`+
        `            keyPassword keystore.keypass\n`+
        `        }\n`+
        `\n`+
        `        release {\n`+
        `            storeFile file('../configs/' + keystore.name + '.keystore')\n`+
        `            storePassword keystore.pass\n`+
        `            keyAlias keystore.alias\n`+
        `            keyPassword keystore.keypass\n`+
        `        }\n`+
        `    }\n`+
        `\n`+
        `    defaultConfig {\n`+
        `        applicationId pkgName\n`+
        `\n`+
        `        targetSdkVersion ${formData.appTargetVer}\n`+
        `        minSdkVersion ${formData.appMinVer}\n`+
        `\n`+
        `        versionCode resultVersionCode\n`+
        `        versionName resultVersionName\n`+
        `\n`+
        `        multiDexEnabled ${formData.multiDex}\n`+
        `\n`+
        `       additionalMetas.each { key, val ->\n`+
        `            manifestPlaceholders.putAt(key, val)\n`+
        `       }\n`+
        `\n`+
        `${ndkOptions}\n`+
        `   }\n`+
        `${lintOptions}\n`+
        `${aaptOptions}\n`+
        `${buildFeatures}\n`+
        `${packagingOptions}\n`+
        `${compileOptions}\n\n`+
        `    namespace pkgName\n\n`+
        `    ${sourceSetsContent ? 'sourceSets.main {\n'+
            sourceSetsContent +
            '\t}' : ''}\n\n`+
        `    buildTypes {\n`+
        `        release {\n`+
        `            minifyEnabled ${formData.releaseMinifyEnabled}\n`+
        `${releaseShrink}`+
        `            signingConfig signingConfigs.release\n`+
        `            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'${formData.packType === 'unity' ? ', \'proguard-unity.txt\'' : ''}\n`+
        `        }\n\n`+
        `        debug {\n`+
        `${buildTypeDebug}`+
        `        }\n`+
        `    }\n`+

        `    android.applicationVariants.all { variant ->\n`+
        `        variant.outputs.all {\n`+
        `            outputFileName = "\${applicationId}-\${variant.versionName}.apk"\n`+
        `        }\n`+
        `    }\n`+
        `${bundleOptions}` +
        `\n`+
        `}\n`+
        `${libOptions}`+
        `dependencies {\n`+
        `${dependencies}`+
        `}\n` +
        `${pluginEnd}`;

    return gradleContent
}

const formatSettingsGradle = () => {
    const buildMarven = getMavenContent()
    const projectMarven = getMavenContent()
    return `pluginManagement {` +
        `\n\trepositories {` +
        `${buildMarven}` +
        `\n\t}\n` +
        `\n}` +
        `\ninclude ':launcher', ':unityLibrary'` +
        `\ninclude ':UnityDataAssetPack'` +
        `\ndependencyResolutionManagement {` +
        `\n\trepositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)` +
        `\n\trepositories {` +
        `${projectMarven}` +
        `\n\t\tflatDir {` +
        `\n\t\tdirs "\${project(':unityLibrary').projectDir}/libs"` +
        `\n\t\t}` +
        `\n\t}` +
        `\n}`
}

const getMavenContent = () => {
    let maven = '';
    let commonUrl = ['mavenCentral()','jcenter()', 'google()', 'gradlePluginPortal()'];
    commonUrl.forEach(v => {
        maven += `\n\t\t${v}`
    })

    formData.repository.forEach(v => {
        if (v.startsWith('https://')) {
            maven += `\n\t\tmaven {\n\t\t\turl '${v}'\n\t\t}`
        } else if (v.startsWith('http://')) {
            maven += `\n\t\tmaven {\n\t\t\tallowInsecureProtocol true\n\t\t\turl '${v}'\n\t\t}`
        } else if (v.startsWith('maven')) {
            maven += `\n\t\t${v}`
        }
    })

    formData.mavenUrl.forEach(v => {
        if (v.url.startsWith('https://')) {
            maven += `\n\t\tmaven {\n\t\t\turl '${v.url}'\n\t\t}`
        } else if (v.url.startsWith('http://')) {
            maven += `\n\t\tmaven {\n\t\t\tallowInsecureProtocol true\n\t\t\turl '${v.url}'\n\t\t}`
        } else if (v.url.startsWith('maven')) {
          maven += `\n\t\t${v.url}`
        }
    })

    return maven
}

const refreshConfig = () => {
    if (!formData.appId) {
        Message.warning("请选择包名")
        return true
    }

    loadPackInfo()
}

const saveConfig = () => {
    if (saveConfigLoading.value) {
        Message.warning("当前有正在保存的任务，请稍后再试")
        return true
    }

    if (!formData.appId) {
        Message.warning("请选择包名")
        return true
    }

    let isValid = false
    formRef.value.validate((errors: any) => {
        isValid = !errors;
    }).then(() => {
        if (!isValid) {
            Message.warning("请检查错误")
            return
        }

        if (!formData.metaConfigs.length) {
            Message.warning("检测到未配置metadata，请配置")
            return
        }

        saveConfigLoading.value = true
        sendSave()
    })
}

const sendSave = () => {
    let saveData = getPackConfigSaveFormat()
    savePackConfig(saveData).then(res => {
        if (saveConfigLoading.value) {
            Message.success('保存成功')
        }
    }).catch(e => {
        if (saveConfigLoading.value) {
            Message.error('保存失败', e)
        }
    }).finally(() => {
        if (saveConfigLoading.value) {
            saveConfigLoading.value = false
        }
    })
}

const getPackConfigSaveFormat = () => {
    return {
        appId: formData.appId,
        dataVersion: formData.dataVersion,
        packType: formData.packType,
        packMode: formData.packMode,
        cpuLibs: formData.cpuLibs,
        gitOpts: {
            branch: formData.branch,
            url: formData.url
        },
        appOpts: {
            packageName: formData.package,
            name: formData.name,
            versionCode: formData.versionCode,
            versionName: formData.versionName,
            versionNameAuto: formData.versionNameAuto
        },
        pluginOpts: {
            ndkVersion: formData.ndkVersion,
            gradleVersion: formData.gradleVersion,
        },
        buildOpts: {
            sdkVer: formData.sdkVer,
            keystoreVerify: formData.keystoreVerify,
            keystoreVerifyLog: formData.keystoreVerifyLog,
            adaptiveIcon: formData.adaptiveIcon,
            resguard: formData.resguard,
            encryptDefaultJson: formData.encryptDefaultJson,
            encryptMode: formData.encryptMode,
            sha1: formData.sha1,
            maxAdReview: formData.maxAdReview,
            exportProject: formData.exportProject,
            adMediation: formData.adMediation,
            adPlatform: formData.adPlatform,
            sdkDependencies: formData.sdkDependencies
        },
        metadataOpts: formData.metaConfigs,
        removePermissionOpts: formData.removePermissions,
        names: formData.names,
        keystoreOpts: {
            name: formData.keystoreName,
            pass: formData.keystorePass,
            alias: formData.keystoreAlias,
            keypass: formData.keystoreKeyPass
        },
        mavenUrl: formData.mavenUrl,
        globalDependencies: formData.globalDependencies,
        globalPlugin: formData.globalPlugin,
        globalBuildFeatures: formData.globalBuildFeatures,
        globalAaptOpts: formData.globalAaptOpts,
        globalNdkOpts: formData.globalNdkOpts,
        globalPackagingOpts: formData.globalPackagingOpts,
        globalLintOpts: formData.globalLintOpts,
        globalCompileOpts: formData.globalCompileOpts,
        globalBuild: {
            compileVer: formData.globalCompileVer,
            minVer: formData.globalMinVer,
            targetVer: formData.globalTargetVer,
            namespace: formData.globalNamespace
        },
        appPlugin: formData.appPlugin,
        appDependencies: formData.appDependencies,
        appBuildFeatures: formData.appBuildFeatures,
        appAaptOpts: formData.appAaptOpts,
        appNdkOpts: formData.appNdkOpts,
        appPackagingOpts: formData.appPackagingOpts,
        appLintOpts: formData.appLintOpts,
        appCompileOpts: formData.appCompileOpts,
        appBuild: {
            compileVer: formData.appCompileVer,
            minVer: formData.appMinVer,
            targetVer: formData.appTargetVer,
            multiDex: formData.multiDex,
            debugMinifyEnabled: formData.debugMinifyEnabled,
            releaseMinifyEnabled: formData.releaseMinifyEnabled,
            releaseShrinkResources: formData.releaseShrinkResources,
        },
        appSourcePath: {
            aidl: formData.aidl,
            java: formData.java,
            jniLibs: formData.jniLibs,
            assets: formData.assets
        },
        bundleOpts: formData.bundleOpts,
        rootPlugins: formData.rootPlugins,
        filesUploaded: {
            ksUploaded: formData.ksUploaded,
            gsUploaded: formData.gsUploaded,
            iconUploaded: formData.iconUploaded
        }
    }
}

const backupOperate = (operate_type: number, removeDir: string = '') => {
    if (operate_type == 1) {
        if (!formData.url) {
            Message.warning('请填写项目地址')
            return
        }
        if (!formData.versionName) {
            Message.warning('请填写版本号')
            return
        }
    }

    if (operate_type == 3 && removeDir == '') {
        Message.warning('删除的备份为空')
        return
    }


    if (operate_type == 1) {
        backupLoading.value = true;
    } else if (operate_type == 3) {
        deleteBackup.value.push(removeDir)
    }
    sendMessage(JSON.stringify({
        method: "backup",
        osName: osName,
        payload: {
            action: operate_type,
            name: formData.appId,
            url: formData.url,
            branch: formData.branch,
            versionName: formData.versionName,
            removeDir: removeDir
        }
    }))
}

const sendMessage = (data: any) => {
    emits('sendMessage', data)
}

const updateLoading = (keyName: string, value: boolean) => {
    emits('updateLoading', keyName, value)
}

const uploadPackFile = (upload_type: string) => {
    sendMessage(JSON.stringify({
        method: "uploadServer",
        osName: osName,
        payload: {}
    }))

    uploadFileModal.show = true
    uploadFileModal.type = upload_type
    switch (upload_type) {
        case 'keystore':
            uploadFileModal.title = '上传或生成keystore'
            uploadFileModal.accept = '.keystore'
            uploadFileModal.method = 'upload_keystore'
            uploadFileModal.tips = '请上传与签名名称相同的keystore文件'
            break
        case 'encryption_public_key':
            uploadFileModal.title = '上传encryption_public_key.pem'
            uploadFileModal.method = 'upload_encryption_public_key'
            uploadFileModal.accept = '.pem'
            uploadFileModal.tips = ''
            break
        case 'google_services':
            uploadFileModal.title = '上传google-services.json'
            uploadFileModal.method = 'upload_google_services'
            uploadFileModal.accept = '.json'
            uploadFileModal.tips = ''
            break
        case 'icon':
            uploadFileModal.title = '上传icon'
            uploadFileModal.method = 'upload_icon'
            uploadFileModal.accept = '.zip'
            uploadFileModal.tips = '请将icon相关图标压缩到zip文件后上传'
            break
    }
}

const cancelUpload = () => {
    uploadFileModal.show = false
    uploadFileModal.type = ''
    uploadFileModal.title = ''
    uploadFileModal.method = ''
    uploadFileModal.accept = ''
    uploadFileModal.tips = ''
}

const showContentModal = (show_type: string) => {
    contentShowModal.show = true
    contentShowModal.type = show_type
    contentShowModal.loading = true
    switch (show_type) {
        case 'keystore':
            contentShowModal.title = '【' + formData.keystoreName + '】的详情'
            getKeystoreInfo()
            break
        case 'google_services':
            contentShowModal.title = '【google-services.json】的详情'
            getGoogleServices()
            break
    }
}

const hideContentShow = () => {
    contentShowModal.show = false
    contentShowModal.loading = false
    contentShowModal.type = ''
    contentShowModal.title = ''
    contentShowModal.content = ''
}

const copyText = (content: string) => {
    let inputElement = document.createElement('input')
    inputElement.value = content;
    document.body.appendChild(inputElement);
    inputElement .select(); //选中文本
    document.execCommand("copy"); //执行浏览器复制命令
    inputElement.remove();
    Message.success("复制成功");
}
const getHistoryLog = (pack_result: number, current: number = 1, page_size: number = 10) => {
    if (pack_result == 1) {
        completeList.loading = true
    } else {
        failList.loading = true
    }
    getPackHistoryList({app_id: formData.appId, current: current, page_size: page_size, pack_result: pack_result}).then(res => {
        if (pack_result == 1) {
            completeList.total = res.total
            completeList.list = []
            res.items.forEach(v => {
                completeList.list.push({
                    id: v.id,
                    time: formatTimestamp(v.createTime),
                    name: `[${v.packType} - ${v.appType}] ${v.appId}:${v.appOpts.packageName}:${v.appOpts.versionName}`,
                    status: '成功',
                    resultType: v.appType,
                    buildType: v.packType,
                    buildVersion: v.dataVersion,
                    appId: v.appId,
                    packageName: v.appOpts.packageName,
                    versionName: v.appOpts.versionName,
                    creator: v.creator,
                    sdkVersion: v.buildOpts.sdkVer
                })
            })
        } else {
            failList.total = res.total;
            failList.list = []
            res.items.forEach(v => {
                failList.list.push({
                    id: v.id,
                    time: formatTimestamp(v.createTime),
                    name: `[${v.packType} - ${v.appType}] ${v.appId}:${v.appOpts.packageName}:${v.appOpts.versionName}`,
                    status: '失败',
                    resultType: v.appType,
                    buildType: v.packType,
                    buildVersion: v.dataVersion,
                    appId: v.appId,
                    packageName: v.appOpts.packageName,
                    versionName: v.appOpts.versionName,
                    creator: v.creator,
                    sdkVersion: v.buildOpts.sdkVer
                })
            })
        }
    }).catch(e => {
        console.log(e)
    }).finally(() => {
        if (pack_result == 1) {
            completeList.loading = false
        } else {
            failList.loading = false
        }
    })
}

const taskTabChange = (tab: string|number) => {
    if (tab === 'completed') {
        getHistoryLog(1)
    } else if (tab === 'failed') {
        getHistoryLog(2)
    }
}

const closeDraw = () => {
    loadDataVersion(formData.appId)
    drawerTab.value = ''
}

const delIcon = (img_name: string) => {
  sendMessage(JSON.stringify({
    method: "delIcon",
    osName: osName,
    payload: {
      appId: formData.appId,
      imgName: img_name
    }
  }))
}

const handleMessage = (message) => {
    switch (message.method) {
        case 'uploadServer':
            uploadServer.value = message.payload ? message.payload.server : ''
            break
        case 'message_error':
            if (message.payload && message.payload.notify) {
                Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
            }

            break
        case 'delIcon':
          if (message.payload) {
            if (message.payload.notify) {
              Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
            } else {
              Message.success(message.payload.imgName + "删除成功")
              iconUrlsContent.value = iconUrlsContent.value.filter(item => item.name != message.payload.imgName)
              if (!iconUrlsContent.value.length) {
                formData.iconUploaded = 0
              }
            }
          }
          break
        case 'taskList':
            runningList.value = []
            if (message.payload.tasks.running) {
                message.payload.tasks.running.forEach(s => {
                    runningList.value.push({
                        id: s.logName,
                        time: s.createTime,
                        name: `[${s.buildType} - ${s.resultType}] ${s.appId}:${s.packageName}:${s.versionName}`,
                        status: "打包中",
                        packageName: s.packageName,
                        appId: s.appId,
                        log: s.log,
                        sdkVersion: s.sdkVersion,
                        buildVersion: s.jsonVersion,
                        creator: s.creator
                    })
                })
            }

            waitingList.value = []
            if (message.payload.tasks) {
                message.payload.tasks.waiting.forEach(s => {
                    let resultType = s.isBuildAab ? 'aab' : 'apk'
                    waitingList.value.push({
                        id: s.taskId,
                        time: s.createTime,
                        name: `[${s.buildType} - ${resultType}] ${s.appId}:${s.packageName}:${s.versionName}`,
                        status: "排队中",
                        packageName: s.packageName,
                        appId: s.appId,
                        sdkVersion: s.sdkVersion,
                        buildVersion: s.jsonVersion,
                        creator: s.creator
                    });
                });
            }
            break
        case "backup":
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.action == 1) {
                        backupLoading.value = false;
                    }
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    backupList.value = message.payload.list;
                    backupList.value.reverse()
                    if (message.payload.action == 1) {
                        backupLoading.value = false;
                        Message.success("成功添加备份");
                    } else if (message.payload.action == 3) {
                        let i = deleteBackup.value.indexOf(message.payload.removeDir)
                        if (i > -1) {
                            deleteBackup.value.splice(i, 1)
                        }

                        Message.success("成功删除备份");
                    }
                }
            }
            break
        case 'getKeystoreInfo':
            contentShowModal.loading = false
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.notify.type == 'upload_keystore') {
                        uploadPackFile('keystore')
                    } else {
                        Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                        if (contentShowModal.type === 'keystore') {
                            contentShowModal.content = '获取失败'
                        }
                    }
                } else {
                    if (contentShowModal.type === 'keystore') {
                        contentShowModal.content = message.payload.value
                    }
                }
            }
            break
        case 'getGoogleServices':
            contentShowModal.loading = false
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.notify.type == 'upload_google_services') {
                        uploadPackFile('google_services')
                    } else {
                        Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                        if (contentShowModal.type === 'google_services') {
                            contentShowModal.content = '获取失败'
                        }
                    }
                } else {
                    if (contentShowModal.type === 'google_services') {
                        contentShowModal.content = message.payload.value
                    }
                }
            }
            break
        case 'exportKeystore':
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.notify.type == 'upload_keystore') {
                        uploadPackFile('keystore')
                    } else if (message.payload.notify.type == 'encryption_public_key') {
                        uploadPackFile('encryption_public_key')
                    } else {
                        Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    }
                    if (message.payload.isNew) {
                        exportKeystoreNewLoading.value = false
                    } else {
                        exportKeystoreOldLoading.value = false
                    }
                } else {
                    if (message.payload.isNew) {
                        exportKeystoreNewLoading.value = false
                    } else {
                        exportKeystoreOldLoading.value = false
                    }
                }
            }
            break
        case 'generateKeystore':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("生成成功")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.ksUploaded = 1
                    sendSave()
                }
            }
            break
        case 'UploadIcon':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.iconUploaded = 1
                    sendSave()
                    loadIconContents()
                }
            }
            break
        case 'UploadGoogleServices':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.gsUploaded = 1
                    sendSave()
                }
            }
            break
        case 'UploadKeystore':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.ksUploaded = 1
                    sendSave()
                }
            }
            break
        case 'UploadEncryptionPublicKey':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成，开始导出zip")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    exportKeystore(null, true)
                }
            }
            break
        case 'buildApk':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    updateLoading('aabPackLoading', false)
                    updateLoading('aabPackLoading', false)
                } else {
                    if (message.payload.action) {
                        if (message.payload.action === "build_result") {
                            const task = message.payload.buildType + ":" + message.payload.packageName;
                            formData.versionCode = message.payload.versionCode
                            formData.versionName = message.payload.versionName
                            if (message.payload.buildResult === "success") {
                                packResult.value.push("你的" + task + "打包成功")
                            } else {
                                if (message.payload.reason === '任务已取消') {
                                    packResult.value.push("你的" + task + "打包失败: "+ message.payload.reason)
                                } else {
                                    packResult.value.push("你的" + task + "打包失败: "+ message.payload.reason +", 请查看日志排查错误")
                                }
                            }
                            getHistoryLog(1)
                            getHistoryLog(2)
                        } else if (message.payload.action === "add_task") {
                            if (message.payload.isBuildAab) {
                                aabPackLoading.value = false
                                updateLoading('aabPackLoading', false)
                            } else {
                                apkPackLoading.value = false
                                updateLoading('apkPackLoading', false)
                            }
                            Message.success(message.payload.message)
                        }
                    }
                }
            }
            break
        case 'getCompleteLog':
            let logStr = 'error log of this build task maybe have been delete!'
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    logStr = message.payload.notify.message ? message.payload.notify.message : logStr
                } else {
                    logStr = message.payload.error ? message.payload.error : logStr
                }
            }
            completeLogRef.value.setLogContent(logStr)
            break;
        case 'downloadPackage':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                }
            }
            break
        case 'modifyJson':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("app.json保存成功")
                }
            }
            appConfigSaved.value = true
            if (globalGradleSaved.value) {
                saveConfigLoading.value = false
            }
            break
        case 'modifyGradle':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("gradle保存成功")
                }
            }
            globalGradleSaved.value = true
            if (appConfigSaved.value) {
                saveConfigLoading.value = false
            }
            break
        case 'cancelTask':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("任务取消成功")
                }
            }
            break
        case 'checkProjectStruct':
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.notify.type == 'check_file_struct') {
                        confirmModal.show = true
                        confirmModal.loading = false
                        confirmModal.title = '警告'
                        confirmModal.content = '检测到项目结构不符合，如需要打包需调整目录结构，是否调整？'
                        confirmModal.cancelHandle = () => {
                            confirmModal.show = false
                            if (message.payload.payload['isBuildAab']) {
                                aabPackLoading.value = false
                                updateLoading('aabPackLoading', false)
                            } else {
                                apkPackLoading.value = false
                                updateLoading('apkPackLoading', false)
                            }
                            Message.error('打包任务添加失败')
                        }
                        confirmModal.okHandle = () => {
                            confirmModal.loading = true
                            sendMessage(JSON.stringify({
                                method: 'changeProjectStruct',
                                osName: osName,
                                payload: message.payload.payload
                            }))
                        }
                    } else {
                        Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    }
                } else {
                    checkProjectLoading.value = false
                    Message.success("项目结构检查通过，开始打包")
                    projectChecked.value = true
                    sendPack(message.payload.payload)
                }
            }
            break
        case 'changeProjectStruct':
            if (message.payload) {
                if (message.payload.notify) {
                    confirmModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("项目结构调整完成，开始打包")
                    confirmModal.show = false
                    projectChecked.value = true
                    const payload = message.payload.payload
                    payload['saveData']['gitOpts']['url'] = message.payload.url
                    payload['appJson']['git']['url'] = message.payload.url
                    if (payload['appId'] === formData.appId) {
                        formData.url = message.payload.url
                    }

                    sendPack(payload)
                }
            }
            break
        case 'getSdkVerList':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    sdkOptions.value = JSON.parse(message.payload.sdkVerList)
                    formData.repository = JSON.parse(message.payload.repository)

                    sdkContent.value = {}
                    if (formData.sdkVer && sdkOptions.value && sdkOptions.value.includes(formData.sdkVer)) {
                        changeSdk(formData.sdkVer)
                    }
                }
            }
            sdkLoading.value = false
            break
        case 'getSdkContent':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    sdkContent.value = JSON.parse(message.payload.sdkContent)
                    console.log(sdkContent.value)
                }
            }
            adPlatformLoading.value = false
            break
        case 'getIconUrlContent':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    iconLoading.value = false
                } else {
                    if (message.payload.imageReqId === imagesReqId.value) {
                        iconUrlsContent.value.push(message.payload.imageUrlContent)

                        if (iconUrlsContent.value.length === message.payload.imagesTotal) {
                            iconLoading.value = false
                        }
                    }
                }
            }
            break
    }
}


defineExpose({
    refreshPage,
    handleMessage,
    buildApk
})
</script>

<style scoped lang="less">
.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
}

.cross-content {
    width: 100%;
    height: 100%;
    display: flex;

    .minus-btn{
        width: 15px;
        height: 15px;
        margin-top: calc(50% - 8px);
    }

    .group-box {
        min-width: 160px;
        height: 100%;
        border-right: 1px solid var(--color-secondary);
        overflow-x: hidden;
        overflow-y: auto;

        :deep(.arco-anchor-link-item) {
            .arco-anchor-link{
                font-size: 16px;
                padding: 5px 8px;
            }
        }
    }

    .active {

    }

    .group-detail {
        padding: 0 16px;
        height: 100%;
        width: 100%;

        .group-title {
            :deep(.arco-form-item-label) {
                font-size: 14px;
                font-weight: 600;
            }
        }

        .remark {
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: var(--tant-border-radius-medium);
            padding: 10px 15px;
            position: relative;
            margin-top: 15px;
            margin-bottom: 8px;
            width: 100%;
            background-color: var(--tant-fill-color-fill1-2);

            .btn-icon {
                position: absolute;
                right: 12px;
                top: 8px;
                cursor: pointer;
            }
        }

        .sdkDependencies {
            :deep(.arco-form-item-content-flex) {
                display: block;

                .arco-checkbox-group {
                    display: block;
                }
                .arco-checkbox-group-direction-vertical .arco-checkbox{
                    width: fit-content;
                }
            }
        }

        .section-area {
            font-size: 18px;
            font-weight: 400;
            padding-bottom: 15px
        }

        .maven-url {
            width: 100%;
            :deep(.arco-space-item) {
                width: 100%;
            }
        }

        :deep(.arco-affix) {
            z-index: 100;
        }
        :deep(.arco-transfer-view) {
            width: 270px;
            height: 330px;
        }

        :deep(#iconUploaded) {
            .arco-form-item-content{
                display: block;
            }
        }
    }

    :deep(#adPlatform) {
        .arco-form-item-content-flex {
            display: block;
        }
        .arco-checkbox-group {
            line-height: 2;
            .arco-checkbox{
                width: 170px;
            }
        }
    }
}
.sider-btn {
    position: fixed;
    top: 145px;
    right: 0;
    z-index: 300;
    cursor: pointer;

    :deep(.arco-list-item) {
        padding: 10px 0 !important;
    }

    .sider-btn-item{
        writing-mode: vertical-rl;
    }

    .side-btn-active{
        background-color: rgb(143, 184, 243) !important;
        color: #fff !important;
    }
}
.create-condition{
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    color: rgb(var(--primary-6));
    font-size: 14px;
    line-height: 36px;
    text-align: left;
    background-color: var(--color-bg-popup);
    cursor: pointer;
}
.icon-del-btn{
  display: none;
  width: 100%;
  padding: 10px;
  cursor: pointer;
  position: absolute;
  text-align: end;
  color: #fff;
  background: linear-gradient(to top, transparent, #454545d1);
  top: 12px;
}
.icon-box{
  text-align: center;
  position: relative;
}
.icon-box:hover .icon-del-btn {
  display: block;
}
</style>
