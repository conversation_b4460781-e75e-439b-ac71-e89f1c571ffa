<script setup lang="ts">

interface Props {
  /**
   * 数值
   */
  title: string

  /**
   * 数值
   */
  total: number | undefined

  /**
   * 环比值
   */
  diff: number | undefined

  /**
   * 环比
   */
  diffRate: number | undefined
}

const props = defineProps<Props>()

</script>

<template>
  <div class="small-report-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ title }}
      </div>
    </div>
    <a-spin v-show="total===undefined" style="width: 100%; height: 60%; display: flex; justify-content: center; align-items: center"/>
    <div v-show="total!==undefined" class="report-card-content">
      <div class="primary-data">
        {{ total?.toLocaleString() }}
      </div>
      <div class="secondary-data">
        <div v-if="diff ===0">
          -- ( -- %)
        </div>
        <div v-if="diff && diff >=0" class="up">
          ↑ +{{ diff.toLocaleString() }} (+{{ diffRate }}%)
        </div>
        <div v-if="diff && diff < 0" class="down">
          ↓ {{ diff.toLocaleString() }} ({{ diffRate }}%)
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@import './style.less';
</style>