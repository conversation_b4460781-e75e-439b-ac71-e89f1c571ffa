<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
                <a-select
                  v-model="filterParams.adIds"
                  placeholder="请选择广告单元"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adUnitOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdUnitSearch"
                  @dropdown-reach-bottom="loadMoreAdUnits"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adUnitOptions" 
                    :key="item.adUnitId" 
                    :value="item.adUnitId" 
                    :label="item.adUnitName">
                    {{ item.adUnitName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.adIds"
                  placeholder="请选择广告"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdSearch"
                  @dropdown-reach-bottom="loadMoreAds"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adOptions" 
                    :key="item.adId" 
                    :value="item.adId" 
                    :label="item.adName">
                    {{ item.adName }}
                  </a-option>
              </a-select>
              <a-select
                  placeholder="请选择产品"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>产品-{{ data?.label }}</span>
                  </template>
              </a-select>
              <a-select
                  placeholder="请选择状态"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>状态-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">投放中</a-option>
                  <a-option value="2">已暂停</a-option>
                  <a-option value="3">已删除</a-option>
                  <a-option value="4">超预算</a-option>
                  <a-option value="5">审核中</a-option>
                  <a-option value="6">审核不通过</a-option>
                  <a-option value="7">其他状态</a-option>
              </a-select>
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption :disabled="!selectedKeys.length" @click="batchOpen">开启</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchPause">暂停</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchTime">定时开关</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchReviseBudget">修改预算</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="linkCreativeBatch">查看创意</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="linkSubChannelBatch">查看子渠道</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchRelated">关联产品</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditTag">编辑标签</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="changeTotalBid">修改总出价</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="changeAreaBid">批量修改分地区出价</a-doption>
                    <a-doption @click="excelBidEdit">Excel预算编辑</a-doption>
                    <a-doption @click="excelBlockEdit">Excel屏蔽子渠道</a-doption>
                </template>
            </a-dropdown>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-tooltip placement="top" content="可手动同步近3天(含今天)的数据，其他时段系统将定期自动同步">
                <a-button class="br4" style="margin-left: 12px;" @click="batchSync">
                    <template #icon>
                        <icon-sync />
                    </template>
                    <template #default>同步数据</template>
                </a-button>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="adUnitId"
        >
          <template #effectiveStatus="{ record }">
            <a-switch v-model="record.effectiveStatus" checked-value="ACTIVE" unchecked-value="PAUSED" size="small" :before-change="() => statusChange(record)"/>
          </template>
          <template #accountStatus="{ record }">
              <a-badge :status="record.accountStatus === 1 ? 'success' : 'normal'" :text="record.accountStatus === 1 ? 'ACTIVE' : 'DISABLED'"/>
          </template>
           <template #status="{ record }">
              <a-badge :status="record.status === 'ACTIVE' ? 'success' : 'normal'" :text="record.status"/>
          </template>
          <template #actions="{record}">
            <div class="actions-content">
              <a class="actions-btn">详情</a>
              <a class="actions-btn">编辑创意</a>
              <a class="actions-btn">复制</a>
              <a-dropdown>
                <a class="actions-btn">更多<icon-down style="font-size: 12px;"/></a>
                <template #content>
                  <a-doption>广告日志</a-doption>
                  <a-doption>同步数据</a-doption>
                </template>
              </a-dropdown>
            </div>
          </template>
          <template #iconPic="{ record }">
            <img src="https://xmp-api.mobvista.com/mediacenter/api/cdn?url=http%3A%2F%2Fcdn-adn.rayjump.com%2Fcdn-adn%2Fv2%2Fportal%2F24%2F11%2F26%2F10%2F26%2F674531bb5e448.png&sign=5c4928d59ae0816422e57a3530bcad20&c_id=3003" style="width: 40px;height: 40px;"/>
          </template>
          <template #adUnitName="{ record,rowIndex }">
            <div class="cell-content">
                <div class="text">{{ record.adUnitName }}</div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
            </div>
          </template>
          <template #editLog="{ record }">
            <a-tooltip v-for="(item, index) in record.editLog" :key="index">
              <template #content>
                <div class="edit-log-box">
                  <div class="edit-log-item">
                    <div class="label">时间</div>
                    <div class="info">{{ item.dateTime }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">时区</div>
                    <div class="info">{{ item.timezone }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号ID</div>
                    <div class="info">{{ item.channelUserName }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号</div>
                    <div class="info">{{ item.operationEmail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">操作类型</div>
                    <div class="info">{{ item.operationDetail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑前</div>
                    <div class="info">{{ item.operationBefore }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑后</div>
                    <div class="info">{{ item.operationAfter }}</div>
                  </div>
                </div>
              </template>
              <img 
                :src="formatEditImg(item)" 
                style="width: 20px;height: 20px;margin-right: 8px;"
              />
            </a-tooltip>
          </template>
          <template #createdTime="{ record }">
            {{ record.createdTime ? dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template #lifetimeBudget="{ record }">
            {{ !record.lifetimeBudget && !record.dailyBudget ? '使用Ad set预算' : record.dailyBudget }}
          </template>
          <template #dailyBudget="{ record }">
            {{ record.dailyBudget || '无预算' }}
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'effectiveStatus'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 批量同步数据 -->
      <BatchSyncData ref="syncRef"/>
      <!-- 定时开关 -->
      <TimeSwitch ref="timeRef"/>
      <!-- 批量编辑标签 -->
      <BatchEditTags ref="tagRef"/>
      <!-- 修改预算 -->
      <BatchReviseBudget ref="budgetRef"/>
      <!-- 关联产品 -->
      <RelatedProducts ref="productsRef"/>
      <!-- 批量修改总出价 -->
      <BatchChangeTotalBid ref="totalBidRef"/>
      <!-- 批量修改地区出价 -->
      <BatchChangeAreaBid ref="areaBidRef"/>
      <!-- excel批量编辑 -->
      <ExcelBidEdit ref="excelRef"/>
      <!-- excel批量屏蔽子渠道 -->
      <ExcelBlockSub ref="excelBlockRef"/>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject, watch } from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import BatchSyncData from "@/views/launch/promotion/components/BatchSyncData.vue";
import TimeSwitch from "@/views/launch/promotion/components/TimeSwitch.vue";
import {mintegralDrillList} from "@/views/launch/promotion/components/promotionData"
import BatchEditTags from "@/views/launch/promotion/components/BatchEditTags.vue";
import RelatedProducts from "@/views/launch/promotion/components/RelatedProducts.vue";
import BatchReviseBudget from "@/views/launch/promotion/components/BatchAllReviseBudget.vue";
import DrillDrawer from "../components/DrillDrawer.vue";
import BatchChangeTotalBid from "../components/ChangeTotalBid.vue";
import BatchChangeAreaBid from "../components/ChangeAreaBid.vue";
import ExcelBidEdit from "../components/ExcelBidEdit.vue";
import ExcelBlockSub from "../components/ExcelBlockSub.vue";

// 设置数据
const filterParams = reactive({
  adIds:[]
})
const emits = defineEmits(['changeTabs'])
const dateTime = ref(inject('dateTime') as any[])
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
// 模拟table数据
const loading = ref(false)
const dropdownList = mintegralDrillList.adUnit
const tableData = ref<any>([])
const noSumColumns = ref([
    'iconPic',
    'adUnitName',
    'adId',
    'actions',
    'status',
    'editLog',
    'createdTime',
    'formatCountries',
    'mediaStatus',
    'lifetimeBudget',
    'dailyBudget',
    'bid',
])
const columns = ref<any>([
  { title: '', dataIndex: 'effectiveStatus',slotName:'effectiveStatus',width:100,fixed:'left' },
  { title: '图标', dataIndex: 'iconPic',slotName:'iconPic',width:60,fixed:'left',align:'center' },
  { title: '广告单元名称', dataIndex: 'adUnitName',slotName:'adUnitName',width:250,fixed:'left', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '操作', dataIndex: 'actions',slotName:'actions',width:260,fixed:'left',align:'center' },
  { title: '地区名称', dataIndex: 'formatCountries',minWidth:180,slotName:'formatCountries',tooltip:true,ellipsis:true },
  { title: '媒体状态', dataIndex: 'mediaStatus',minWidth:180,slotName:'mediaStatus' },
  { title: '预算', dataIndex: 'lifetimeBudget',slotName:'lifetimeBudget',minWidth:180 },
  { title: '日预算消耗', dataIndex: 'dailyBudget',slotName:'dailyBudget',minWidth:180 },
  { title: '出价', dataIndex: 'bid',slotName:'bid',minWidth:180 },
  { title: '创建时间', dataIndex: 'createdTime',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '最近操作', dataIndex: 'editLog',slotName:'editLog',minWidth:180 },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])
const adOptions = ref<any>([])
const adOptionsLoading = ref(false);
const adPagination = reactive({
  current: 1,
  pageSize: 20,
});
const adUnitOptions = ref<any>([])
const adUnitOptionsLoading = ref(false);
const adUnitPagination = reactive({
  current: 1,
  pageSize: 20,
});
// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const syncRef = ref()
const batchSync = () => {
  syncRef.value.openModal()
}
const formatEditImg = (val) => {
  const editImgMap = {
    "EDIT_BUDGET":"/icon/launch/operation-budget.svg",
    "EDIT_BID":"/icon/launch/operation-bid.svg",
    "EDIT_ON":"/icon/launch/operation-on.svg",
  }
  return editImgMap[val.operationType]
}
const confirmVisible = ref(false)
const confirmType = ref('')
const statusChange = (record) => {
  confirmType.value = record.authStatus ? 'pause' : 'open'
  confirmVisible.value = true
  return new Promise((resolve, reject) => {
    if(confirmVisible.value){
      resolve(false);
    }
    resolve(true);
  });
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'ad',value:val,recordData:record})
}

// 开启
const batchOpen = () => {
  confirmVisible.value = true
  confirmType.value = 'open'
}
// 暂停
const batchPause = () => {
  confirmVisible.value = true
  confirmType.value = 'pause'
}
// 定时开关
const timeRef = ref()
const batchTime = () => {
  timeRef.value.openModal()
}
// 批量编辑标签
const tagRef = ref()
const batchEditTag = () => {
  tagRef.value.openModal('ad')
}
// 批量修改预算
const budgetRef = ref()
const batchReviseBudget = () => {
  budgetRef.value.openModal()
}
// 查看创意
const linkCreativeBatch = () => { 
  emits('changeTabs',{ids:selectedKeys.value,tab:'creative'})
}
// 查看子渠道
const linkSubChannelBatch = () => {
  emits('changeTabs',{ids:selectedKeys.value,tab:'subChannel'})
}
// 关联产品
const productsRef = ref()
const batchRelated = () => {
  productsRef.value.openModal()
}
// 批量修改总出价
const totalBidRef = ref()
const changeTotalBid = () => {
  totalBidRef.value.openModal()
}
// 批量修改地区出价
const areaBidRef = ref()
const changeAreaBid = () => {
  areaBidRef.value.openModal()
}
// excel出价编辑
const excelRef = ref()
const excelBidEdit = () => {
  excelRef.value.openModal('adUnit')
}
// excel屏蔽子渠道
const excelBlockRef = ref()
const excelBlockEdit = () => {
  excelBlockRef.value.openModal('adUnit')
}
// 加载广告选项
const loadAdOptions = async () => {
  adOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adPagination.pageSize,
      current:adPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adPagination.current === 1) {
      adOptions.value = items;
    } else {
      adOptions.value = [...adOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告选项失败:', error);
  } finally {
    adOptionsLoading.value = false;
  }
}
const handleAdSearch = (v) => {
}
const loadMoreAds = async () => {
  adPagination.current += 1;
  // 获取更多广告数据
  await loadAdOptions();
}
// 加载广告单元选项
const loadAdUnitOptions = async () => {
  adUnitOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'adUnit',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adUnitPagination.pageSize,
      current:adUnitPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adUnitPagination.current === 1) {
      adUnitOptions.value = items;
    } else {
      adUnitOptions.value = [...adUnitOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告单元选项失败:', error);
  } finally {
    adUnitOptionsLoading.value = false;
  }
}
const handleAdUnitSearch = (v) => {
}
const loadMoreAdUnits = async () => {
  adUnitPagination.current += 1;
  // 获取更多广告单元数据
  await loadAdUnitOptions();
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
}
const init = (filter?:any) => {
  getList()
}
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>
