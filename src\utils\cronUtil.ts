import cronExpressionParser from 'cron-parser';
import _ from "lodash";
import {formatTimestamp} from "@/utils/dateUtil";

/**
 * 时区映射
 */
export function offsetToIANA(offset: string): string | undefined {
  const map: Record<string, string> = {
    '-12:00': 'Etc/GMT+12',
    '-11:00': 'Pacific/Pago_Pago',
    '-10:00': 'Pacific/Honolulu',
    '-09:00': 'America/Anchorage',
    '-08:00': 'America/Los_Angeles',
    '-07:00': 'America/Denver',
    '-06:00': 'America/Chicago',
    '-05:00': 'America/New_York',
    '-04:00': 'America/Halifax',
    '-03:00': 'America/Argentina/Buenos_Aires',
    '-02:00': 'Etc/GMT+2',
    '-01:00': 'Atlantic/Azores',
    '+00:00': 'UTC',
    '+01:00': 'Europe/Berlin',
    '+02:00': 'Europe/Kiev',
    '+03:00': 'Europe/Moscow',
    '+03:30': 'Asia/Tehran',
    '+04:00': 'Asia/Dubai',
    '+04:30': 'Asia/Kabul',
    '+05:00': 'Asia/Karachi',
    '+05:30': 'Asia/Kolkata',
    '+05:45': 'Asia/Kathmandu',
    '+06:00': 'Asia/Dhaka',
    '+06:30': 'Asia/Yangon',
    '+07:00': 'Asia/Bangkok',
    '+08:00': 'Asia/Shanghai',
    '+08:45': 'Australia/Eucla',
    '+09:00': 'Asia/Tokyo',
    '+09:30': 'Australia/Darwin',
    '+10:00': 'Australia/Sydney',
    '+10:30': 'Australia/Lord_Howe',
    '+11:00': 'Pacific/Noumea',
    '+12:00': 'Pacific/Auckland',
    '+13:00': 'Pacific/Tongatapu',
    '+14:00': 'Pacific/Kiritimati',
  };

  return map[offset];
}


/**
 * 通过 CRON 表达式计算下一次执行时间
 * @param cronExpr - CRON 表达式，如 “5 * * * *”
 * @param fromDate - 可选，起始时间，默认当前时间
 * @returns 下一个执行时间（Date 对象），如果表达式无效则返回 null
 */
export function getNextExecutionTime(cronExpr: string,
                                     fromDate: Date = new Date(),
                                     offset?: string = '+08:00'): string | null {
  if (_.isEmpty(cronExpr)) {
    return null;
  }
  const options: any = {currentDate: fromDate};

  if (offset) {
    const tz = offsetToIANA(offset);
    if (tz) {
      options.tz = tz;
    } else {
      console.warn(`不支持的时区偏移: ${offset}，将使用本地时间`);
    }
  }

  try {
    const interval = cronExpressionParser.parse(cronExpr, options);
    return formatTimestamp(interval.next().getTime(), offset);
  } catch (err) {
    console.error('Invalid CRON expression:', cronExpr);
    return null;
  }
}
