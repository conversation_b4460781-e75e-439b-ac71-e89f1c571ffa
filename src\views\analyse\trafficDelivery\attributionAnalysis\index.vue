<template>
  <div ref="pageContainer" class="page">
    <div class="page-head-multiple-unfold">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <div class="edit-select">
            <a-select v-model:model-value="params.appView" :loading="loading" :style="{ width: '240px', borderRadius: '4px', marginLeft: '12px' }" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="viewChange">
              <template #label="{ data }">
                <span>视图-{{ data?.label }}</span>
              </template>
              <a-option v-for="item in appViewList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
            </a-select>
            <a-tooltip content="编辑">
              <icon-edit v-if="!loading" size="16" class="edit" @click="editIndicators" />
            </a-tooltip>
          </div>
        </div>
        <div class="filter-item">
          <select-app-list @change="refresh"/>
        </div>
        <div class="filter-item">
          <SelectedTeamList v-model:teams="params.teamCodes" @change="refresh" />
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick" />
        </div>
      </div>
    </div>
    <div class="sub-header">
      <div class="filter-line" style="border-radius: 0 0 9px 9px">
        <div class="filter-item">
          <a-select
            v-model:model-value="params.selectGroups"
            :style="{ borderRadius: '4px', width: '200px' }"
            :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
            placeholder="选择分组"
            multiple
            :max-tag-count="1"
            :tag-nowrap="true"
            :scrollbar="true"
            @clear="computedData"
            @remove="computedData"
            @popup-visible-change="groupChange"
          >
            <a-option v-for="item in groupList" :key="item.code" :value="item.code">{{ item.displayName }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <dateSet :time-particle="{ timeParticleSize: params.timeParticleSize, firstDayDfWeek: params.firstDayDfWeek }" :no-mh="true" @time-change="timeChange" />
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="computedData">
            <template #icon>
              <icon-refresh class="nav-icon" />
            </template>
          </a-button>
        </div>
        <div class="filter-item">
          <a-tooltip content="导出">
            <div class="operation-icon button-background" style="color: #fff; background-color: rgb(var(--primary-6))" @click="exportXlsx">
              <icon-download class="icon" />
            </div>
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="page-body">
      <div class="table-content">
        <a-table :loading="dataLoading" :columns="columns" :data="tableData" :pagination="pagination" size="small" column-resizable :filter-icon-align-left="true" style="white-space: nowrap" :scroll="{ y: '100%' }" @page-size-change="pageSizeChange" @filter-change="handleTableFilter">
          <template v-for="(_, index) in resultData?.groupsDesc" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
            <div class="custom-filter">
              <a-space direction="vertical" :size="8">
                <span>筛选：</span>
                <a-select
                  :model-value="filterValue"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :options="getFilterOptionsByIndex(index)"
                  :style="{ width: '200px' }"
                  :trigger-props="{
                    position: 'top',
                    autoFitPopupMinWidth: true,
                    updateAtScroll: true,
                  }"
                  @update:model-value="(val) => setFilterValue(val)"
                >
                </a-select>
                <div class="custom-filter-footer">
                  <a-button size="mini" @click="handleFilterReset">重置</a-button>
                  <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
                </div>
              </a-space>
            </div>
          </template>
          <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record }">
            {{ formatNumber(record[column.dataIndex], column.displayType) }}
            <span v-if="column.displayType?.type === 'percent'">%</span>
          </template>
          <template v-for="(column, index) in columns.filter((col) => col.code)" :key="`header-${index}`" #[`${column.dataIndex}-title`]>
            <IndicatorTooltip :indicator-code="column.code" :indicator-data="indicatorDetailsMap.get(column.code)">
              <span>{{ column.title }}</span>
            </IndicatorTooltip>
          </template>
        </a-table>
      </div>
    </div>
    <indicatorsSelect ref="selectRefs" page-type="attribution" :show-type="true" :filter-params="params" :app-view-data="handleViewData" :indicator-list="indicatorList" @update-view="updateView" @update-indicator="updateIndicator" />
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import DatePicker from '@/components/date-picker/index.vue';
  import { useEventBus, useSessionStorage } from '@vueuse/core';
  import { LocalStorageEventBus } from '@/types/event-bus';
  import { useRoute } from 'vue-router';
  import { analyseStore } from '@/store';
  import selectAppList from "@/components/selected-game-app-list/index.vue"
  import selectCountryList from '@/components/selected-country-list/index.vue';
  import dateSet from '@/views/analyse/components/dateSet.vue';
  import { Message } from '@arco-design/web-vue';
  import { createCustomSorter, getCustomStringLength } from '@/utils/strUtil';
  import { queryAttributionReportData,queryTrafficReportData, reportQueryResponseData } from '@/api/analyse/analyse';
  import { getAttributionViewDetail, getAttributionViews } from '@/api/analyse/api';
  import * as XLSX from 'xlsx';
  import { compressDateRangeToEndDate, getDateRangeEndDate, getDateRangeStartDate } from '@/utils/dateUtil';
  import indicatorsSelect from '@/views/analyse/appData/components/viewSelect/index.vue';
  import { isEqual } from 'lodash';
  import { TimeParticleSize } from '@/api/enum';
  import IndicatorTooltip from '@/components/indicator-tooltip/index.vue';
  import useIndicatorDetailHelper from '@/utils/indicator-detail-helper';
  import { usePageFilter } from '@/utils/filterConfigUtil';
  import SelectedTeamList from '@/components/selected-team-list/index.vue';

  const pageContainer = ref<HTMLElement | null>(null);
  const localStorageEventBus = useEventBus(LocalStorageEventBus);
  const route = useRoute();
  const analyseData = analyseStore();

  // 添加指标详情辅助工具
  const { createIndicatorMap } = useIndicatorDetailHelper();
  const indicatorDetailsMap = ref(new Map());

  const appIdList = useSessionStorage("app-id-list", [])
  const params = reactive({
    subjectCode: 'traffic',
    appIds: appIdList,
    teamCodes:[],
    country: ['global'],
    date: {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天',
    },
    appView: '',
    selectGroups: [] as any,
    timeParticleSize: TimeParticleSize.TOTAL,
    firstDayDfWeek: null,
  });
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(params);
  // 添加用于存储上一次复选参数值的响应式变量
  const lastParams = ref<{
    appIds: string[];
    country: string[];
    selectGroups: string[];
    teamCodes: string[];
  }>({
    appIds: [],
    country: [],
    selectGroups: [],
    teamCodes: []
  });
  const appViewList = ref();
  const handleViewData = ref();
  const loading = ref(false);
  const dataLoading = ref(false);
  const columns = ref<any>([]);
  const tableData = ref<any>([]);

  const pagination = ref<any>({
    pageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    showPageSize: true,
    showTotal: true,
  });
  const pageSizeChange = (size: number) => {
    pagination.value.pageSize = size;
  };
  const groupList = ref();
  const indicatorList = ref<any>([]);
  const requestId = ref('');
  const checkList = ref<any>([]);
  const timeOut = ref();
  const resultData = ref();
  const totalNum = ref(0);
  const formatNumber = (num: number, displayType?: { type: string; decimalNum: number }) => {
    if (num === null || num === undefined) return '0';
    let formattedValue = '';
    if (displayType && displayType.type) {
      if (displayType.type === 'default') {
        formattedValue = num.toFixed(displayType.decimalNum);
      } else if (displayType.type === 'percent') {
        // 乘以 100
        formattedValue = (num * 100).toFixed(displayType.decimalNum);
      }
    } else {
      formattedValue = num.toFixed(2);
    }
    return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',') || 0;
  };
  // 判断周几
  const getDayOfWeek = (dateString: string) => {
    if (params.timeParticleSize === TimeParticleSize.DAY1) {
      const date = new Date(dateString);
      const days = ['日', '一', '二', '三', '四', '五', '六'];
      return `${dateString}(${days[date.getDay()]})`;
    }
    if (params.timeParticleSize === TimeParticleSize.WEEK1) {
      return `${dateString}当周`;
    }
    if (params.timeParticleSize === TimeParticleSize.MONTH1) {
      return `${dateString}月`;
    }
    if (params.timeParticleSize === TimeParticleSize.QUARTER1) {
      return `${dateString}季度`;
    }
    if (params.timeParticleSize === TimeParticleSize.YEAR1) {
      return `${dateString}年`;
    }
    return dateString;
  };
  const selectRefs = ref();
  const editIndicators = () => {
    selectRefs.value.openModal(params.appView);
  };
  // 根据指标组code获取详情，checkList
  const getIndicatorsDetail = async () => {
    if (params.appView) {
      await getAttributionViewDetail(params.appView).then((res) => {
        checkList.value = res || [];
      });
    }
  };
  const computedData = async () => {
    dataLoading.value = true;
    timeOut.value = setTimeout(() => {
      if (dataLoading.value) {
        dataLoading.value = false;
      }
    }, 80000);
    const aggregatesList = [] as any;
    groupList.value.forEach((item: any) => {
      params.selectGroups.forEach((el: any) => {
        if (item.code === el) {
          aggregatesList.push({
            aggregateType: 'event',
            objectDisplayName: item.displayName,
            objectName: item.name,
            objectId: item.code,
            objectType: 'string',
          });
        }
      });
    });
    const filterData = {
      logicalOperation: 'and',
      filters: [
        {
          calcuSymbol: 'eq',
          displayName: '国家',
          filterType: 'operation',
          logicalOperation: 'and',
          objectId: 'operation_attribute202412210011',
          objectName: 'country',
          objectType: 'string',
          subFilters: [],
          thresholds: params.country,
        },
      ],
    };
    await getIndicatorsDetail();
    const queryParams = {
      aggregates: aggregatesList,
      indicators: checkList.value,
      filter: params?.country?.length ? filterData : {},
      subject: params.subjectCode,
      timeParticleSize: params.timeParticleSize,
      firstDayDfWeek: params.firstDayDfWeek,
      dateRange: params.date,
      teamCodes: params.teamCodes,
    };
    requestId.value = queryTrafficReportData(queryParams);
  };
  const refresh = () => {
    // 检查参数是否有变化
    const hasParamsChanged = !isEqual(
      { appIds: params.appIds, country: params.country, selectGroups: params.selectGroups, teamCodes: params.teamCodes },
      { appIds: lastParams.value.appIds, country: lastParams.value.country, selectGroups: lastParams.value.selectGroups, teamCodes: lastParams.value.teamCodes }
    );
    // 只有当参数发生变化时才执行刷新
    if (hasParamsChanged) {
      // 更新上一次参数值
      lastParams.value = {
        teamCodes: [...params.teamCodes],
        appIds: [...params.appIds],
        country: [...params.country],
        selectGroups: [...params.selectGroups],
      };
      computedData();
    }
  };
  const getFilterData = async () => {
    groupList.value = await analyseData.fetchOperationGroupLists(params.subjectCode);
    const mediaSourceCode = groupList.value?.find((item: any) => item.name === 'media_source')?.code;
    if (mediaSourceCode && !params.selectGroups.includes(mediaSourceCode)) {
      params.selectGroups.push(mediaSourceCode);
    }
    indicatorList.value = await analyseData.fetchAttributionIndexInfo(params.subjectCode);
    await getAttributionViews(params.subjectCode).then((res) => {
      appViewList.value = [...(res.sys || []), ...(res.user || []), ...(res.share || [])];
      handleViewData.value = res;
      params.appView = appViewList.value.find((item: any) => item.isDefault)?.code || appViewList.value[0]?.code;
    });
  };

  // 按小时 只能选一天
  const minutesVerification = () => {
    const timeType = params.timeParticleSize;
    if (timeType === 'm1' || timeType === 'm5' || timeType === 'm10') {
      Message.warning('按分钟查看，时间范围一次最多展示1天');
      const compressDateRange = compressDateRangeToEndDate(params.date);
      params.date = compressDateRange;
    }
  };
  const timeChange = (v: any) => {
    params.timeParticleSize = v.timeParticleSize;
    params.firstDayDfWeek = v.firstDayDfWeek;
    minutesVerification();
    computedData();
  };
  const init = async () => {
    loading.value = true;
    await getFilterData();
    loading.value = false;
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, { ...savedConfig });
    }
    // 初始化 lastParams 的值
    lastParams.value = {
      appIds: [...params.appIds] as string[],
      country: [...params.country] as string[],
      selectGroups: [...params.selectGroups] as string[],
      teamCodes: [...params.teamCodes] as string[],
    };
    computedData();
  };
  const viewChange = (v: any) => {
    computedData();
  };
  const countryChange = (v: any) => {
    params.country = v;
    refresh();
  };
  const datePick = (date: any) => {
    params.date = date;
    computedData();
  };
  const groupChange = (v: any) => {
    if (params.appView && !v) {
      refresh();
    }
  };
  const updateView = async (v) => {
    params.appView = v || params.appView;
    await getAttributionViews(params.subjectCode).then((res) => {
      appViewList.value = [...(res.sys || []), ...(res.user || []), ...(res.share || [])];
      handleViewData.value = res;
    });
    // 检查更新后的appView是否还在数组中
    const viewExists = appViewList.value.find((item) => item.code === params.appView);
    if (!viewExists) {
      const defaultObj = appViewList.value.find((item) => item.isDefault);
      params.appView = defaultObj?.code || appViewList.value[0]?.code;
      computedData();
    }
    if (v) {
      computedData();
    }
  };
  const updateIndicator = async () => {
    indicatorList.value = await analyseData.fetchAttributionIndexInfo(params.subjectCode);
  };
  onMounted(() => {
    init();
  });
  const handleTableData = () => {
    tableData.value = [];
    const data = resultData.value.y.flatMap((item) =>
      item.yData.map((el) => ({
        code: item.eventCode || item.indicatorCode,
        name: item.displayName,
        displayType: item.displayType,
        groupValue: el.group.map((value) => (value === null ? 'null' : value)).join(','),
        groupList: el.group.map((value) => (value === null ? 'null' : value)),
        values: el.values,
      }))
    );
    const uniqueData = Array.from(new Set(data.map((item) => item.name))).map((name) => data.find((item) => item.name === name)); // 根据 name 去重
    // 创建指标详情映射
    indicatorDetailsMap.value = createIndicatorMap(uniqueData);

    const xDateList = resultData.value.x;
    const columnsDate = xDateList && xDateList.length > 0 ? xDateList.map((item) => item) : [];

    const spliceList = [] as any;
    const groupsList =
      resultData.value?.groups?.map((item) => {
        return item.map((value) => (value === null ? 'null' : value)).join(','); // 将 null 转换为字符串 'null'
      }) || [];

    columns.value = data.length
      ? [
          {
            title: '日期',
            dataIndex: 'date',
            width: 140,
            fixed: 'left',
            sortable: { sortDirections: ['ascend', 'descend'] },
          },
          ...uniqueData.map((item, index) => ({
            code: item.code,
            title: item.name,
            dataIndex: `num${index + 1}`,
            slotName: `num${index + 1}`,
            titleSlotName: `num${index + 1}-title`,
            indicatorCode: item.code,
            displayType: item.displayType,
            width: getCustomStringLength(item.name) + 50,
            sortable: { sortDirections: ['ascend', 'descend'] },
          })),
        ]
      : [];

    const result = [] as any;
    const dates = columnsDate.map((item) => getDayOfWeek(item));
    const yLength = resultData.value.y.length;
    dates.forEach((date, dateIndex) => {
      groupsList.forEach((groupValue) => {
        const entry = {
          date,
          groupValue,
          groupList: [],
        };

        // 初始化所有 num 值为 null
        for (let i = 0; i < yLength; i += 1) {
          entry[`num${i + 1}`] = null;
        }
        // 遍历数据,填充对应的 num 值
        data.forEach((item) => {
          if (item.groupValue === groupValue) {
            for (let i = 0; i < yLength; i += 1) {
              if (entry[`num${i + 1}`] === null) {
                entry[`num${i + 1}`] = item.values[dateIndex];
                entry.groupList = item.groupList;
                break; // 找到第一个为null的num并赋值后,跳出循环
              }
            }
          }
        });
        result.push(entry);
      });
    });
    tableData.value = [...result];

    // 当有分组项的情况下
    if (resultData.value.groupsDesc && resultData.value.groupsDesc.length > 0) {
      resultData.value.groupsDesc.forEach((item, index) => {
        spliceList.push({
          title: item.name,
          dataIndex: `group-${index}`,
          width: getCustomStringLength(item.name) + 80,
          ellipsis: true,
          tooltip: true,
          sortable: {
            sortDirections: ['ascend', 'descend'],
            sorter: (a, b, extra) => {
              const { direction } = extra;
              const { format } = item;
              const valueA = a[`group-${index}`];
              const valueB = b[`group-${index}`];
              return createCustomSorter(format)(valueA, valueB, direction);
            },
          },
          filterable: {
            filter: (value, row) => {
              if (!value || value.length === 0) return true;
              return value.includes(row.groupList[index]);
            },
            slotName: `group-filter-${index}`, // 使用唯一的插槽名
            multiple: true,
          },
        });
        tableData.value.forEach((el) => {
          el[`group-${index}`] = el.groupList[index];
        });
      });
      columns.value.splice(1, 0, ...spliceList);
    }
  };
  // 处理特殊分组条件级联选中处理
  const filtersValue = ref<any>({});
  const handleTableFilter = (dataIndex, filters) => {
    filtersValue.value[dataIndex] = filters;
    const filterNames = columns.value
      .filter((column) => {
        const filterValues = filtersValue.value[column.dataIndex];
        return filterValues && filterValues.length > 0;
      })
      .map((column) => column.title);
    // 级联关系
    const cascadeMap = {
      '平台渠道': '广告系列',
      '广告系列': '广告组',
      '广告组': '广告'
    };
    
    // 检查是否有级联项需要自动勾选
    const groupValue = [...params.selectGroups];
    let hasChanges = false;
    // 检查是否需要级联选择
    filterNames.forEach(filterName => {
      if (cascadeMap[filterName]) {
        const parentItem = groupList.value.find(item => item.displayName === filterName);
        const cascadeItemName = cascadeMap[filterName];
        const cascadeItem = groupList.value.find(item => item.displayName === cascadeItemName);
        if (parentItem && cascadeItem) {
          // 检查级联项是否已勾选
          const isCascadeItemSelected = groupValue.includes(cascadeItem.code);
          if (!isCascadeItemSelected) {
            groupValue.push(cascadeItem.code);
            hasChanges = true;
          }
        }
      }
    });
    if (hasChanges) {
      params.selectGroups = groupValue;
      refresh();
    }
  };
  // 添加一个获取筛选选项的函数
  const getFilterOptionsByIndex = (index: number) => {
    if (!tableData.value?.length) return [];
    return Array.from(new Set(tableData.value.map((row) => row.groupList[index])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value,
      }));
  };
  // 导出
  const exportXlsx = () => {
    // 获取表头
    const headers = columns.value.map((item) => item.title);
    const columnsList = [headers];
    tableData.value.forEach((item: any) => {
      const row = headers.map((header) => {
        const dataIndex = columns.value.find((col) => col.title === header)?.dataIndex;
        return dataIndex ? item[dataIndex] : '';
      });
      columnsList.push(row);
    });

    const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
    const newWorkbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
    XLSX.writeFile(newWorkbook, `归因分析_${getDateRangeStartDate(params.date)}-${getDateRangeEndDate(params.date)}.xlsx`);
  };

  watch(reportQueryResponseData, async (newData, oldData) => {
    if (newData === undefined || newData.requestId !== requestId.value) {
      return;
    }
    if (timeOut.value) {
      clearTimeout(timeOut.value);
    }
    resultData.value = newData.result;
    totalNum.value = newData?.result?.totalNum;
    handleTableData();
    dataLoading.value = false;
  });
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id-list') {
      params.appIds = value
      // computedData();
    }
  });
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .table-content {
    height: 100%;
    width: 100%;
    .filter-content {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .edit-select {
    width: 100%;
    position: relative;
    .edit {
      position: absolute;
      right: 30px;
      top: 8px;
      opacity: 0;
      cursor: pointer;
    }
    &:hover {
      .edit {
        opacity: 1;
      }
    }
  }
  .operation-icon {
    padding: 4px;
    display: inline-block;
    cursor: pointer;
    border-radius: 4px;
    &:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
    }
    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }
  .button-background {
    background-color: #ffffff;
    padding: 8px;
    border-radius: 4px;
    color: var(--tant-text-gray-color-text1-2);
  }

  .button-background:hover {
    color: #bdbebf;
    background-color: #fff;
  }
  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
</style>
