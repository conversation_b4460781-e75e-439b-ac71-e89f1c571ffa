import {defineStore} from "pinia";

interface DateRange {
    startDate: string;
    endDate: string;
    dateText: string;
}
 const useReportDataStore = defineStore('reportDataStore', {
    state: () => ({
        // 以报表 id 作为键，保存每个报表的日期信息
        dateRanges: {}as Record<string, DateRange> ,
        firstDayDfWeeks:{} as Record<string, number>,
        timeParticleSizes:{}as Record<string, string>,
        comparedDateLists:{}as Record<string, any>,
        timeSpans:{}as Record<string, any>,
        arrangeTypes:{}as Record<string, any>
    }),
    actions: {
        setDateRanges(reportId:string, dateRange:any) {
            this.dateRanges[reportId] = dateRange;
        },
        getDateRanges(reportId:string) {
            return this.dateRanges[reportId] || { startDate: '', endDate: '',dateText:'' };
        },
        setFirstDayDfWeeks(reportId:string, firstDayDfWeek:any) {
            this.firstDayDfWeeks[reportId] = firstDayDfWeek;
        },
        getFirstDayDfWeeks(reportId:string) {
            return this.firstDayDfWeeks[reportId] ||  null;
        },
        setTimeParticleSizes(reportId:string, timeParticleSize:any) {
            this.timeParticleSizes[reportId] = timeParticleSize;
        },
        getTimeParticleSizes(reportId:string) {
            return this.timeParticleSizes[reportId] || 'D1';
        },
        setComparedDateLists(reportId:string,comparedDateList:Array<any>) {
            this.comparedDateLists[reportId] = comparedDateList;
        },
        getComparedDateLists(reportId:string) {
            return this.comparedDateLists[reportId] || [];
        },
        setTimeSpans(reportId:string,timeSpan:any) {
            this.timeSpans[reportId] = timeSpan;
        },
        getTimeSpans(reportId:string) {
            return this.timeSpans[reportId] || { firstDayDfWeek: 1, number: 7,unit:'DAY' };
        },
        setArrangeType(reportId:string,arrangeType:any) {
            this.arrangeTypes[reportId] = arrangeType;
        },
        getArrangeType(reportId:string) {
            return this.arrangeTypes[reportId] || 'event';
        },
    }
});
export default useReportDataStore;
