<template>
    <!-- 事件属性指标 -->
    <div>
        <a-trigger v-model:popup-visible="selectVisible" trigger="click" :unmount-on-close="false" position="bl"
            :update-at-scroll="true" @click="handleSelectVisible">
            <div class="filter-btn select-btn">
                <span class="filter-label">{{ selectObject.eventAttrName }}</span>
            </div>
            <template #content>
                <div class="virtual-container">
                    <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
                        <a-input v-model:model-value="searchInput" placeholder="请输入搜索"
                            style="border: none;height: 40px;">
                            <template #prefix>
                                <icon-search />
                            </template>
                        </a-input>
                    </div>
                    <div class="dropdown-list">
                        <a-list :virtual-list-props="{ height: 256 }" :data="filteredLists">
                            <template #item="{ item, index }">
                                <a-list-item :key="index">
                                    <div class="li-box">
                                        <div v-if="item.typeName" class="li-title">
                                            <div class="li-type-class">
                                                <div class="item-label">{{ item.typeName }}</div>
                                            </div>
                                        </div>
                                        <div v-if="item.name" class="li-content">
                                            <a-trigger v-if="item.code" :trigger="['hover']" position="right"
                                                :update-at-scroll="true">
                                                <div class="li-class"
                                                    :class="{ 'li-active': selectObject.eventAttrName == item.name }"
                                                    @click="sub1Change(item)">
                                                    <div class="item-label">{{ item.displayName }}</div>
                                                </div>
                                                <template #content>
                                                    <div class="trigger-box">
                                                        <div class="card-header">
                                                            <div class="card-header-container">
                                                                <div class="header-icon"><icon-launch /></div>
                                                                <div class="header-title">
                                                                    <div class="name">{{ item.displayName }}</div>
                                                                </div>
                                                                <div class="header-type">{{ getAttrName(item.type) }}
                                                                </div>
                                                            </div>
                                                            <div class="title-sub">{{ item.name }}</div>
                                                        </div>
                                                        <div class="card-desc">
                                                            <div class="span-desc">{{ item.note }}</div>
                                                        </div>
                                                        <div class="card-footer">
                                                            <div>
                                                                {{ getLabel(item.dataType) }}
                                                            </div>
                                                            <div class="action">
                                                                <a-tooltip content="前往属性详情" position="top">
                                                                    <div class="action-icon">
                                                                        <icon-launch />
                                                                    </div>
                                                                </a-tooltip>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </a-trigger>
                                            <div v-else class="li-class"
                                                :class="{ 'li-active': selectObject.eventAttrName == item.name }"
                                                @click="sub1Change(item)">
                                                <div class="item-label">{{ item.name }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </a-list-item>
                            </template>
                        </a-list>
                    </div>
                </div>
            </template>
        </a-trigger>



    </div>
</template>

<script setup lang="ts">
import {computed, defineProps, ref, watch} from 'vue'
import {getAnalyseEvent} from "@/api/analyse/api";

const emits = defineEmits(['subChange']);
const selectVisible = ref(false)
const handleSelectVisible = () => {
    selectVisible.value = !selectVisible.value;
}

const searchInput = ref('')

const props = defineProps({
    panelData: {
        type: Object,
        default() {  // 默认值
            return {
                eventAttrCode:"",
                eventAttrName:"总次数",
                eventDisplayName:"anonymous_login",
                eventName:"anonymous_login",
                eventType:"2",
                filter:{},
                type:"event"
            };
        },
    }
})

const virtualList = ref<any>([])

const selectObject = ref({
    eventAttrCode: props.panelData.eventAttrCode || '',
    eventAttrName: props.panelData.eventAttrName || '',
    statisticalType: props.panelData.statisticalType || '',
    statisticalName: props.panelData.statisticalName || '',
    numRule: props.panelData.numRule || '',
    objectType: ''
})

// 方法获取属性
const getAttrName = (type: any) => {
    const labels = {
        '1': '预置属性',
        '2': '自定属性',
        '3': '虚拟属性',
        '4': '维度表属性',
    };
    return labels[type] || '';
};

// 方法获取标签
const getLabel = (objectType: any) => {
    const labels = {
        string: '文本',
        number: '数值',
        boolean: '布尔',
        time: '时间',
        array: '列表',
        date: '日期',
        datetime: '日期时间',
        variant: '对象组'
    };
    return labels[objectType] || objectType;
};

const typeList = ref<any>([
    {
        typeName: '预置计算方法',
        itemData: [
            {
                name: '总次数'
            },
            {
                name: '触发用户数'
            },
            {
                name: '人均次数'
            },
        ]
    },
    {
        typeName: '事件属性指标',
        itemData: []
    }
])

const filteredLists = computed(() => {
    const str = searchInput.value.trim().toLowerCase();
    if (str) {
        return virtualList.value.filter(item => item?.name?.includes(str));
    }
    return virtualList.value
});

const sub1Change = (v) => {
    selectObject.value.eventAttrName = v.name
    selectObject.value.eventAttrCode = v.code
    selectObject.value.objectType = v.dataType
    const arr = typeList.value[0].itemData.map(item => item.name)
    if (arr.includes(v.name)) {
        selectObject.value.statisticalType = ''
        selectObject.value.statisticalName = ''
        selectObject.value.numRule = '用户'
    } else {
        selectObject.value.statisticalType = v.dataType === 'number' ? 'sum' : 'mode'
        selectObject.value.statisticalName = v.dataType === 'number' ? '总和' : '去重数'
        selectObject.value.numRule = ''
    }
    selectVisible.value = false
    emits('subChange', selectObject.value)
}

interface TypeItem {
    typeName: string;
}
const previousEventCode = ref(''); // 用于存储上一次的 eventName

const getatrList = async () => {
    // 检查当前的 eventName 是否与上一次不同
    if (props.panelData.eventCode !== previousEventCode.value) {
        previousEventCode.value = props.panelData.eventCode; // 更新上一次的 eventName
        const data = {
            event:props.panelData.eventCode,
            inApp:1
        }
        await getAnalyseEvent(data).then((res) => {
            typeList.value[1].itemData = res;
        });
        virtualList.value = typeList.value.flatMap((item) => {
            const typeItem: TypeItem = {
                typeName: item.typeName,
            };
            return [typeItem, ...item.itemData];
        });
        // console.log(virtualList.value, '1111');
    }
};

watch(() => props.panelData, (newVal) => {
    selectObject.value.eventAttrCode = newVal.eventAttrCode || '';
    selectObject.value.eventAttrName = newVal.eventAttrName || '';
    selectObject.value.statisticalType = newVal.statisticalType || '';
    selectObject.value.statisticalName = newVal.statisticalName || '';
    // selectObject.value.numRule = newVal.numRule || '用户';
    // console.log(props.panelData,'props.panelData');
    getatrList()

}, { immediate: true });

</script>

<style scoped lang="less">
.fixed-modal{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}
.filter-btn{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    box-sizing: border-box;
    .btn-icon{
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    &:hover{
        border-color: var(--tant-primary-color-primary-hover);
    }
}
.select-btn{
    border: 1px solid var(--tant-border-color-border1-1);
    background: #f2f3f8;
    // margin: 0 12px;
    padding: 4px 8px;
    .filter-label{
        flex: 1 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 82px;
    }
}
:deep(.week-icon):hover{
    color: var(--tant-primary-color-primary-active);
}
.event-quotas-filter{
    width: 430px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, .15);
    .content{
        height: calc(100% - 96px);
        padding: 16px 0;
        background: #fff;
        .select-panel{
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding: 0 16px;
            .select-panel-span{
                color: var(--tant-text-gray-color-text1-2);
                font-weight: 400;
                font-size: 14px;
                .select-panel-event{
                    margin: 0 6px;
                    color: var(--tant-text-gray-color-text1-2);
                    font-weight: 500;
                }
            }
        }
        .select-date-panel{
            margin-top: 10px;
            padding: 0 16px;
            .date-panel-range{
                display: flex;
                align-items: center;
                .particle-select{
                    margin-left: 6px;
                    display: inline-block;
                    //width: 82px;
                    height: 32px;
                    
                }
                .input-number{
                    display: inline-block;
                    width: 60px;
                    height: 32px;
                    margin: 0 6px;
                    vertical-align: top;
                    background-color: #fff;
                }
                .date-delete{
                    margin-left: 16px;
                    color: var(--tant-text-gray-color-text1-2);
                    background-color: transparent;
                    border: none;
                    height: 24px;
                    width: 24px;
                    &:hover{
                        color: var(--tant-status-danger-color-danger-default);
                        background-color: var(--tant-status-danger-color-danger-fill-hover);
                    }
                }
            }
        }
    }
    .date-foot{
        display: flex;
        align-items: center;
        padding: 12px 14px;
        font-size: 13px;
        line-height: 20px;
        background: var(--tant-bg-gray-color-bg2-1);
        border-top: 1px solid var(--tant-border-color-border1-1);
        button{
            height: 24px;
            padding: 1px 8px;
            font: var(--tant-body-font-body-regular);
            border-radius: var(--tant-border-radius-medium);
            color: var(--tant-text-gray-color-text1-2);
            background-color: transparent;
            border: none;
        }
        button:hover{
            background-color: var(--tant-secondary-color-secondary-transp-hover);
        }
        .add-date-tip{
            display: flex;
            align-items: center;
            color: var(--tant-text-gray-color-text1-3);
            .bulb{
                margin-right: 10px;
                color: var(--tant-decorative-yellow-color-decorative6-1);
                font-size: 28px;
                vertical-align: top;
            }
            a{
                color: var(--tant-primary-color-primary-default);
            }
        }
    }
}
:deep(.arco-list-item) {
    padding: 0 !important;
    border: none!important;
}
:deep(.arco-list-bordered) {
    border: none!important;
}
.virtual-container{
    min-width: 100px;
    max-width: 240px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 16px 40px rgba(10, 16, 50, .05), 0 6px 20px rgba(10, 16, 50, .03), 0 0 5px rgba(10, 16, 50, .02);
    .dropdown-list{
        height: 256px;
        overflow: auto;
        .li-box{
            &:not(:last-of-type){
                border-bottom: 1px solid var(--tant-border-color-border1-1);
            }
            .li-title,.li-content{
                width: 100%;
                height: 32px;
                padding: 4px 4px 0;
                // margin-bottom: 8px;
                cursor: default;
                .li-type-class{
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    padding: 8px 8px 0;
                    color: var(--tant-text-gray-color-text1-3);
                    font-weight: 500;
                    font-size: 12px;
                    border-radius: 4px;
                    .item-label{
                        flex: 1 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                .li-class{
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    padding: 8px 8px 0;
                    color: var(--tant-text-gray-color-text1-3);
                    font-weight: 500;
                    font-size: 12px;
                    border-radius: 4px;
                    &:hover{
                        background-color: var(--tant-secondary-color-secondary-fill);
                    }
                    .item-label{
                        flex: 1 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                
            }
            .li-content{
                height: 42px;
                cursor: pointer;
                .li-class{
                    padding: 8px;
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    border-radius: 4px;
                    &:hover{
                        background-color: var(--tant-secondary-color-secondary-fill);
                    }
                }
                
            }
            .li-active{
                background-color: var(--tant-secondary-color-secondary-fill);
            }
        }
    }
}
.trigger-box{
    display: flex;
    flex-direction: column;
    width: 280px;
    max-height: 360px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-small-shadow-small-overall);
    .card-header{
        padding: 12px;
        background-color: var(--tant-bg-gray-color-bg2-1);
        border-radius: 4px 4px 0 0;
        .card-header-container{
            display: flex;
            flex-direction: row;
            .header-icon{
                margin-right: 5px;
                color: var(--tant-text-gray-color-text1-1);
            }
            .header-title{
                flex-grow: 1;
                color: var(--tant-text-gray-color-text1-1);
                .name{
                    display: inline-block;
                    width: 100%;
                    padding: 0 4px;
                    word-break: break-all;
                    border-radius: 4px;
                    color: var(--tant-text-gray-color-text1-1);
                    font-weight: 500;
                    line-height: 22px;
                }
                
            }
            .header-type{
                flex-shrink: 0;
                width: 62px;
                margin-left: 6px;
                color: var(--tant-text-gray-color-text1-3);
                font-weight: 400;
                font-size: 12px;
                text-align: right;
            }
        }
        .title-sub{
            margin-left: 24px;
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            word-break: break-all;
        }
    }
    .card-desc{
        flex-grow: 1;
        height: 130px;
        padding: 12px 8px;
        overflow-y: auto;
        color: var(--tant-text-gray-color-text1-2);
        font-weight: 400;
        font-size: 14px;
        .span-desc{
            display: inline-block;
            width: 100%;
            padding: 0 4px;
            word-break: break-all;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    }
    .card-footer{
        display: flex;
        flex-shrink: 0;
        justify-content: space-between;
        height: 34px;
        padding-top: 6px;
        padding-right: 12px;
        padding-left: 12px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        border-radius: 0 0 4px 4px;
        box-shadow: inset 0 1px #e6e6e6;
        .action{
            .action-icon{
                display: inline-block;
                min-width: 24px;
                height: 24px;
                padding: 0 4px;
                line-height: 22px;
                text-align: center;
                background-color: transparent;
                border-radius: 2px;
                cursor: pointer;
                transition: background-color .3s;
            }
            .action-icon:hover {
                background-color: var(--tant-primary-color-primary-fill-hover);
                color: var(--tant-primary-color-primary-default);
            }
        }
    }
}
.week-set-body{
    width: 352px;
    padding: 16px 16px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-bottom);
    .week-set-title{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }
    .week-set-desc{
        display: block;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;
        span{
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            padding: 0 4px;
        }
    }
    .week-set-weeks {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-right: -16px;
        padding-bottom: 16px;
        .week-set-week {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 74px;
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 5px 16px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            white-space: nowrap;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-active);
                border-color: var(--tant-secondary-color-secondary-fill-active);
            }
        }
        .week-active{
                background-color: var(--tant-secondary-color-secondary-fill-active);
                border-color: var(--tant-secondary-color-secondary-fill-active);
        }
        
    }
    .week-set-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: -16px;
        margin-left: -16px;
        padding-top: 8px;
        padding-right: 16px;
        border-top: 1px solid var(--tant-border-color-border1-1);
        button{
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
}
.entity-panel{
    display: flex;
    margin-top: 16px;
    padding: 12px 16px 0;
    border-top: 1px dashed var(--tant-border-color-border1-1);
    line-height: 32px;
}
</style>