<template>
  <div class="relatedEvents">
    <div class="tableHeader">
      <div class="title-input">
        <a-input
            v-model="searchTerm"
            class="title-input-hover"
            style="width: 280px; padding:0 8px"
            placeholder="搜索事件名\显示名">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
      </div>
    </div>
    <div class="table">
      <div class="next-table">
        <div style="max-height: 100%;height: 600px;;overflow: hidden auto">
          <a-table

              :hoverable="false" :pagination="false"
              :sticky-header="true" :columns="columns" :data="data">
            <template #s-type="{record}">
              <div v-if="record.type===EventType.CUSTOM">
                自定义事件
              </div>
              <div v-if="record.type===EventType.VIRTUAL">
                虚拟事件
              </div>
              <div v-if="record.type===EventType.PREDEFINED">
                虚拟事件
              </div>
            </template>
            <template #t-type>
              <div style="display: flex;align-items: center;justify-content: center;gap: 3px">
                <div>事件类型</div>
                <a-trigger
                    v-model:popup-visible="TypeVisible"
                    trigger="click">
                  <div class="state-tip">
                    <img :src="attrIcon" alt="" style="padding-bottom: 3px;">
                  </div>
                  <template #content>
                    <div class="dropdown" style="height:auto ;width:auto;overflow-y: auto">
                      <div class="dropdown-item" @click="chooseTypeAll">
                        <div class="dropdown-label">
                          全部
                        </div>
                      </div>
                      <div v-for="item in selectOptions" :key="item.value" class="dropdown-item">
                        <div class="dropdown-label" @click="chooseType(item.value)">
                          {{ item.label }}
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </template>
            <template #s-displayName="{record}">
              <div style="display: flex;">
                <a-tooltip :content="record.displayName" position="top">
                  <div>{{ record.displayName }}</div>
                </a-tooltip>
              </div>
            </template>
            <template #s-note="{record}">
              <div style="display:flex;">
                <div v-if="record.note">{{record.note}}</div>
                <div v-else>&nbsp;&nbsp;&nbsp;-</div>
              </div>

            </template>
          </a-table>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue';
import {getEventAttrLinkList} from "@/api/setting/api";
import {EventType} from "@/api/enum";


const props = defineProps({
  dataCode: String
})
const searchTerm = ref('')
const columns = [
  {
    title: '事件名',
    dataIndex: 'name',
  },
  {
    title: '显示名',
    dataIndex: 'displayName',
    slotName: 's-displayName'
  },
  {
    title: '事件类型',
    dataIndex: 'type',
    titleSlotName: 't-type',
    slotName: 's-type'
  },
  {
    title: '备注',
    dataIndex: 'note',
    slotName: 's-note'
  },
];
const selectOptions = [
  {label: '预置事件', value: EventType.PREDEFINED},
  {label: '自定义事件', value: EventType.CUSTOM},
  {label: '虚拟事件', value: EventType.VIRTUAL},
]
const data = ref()
const TypeVisible = ref(false)
const isChanged = ref(false);
const TypeCheckDate = ref()

const chooseType = (v: string) => {
  TypeCheckDate.value = v
  TypeVisible.value = false
}
const chooseTypeAll = () => {
  TypeCheckDate.value = undefined
  TypeVisible.value = false
}
const attrIcon = computed(() => {
  return !isChanged.value ? '/icon/filter.svg' : '/icon/filterBlue.svg'
})


watch(TypeCheckDate, (newTypeCheckDate) => {
  isChanged.value = newTypeCheckDate
});
onMounted(() => {
  if (props.dataCode) {
    console.log(props.dataCode)
    getEventAttrLinkList(props.dataCode).then(res => {
      data.value = structuredClone(res);
    })
  }

})
</script>

<style scoped lang="less">

.relatedEvents {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  padding: 4px 24px;
  overflow-y: auto;

  .tableHeader {
    display: flex;
    flex: 0 0 auto;
    justify-content: flex-end;
    width: 100%;
    margin-bottom: 16px;

    .title-input {
      width: 280px;
      margin-left: auto;

      .title-input-hover:focus-within {
        box-shadow: var(--tant-input-shadow-active-overall);
        border-color: var(--tant-primary-color-primary-active);
      }
    }

  }

  .table {
    flex: 1 1;
    width: 100%;
    height: 100%;
    margin-bottom: 16px;
    overflow: hidden;

    .next-table {
      height: 100%;
    }
  }


}

.dropdown {
  box-shadow: var(--tant-small-shadow-small-overall);
  width: 100px;
  height: 120px;
  overflow-y: scroll;
  margin-top: 10px;
  margin-left: 20px;
  border-radius: 4px;
  background-color: var(--tant-bg-white-color-bg1-1);
  padding: 5px;

  .dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;

    .dropdown-label {
      padding: 4px 4px 0;
    }
  }

  .dropdown-item:hover {
    background-color: var(--tant-bg-gray-color-bg2-1);
  }
}

.state-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--tant-secondary-color-secondary-hover);
}
</style>
