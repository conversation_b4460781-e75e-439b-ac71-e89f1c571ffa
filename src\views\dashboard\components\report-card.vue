<template>
  <div ref="cardRef" class="card">
    <div class=" drag-allow-from card-title">
      <div class="title-text">
        <div class="title-text-items">
          <span class="url-hover">{{ reportDetail?.name }}</span>
          <a-tooltip v-if="reportDetail?.description" :content="reportDetail?.description" position="top">
            <span role="img" class="custom-tooltip">
            <img src="/icon/tooltip.svg" alt="">
            </span>
          </a-tooltip>
        </div>
      </div>

      <div v-show="reportDetail?.queryParam" class="operation">
        <a-tooltip content="刷新">
          <div class="operation-icon">
            <icon-refresh class="icon" @click="cardRefresh"/>
          </div>
        </a-tooltip>
        <a-tooltip content="编辑">
          <div v-show="reportDetail?.model" class="operation-icon" @click="editReportDetail">
            <icon-fullscreen class="icon"/>
          </div>
        </a-tooltip>

        <a-dropdown :popup-max-height="false" :popup-container="cardRef">
          <a-tooltip content="更多">
            <div class="operation-icon">
              <icon-more class="icon"/>
            </div>
          </a-tooltip>
          <template #content>
            <a-doption
                v-show="reportDetail?.model && showSmallCard && [ReportAnalyseModel.EVENT].includes(reportDetail.model)"
                @click="resize('small')">
              小图
              <icon-check v-if="size == 'small'"/>
            </a-doption>
            <a-doption v-show="reportDetail?.model" @click="resize('middle')">中图
              <icon-check v-if="size == 'middle'"/>
            </a-doption>
            <a-doption @click="resize('large')">大图
              <icon-check v-if="size == 'large'"/>
            </a-doption>
            <a-divider :margin="1"/>
            <a-doption>报表设置</a-doption>
            <a-doption @click="exportXlsx">数据导出</a-doption>
            <a-doption @click="copyUI">复制</a-doption>
            <a-doption @click="removeUI">删除</a-doption>
          </template>
        </a-dropdown>
        <a-modal
            v-model:visible="visibleDelete"
            :width="320"
            title-align="start"
            ok-text="删除"
        >
          <template #title> 移除报表</template>
          <div class="prompt">确认移除「{{ reportDetail?.name }}」报表吗？</div>
          <template #footer>
            <a-button @click="handleDeleteCancels">取消</a-button>
            <a-button type="primary" status="danger" @click="handleDeleteOk">删除</a-button>
          </template>
        </a-modal>
      </div>
    </div>
    <div class="no-drag card-data">
      <a-spin dot :loading="loading" :size="10" style="width: 100%;height: 100%">
        <div v-if="reportDetail?.model==ReportAnalyseModel.FUNNEL" style="width: 100%;height: 100%">
          <funnelCardContent
              ref="funnelRef"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              :data-range-data="dataRangeData"
              @change-params="changeParams"
              @date-change="cardDateChange"/>
        </div>
        <div v-if="reportDetail?.model==ReportAnalyseModel.RETENTION" style="width: 100%;height: 100%">
          <retentionCardContent
              ref="retentionRef"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              :data-range-data="dataRangeData"
              @change-params="changeParams"
              @date-change="cardDateChange"
              @query-params-change="singleParamRefsh"/>
        </div>
        <div v-if="reportDetail?.model==ReportAnalyseModel.SCATTER" style="width: 100%;height: 100%">
          <ScatterLargeCardContent
              ref="scatterRef"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              :data-range-data="dataRangeData"
              @change-params="changeParams"
              @date-change="cardDateChange"
              @query-params-change="singleParamRefsh"/>
        </div>
        <div v-if="reportDetail?.model==ReportAnalyseModel.CUSTOM" style="width: 100%;height: 100%">
          <custom-card-content
              ref="customRef"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              :report-detail="reportDetail"
              @page-size-change="ps => resize(size,ps)"
              @change-params="changeParams"
              @query-params-change="singleParamRefsh"/>
        </div>
        <div v-if="reportDetail?.model==ReportAnalyseModel.GROUP" style="width: 100%;height: 100%">
          <group-large-card-content
              ref="groupRef"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              @page-size-change="ps => resize(size,ps)"
              @change-params="changeParams"/>
        </div>
        <div v-if="reportDetail?.model && [ReportAnalyseModel.EVENT,ReportAnalyseModel.APPLICATION].includes(reportDetail.model)" style="width: 100%;height: 100%">
          <event-small-card-content v-show="size == 'small'" :report="props.report" :report-analysis-data="reportAnalysisData"/>
          <event-card-content
              v-show="size === 'large' || size === 'middle'"
              ref="eventRef"
              :size="size"
              :report-analysis-data="reportAnalysisData"
              :report="props.report"
              :data-range-data="dataRangeData"
              :model-type="reportDetail.model"
              @page-size-change="ps => resize(size,ps)"
              @change-params="changeParams"
              @date-change="cardDateChange"
              @query-params-change="singleParamRefsh"/>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import EventSmallCardContent from '@/views/dashboard/components/report-card/event/event-small-card-content.vue';
import EventCardContent from '@/views/dashboard/components/report-card/event/event-card-content.vue';
import {querySingleAppReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {computed, onMounted, reactive, ref, watch} from 'vue';
import {detailReport} from "@/api/report/api";
import {removeDashboardUI, copyReportUI} from "@/api/dashboard/api";
import {ReportDto, WsEventAnalysisResultData} from "@/api/report/type";
import {ReportQueryResponse, ReportQuerySource} from "@/api/type";
import {ReportAnalyseModel} from "@/api/analyse/type";
import {Message} from "@arco-design/web-vue";
import useReportDataStore from "@/store/modules/report";
import funnelCardContent from '@/views/dashboard/components/report-card/funnel/FunnelLargeCardContent.vue';
import retentionCardContent from '@/views/dashboard/components/report-card/retention/RetentionLargeCardContent.vue';
import GroupLargeCardContent from "@/views/dashboard/components/report-card/group/GroupLargeCardContent.vue";
import ScatterLargeCardContent from '@/views/dashboard/components/report-card/scatter/ScatterLargeCardContent.vue';
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import CustomCardContent from "@/views/dashboard/components/report-card/custom/custom-card-content.vue";
import { debounce } from 'lodash';

interface Props {
  /**
   * 看板id
   */
  dashboardId: string;
  /**
   * 报表信息
   */
  report: any;
  /**
   * 请求参数，暂只支持时间范围
   */
  queryParam: any
}

defineOptions({
  inheritAttrs: false,
});
const props = defineProps<Props>();
const customRef = ref();
const reportDetailStore = useReportDataStore()
const reportAnalysisData = ref<ReportQueryResponse>();
const reportDetail = ref<ReportDto>();
const emit = defineEmits(['resize', 'changeParams', 'fetchDashboard', 'deleteReport', 'loadingChange', 'copyReport']);
const requestId = ref<string>();
const loading = ref<boolean>(true);
const size = computed(() => props.report.w === 5 ? 'small' : props.report.w === 10 ? 'middle' : 'large');


const cardRef = ref<HTMLElement>();
const visibleDelete = ref(false)

// 传递给子组件回显的日期
const dataRangeData = ref<any>({...props.queryParam?.dateRange});

// 卡片整体刷新方法，时间保持跟nav中一致,(原本是保存参数中的时间)
const freshData = debounce(() => {
  loading.value = true
  detailReport(props.report.objectCode).then((resp: ReportDto) => {
    reportDetail.value = resp
    dataRangeData.value = {...(props.queryParam?.dateRange || reportDetail.value.queryParam?.dateRange)};
    if (reportDetail.value.code != null) {
      reportDetailStore.setDateRanges(props.report.objectCode, reportDetail.value.queryParam?.dateRange);
      reportDetailStore.setFirstDayDfWeeks(props.report.objectCode, reportDetail.value.queryParam?.firstDayDfWeek)
      reportDetailStore.setTimeParticleSizes(props.report.objectCode, reportDetail.value.queryParam?.timeParticleSize);
      reportDetailStore.setComparedDateLists(props.report.objectCode, reportDetail.value.queryParam?.comparedDateList);
      reportDetailStore.setTimeSpans(props.report.objectCode, reportDetail.value.queryParam?.timeSpan);
      reportDetailStore.setArrangeType(props.report.objectCode, reportDetail.value.queryParam?.tableArrangeType);
    }
    loading.value = false
    requestId.value = querySingleAppReportData(ReportQuerySource.SYSTEM, resp.model, props.report.objectCode, props.dashboardId, undefined, {...props.queryParam, dateRange: dataRangeData.value});
  })
}, 500)

// 复制
const copyUI = () => {
  copyReportUI({
    dashboardId: props.dashboardId,
    reportId: props.report.objectCode
  }).then(res => {
    Message.success('复制成功')
    const copyConfig = res?.uiConfig || {}
    emit('copyReport', {i: res?.code,...copyConfig})
  }).catch(e => {
    console.error(e)
    Message.error('复制失败')
  })
}
// 删除
const removeUI = () => {
  visibleDelete.value = true
}
const handleDeleteOk = () => {
  removeDashboardUI({dashboardId: props.dashboardId, reportId: props.report.objectCode}).then(res => {
    Message.success('删除成功')
    emit('deleteReport', props.report.objectCode)
    visibleDelete.value = false
  }).catch(e => {
    console.error(e)
  })
}
const handleDeleteCancels = () => {
  visibleDelete.value = false
}
const resize = (sizeType: string, pageSize: number | undefined) => {
  let ps;
  if (reportDetail.value?.model === ReportAnalyseModel.RETENTION ||
      reportDetail.value?.model === ReportAnalyseModel.FUNNEL||
      reportDetail.value?.model === ReportAnalyseModel.SCATTER) {
    pageSize = 10
  }
  if (pageSize) {
    // pageSize 存在，指定分页大小切换
    ps = pageSize
  } else if (size.value === 'small') {
    // pageSize不存在，小卡片切换中、大卡片，默认10
    const dataLength = reportAnalysisData.value?.result?.x?.length;
    ps = (dataLength > 0 && dataLength < 10) ? dataLength : 10
  } else {
    // pageSize不存在，中、大卡片切换，保留分页
    ps = (props.report?.h - 4.25) / 0.67
  }
  emit('resize', sizeType, props.report?.objectCode, ps);
};
// 单卡片刷新
const changeParams = (value: string) => {
  emit('changeParams', value, props.report?.objectCode)
  // freshData()
}
const cardQueryParam = reactive({
  dateRange: props.queryParam.dateRange,
})
// 卡片单个刷新方法，因为其中时间不应与nav中的一致
const cardRefresh = () => {
  loading.value = true
  detailReport(props.report.objectCode).then((resp: ReportDto) => {
    requestId.value = querySingleAppReportData(ReportQuerySource.SYSTEM, resp.model, props.report.objectCode, props.dashboardId, undefined, cardQueryParam);
  })
}
const cardDateChange = (data) => {
  cardQueryParam.dateRange = data
  cardRefresh()
}
const singleParamRefsh = (v) => {
  Object.assign(cardQueryParam, v)
  cardRefresh()
}
// 点击编辑页
const editReportDetail = () => {
  router.push({
    name: ROUTE_NAME.REPORT_EDIT,
    query: {code: reportDetail.value?.code, model: reportDetail.value?.model},
  });
}
defineExpose({
  freshData
})

watch(reportQueryResponseData, (newData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    loading.value = false
    return
  }
  if (reportDetail.value?.model === ReportAnalyseModel.CUSTOM) {
    customRef.value.setChartOption(reportDetail.value.chartParams.length > 0 ? reportDetail.value.chartParams[0].graphStyle : undefined)
  }
  reportAnalysisData.value = newData;

  // 确保数据加载完成后再关闭loading
  if (reportAnalysisData.value) {
    loading.value = false;
  }
})

const showSmallCard = computed(() => {
  const reportData = reportAnalysisData.value?.result as WsEventAnalysisResultData;
  return reportData?.y && reportData?.y.length <= 1 && // 只有一个指标
      reportData?.groups && reportData?.groups.length === 1 // y 数据只有一个分组
});

// 监听props.queryParam变化
watch(() => props.queryParam, (newVal, oldVal) => {
  freshData()
}, {deep: true})

onMounted(() => {
  freshData();
})
// 数据导出
const eventRef = ref()
const retentionRef = ref()
const funnelRef = ref()
const scatterRef = ref()
const groupRef = ref()
const exportXlsx = () => {
  if (size.value === 'large') {
    eventRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name,)
  }
  if (size.value === 'middle') {
    eventRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
  }
  customRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
  retentionRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
  funnelRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
  scatterRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
  groupRef.value?.exportXlsx(cardQueryParam.dateRange, reportDetail.value?.name)
}
</script>

<style scoped lang="less">
.card {
  width: 100%;
  height: 100%;
  border: 1px solid transparent;
  transition: all 0.3s;
}

.card:hover {
  box-shadow: var(--tant-medium-shadow-medium-overall);
  border: 1px solid var(--tant-primary-color-primary-default);
  border-radius: 4px;
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 46px;
  margin-bottom: 0;
  padding: 16px 24px 8px;
  line-height: 20px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom: none;
  cursor: grab;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  .title-text {
    display: inline-block;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header5-medium);
    font-size: var(--font-size-title-small);
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .title-text-items {
      display: flex;
      align-items: center;

      .custom-tooltip {
        //display: flex;
        //align-items: center;
        margin-top: 3px;
        margin-left: 8px;

      }
    }
  }

  .operation {
    display: none;
    align-items: center;
    transition: opacity 0.3s;
    height: 32px !important;
  }

  .operation-icon {
    padding: 4px;
    display: inline-block;
    margin-left: 8px;
    cursor: pointer;

    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }
}

.card-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100% - 46px);
  padding: 0 24px 16px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

.card:hover .card-title .operation {
  display: flex;
  unicode-bidi: isolate;
}

:deep(.arco-table-tr) {
  height: auto;
}
</style>
