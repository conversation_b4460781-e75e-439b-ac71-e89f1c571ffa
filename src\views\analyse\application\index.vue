<template>
  <div id="applicationRoot">
    <div class="analyse-content">
      <analyse-header
          :report-data="reportData"
          :header-info="headerInfo"
          @call-computed-data="computedData"
          @call-export-xlsx="downloadAllData"
          @update-report-form="updateReportForm"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="!dataLoading" class="box">
                <div v-if="dataLoading" class="query-condition">
                  <!-- 分析指标 -->
                  <analysisIndex 
                    :analysis-index-data="analysisIndexData"
                    :subject-data="subjectData"
                    :indicator-list="indicatorList"
                    @indicators-change="indicatorsChange"
                    @subject-change="subjectChange"
                    @reset-params="reset"/>
                  <!-- 全局筛选 -->
                  <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem :group-item="groupData" @aggregates-change="aggregatesChange"/>
                </div>
                <div v-if="showBodyLeft" class="left-footer">
                  <a-button @click="toSave">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="!showDrawer ? { width:'calc(100% - 24px)'} : { width:'calc(100% - 344px)',overflow: 'scroll',transition: 'all .3s ease-in-out'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker v-if="dataLoading" :date-range="boardDate" disabled-after-today @date-pick="datePick"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        颗粒度：
                        <dateSet v-if="dataLoading" :no-mh="true" :time-particle="timeParticle" @time-change="timeChange"/>
                      </div>
                    </a-space>
                    <div>
                      <a-button-group style="background-color: #fff;margin-right: 20px;">
                        <a-popover title="趋势图" position="bottom" :content-style="{width: '260px'}">
                          <a-button
                              :disabled="totalNum === 0 || isChartType.includes('trend') || queryParam.timeParticleSize === TimeParticleSize.TOTAL"
                              @click="selectChart('trend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示指针随时间变化的趋势 <br>
                            <img :src="trend" alt="" style="width: 90%;height: 90%;"/><br>
                            <span v-if="queryParam.timeParticleSize === TimeParticleSize.TOTAL">*当时间粒度非合计时可用</span>
                          </template>
                        </a-popover>
                        <a-popover title="堆积图" position="bottom" :content-style="{width: '260px'}">
                          <a-button
                              :disabled="totalNum === 0 || isChartType.includes('stacked') || !eventData.groupsDesc.length || queryParam.timeParticleSize === TimeParticleSize.TOTAL"
                              @click="selectChart('stacked')"
                          >
                            <template #icon><img class="option-icon" src="/icon/stack-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示指标整体以及各个分组的变化趋势 <br>
                            <img :src="stacked" alt="" style="width: 90%;height: 90%;"/><br>
                            <span v-if="queryParam.timeParticleSize === TimeParticleSize.TOTAL || !eventData.groupsDesc.length">*当时间粒度非合计，且有分组项或事件拆分时，可用</span>
                          </template>
                        </a-popover>
                        <a-popover title="累计图" position="bottom" :content-style="{width: '260px'}" style="">
                          <a-button
                              :disabled="totalNum === 0 || isChartType.includes('total') || queryParam.timeParticleSize === TimeParticleSize.TOTAL"
                              @click="selectChart('total')"
                          >
                            <template #icon><img class="option-icon" src="/icon/total-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示指标累计值变化的趋势 <br>
                            <img :src="total" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover>
                        <a-popover title="分布图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="totalNum === 0 || isChartType.includes('distribution')" @click="selectChart('distribution')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/>
                            </template>
                          </a-button>
                          <template #content>
                            展示各个指标的分布情况 <br>
                            <img :src="distribution" alt="" style="width: 90%;height: 90%;"/>
                          </template>
                        </a-popover>
                        <a-popover title="饼状分布" position="bottom" :content-style="{width: '260px'}">
                          <a-button
                              :disabled="totalNum === 0 || isChartType.includes('pieTrend') || !eventData.groupsDesc.length"
                              @click="selectChart('pieTrend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/pie.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示各个指标的分组占比情况 <br>
                            <img :src="pieTrend" alt="" style="width: 100%;height: 90%;"/><br>
                            <span v-if="!eventData.groupsDesc.length">*当有分组项或事件拆分，可用</span>
                          </template>
                        </a-popover>
                      </a-button-group>
                      <a-tooltip content="可视化配置">
                        <a-button :disabled="totalNum === 0" @click="onCollapse">
                          <template #icon>
                            <icon-settings/>
                          </template>
                        </a-button>
                      </a-tooltip>
                    </div>
                  </div>
                  <div v-if="totalNum > 0" class="right-content">
                    <a-layout style="height: 490px;width: 100%" :style="showDrawer?'':''">
                      <a-layout-content v-if="!loading">
                        <a-alert v-if="eventData?.resultsExceedsLimit" style="margin-top: 5px;">
                          因数据条数过多，优先展示前1000条数据。建议修改分组项减少数据条数，或点击
                          <span class="download-txt" @click="downloadAllData">数据下载</span>可获取完整数据。
                        </a-alert>
                        <event-echars-vue ref="eventechars" :chart-type="isChartType" :event-data="eventData" :time-format="queryParam.timeParticleSize" :showlabel="showlabel" :show-rate="showRate"
                                          :limit-screen="limitScreen" :group-num="groupNum" :y-max="yMax" :y-min="yMin"/>
                        <!-- <compareEchart ref="eventechars" :chart-type="isChartType" :event-data="eventData" :time-format="queryParam.timeParticleSize" :showlabel="showlabel" :show-rate="showRate" :group-num="groupNum" :y-max="yMax" :y-min="yMin"/> -->
                      </a-layout-content>
                      <a-layout-footer v-if="!loading">
                        <div style="display: flex;justify-content: space-between;margin-bottom: 18px;">
                          <div>
                            <a-radio-group v-model="tableRadio" type="button" @change="handleChangeTable">
                              <!-- <a-tooltip content="合并">
                                <a-radio value="hb">合并</a-radio>
                              </a-tooltip>
                              <a-tooltip content="平铺">
                                <a-radio value="pp">平铺</a-radio>
                              </a-tooltip> -->
                              <a-tooltip content="累计">
                                <a-radio v-if="isChartType.includes('total')" value="lj">累计</a-radio>
                              </a-tooltip>
                            </a-radio-group>
                          </div>
                          <div>
                            <a-dropdown @select="tableTypeChange">
                              <a-button class="btn" style="margin-right: 12px;">
                                {{ tableTypeName }}
                                <img v-if="queryParam.tableArrangeType == 'dateUp'" src="/icon/analysis/dateUp.svg" alt="">
                                <img v-if="queryParam.tableArrangeType == 'dateDown'" src="/icon/analysis/dateDown.svg" alt="">
                              </a-button>
                              <template #content>
                                <a-doption value="event">按指标</a-doption>
                                <a-dsubmenu trigger="hover">
                                  <template #default>按日期</template>
                                  <template #content>
                                    <a-doption value="dateUp" style="width: 80px;">
                                      <div
                                          style="width: 100%;display: flex;justify-content:space-between;align-items: center;">
                                        <span>升序</span>
                                        <img src="/icon/analysis/dateUp.svg" alt="">
                                      </div>
                                    </a-doption>
                                    <a-doption value="dateDown">
                                      <div
                                          style="width: 100%;display: flex;justify-content:space-between;align-items: center;">
                                        <span>降序</span>
                                        <img src="/icon/analysis/dateDown.svg" alt="">
                                      </div>
                                    </a-doption>
                                  </template>
                                </a-dsubmenu>
                              </template>
                            </a-dropdown>
                            <button class="btn" @click="showTotal">阶段汇总配置</button>
                            <a-button style="margin-left: 12px;" @click="importTable()">导出</a-button>
                          </div>
                        </div>
                        <event-table-vue
                          ref="eventtable"
                          :time-format="queryParam.timeParticleSize"
                          :table-radio="tableRadio"
                          :event-data="eventData"
                          :arrange-type="queryParam.tableArrangeType"/>
                      </a-layout-footer>
                    </a-layout>
                  </div>
                  <div v-if="!loading && totalNum == 0" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div class="drawer" :style="showDrawer ? {width: '320px'} : {width: '0px'}">
                <div class="drawer-header">
                  <div class="title">可视化配置</div>
                  <div class="icon">
                    <a-button @click="() => showDrawer = false">
                      <template #icon>
                        <icon-double-right/>
                      </template>
                    </a-button>
                  </div>
                </div>
                <a-collapse v-model:active-key="activeKey" expand-icon-position="right" class="customStyle" :bordered="false" @change="handleChangeCollapse">
                  <a-collapse-item key="1" header="通用设置">
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示数值</span>
                          <a-tooltip content="对第N日趋势图、每日趋势图生效" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showlabel" size="small"/>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                  <a-collapse-item key="2" header="趋势图">
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>指标展示设置</span>
                        </div>
                        <div class="title">
                          <span>辅助线设置</span>
                        </div>
                        <div class="title">
                          <span>Y轴自定义</span>
                        </div>
                        <div class="title">
                          <span></span>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-popover trigger="click" position="left" :popup-visible="metricVisible" :popup-offset="130" :show-arrow="false">
                            <a-button style="width: 24px;height: 24px;" @click="metricClick">
                              <template #icon>
                                <icon-settings/>
                              </template>
                            </a-button>
                            <template #content>
                              <div class="popver-content">
                                <div class="popverTitle">
                                  <span>指标显示设置</span>
                                  <a-button class="title-btn" @click="() => metricVisible = false">
                                    <template #icon>
                                      <icon-close/>
                                    </template>
                                  </a-button>
                                </div>
                                <a-table :columns="configColumns" :pagination="false" size="small" :bordered="false" :data="configData" style="margin: 16px 0;">
                                  <template #name="{ rowIndex }">
                                    <i class="i">{{ rowIndex + 1 }}</i>
                                    <span style="font-weight: 500">{{ configData[rowIndex].name }}&nbsp;</span>
                                  </template>
                                  <template #type="{ rowIndex }">
                                    <a-select v-model="configData[rowIndex].type" :style="{width:'120px',borderRadius: '4px'}">
                                      <a-option value="line">折线图</a-option>
                                      <a-option value="bar">堆积柱状图</a-option>
                                    </a-select>
                                  </template>
                                  <template #showSub="{ rowIndex }">
                                    <a-tooltip>
                                      <template #content>
                                        <div style="width: 220px;">
                                          <div>选择次轴后，该指标将使用图表右侧的次轴</div>
                                          <img src="/src/assets/images/incomplete-date.png" alt="" style="width: 100%;height: 100px;">
                                          <div>
                                            适用于指标数字单位相差较大的场景，通过将不同指标分别放在主轴和次轴，可以更好地观察数据表现
                                          </div>
                                        </div>
                                      </template>
                                      <a-switch v-model="configData[rowIndex].showSub" size="small"/>
                                    </a-tooltip>
                                  </template>
                                </a-table>
                                <div class="popverFooter">
                                  <a-button style="margin-right: 10px;" class="cancel" @click="metricVisible = false">
                                    取消
                                  </a-button>
                                  <a-button type="primary" @click="metricSet">应用</a-button>
                                </div>
                              </div>
                            </template>
                          </a-popover>
                        </div>
                        <div class="title">
                          <div class="title">
                            <a-popover trigger="click" position="left" :popup-visible="guidLineVisible" :popup-offset="130" :show-arrow="false">
                              <a-button style="width: 24px;height: 24px;" @click="guidLineClick">
                                <template #icon>
                                  <icon-settings/>
                                </template>
                              </a-button>
                              <template #content>
                                <div class="popver-content" style="width: 436px;">
                                  <div class="popverTitle">
                                    <span>
                                      辅助线设置
                                      <a-tooltip content="在 Y 轴主轴添加辅助线" position="top">
                                        <icon-info-circle/>
                                      </a-tooltip>
                                    </span>
                                    <a-button class="title-btn" @click="() => guidLineVisible = false">
                                      <template #icon>
                                        <icon-close/>
                                      </template>
                                    </a-button>
                                  </div>
                                  <div class="popverContent">
                                    <a-button v-if="guidSetList.length === 0" type="text" @click="addGuidList">
                                      <template #icon>
                                        <icon-plus/>
                                      </template>
                                      辅助线
                                    </a-button>
                                    <div v-for="(item,index) in guidSetList" :key="index" class="marklineItem">
                                      <a-form ref="guidRef" :model="item" auto-label-width>
                                        <div class="marklineConfig">
                                          <a-input v-model:model-value="item.lineName" style="width: 164px; margin-right: 8px; margin-bottom: 8px;" placeholder="辅助线名称" allow-clear/>
                                          <a-select v-model:model-value="item.lineType" style="width: 98px;height: 32px; margin-right: 8px;">
                                            <a-option value="solid">
                                              <span class="solidLine"></span>
                                              实线
                                            </a-option>
                                            <a-option value="dashed">
                                              <span class="dashedLine"></span>
                                              虚线
                                            </a-option>
                                          </a-select>
                                          <a-color-picker v-model:model-value="item.lineColor"/>
                                          <div class="markLineValue">
                                            <a-form-item field="lineValue" label="数值" :hide-asterisk="true" style="margin-bottom: 0;" required :rules="[{ required: true, message: '请输入数值' }]">
                                              <a-input-number v-model:model-value="item.lineValue" style="width: 124px;margin-left: 8px;"/>
                                            </a-form-item>
                                            <!-- <span style="line-height: 32px;">数值</span>
                                            <a-input-number v-model:model-value="item.lineValue" style="width: 124px;margin-left: 8px;"/> -->
                                          </div>
                                          <a-checkbox v-model:model-value="item.showValue" style="height: 32px;">
                                            显示数值
                                          </a-checkbox>
                                        </div>
                                      </a-form>
                                      <div class="marklineAction">
                                        <a-tooltip :content="guidSetList.length === 10 ? '数量已达上限' : '添加'" position="top">
                                          <a-button :disabled="guidSetList.length === 10" class="btn-24" style="margin-right: 8px;" @click="addLineItem(index)">
                                            <template #icon>
                                              <icon-plus-circle/>
                                            </template>
                                          </a-button>
                                        </a-tooltip>
                                        <a-tooltip content="删除" position="top">
                                          <a-button class="btn-24" @click="deleteLineItem(index)">
                                            <template #icon>
                                              <icon-minus-circle/>
                                            </template>
                                          </a-button>
                                        </a-tooltip>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="popverFooter">
                                    <a-button style="margin-right: 10px;" class="cancel" @click="guidLineVisible = false">取消
                                    </a-button>
                                    <a-button type="primary" :disabled="guidSetList.length === 0" @click="guideSet">
                                      应用
                                    </a-button>
                                  </div>
                                </div>
                              </template>
                            </a-popover>
                          </div>
                        </div>
                        <div class="title">
                          <span>最大值</span>
                          <a-input-number v-model="yMax" :style="{width:'70px',height:'25px',marginLeft:'16px'}"/>
                        </div>
                        <div class="title">
                          <span>最小值</span>
                          <a-input-number v-model="yMin" :style="{width:'70px',height:'25px',marginLeft:'16px'}"/>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                  <a-collapse-item key="3" :disabled="!eventData.groupsDesc.length">
                    <template #header>
                      <a-popover title="饼状分布" position="left" :content-style="{width: '260px'}">
                        <div style="display: flex;align-items: center;">
                          <img class="option-icon" src="/icon/pie.svg" alt=""/>
                          饼状分布
                        </div>
                        <template #content>
                          展示各个指标的分组占比情况 <br>
                          <img :src="pieTrend" alt="" style="width: 100%;height: 90%;"/> <br>
                          *当有分组项或事件拆分，可用
                        </template>
                      </a-popover>
                    </template>
                    <div class="spanitem">
                      <div class="label">
                        <div class="title">
                          <span>显示百分比</span>
                        </div>
                        <div class="title">
                          <span>同屏展示上限</span>
                        </div>
                        <div class="title">
                          <span>分组</span>
                          <a-tooltip content="未被选中的分组值将被计入“其他”中" position="top">
                            <icon-info-circle/>
                          </a-tooltip>
                        </div>
                      </div>
                      <div class="label">
                        <div class="title">
                          <a-switch v-model="showRate" size="small"/>
                        </div>
                        <div class="title">
                          <a-select v-model:model-value="limitScreen" :default-value="2" :style="{width:'90px'}" @change="limitScreenChange">
                            <a-option :value="1">1个</a-option>
                            <a-option :value="2">2个</a-option>
                            <a-option :value="4">4个</a-option>
                            <a-option :value="6">6个</a-option>
                          </a-select>
                        </div>
                        <div class="title">
                          <a-select v-model="groupNum" :style="{width:'90px'}" @change="groupsNumChange">
                            <a-option :value="10">前10项</a-option>
                            <a-option :value="15">前15项</a-option>
                            <a-option :value="20">前20项</a-option>
                            <a-option :value="30">前30项</a-option>
                            <a-option :value="50">前50项</a-option>
                          </a-select>
                        </div>
                      </div>
                    </div>
                  </a-collapse-item>
                </a-collapse>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
    <!-- 保存弹窗 -->
    <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">保存报表</div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <a-form-item field="dashboard" label="保存至看板">
          <dashboard-select v-model:selected="form.dashboard" type="dashboard" @change="dashboardChange"/>
        </a-form-item>
        <a-form-item field="description" label="备注">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click.stop="handleSaveCancel">取消</a-button>
        <a-button type="primary" :loading="saveLoading" @click="saveReport">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {distribution, pieTrend, stacked, total, trend} from "@/views/dashboard/components/img";
import {getAnalyticsDataDownload, saveAnalyseReportList} from "@/api/analyse/api";
import {cloneDeep, debounce, isEmpty} from "lodash";
import {Indicator, ReportAnalyseModel} from "@/api/analyse/type";
import {queryOpearationReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import {analyseStore, toolStore} from '@/store';
import eventEcharsVue from "@/views/analyse/event/components/eventEchars.vue";
import eventTableVue from "@/views/analyse/event/components/eventTable.vue";
import {LocalStorageEventBus} from "@/types/event-bus";
import {detailReport} from "@/api/report/api";
import {useRoute} from 'vue-router';
import {updateDisplayNames} from '@/views/analyse/components/util/mapNameChange';
import {compressDateRangeToEndDate} from "@/utils/dateUtil";
import {ChartType, IndicatorType, TimeParticleSize} from "@/api/enum";
import {removeEnumList, verifyLeastIndicator} from "@/views/analyse/components/util/verify";
import analysisIndex from "./components/analysisIndex.vue"
import globalFilter from "../components/operation/globalFilter.vue"
import groupItem from "../components/operation/groupItem.vue"
import analyseHeader from "../components/analyseHeader.vue"
import dateSet from "../components/dateSet.vue"

// import compareEchart from "./components/compareEchart.vue";
// import eventDataJson from "./components/json";
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const paramsDataStorage = useSessionStorage("application-params-data", {});
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
const route = useRoute();
const reportId = ref(route.query.code);
const analyseData = analyseStore();
const toolData = toolStore();
const eventData = ref({
  y: [],
  groupsDesc: []
})
const headerInfo = reactive({
  title: '运营分析',
  img: '/icon/topMenu/event.svg',
  tips: '分析某段时间内，某个运营指标的整体趋势情况',
  root: '#applicationRoot',
  showUpdateTime: true,
  showAppSelect: true,
  showDownloadData: true,
  showMoreValue: true,
  multipleApp: false
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// 查询暂无requestId返回
const requestId = ref()
// ------left start
const loading = ref(false)
const totalNum = ref(0)
const isReset = ref(false)
// 传参数组

const queryParam = reactive({
  subject: '',
  indicators: [] as Indicator[], // 查询指标
  filter: {}, // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  }, // 查询日期范围
  timeParticleSize: TimeParticleSize.DAY1, // 时间粒度
  comparedDateList: [], // 对比周期
  firstDayDfWeek: null, // 周几作为一周开始
  tableArrangeType:'event'
})
const dataLoading = ref(false)
const analysisIndexData = ref<any>([]) // 分析指标传参
const globalFilterData = ref({}) // 全局筛选传参
const groupData = ref<any>([]) // 分组项传参
const indicatorList = ref<any>([])
const eventBus = useEventBus('eventList');

// 指标设置popup
const metricVisible = ref(false)
const configColumns = ref<any>([
  {
    title: '指标名称',
    dataIndex: 'name',
    slotName: 'name',
    width: 210,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '图表类型',
    dataIndex: 'type',
    slotName: 'type'
  },
  {
    title: '次轴展示',
    dataIndex: 'showSub',
    slotName: 'showSub'
  }
])
const configData = ref<any>([])
// 辅助线设置
const guidLineVisible = ref(false)

const guidSetList = ref<any>([])

const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const saveFormRef = ref();

const handleAttrBus = debounce(async () => {
  const eventLists = queryParam.indicators.flatMap(item =>
      item.eventList
  );
  eventBus.emit(eventLists);
  form.value.name = queryParam.indicators.length > 1
      ? `${queryParam.indicators[0]?.displayName}等${queryParam.indicators.length}个`
      : `${queryParam.indicators[0]?.displayName}`;
}, 300)
// 分析指标传参
const indicatorsChange = (v) => {
  queryParam.indicators = cloneDeep(v)
  handleAttrBus();
  paramsDataStorage.value = cloneDeep(queryParam);
}

// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
  paramsDataStorage.value = queryParam;
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
  paramsDataStorage.value = queryParam;
}

const activeKey = ref(['1', '2'])
const eventtable = ref()
const eventechars = ref()
const isChartType = ref<string>('trend')
const tableTypeName = ref('按指标')
const tableRadio = ref<string>('hb')
const reportData = ref()

const selectChart = (type: string) => {

  isChartType.value = type
  if (type === 'trend') {
    activeKey.value = ['1', '2']
  } else if (type === 'pieTrend') {
    activeKey.value = ['1', '3']
  }
  tableRadio.value = type === 'total' ? 'lj' : ''
  eventechars.value?.changeType(type)
}
const timeOut = ref()

const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (item.calcuSymbol !== 'ex' && item.calcuSymbol !== 'nex' && !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取三个需要验证的列表
  const firstList = params.indicators
      .filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.indicators
      .flatMap(item =>
          item.eventList
              .filter(event => event.filter?.filters?.length > 0)
              .map(event => event.filter.filters)
      );
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList);
};
// 创建指标项的工具函数
const createIndicatorItem = (indicator) => ({
  indicatorName: indicator?.name,
  indicatorDisplayName: indicator?.displayName,
  indicatorCode: indicator?.code,
  type: indicator?.objectType,
  eventType: indicator?.type,
  filter: {}
});
// 创建分析指标数据项的工具函数
const createAnalysisItem = (indicator) => ({
  name: indicator?.name,
  code: indicator?.code,
  type: IndicatorType.OPERATION,
  isBasic: true,
  displayType: {},
  displayName: `${indicator?.displayName}`,
  unitName: '',
  eventList: [createIndicatorItem(indicator)],
  filter: {}
});
// 主体改变
const subjectChange = async (v) => {
  queryParam.subject = v
  toolData.updateTemporaryList([])
  indicatorList.value = (await toolData.fetchOperationModalList(v)).flatMap(category => category.items || []);
  const firstIndicator = indicatorList.value[0];
  analysisIndexData.value = [{
    name: firstIndicator?.name,
    code: firstIndicator?.code,
    type: IndicatorType.OPERATION,
    isBasic: true,
    displayType: {},
    displayName: `${firstIndicator?.displayName}`,
    unitName: '',
    eventList: [createIndicatorItem(firstIndicator)],
    filter: {},
    ignoreCalc: false
  }];
}

// 计算
const computedData = () => {
  loading.value = true

  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.warning('事件查询超时，请重试！')
    }
  }, 60*1000)
  const hasInvalidDisplayName = queryParam.indicators.some(indicator => !indicator.displayName);
  if (hasInvalidDisplayName) {
    Message.error('请输入指标名');
    loading.value = false;
    return;
  }
  if (!verifyLeastIndicator(queryParam)) {
    Message.error('请至少选中一个指标计算');
    loading.value = false;
    return;
  }
  if (paramsVerify(queryParam)) {
    requestId.value = queryOpearationReportData(removeEnumList(queryParam));
  } else {
    Message.error('筛选条件参数错误')
    loading.value = false
  }
}

watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (newData.result === null) {
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }
  if (timeOut.value) {
    clearTimeout(timeOut.value);
  }
  eventData.value = newData.result
  // eventData.value = eventDataJson
  paramsDataStorage.value = queryParam;
  sessionStorage.setItem('application-subject-code', queryParam.subject);
  totalNum.value = newData?.result?.totalNum
  // totalNum.value = eventDataJson.totalNum
  const data = [] as any
  eventData.value.y.forEach(item => {
    data.push({
      name: item.displayName
    });
  });
  configData.value = data.map(item => {
    return {
      name: item.name,
      type: 'line',
      showSub: false
    }
  })
  loading.value = false
  if(reportData.value?.queryParam?.timeParticleSize === TimeParticleSize.TOTAL || queryParam?.timeParticleSize === TimeParticleSize.TOTAL){
    selectChart('distribution')
  }
})

const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  saveVisible.value = false;
}

const saveLoading = ref(false)
const editReportData = reactive({
  name: '',
  description: ''
})

const dashboardData = ref()
const dashboardChange = (v) => {
  dashboardData.value = v
}
// 保存报表
const saveReport = debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    return;
  }
  saveLoading.value = true;
  try {
    const saveParams = {
      model: ReportAnalyseModel.APPLICATION,
      reportId: reportId.value || undefined,
      dashboard: form.value.dashboard,
      name: editReportData?.name || form.value.name,
      description: editReportData?.description || form.value.description,
      queryParam,
      chartParams: [],
      chartType: ChartType.TREND
    };
    await saveAnalyseReportList(
        saveParams.model,
        saveParams.reportId,
        saveParams.dashboard,
        saveParams.name,
        saveParams.description,
        saveParams.queryParam,
        saveParams.chartParams,
        saveParams.chartType
    );
    dashboardSelected.value = { ...dashboardData.value }
    const successMsg = isEmpty(form.value.dashboard) ? "报表已保存" : "报表已保存并添加至看板";
    Message.success(successMsg);
    eventBus.emit('saveReport');
    if (!reportId.value) {
      saveVisible.value = false;
    }
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300);
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  saveReport()
}
const toSave = () => {
  if (reportId.value) {
    saveReport()
  } else {
    saveVisible.value = true
  }
}
// ---left end

// ------right start

const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
const showDrawer = ref<boolean>(false)

const showlabel = ref<boolean>(false)
const showRate = ref<boolean>(false)
const yMax = ref<number | null>(null)
const yMin = ref<number | null>(null)
const limitScreen = ref(2)
const groupNum = ref(10)

// 按小时 只能选一天
const minutesVerification = () => {
  const timeType = queryParam.timeParticleSize
  if (timeType === 'm1' || timeType === 'm5' || timeType === 'm10') {
    Message.warning('按分钟查看，时间范围一次最多展示1天')
    const compressDateRange = compressDateRangeToEndDate(boardDate.value);
    boardDate.value = compressDateRange;
    queryParam.dateRange = compressDateRange;
  }
}
const timeParticle = ref({
  timeParticleSize: '',
  firstDayDfWeek: null
})
const timeChange = (v) => {
  queryParam.timeParticleSize = v.timeParticleSize

  if (v.timeParticleSize === TimeParticleSize.TOTAL) {
    // isChartType.value = 'distribution'
    selectChart('distribution')
  }
  queryParam.firstDayDfWeek = v.firstDayDfWeek
  timeParticle.value = v
  minutesVerification()
  computedData()
}
const subjectData = ref<any>()

const datePick = (date: any) => {
  boardDate.value = date
  queryParam.dateRange = date;
  minutesVerification()
  computedData()
};

const handleChangeTable = (value: string) => {
}


const handleChangeCollapse = (key: any) => {
  metricVisible.value = false
  guidLineVisible.value = false
  // 可视化配置-趋势图-饼图分布-联动效果
  const index2 = key.indexOf('2');
  const index3 = key.indexOf('3');

  if (index2 !== -1 && index3 !== -1) {
    const indexToRemove = Math.min(index2, index3);
    activeKey.value.splice(indexToRemove, 1); // 删除索引靠前的那一项
  }
  if (activeKey.value.includes('2') && !activeKey.value.includes('3')) {
    isChartType.value = 'trend'
    eventechars.value.changeType('trend')

  } else if (activeKey.value.includes('3') && !activeKey.value.includes('2')) {
    isChartType.value = 'pieTrend'
    eventechars.value.changeType('pieTrend')

  }

}

const onCollapse = () => {
  showDrawer.value = !showDrawer.value;
};
const metricClick = () => {
  metricVisible.value = true
  guidLineVisible.value = false
}
const guidLineClick = () => {
  metricVisible.value = false
  guidLineVisible.value = true
}
// 添加辅助线
const addGuidList = () => {
  guidSetList.value.push(
      {
        lineName: '',
        lineType: 'solid',
        lineColor: '#8E8E8E',
        lineValue: null,
        showValue: true
      }
  )
}
const addLineItem = (index: number) => {
  const newItem = {
    lineName: '',
    lineType: 'solid',
    lineColor: '#8E8E8E',
    lineValue: null,
    showValue: true
  }
  guidSetList.value.splice(index + 1, 0, newItem);
}
const deleteLineItem = (index: number) => {
  guidSetList.value.splice(index, 1);
}
const guidRef = ref();
// 应用
const guideSet = () => {
  // 校验
  Promise.all(guidSetList.value.map((item, index) => {
    return guidRef.value[index].validate();
  })).then(results => {
    const allValid = results.every(result => !result);
    if (allValid) {
      eventechars.value.freshData([], guidSetList.value)
      guidLineVisible.value = false
    }
  })
}
// 指标显示应用
const metricSet = () => {
  eventechars.value.freshData(configData.value)
  metricVisible.value = false
}
const limitScreenChange = () => {
  eventechars.value.changeType('pieTrend')
}
const groupsNumChange = () => {
  eventechars.value.changeType('pieTrend')
}

const tableTypeChange = (v) => {
  queryParam.tableArrangeType = v
  tableTypeName.value = v === 'event' ? '按指标' : '按日期'
  // eventtable.value.freshData(v)
}
const showTotal = () => {
  eventtable.value.showTotal()
}
const importTable = (name?: string) => {
  eventtable.value.exportXlsx(queryParam.dateRange, name)
}
const downloadAllData = () => {
  getAnalyticsDataDownload(ReportAnalyseModel.APPLICATION, queryParam).then(res => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', '运营分析_全量数据.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  })
}

// ---right end


const showBodyLeft = ref(true)

const getIndexList = async (code: string) => {
  toolData.updateTemporaryList([])
  indicatorList.value = (await toolData.fetchOperationModalList(code)).flatMap(category => category.items || []);
}


const init = async () => {
  dataLoading.value = false
  checkedSessionGroups.value = []
  const sourceData = await analyseData.fetchSubjectLists(['application'])
  // 结构调整，对象分组
  const flattenedList = Object.values(sourceData).flat();
  const subjectCode = sessionStorage.getItem('application-subject-code');
  if (flattenedList?.length) {
    subjectData.value = sourceData
    const {code} = flattenedList[0];
    queryParam.subject = subjectCode || code
    await getIndexList(queryParam.subject)
    await Promise.all([
      analyseData.fetchOperationFilterLists(queryParam.subject),
      analyseData.fetchOperationGroupLists(queryParam.subject)
    ]);
  }
  let parsedData = {} as any
  if (reportId.value) {
    await detailReport(reportId.value as string).then(res => {
      parsedData = res.queryParam
      parsedData.dateRange = boardDate.value
      reportData.value = res
      editReportData.name = res?.name
      editReportData.description = res?.description
      queryParam.tableArrangeType = res.queryParam?.tableArrangeType || 'event'
      tableTypeName.value = res.queryParam?.tableArrangeType === 'event'? '按指标' : '按日期'
    })
  }else{
    tableTypeName.value = '按指标'
  }
  // 如果没有从报表获取到数据，则尝试从会话存储获取
  if (Object.keys(parsedData).length === 0) {
    parsedData = paramsDataStorage.value;
  }
  // 处理分析指标
  if (parsedData.indicators && parsedData.indicators.length > 0) {
    analysisIndexData.value = parsedData.indicators.map(item => {
      return {
        ...item,
        unitName: item.unitName,
        eventList: item.eventList
      }
    })
  }
  // 更新可能被修改的displayName
  updateDisplayNames(analysisIndexData.value)
  if (!analysisIndexData.value.length) {
    const firstIndicator = indicatorList.value[0];
    analysisIndexData.value.push(createAnalysisItem(firstIndicator));
  }

  queryParam.indicators = analysisIndexData.value.map(item => {
    return {
      displayName: item.displayName,
      displayType: item.displayType,
      isAddUp:item.isAddUp,
      name: item.name,
      type: item.type,
      isBasic: item.isBasic,
      eventList: item.eventList,
      filter: item.filter,
      formula: item?.formula,
      ignoreCalc: item.ignoreCalc
    }
  })
  form.value.name = analysisIndexData.value.length > 1
      ? `${analysisIndexData.value[0]?.displayName}等${analysisIndexData.value.length}个`
      : analysisIndexData.value[0]?.displayName;
  // 处理事件筛选
  if (parsedData.filter) {
    globalFilterData.value = parsedData.filter
    queryParam.filter = parsedData.filter
  }
  // 处理分组项
  if (parsedData.aggregates && parsedData.aggregates.length > 0) {
    groupData.value = cloneDeep(parsedData.aggregates)
    queryParam.aggregates = cloneDeep(parsedData.aggregates)
  }
  // 处理时间
  if (parsedData.dateRange) {
    boardDate.value = parsedData.dateRange
    queryParam.dateRange = parsedData.dateRange
  }else{
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    boardDate.value = date
    queryParam.dateRange = date
  }

  // 处理dateSet
  timeParticle.value = {
    timeParticleSize: parsedData.timeParticleSize ? parsedData.timeParticleSize : TimeParticleSize.DAY1,
    firstDayDfWeek: parsedData.firstDayDfWeek ? parsedData.firstDayDfWeek : null
  }
  queryParam.timeParticleSize = timeParticle.value.timeParticleSize
  queryParam.firstDayDfWeek = timeParticle.value.firstDayDfWeek

  // await handleAttrBus()
  dataLoading.value = true
  setTimeout(() => {
    computedData()
  }, 1000)
}
init()

localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    paramsDataStorage.value = {};
    sessionStorage.removeItem('application-subject-code');
    init()
  }
})
const reset = () => {
  isReset.value = true
  analysisIndexData.value = []
  globalFilterData.value = {}
  queryParam.filter = {}
  groupData.value = []
  paramsDataStorage.value = {}
  init()
}
</script>

<style scoped lang="less">
#applicationRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;

}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.btn-24 {
  width: 24px;
  height: 24px;
  padding: 4px;
  border-radius: var(--tant-border-radius-medium);
  // background-color: var(--tant-secondary-color-secondary-transp-hover);
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  align-items: center;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  //padding-bottom: 18px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-content::before, .right-content::after {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-content::after {
  right: -24px;
  left: auto;
}

.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;

}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}

.popver-content {
  width: 496px;

  .popverTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 4px 16px 16px;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header5-medium);

    .title-btn {
      color: var(--tant-text-gray-color-text1-2);
      background-color: transparent;
      border: none;
      padding: 8px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
      }
    }
  }

  .i {
    display: inline-block;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    color: var(--tant-bg-white-color-bg1-1);
    font-size: 12px;
    font-style: normal;
    line-height: 24px;
    text-align: center;
    background-color: var(--tant-secondary-color-secondary-default);
    border-radius: 4px;
    transition: all .3s;
    margin-right: 12px;
  }

  .popverFooter {
    display: flex;
    justify-content: flex-end;
    margin-right: -16px;
    margin-left: -16px;
    padding-top: 16px;
    padding-right: 24px;
    border-top: 1px solid var(--tant-border-color-border1-1);

    button {
      border-radius: 4px;
    }
  }

  .popverContent {
    min-height: 190px;
    max-height: 400px;
    padding: 3px 0 0 3px;
    overflow: auto;

    button:hover {
      background: transparent;
    }

    .marklineItem {
      display: flex;
      justify-content: space-between;

      :deep(.arco-form-item-label-col) {
        padding-right: 4px;
      }

      :deep(.arco-form-item-message) {
        padding-left: 8px;
      }

      .marklineConfig {
        display: flex;
        flex-wrap: wrap;
      }

      .markLineValue {
        display: flex;
        margin-right: 8px;
        margin-bottom: 8px;
      }

      .marklineAction {
        display: flex;
        align-items: center;
        height: 32px;
        margin-left: 4px;

        button {
          background-color: transparent;
        }

        button:nth-child(1):hover {
          background-color: var(--tant-secondary-color-secondary-transp-hover);
        }

        button:nth-child(2):hover {
          background-color: var(--tant-status-danger-color-danger-fill-hover);
        }
      }
    }
  }
}

.solidLine {
  display: inline-block;
  width: 30px;
  height: 6px;
  margin-right: 4px;
  border-top: 1px solid;
}

.dashedLine {
  display: inline-block;
  width: 30px;
  height: 6px;
  margin-right: 4px;
  background: linear-gradient(to left, transparent 0%, transparent 50%, var(--tant-text-gray-color-text1-3) 50%, var(--tant-text-gray-color-text1-3) 100%);
  background-repeat: repeat-x;
  background-size: 10px 1px;
}
</style>