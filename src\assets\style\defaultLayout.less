.page {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);

  .page-head {
    .arco-picker,
    .arco-button,
    .arco-input-wrapper,
    .arco-textarea,
    .arco-select-view-multiple,
    .arco-select-view-single {
      border: 0;
    }

    .arco-btn-size-medium {
      border: 0;
      border-radius: var(--tant-border-radius-medium);
    }

    display: flex;
    height: 38px;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    background-color: var(--tant-bg-gray-color-bg2-1);


    .title {
      display: flex;
      flex: 1 1;
      align-items: flex-end;
      justify-content: flex-start;
      height: 38px;
      width: auto;
      margin-right: 48px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 38px;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header3-medium);
    }

    .filter {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;

      .filter-item {
        display: flex;
        align-items: center;
      }
    }
  }

  .page-head-multiple-fold,
  .page-head-multiple-unfold {
    .arco-btn-size-medium {
      border: 0;
      border-radius: var(--tant-border-radius-medium);
    }

    display: flex;
    height: 38px;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .title {
      display: flex;
      flex: 1 1;
      align-items: flex-end;
      justify-content: flex-start;
      height: 38px;
      width: auto;
      margin-right: 48px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 38px;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header3-medium);
    }

    .filter {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background-color: white;
      gap: 8px;
      padding: 8px 8px;
      border-radius: 9px 9px 0 0;

      .filter-item {
        display: flex;
        align-items: center;

        .btn {
          padding: 0 9px;
        }
      }
    }
  }

  .page-head-multiple-fold {
    .filter{
      background: transparent;
    }
  }

  .sub-header {
    display: flex;
    flex-direction: column;
    align-items: end;
    justify-content: flex-end;
    border-radius: 9px;
    .arco-btn-size-medium {
      border: 0;
      border-radius: var(--tant-border-radius-medium);
    }
    .filter-line {
      display: flex;
      align-items: center;
      background-color: white;
      gap: 8px;
      padding: 8px 8px;

      .filter-item {
        display: flex;
        align-items: center;

        .btn {
          padding: 0 9px;
        }
      }
    }
  }

  .page-body {
    display: flex;
    flex-direction: column;
    padding: 8px 16px;
    width: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);
    height: calc(100% - 62px);
    overflow: auto;
    margin-top: 24px;
  }
}



