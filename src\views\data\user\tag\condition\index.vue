<template>
  <div class="single-page">
    <a-page-header
        class="header"
        :title="_.isEmpty(tagCode)?'创建条件标签':'编辑条件标签'"
        @back="cancelConfirmVisible = true">
      <template #extra>
        <a-space>
          <a-button @click="cancelConfirmVisible = true">取消</a-button>
          <a-button type="primary" @click="pushSave">保存</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="container">
      <div class="block rule-block">
        <div class="block-title">
          标签规则
          <div class="description">
            用户将优先匹配至满足条件且顺序靠前的标签值中
          </div>
        </div>
        <div class="rule-item">
          <div class="title">
            数据来源
          </div>
          <select-data-source v-model:data-source="dataSource" disable-storage/>
        </div>
        <div class="user-condition">
          <div class="tag-value-list">
            <draggable
                :list="tagConditionData"
                group="tag-value-list"
                item-key="key">
              <template #item="{ element,index }">
                <div
                    class="tag-value-item"
                    :class="tagActiveKey === element.key? 'tag-value-item-active' : ''"
                    @click="selectTag(element.key)">
                  <icon-drag-dot-vertical v-show="true" class="drag-icon"/>
                  <span v-show="true">
                    <icon-drag-dot-vertical style="width: 14px;height: 14px;opacity: 0;"/>
                  </span>
                  <a-tooltip position="top" :content="element.tagValue">
                    <span class="tag-value-text" :style="tagActiveKey === element.key?'color: var(--tant-primary-color-primary-active);': ''">
                      {{ element.tagValue }}
                    </span>
                  </a-tooltip>
                  <a-tooltip position="top" content="复制">
                    <icon-copy @click="copyTag(element.key)"/>
                  </a-tooltip>
                  <a-tooltip position="top" content="删除">
                    <icon-delete style="margin-left: 6px;"  @click.stop="deleteTag(element.key)"/>
                  </a-tooltip>
                </div>
              </template>
            </draggable>
            <div class="tag-value-add" @click="addTag">
              <button type="button" class="add-btn">
                <icon-plus/>
                添加
              </button>
            </div>
          </div>
          <div class="tag-rule">
            <div class="info-item">
              <div class="info-item-label">
                标签值
              </div>
              <div class="info-item-content">
                <a-input v-model="tagConditionData[tagActiveIndex].tagValue" placeholder="请输入" :style="{ width: '240px' }"></a-input>
              </div>
            </div>
            <div class="info-item">
              <div class="info-item-label">
                条件
              </div>
              <div class="info-item-content">
                <cluster-index :ref="el => tagConditionRefs[tagActiveIndex] = el" :only-evt="true" :user-filter="tagConditionData?.[tagActiveIndex]?.condition" @change="filtersChange"/>
                <a-dropdown trigger="hover" :popup-translate="[18, 0]">
                  <div class="ta-filter-button">
                    <div class="ta-filter-button-icon">
                      <icon-plus/>
                    </div>
                    添加筛选条件
                  </div>
                  <template #content>
                    <a-doption @click="addEvent('sequentially')">做过/没做过的事件</a-doption>
                    <a-doption @click="addEvent('inOrder')">依次/没依次做过的事件</a-doption>
                    <a-doption @click="addEvent('userProperty')">用户属性满足</a-doption>
                  </template>
                </a-dropdown>
              </div>
            </div>
            <div class="info-item">
              <div class="info-item-label">
                说明 <span class="description">(选填)</span>
              </div>
              <div class="info-item-content">
                <a-textarea
                    v-model="tagConditionData[tagActiveIndex].note"
                    placeholder="请输入说明"
                    show-word-limit
                    :max-length="{length:200}" :rows="4"
                    :style="{ width: '400px' }">
                </a-textarea>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="block info-block">
        <div class="block-title">
          基本信息
        </div>
        <div class="tag-info">
          <a-form ref="formRef" :model="form" :rules="rules" class="form">
            <a-form-item
                field="appId"
                validate-trigger="blur"
                label="应用ID"
                label-col-flex="90px"
            >
              <a-input v-model="appId" disabled :style="{ width: '384px' }"></a-input>
            </a-form-item>
            <a-form-item
                field="displayName"
                validate-trigger="blur"
                label="标签名称"
                label-col-flex="90px"
            >
              <a-input v-model="form.displayName" placeholder="请输入标签名称" :style="{ width: '384px' }"></a-input>
            </a-form-item>
            <a-form-item field="status" label="启用状态" label-col-flex="90px">
              <a-switch v-model="form.status" :checked-value="1" :unchecked-value="0"/>
            </a-form-item>
            <!--            <a-form-item-->
            <!--                field="timeZone"-->
            <!--                validate-trigger="blur"-->
            <!--                label="时区"-->
            <!--                label-col-flex="90px"-->
            <!--            >-->
            <!--              <time-zone-select v-model:time-zone="form.timeZone" disable-storage/>-->
            <!--            </a-form-item>-->
            <!--            <a-form-item-->
            <!--                field="calculateCron"-->
            <!--                validate-trigger="blur"-->
            <!--                label="定时计算"-->
            <!--                label-col-flex="90px"-->
            <!--            >-->
            <!--              <a-switch v-model="showCalculateCron"/>-->
            <!--              <a-input v-if="showCalculateCron" v-model="form.calculateCron" class="cron-input" placeholder="请输入计算周期"/>-->
            <!--            </a-form-item>-->
            <a-form-item
                label-col-flex="90px"
            >
              <template #label>备注 <span class="description">(选填)</span>
              </template>
              <a-textarea v-model="form.remark" placeholder="请输入备注" show-word-limit :rows="3" :max-length="{length:200}" :style="{ width: '384px' }"></a-textarea>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    <a-modal v-model:visible="cancelConfirmVisible" :align-center="false" title-align="start" :width="300" :top="140" @cancel="cancelConfirmVisible = false">
      <template #title>
        退出编辑
      </template>
      <div>用户标签编辑尚未保存。确认退出编辑吗？</div>
      <template #footer>
        <div style="display: flex;justify-content: flex-end;">
          <a-button style="margin-right: 8px;" @click="cancelConfirmVisible = false">继续编辑</a-button>
          <a-button type="primary" @click="router.back()">退出</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {getUserTagDetail, updateUserTagItem, userTagAdd} from "@/api/setting/api";
import selectDataSource from "@/components/selected-data-source/index.vue"
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {Message} from "@arco-design/web-vue";
import {toolStore} from '@/store';
import {ROUTE_NAME} from "@/router/constants";
import _ from "lodash";
import draggable from "vuedraggable";
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue";
import {useSessionStorage} from "@vueuse/core";

const appId = useSessionStorage('app-id', '')?.value;
/**
 * 事件属性选择器使用
 * todo 优化
 */
const toolData = toolStore();
const modelLists = ref<any>([])
// 路由
const route = useRoute()
const router = useRouter()
/**
 * 标签数据
 */
const tagConditionData = ref([
  {
    key: 0,
    tagValue: '标签值1',
    condition: {
      // logicalOperationType: 'and', // 事件条件与用户条件且/或
      // eventIsAnd: 'and', // 事件条件且/或
      // userIsAnd: 'and', // 用户条件且/或
      // singleList: [],
      // sequentiallyList: [],
      // userList: []
    },
    note: ''
  }
]);

/**
 * 数据源
 */
const dataSource = ref<string>('event');

/**
 * 当前选中的tag的key
 */
const tagActiveKey = ref(0)
/**
 * 当前选中的tag的index
 */
const tagActiveIndex = computed(() => {
  const index = tagConditionData.value?.findIndex(item => item.key === tagActiveKey.value);
  if (index === -1) {
    tagActiveKey.value = tagConditionData.value[0].key;
    return 0;
  }
  return index;
})
/**
 * 用户标签编码
 */
const tagCode = ref<string>()
/**
 * 展示计算周期选择器
 */
const showCalculateCron = ref<boolean>(false)
/**
 * 群组筛选条件
 */
 const tagConditionRefs = ref<Record<number, any>>({});
/**
 * 退出确认框
 */
const cancelConfirmVisible = ref(false)

/**
 * 创建标签名
 */
function formatCurrentTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `标签${year}${month}${day}_${hours}${minutes}${seconds}`;
}

/**
 * 基本信息表单
 */
const form = ref({
  displayName: formatCurrentTimestamp(),
  timeZone: '+08:00',
  calculateCron: '0 0 1 * * ?',
  remark: '',
  status: 1
})
const rules = {
  displayName: [
    {
      required: true,
      message: '标签名不能为空',
    },
  ],
  timeZone: [
    {
      required: true,
      message: '时区不能为空',
    },
  ]
}

/**
 * 获取标签序号
 */
const getNewTagKey = () => {
  let key = 0;
  while (tagConditionData.value.some(item => item.key === key)) {
    key++;
  }
  return key;
}

/**
 * 新增标签
 */
const addTag = () => {
  if (tagConditionData.value.length >= 20) {
    Message.error('标签值最多20条')
    return
  }
  const tagKey = getNewTagKey();
  tagConditionData.value.push({
    key: tagKey,
    tagValue: `标签值${tagKey + 1}`,
    note: '',
    condition: {}
  })
};

/**
 * 复制标签
 */
const copyTag = (key: number) => {
  if (tagConditionData.value.length >= 20) {
    Message.error('标签值最多20条')
    return
  }
  const tagKey = getNewTagKey();
  const index = tagConditionData.value?.findIndex(item => item.key === key);
  const newItem = {...tagConditionData.value[index > -1 ? index : 0]}
  newItem.key = tagKey
  newItem.tagValue = `标签值${tagKey + 1}`
  tagConditionData.value.push(newItem)
};

/**
 * 删除标签
 */
const deleteTag = (key: number) => {
  if (tagConditionData.value.length <= 1) {
    Message.error('至少保留一个标签值')
    return
  }
  const index = tagConditionData.value?.findIndex(item => item.key === key);
  tagConditionData.value.splice(index, 1)
  if (tagActiveKey.value === key) {
    tagActiveKey.value = tagConditionData.value[0].key
  }
};

/**
 * 选择标签
 */
const selectTag = (key: number) => {
  tagActiveKey.value = key;
}

/**
 * 用户属性条件检查
 */
const paramsVerify = (params) => {
  const filters = params?.filters || [];
  for (const filter of filters) {
    // 如果 calcuSymbol 不是 ex 或 nex，则必须有 thresholds 且长度大于 0
    if (filter.calcuSymbol === 'ex' || filter.calcuSymbol === 'nex' || filter.thresholds?.length) {
      continue;
    }
    return false
  }
  return true;
}

/**
 * 保存标签
 */
async function pushSave() {
  for (const tagCondition of tagConditionData.value) {
    const sequenceConditionLength = tagCondition?.condition?.eventCondition?.sequenceConditionExpressions?.length || 0;
    const singleConditionLength = tagCondition?.condition?.eventCondition?.singleConditionExpressions?.length || 0;
    const userConditionLength = tagCondition?.condition?.userCondition?.filters?.length || 0;
    if (sequenceConditionLength + singleConditionLength + userConditionLength === 0) {
      Message.warning('标签【' + tagCondition.tagValue + '】的条件不能为空，请点击按钮添加！')
      return
    }

    //  用户属性条件校验
    if (userConditionLength && !paramsVerify(tagCondition?.condition?.userCondition)) {
      Message.warning('标签【' + tagCondition.tagValue + '】的用户属性筛选参数错误！')
      return
    }
  }

  const data = {
    display_name: form.value.displayName,
    type: 1,
    timeZone: form.value.timeZone,
    calculateCron: showCalculateCron.value ? form.value.calculateCron : '',
    conditions: tagConditionData.value,
    dataType: 'string',
    dataSource: dataSource.value,
    note: form.value.remark,
    status: form.value.status
  }

  if (_.isEmpty(tagCode.value)) {
    // 新增
    await userTagAdd(data).then(res => {
      if (Object.keys(res).length > 0) {
        Message.success('保存成功')
        router.push({
          name: ROUTE_NAME.USER_TAG
        })
      }
    }).catch(error => {
      console.error(error)
    })
  } else {
    // 编辑
    data.code = tagCode.value
    await updateUserTagItem(data).then(res => {
      if (Object.keys(res).length > 0) {
        Message.success('保存成功')
        router.push({
          name: ROUTE_NAME.USER_TAG
        })
      }
    }).catch(error => {
      console.error(error)
    })
  }
}

/**
 * 添加筛选条件
 * @param type 条件类型
 */
 const addEvent = async (type: string) => {
  const currentRef = tagConditionRefs.value[tagActiveKey.value];
  if (!currentRef) return;
  switch (type) {
    case 'sequentially':
      currentRef.add();
      break;
    case 'inOrder':
      currentRef.addDone();
      break;
    case 'userProperty':
      currentRef.addUser();
      break;
    default:
      break;
  }
}

/**
 * 标签条件变更
 */
 function deepMergeCondition(oldCondition, newCondition) {
  // 合并 userCondition
  const merged = {
    ...oldCondition,
    ...newCondition,
    userCondition: {
      ...oldCondition.userCondition,
      ...newCondition.userCondition
    },
    eventCondition: {
      ...oldCondition.eventCondition,
      ...newCondition.eventCondition,
      singleConditionExpressions: (newCondition.eventCondition?.singleConditionExpressions || []).map((expr, idx) => ({
        ...((oldCondition.eventCondition?.singleConditionExpressions || [])[idx] || {}),
        ...expr,
        filter: {
          ...(((oldCondition.eventCondition?.singleConditionExpressions || [])[idx] || {}).filter || {}),
          ...(expr.filter || {})
        }
      })),
      sequenceConditionExpressions: (newCondition.eventCondition?.sequenceConditionExpressions || []).map((expr, idx) => ({
        ...((oldCondition.eventCondition?.sequenceConditionExpressions || [])[idx] || {}),
        ...expr,
        filter: {
          ...(((oldCondition.eventCondition?.sequenceConditionExpressions || [])[idx] || {}).filter || {}),
          ...(expr.filter || {})
        }
      }))
    }
  };
  return merged;
}

function filtersChange(filters: any) {
  const oldCondition = tagConditionData.value[tagActiveIndex.value].condition || {};
  tagConditionData.value[tagActiveIndex.value].condition = deepMergeCondition(oldCondition, filters);
}

/**
 * 初始化
 */
onMounted(async () => {
  const queryCode = route?.query.code;
  if (queryCode?.length > 0) {
    getUserTagDetail(queryCode).then(res => {
      tagCode.value = res.code
      form.value.displayName = res.displayName
      form.value.remark = res.note
      form.value.timeZone = res.timeZone
      form.value.status = res.status
      dataSource.value = res.dataSource || 'event'
      tagConditionData.value = res.conditions
      tagActiveKey.value = res.conditions[0].key
      const {calculateCron} = res;
      if (!_.isEmpty(calculateCron)) {
        showCalculateCron.value = true
        form.value.calculateCron = calculateCron
      }
    }).catch(e => console.error(e))
  }

  // 初始化事件属性 todo 优化
  const data = {
    event: [],
    inApp: 1
  }
  await toolData.fetchAttrFlatList(data)
  toolData.updateTemporaryList([])
  modelLists.value = (await toolData.fetchAllModalList()).flatMap(category => category.items || []);
})
</script>

<style scoped lang="less">
.single-page {
  width: 100%;
  height: 100%;

  .header {
    background: var(--color-bg-2);
    margin-bottom: 24px;
  }

  .container {
    margin: 24px;
    padding: 24px;
    height: calc(100vh - 120px);
    overflow: auto;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;

    .block {
      margin-bottom: 32px;

      .block-title {
        display: flex;
        align-items: flex-end;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font: var(--tant-header-font-header4-medium);

        .description {
          margin-left: 8px;
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }
      }

      .rule-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .title {
          padding-right: 16px;
        }
      }

      .user-condition {
        display: flex;
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: 4px;

        .tag-value-list {
          padding: 12px;
          overflow-y: auto;
          background-color: var(--tant-fill-color-fill1-2);

          .tag-value-add {
            height: 40px;
            text-align: center;
            border: 1px dashed var(--tant-border-color-border1-2);
            border-radius: 4px;
            cursor: pointer;

            .add-btn {
              margin-top: 4px;
              background-color: transparent;
              border: none;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              height: 32px;
              padding: 5px 16px;
              color: var(--tant-secondary-color-secondary-default);
              font: var(--tant-body-font-body-regular);
              text-transform: capitalize;
              border-radius: var(--tant-border-radius-medium);
              box-shadow: unset;
              cursor: pointer;
            }

            &:hover {
              border-color: var(--tant-status-info-color-info-hover);
            }
          }

          .tag-value-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            height: 38px;
            width: 200px;
            margin-bottom: 12px;
            padding: 0 8px;
            color: var(--tant-text-gray-color-text1-2);
            font: var(--tant-body-font-body-regular);
            background-color: var(--tant-bg-white-color-bg1-1);
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 4px;

            .drag-icon {
              width: 14px;
              height: 14px;
            }

            .tag-value-text {
              display: flex;
              flex: 1 1;
              align-items: center;
              margin-right: 4px;
              margin-left: 4px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            &:hover {
              border-color: var(--tant-primary-color-primary-active) !important;
              box-shadow: var(--tant-input-shadow-active-overall);
              color: var(--tant-primary-color-primary-active) !important;
            }
          }

          .tag-value-item-active {
            border-color: var(--tant-primary-color-primary-active) !important;
            box-shadow: var(--tant-input-shadow-active-overall);
          }
        }

        .tag-rule {
          flex: 1 1;
          padding: 24px;
          overflow: auto;
          background-color: var(--color-white);
          border: 1px solid var(--color-gray-3);

          .info-item {
            display: table-row;

            .info-item-label {
              display: table-cell;
              min-width: 86px;
              padding-right: 16px;
              padding-bottom: 24px;
              color: var(--tant-text-gray-color-text1-2);
              font: var(--tant-body-font-body-regular);
              line-height: 32px;
              text-align: right;
              vertical-align: top;

              .description {
                color: var(--tant-text-gray-color-text1-3);
                font: var(--tant-description-font-description-regular);
              }
            }

            .info-item-content {
              display: table-cell;
              padding-bottom: 24px;
              width: 100%;
            }
          }

        }

        .ta-filter-button {
          display: inline-flex;
          color: var(--tant-primary-color-primary-default);
          align-items: center;
          padding: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all .3s;

          .ta-filter-button-icon {
            background-color: #eaeefd;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3px;
            margin-right: 6px;
          }

          &:hover {
            background-color: var(--tant-bg-white-color-bg1-1);
            color: var(--tant-primary-color-primary-hover);
          }
        }
      }

      .tag-info {
        .description {
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }

        .cron-input {
          width: 328px;
          margin-left: 16px;
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .rule-block {
      height: calc(100% - 308px);

      .user-condition {
        height: calc(100% - 68px);
      }
    }
  }
}


</style>