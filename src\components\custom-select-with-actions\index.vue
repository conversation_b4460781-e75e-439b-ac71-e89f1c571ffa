<template>
  <a-select v-model:model-value="internalValue" v-bind="$attrs" :filter-option="customFilterOption" @search="handleSearch" @change="handleChange" @input-value-change="handleInputValueChange">
    <template #search-icon>
      <div class="select-suffix">
        <div class="select-suffix-item">
          <a-tag v-if="!selectAll" class="tag-btn" @click="selectAllClick"> 全选 </a-tag>
          <a-tag v-else class="tag-btn" bordered color="green" @click="selectAllClick"> 全选 </a-tag>
          <a-tag v-if="internalValue.length > 0" class="tag-btn" color="orange" style="margin-left: 3px;" @click="clearAllClick"> 清空 </a-tag>
        </div>
        <div v-if="showRegularToggle" class="select-suffix-item">
          <a-tag v-if="!allowRegular" class="tag-btn" @click="allowRegular = !allowRegular">
            <img class="icon" src="/icon/no-regular.png" alt="" />
          </a-tag>
          <a-tag v-else class="tag-btn" color="blue" @click="allowRegular = !allowRegular">
            <img class="icon" style="margin-top: -1px" src="/icon/regular-blue.png" alt="" />
          </a-tag>
        </div>
      </div>
    </template>
    <a-option 
      v-for="item in sortedOptions" 
      :key="item[valueKey]" 
      :value="item[valueKey]"
      :disabled="getDisabledStatus(item)"
    >
      {{ item[labelKey] && item[displayKey] ? item[labelKey] + '-' : (item[labelKey] || '') }}{{ item[displayKey] || '' }}
    </a-option>
  </a-select>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { sortSelectedFirst } from '@/utils/sortUtil';

  interface Props {
    modelValue: any[];
    options: any[];
    valueKey?: string;
    labelKey?: string;
    displayKey?: string;
    showRegularToggle?: boolean; // 是否显示正则
    disabledCondition?: (item: any) => boolean; // 禁用条件函数
  }

  const props = withDefaults(defineProps<Props>(), {
    valueKey: 'code',
    labelKey: 'name',
    displayKey: 'displayName',
    showRegularToggle: false,
    disabledCondition: undefined
  });

  const emit = defineEmits(['update:modelValue', 'change', 'search']);
  const sortedOptions = ref<any[]>([]);

  const searchText = ref('');
  const allowRegular = ref(false);

  // 内部值管理
  const internalValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 获取过滤后的选项（根据搜索条件）
  const getFilteredOptions = () => {
    if (!searchText.value) return props.options;
    
    if (allowRegular.value) {
      // 正则搜索
      try {
        const reg = new RegExp(searchText.value, 'i');
        return props.options.filter((item) => {
          return reg.test(item[props.labelKey] ?? '') || reg.test(item[props.displayKey] ?? '');
        });
      } catch {
        // 正则表达式无效时，回退到普通搜索
        const searchValue = searchText.value.toLowerCase();
        return props.options.filter((item) => {
          const name = item[props.labelKey]?.toLowerCase() || '';
          const displayName = item[props.displayKey]?.toLowerCase() || '';
          return name.includes(searchValue) || displayName.includes(searchValue);
        });
      }
    } else {
      // 普通搜索
      const searchValue = searchText.value.toLowerCase();
      return props.options.filter((item) => {
        const name = item[props.labelKey]?.toLowerCase() || '';
        const displayName = item[props.displayKey]?.toLowerCase() || '';
        return name.includes(searchValue) || displayName.includes(searchValue);
      });
    }
  };

  // 计算是否全选（基于当前搜索结果，排除禁用项）
  const selectAll = computed(() => {
    const filteredOptions = getFilteredOptions();
    const enabledOptions = filteredOptions.filter(item => !getDisabledStatus(item));
    const enabledValues = enabledOptions.map((item) => item[props.valueKey]);
    return enabledValues.length > 0 && enabledValues.every((value) => props.modelValue.includes(value));
  });
  watch(
    () => props.options,
    () => {
      sortedOptions.value = sortSelectedFirst(props.options, props.modelValue);
    },
    { immediate: true }
  );

  // 自定义过滤函数
  const customFilterOption = (inputValue: string, option: any) => {
    if (allowRegular.value) {
      try {
        const reg = new RegExp(inputValue, 'i');
        return reg.test(option.value ?? '') || reg.test(option.label ?? '');
      } catch {
        return inputValue?.length <= 0 || option?.value?.toLowerCase()?.includes(inputValue?.toLowerCase()) || option?.label?.toLowerCase()?.includes(inputValue?.toLowerCase());
      }
    }
    return inputValue?.length <= 0 || option?.value?.toLowerCase()?.includes(inputValue?.toLowerCase()) || option?.label?.toLowerCase()?.includes(inputValue?.toLowerCase());
  };

  // 全选点击处理
  const selectAllClick = () => {
    const filteredOptions = getFilteredOptions();
    // 排除禁用项
    const enabledOptions = filteredOptions.filter(item => !getDisabledStatus(item));
    const enabledValues = enabledOptions.map((item) => item[props.valueKey]);
    
    if (selectAll.value) {
      // 取消选中当前搜索结果中的启用选项
      const newValue = props.modelValue.filter((x) => !enabledValues.includes(x));
      emit('update:modelValue', newValue);
    } else {
      // 选中当前搜索结果中的启用选项
      const newValue = [...new Set([...props.modelValue, ...enabledValues])];
      emit('update:modelValue', newValue);
    }
  };

  // 清空点击处理
  const clearAllClick = () => {
    emit('update:modelValue', []);
  };
  // 搜索处理
  const handleSearch = (value: string) => {
    searchText.value = value;
    emit('search', value);
  };
  const handleInputValueChange = (value: string) => {
    searchText.value = value;
  };
  // 变化处理
  const handleChange = (value: any) => {
    emit('change', value);
  };

  // 监听搜索文本变化，更新全选状态
  watch([searchText, allowRegular], () => {}, { immediate: true });

  // 获取禁用状态
  const getDisabledStatus = (item: any) => {
    return props.disabledCondition ? props.disabledCondition(item) : false;
  };
</script>

<style scoped lang="less">
  .select-suffix {
    display: flex;

    .select-suffix-item {
      margin-left: 8px;
      height: 24px;

      .tag-btn {
        width: 30px;
        height: 16px;
        font-size: 10px;
        padding: 4px;
        cursor: pointer;
        user-select: none;

        .icon {
          width: 20px;
          height: 14px;
        }
      }
    }
  }
</style>
