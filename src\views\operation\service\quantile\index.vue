<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
                <div class="filter-item">
                    <a-button class="button" type="primary" style="margin-left: 16px" @click="openModal">
                        <template #icon>
                            <icon-plus />
                        </template>
                        配置
                    </a-button>
                </div>
            </div>
        </div>
        <div class="page-body">
            <a-split disabled size="150px">
                <template #first>
                    <div style="padding-right: 10px;">
                        <div style="border: solid 1px #E3E4E5; padding: 0 5px;">
                            <div class="flex justify-center" style="padding: 9px 0; border-bottom: solid 1px #eee">30日快照</div>
                            <a-scrollbar style="height: calc(100vh - 235px);overflow: auto;">
                                <a-menu v-model:selected-keys="queryDate">
                                    <a-menu-item @click="changeQueryDate(dateStr)" :key="dateStr" v-for="(dateStr, index) in last30Days">{{dateStr}}</a-menu-item>
                                </a-menu>
                            </a-scrollbar>
                        </div>
                    </div>
                </template>
                <template #second>
                    <div style="padding: 0 10px">
                        <a-table
                            :loading="loading"
                            :columns="columns"
                            :data="loginData"
                            :bordered="false"
                            :hoverable="true"
                            sticky-header
                            :pagination="false"
                            :table-layout-fixed="true"
                            :scrollbar="scrollbar"
                            :scroll="scroll">
                        </a-table>
                        <a-pagination
                            :total="totalCount"
                            v-model:current="pageCurrent"
                            v-model:page-size="pageSize"
                            :page-size-options="pageSizeOptions"
                            @change="pageChange"
                            @page-size-change="pageSizeChange"
                            class="justify-end mt-15px"
                            show-total
                            show-page-size
                        />
                    </div>
                </template>
            </a-split>
        </div>
        <a-modal v-model:visible="modalVisible" :width="520" title-align="start" title="分位数下发配置" :footer="false" @cancel="closeModal">
            <a-form ref="basicFormRef" class="attr-form" :rules="basicRules" :model="basicForm" layout="vertical">
                <a-form-item field="timeRange" label="日期范围" validate-trigger="blur">
                    <a-select
                        v-model="basicForm.timeRange"
                        :options="dateRangeOptions"
                        :field-names="{value: 'code', label: 'name'}"
                        allow-search
                        placeholder="请选择日期范围"
                    ></a-select>
                </a-form-item>
                <a-form-item field="installTimeRange" label="安装日期过滤">
                    <a-select
                        v-model="basicForm.installTimeRange"
                        :options="installDateRangeOptions"
                        :field-names="{value: 'code', label: 'name'}"
                        allow-search
                        allow-clear
                        placeholder="(可选)请选择安装日期范围"
                    ></a-select>
                </a-form-item>
                <a-form-item field="relatedEvent" label="目标事件" validate-trigger="blur">
                    <a-select
                        v-model="basicForm.relatedEvent"
                        :options="evtLists"
                        :field-names="{value: 'name', label: 'name'}"
                        @change="eventChange"
                        allow-search
                        placeholder="请选择目标事件"
                    ></a-select>
                </a-form-item>
                <a-form-item field="groupAttrs" label="分组属性">
                    <a-select
                        v-model="basicForm.groupAttrs"
                        :options="evtAttrLists"
                        :field-names="{value: 'name', label: 'name'}"
                        allow-search
                        multiple
                        placeholder="请选择分组属性"
                    ></a-select>
                </a-form-item>
                <a-form-item label="目标属性" field="targetAttr" validate-trigger="blur">
                    <a-row :gutter="8" class="justify-between w-full" style="margin: 0">
                        <a-col :span="10" style="padding: 0 4px 0 0">
                            <a-form-item field="targetAttr" validate-trigger="blur" no-style>
                                <a-select
                                    v-model="basicForm.targetAttr"
                                    :options="evtAttrLists"
                                    :field-names="{value: 'name', label: 'name'}"
                                    allow-search
                                    placeholder="请选择目标属性"
                                >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="10" style="padding: 0 0 0 4px">
                            <a-form-item field="aggregationType" validate-trigger="blur" no-style>
                                <a-select
                                    v-model="basicForm.aggregationType"
                                    :options="aggregationTypeOptions"
                                    :field-names="{value: 'code', label: 'name'}"
                                    allow-search
                                    placeholder="请选择聚合类型"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="4" style="height: 29px; display: flex">
                            <a-form-item field="filterTarget" no-style>
                                <a-checkbox v-model="basicForm.targetFilterZero">过滤0值</a-checkbox>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form-item>
                <a-form-item field="groupCount" label="分位数段数" validate-trigger="blur">
                    <a-input-number v-model="basicForm.groupCount" :min="0" placeholder="请输入分位数段数"/>
                </a-form-item>
                <a-form-item field="lowerLimit" label="事件数阈值" validate-trigger="blur">
                    <a-input-number v-model="basicForm.lowerLimit" :min="0" placeholder="请输入事件输约束值"/>
                </a-form-item>
            </a-form>
            <div class="footer">
                <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
                <a-button type="primary" :loading="saveLoading" @click="saveData">
                    保存
                </a-button>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">

import {reactive, ref} from "vue";
import {getQuantileConfig, getQuantileList, saveQuantileConfig} from "@/api/marketing/api";
import selectApp from "@/components/selected-game-app/index.vue"
import {Message} from '@arco-design/web-vue';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {useRoute} from 'vue-router';
import {getCurrentUTCDateISO, getFullDays} from "@/utils/dateUtil";
import {analyseStore, toolStore} from "@/store";
import {getAnalyseEvent} from "@/api/analyse/api";

const route = useRoute();
const analyseData = analyseStore();
const appId = ref(useSessionStorage('app-id', '')?.value)
const curDate = new Date()
const last30Date = new Date(curDate.getTime() - 86400 * 1000 * 30)
const last30Days = getFullDays(getCurrentUTCDateISO(last30Date), getCurrentUTCDateISO())
last30Days.reverse()
const queryDate = ref([last30Days[0]])
const changeQueryDate = (val) => {
    pageCurrent.value = 1
    getDataList()
}

const params = reactive({
    country: ['global'],
    date: {
        recentStartDate: 29,
        recentEndDate: 0,
        dateText: '最近30天',
    },
    platform: 'all',
})
const loginData = ref<any>([])
const totalCount = ref(0)
const pageCurrent = ref(1)
const pageSize = ref(25)
const pageSizeOptions = ref([25, 50 ,100])
const evtLists = ref<any>([])
const evtAttrLists = ref<any>([])
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const scrollbar = ref(true)
const scroll = {x: 800,y: 'calc(100vh - 242px)'}
const columns = [
    {
        title: '日期',
        dataIndex: 'rpDate',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
    },
    {
        title: '分组项',
        dataIndex: 'groupAttrs',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
        render: (value) => {
          let group_attr = ''
          const {record} = value;
          if (Object.keys(record.groupAttrs).length){
            group_attr = JSON.stringify(record.groupAttrs)
          }

          return group_attr;
        },
    },
    {
        title: '分位数',
        dataIndex: 'quantile',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
        render: (value) => {
          const {record} = value;
          return JSON.stringify(record.quantile)
        }
    },
]
const loading = ref(false)


const getDataList = async () => {
    loading.value = true
    try {
        const params = {
            appId: appId.value,
            queryDate: queryDate.value[0],
            page: pageCurrent.value,
            pageSize: pageSize.value
        }
        const data = await getQuantileList(params)
        loginData.value = data.list
        totalCount.value = data.total
    } catch (error) {
        console.log(error)
    } finally {
        loading.value = false
    }
}

const pageChange = (value: number) => {
  getDataList()
}
const pageSizeChange = (value: number) => {
  pageCurrent.value = 1
  getDataList()
}
const eventChange = async (value: string) => {
  await getAnalyseEvent({
    event: value,
    inApp: 1
  }).then((res) => {
    evtAttrLists.value = res
  });
}
const modalVisible = ref(false)
const platformTitle = ref('')
const basicFormRef = ref()
// 表单规则
const basicRules = {
    timeRange: [
        {
            required: true,
            message: '请选择日期范围'
        }
    ],
  relatedEvent: [
        {
            required: true,
            message: '请选择相关事件'
        }
    ],
    groupCount: [
        {
            required: true,
            message: '请输入分位数段数'
        },
        {
          type: 'number',
          min: 1,
          message: "请输入大于0的整数"
        }
    ],
  lowerLimit: [
        {
            required: true,
            message: '请输入事件数阈值'
        },
        {
          type: 'number',
          min: 1,
          message: "请输入大于0的整数"
        }
    ],
    targetAttr: [
        {
            required: true,
            message: '请选择目标属性'
        }
    ],
    aggregationType: [
        {
            required: true,
            message: '请选择聚合类型'
        }
    ]
}

//日期范围选项
const dateRangeOptions = [
  {code: '1-1', name: '最近一天'},
  {code: '1-2', name: '最近两天'},
  {code: '1-3', name: '最近三天'},
  {code: '1-5', name: '最近五天'},
  {code: '1-7', name: '最近七天'},
  {code: '1-10', name: '最近十天'},
  {code: '1-14', name: '最近十四天'}
]

//安装日期范围选项
const installDateRangeOptions = [
  {code: '1-1', name: '最近一天'},
  {code: '1-2', name: '最近两天'},
  {code: '1-3', name: '最近三天'},
  {code: '1-5', name: '最近五天'},
  {code: '1-7', name: '最近七天'},
  {code: '1-10', name: '最近十天'},
  {code: '1-14', name: '最近十四天'}
]

//聚合类型选项
const aggregationTypeOptions = [
  {code: 'avg', name: '平均数'},
  {code: 'median', name: '中位数'},
  {code: 'mode', name: '众数'},
  {code: 'min', name: '最小值'},
  {code: 'max', name: '最大值'},
]

// 表单信息
const basicForm = reactive({
    timeRange: '',
    installTimeRange: '',
    relatedEvent: '',
    groupAttrs: [],
    groupCount: null,
    targetAttr:'',
    targetFilterZero: false,
    aggregationType:'',
    lowerLimit: null
});


// 获取基本信息
const getInfo = async () => {
    try {
        await getQuantileConfig(appId.value).then(res => {
            if (res?.relatedEvent) {
              eventChange(res?.relatedEvent)
            }
            basicForm.timeRange = res?.timeRange
            basicForm.installTimeRange = res?.installTimeRange
            basicForm.relatedEvent = res?.relatedEvent
            basicForm.groupAttrs = res?.groupAttrs
            basicForm.groupCount = res?.groupCount
            basicForm.targetAttr = res?.targetAttr
            basicForm.targetFilterZero = res && res.targetFilterZero ? true : false
            basicForm.aggregationType = res?.aggregationType
            basicForm.lowerLimit = res?.lowerLimit
        })
    } catch (error) {
        console.log(error, 'pay-init');
    }
}
const openModal = async () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveLoading = ref(false)
const saveData = () => {
    basicFormRef.value.validate(async (valid: any) => {
        saveLoading.value = true
        const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
        if (!valid) {
            try {
                const data = {
                    appId:appId.value,
                    timeRange: basicForm.timeRange,
                    installTimeRange: basicForm.installTimeRange,
                    relatedEvent: basicForm.relatedEvent,
                    groupAttrs: basicForm.groupAttrs,
                    groupCount: basicForm.groupCount,
                    targetAttr: basicForm.targetAttr,
                    targetFilterZero: basicForm.targetFilterZero ? 1 : 0,
                    aggregationType: basicForm.aggregationType,
                    lowerLimit: basicForm.lowerLimit
                }
                await saveQuantileConfig(data)
                Message.success('保存成功');
                modalVisible.value = false
                getInfo()
            } catch (error) {
                console.log(error);
            } finally {
                saveLoading.value = false;
            }
        } else {
            saveLoading.value = false;
        }
    })
}

localStorageEventBus.on((name, value) => {
    if (name === "app-id") {
        appId.value = value
        init()
    }
})

const init = async () => {
    const toolData = toolStore();
    evtLists.value = (await toolData.fetchAllModalList()).flatMap(category => category.items || []);
    getInfo().then(()=>{
        const storedData = JSON.parse(sessionStorage.getItem('app-data') || '{}');
        platformTitle.value = storedData.osName === 'ios' ? 'Apple' : 'Google'
    })
    getDataList()
}
init()
</script>

<style scoped lang="less">
.w-full{width: 100%}
.h-full{height: 100%}
.flex {
    display: flex;
}
.justify-between {
    justify-content: space-between;
}
.justify-center {
    justify-content: center;
}
.justify-end {
    justify-content: end;
}
.relative{
    position: relative;
}
.mt-15px {
  margin-top: 15px;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

.form-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.form-label{
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-bottom: 12px;
}
</style>
