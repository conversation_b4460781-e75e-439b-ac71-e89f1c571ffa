<template>
    <a-drawer :width="960" :visible="modalVisible" unmountOnClose @ok="saveData" @cancel="handleCancel">
        <template #title>
            {{ modalTitle }}
        </template>
        <a-spin :loading="loading" style="width: 100%;">
            <a-form ref="formRef" :model="form" :rules="rules" label-align="left" style="width: 100%;">
                <a-form-item field="name" label="分组名称">
                    <a-input v-model="form.name" max-length="50" style="width: 400px;" show-word-limit/>
                </a-form-item>
                <a-form-item field="description" label="描述">
                    <a-textarea
                    v-model="form.description"
                    placeholder="请输入"
                    style="height: 100px;"
                    :max-length="{length:200,errorOnly:true}"
                    allow-clear
                    show-word-limit/>
                </a-form-item>
                <div v-for="(item,index) in groupList" :key="index" class="domain-item">
                    <div class="head">
                        <div class="justify" style="height: 100%;">
                            <div class="content">
                                <a-space>
                                    <icon-up v-if="item.showContent" class="icon-style" @click="() => item.showContent = false"/>
                                    <icon-down v-else class="icon-style" @click="() => item.showContent = true"/>
                                    <span v-if="!item.showNameInput">{{ item.name }}</span>
                                    <a-input v-else v-model="item.name" max-length="50" style="width: 180px;" @blur="item.showNameInput = false"/>
                                    <icon-edit v-if="form.editable" class="icon-style" @click="() => item.showNameInput = !item.showNameInput"/>
                                </a-space>
                            </div>
                            <div class="extra">
                                <icon-delete v-if="groupList.length>2" style="color: red;" @click="deleteList(index)"/>
                            </div>
                        </div>
                    </div>
                    <div v-if="item.showContent" class="content-info">
                        <a-form-item :field="`domains.${index}.flowPercentage`" label="实验流量" :rules="[{ required: true, message: '请输入实验流量' }]">
                            <a-input-number v-model="item.flowPercentage" :step="0.1" :precision="1" style="width: 100px;" :disabled="!form.editable" @change="onFlowChange(index)">
                                <template #suffix>
                                    %
                                </template>
                            </a-input-number>
                        </a-form-item>
                        <a-form-item field="subDomainGroupCodes" label="嵌套子互斥域组">
                            <a-select
                                v-model:model-value="item.subDomainGroupCodes"
                                :disabled="!form.editable"
                                :style="{width:'320px'}"
                                :loading="domainLoading"
                                placeholder="请选择"
                                multiple
                                :filter-option="false"
                                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                @search="handleDomainSearch">
                                <a-option v-for="layer of domainOptions" :key="layer.code" :value="layer.code">{{layer.name}}</a-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item field="flowLayerCodes" label="绑定的流量层">
                            <a-select
                                v-model:model-value="item.flowLayerCodes"
                                :disabled="!form.editable"
                                :style="{width:'320px'}"
                                :loading="layerLoading"
                                placeholder="请选择"
                                multiple
                                :filter-option="false"
                                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                @search="handleSearch">
                                <a-option v-for="layer of filteredOptions(index)" :key="layer.code" :value="layer.code">{{layer.name}}</a-option>
                            </a-select>
                            <a-button v-if="form.editable" type="text" @click="addLayer">
                                <template #icon>
                                    <icon-plus />
                                </template>
                                新建流量层
                            </a-button>
                        </a-form-item>
                    </div>
                </div>
            </a-form>
            <a-button v-if="form.editable" type="outline" style="border-radius: 4px;" @click="addList">
                <template #icon>
                    <icon-plus/>
                </template>
                添加互斥域
            </a-button>
        </a-spin>
        <template #footer>
            <div class="footer">
                <a-button style="margin-right: 10px;" class="cancel" @click="handleCancel">取消</a-button>
                <a-button type="primary" :loading="saveLoading" @click="saveData">
                    保存
                </a-button>
            </div>
        </template>
        <layerModel ref="layerRef" @update-data="getLayerOptions"/>
  </a-drawer>
</template>

<script setup lang="ts">
import {reactive, ref,watch} from "vue";
import {Message, Notification} from '@arco-design/web-vue';
import {getLayerList,getDomainList,getDomainGroupDetail,saveDomainGroup,getDomainGroupList} from "@/api/ab/api"
import layerModel from "@/views/abTest/management/flowLayer/components/handleModal.vue"

const loading = ref(false)
const saveLoading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref('')
const options = ref<any>([]);
const layerLoading = ref(false)
const domainOptions = ref<any>([]);
const domainLoading = ref(false)

const rules = {
    name: [
        {
            required: true,
            message:'请填写分组名称',
        }
    ],
}

const formRef = ref()
const groupList = ref<any>([])
const form = reactive({
    code:'',
    name: '',
    description:'',
    editable:true,
    domains: groupList
})
const layerDetail = ref()
const emits = defineEmits(['updateData']);

// 获取互斥域组列表
const getDomainOptions = async (name?:string) => {
    domainLoading.value = true;
    try {
        const params = {
            name: name || '',
            current: 1,
            pageSize: 20,
        }
        const res = await getDomainGroupList(params);
        domainOptions.value = res.items || [];
    } catch (e) {
        domainOptions.value = [];
    }finally {
        domainLoading.value = false;
    }
}
const handleDomainSearch = async (value) => {
    await getDomainOptions(value);
};
// 获取流量层列表
const getLayerOptions = async (name?:string) => {
    layerLoading.value = true;
    try {
        const params = {
            name: name || '',
            current: 1,
            isBind:false,
            pageSize: 20,
        }
        const res = await getLayerList(params);
        // 直接push去重
        const codeSet = new Set(options.value.map(opt => opt.code));
        (res.items || []).forEach(item => {
            if (!codeSet.has(item.code)) {
                options.value.push(item);
                codeSet.add(item.code);
            }
        });
    } catch (e) {
        // 不清空，保留原有 options
    }finally {
        layerLoading.value = false;
    }
}
const handleSearch = async (value) => {
    await getLayerOptions(value);
};
const filteredOptions = (currentIndex: number) => {
    // 当前分组已选
    const currentSelected = groupList.value[currentIndex]?.flowLayerCodes || [];
    // 其他分组已选（本地 groupList）
    const otherSelectedLocal = groupList.value
        .filter((_, idx) => idx !== currentIndex)
        .flatMap(item => item.flowLayerCodes || []);
    // 其他分组已选（后端回显 layerDetail.value.flowDomains）
    let otherSelectedRemote: string[] = [];
    if (layerDetail.value?.flowDomains) {
        otherSelectedRemote = layerDetail.value.flowDomains
            .filter((_, idx) => idx !== currentIndex) // 排除当前分组
            .flatMap(domain => domain.flowLayers?.map(l => l.code) || []);
    }
    // 合并所有其他分组已选
    const otherSelected = [...otherSelectedLocal, ...otherSelectedRemote];

    // 用 Map 合并 options 和当前分组已选，防止丢失回显项
    const map = new Map();
    options.value.forEach(opt => map.set(opt.code, opt));
    currentSelected.forEach(code => {
        if (!map.has(code)) {
            // 优先从 layerDetail.value.flowDomains 找 name
            let name = code;
            if (layerDetail.value?.flowDomains) {
                for (const group of layerDetail.value.flowDomains) {
                    const found = group.flowLayers?.find?.(l => l.code === code);
                    if (found) {
                        name = found.name || code;
                        break;
                    }
                }
            }
            map.set(code, { code, name });
        }
    });
    // 过滤掉其他分组已选
    return Array.from(map.values()).filter(opt => {
        if (currentSelected.includes(opt.code)) return true;
        return !otherSelected.includes(opt.code);
    });
};
// 获取互斥域组详情
// const getDetail = async(code:string) => {
//     loading.value = true
//     try {
//         const res = await getDomainGroupDetail(code);
//     } catch (e) {
//         console.log(e);
//     }finally {
//         loading.value = false;
//     }
// }
const layerRef = ref()
const addLayer = () => {
    layerRef.value.openModal()
}
const openModal = async (obj?:any) => {
    loading.value = true
    options.value = []
    domainOptions.value = []
    try {
        await Promise.all([
            getLayerOptions(),
            getDomainOptions()
        ])
        if(obj){
            console.log(obj,'obj');
            layerDetail.value = obj
            modalTitle.value = '编辑互斥域分组'
            form.code = obj.code
            form.name = obj.name
            form.description = obj.description
            form.editable = obj.editable ?? true
            groupList.value = obj.flowDomains.map(item => {
                return {
                    code: item.code,
                    name: item.name,
                    flowPercentage: item.flowPercentage,
                    subDomainGroupCodes: item.subDomainGroups.map(item => item.code),
                    flowLayerCodes: item.flowLayers.map(item => item.code),
                    showContent: true,
                    showNameInput: false,
                }
            })
            const allSelected = obj.flowDomains.flatMap(domain => domain.flowLayers);
            allSelected.forEach(sel => {
                if (!options.value.some(opt => opt.code === sel.code)) {
                    options.value.push(sel);
                }
            });
        }else{
            modalTitle.value = '新建互斥域分组'
            form.code = ''
            form.name = ''
            form.description = ''
            form.editable = true
            groupList.value = [
                {
                    name:'互斥域1',
                    flowPercentage:50,
                    subDomainGroupCodes:[],
                    flowLayerCodes:[],
                    showContent:true,
                    showNameInput:false,
                },
                {
                    name:'互斥域2',
                    flowPercentage:50,
                    subDomainGroupCodes:[],
                    flowLayerCodes:[],
                    showContent:true,
                    showNameInput:false,
                }
            ]
        }
        modalVisible.value = true
    } finally {
        loading.value = false
    }
}

// 2. 添加方法
const addList = () => {
    // 计算当前所有分组的 flowPercentage 总和
    const totalFlow = groupList.value.reduce((sum, item) => sum + (item.flowPercentage || 0), 0);
    // 新增分组的 flowPercentage
    const newFlow = totalFlow < 100 ? 100 - totalFlow : 0;
    groupList.value.push({
        name: `互斥域${groupList.value.length + 1}`,
        flowPercentage: newFlow,
        subDomainGroupCodes: [],
        flowLayerCodes: [],
        showContent: true,
        showNameInput: false,
    });
}

const deleteList = (index: number) => {
    groupList.value.splice(index, 1);
}

const handleCancel = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            const totalFlow = parseFloat(form.domains.reduce((sum, item) => sum + (item.flowPercentage || 0), 0).toFixed(1));
            if (totalFlow !== 100) {
                Notification.error(`互斥域组总流量不等于100%，当前总流量为${totalFlow}%`)
                return;
            }
            saveLoading.value = true;
            try {
                const params = {
                    code: form.code,
                    name: form.name,
                    description: form.description,
                    flowDomains: groupList.value.map(item => {
                        return {
                            code: item.code,
                            name: item.name,
                            flowPercentage: item.flowPercentage,
                            subDomainGroupCodes: item.subDomainGroupCodes,
                            flowLayerCodes: item.flowLayerCodes,
                        }
                    })
                }
                await saveDomainGroup(params);
                if(form.code){
                    Message.success('保存成功')
                }else{
                    Message.success('创建成功');
                }
                modalVisible.value = false;
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }finally {
                saveLoading.value = false;
            }
        }
    })
}
const lastManualChange = ref(false);

function onFlowChange(index: number) {
    // 如果是最后一项，标记为手动修改
    if (index === groupList.value.length - 1) {
        lastManualChange.value = true;
    } else {
        lastManualChange.value = false;
    }
}
watch(
  groupList,
  (newList, oldList) => {
    if (!Array.isArray(newList) || newList.length < 1) return;
    // 除最后一个分组外的总和
    const sumExceptLast = newList
      .slice(0, -1)
      .reduce((sum, item) => sum + (Number(item.flowPercentage) || 0), 0);
    // 只在总和不超过100且最后一项未被手动修改时自动调整
    if (sumExceptLast <= 100 && !lastManualChange.value) {
      newList[newList.length - 1].flowPercentage = +(100 - sumExceptLast).toFixed(1);
    }
  },
  { deep: true }
);
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.domain-item{
    margin-bottom: 20px;
    overflow: hidden;
    .justify{
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow: hidden;
        width: 100%;
    }
    .icon-style{
        cursor: pointer;
    }
    .head{
        background-color: var(--color-fill-2);
        border-radius: 8px 8px 0 0;
        box-sizing: border-box;
        height: 40px;
        padding: 0 20px;
        .content{
            flex-grow: 1;
            flex-shrink: 1;
        }
        .extra {
            flex-shrink: 0;
            cursor: pointer
        }
    }
    .content-info{
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: 0 0 8px 8px;
        border-top: none;
        overflow: hidden;
        padding: 16px 20px 0;
    }
}
.arco-btn-text:hover{
    background: transparent !important;
}
</style>