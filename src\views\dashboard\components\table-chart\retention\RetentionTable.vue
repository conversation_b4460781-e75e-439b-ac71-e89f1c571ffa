<template>
  <div class="chart-content">
    <a-table 
      :columns="columns" 
      :data="tableData" 
      :pagination="tableData?.length > 50" 
      size="small" 
      column-resizable 
      :bordered="{cell:true}" 
      :scroll="scroll" 
      :filter-icon-align-left="true">
      <template #timeHeader0>
        {{ tipHeaderName }}
        <a-tooltip :content="`【${tipHeaderName}】留存指触发初始事件的【${tipHeaderName}】，同时也触发回访事件（不区分两者的先后顺序）`" position="top">
          <icon-info-circle/>
        </a-tooltip>
      </template>
      <!-- 添加筛选器插槽 -->
      <template v-for="(_, idx) in props.reportAnalysisData?.groupsDesc" :key="idx" #[`group-filter-${idx}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
        <div class="custom-filter">
          <a-space direction="vertical" :size="8">
            <a-select
              :model-value="filterValue"
              placeholder="请选择"
              allow-clear
              allow-search
              multiple
              :options="getFilterOptions(idx)"
              :style="{ width: '200px' }"
              :trigger-props="{
                position: 'top',
                autoFitPopupMinWidth: true,
                updateAtScroll: true
              }"
              @update:model-value="(val) => setFilterValue(val)">
            </a-select>
            <div class="custom-filter-footer">
              <a-button size="mini" @click="handleFilterReset">重置</a-button>
              <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
            </div>
          </a-space>
        </div>
      </template>
      <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record,rowIndex }">
        <div class="cell-class" :style="props.reportAnalysisData?.groupsDesc?.length ? (index > props.reportAnalysisData.groupsDesc.length ? `background: ${getBackgroundColor(record[column.rateIndex])}` : '') : (index >1 ?`background: ${getBackgroundColor(record[column.rateIndex])}` : '')">
          <!-- 时间列 -->
          <div v-if="index === 0">
            <span>{{record[column.dataIndex]}}</span>
            <a-tooltip v-if="rowIndex === 0 && !props.reportAnalysisData?.groupsDesc?.length" position="top">
              <template #content>
                <div v-if="props.retentionType === 'retention'">
                  {{ columns[props.reportAnalysisData?.groupsDesc?.length ? props.reportAnalysisData.groupsDesc.length + 1 : 1]?.title }}：阶段总和<br>
                  留存人数： 阶段总和<br>
                  留存率： 加权平均
                </div>
                <div v-else>
                  {{ columns[props.reportAnalysisData?.groupsDesc?.length ? props.reportAnalysisData.groupsDesc.length + 1 : 1]?.title }}阶段总和:<br>
                  流失人数： 阶段总和<br>
                  流失率： 加权平均
                </div>
              </template>
              <icon-info-circle style="margin-left: 8px;"/>
            </a-tooltip>
          </div>
          
          <!-- 分组列 -->
          <div v-if="props.reportAnalysisData?.groupsDesc?.length && index > 0 && index <= props.reportAnalysisData.groupsDesc.length">
            <span>{{record[column.dataIndex]}}</span>
          </div>
          
          <!-- 数据列 -->
          <div v-if="props.reportAnalysisData?.groupsDesc?.length ? (index > props.reportAnalysisData.groupsDesc.length) : (index > 0)" class="hover-box">
            <!-- 第一行数据 -->
            <span v-if="rowIndex === 0 && (props.reportAnalysisData?.groupsDesc?.length ? index === props.reportAnalysisData.groupsDesc.length + 1 : index === 1)">
              {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}
            </span>
            <a-tooltip
              v-if="rowIndex === 0 &&
              (props.reportAnalysisData?.groupsDesc?.length ? index > props.reportAnalysisData.groupsDesc.length + 1 : index > 1)"
              :content="`${props.reportAnalysisData?.groupsDesc?.length ? `${record.date}进行${eventData?.[0]?.eventDisplayName}的 ${record['userNum'] || '-'}个用户中，有${record[column.dataIndex] || '-'}个在${column.title}进行了${eventData?.[1]?.eventDisplayName}，占比${record[column.rateIndex] || '-'}%` : `在${tableData[1]?.date}到${tableData[tableData.length-1]?.date}期间，进行了${eventData?.[0]?.eventDisplayName}的用户中，有${record[column.rateIndex] || '-'}%在${column.title}进行了${eventData?.[1]?.eventDisplayName}`}`"
              position="top">
              <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
            </a-tooltip>
            
            <!-- 非第一行数据 -->
            <a-tooltip v-if="rowIndex > 0 && (props.reportAnalysisData?.groupsDesc?.length ? index === props.reportAnalysisData.groupsDesc.length + 1 : index === 1)" :content="`${record.date}有${record[column.dataIndex] || '-'}个用户参与了${eventData?.[0]?.eventDisplayName}`" position="top">
              <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
            </a-tooltip>
            <a-tooltip v-if="rowIndex > 0 && (props.reportAnalysisData?.groupsDesc?.length ? index > props.reportAnalysisData.groupsDesc.length + 1 : index > 1)" :content="`${record.date}进行${eventData?.[0]?.eventDisplayName}的 ${record['userNum'] || '-'}个用户中，有${record[column.dataIndex] || '-'}个在${column.title}进行了${eventData?.[1]?.eventDisplayName}，占比${record[column.rateIndex] || '-'}%`" position="top">
              <span> {{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
            </a-tooltip>
          </div>
          <div v-if="props.reportAnalysisData?.groupsDesc?.length ? (index > props.reportAnalysisData.groupsDesc.length + 1) : (index > 1)">
            {{ record[column.rateIndex] !== undefined && record[column.rateIndex] !== null ? `${record[column.rateIndex]}%` : '-' }}
          </div>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref, watch} from "vue";
import * as XLSX from 'xlsx';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {getCustomStringLength,createCustomSorter} from "@/utils/strUtil";

interface Props {
  // 留存类型
  retentionType: string
  /**
   * 报表数据
   */
  reportAnalysisData: object;
}

const props = defineProps<Props>()

const columns = ref()
const tableData = ref()
const groups = ref<any>([])
const tipHeaderName = ref('')
const eventData = ref<any>([])

// 判断周几
const getDayOfWeek = (dateString, wsResultData) => {
  const timeSpan = wsResultData?.timeSpan
  if (timeSpan.unit === 'DAY') {
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if (timeSpan.unit === 'WEEK') {
    return `${dateString}周`;
  }
  return `${dateString}月`;
}
const formatNumber = (num) => {
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
const processData = (numType, wsResultData) => {
  const retentionNumData = wsResultData?.retentionQueryResult.find(item => item.type === `${numType}_num`)?.resultData;
  const retentionRateData = wsResultData?.retentionQueryResult.find(item => item.type === `${numType}_rate`)?.resultData;
  return retentionNumData.map((numData, index) => {
    const rateData = retentionRateData[index];
    // 获取格式化后的日期
    const formattedDate = numData.date === '阶段值' ? numData.date : getDayOfWeek(numData.date, wsResultData);
    // 创建对象
    const rowData = {
      date: formattedDate,
      userNum: numData?.summaryData?.[0], //  第一项
      groupData: numData.groupData,
      groupRateData: rateData.groupData
    };
    // 添加时间数据
    numData?.summaryData.forEach((value, idx) => {
      rowData[`time${idx}`] = value;
    });
    // 添加率数据
    rateData?.summaryData.forEach((value, idx) => {
      rowData[`rate${idx}`] = Number((value * 100).toFixed(2)); // 将率转换为百分比
    });
    return rowData;
  });
};

function renderChart(wsResultData: any) {
  if (!wsResultData) {
    return
  }
  if (!wsResultData?.retentionQueryResult?.length) {
    return
  }
  eventData.value = wsResultData?.events
  const eventName = wsResultData?.events?.[0]?.eventDisplayName;
  groups.value = wsResultData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(',');
  }).filter(item => item !== '总体') || [];
  
  const timeSpan = wsResultData?.timeSpan
  let unitName = ''
  if (timeSpan.unit === 'DAY') {
    unitName = '日'
    tipHeaderName.value = '当日'
  } else if (timeSpan.unit === 'WEEK') {
    unitName = '周'
    tipHeaderName.value = '当周'
  } else {
    unitName = '月'
    tipHeaderName.value = '当月'
  }

  const isRetention = props.retentionType !== 'churn';
  
  // 如果有分组项，处理分组数据
  if (wsResultData?.groupsDesc?.length) {
    const groupsDescList = wsResultData.groupsDesc;
    
    // 时间列
    const timeColumn = {
      title: '日期',
      dataIndex: 'date',
      slotName: 'date',
      width: 140,
      fixed: 'left',
      sortable: { sortDirections: ['ascend', 'descend'] }
    };
    
    // 分组列
    const groupColumns = groupsDescList.map((item, idx) => ({
      title: item.name,
      dataIndex: `group${idx}`,
      slotName: `group${idx}`,
      width: getCustomStringLength(item.name) + 80,
      fixed: 'left',
      sortable: {
          sortDirections: ['ascend', 'descend'],
          sorter: (a, b, extra) => {
              const { direction } = extra;
              const format = item.format;
              const valueA = a[`group${idx}`];
              const valueB = b[`group${idx}`];
              return createCustomSorter(format)(valueA, valueB, direction);
          }
      },
      filterable: {
          filter: (value, row) => !value?.length || value.includes(row[`group${idx}`] ?? null),
          slotName: `group-filter-${idx}`,
          multiple: true
      }
    }));

    // 用户数列
    const userNumColumn = {
      title: eventName,
      dataIndex: 'userNum',
      slotName: 'userNum',
      width: 160,
      fixed: 'left',
      bodyCellStyle: { backgroundColor: 'rgb(249, 249, 251)' },
      sortable: { sortDirections: ['ascend', 'descend'] }
    };

    // 生成日期列表和日期列
    const length = isRetention ? timeSpan.number + 1 : timeSpan.number;
    const dateList = Array.from({ length }, (_, index) => {
      if (isRetention) {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
      }
      return `${index + 1}${unitName}`;
    });

    const dateColumns = dateList.map((item, index) => ({
      title: item,
      dataIndex: `time${index + 1}`,
      rateIndex: `rate${index}`,
      slotName: `time${index + 1}`,
      ...(isRetention && { titleSlotName: `timeHeader${index}` }),
      width: 120,
      sortable: { sortDirections: ['ascend', 'descend'] }
    }));

    columns.value = [timeColumn, ...groupColumns, userNumColumn, ...dateColumns];
    
    // 处理分组数据
    const processedData = processData(isRetention ? 'retention' : 'churn', wsResultData);
    const groupData = [];
    
    processedData.forEach(item => {
      if (item.groupData?.length) {
        item.groupData.forEach((numData, idx) => {
          const rateData = item.groupRateData[idx];
          groupData.push({
            date: item.date,
            userNum: numData.values?.[0],
            ...Object.fromEntries(groupsDescList.map((_, i) => [`group${i}`, numData.group[i]])),
            ...(numData.values?.length ? Object.fromEntries(numData.values.map((val, i) => [`time${i + 1}`, val])) : {}),
            ...(rateData.values?.length ? Object.fromEntries(rateData.values.map((val, i) => [`rate${i}`, Number((val * 100).toFixed(2))])) : {})
          });
        });
      }
    });
    
    // 按分组项拼接+日期排序，阶段值排在对应日期上面
    tableData.value = groupData.sort((a, b) => {
      // 拼接分组项
      const groupStrA = groupsDescList.map((_, i) => a[`group${i}`] || '').join(',');
      const groupStrB = groupsDescList.map((_, i) => b[`group${i}`] || '').join(',');
      
      // 先按分组项排序
      const groupCompare = groupStrA.localeCompare(groupStrB);
      if (groupCompare !== 0) return groupCompare;
      
      // 分组相同时，按日期排序，但阶段值优先
      const dateA = a.date;
      const dateB = b.date;
      
      // 如果其中一个是阶段值，阶段值排在前面
      if (dateA === '阶段值' && dateB !== '阶段值') return -1;
      if (dateA !== '阶段值' && dateB === '阶段值') return 1;
      if (dateA === '阶段值' && dateB === '阶段值') return 0;
      
      // 都不是阶段值时，正常日期排序
      return dateA.localeCompare(dateB);
    });
  } else {
    // 无分组项的原有逻辑
    tableData.value = processData(isRetention ? 'retention' : 'churn', wsResultData);
    
    const dateList = Array.from({ length: isRetention ? timeSpan.number + 1 : timeSpan.number }, (_, index) => {
      if (isRetention) {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
      }
      return `${index + 1}${unitName}`;
    });

    const list = dateList.map((item, index) => ({
      title: item,
      dataIndex: `time${index + 1}`,
      rateIndex: `rate${index}`,
      slotName: `time${index + 1}`,
      ...(isRetention && { titleSlotName: `timeHeader${index}` }),
      width: 120,
      sortable: { sortDirections: ['ascend', 'descend'] }
    }));

    columns.value = [
      {
        title: '日期',
        dataIndex: 'date',
        slotName: 'date',
        width: 140,
        fixed: 'left',
        sortable: { sortDirections: ['ascend', 'descend'] }
      },
      {
        title: eventName,
        dataIndex: 'userNum',
        slotName: 'userNum',
        width: isRetention ? 160 : 140,
        fixed: 'left',
        bodyCellStyle: { backgroundColor: 'rgb(249, 249, 251)' },
        sortable: { sortDirections: ['ascend', 'descend'] }
      },
      ...list
    ];
  }
}

watch(() => props.reportAnalysisData, async (newData, oldData) => {
  renderChart(newData);
})
watch(() => props.retentionType, async (newData, oldData) => {
  renderChart(props.reportAnalysisData);
})

const scroll = computed(() => {
  return {
    y: '100%'
  }
})

// 获取筛选选项
const getFilterOptions = (idx) => {
  const uniqueValues = [...new Set(tableData.value?.map(row => row[`group${idx}`]).filter(val => val !== null && val !== undefined))]
  return uniqueValues.map(val => ({ label: val, value: val }))
}

// 获取背景色
// 处理表格颜色深浅
const getBackgroundColor = (value) => {
  // 如果没有值，返回白色
  if (value === null || value === undefined || value === '') {
      return 'rgb(255, 255, 255)'; // 白色
  }
  // 确保值是数字
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
      return 'rgb(255, 255, 255)';
  }
  // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
  const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
  return `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
}
onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }

  renderChart(props.reportAnalysisData)
})
const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  const dateString = date ? `${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}` : ''
  XLSX.writeFile(newWorkbook, `留存分析${name ? `_${name}` : ''}${dateString ? `_${dateString}` : ''}.xlsx`);
};
defineExpose({
  exportXlsx
})
</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  padding-top: 8px;
  width: 100%;
  height: 100%;
}

:deep(tbody .arco-table-cell) {
  padding: 0;
}

.cell-class {
  padding: 5px 16px;
  height: 54px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hover-box {
  display: flex;
  align-items: center;

  &:hover {
    .add-group {
      opacity: 1;
    }
  }

  .add-group {
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;

    &:hover {
      color: #8d0088;
    }
  }
}
.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}
</style>
