<template>
  <div class="page-content">
      
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  v-model="filterParams.accountIds"
                  placeholder="请选择广告账户"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="accountOptionsLoading"
                  :filter-option="false"
                  allow-search
                  :tag-nowrap="true"
                  @search="handleAccountSearch"
                  @dropdown-reach-bottom="loadMoreAccounts"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告账户-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in accountOptions" 
                    :key="item.accountId" 
                    :value="item.accountId" 
                    :label="item.accountName">
                    {{ item.accountName }}
                  </a-option>
              </a-select>
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption style="width: 136px;" :disabled="!selectedKeys.length">同步</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="handleRemarks">修改备注名</a-doption>
                    <a-doption :disabled="!selectedKeys.length">编辑标签</a-doption>
                </template>
            </a-dropdown>
            <a-button class="br4" style="margin-left: 12px;" @click="addAccount">添加账号</a-button>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-tooltip placement="top" content="可手动同步近3天(含今天)的数据，其他时段系统将定期自动同步">
                <a-button class="br4" style="margin-left: 12px;" @click="batchSync">
                    <template #icon>
                        <icon-sync />
                    </template>
                    <template #default>同步数据</template>
                </a-button>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="accountId"
        >
          <template #actions="{record}">
            <div class="actions-content">
              <a v-if="record.authStatus === 1" class="actions-btn">同步</a>
              <a v-if="record.authStatus === 1" class="actions-btn">添加广告</a>
            </div>
          </template>
          <template #accountName="{ record }">
            <div class="cell-content">
              <div class="text-link" @click="linkCampaign(record)">{{ record.accountName }}</div>
              <div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </div>
          </template>
          <template #authStatus="{ record }">
            <a-badge v-if="record.authStatus === 1" status="success" text="已授权"/>
            <a-badge v-if="record.authStatus === 0" status="warning" text="未绑定"/>
            <a-badge v-if="record.authStatus === 2" status="normal" text="授权失效"/>
            <a-badge v-if="record.authStatus === 3" status="danger" text="已解绑"/>
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'accountName'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
       <!-- 批量修改备注 -->
      <EditRemarkModal ref="remarkRef"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 批量同步数据 -->
      <BatchSyncData ref="syncRef"/>
      <!-- 添加账号 -->
      <AddAccount ref="addRef"/>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject} from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import EditRemarkModal from "@/views/launch/promotion/components/EditRemarkModal.vue";
import BatchSyncData from "@/views/launch/promotion/components/BatchSyncData.vue";
import {mintegralDrillList} from "@/views/launch/promotion/components/promotionData"
import DrillDrawer from "../components/DrillDrawer.vue";
import AddAccount from "../components/AddAccount.vue";


// 设置数据
const filterParams = reactive({
  accountIds:[]
})
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
const dateTime = ref(inject('dateTime') as any[])
const emits = defineEmits(['changeTabs']);
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
// 模拟table数据
const loading = ref(false)
const dropdownList = mintegralDrillList.account
const tableData = ref<any>()
const noSumColumns = ref(['accountName','actions','authStatus','updateTime'])
const columns = ref<any>([
  { title: '广告账户名称', dataIndex: 'accountName',slotName:'accountName',width:250,fixed:'left' },
  { title: '操作', dataIndex: 'actions',slotName:'actions',width:200,fixed:'left',align:'center' },
  { title: '授权状态', dataIndex: 'authStatus',slotName:'authStatus',minWidth:180},
  { title: '上次更新时间', dataIndex: 'updateTime',width:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])

// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const accountOptions = ref<any>([])
const accountOptionsLoading = ref(false);
const accountPagination = reactive({
  current: 1,
  pageSize: 20,
});
const linkCampaign = (record) => {
  const idList = [record.accountId]
  emits('changeTabs',{ids:idList,tab:'campaign'})
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'account',value:val,recordData:record})
}
const syncRef = ref()
const batchSync = () => {
  syncRef.value.openModal()
}
const confirmVisible = ref(false)
const confirmType = ref('')

// 批量修改备注
const remarkRef = ref()
const handleRemarks = () => {
  remarkRef.value.openModal()
}
// 添加账号
const addRef = ref()
const addAccount = () => {
  addRef.value.openModal()
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'facebook',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      accountOptions.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
}
// 加载广告账户选项
const loadAccountOptions = async () => {
  accountOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:accountPagination.pageSize,
      current:accountPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    if (accountPagination.current === 1) {
      accountOptions.value = items;
    } else {
      accountOptions.value = [...accountOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告账户选项失败:', error);
  } finally {
    accountOptionsLoading.value = false;
  }
};   
const handleAccountSearch = (v) => {
}
const loadMoreAccounts = async () => {
  accountPagination.current += 1;
  // 获取更多广告系列数据
  await loadAccountOptions();
}
const init = () => {
  getList()
}
// init()
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>