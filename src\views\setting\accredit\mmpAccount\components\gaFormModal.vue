
<template>
    <a-modal v-model:visible="modalVisible" :width="560" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-alert type="warning" style="margin-bottom: 16px;">
            <span class="href-name">GA4服务账号授权</span>
        </a-alert>
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="clientEmail" label="服务账号电子邮件" validate-trigger="blur">
                <a-input v-model="form.clientEmail" placeholder="请输入服务账号电子邮件"/>
            </a-form-item>
            <a-form-item field="authCredicret" label="服务账号授权文件内容" validate-trigger="blur">
                <a-textarea v-model:model-value="form.authCredicret" style="height: 120px;"/>
            </a-form-item>
            <a-form-item field="isEnabled" label="开启状态" validate-trigger="change">
                <a-select v-model:model-value="form.isEnabled" placeholder="请选择开启状态" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option :value="1">开启</a-option>
                    <a-option :value="0">关闭</a-option>
                </a-select>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addGa, getGaInfo, updateGa} from "@/api/setting/api"

const modalVisible = ref(false)
const modalTitle = ref('添加GA4授权')
const form = reactive({
    clientEmail:'',
    isEnabled:1,
    authCredicret:'',
})
const rules = {
    clientEmail: [
        {
            required: true,
            message:'请输入服务账号电子邮件'
        }
    ],
    isEnabled: [
        {
            required: true,
            message:'请选择开启状态'
        }
    ],
    authCredicret: [
        {
            required: true,
            message:'请输入服务账号授权文件内容'
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);

const openModal = async (clientEmail?:string) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalTitle.value = clientEmail ? '编辑GA4授权' :'添加GA4授权'
    if(clientEmail){
        await getGaInfo(clientEmail).then(res => {
            const {clientEmail,authCredicret,isEnabled} = res
            form.authCredicret = JSON.stringify(authCredicret)
            form.clientEmail = clientEmail
            form.isEnabled = isEnabled
        })
    }
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                let parsedAuth;
                try {
                    parsedAuth = JSON.parse(form.authCredicret);
                } catch (parseError) {
                    Message.error('授权文件内容格式不正确，请输入有效的 JSON 格式');
                    loading.value = false;
                    return;
                }

                const data = {
                    ...form,
                    authCredicret: parsedAuth
                }

                if(modalTitle.value === '编辑GA4授权') {
                    await updateGa(data).then(res => {
                        Message.success('更新成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                } else {
                    await addGa(data).then(res => {
                        Message.success('创建成功');
                        modalVisible.value = false;
                        emits('updateData')
                    })
                }
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.href-name {
    text-decoration: underline;
    color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: auto;
    cursor: pointer;
}
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    // button{
    //     border-radius: 4px;
    // }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>