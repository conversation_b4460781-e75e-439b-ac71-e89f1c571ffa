<template>
  <div class="page-container">
    <a-page-header
        class="header"
        title="创建标签"
        @back="router.back()"
    >
      <template #extra>
        <a-space>
          <a-button @click="router.back()">取消</a-button>
        </a-space>
      </template>
    </a-page-header>
    <div class="content">
      <div class="title">
        选择你想要创建的标签类型
      </div>
      <div class="card-list">
        <div class="card-item" @click="gotoCreateConditionTag">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-screen.png">
            <div class="card-left">
              <div class="card-title">
                条件标签
              </div>
              <div class="card-desc">
                筛选特定行为条件或用户属性的用户(分析主体)，并赋予标签值。
              </div>
            </div>
          </div>
        </div>
        <div class="card-item" @click="gotoCreateIndicatorTag">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-target.png">
            <div class="card-left">
              <div class="card-title">
                指标值标签
              </div>
              <div class="card-desc">
                指定时段内，用户(分析主体)完成事件的聚合指标，作为标签值。
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-id.png">
            <div class="card-left">
              <div class="card-title">
                ID标签
              </div>
              <div class="card-desc">
                上传 ID 定义标签用户(分析主体)，并同时赋予标签值。
              </div>
            </div>
          </div>
        </div>
        <div class="card-item">
          <div class="card-wrapper">
            <img src="/image/userGroup/pic-sql.png">
            <div class="card-left">
              <div class="card-title">
                SQL标签
              </div>
              <div class="card-desc">
                配置生成用户集合的 SQL 脚本作为条件，并将其定义为标签，支持更新定时。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useRouter} from 'vue-router'
import {ROUTE_NAME} from "@/router/constants";

const router = useRouter()

function gotoCreateConditionTag() {
  router.push({name: ROUTE_NAME.USER_TAG_CONDITION})
}
function gotoCreateIndicatorTag() {
  router.push({name: 'data.user.tag.create.indicator'})
}
</script>

<style scoped lang="less">
@import '../../style/create.less';
</style>