<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  v-model="filterParams.adsetIds"
                  placeholder="请选择广告组"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adsetOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdsetSearch"
                  @dropdown-reach-bottom="loadMoreAdsets"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告组-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adsetOptions" 
                    :key="item.adsetId" 
                    :value="item.adsetId" 
                    :label="item.adsetName">
                    {{ item.adsetName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.campaignIds"
                  placeholder="请选择广告系列"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="campaignOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleCampaignSearch"
                  @dropdown-reach-bottom="loadMoreCampaigns"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告系列-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in campaignOptions" 
                    :key="item.campaignId" 
                    :value="item.campaignId" 
                    :label="item.campaignName">
                    {{ item.campaignName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.accountIds"
                  placeholder="请选择广告账户"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="accountOptionsLoading"
                  :filter-option="false"
                  allow-search
                  :tag-nowrap="true"
                  @search="handleAccountSearch"
                  @dropdown-reach-bottom="loadMoreAccounts"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告账户-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in accountOptions" 
                    :key="item.accountId" 
                    :value="item.accountId" 
                    :label="item.accountName">
                    {{ item.accountName }}
                  </a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择创建"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>创建-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">不限</a-option>
                  <a-option value="2">是</a-option>
                  <a-option value="3">否</a-option>
              </a-select> -->
              <a-select
                  placeholder="请选择产品"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>产品-{{ data?.label }}</span>
                  </template>
              </a-select>
              <a-select
                  placeholder="请选择状态"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>状态-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">投放中</a-option>
                  <a-option value="2">已暂停</a-option>
                  <a-option value="3">已删除</a-option>
                  <a-option value="4">超预算</a-option>
                  <a-option value="5">审核中</a-option>
                  <a-option value="6">审核不通过</a-option>
                  <a-option value="7">其他状态</a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择渠道"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>渠道-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">Meta</a-option>
                  <a-option value="2">Mintegral</a-option>
              </a-select> -->
              <!-- <a-select
                  placeholder="请选择标签"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>标签-{{ data?.label }}</span>
                  </template>
              </a-select> -->
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption style="width: 136px;" @click="excelEdit">Excel编辑</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchOpen">开启</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchPause">暂停</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchTime">定时开关</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchDelete">删除</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchRelated">关联产品</a-doption>
                </template>
            </a-dropdown>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="adsetId"
        >
          <template #effectiveStatus="{ record }">
            <a-switch v-model="record.effectiveStatus" checked-value="ACTIVE" unchecked-value="PAUSED" size="small" :before-change="() => statusChange(record)"/>
          </template>
          <template #channel="{record}">
            <img v-if="record.channel === 'Mintegral'" src="/icon/mintegral.png" alt="" class="channel-img">
            <img v-else src="/icon/meta.svg" alt="" class="channel-img">
          </template>
          <template #adsetName="{ record }">
            <div class="cell-content">
              <div class="title-box">
                <div class="text">{{ record.adsetName }}</div>
              </div>
              <div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </div>
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'effectiveStatus'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
      <!-- 关联产品 -->
      <RelatedProducts ref="productsRef"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 定时开关 -->
      <TimeSwitch ref="timeRef"/>
      <!-- Excel编辑 -->
      <ExcelEdit ref="excelRef"/>
      <!-- 删除确认 -->
      <a-modal
        v-model:visible="deleteVisible"
        width="416px"
        :hide-title="true"
        :footer="false"
        @cancel="deleteCancel"
      >
      <div class="modal-content">
        <div class="modal-header">
          <icon-question-circle-fill style="color: rgb(var(--warning-6));font-size: 28px;margin-right: 8px;"/>
          删除
        </div>
        <div class="modal-body">
          当前选中1个adset，确定删除这些adset？此操作不可逆，请谨慎操作。
        </div>
      </div>
      <div class="modal-footer">
        <a-button style="margin-right: 24px;" @click="deleteCancel">取消</a-button>
        <a-button type="primary" @click="deleteOk">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject} from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import RelatedProducts from "@/views/launch/promotion/components/RelatedProducts.vue";
import TimeSwitch from "@/views/launch/promotion/components/TimeSwitch.vue";
import ExcelEdit from "@/views/launch/promotion/components/ExcelEdit.vue";
import {sumDrillList} from "@/views/launch/promotion/components/promotionData"
import DrillDrawer from "../components/DrillDrawer.vue";

// 设置数据
const filterParams = reactive({
  campaignIds: [],
  accountIds:[],
  adsetIds:[]
})
const dateTime = ref(inject('dateTime') as any[])
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
// 模拟table数据
const loading = ref(false)
const dropdownList = sumDrillList.adset
const tableData = ref<any>([])
const noSumColumns = ref(['adsetName','channel','adsetId'])
const columns = ref<any>([
  { title: '', dataIndex: 'effectiveStatus',slotName:'effectiveStatus',width:100,fixed:'left' },
  { title: '广告组名称', dataIndex: 'adsetName',slotName:'adsetName',width:250,fixed:'left' },
  { title: '渠道', dataIndex: 'channel',slotName:'channel',width:80,fixed:'left' },
  { title: '广告组ID', dataIndex: 'adsetId',minWidth:180 },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])
const adsetOptions = ref<any>([])
const adsetOptionsLoading = ref(false);
const adsetPagination = reactive({
  current: 1,
  pageSize: 20,
});
const campaignOptions = ref<any>([])
const campaignOptionsLoading = ref(false);
const campaignPagination = reactive({
  current: 1,
  pageSize: 20,
});
const accountOptions = ref<any>([])
const accountOptionsLoading = ref(false);
const accountPagination = reactive({
  current: 1,
  pageSize: 20,
});
// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const confirmVisible = ref(false)
const confirmType = ref('')
const statusChange = (record) => {
  confirmType.value = record.authStatus ? 'pause' : 'open'
  confirmVisible.value = true
  return new Promise((resolve, reject) => {
    if(confirmVisible.value){
      resolve(false);
    }
    resolve(true);
  });
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'adset',value:val,recordData:record})
}

const deleteVisible = ref(false)
const deleteCancel = () => {
  deleteVisible.value = false
}
const deleteOk = () => {
  
}
const batchDelete = () => {
  deleteVisible.value = true
}

// 关联产品
const productsRef = ref()
const batchRelated = () => {
  productsRef.value.openModal()
}

// 
const batchOpen = () => {
  confirmVisible.value = true
  confirmType.value = 'open'
}
const batchPause = () => {
  confirmVisible.value = true
  confirmType.value = 'pause'
}
// 定时开关
const timeRef = ref()
const batchTime = () => {
  timeRef.value.openModal()
}
// Excel编辑
const excelRef = ref()
const excelEdit = () => {
  excelRef.value.openModal('adset')
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'all',
      groupLabel:'adset',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      adsetOptions.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
  
}

// 加载广告组选项
const loadAdsetOptions = async () => {
  adsetOptionsLoading.value = true;
  
  try {
    const params = {
      channel:'all',
      groupLabel:'adset',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adsetPagination.pageSize,
      current:adsetPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adsetPagination.current === 1) {
      adsetOptions.value = items;
    } else {
      adsetOptions.value = [...adsetOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告组选项失败:', error);
  } finally {
    adsetOptionsLoading.value = false;
  }
}
const handleAdsetSearch = (v) => {
}
const loadMoreAdsets = async () => {
  adsetPagination.current += 1;
  // 获取更多广告组数据
  await loadAdsetOptions();
}
// 加载广告系列选项
const loadCampaignOptions = async () => {
  campaignOptionsLoading.value = true;
  
  try {
    const params = {
      channel:'all',
      groupLabel:'campaign',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:campaignPagination.pageSize,
      current:campaignPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (campaignPagination.current === 1) {
      campaignOptions.value = items;
    } else {
      campaignOptions.value = [...campaignOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    campaignOptionsLoading.value = false;
  }
};   
const handleCampaignSearch = (v) => {
}
const loadMoreCampaigns = async () => {
  campaignPagination.current += 1;
  // 获取更多广告系列数据
  await loadCampaignOptions();
}
// 加载广告账户选项
const loadAccountOptions = async () => {
  accountOptionsLoading.value = true;
  try {
    const params = {
      channel:'all',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:accountPagination.pageSize,
      current:accountPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    if (accountPagination.current === 1) {
      accountOptions.value = items;
    } else {
      accountOptions.value = [...accountOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    accountOptionsLoading.value = false;
  }
};   
const handleAccountSearch = (v) => {
}
const loadMoreAccounts = async () => {
  accountPagination.current += 1;
  // 获取更多广告账户数据
  await loadAccountOptions();
}
const init = (filter?:any) => {
  filterParams.campaignIds = filter?.ids || []
  getList()
  loadAccountOptions()
  loadCampaignOptions()
}
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>