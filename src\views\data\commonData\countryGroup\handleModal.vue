<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="显示名">
                <a-input v-model="form.name" :disabled="!!form.isAdminEditable && !isAdmin"/>
            </a-form-item>
            <a-form-item field="members" label="国家">
                <CustomSelectWithActions
                    v-if="modalVisible"
                    v-model="form.members"
                    :options="props.countryList"
                    :max-tag-count="3"
                    multiple
                    allow-search
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                    :disabled="!!form.isAdminEditable && !isAdmin"
                />
            </a-form-item>
          <a-form-item field="note" label="备注">
            <a-textarea v-model="form.note" placeholder="" allow-clear :disabled="!!form.isAdminEditable && !isAdmin"/>
          </a-form-item>
          <a-form-item v-if="isAdmin" field="isAdminEditable" label="仅管理员编辑">
              <a-switch v-model="form.isAdminEditable" :checked-value="1" :unchecked-value="0"/>
          </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref,computed} from "vue";
import {Message} from '@arco-design/web-vue';
import {saveCountryGroup} from "@/api/marketing/api";
import {useUserStore} from '@/store';
import CustomSelectWithActions from "@/components/custom-select-with-actions/index.vue";

interface Props {
    countryList:any
}

const props = defineProps<Props>();
const userStore = useUserStore();
const isAdmin = computed(() => {
  return userStore.role.includes('admin');
});
const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    code:'',
    name: '',
    note: '',
    members:[],
    isAdminEditable:0
})
const rules = {
    name: [
        {
            required: true,
            message:'请填写显示名',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);
const openModal = (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    if(obj){
        const {code,name,note,members,isAdminEditable} = obj
        form.code = code
        form.name = name
        form.note = note
        form.members = members
        form.isAdminEditable = isAdminEditable
        modalTitle.value = '编辑国家组'
    }else{
        form.code =''
        form.isAdminEditable = 0
        modalTitle.value = '创建国家组'
    }
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            // 
            try {
                await saveCountryGroup(form);
                if(form.code){
                    Message.success('保存成功')
                }else{
                    Message.success('创建成功');
                }
                modalVisible.value = false;
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>