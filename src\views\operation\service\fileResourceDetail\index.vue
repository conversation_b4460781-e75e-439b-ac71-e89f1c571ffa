<template>
    <a-layout class="layout">
        <a-layout-header class="layout__header">
            <div class="header-title">
                <button class="title-button" @click="goBack">
                    <span role="img">
                      <icon-left size="13"/>
                    </span>
                </button>
                <span style="align-items: center;margin-right: 10px">热更新资源列表</span>
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
                <div class="filter-item">
                    <a-select
                        :style="{width: '300px'}"
                        :loading="bucketOptionsLoading"
                        v-model:model-value="bucket"
                        :options="bucketOptions"
                        placeholder="请选择存储桶"
                        allow-search
                        @change="bucketChange"
                    />
                </div>
                <div class="filter-item">
                    <a-select
                        :style="{width: '120px'}"
                        :loading="versionOptionsLoading"
                        v-model:model-value="version"
                        :options="versionOptions"
                        placeholder="请选择应用版本"
                        allow-search
                        @change="versionChange"
                    />
                </div>
                <div class="filter-item" v-if="classnameOptions.length > 0">
                    <a-select
                        :style="{width: '120px'}"
                        :loading="classnameOptionsLoading"
                        v-model:model-value="classname"
                        :options="classnameOptions"
                        placeholder="请选择子类别"
                        allow-search
                        @change="classnameChange"
                    />
                </div>
                <div class="filter-item">
                    <a-select
                        :style="{width: '120px'}"
                        :loading="resourceVersionOptionsLoading"
                        v-model:model-value="resourceVersion"
                        :options="resourceVersionOptions"
                        placeholder="请选择资源版本"
                        allow-search
                        @change="resourceVersionChange"
                    />
                </div>
            </div>
        </a-layout-header>

        <a-layout-content class="layout__content">
            <a-spin :loading="loading" class="container">
                <a-tree
                    :blockNode="true"
                    :data="treeData"
                    :load-more="loadMore"
                    size="large"
                >
                    <template #title="nodeData">
                        <span v-if="nodeData.type === 'file'" class="link" @click="viewFile(nodeData)">
                            {{nodeData.title}}
                        </span>
                        <span v-else>{{nodeData.title}}</span>
                    </template>
                    <template #extra="nodeData">
                        <a-row v-if="nodeData.type !== 'folder'" style="width: 300px;color: gray">
                            <a-col :span="14" style="font-size: 12px;">
                                <div>日期：{{nodeData.lastModified}}</div>
                            </a-col>
                            <a-col :span="8" style="font-size: 12px;">
                                <div>大小：{{nodeData.size}}</div>
                            </a-col>
                            <a-col :span="2">
                                <a-spin v-if="nodeData.loading" :size="14"/>
                                <a-popconfirm v-else :content="`确定删除文件 ${nodeData.title}？`" type="warning" @ok="deleteResource(nodeData)">
                                    <a-tooltip content="点击删除">
                                        <icon-delete/>
                                    </a-tooltip>
                                </a-popconfirm>
                            </a-col>
                        </a-row>
                    </template>
                </a-tree>
            </a-spin>
        </a-layout-content>
    </a-layout>
</template>

<script setup lang="ts">
import {h, ref, computed} from "vue";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import {useRoute} from 'vue-router';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import { IconFile, IconFileAudio, IconFileImage, IconFileVideo, IconFilePdf, IconFolder } from '@arco-design/web-vue/es/icon';
import selectApp from "@/components/selected-game-app/index.vue"
import {
    deleteFileResource,
    getFileResourceAppBucketList,
    getFileResourceAppClassnamesList,
    getFileResourceAppResourceVersionList,
    getFileResourceAppVersionList,
    getFileResourceFileList
} from "@/api/marketing/api";
import {Message} from "@arco-design/web-vue";


const route = useRoute()
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const appId = ref(useSessionStorage('app-id', '')?.value)
const bucket = ref('');
const version = ref('');
const resourceVersion = ref('');
const classname = ref('');
const bucketOptionsLoading = ref(false)
const bucketOptions = ref([])
const versionOptionsLoading = ref(false)
const versionOptions = ref([])
const classnameOptionsLoading = ref(false)
const classnameOptions = ref([])
const resourceVersionOptionsLoading = ref(false)
const resourceVersionOptions = ref([])
const goBack = () => {
    router.push({
        name: ROUTE_NAME.FILE_RESOURCE,
    });
}

// 获取详情
const loading = ref(false)
const treeData = ref([])
const cdnUrl = ref('')

localStorageEventBus.on((name, value) => {
    if (name === "app-id") {
        appId.value = value
        initBucketOptions()
    }
})

const initBucketOptions = async () => {
    bucketOptionsLoading.value = true
    getFileResourceAppBucketList({
        appId: appId.value
    }).then(res => {
        bucketOptions.value = res || []
        console.log(bucketOptions)
        if (!bucketOptions.value.includes(bucket.value)) {
            if (bucketOptions.value.length) {
                bucket.value = bucketOptions.value[0]
            } else {
                bucket.value = ''
                version.value = ''
                classname.value = ''
                resourceVersion.value = ''
                versionOptions.value = []
                classnameOptions.value = []
                resourceVersionOptions.value = []
                Message.error("未找到资源")
                return
            }
        }
        initVersionOptions()
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        bucketOptionsLoading.value = false
    })
}

const initVersionOptions = async () => {
    versionOptionsLoading.value = true
    getFileResourceAppVersionList({
        bucket: [bucket.value],
        appId: appId.value
    }).then(res => {
        versionOptions.value = res || []
        if (!versionOptions.value.includes(version.value)) {
            if (versionOptions.value.length) {
                version.value = versionOptions.value[0]
            } else {
                version.value = ''
                classname.value = ''
                resourceVersion.value = ''
                classnameOptions.value = []
                resourceVersionOptions.value = []
                Message.error("未找到资源")
                return
            }
        }
        initClassnameOptions()
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        versionOptionsLoading.value = false
    })
}

const initClassnameOptions = async () => {
    classnameOptionsLoading.value = true
    getFileResourceAppClassnamesList({
        bucket: [bucket.value],
        appId: appId.value,
        version: [version.value]
    }).then(res => {
        classnameOptions.value = res || []
        if (!versionOptions.value.includes(version.value)) {
            if (versionOptions.value.length) {
                version.value = versionOptions.value[0]
            } else {
                classname.value = ''
            }
        }
        initResourceVersionOptions()
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        classnameOptionsLoading.value = false
    })
}

const initResourceVersionOptions = async () => {
    resourceVersionOptionsLoading.value = true
    getFileResourceAppResourceVersionList({
        bucket: [bucket.value],
        appId: appId.value,
        version: [version.value],
        classname: classname.value ? [classname.value] : []
    }).then(res => {
        resourceVersionOptions.value = res || []
        if (!resourceVersionOptions.value.includes(resourceVersion.value)) {
            if (resourceVersionOptions.value.length) {
                resourceVersion.value = resourceVersionOptions.value[0]
            } else {
                resourceVersion.value = ''
                Message.error("未找到资源")
                return
            }
        }
        getList()
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        resourceVersionOptionsLoading.value = false
    })
}

const bucketChange = async () => {
    loading.value = true;
    initVersionOptions()
}

const versionChange = async () => {
    loading.value = true;
    initClassnameOptions()
}

const classnameChange = async () => {
    loading.value = true;
    initResourceVersionOptions()
}

const resourceVersionChange = async () => {
    loading.value = true;
    treeData.value = []
    getList()
}

const getList = (nodeData = null) => {
    getFileResourceFileList({
        bucket: bucket.value,
        appId: appId.value,
        version: version.value,
        resourceVersion: resourceVersion.value,
        classname: classname.value,
        folderName: nodeData ? nodeData.key : ''
    }).then(res => {
        cdnUrl.value = res.cdnUrl
        if (nodeData) {
            nodeData.children = []
        }

        if (res.folders) {
            res.folders.forEach(v => {
                let path_info_arr = v.Prefix.split('/')
                let d = {
                    title: path_info_arr[path_info_arr.length - 2],
                    key: v.Prefix,
                    type: 'folder',
                    icon: () => h(IconFolder),
                    children: [],
                }
                if (nodeData) {
                    nodeData.children.push(d)
                } else {
                    treeData.value.push(d)
                }
            })
        }

        if (res.files) {
            res.files.forEach(v => {
                let path_info_arr = v.Key.split('/')
                let filename = path_info_arr[path_info_arr.length - 1]
                const lastDotIndex = filename.lastIndexOf('.');
                const ext =  lastDotIndex > 0 ? filename.slice(lastDotIndex + 1).toLowerCase() : '';
                let file_type = 'unknown'
                if (['mp3','wav','flac','aac','ogg','wma','aiff','alac','dsf','dff','ape'].includes(ext)) {
                    file_type = 'audio'
                } else if (['mp4','avi','mkv','mov','wmv','flv','webm','mpg','mpeg','mts','m2ts','ts'].includes(ext)) {
                    file_type = 'video'
                } else if (['jpg','jpeg','png','gif','bmp','tif','tiff','svg','webp','heif','heic','arw','cr2','nef'].includes(ext)) {
                    file_type = 'image'
                } else if (['pdf'].includes(ext)) {
                    file_type = 'pdf'
                }

                let d = {
                    title: filename,
                    key: v.Key,
                    type: 'file',
                    size: Math.ceil(parseInt(v.Size)/1024) + 'KB',
                    lastModified: formatTimeToUTC8(v.LastModified),
                    icon: () => {
                        if (file_type === 'audio') {
                            return h(IconFileAudio)
                        } else if (file_type === 'image') {
                            return h(IconFileImage)
                        } else if (file_type === 'video') {
                            return h(IconFileVideo)
                        } else if (file_type === 'pdf') {
                            return h(IconFolder)
                        } else {
                            return h(IconFile)
                        }
                    },
                    isLeaf: true,
                }

                if (nodeData) {
                    nodeData.children.push(d)
                } else {
                    treeData.value.push(d)
                }
            })
        }
    }).finally(() => {
        loading.value = false
    })
}

const loadMore = (nodeData) => {
    getList(nodeData)
}

const viewFile = (nodeData) => {
    window.open(`${cdnUrl.value}/${nodeData.key}`)
}

const deleteResource = (nodeData) => {
    nodeData.loading = true

    deleteFileResource({
        bucket: bucket.value,
        appId: appId.value,
        version: version.value,
        resourceVersion: resourceVersion.value,
        classname: classname.value,
        fileKey: nodeData.key
    }).then(res => {
        if (res === 'ok') {
            deleteTreeNodeByKey(treeData.value, nodeData.key)
            Message.success(`删除成功`)
        } else {
            nodeData.loading = false
            Message.error(`删除失败`)
        }
    }).catch(e => {
        console.log(e)
        nodeData.loading = false
        Message.error(`删除失败`)
    })
}

const deleteTreeNodeByKey = (data, keyToDelete) => {
    data.some((v, k) => {
        if (v.type === 'file') {
            if (keyToDelete === v.key) {
                data.splice(k, 1)
                return true
            }
        } else if (keyToDelete.includes(v.key) && v.children) {
            deleteTreeNodeByKey(v.children, keyToDelete)
        }
    });
}

const formatTimeToUTC8 = (isoTime) => {
    const date = new Date(isoTime);

    // 使用 toLocaleString 并指定时区为 UTC+8
    const opts = {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };

    const formatted = new Intl.DateTimeFormat('zh-CN', opts).format(date);

    // 输出格式为 YYYY-MM-DD HH:mm:ss
    return formatted.replace(/\//g, '-').replace(/,/, '');
}

const init = async () => {
    loading.value = true;
    // 获取文件列表
    bucket.value = route.query.bucket as string || '';
    version.value = route.query.version as string || '';
    resourceVersion.value = route.query.resourceVersion as string || '';
    classname.value = route.query.classname as string || '';
    initBucketOptions()
}
init()
</script>

<style scoped lang="less">
.layout {
    background-color: var(--tant-bg-gray-color-bg2-1);
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100vh;

    .layout__header {
        display: flex;
        align-items: center;
        height: 56px;
        padding: 12px 24px;
        background-color: var(--tant-bg-white-color-bg1-1);

        .header-title {
            flex: 1 1;
            color: var(--tant-text-gray-color-text1-1);
            font: var(--tant-header-font-header4-medium);
            align-items: center;

            .title-button {
                color: var(--tant-text-gray-color-text1-2);
                background-color: transparent;
                border: none;
                margin-right: 4px;
                padding: 8px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                height: 32px;
                font: var(--tant-body-font-body-regular);
                text-transform: capitalize;
                border-radius: var(--tant-border-radius-medium);
                box-shadow: unset;
            }

            .title-button:hover {
                background-color: var(--tant-secondary-color-secondary-transp-hover);
                cursor: pointer;
                transition: .5s;

            }

            .title-button > span {
                display: flex;
                align-items: center;
                line-height: normal;
            }

        }
        .filter {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
        }
    }

    .layout__content {
        display: flex;
        flex: 1 1;
        height: calc(100vh - 100px);
        margin: 24px;
        overflow: hidden;
        background-color: var(--tant-bg-white-color-bg1-1);
        border-radius: 4px;
    }
}

.container {
    width: 100%;
    height: calc(100vh - 120px);
    overflow-y: auto;
    padding: 20px 20px;
}

:deep(.arco-tabs-content) {
    height: calc(100vh - 160px);
    overflow: auto;
}
.link {
    color: rgb(var(--primary-6));
    cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
}
</style>
