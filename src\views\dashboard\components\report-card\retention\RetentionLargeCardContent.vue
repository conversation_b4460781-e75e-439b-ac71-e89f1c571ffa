<template>
  <div class="card-content">
    <div class="card-toolbar">
      <div class="card-filter">
        <dateDropdown :time-span-data="timeSpanData" :show-border="false" @time-span-change="timeSpanChange"/>
        <a-divider :margin="8" direction="vertical"/>
        <a-dropdown :update-at-scroll="true" @select="handleSelect">
          <span style="line-height: 24px;cursor: pointer;">{{ retentionName }}</span>
          <template #content>
            <a-doption value="retention">留存</a-doption>
            <a-doption value="churn">流失</a-doption>
          </template>
        </a-dropdown>
        <a-divider :margin="8" direction="vertical"/>
        <date-picker :date-range="boardDate" position="tl" @date-pick="datePick"/>
      </div>
      <div class="card-config">
        <a-select
            id="select"
            v-model="selectValue"
            :bordered="false"
            :style="{width:'92px',padding:'0 0 0 13px',position:'relative'}"
            default-value="trend-chart"
            @change="handleChange"
        >
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/'+data?.value+'-chart.svg'" alt=""/>
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon>
          </template>
          <a-popover title="表格" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option :value="ChartType.TABLE">
              <template #icon>
                <img class="option-icon" src="/icon/table-chart.svg" alt=""/>
              </template>
              <template #default>表格</template>
            </a-option>
            <template #content>
              <div>
                <p>展示各日期下的留存分布情况</p>
                <img :src="table" alt="" style="width: 90%;height: 90%;"/>
              </div>
            </template>
          </a-popover>
          <a-popover title="趋势图" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option :value="ChartType.TREND">
              <template #icon>
                <img class="option-icon" src="/icon/trend-chart.svg" alt=""/>
              </template>
              <template #default>趋势图</template>
            </a-option>
            <template #content>
              对比在不同日期完成初始事件的用户，在后续第N日的留存/流失指标 <br>
              <img :src="trend" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
        </a-select>
      </div>
    </div>
    <a-alert v-if="reportData?.resultsExceedsLimit" style="margin-top: 5px;flex-shrink: 0;">
      因数据条数过多，优先展示前1000条数据
    </a-alert>
    <div class="data-chart" :style="{height: chartHeight}">
      <div class="chart-box">
        <TableChart v-show="showTable" ref="tableRef" :retention-type="retentionType" :name="props.name" :report-analysis-data="reportData"/>
        <TrendChart v-show="showTrend" ref="trendChartRef" :retention-type="retentionType" :report-analysis-data="reportData"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watch} from "vue";
import {table, trend} from "@/views/dashboard/components/img"
import {ReportQueryResponse} from "@/api/type";
import {ChartType} from "@/api/enum";
import DatePicker from "@/components/date-picker/index.vue";
import useReportDataStore from "@/store/modules/report";
import TrendChart from "@/views/dashboard/components/table-chart/retention/RetentionChart.vue";
import TableChart from "@/views/dashboard/components/table-chart/retention/RetentionTable.vue";
import dateDropdown from "@/views/analyse/components/dateDropdown.vue";
import {DateRange} from "@/api/analyse/type";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  report: any;
  name: string
  dataRangeData: any
}
const props = defineProps<Props>();
const emits = defineEmits(['changeParams', 'update:modelValue', 'dateChange', 'queryParamsChange']);
const reportDataStore = useReportDataStore()

const trendChartRef = ref()
const reportData = ref<any>();
const timeSpanData = ref({
  firstDayDfWeek: 1,
  number: 7,
  unit: "DAY"
})
const showTable = ref<boolean>(true);
const showTrend = ref<boolean>(false);
const selectValue = ref<ChartType>(ChartType.TABLE);
const boardDate = ref<DateRange>({...props.dataRangeData});
const retentionName = ref('留存')
const retentionType = ref('retention')
const tableRef = ref()


const timeSpanChange = (v) => {
  timeSpanData.value = v
  emits('queryParamsChange', {timeSpan: timeSpanData.value})
}
const handleSelect = (v) => {
  retentionType.value = v
  retentionName.value = v === 'retention' ? '留存' : '流失'
}
const handleChange = (value: ChartType, frash?: boolean) => {
  switch (value) {
    case ChartType.TABLE:
      showTable.value = true;
      showTrend.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TABLE})
      }
      break;
    case ChartType.TREND:
      showTable.value = false;
      showTrend.value = true
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TREND})
      }
      break;
    default:
      break;
  }
};
const transformData = (data: ChartType) => {
  switch (data) {
    case ChartType.TABLE:
      selectValue.value = ChartType.TABLE
      handleChange(ChartType.TABLE, true)
      break
    case ChartType.TREND:
      selectValue.value = ChartType.TREND
      handleChange(ChartType.TREND, true)
      break
    default:
      break
  }
}
const datePick = (date: any) => {
  boardDate.value = date
  reportDataStore.setDateRanges(props.report.objectCode, date);
  emits('dateChange', boardDate.value)
};
const exportXlsx = (date: any, name?: string) => {
  tableRef.value?.exportXlsx(date, name)
}
const init = () => {
  transformData(props.report.configParams?.chartType || ChartType.TABLE)
}

// 监听报表状态
watch(() => reportDataStore, (newVal) => {
  timeSpanData.value = reportDataStore.getTimeSpans(props.report.objectCode)
}, {immediate: true, deep: true})
// 添加对 dataRangeData 的监听
watch(() => props.dataRangeData, (newVal) => {
  boardDate.value = newVal
}, {deep: true})
watch(() => props.reportAnalysisData, (newData) => {
  if (newData === undefined) {
    return
  }
  const result = newData?.result;
  reportData.value = result;
  timeSpanData.value = result?.timeSpan
})

// 图标高度
const chartHeight = computed(() => reportData.value?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)')
defineExpose({
  exportXlsx
})

init()
</script>

<style scoped lang="less">
@import "@/views/dashboard/style/card.less";
</style>
