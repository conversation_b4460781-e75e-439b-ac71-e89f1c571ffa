<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    名称检索：
                    <a-input
                        v-model:model-value="searchName"
                        :style="{width:'240px'}"
                        allow-clear
                        placeholder="请输入需要检索的名称"
                        @change="getAttrList"/>
                </div>
                <div class="filter-item">
                    <a-button type="primary" @click="getAttrList">搜索</a-button>
                </div>
                <div class="filter-item">
                    <a-button type="primary" @click="showCreateAttribution">
                        <icon-plus/>
                    </a-button>
                </div>
            </div>
        </div>
        <div class="page-body">
            <a-table
                :loading="loading"
                :columns="columns"
                :data="data"
                :scrollbar="true"
                :pagination="{
                    showPageSize:true,
                    defaultPageSize:20,
                    showJumper: true,
                    autoAdjust: true,
                }"
                :scroll="{x:'100%', y:'100%'}"
                :bordered="false"
                :hoverable="true"
                sticky-header
                :table-layout-fixed="true"
                :filter-icon-align-left="true"
                :column-resizable="true"
            >
                <template #optional="{ record }">
                    <a-button  @click="editData(record)" type="text">编辑</a-button>
                </template>
            </a-table>
        </div>
        <createAttributionModal ref="attributionRef" @attribution-change="getAttrList" />
    </div>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import {ref} from "vue";
import createAttributionModal from "./components/createAttributionModal.vue";
import _ from "lodash";
import {getAttributionInputTypeComment} from "@/api/enum";
import {getFieldTypeName} from "@/api/type";
import {getAttributionList} from "@/api/marketing/api";

const route = useRoute();
const attributionRef = ref()
const searchName = ref('')
const editModalShow = ref(false)
const loading = ref(false)
const data = ref<any>([]);
const columns = ref<any>([
    {
        title: '属性编码',
        dataIndex: 'code',
        fixed: 'left',
        width: 160,
    },
    {
        title: '属性名称',
        dataIndex: 'name',
        width: 200,
        ellipsis: true,
        tooltip: true,
    },
    {
        title: '属性值类型',
        dataIndex: 'valueType',
        width: 200,
        render: (value) => {
            const {record} = value;
            return getFieldTypeName(record.valueType)
        },
        align: 'center',
    },
    {
        title: '取值途径',
        dataIndex: 'obtain_method',
        width: 200,
        render: (value) => {
            const {record} = value;
            return getAttributionInputTypeComment(record.obtainMethod)
        },
        align: 'center',
    },
    {
        title: '创建人',
        dataIndex: 'creator',
        width: 120,
        render: (value) => {
            const {record} = value;
            return record?.creator?.name || ''
        },
        align: 'center',
    },
    {
        title: '操作',
        slotName: 'optional',
        align: 'center',
        fixed: 'right',
        width: 160
    },
]);

const showCreateAttribution = () => {
    attributionRef.value.openModal()
}

const editData = (record) => {
    attributionRef.value.openModal(record, 'edit')
}

const getAttrList = () => {
    loading.value = true
    getAttributionList({}).then(res => {
        data.value = _.isEmpty(searchName.value) ? res : res?.filter(item => {
            return item.code?.includes(searchName.value) || item.name?.includes(searchName.value)
        })
    }).finally(() => {
        loading.value = false
    })
}

const init = () => {
    getAttrList()
}

init()

</script>

<style scoped lang="less">
.page {
    .page-head {
        .filter {
            .filter-item {
                white-space: nowrap;
            }
        }
    }
}
</style>
