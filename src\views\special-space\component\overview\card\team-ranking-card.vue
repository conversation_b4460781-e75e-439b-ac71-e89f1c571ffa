<script setup lang="ts">
import {watch} from "vue";
import TeamRanking from "@/views/special-space/component/overview/chart/team-ranking.vue";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 分组
   */
  group: string[]

  /**
   * 数据
   */
  data: any[]

  /**
   * 详细数据名
   */
  dataName: string

  /**
   * 加载中
   */
  loading: boolean
}

const props = defineProps<Props>()
const emits = defineEmits(['showFullScreen']);
watch(props,()=>{
})
</script>

<template>
  <div class="report-half-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ props.dataName }}
      </div>
      <div class="report-card-indicator">
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="全屏">
          <div class="operation-icon" @click="()=>{}">
<!--            <icon-fullscreen class="icon"/>-->
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-content">
      <a-spin :loading="!props.group || props.group?.length == 0 || props.loading" style="width: 100%; height: 100%">
        <team-ranking
            :date="props.date"
            :group="props.group"
            :data="props.data"
            :data-name="props.dataName"
        />
      </a-spin>
    </div>
  </div>

</template>

<style scoped lang="less">
@import './style.less';

</style>