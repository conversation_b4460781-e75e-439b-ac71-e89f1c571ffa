<template>
  <div ref="pageContainer" class="page">
    <div :class="showMainHeaderActions ? 'page-head-multiple-fold' : 'page-head-multiple-unfold'">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          <select-app-list @change="appChange" />
        </div>
        <div class="filter-item">
          <SelectedTeamList v-model:teams="params.teamCodes" @change="teamChange" />
        </div>
        <div class="filter-item">
          <selectCountryList :default-codes="params.country" @countrys-change="countryChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.date" disabled-after-today @date-pick="datePick" />
        </div>
        <div v-if="showMainHeaderActions" class="filter-item">
          <a-select v-model:model-value="params.dateType" :style="{ width: '80px' }" placeholder="选择时间粒度" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="search">
            <a-option value="day">按天</a-option>
            <a-option value="week">按周</a-option>
            <a-option value="month">按月</a-option>
          </a-select>
        </div>
        <div v-if="showMainHeaderActions" class="filter-item">
          <a-select v-model:model-value="params.times" :style="{ width: '80px' }" placeholder="选择周期" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="search">
            <a-option :value="30">30</a-option>
            <a-option :value="60">60</a-option>
            <a-option :value="90">90</a-option>
            <a-option :value="180">180</a-option>
          </a-select>
        </div>
        <div v-if="showMainHeaderActions" class="filter-item">
          <a-button type="primary" :loading="tableLoading" @click="search">查询</a-button>
        </div>
      </div>
    </div>
    <div v-if="!showMainHeaderActions" class="sub-header">
      <div class="filter-line" style="border-radius: 0 0 9px 9px">
        <div class="filter-item">
          <a-select v-model:model-value="params.dateType" :style="{ width: '80px' }" placeholder="选择时间粒度" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="search">
            <a-option value="day">按天</a-option>
            <a-option value="week">按周</a-option>
            <a-option value="month">按月</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-select v-model:model-value="params.times" :style="{ width: '80px' }" placeholder="选择周期" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="search">
            <a-option :value="30">30</a-option>
            <a-option :value="60">60</a-option>
            <a-option :value="90">90</a-option>
            <a-option :value="180">180</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-button type="primary" :loading="tableLoading" @click="search">查询</a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <div v-if="loading" class="spin">
        <a-spin :loading="loading" class="spin-icon" dot>
          <template #tip>
            <span class="spin-tip">加载中...</span>
          </template>
        </a-spin>
      </div>
      <div v-if="!loading" class="card-content">
        <div class="handle-box">
          <div class="handle-left">
            <a-checkbox v-model="handleParams.recycle">显示回收</a-checkbox>
            <a-checkbox v-model="handleParams.meanwhile">显示留存</a-checkbox>
            <a-checkbox v-model="handleParams.isOrganic" style="margin-left: 8px" @change="organicChange">Organic</a-checkbox>
          </div>
          <div class="handle-right">
            <div class="edit-select" style="width: 180px; margin-right: 8px; flex-shrink: 0">
              <a-select v-model:model-value="rowShowCode" allow-clear placeholder="数据列选择" :style="{ border: '1px solid var(--color-fill-3)' }" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="rowChange">
                <a-option v-for="item in rowGroupList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
              </a-select>
              <a-tooltip content="编辑">
                <icon-edit size="16" class="edit" @click="editRow" />
              </a-tooltip>
            </div>
            <a-select v-model:model-value="params.group" :style="{ marginRight: '8px', border: '1px solid var(--color-fill-3)', borderRadius: '4px' }" placeholder="选择分组" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="adGroupChange">
              <a-option :value="0">不分组</a-option>
              <a-option :value="1">媒体渠道</a-option>
              <a-option :value="2">广告系列</a-option>
              <a-option :value="3">广告组</a-option>
              <a-option :value="4">广告</a-option>
            </a-select>
            <div :style="{ 'min-width': '220px' }">
              <a-select v-model:model-value="params.agency" :style="{ marginRight: '8px', border: '1px solid var(--color-fill-3)', borderRadius: '4px' }" :options="agencyData" placeholder="代理选择" :max-tag-count="1" :tag-nowrap="true" @change="search" multiple></a-select>
            </div>
            <a-trigger position="left" :show-arrow="params.adFilter.length > 0 && showDrop" :arrow-style="{ backgroundColor: '#475466' }">
              <div style="flex-shrink: 0; min-width: 240px; margin-right: 8px">
                <a-tree-select
                  v-model="selected"
                  :loading="treeLoading"
                  :style="{ border: '1px solid var(--color-fill-3)', borderRadius: '4px' }"
                  :allow-clear="true"
                  :data="treeData"
                  placeholder="筛选"
                  :max-tag-count="1"
                  :tree-checkable="true"
                  :scrollbar="true"
                  tree-checked-strategy="parent"
                  :allow-search="true"
                  :tree-props="{
                    virtualListProps: {
                      height: 500,
                    },
                    defaultExpandedKeys: ['all'],
                  }"
                  dropdown-class-name="treeClass"
                  @change="filterChange"
                  @popup-visible-change="popChange"
                >
                </a-tree-select>
              </div>
              <template #content>
                <div v-if="params.adFilter.length && showDrop" class="filter-select-content">
                  <div v-for="(item, index) in params.adFilter" :key="index" class="select-item">
                    <span> 媒体查询：{{ item.media_source }} </span>
                    <span v-if="item.campaign"> <icon-arrow-right />广告系列：{{ item.campaign }} </span>
                    <span v-if="item.adset"> <icon-arrow-right />广告组：{{ item.adset }} </span>
                    <span v-if="item.ad"> <icon-arrow-right />广告素材：{{ item.ad }} </span>
                    ;
                  </div>
                </div>
              </template>
            </a-trigger>
            <a-radio-group v-model:model-value="handleParams.valueType" type="button">
              <a-radio value="default">123</a-radio>
              <a-radio value="percent">%</a-radio>
            </a-radio-group>
            <a-tooltip content="导出">
              <div class="operation-icon" @click="exportXlsx">
                <icon-download class="icon" />
              </div>
            </a-tooltip>
          </div>
        </div>
        <a-table ref="tableRef" :loading="tableLoading" :columns="columns" :data="tableData" :table-layout-fixed="true" :pagination="pagination" size="small" :scroll="scroll" @page-change="pageChange">
          <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record, rowIndex }">
            <div v-if="column.dataIndex.includes('incomeD')" class="cell-class" :style="getBackgroundColor(rowIndex, record[column.dataIndex])">
              <div v-if="handleParams.recycle">{{ handleRoiValue(record, record[column.dataIndex]) }}<span v-if="handleParams.valueType === 'percent' && record[column.dataIndex] != null">%</span></div>
              <div v-if="handleParams.meanwhile">{{ handleRetentionValue(record, column.dataIndex) }}</div>
              <div v-if="!handleParams.meanwhile && !handleParams.recycle">-</div>
            </div>
            <div v-if="column.dataIndex === 'date'" class="cell-class">
              <div :style="isWeekend(record[column.dataIndex]) ? 'color:red' : ''">{{ record[column.dataIndex] }}</div>
            </div>
            <div v-if="column.dataIndex === 'spend'" class="cell-class" :style="getRoiColor(record)">
              {{ record[column.dataIndex] }}
            </div>
            <div v-if="column.dataIndex === 'maEcpm'" class="cell-class">
              <a class="href-name" @click="openModal(record)">{{ record[column.dataIndex] }}</a>
            </div>
            <div v-if="column.dataIndex === 'predict'" class="cell-class">
              <span v-if="record[column.dataIndex] === '数据不足'">
                <a class="href-name" style="color: red" @click="openPreModal(record)">{{ record[column.dataIndex] }}</a>
              </span>
              <span v-else>
                <a class="href-name" @click="openPreModal(record)">{{ record[column.dataIndex] }}</a>
                <p v-if="record[column.dataIndex] === '无法回本'" class="text-tip" style="margin-bottom: 4px">180天回本{{ record.d180 }}</p>
                <p v-if="record[column.dataIndex] === '无法回本'" class="text-tip">360天回本{{ record.d360 }}</p>
              </span>
            </div>
            <div v-if="['new', 'ecpi', 'apEcpm', 'cumulativeProfit', 'cumulativeRevenue', 'cumulativeSpend', 'cumulativeInstalls', 'runningDays'].includes(column.dataIndex)" class="cell-class">
              {{ record[column.dataIndex] }}
            </div>
            <div v-if="['channel', 'campaign', 'adset', 'ad'].includes(column.dataIndex)" class="cell-class">
              <a-tooltip :content="record[column.dataIndex]">
                <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis">{{ record[column.dataIndex] }}</div>
              </a-tooltip>
            </div>
          </template>
        </a-table>
      </div>
    </div>
    <detailModal ref="ecpmRefs" :filter-params="params" />
    <predictModal ref="predictRefs" :filter-params="params" />
    <rowSelect ref="rowRefs" @update-row="updateRow" />
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { useElementSize, useEventBus, useSessionStorage } from '@vueuse/core';
  import selectCountryList from '@/components/selected-country-list/index.vue';
  import DatePicker from '@/components/date-picker/index.vue';
  import { getAgencyList, getAnalyticsViewDetail, getAnalyticsViewList, getMediaSourceList, getPlacementRetention, getPlacementRoi } from '@/api/analyse/api';
  import * as XLSX from 'xlsx';
  import selectAppList from '@/components/selected-game-app-list/index.vue';
  import { LocalStorageEventBus } from '@/types/event-bus';
  import { useRoute } from 'vue-router';
  import { getDateRangeEndDate, getDateRangeStartDate } from '@/utils/dateUtil';
  import { isEqual } from 'lodash';
  import { usePageFilter } from '@/utils/filterConfigUtil';
  import SelectedTeamList from '@/components/selected-team-list/index.vue';
  import detailModal from './components/detailModal.vue';
  import predictModal from './components/predictModal.vue';
  import rowSelect from './components/rowSelect.vue';

  const pageContainer = ref<HTMLElement | null>(null);
  const { width: containerWidth } = useElementSize(pageContainer);
  const showMainHeaderActions = computed(() => containerWidth.value > 1280);
  const route = useRoute();
  const appIdsRef = useSessionStorage('app-id-list', []);
  const loading = ref(false);
  const localStorageEventBus = useEventBus(LocalStorageEventBus);

  const params = reactive({
    appIds: appIdsRef,
    country: ['global'],
    teamCodes: [],
    date: {
      recentStartDate: 29,
      recentEndDate: 0,
      dateText: '最近30天',
    },
    times: 30,
    dateType: 'day',
    adFilter: [] as any,
    group: 0,
    agency: [],
  });
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(params);
  const lastParams = ref({
    appIds: [],
    country: [],
    teamCodes: [],
  });
  const selected = ref(['all']);
  const treeData = ref([
    {
      title: '媒体渠道',
      value: 'all',
      key: 'all',
      children: [] as any,
    },
  ]);

  const agencyLoading = ref(false);
  const agencyData = ref([]);

  const treeLoading = ref(false);
  const retentionData = ref<any>([]);
  const tableData = ref<any>([]);
  const handleParams = reactive({
    recycle: true,
    meanwhile: true,
    isOrganic: true,
    valueType: 'percent',
  });
  // 判断是不是周末
  const isWeekend = (dateString) => {
    if (params.dateType === 'day') {
      const date = new Date(dateString);
      const day = date.getDay(); // 获取星期几，0 是周日，6 是周六
      return day === 0 || day === 6; // 如果是周日或周六，则返回 true
    }
    return false;
  };

  const retentionDataMap = new Map();
  const tableDataMap = new Map();

  const initializeRetentionDataMap = () => {
    retentionData.value.forEach((item) => {
      const key = `${item.date}${item?.channel}${item?.campaign}${item?.adset}${item?.ad}`;
      retentionDataMap.set(key, item);
    });
  };
  const initializeTableDataMap = () => {
    tableData.value.forEach((item) => {
      const key = `${item.date}${item?.channel}${item?.campaign}${item?.adset}${item?.ad}`;
      tableDataMap.set(key, item);
    });
  };
  const handleRoiValue = (record, value) => {
    const compareString = `${record.date}${record?.channel}${record?.campaign}${record?.adset}${record?.ad}`;
    const item = tableDataMap.get(compareString); // 使用 Map 进行查找
    if (value != null) {
      if (value === 0) {
        return '0.00';
      }
      if (handleParams.valueType === 'percent' && item.spend) {
        return ((value / item.spend) * 100).toFixed(2);
      }
      if (handleParams.valueType === 'percent' && !item.spend) {
        return '0.00';
      }
      return value;
    }
    return '--';
  };
  const handleRetentionValue = (record, dataIndex) => {
    // 使用正则表达式提取数字
    const newValue = record.new;
    const match = dataIndex.match(/\d+/);
    const index = match ? parseInt(match[0], 10) : null;

    if (index != null) {
      const compareString = `${record.date}${record?.channel}${record?.campaign}${record?.adset}${record?.ad}`;
      const item = retentionDataMap.get(compareString); // 使用 Map 进行查找

      if (item) {
        const num = item[`d${index}`] ?? null;
        if (num != null && newValue) {
          if (handleParams.valueType === 'percent') {
            return `${((num / newValue) * 100).toFixed(2)}%`;
          }
          return Math.round(num);
        }
      }
    }
    return '--'; // 如果没有找到匹配项或条件不满足，返回 '--'
  };
  const currentPage = ref(1); // 当前页码

  const pagination = computed(() => {
    if (tableData.value?.length > 100) {
      return {
        current: currentPage.value,
        pageSize: 30,
        total: tableData.value.length,
      };
    }
    return false;
  });
  const pageChange = (v) => {
    currentPage.value = v;
  };
  // 校验selected的值变化后还在不在树状数据中
  const validateSelected = () => {
    const flattenTreeData = (nodes) => {
      let result = [];
      nodes.forEach((node) => {
        result.push(node.value);
        if (node.children && node.children.length > 0) {
          result = result.concat(flattenTreeData(node.children));
        }
      });
      return result;
    };
    // 获取所有可用的 treeData 值
    const availableValues = flattenTreeData(treeData.value);
    // 过滤出存在于 treeData 中的值
    const validSelected = selected.value.filter((value) => availableValues.includes(value));
    // 如果过滤后没有有效值，则设置为默认值
    if (validSelected.length === 0) {
      selected.value = ['all'];
    } else if (validSelected.length !== selected.value.length) {
      // 如果有值被过滤掉，更新 selected
      selected.value = validSelected;
    }
  };
  // 定义一个特殊分隔符
  const SEPARATOR = '@@##@@';
  // 获取媒体筛选列表
  const getMediaList = async () => {
    treeLoading.value = true;
    agencyLoading.value = true;
    treeData.value[0].children = [];
    const data = {
      appId: params.appIds.join(','),
      country: params.country.join(','),
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
    };

    async function getMSList() {
      await getMediaSourceList(data).then((res) => {
        // 分组，五层结构，all-全部，mediasource, campaign,adset,ad分组
        const groupedData = res.reduce((acc, item) => {
          const source = item.mediaSource;
          if (!acc[source]) {
            acc[source] = [];
          }
          acc[source].push(item);
          return acc;
        }, {});

        Object.keys(groupedData).forEach((source) => {
          const campaignGrouped = groupedData[source].reduce((acc, item) => {
            const campaign = item.campaign;
            if (!acc[campaign]) {
              acc[campaign] = [];
            }
            acc[campaign].push(item);
            return acc;
          }, {});
          treeData.value[0].children.push({
            title: source,
            value: source,
            key: source,
            children: Object.keys(campaignGrouped)
              .map((campaign) => {
                const adsetGrouped = campaignGrouped[campaign].reduce((acc, item) => {
                  const adset = item.adset;
                  if (!acc[adset]) {
                    acc[adset] = [];
                  }
                  acc[adset].push(item);
                  return acc;
                }, {});

                return {
                  title: campaign,
                  value: `${source}${SEPARATOR}${campaign}`,
                  key: `${source}${SEPARATOR}${campaign}`,
                  children: Object.keys(adsetGrouped)
                    .map((adset) => {
                      return {
                        title: adset,
                        value: `${source}${SEPARATOR}${campaign}${SEPARATOR}${adset}`,
                        key: `${source}${SEPARATOR}${campaign}${SEPARATOR}${adset}`,
                        children: adsetGrouped[adset]
                          .map((el) => {
                            return {
                              ...el,
                              title: el.ad,
                              value: `${source}${SEPARATOR}${campaign}${SEPARATOR}${adset}${SEPARATOR}${el.ad}`,
                              key: `${source}${SEPARATOR}${campaign}${SEPARATOR}${adset}${SEPARATOR}${el.ad}`,
                            };
                          })
                          .filter((ff) => ff.ad),
                      };
                    })
                    .filter((ff) => ff.title),
                };
              })
              .filter((ff) => ff.title),
          });
        });
      });
    }

    async function getAGList() {
      await getAgencyList(data).then((res) => {
        agencyData.value = [];
        res.forEach((v) => {
          if (v['agency']) {
            agencyData.value.push(v['agency']);
          }
        });
      });
    }

    await Promise.all([getMSList(), getAGList()]);
    validateSelected();
    treeLoading.value = false;
    agencyLoading.value = false;
  };

  // const columns = ref<any>([]);
  const rowShowCode = ref('');
  const rowGroupList = ref<any>([]);
  const selectRowList = ref<any>([]);
  // 处理表头
  const columns = computed(() => {
    const list = [
      {
        title: '日期',
        dataIndex: 'date',
        width: 140,
        slotName: 'date',
        fixed: 'left',
        sortable: { sortDirections: ['ascend', 'descend'] },
      },
      {
        title: '投放',
        dataIndex: 'spend',
        width: 140,
        slotName: 'spend',
        fixed: 'left',
        sortable: { sortDirections: ['ascend', 'descend'] },
      },
      {
        title: '新增',
        dataIndex: 'new',
        width: 140,
        slotName: 'new',
        fixed: 'left',
      },
      {
        title: 'ecpi',
        dataIndex: 'ecpi',
        width: 140,
        slotName: 'ecpi',
        fixed: 'left',
      },
    ];
    if (selectRowList.value.length) {
      selectRowList.value.forEach((item) => {
        list.push({
          title: item.name,
          dataIndex: item.code,
          width: 140,
          slotName: item.code,
          // fixed: 'left',
        });
      });
    }
    if (params.group === 1) {
      list.splice(1, 0, {
        title: '媒体渠道',
        dataIndex: 'channel',
        width: 160,
        slotName: 'channel',
        fixed: 'left',
      });
    } else if (params.group === 2) {
      list.splice(1, 0, {
        title: '广告系列',
        dataIndex: 'campaign',
        width: 160,
        slotName: 'campaign',
        fixed: 'left',
      });
    } else if (params.group === 3) {
      list.splice(1, 0, {
        title: '广告组',
        dataIndex: 'adset',
        width: 160,
        slotName: 'adset',
        fixed: 'left',
      });
    } else if (params.group === 4) {
      list.splice(1, 0, {
        title: '广告',
        dataIndex: 'ad',
        width: 160,
        slotName: 'ad',
        fixed: 'left',
      });
    }
    // if(handleParams.backCycle){
    //   const spendIndex = list.findIndex(col => col.dataIndex === 'spend');
    //   if (spendIndex !== -1) {
    //       list.splice(spendIndex + 1, 0, {
    //           title: '回本周期',
    //           dataIndex: 'predict',
    //           width: 140,
    //           slotName: 'predict',
    //           fixed: 'left',
    //       });
    //   }
    // }
    for (let i = 0; i <= params.times; i++) {
      list.push({
        title: `第${i}天`,
        dataIndex: `incomeD${i}`,
        minWidth: 140,
        slotName: `incomeD${i}`,
      });
    }
    return list;
  });
  const scroll = {
    y: '92%',
    x: '100%',
  };
  const getDaysDiff = (dateStr1, dateStr2) => {
    const date1 = new Date(dateStr1);
    const date2 = new Date(dateStr2);
    const utc1 = Date.UTC(date1.getFullYear(), date1.getMonth(), date1.getDate());
    const utc2 = Date.UTC(date2.getFullYear(), date2.getMonth(), date2.getDate());
    return Math.floor((utc2 - utc1) / (1000 * 60 * 60 * 24));
  };
  // utc时间
  const getRoiData = async () => {
    // handleColumns()
    const data = {
      appId: params.appIds.join(','),
      country: params.country.join(','),
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
      times: params.times,
      dateType: params.dateType,
      adFilter: params.adFilter,
      group: params.group,
      predict: true,
      agencyFilter: params.agency,
    };
    await getPlacementRoi(data).then((res) => {
      tableData.value = res?.map((item) => {
        let predictValue;
        if (item.predict > 0) {
          predictValue = item.predict;
        } else if (item.predict === 0) {
          predictValue = '数据不足';
        } else if (item.predict === null) {
          predictValue = '无法回本';
        }
        return {
          ...item,
          predict: predictValue,
        };
      });
      const now = new Date();
      const utcYear = now.getUTCFullYear();
      const utcMonth = now.getUTCMonth() + 1;
      const utcDate = now.getUTCDate();
      const compartDate = `${utcYear}-${utcMonth}-${utcDate}`;
      tableData.value.forEach((item) => {
        for (let i = 0; i <= params.times; i++) {
          const incomeKey = `incomeD${i}`;
          const incomeDate = new Date(item.date);
          // 计算日期差异
          const timeDiff = getDaysDiff(incomeDate, compartDate);
          // 超出日期部分置为null
          if (i > timeDiff && params.dateType === 'day') {
            item[incomeKey] = null;
          }
        }
      });
      initializeTableDataMap();
      currentPage.value = 1;
    });
  };
  // 留存数据
  const getRetentionData = async () => {
    const data = {
      appId: params.appIds.join(','),
      country: params.country.join(','),
      startDate: getDateRangeStartDate(params.date),
      endDate: getDateRangeEndDate(params.date),
      times: params.times,
      dateType: params.dateType,
      adFilter: params.adFilter,
      group: params.group,
      agencyFilter: params.agency,
    };
    await getPlacementRetention(data).then((res) => {
      retentionData.value = res;
      const now = new Date();
      const utcYear = now.getUTCFullYear();
      const utcMonth = now.getUTCMonth() + 1;
      const utcDate = now.getUTCDate();
      const compartDate = `${utcYear}-${utcMonth}-${utcDate}`;
      retentionData.value.forEach((item) => {
        for (let i = 0; i <= params.times; i++) {
          const incomeKey = `d${i}`;
          const incomeDate = new Date(item.date);
          // 计算日期差异
          const timeDiff = getDaysDiff(incomeDate, compartDate);
          // 超出置为null
          if (i > timeDiff && params.dateType === 'day') {
            item[incomeKey] = null;
          }
        }
      });
      initializeRetentionDataMap();
    });
  };
  const getViewList = async () => {
    await getAnalyticsViewList({ objectType: 11 }).then((res) => {
      rowGroupList.value = res;
    });
  };
  const init = async () => {
    loading.value = true;
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, { ...savedConfig });
    }
    await getMediaList();
    // 根据 adFilter 处理 selected 的回显值
    if (params.adFilter && params.adFilter.length > 0) {
      // 如果 adFilter 有值，则根据 adFilter 设置 selected
      const allSelected = params.adFilter.some((item) => item.mediaSource === 'all');
      if (allSelected) {
        // 如果包含 'all'，则 selected 设置为 ['all']
        selected.value = ['all'];
      } else {
        // 否则根据 adFilter 的内容构建 selected 数组
        selected.value = params.adFilter.map((item) => {
          const parts = [];
          if (item.mediaSource) parts.push(item.mediaSource);
          if (item.campaign) parts.push(item.campaign);
          if (item.adset) parts.push(item.adset);
          if (item.ad) parts.push(item.ad);
          return parts.join(SEPARATOR);
        });
        validateSelected();
      }
    } else {
      // 如果 adFilter 没有值，则设置默认值
      params.adFilter = [{ media_source: 'all' }];
      selected.value = ['all'];
    }
    try {
      await Promise.all([getRetentionData(), getRoiData(), getViewList()]);
      // 初始化 lastParams 的值
      lastParams.value = {
        appIds: [...params.appIds],
        country: [...params.country],
        teamCodes: [...params.teamCodes],
      };
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };
  onMounted(() => {
    init();
  });
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id-list') {
      params.appIds = value;
      // init()
    }
  });
  // 处理表格颜色深浅
  const getBackgroundColor = (rowIndex, value) => {
    const { spend } = tableData.value[rowIndex];
    // 如果没有值，返回白色背景
    if (value === null || value === undefined || value === '' || !spend) {
      return { background: 'rgb(255, 255, 255)', color: '#000' }; // 白色背景，黑色文本
    }
    const numValue = Number(value);
    if (Number.isNaN(numValue)) {
      return { background: 'rgb(255, 255, 255)', color: '#000' }; // 白色背景，黑色文本
    }
    const rate = spend ? (numValue / spend) * 100 : 0;
    // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
    const alpha = Math.min(Math.max(rate / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
    const backgroundColor = `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
    // 控制文本颜色
    const textColor = rate > 50 ? '#fff' : '#000';
    return { background: backgroundColor, color: textColor };
  };
  // 处理投放列颜色
  const getRoiColor = (record) => {
    const alpha = Math.min(Math.max(record.roas, 0), 1);
    const backgroundColor = record.roi ? `rgba(44,237,125, ${alpha})` : `rgba(241,92,128, ${alpha})`;
    const textColor = record.roas > 1 ? '#000' : '#000';
    return { background: backgroundColor, color: textColor };
  };
  // 查询
  const tableLoading = ref(false);
  const search = async () => {
    try {
      tableLoading.value = true;
      tableData.value = [];
      await Promise.all([getRetentionData(), getRoiData()]);
    } catch (error) {
      console.error('adeffect查询数据获取失败:', error);
    } finally {
      tableLoading.value = false;
    }
  };
  const handleMultipleChange = async () => {
    // 检查复选参数是否有变化
    const hasParamsChanged = !isEqual({ appIds: params.appIds, country: params.country, teamCodes: params.teamCodes }, { appIds: lastParams.value.appIds, country: lastParams.value.country, teamCodes: lastParams.value.teamCodes });

    if (hasParamsChanged) {
      // 更新上一次参数值
      lastParams.value = {
        appIds: [...params.appIds],
        country: [...params.country],
        teamCodes: [...params.teamCodes],
      };
      tableLoading.value = true;
      await getMediaList();
      search();
    }
  };
  const appChange = async (v) => {
    await handleMultipleChange();
  };
  const countryChange = async (v) => {
    params.country = v;
    await handleMultipleChange();
  };
  const teamChange = async (v) => {
    params.teamCodes = v;
    await handleMultipleChange();
  };
  const datePick = async (date: any) => {
    params.date = date;
    tableLoading.value = true;
    await getMediaList();
    search();
  };

  const showDrop = ref(true);
  const popChange = (v) => {
    showDrop.value = !v;
  };

  const adGroupChange = (v) => {
    search();
  };
  const rowRefs = ref();
  const editRow = () => {
    rowRefs.value.openModal(rowShowCode.value);
  };
  const getRowSelectDetail = async () => {
    await getAnalyticsViewDetail(rowShowCode.value).then((res) => {
      const rowList = [
        {
          code: 'predict',
          name: '回本周期',
        },
        {
          code: 'maEcpm',
          name: '变现ECPM',
        },
        {
          code: 'apEcpm',
          name: '买量ECPM',
        },
        {
          code: 'cumulativeProfit',
          name: '累计利润',
        },
        {
          code: 'cumulativeRevenue',
          name: '累计收入',
        },
        {
          code: 'cumulativeSpend',
          name: '累计投放',
        },
        {
          code: 'cumulativeInstalls',
          name: '累计安装量',
        },
        {
          code: 'runningDays',
          name: '运行天数',
        },
      ];
      selectRowList.value = res?.indicator?.map((code) => rowList.find((row) => row.code === code));
    });
  };
  const rowChange = async () => {
    if (rowShowCode.value) {
      getRowSelectDetail();
    } else {
      selectRowList.value = [];
    }
  };
  const updateRow = (v) => {
    if (v) {
      rowShowCode.value = v;
    }
    getViewList();
    getRowSelectDetail();
  };
  const filterChange = (v) => {
    // params.adFilter = []
    // if (v.length) {
    //   const adString = v.join(',')
    //   handleParams.isOrganic = ['Organic', 'all'].some(item => selected.value.includes(item));
    //   if (adString === 'all') {
    //     params.adFilter = [{media_source: "all"}]
    //   } else {
    //     params.adFilter = v.map(item => {
    //       const parts = item.split('-');
    //       const mediaSource = parts[0] || null;
    //       const campaign = parts[1] || null;
    //       const adset = parts[2] || null;
    //       const ad = parts[3] || null;
    //       const result = {};
    //       if (mediaSource) result.media_source = mediaSource;
    //       if (campaign) result.campaign = campaign;
    //       if (adset) result.adset = adset;
    //       if (ad) result.ad = ad;
    //       return result
    //     });
    //   }
    //   search()
    // }
  };
  watch(
    () => selected.value,
    (newValue) => {
      params.adFilter = [];
      handleParams.isOrganic = ['Organic', 'all'].some((item) => selected.value.includes(item));
      if (newValue.length) {
        const adString = newValue.join(',');
        if (adString === 'all') {
          params.adFilter = [{ media_source: 'all' }];
        } else {
          params.adFilter = newValue.map((item) => {
            const parts = item.split(SEPARATOR);
            const mediaSource = parts[0] || null;
            const campaign = parts[1] || null;
            const adset = parts[2] || null;
            const ad = parts[3] || null;
            const result = {};
            if (mediaSource) result.media_source = mediaSource;
            if (campaign) result.campaign = campaign;
            if (adset) result.adset = adset;
            if (ad) result.ad = ad;
            return result;
          });
        }
        search();
      }
    },
    { deep: true }
  );
  const organicChange = () => {
    if (handleParams.isOrganic && !['Organic', 'all'].some((item) => selected.value.includes(item))) {
      selected.value.push('Organic');
      const list = treeData.value[0].children.map((item) => item.value);
      if (selected.value.length === list.length) {
        selected.value = ['all'];
      }
    }
    if (!handleParams.isOrganic && selected.value.includes('Organic')) {
      selected.value = selected.value.filter((item) => item !== 'Organic');
    }
    if (!handleParams.isOrganic && selected.value.includes('all')) {
      const list = treeData.value[0].children.map((item) => item.value).filter((item) => item !== 'Organic');
      selected.value = list;
    }
  };
  const ecpmRefs = ref();
  const openModal = (record) => {
    const nameMap = {
      1: record.channel,
      2: record.campaign,
      3: record.adset,
      4: record.ad,
    };
    const name = nameMap[params.group];
    ecpmRefs.value.openModal(name);
  };
  const predictRefs = ref();
  const openPreModal = (record) => {
    predictRefs.value.openModal(record);
  };
  const fixedIndex = computed(() => {
    const selectIndex = selectRowList.value.length - 1;
    const fixIndex = columns.value.filter((item) => item.fixed).length;
    return selectIndex + fixIndex - 1;
  });
  // 导出
  const exportXlsx = () => {
    // 获取表头
    const headers = columns.value.map((item) => item.title);
    const columnsList = [headers];
    tableData.value.forEach((item, tableIndex) => {
      const row = headers.map((header, headerIndex) => {
        const dataIndex = columns.value.find((col) => col.title === header)?.dataIndex;
        const value = dataIndex ? item[dataIndex] : '';
        if (dataIndex && headerIndex > fixedIndex.value && handleParams.valueType === 'percent') {
          const spend = item.spend;
          const newValue = item.new;
          const match = dataIndex.match(/\d+/);
          const retentionIndex = match ? parseInt(match[0], 10) : null;

          // 获取留存数据，需要根据日期匹配而不是索引匹配
          let num = null;
          if (retentionIndex != null && retentionData.value) {
            // 根据日期匹配留存数据
            const retentionItem = retentionData.value.find((retItem) => retItem.date === item.date);
            if (retentionItem) {
              num = retentionItem[`d${retentionIndex}`];
            }
          }

          if (value != null && spend && spend > 0) {
            if (num != null && handleParams.meanwhile && newValue && newValue > 0) {
              return `${((value / spend) * 100).toFixed(2)}%,${((num / newValue) * 100).toFixed(2)}%`;
            }
            if (num != null && handleParams.meanwhile && (!newValue || newValue === 0)) {
              return `${((value / spend) * 100).toFixed(2)}%,--`;
            }
            return `${((value / spend) * 100).toFixed(2)}%`;
          }
          if (value === null) {
            if (num != null && handleParams.meanwhile && newValue && newValue > 0) {
              return `--,${((num / newValue) * 100).toFixed(2)}%`;
            }
            if (!handleParams.meanwhile) {
              return '--';
            }
            return `--,--`;
          }
          // 当spend为0或空时的处理
          if ((!spend || spend === 0) && num != null && handleParams.meanwhile && newValue && newValue > 0) {
            return `--,${((num / newValue) * 100).toFixed(2)}%`;
          }
          return '--';
        }
        if (dataIndex && headerIndex > fixedIndex.value && handleParams.valueType === 'default') {
          const match = dataIndex.match(/\d+/);
          const retentionIndex = match ? parseInt(match[0], 10) : null;

          // 获取留存数据，需要根据日期匹配而不是索引匹配
          let num = null;
          if (retentionIndex != null && retentionData.value) {
            // 根据日期匹配留存数据
            const retentionItem = retentionData.value.find((retItem) => retItem.date === item.date);
            if (retentionItem) {
              num = retentionItem[`d${retentionIndex}`];
            }
          }

          if (value != null && handleParams.meanwhile) {
            if (num != null) {
              return `${value || '0.00'},${num}`;
            }
            return `${value || '0.00'},--`;
          }
          if (value === null && handleParams.meanwhile) {
            if (num != null) {
              return `--,${num}`;
            }
            return `--,--`;
          }
          if (!handleParams.meanwhile) {
            if (value != null) {
              return value || '0.00';
            }
            return '--';
          }
          return '--';
        }
        return value || '--';
      });
      columnsList.push(row);
    });

    const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
    const newWorkbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
    XLSX.writeFile(newWorkbook, `广告成效_${getDateRangeStartDate(params.date)}-${getDateRangeEndDate(params.date)}.xlsx`);
  };
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .spin {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .spin-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .spin-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--tant-slate-slate-50);
      }
    }
  }
  .card-content {
    width: 100%;
    height: 100%;
    background-color: var(--tant-bg-white-color-bg1-1);

    .handle-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .handle-left {
        // display: flex;
      }

      .handle-right {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  :deep(.arco-empty) {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .operation-icon {
    padding: 4px;
    display: inline-block;
    margin-left: 8px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
    }

    .icon {
      display: flex;
      align-items: center;
      line-height: normal;
      font-size: 16px;
    }
  }

  :deep(tbody .arco-table-cell) {
    padding: 0;
  }

  .cell-class {
    padding: 5px 16px;
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .filter-select-content {
    max-height: 400px;
    min-width: 240px;
    max-width: 800px;
    overflow: auto;
    background-color: #475466;
    color: #fff;
    padding: 12px;
    // border: 1px solid var(--color-fill-3);
    border-radius: var(--border-radius-medium);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }

  :global(.treeClass .arco-tree-select-tree-wrapper) {
    max-height: 500px !important;
  }

  .href-name {
    text-decoration: underline;
    //   color: var(--tant-status-info-color-info-hover);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: auto;
    cursor: pointer;

    &:hover {
      color: #8d0088;
    }
  }

  .text-tip {
    font-size: 12px;
    color: #c7c5c5;
    line-height: 1;
  }

  .edit-select {
    width: 100%;
    position: relative;

    .edit {
      position: absolute;
      right: 30px;
      top: 8px;
      opacity: 0;
      cursor: pointer;
    }

    &:hover {
      .edit {
        opacity: 1;
      }
    }
  }
</style>
