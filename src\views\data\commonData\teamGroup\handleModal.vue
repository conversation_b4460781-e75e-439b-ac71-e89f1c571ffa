<template>
  <a-modal v-model:visible="modalVisible" :width="520" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
    <a-spin :loading="loading" style="width: 100%; height: 100%">
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%">
        <a-form-item field="name" label="显示名">
          <a-input v-model="form.name" />
        </a-form-item>
        <a-form-item field="appIds" label="应用">
          <a-select v-model:model-value="form.appIds" :max-tag-count="3" multiple :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" :filter-option="false" :show-extra-options="false" placeholder="搜索添加应用" @search="handleSearch">
            <a-option v-for="item in appIdsList" :key="item.code" :value="item.code"> {{ item.code }}-{{ item.name }} </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="description" label="描述">
          <a-textarea v-model="form.description" placeholder="请输入" style="height: 100px" />
        </a-form-item>
      </a-form>
      <div class="footer">
        <a-button style="margin-right: 10px" class="cancel" @click="closeModal">取消</a-button>
        <a-button type="primary" @click="saveData"> 保存 </a-button>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from 'vue';
import {Message} from '@arco-design/web-vue';
import {addOpGroupList, getAppPageList, getGroupDetail} from '@/api/marketing/api';

const loading = ref(false);
  const modalVisible = ref(false);
  const modalTitle = ref('');
  const form = reactive({
    code: '',
    name: '',
    appIds: [],
    description: '',
  });
  const pageParams = reactive({
    current: 1,
    pageSize: 10,
    text: '',
    groupStatus: 'no'
  });
  const rules = {
    name: [
      {
        required: true,
        message: '请填写显示名',
      },
    ],
    // appIds: [
    //     {
    //         required: true,
    //         message:'请选择应用',
    //     }
    // ],
  };
  const formRef = ref();
  const appPageList = ref<any>([]);
  const appSelectList = ref<any>([]);
  const emits = defineEmits(['updateData']);
  const appIdsList = computed(() => {
    const combinedList = appSelectList.value.concat(appPageList.value);
    const uniqueList = Array.from(new Map(combinedList.map((item) => [item.code, item])).values()) as any;
    return uniqueList;
  });
  const appLoading = ref(false);
  const getPageList = async () => {
    appLoading.value = true;
    appPageList.value = [];
    await getAppPageList(pageParams).then((res) => {
      appPageList.value = res.items || [];
    });
    appLoading.value = false;
  };
  const openModal = async (obj?: any) => {
    formRef.value.resetFields();
    formRef.value.clearValidate();
    pageParams.text = '';
    pageParams.current = 1;
    pageParams.pageSize = 10;
    // await getPageList()
    appPageList.value = [];
    appSelectList.value = [];
    modalVisible.value = true;
    if (obj) {
      loading.value = true;
      const { code, name, description } = obj;
      form.code = code;
      form.name = name;
      form.description = description;
      modalTitle.value = '编辑项目分组';
      await getGroupDetail(code).then(res => {
        appSelectList.value = res.appIds || []
        form.appIds = res.appIds.map(item => item.code)
    })
    loading.value = false
      loading.value = false;
    } else {
      form.code = '';
      modalTitle.value = '创建项目分组';
    }
  };
  const handleSearch = async (v) => {
    pageParams.text = v;
    if (pageParams.text) {
      await getPageList();
    } else {
      appPageList.value = [];
    }
  };
  const closeModal = () => {
    modalVisible.value = false;
  };
  const saveData = () => {
    formRef.value.validate(async (valid: any) => {
      if (!valid) {
        //
        try {
          await addOpGroupList(form);
          if (form.code) {
            Message.success('保存成功');
          } else {
            Message.success('创建成功');
          }
          modalVisible.value = false;
          emits('updateData');
        } catch (error) {
          console.error('创建失败:', error);
        }
      }
    });
  };

  defineExpose({
    openModal,
  });
</script>

<style scoped lang="less">
  .footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button {
      border-radius: 4px;
    }
  }
  .cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
      background-color: var(--color-secondary);
    }
  }
</style>
