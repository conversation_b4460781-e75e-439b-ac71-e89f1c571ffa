<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          上级菜单：
          <a-tree-select
              v-model:model-value="searchParentCode"
              allow-clear
              allow-search
              :filter-tree-node="(searchValue, nodeData) =>  nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1"
              dropdown-class-name="menu-tree-select-wrapper"
              :data="treeData"
              placeholder="请选择上级菜单"
              :style="{width:'240px'}"
          />
        </div>
        <div class="filter-item">
          路由编码：
          <a-input
              v-model:model-value="searchCode"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的菜单编码"/>
        </div>
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchMenu"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="searchMenu">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="add">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #optional="{ record }">
          <a-button type="text" @click="editMenu(record)">编辑</a-button>
          <a-popconfirm :content="`确认删除“${record.name}”?`" @ok="deleteMenu(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :visible="editModalShow"
        title-align="start"
        :title="formData.code?'编辑菜单':'新增菜单'"
        :ok-text="formData.code?'更新':'保存'"
        @cancel="editMenuCancel"
        @ok="editMenuConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical">
        <a-form-item field="name" :rules="[{required:true,message:'菜单名称不能为空'}]" label="菜单名称">
          <a-input v-model="formData.name" placeholder="请输入菜单名称"/>
        </a-form-item>
        <a-form-item :required="formData.routeName" field="routeName" :rules="[{required:true,message:'路由名称不能为空'}]" label="路由名称">
          <a-input v-model="formData.routeName"  placeholder="请输入路由名称"/>
        </a-form-item>
        <a-form-item field="path" label="访问路径" :rules="[{required:true,message:'访问路径不能为空'}]" >
          <a-input v-model="formData.path" placeholder="请输入访问路径"/>
        </a-form-item>
        <a-form-item field="sort" :rules="[{required:true,message:'菜单顺序不能为空'}]" label="菜单顺序">
          <a-input-number v-model="formData.sort" :precision="0" placeholder="请输入菜单顺序"/>
        </a-form-item>
        <a-form-item field="component" label="组件文件">
          <a-input v-model="formData.component" placeholder="请输入组件文件名"/>
        </a-form-item>
        <a-form-item field="icon" label="菜单图标">
          <a-input v-model="formData.icon" placeholder="请输入菜单图标地址"/>
        </a-form-item>
        <a-form-item field="parentCode" label="上级菜单">
          <a-tree-select
              v-model:model-value="formData.parentCode"
              allow-clear
              allow-search
              :filter-tree-node="(searchValue, nodeData) =>  nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1"
              dropdown-class-name="menu-tree-select-wrapper"
              placeholder="请选择上级菜单"
              :data="treeData"
          />
        </a-form-item>
        <a-form-item style="margin-bottom: 0">
          <a-form-item field="hasSideMenu" label="侧边菜单">
            <a-switch v-model="formData.hasSideMenu"/>
          </a-form-item>
          <a-form-item field="hideInMenu" label="显示状态">
            <a-switch v-model="formData.hideInMenu" :checked-value="0" :unchecked-value="1"/>
          </a-form-item>
          <a-form-item field="requiresAuth" label="白名单">
            <a-switch v-model="formData.requiresAuth" :checked-value="0" :unchecked-value="1"/>
          </a-form-item>
        </a-form-item>
        <a-form-item field="note" label="备注">
          <a-textarea v-model="formData.note" placeholder="请输入菜单备注"/>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getSystemMenu, removeSystemMenu, saveSystemMenu} from "@/api/setting/api";
import {getMenuList} from "@/api/authorize/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {useRoute} from "vue-router";

const route = useRoute();
const data = ref<any>([]);
const fullData = ref<any>([]);
const treeData = ref<any>([]);
const loading = ref<boolean>(true);
const searchCode = ref<string>();
const searchName = ref<string>();
const searchParentCode = ref<string>();
const formData = ref<any>({});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const columns = ref<any>([
  {
    title: '菜单名称',
    dataIndex: 'name',
    fixed: 'left',
    width: 200,
  },
  {
    title: '路由名称',
    dataIndex: 'routeName',
    fixed: 'left',
    width: 260
  },
  {
    title: '上级菜单',
    dataIndex: 'parentCode',
    width: 200,
    render: (value) => {
      const {record} = value;
      return fullData.value.filter((item: any) => item.code == record.parentCode)?.[0]?.name
    }
  },
  {
    title: '菜单顺序',
    dataIndex: 'sort',
    align: 'center',
    width: 100,
  },
  {
    title: '菜单图标',
    dataIndex: 'icon',
    width: 180,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '侧边菜单',
    dataIndex: 'hasSideMenu',
    width: 100,
    render: (value) => {
      const {record} = value;
      return record.hasSideMenu ? '存在' : '--'
    }
  },
  {
    title: '显示状态',
    dataIndex: 'hideInMenu',
    width: 100,
    render: (value) => {
      const {record} = value;
      return record.hideInMenu ? '隐藏' : '显示'
    }
  },
  {
    title: '白名单',
    dataIndex: 'requiresAuth',
    width: 100,
    render: (value) => {
      const {record} = value;
      return record.requiresAuth ? '--' : '是'
    }
  },
  {
    title: '备注',
    dataIndex: 'note',
    ellipsis: true,
    tooltip: true,
    width: 100,
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 200
  },
]);

const refreshMenuTree = () => {
  getMenuList().then(res => {
    sessionStorage.setItem("menu-list", JSON.stringify(res));
    treeData.value = res?.map(item => {
      return {
        ...item,
        key: item.code,
        title: item.name,
        children: item.children.map(child => {
          return {
            ...child,
            key: child.code,
            title: child.name,
            children: undefined
          }
        })
      }
    }) || []
  })
}

onMounted(() => {
  loading.value = true
  getSystemMenu().then(res => {
    data.value = res
    fullData.value = res
  }).finally(() => {
    loading.value = false
  })
  refreshMenuTree()
})

const searchMenu = () => {
  const pageParams = {
    code: searchCode.value,
    name: searchName.value,
    parent: searchParentCode.value
  };
  loading.value = true
  getSystemMenu(pageParams).then(res => {
    data.value = res
  }).finally(() => {
    loading.value = false
  })
}

const add = () => { 
  formData.value.code = ''
  editModalShow.value = true;
}
const editMenu = (record: any) => {
  formData.value = {...record};
  editModalShow.value = true;
}

const formSubmit = (menuData) => {
  if (_.isEmpty(menuData)) {
    Message.warning("菜单数据不能为空！")
  }
  menuData.parentCode = menuData.parentCode || '';
  saveSystemMenu(menuData).then(res => {
    Message.info("菜单数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    searchMenu()
    refreshMenuTree()
  }).catch(e => {
    Message.error("菜单数据保存失败！", e)
  })
}

const editMenuConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editMenuCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}

const deleteMenu = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除菜单id不能为空！")
  }
  removeSystemMenu(code).then(res => {
    Message.info("菜单数据删除成功！")
    searchMenu()
    refreshMenuTree()
  }).catch(e => {
    Message.error("菜单数据删除失败！", e)
  })
}
</script>

<style scoped lang="less">
:global(.menu-tree-select-wrapper .arco-tree-select-tree-wrapper) {
  max-height: 500px !important;
}
</style>