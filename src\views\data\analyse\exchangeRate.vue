<script setup lang="ts">

//

import dayjs from "dayjs";
import {computed, ref} from "vue";
// 获取时间
const nowData = dayjs().format("YYYY-MM-DD ");
// 假设 hasPermission 是你从后端获取的权限状态
const hasPermission = ref(false);
// 根据权限判断按钮是否禁用
const isActive = computed(() => hasPermission.value);
</script>

<template>
  <div class="currency">
    <div class="title">
      <div class="title-left">
        <div class="title-text">
          汇率换算
        </div>
      </div>
      <div class="title-right">
        <div class="right-button">
          <a-tooltip  v-if="!isActive" content="暂无该操作的权限，请联系管理员">
          <a-button
                :class="isActive ? 'button' : 'disabled-button'" :disabled="!isActive">
              <icon-file size=20 style="padding-right: 5px"/>
              <span>
          对接文档
        </span>
            </a-button>
          </a-tooltip>
          <a-tooltip  v-if="!isActive" content="暂无该操作的权限，请联系管理员">
          <a-button
                :class="isActive ? 'button2' : 'disabled-button2'" :disabled="!isActive">
              <icon-settings size=20 style="padding-right: 5px"/>
              <span>
            配置数据源
          </span>
            </a-button>
          </a-tooltip>
        </div>

      </div>

    </div>
    <div class="content">
      <div class="exchangeRateConfig">
        <p class="subtitle">
          数据源
        </p>
        <div class="configGroup">
          <div class="configItem">
            <span class="tag">预置</span>
            <span class="configItemCouple">
              <span class="rightVal">系统预置</span>
            </span>
          </div>
          <div class="configItem">
            <p class="desc" data-status="0">
              <span role="img" class="iconLeft">
                <icon-check-circle-fill />
              </span>
              已接入
            </p>
            <p class="configItemCouple">
              <span class="leftVal">起始日期</span>
              <span class="rightVal">2017-01-01</span>
            </p>
          </div>
          <div class="configItem">
            <p class="configItemCouple" style="margin-top: auto">
              <span class="leftVal">最后更新日期</span>
              <span class="rightVal">{{ nowData }}</span>
            </p>
          </div>
        </div>
      </div>
      <div class="currencyConfig">
        <div class="left">货币配置</div>
      </div>
      <div class="empty">
        <img src="/src/assets/images/empty.png" style="width: 368px;" alt="">
        <p>当前项目没有配置多货币规则</p>
        <p class="guide">
          前往
          <a style="margin: 0 4px" href="https://docs.thinkingdata.cn/ta-manual/v4.3/user_guide/data/exchange/exchange.html" target="_blank">
            使用手册
          </a>
          学习如何让你的项目兼容多货币
        </p>
        <a-tooltip  v-if="!isActive" content="暂无该操作的权限，请联系管理员">
        <button :data-active="isActive" :disabled="!isActive" class="button-disable">
          <span>
            <span class="iconLeft">
              <icon-plus />
            </span>
            添加货币
          </span>
        </button>
        </a-tooltip>
      </div>
    </div>



  </div>
</template>

<style scoped lang="less">
@keyframes loadingCircle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
a {
  color: var(--tant-primary-color-primary-default);
}
.currency{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  height: 100%;
  min-height: 88vh;
  border-radius: 4px;
}
.title {
  display: flex;
  flex: 0 0 auto;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;

  .title-left {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header3-medium);

    .title-text {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header3-medium);
    }
  }

  .title-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .right-button {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      border-radius: 3px;

      .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        color: var(--tant-primary-color-primary-default);
      }

      .disabled-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        color: var(--tant-disabled-color-disabled-text);
        background-color: transparent;
      }

      .button2 {
        background-color: var(--tant-bg-white-color-bg1-1);
        color: var(--tant-secondary-color-secondary-default);
        border: none;
      }

      .disabled-button2 {
        background-color: var(--tant-bg-white-color-bg1-1);
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.content {
  display: flex;
  flex: 1 1;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  width: 100%;
  padding: 24px;
  overflow: hidden;
  background: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;

  .exchangeRateConfig {
    flex: 0 0 auto;
    width: 100%;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--tant-border-color-border1-1);

    .subtitle {
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .configGroup {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      margin-bottom: 16px;

      .configItem {
        display: flex;
        flex: 1 1;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        height: 48px;

        .tag {
          height: 18px;
          padding: 0 4px;
          color: var(--tant-secondary-color-secondary-default);
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          text-align: center;
          background: var(--tant-secondary-color-secondary-fill);
          border-radius: 2px;
        }

        .configItemCouple {
          color: var(--tant-text-gray-color-text1-3);

          .leftVal {
            margin-right: 8px;
          }

          .rightVal {
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 500;
            font-size: 14px;
          }
        }

        .desc {
          color: var(--tant-status-success-color-success-default);

          .iconLeft {

            margin-right: 4px;
            animation: loadingCircle 1s linear infinite;
          }
        }
      }
    }
  }
  .currencyConfig{
    display: flex;
    flex: 0 0 auto;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 16px;
    .left{
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }
  }
  .empty{
    display: flex;
    flex: 1 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    .guide{
      margin-bottom: 24px;
      color: var(--tant-text-gray-color-text1-3);
      font-size: 14px;
      line-height: 22px;
    }
    .button-disable{
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      padding: 5px 16px;
      color: var(--tant-white-white-100);
      font: var(--tant-body-font-body-regular);
      text-transform: capitalize;
      border-radius: var(--tant-border-radius-medium);
      box-shadow: unset;
      border: none;
      cursor: not-allowed;
      background-color: var(--tant-primary-color-primary-disable);
      border-color: var(--tant-primary-color-primary-disable);
    }
  }
}

</style>