<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <select-app/>
                </div>
                <div class="filter-item">
                    <a-select
                        :style="{width: '320px'}"
                        :loading="bucketOptionsLoading"
                        v-model:model-value="bucket"
                        :options="bucketOptions"
                        placeholder="请选择存储桶"
                        :max-tag-count="1"
                        allow-search
                        multiple
                        @change="bucketChange"
                    />
                </div>
                <div class="filter-item">
                    <a-select
                        :style="{width: '160px'}"
                        :loading="versionOptionsLoading"
                        v-model:model-value="version"
                        :options="versionOptions"
                        placeholder="请选择应用版本"
                        :max-tag-count="1"
                        allow-search
                        multiple
                        @change="versionChange"
                    />
                </div>
                <div class="filter-item" v-if="classnameOptions.length > 0">
                    <a-select
                        :style="{width: '160px'}"
                        :loading="classnameOptionsLoading"
                        v-model:model-value="classname"
                        :options="classnameOptions"
                        placeholder="请选择子类别"
                        :max-tag-count="1"
                        allow-search
                        multiple
                        @change="classnameChange"
                    />
                </div>
            </div>
        </div>
        <div class="page-body">
            <div class="cross-content">
                <a-table :loading="loading" :columns="columns" :data="resourceList" :load-more="loadMore" :show-empty-tree="true" :pagination="false">
                    <template #version="{ record }">
                        {{ record.isLeaf ? '' : record.version }}
                    </template>
                    <template #bucketId="{ record }">
                        {{ record.isLeaf ? '' : record.bucketId }}
                    </template>
                    <template #classname="{ record }">
                        {{ record.isLeaf ? '' : record.classname }}
                    </template>
                    <template #resourceVersion="{ record }">
                        <span class="link" :class="{'font-weight-600': !record.isLeaf}" @click="viewDetail(record)">{{ record.resourceVersion }}</span>
                    </template>
                    <template #operation="{record}">
                        <a-space v-if="!record.isLeaf">
                            <a-popconfirm v-if="record.resourceVersion" :content="`确认刷新[${record.resourceVersion}版本资源吗？`" type="warning" @ok="refreshResource(record)">
                                <a-tooltip content="刷新缓存">
                                    <icon-refresh  class="btn"/>
                                </a-tooltip>
                            </a-popconfirm>
                        </a-space>
                        <a-space v-else>
                            <a-spin v-if="record.loadingSet" :size="14"/>
                            <a-popconfirm v-else :content="`确认设置[${record.resourceVersion}]为默认资源吗？`" type="warning" @ok="setDefault(record)">
                                <a-tooltip content="设置为默认资源版本">
                                    <icon-settings  class="btn"/>
                                </a-tooltip>
                            </a-popconfirm>
                            <a-spin v-if="record.loadingRefresh" :size="14"/>
                            <a-popconfirm v-else :content="`确认刷新[${record.resourceVersion}版本资源吗？`" type="warning" @ok="refreshResource(record)">
                                <a-tooltip  content="刷新缓存">
                                    <icon-refresh class="btn"/>
                                </a-tooltip>
                            </a-popconfirm>
                            <a-spin v-if="record.loadingDel" :size="14"/>
                            <a-popconfirm v-else :content="`确定删除条件配置[${record.resourceVersion}]版本资源吗？`" type="warning" @ok="deleteResource(record)">
                                <a-tooltip content="删除资源">
                                    <icon-delete  class="btn"/>
                                </a-tooltip>
                            </a-popconfirm>
                        </a-space>
                    </template>
                </a-table>
                <div class="pagination">
                    <a-pagination :total="total" show-total @change="pageChange"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import {reactive, ref} from "vue";
import {
    getFileResourceList,
    getFileResourceSubList,
    getFileResourceAppBucketList,
    getFileResourceAppVersionList,
    getFileResourceAppClassnamesList,
    refreshFileResource,
    setFileResourceDefault, deleteFileResource
} from "@/api/marketing/api";
import selectApp from "@/components/selected-game-app/index.vue"
import {useRoute} from "vue-router";
import {Message} from '@arco-design/web-vue';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

const route = useRoute();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const appId = ref(useSessionStorage('app-id', '')?.value)
const bucket = ref([])
const version = ref([])
const classname = ref([])
const bucketOptionsLoading = ref(false)
const bucketOptions = ref([])
const versionOptionsLoading = ref(false)
const versionOptions = ref([])
const classnameOptionsLoading = ref(false)
const classnameOptions = ref([])
const loading = ref(false)
const total = ref(0)
const current = ref(1)
const pageSize = ref(10)
const resourceList = ref([])


const scrollbar = ref(true)
const scroll = {x: 1000,y: 'calc(100vh - 300px)'}
const columns = [
    {
        title: '应用版本',
        slotName: 'version',
        ellipsis: true,
        tooltip: true,
        minWidth: 80,
    },
    {
        title: '存储桶',
        slotName: 'bucketId',
        ellipsis: true,
        tooltip: true,
        minWidth: 200,
    },
    {
        title: '子类名称',
        slotName: 'classname',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
    },
    {
        title: '资源版本',
        slotName: 'resourceVersion',
        minWidth: 80,
    },
    {
        title: '资源目录地址',
        dataIndex: 'resourceUrl',
        ellipsis: true,
        tooltip: true,
        minWidth: 200,
    },
    {
        title: '操作',
        slotName: 'operation',
        minWidth: 150,
    }
]

const modalVisible = ref(false)

const openModal = async () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}

localStorageEventBus.on((name, value) => {
    if (name === "app-id") {
        appId.value = value
        init()
    }
})

const initBucketOptions = async () => {
    bucketOptionsLoading.value = true
    getFileResourceAppBucketList({
        appId: appId.value
    }).then(res => {
        bucketOptions.value = res || []
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        bucketOptionsLoading.value = false
    })
}

const initVersionOptions = async () => {
    versionOptionsLoading.value = true
    getFileResourceAppVersionList({
        bucket: bucket.value,
        appId: appId.value
    }).then(res => {
        versionOptions.value = res || []
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        versionOptionsLoading.value = false
    })
}

const initClassnameOptions = async () => {
    classnameOptionsLoading.value = true
    getFileResourceAppClassnamesList({
        bucket: bucket.value,
        appId: appId.value,
        version: version.value
    }).then(res => {
        classnameOptions.value = res || []
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        classnameOptionsLoading.value = false
    })
}

const getList = async () => {
    let params = {
        bucket: bucket.value,
        appId: appId.value,
        version: version.value,
        classname: classname.value,
        current: current.value,
        pageSize: pageSize.value
    }
    loading.value = true
    getFileResourceList(params).then(res => {
        total.value = res.total
        resourceList.value = res.items
    }).catch(e => {
        Message.error(e)
    }).finally(() => {
        loading.value = false
    })
}

const pageChange = (currentPage: number) => {
    current.value = currentPage
    getList()
}

const bucketChange = async () => {
    current.value = 1
    await Promise.all([
        initVersionOptions(),
        initClassnameOptions(),
        getList()
    ])
}

const versionChange = async () => {
    current.value = 1
    await Promise.all([
        initClassnameOptions(),
        getList()
    ])
}

const classnameChange = async () => {
    current.value = 1
    getList()
}

const loadMore = (record, done) => {
    getFileResourceSubList({
        appId: appId.value,
        bucket: record.bucketId,
        version: record.version,
        classname: record.classname
    }).then(res => {
        let opts = res || [];
        opts.forEach(item => {
            item['isLeaf'] = true
            item['resourceUrl'] = `${record.cdnUrl}/${appId.value}/${item['resourcePathHash']}`
        })
        record.children = opts
        done()
        // done(opts)
    }).catch(e => {
        Message.error(e)
    })
}

const setDefault = (record) => {
    record.loadingSet = true
    setFileResourceDefault({
        bucketId: record.bucketId,
        appId: appId.value,
        version: record.version,
        resourceVersion: record.resourceVersion,
        classname: record.classname
    }).then(res => {
        if (res.status === 'ok') {
            resourceList.value.some(v => {
                if (v.bucketId === record.bucketId && v.version === record.version && v.classname === record.classname) {
                    v.resourceVersion = record.resourceVersion
                    v.resourceUrl = record.resourceUrl
                    return true
                }
            })
            Message.success(`设置成功`)
        } else {
            Message.error(`设置失败`)
        }
    }).catch(e => {
        Message.error(`设置失败`)
    }).finally(() => {
        record.loadingSet = false
    })
}

const refreshResource = (record) => {
    record.loadingRefresh = true
    refreshFileResource({
        bucket: record.bucketId,
        appId: appId.value,
        version: record.version,
        resourceVersion: record.resourceVersion,
        classname: record.classname
    }).then(res => {
        if (res === 'ok') {
            Message.success(`刷新成功`)
        } else {
            Message.error(`刷新失败`)
        }
    }).catch(e => {
        Message.error(`刷新失败`)
    }).finally(() => {
        record.loadingRefresh = false
    })
}

const deleteResource = (record) => {
    record.loadingDel = true
    deleteFileResource({
        bucket: record.bucketId,
        appId: appId.value,
        version: record.version,
        resourceVersion: record.resourceVersion,
        classname: record.classname
    }).then(res => {
        if (res === 'ok') {
            resourceList.value.some((v, k) => {
                if (v.bucketId === record.bucketId && v.version === record.version && v.classname === record.classname && v.resourceVersion === record.resourceVersion) {
                    v.resourceVersion = ''
                    v.resourceUrl = ''
                    v.children.some((_v, _k) => {
                        if (_v.key === record.key) {
                            resourceList.value[k].children.splice(_k, 1)
                            return true
                        }
                    })

                    console.log(resourceList.value)
                    return true
                }
            })
            Message.success(`删除成功`)
        } else {
            record.loadingDel = false
            Message.error(`删除失败`)
        }
    }).catch(e => {
        console.log(e)
        record.loadingDel = false
        Message.error(`删除失败`)
    })
}

const viewDetail = (record) => {
    router.push({
        name: ROUTE_NAME.FILE_RESOURCE_DETAIL,
        query: {
            bucket: record.bucketId,
            version: record.version,
            resourceVersion: record.resourceVersion,
            classname: record.classname
        }
    })
}

const init = async () => {
    bucket.value = []
    version.value = []
    classname.value = []
    await Promise.all([
        initBucketOptions(),
        initVersionOptions(),
        initClassnameOptions(),
        getList()
    ])
}
init()
</script>

<style scoped lang="less">
.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
}

.btn {
    cursor: pointer;
}

.link {
    color: rgb(var(--primary-6));
    cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
}
.font-weight-600{
    font-weight: 600;
}
</style>
