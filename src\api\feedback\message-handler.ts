import {MessageRecord} from '@/api/notify/api';

// 反馈消息类型枚举
export enum FeedbackMessageType {
  NEW_FEEDBACK = 'new_feedback',           // 新反馈通知处理人
  FEEDBACK_REPLIED = 'feedback_replied',   // 反馈被回复通知反馈人
  FEEDBACK_STATUS_CHANGED = 'feedback_status_changed' // 反馈状态变更通知相关人员
}

// 反馈消息扩展数据结构
export interface FeedbackMessageExtraData {
  feedbackId: number;        // 反馈ID
  feedbackTitle: string;     // 反馈标题
  feedbackType: string;      // 反馈类型
  feedbackCreator: string;   // 反馈创建人
  feedbackAssignee: string;  // 反馈处理人
  feedbackStatus: string;    // 反馈状态
  replyContent?: string;     // 回复内容（如果有）
  replyAuthor?: string;      // 回复人（如果有）
}

// 检查消息是否为反馈相关消息
export function isFeedbackMessage(message: MessageRecord): boolean {
  // 这里可以根据消息内容或特定标识来判断是否为反馈消息
  // 目前通过标题关键词来判断
  return message.title.includes('问题反馈') || message.title.includes('反馈');
}

// 处理反馈消息点击事件
export function handleFeedbackMessageClick(message: MessageRecord): void {
  // 从消息内容中提取反馈ID（这需要在发送消息时将反馈ID包含在消息中）
  // 这里我们模拟从消息内容中提取反馈ID的逻辑
  const feedbackId = extractFeedbackIdFromMessage(message);
  
  if (feedbackId) {
    // 触发自定义事件，通知反馈面板组件打开并定位到指定反馈
    const event = new CustomEvent('feedbackMessageClicked', {
      detail: {
        feedbackId: feedbackId,
        message: message
      }
    });
    window.dispatchEvent(event);
  } else {
    console.warn('无法从消息中提取反馈ID:', message);
  }
}

// 从消息内容中提取反馈ID（模拟实现）
function extractFeedbackIdFromMessage(message: MessageRecord): number | null {
  // 这里应该根据实际的消息数据结构来提取反馈ID
  // 可以通过消息内容中的特定格式或额外的数据字段来获取
  
  // 实际项目中应该通过更可靠的方式来传递反馈ID
  try {
    // 从消息内容中提取ID: 通过正则表达式匹配 "ID:数字" 格式
    const content = message.content;
    if (content.includes('ID:')) {
      const match = content.match(/ID:(\d+)/);
      if (match && match[1]) {
        return parseInt(match[1], 10);
      }
    }
    
    // 如果消息对象有额外的数据字段，也可以从那里获取
    // 注意：这需要消息对象支持额外字段
    
    // 如果没有找到，返回null
    return null;
  } catch (error) {
    console.error('提取反馈ID时出错:', error);
    return null;
  }
}

// 创建反馈消息的额外数据（用于在发送消息时附加到消息对象）
export function createFeedbackMessageExtraData(data: FeedbackMessageExtraData): any {
  return {
    feedbackId: data.feedbackId,
    feedbackTitle: data.feedbackTitle,
    feedbackType: data.feedbackType,
    feedbackCreator: data.feedbackCreator,
    feedbackAssignee: data.feedbackAssignee,
    feedbackStatus: data.feedbackStatus,
    replyContent: data.replyContent,
    replyAuthor: data.replyAuthor
  };
}