<script setup lang="ts">

import {onMounted, reactive, ref, watch} from "vue";
import _ from "lodash";
import dayjs from 'dayjs';
import {getAppInfo, getAppInit, getOpCategoryList, getOpDeveloperList, getOpGroupList, getOpOsList, getOpTagList, getOpTeamList, getPromotionInfo, getRetentionModel, getStoreList, postPromotionUpdateApp, postUpdateApp} from "@/api/marketing/api";
import {useRoute} from 'vue-router';
import selectApp from "@/components/selected-game-app/index.vue"
import {Message} from '@arco-design/web-vue';
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {sortSelectedFirst} from "@/utils/sortUtil";
import advertisingUnit from "./compontents/advertising-unit.vue";
import updateLog from "../updateLog/index.vue";
import areaModal from "./compontents/addModal.vue";
import platModal from "./compontents/platModal.vue";
import retentionModal from "./compontents/retentionModal.vue";


const route = useRoute()

const gameAppList = ref<any>([])
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const paneActiveKey = ref<string>('1')
const appBasic = ref()
const appLabel = ref()
const appCategory = ref()
const projectTeam = ref()
const opGroupList = ref()
const appDeveloper = ref()
const appPlatform = ref()
const retentionModel = ref()
const storeList = ref()
const appId = ref('')
// 表单ref
const expandFormRef = ref()
const basicFormRef = ref()
// 表单规则
const basicRules = {
  appId: [
    {
      required: true,
    }
  ],
  platform: [
    {
      required: true,
      message: '请选择应用商店'
    }
  ],
  platformLink: [
    {
      required: true,
      message: '请输入应用商店URL'
    }
  ],
  icon: [
    {
      required: false,
      message: '请输入应用ICON'
    }
  ],
  storeId: [
    {
      required: true,
      message: '请输入Appstoreid'
    }
  ],
  package: [
    {
      required: true,
      message: '请输入应用包名'
    }
  ],
  osType: [
    {
      required: true,
      message: '请选择应用平台'
    }
  ],
  name: [
    {
      required: true,
      message: '请输入应用名称'
    }
  ],
  status: [
    {
      required: true,
      message: '请选择应用上架状态'
    }
  ],
}

// 表单信息
const basicForm = reactive({
  appId: '',
  status: 1,
  package: '',
  osType: '',
  categoryCode: '',
  appDeveloperCode: '',
  storeId: '',
  retentionModelCode: '',
  name: '',
  onlineDate: '',
  tagCodes: [],
  appTeamCode: [],
  appGroupCode: '',
  keystoreHash: '',
  autoRegisterType: 0,
  platform: '',
  platformLink: '',
  icon: '',
  sdk: '',
  firstDataSource:'',
  eventDataSource:'', // 事件数据源

});
const expansionForm = reactive({
  summary: '',
  version: '',
  icon: '',
  banner: '',
  startId: '',
  cover: '',
  desc: '',
  url: '',
  native: '',
  gifIcon: '',
  video: '',
  down: '',
  downSt: undefined,
  downPersentY: undefined,
  deliciousBanner: '',
  action: '',
})
const loading = ref(true)
// 获取基本信息
const getInfo = async () => {
  await getAppInfo(appId.value).then(res => {
    appBasic.value = _.cloneDeep(res)
    basicForm.appId = appBasic.value.appId
    basicForm.status = appBasic.value.status
    basicForm.osType = appBasic.value.osType
    basicForm.package = appBasic.value.package
    basicForm.categoryCode = appBasic.value.categoryCode
    basicForm.appDeveloperCode = appBasic.value.appDeveloperCode
    basicForm.storeId = appBasic.value.storeId
    basicForm.retentionModelCode = appBasic.value.retentionModelCode
    basicForm.name = appBasic.value.name
    basicForm.onlineDate = appBasic.value.onlineDate
    basicForm.tagCodes = appBasic.value.tagCodes || []
    basicForm.appTeamCode = appBasic.value.appTeamCode || []
    basicForm.appGroupCode = appBasic.value.appGroupCode || ''
    basicForm.keystoreHash = appBasic.value.keystoreHash
    basicForm.platform = appBasic.value.platform
    basicForm.platformLink = appBasic.value.platformLink
    basicForm.icon = appBasic.value.icon
    basicForm.firstDataSource = appBasic.value.firstDataSource
    basicForm.eventDataSource = appBasic.value.eventDataSource
  })
}
// 获取推广信息
const getProInfo = async () => {
  await getPromotionInfo(appId.value).then(res => {
    const obj = _.cloneDeep(res as any)
    expansionForm.summary = obj?.summary
    expansionForm.version = obj?.version
    expansionForm.icon = obj?.icon
    expansionForm.banner = obj?.banner
    expansionForm.startId = obj?.startId
    expansionForm.cover = obj?.cover
    expansionForm.desc = obj?.desc
    expansionForm.url = obj?.url
    expansionForm.desc = obj?.desc
    expansionForm.native = obj?.native
    expansionForm.gifIcon = obj?.gifIcon
    expansionForm.video = obj?.video
    expansionForm.down = obj?.down
    expansionForm.downSt = obj?.downSt
    expansionForm.downPersentY = obj?.downPersentY
    expansionForm.deliciousBanner = obj?.deliciousBanner
    expansionForm.action = obj?.action
    projectTeam.value = sortSelectedFirst(projectTeam.value, basicForm.appTeamCode);
    appLabel.value = sortSelectedFirst(appLabel.value, basicForm.tagCodes);
  })
}
const saveLoading = ref(false)
const saveConfig = () => {
  basicFormRef.value.validate(async (valid: any) => {
    saveLoading.value = true
    if (!valid) {
      try {
        await postUpdateApp(basicForm);
        await postPromotionUpdateApp(basicForm.appId, expansionForm);
        getInfo();
        Message.success('保存成功');
      } catch (error) {
        console.log(error);
      } finally {
        saveLoading.value = false;
      }
    } else {
      saveLoading.value = false;
      // Message.warning('请填写必要数据')
    }
  })
}


const paginationProps = reactive({
  defaultPageSize: 12,
  total: gameAppList.value.length,
  showTotal: true,
})
watch(() => gameAppList.value, (newVal) => {
  paginationProps.total = newVal.length
}, {deep: true})

const logRef = ref()
const adRef = ref()
const configRef = ref()

// 应用类别
const getCategoryList = () => {
  getOpCategoryList().then(res => {
    appCategory.value = _.cloneDeep(res)
  })
}
// 应用标签
const getTagsList = () => {
  getOpTagList().then(res => {
    appLabel.value = _.cloneDeep(res)
  })
}
// 项目团队
const getTeamList = () => {
  getOpTeamList().then(res => {
    projectTeam.value = _.cloneDeep(res)
  })
}
// 项目分组
const getGroupList = () => {
  getOpGroupList().then(res => {
    opGroupList.value = _.cloneDeep(res)
  })
}
// 开发商
const getDeveloperList = () => {
  getOpDeveloperList().then(res => {
    appDeveloper.value = _.cloneDeep(res)
  })
}
// RetentionModel
const getRetentionList = () => {
  getRetentionModel().then(res => {
    retentionModel.value = _.cloneDeep(res)
  })
}
// 应用平台
const getPlatformList = () => {
  getOpOsList().then(res => {
    appPlatform.value = _.cloneDeep(res)
  })
}
// 商店平台列表
const getStore = () => {
  getStoreList(basicForm.osType || 'android').then(res => {
    storeList.value = _.cloneDeep(res)
  })
}
const refresh = (id) => {
  loading.value = true
  appId.value = id
  getInfo().then(() => {
    getProInfo()
    getStore()
    logRef.value.getData()
    adRef.value.getData()
    // configRef.value.getInfo()
    loading.value = false
  })
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") {
    refresh(value)
  }
})

onMounted(async() => {
  await Promise.all([
    getCategoryList(),
    getTagsList(),
    getTeamList(),
    getGroupList(),
    getDeveloperList(),
    getRetentionList(),
    getPlatformList(),
    getStore()
  ])
  refresh(sessionStorage.getItem('app-id'))
})

const showBasicMore = ref(false)
const showExpandMore = ref(false)
const osTypeChange = (v) => {
  basicForm.platform = ''
  getStore()
}

// 新增modal
const addRef = ref()
const addData = (value: string) => {
  addRef.value.openModal(value)
}
const updateListData = async (v) => {
  const operationMap = {
    '新增项目团队': getTeamList,
    '新增应用类型': getCategoryList,
    '新增应用标签': getTagsList,
    '新增开发商': getDeveloperList
  };
  const addFunction = operationMap[v];
  try {
    await addFunction();
  } catch (error) {
    console.error('更新失败:', error);
  }
}
const platRef = ref()
const addPlat = () => {
  platRef.value.openModal()
}
const updatePlat = () => {
  getStore()
}

const retentionRef = ref()
const addRetention = () => {
  retentionRef.value.openModal(basicForm.retentionModelCode)
}
const updateRetention = () => {
  getRetentionList()
}

const initApp = () => {
  getAppInit().then(res => {
    Message.success('应用初始化成功');
  }).catch(e => {
    Message.error(e?.detail?.[0]?.msg || '应用初始化失败');
  })
}
</script>

<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <!-- <div class="filter-item">
          <a-popconfirm content="应用初始化会生成事件表及用户表，请确认！" ok-text="生成" @ok="initApp">
            <a-button type="primary">
              <icon-sync/>
              <span style="margin-left: 6px">初始化</span>
            </a-button>
          </a-popconfirm>
        </div> -->
      </div>
    </div>
    <div class="page-body">
      <a-spin :loading="loading" style="width: 100%;height: 100%;">
        <a-tabs v-model:active-key="paneActiveKey" default-active-key="1" style="width: 100%;height: 100%;">
          <!--         基本信息-->
          <a-tab-pane key="1" title="属性配置" class="pane-content">
            <a-form ref="basicFormRef" class="attr-form" :rules="basicRules" :model="basicForm">
              <div class="type-content">
                <div class="label">
                  基本信息
                </div>
                <div class="item-content">
                  <a-form-item field="appId" label="应用ID" validate-trigger="blur">
                    <a-input v-model="basicForm.appId" placeholder="请输入应用ID" disabled style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item field="osType" label="平台" validate-trigger="change">
                    <a-select v-model:model-value="basicForm.osType" placeholder="请选择应用平台" style="width: 420px;" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="osTypeChange">
                      <a-option v-for="item in appPlatform" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item field="status" label="选择上架状态" validate-trigger="change">
                    <a-select v-model:model-value="basicForm.status" placeholder="请选择应用上架状态" style="width: 420px;" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                      <a-option :value="1">已上架</a-option>
                      <a-option :value="0">未上架</a-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item v-if="basicForm.status" field="platform" label="选择应用商店" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model:model-value="basicForm.platform" placeholder="请选择选择应用商店" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in storeList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                      <a-tooltip content="新增">
                        <icon-plus-circle size="16" class="edit" @click="addPlat"/>
                      </a-tooltip>
                    </div>
                  </a-form-item>
                  <a-form-item v-if="basicForm.status" field="platformLink" label="应用商店URL" validate-trigger="blur">
                    <a-input v-model="basicForm.platformLink" placeholder="请输入应用商店URL" style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item field="name" label="应用名称" validate-trigger="blur">
                    <a-input v-model="basicForm.name" placeholder="请输入应用名称" style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item field="package" label="应用包名：" validate-trigger="blur">
                    <a-input v-model="basicForm.package" placeholder="请输入应用包名" style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item v-if="basicForm.osType === 'ios'" field="storeId" label="appStoreID" validate-trigger="blur">
                    <a-input v-model="basicForm.storeId" placeholder="请输入Appstoreid" style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item field="icon" label="应用ICON" validate-trigger="blur">
                    <a-input v-model="basicForm.icon" placeholder="请输入应用ICON-URL" style="width: 420px;"/>
                  </a-form-item>
                </div>
                <!-- <div class="expand-more" @click="() => showBasicMore = !showBasicMore">
                  展开更多
                  <icon-right v-if="!showBasicMore"/>
                  <icon-down v-else/>
                </div> -->
                <div class="item-content">
                  <a-form-item field="retentionModelCode" label="留存率模型" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.retentionModelCode" placeholder="请选择留存率模型" allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in retentionModel" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                      <a-tooltip content="编辑">
                        <icon-edit size="16" class="edit" @click="addRetention"/>
                      </a-tooltip>
                    </div>
                  </a-form-item>
                  <a-form-item field="onlineDate" label="上线日期" validate-trigger="blur">
                    <a-date-picker v-model="basicForm.onlineDate" placeholder="请选择上线日期" style="width: 420px;" :disabled-date="(current) => dayjs(current).isBefore(dayjs())"/>
                  </a-form-item>
                  <a-form-item field="keystoreHash" label="Keystorehash" validate-trigger="blur">
                    <a-input v-model="basicForm.keystoreHash" placeholder="请输入Keystorehash" style="width: 420px;"/>
                  </a-form-item>
                  <a-form-item field="autoRegisterType" label="事件自动注册" validate-trigger="blur">
                    <a-switch v-model="basicForm.autoRegisterType" :checked-value="1" :unchecked-value="0"/>
                  </a-form-item>
                </div>
              </div>
              <a-divider/>
              <div class="type-content">
                <div class="label">
                  默认数据源<br/>
                </div>
                <div class="item-content">
                  <a-form-item field="firstDataSource" label="聚合数据" validate-trigger="change">
                    <a-radio-group v-model:model-value="basicForm.firstDataSource">
                      <a-radio value="GA">GA</a-radio>
                      <a-radio value="AF">AF</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item field="eventDataSource" label="事件数据" validate-trigger="change">
                    <a-radio-group v-model:model-value="basicForm.eventDataSource">
                      <a-radio value="IVY">IVY</a-radio>
                      <a-radio value="AF">AF</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </div>
              </div>
              <a-divider/>
              <div class="type-content">
                <div class="label">
                  分组<br/>
                  <!-- <span class="desc">将应用划分到不同的预置分组中</span> -->
                </div>
                <div class="item-content">
                  <a-form-item field="appTeamCode" label="项目团队" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.appTeamCode" placeholder="请选择项目团队" multiple :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in projectTeam" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                    </div>
                  </a-form-item>
                  <a-form-item field="appGroupCode" label="项目分组" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.appGroupCode" placeholder="请选择项目分组" allow-clear allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in opGroupList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                    </div>
                  </a-form-item>
                  <a-form-item field="categoryCode" label="应用类型" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.categoryCode" placeholder="请选择应用类型" allow-clear allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in appCategory" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                      <!-- <a-tooltip content="新增">
                        <icon-plus-circle size="16" class="edit" @click="addData('新增应用类型')"/>
                      </a-tooltip> -->
                    </div>
                  </a-form-item>
                  <a-form-item field="tagCodes" label="应用标签" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.tagCodes" placeholder="请选择应用标签" multiple allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in appLabel" :key="item" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                      <!-- <a-tooltip content="新增">
                        <icon-plus-circle size="16" class="edit" @click="addData('新增应用标签')"/>
                      </a-tooltip> -->
                    </div>
                  </a-form-item>
                  <a-form-item field="appDeveloperCode" label="开发商" validate-trigger="change">
                    <div class="edit-select" style="width: 420px;">
                      <a-select v-model="basicForm.appDeveloperCode" placeholder="请选择开发商" allow-clear allow-search :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                        <a-option v-for="item in appDeveloper" :key="item.code" :value="item.code">{{ item.name }}</a-option>
                      </a-select>
                      <!-- <a-tooltip content="新增">
                        <icon-plus-circle size="16" class="edit" @click="addData('新增开发商')"/>
                      </a-tooltip> -->
                    </div>
                  </a-form-item>
                </div>
              </div>
              <a-divider/>
              <div class="type-content">
                <div class="label">
                <span style="cursor: pointer;" @click="() => showExpandMore = !showExpandMore">
                  推广
                  <icon-right v-if="!showExpandMore"/>
                  <icon-down v-else/>
                </span>
                  <br/>
                  <!-- <span class="desc">应用推广配置</span> -->
                </div>
                <div v-show="showExpandMore" class="item-content">
                  <a-form ref="expandFormRef" :rules="basicRules" :model="expansionForm">
                    <!-- 拓展信息 -->
                    <a-form-item field="summary" label="SUMMARY" validate-trigger="blur">
                      <a-input v-model="expansionForm.summary" placeholder="请输入summary" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="version" label="VERSION" validate-trigger="blur">
                      <a-input v-model="expansionForm.version" placeholder="请输入version" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="banner" label="BANNER" validate-trigger="blur">
                      <a-input v-model="expansionForm.banner" placeholder="请输入banner" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="startId" label="START ID" validate-trigger="blur">
                      <a-input v-model="expansionForm.startId" placeholder="请输入startId" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="cover" label="COVER" validate-trigger="blur">
                      <a-input v-model="expansionForm.cover" placeholder="请输cover" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="desc" label="DESC" validate-trigger="blur">
                      <a-input v-model="expansionForm.desc" placeholder="请输入desc" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="url" label="URL" validate-trigger="blur">
                      <a-input v-model="expansionForm.url" placeholder="请输入url" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="native" label="NATIVE" validate-trigger="blur">
                      <a-input v-model="expansionForm.native" placeholder="请输入native" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="gifIcon" label="GIFICON" validate-trigger="blur">
                      <a-input v-model="expansionForm.gifIcon" placeholder="请输入gifIcon" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="video" label="VIDEO" validate-trigger="blur">
                      <a-input v-model="expansionForm.video" placeholder="请输入video" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="down" label="DOWN" validate-trigger="blur">
                      <a-input v-model="expansionForm.down" placeholder="请输入down" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="downSt" label="DOWN_ST" validate-trigger="blur">
                      <a-input-number v-model="expansionForm.downSt" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="downPersentY" label="DOWN_PERSENTY" validate-trigger="blur">
                      <a-input-number v-model="expansionForm.downPersentY" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="deliciousBanner" label="DELICIOUS_BANNER" validate-trigger="blur">
                      <a-input v-model="expansionForm.deliciousBanner" placeholder="请输入deliciousBanner" style="width: 420px;"/>
                    </a-form-item>
                    <a-form-item field="action" label="ACTION" validate-trigger="blur">
                      <a-input v-model="expansionForm.action" placeholder="请输入action" style="width: 420px;"/>
                    </a-form-item>
                  </a-form>
                </div>
              </div>
              <!-- <a-divider/>
              <div class="type-content">
                <div class="label">
                  高级<br/>
                </div>
                <div class="item-content">
                  <a-form-item field="sdk" label="SDK数据授权密钥" validate-trigger="blur">
                    <a-input v-model="basicForm.sdk" placeholder="请输入SDK数据授权密钥" style="width: 420px;"/>
                  </a-form-item>
                </div>
              </div> -->
            </a-form>
            <div v-if="paneActiveKey==='1'" class="buttons">
              <a-button type="primary" :loading="saveLoading" style="border-radius: 5px" @click="saveConfig">保存配置</a-button>
            </div>
          </a-tab-pane>
          <!--       数据配置-->
<!--          <a-tab-pane key="2" title="数据配置" class="pane-content">-->
<!--            <sendDataConfigure ref="configRef"/>-->
<!--          </a-tab-pane>-->
          <!--       广告映射-->
          <a-tab-pane key="3" title="广告映射" class="pane-content">
            <advertisingUnit ref="adRef"/>
          </a-tab-pane>
          <!--       更新日志-->
          <a-tab-pane key="4" title="日志记录" class="pane-content">
            <updateLog ref="logRef"/>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </div>
    <areaModal ref="addRef" :show-desc="true" @update-data="updateListData"/>
    <platModal ref="platRef" @update-data="updatePlat"/>
    <retentionModal ref="retentionRef" :list="retentionModel" @update-data="updateRetention"/>
  </div>

</template>

<style scoped lang="less">
:deep(.arco-tabs-content) {
  padding-top: 0;
  height: calc(100% - 40px);
  overflow-y: auto;
}

:deep(.arco-tabs-content-list) {
  height: 100%
}

:deep(.arco-tabs-pane) {
  height: 100%;
  overflow-y: auto;
}

.pane-content {
  padding: 16px;
  height: 100%;
}

.attr-form {
  padding-bottom: 48px;
}

.buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 8px 16px 8px;
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
}

.type-content {
  .label {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-bottom: 12px;

    .desc {
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-3);
    }
  }

  .expand-more {
    color: var(--tant-text-gray-color-text1-3);
    font-size: 14px;
    cursor: pointer;
    width: 124px;
    margin-bottom: 12px;
  }
}

.edit-select {
  width: 100%;
  position: relative;

  .edit {
    position: absolute;
    right: 30px;
    top: 8px;
    opacity: 0;
    cursor: pointer;
  }

  &:hover {
    .edit {
      opacity: 1;
    }
  }
}
</style>
