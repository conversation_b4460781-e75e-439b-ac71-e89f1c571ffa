<script setup lang="ts">
import {computed, defineExpose, reactive, ref, watch} from 'vue';
import TableChartCustom from '@/views/dashboard/components/table-chart/custom/custom-table-item.vue';
import {ReportQueryResponse} from '@/api/type';
import {AnyObject} from '@/types/global';
import useChartOption from '@/hooks/chart-option';
import {cloneDeep} from 'lodash';

interface Props {
    /**
     * 报表分析数据
     */
    report: any;
    reportAnalysisData: ReportQueryResponse;
    reportDetail: any;
  }

  const props = defineProps<Props>();
  const emits = defineEmits(['changeParams', 'queryParamsChange', 'pageSizeChange']);

  const showTable = ref<boolean>(true);

  const showCharts = ref<boolean>(false);
  const selectValue = ref({ value: 'table-chart', label: '详细数据' });
  const typeValue = ref({ value: 'table-chart', label: '详细数据' });
  const showSetTable = ref<boolean>(false);
  const loading = ref<boolean>(true);
  const isChart = ref<boolean>(false);
  const isOnlyDefault = ref<boolean>(false);

  const xAxis = ref<string[]>([]);
  const ySeries = ref([]);
  const legendData = ref([]);
  const XshowLabel = ref(true);
  const columns = ref([]);
  const filterData = ref([]);
  const showTableHeader = ref<boolean>(false);

  function graphicFactory(side: AnyObject) {
    return {
      type: 'text',
      bottom: '8',
      ...side,
      style: {
        text: '',
        textAlign: 'center',
        fill: '#4E5969',
        fontSize: 12,
      },
    };
  }

  const graphicElements = ref([graphicFactory({ left: '2.6%' }), graphicFactory({ right: 0 })]);

  const { chartOption } = useChartOption(() => {
    return {
      grid: {
        left: '4.6%',
        right: '4.6%',
        top: '30',
        bottom: '50',
      },
      legend: {
        data: legendData.value,
        bottom: '0',
        type: 'scroll',
      },
      xAxis: {
        type: 'category',
        offset: 2,
        data: xAxis.value,
        show: XshowLabel.value,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: [
        {
          type: 'value',
        },
        {
          type: 'value',
          position: 'right',
        },
      ],
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        className: 'echarts-tooltip-diy',
      },
      graphic: {
        elements: graphicElements.value,
      },
      series: ySeries.value,
    };
  });

  const handleChange = (value: any, frash?: boolean) => {
    switch (value) {
      case 'default':
        showSetTable.value = true;
        showCharts.value = false;
        showTable.value = false;
        emits('changeParams', { chartType: 'default' });
        break;
      default:
        if (isChart.value) {
          showSetTable.value = false;
          showCharts.value = true;
          showTable.value = false;
        } else {
          showSetTable.value = false;
          showCharts.value = false;
          showTable.value = true;
        }
        emits('changeParams', { chartType: typeValue.value.value });
        break;
    }
  };

  const setType = (type: any) => {
    switch (type) {
      case 'default':
        showSetTable.value = true;
        showCharts.value = false;
        showTable.value = false;
        selectValue.value = { value: 'default', label: '详细数据' };
        break;
      default:
        if (isChart.value) {
          showSetTable.value = false;
          showCharts.value = true;
          showTable.value = false;
          selectValue.value = cloneDeep(typeValue.value);
        } else {
          showSetTable.value = false;
          showCharts.value = false;
          showTable.value = true;
          selectValue.value = cloneDeep(typeValue.value);
        }
        break;
    }
    loading.value = false;
  };

  const setChartOption = (data: any) => {
    if (!data) {
      isOnlyDefault.value = true;
      setType('default');
      return;
    }
    let option = JSON.parse(data);
    if (option[0] === 'table') {
      columns.value = option[1];
      filterData.value = option[2];
      showTableHeader.value = option[3];
      typeValue.value = option[4];
      isChart.value = false;
      setType(props.report.configParams?.chartType);
    } else {
      // xAxis.value = option[1]
      // ySeries.value = option[2]
      // legendData.value = option[3]
      typeValue.value = option[4];
      isChart.value = true;
      setType(props.report.configParams?.chartType);
    }
  };

  const paramsVisible = ref(false);
  const form = reactive({
    name: '',
    posts: [] as any,
  });
  watch(
    () => props.reportDetail,
    (newVal, oldVal) => {
      form.posts = [];
      const paramsList = props.reportDetail?.queryParam.sqlParams;
      paramsList.forEach((item: any) => {
        let value;
        if (item.paramType === 'Text') {
          value = item?.thresholds?.join(',');
        } else if (item.paramType === 'Variable') {
          value = item?.thresholds[0];
        } else if (item.paramType === 'PartDate') {
          value = [item.partDate?.startDate, item.partDate?.endDate];
        } else if (item.paramType === 'Time') {
          value = item.calcuSymbol === 'scope' ? item?.thresholds : item?.thresholds[0];
        } else {
          value = item?.thresholds;
        }

        form.posts.push({
          type: item.paramType,
          value,
          value1: item?.thresholds?.[0],
          value2: item?.thresholds?.[1],
          name: item.paramName,
          select: item?.calcuSymbol || item?.useTimezone,
          selectorItems: item?.selectorItems || [],
          selectedItem: item?.selectedItem || '',
        });
      });
    },
    { immediate: true, deep: true }
  );
  // 应用
  const applyParams = () => {
    const baseParam = {
      paramDisplay: '',
      paramRemark: '',
    };
    // 参数处理器映射
    const paramHandlers = {
      Variable: (item) => ({
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        calcuSymbol: item.select,
        thresholds: [item.value],
      }),
      Selector: (item) => ({
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        selectorItems: item.selectorItems,
        selectedItem: item.selectedItem,
      }),
      PartDate: (item) => ({
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        useTimezone: item.select,
        partDate: {
          startDate: item.value[0],
          endDate: item.value[1],
        },
      }),
      Number: (item) => {
        let thresholds = [];
        if (item.select === 'scope') {
          thresholds = [item.value1, item.value2];
        } else if (item.select === 'ex' || item.select === 'nex') {
          thresholds = [];
        } else {
          thresholds = [item.value1];
        }
        return {
          ...baseParam,
          paramName: item.name,
          paramType: item.type,
          calcuSymbol: item.select,
          thresholds,
        };
      },
      Text: (item) => {
        let thresholds = [];
        if (item.select === 'ex' || item.select === 'nex') {
          thresholds = [];
        } else {
          thresholds =
            item.value
              ?.split(',')
              .map((v) => v.trim())
              .filter(Boolean) || [];
        }
        return {
          ...baseParam,
          paramName: item.name,
          paramType: item.type,
          calcuSymbol: item.select,
          thresholds,
        };
      },
      Time: (item) => ({
        ...baseParam,
        paramName: item.name,
        paramType: item.type,
        calcuSymbol: item.select,
        thresholds: item.select === 'scope' ? [...item.value] : [item.value],
      }),
    };

    // 生成参数列表
    const sqlParamsData = form.posts
      .map((item) => {
        const handler = paramHandlers[item.type];
        return handler ? handler(item) : null;
      })
      .filter(Boolean);
    emits('queryParamsChange', { sql: props.reportDetail?.queryParam.sql, sqlParams: sqlParamsData });
    paramsVisible.value = false;
  };
  const validateForm = () => {
    const invalidParams = form.posts.filter((post) => {
      if (post.type === 'Number') {
        if (post.select === 'ex' || post.select === 'nex') {
          return false;
        }
        if (post.select === 'scope') {
          return !post.value1 || !post.value2; // 同时校验 value1 和 value2
        }
        return !post.value1;
      }
      if (post.type === 'PartDate' || post.type === 'Time') {
        return !post.value || (post.select === 'scope' && (!Array.isArray(post.value) || post.value.length !== 2));
      }
      return !post.value;
    });

    // 返回 true 表示表单有效，返回 false 表示表单无效
    return invalidParams.length === 0;
  };
  const tableRef = ref();
  const exportXlsx = (date: any, name?: string) => {
    tableRef.value?.exportXlsx(date, name);
  };
  defineExpose({
    exportXlsx,
    setChartOption,
  });

  const renderChart = (newData: any) => {
    if (!newData || !newData.result) return;
    const result = newData.result;
    const headers = result.headers;
    const rows = result.rows;
    // 检查数据有效性
    if (!headers || !rows || rows.length === 0) return;
    // X轴数据：取第一列（关卡id）
    xAxis.value = rows.map((row) => row[0]);
    // 图例数据：取headers中除第一项外的所有项
    legendData.value = headers.slice(1);
    // Y轴数据系列：为每个除第一列外的字段创建一个系列
    ySeries.value = headers.slice(1).map((header, index) => {
      // index + 1 跳过第一列
      return {
        name: header,
        type: 'line',
        data: rows.map((row) => row[index + 1]),
      };
    });
  };
  watch(
    () => props.reportAnalysisData,
    (newData) => {
      renderChart(newData);
    }
  );
  // 图标高度
  const chartHeight = computed(() => (props.reportAnalysisData?.result?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)'));
</script>

<template>
  <div class="card-content">
    <div class="card-toolbar">
      <div class="card-filter">
        <a-trigger v-model:popup-visible="paramsVisible" position="bl" trigger="click" :unmount-on-close="false" :update-at-scroll="true">
          <div v-if="form.posts.length" class="url-hover card-ranger-picker">调整参数</div>
          <template #content>
            <div class="sql-params-content">
              <a-form :model="form">
                <a-form-item v-for="(post, index) of form.posts" :key="index" :field="`posts[${index}].value`" :hide-label="true">
                  <span class="param-number">{{ index + 1 }}</span>
                  <span class="param-title">{{ post.name.includes(':') ? post.name.split(':')[1] : post.name }}</span>
                  <div v-if="post.type === 'PartDate'">
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" class="param-select" disabled>
                        <template #arrow-icon />
                        <a-option :value="0">不受时区影响</a-option>
                        <a-option :value="1">受时区影响</a-option>
                      </a-select>
                      <a-range-picker v-model="post.value" style="width: 254px" :allow-clear="false" />
                    </a-space>
                  </div>
                  <div v-else-if="post.type === 'Variable'">
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" default-value="Variable" class="param-select" disabled>
                        <template #arrow-icon />
                        <a-option value="Variable">自定义</a-option>
                      </a-select>
                      <a-input v-model="post.value" :style="{ width: '220px' }" />
                    </a-space>
                  </div>
                  <div v-else-if="post.type === 'Number'">
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" class="param-select" disabled>
                        <template #arrow-icon />
                        <a-option value="eq">等于</a-option>
                        <a-option value="neq">不等于</a-option>
                        <a-option value="lt">小于</a-option>
                        <a-option value="lteq">小于等于</a-option>
                        <a-option value="gt">大于</a-option>
                        <a-option value="gteq">大于等于</a-option>
                        <a-option value="ex">有值</a-option>
                        <a-option value="nex">无值</a-option>
                        <a-option value="scope">区间</a-option>
                      </a-select>
                      <a-input-number v-if="form.posts[index].select != 'ex' && form.posts[index].select != 'nex'" v-model="post.value1" :style="{ width: form.posts[index].select == 'scope' ? '110px' : '220px' }" :min="0" />
                      <a-input-number v-if="form.posts[index].select == 'scope'" v-model="post.value2" :min="0" :style="{ width: form.posts[index].select == 'scope' ? '110px' : '220px' }" />
                    </a-space>
                  </div>
                  <div v-else-if="post.type === 'Text'">
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" class="param-select" disabled>
                        <template #arrow-icon />
                        <a-option value="eq">等于</a-option>
                        <a-option value="neq">不等于</a-option>
                        <a-option value="lt">小于</a-option>
                        <a-option value="lteq">小于等于</a-option>
                        <a-option value="gt">大于</a-option>
                        <a-option value="gteq">大于等于</a-option>
                        <a-option value="con">包含</a-option>
                        <a-option value="ex">有值</a-option>
                        <a-option value="nex">无值</a-option>
                      </a-select>
                      <a-input v-if="form.posts[index].select != 'ex' && form.posts[index].select != 'nex'" v-model="post.value" :style="{ width: '220px' }" />
                    </a-space>
                  </div>
                  <div v-else-if="post.type === 'Time'">
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" class="param-select" :trigger-props="{ autoFitPopupMinWidth: true }" disabled>
                        <template #arrow-icon />
                        <a-option value="eq">等于</a-option>
                        <a-option value="neq">不等于</a-option>
                        <a-option value="lt">小于</a-option>
                        <a-option value="lteq">小于等于</a-option>
                        <a-option value="gt">大于</a-option>
                        <a-option value="gteq">大于等于</a-option>
                        <a-option value="scope"
                          >时间区间
                          <a-tooltip content="选择具体时间范围，包含区间左右值" position="right" mini>
                            <icon-info-circle />
                          </a-tooltip>
                        </a-option>
                      </a-select>
                      <a-date-picker v-if="form.posts[index].select !== 'scope'" v-model="post.value" style="width: 220px" :allow-clear="false" show-time :time-picker-props="{ defaultValue: '09:09:06' }" format="YYYY-MM-DD HH:mm:ss" />
                      <a-range-picker v-else v-model="post.value" style="width: 254px" :allow-clear="false" />
                    </a-space>
                  </div>
                  <div v-else-if="post.type === 'Selector'">
                    <a-space align="center" :size="10">
                      <div v-if="form.posts[index].value === 'Selector'" style="cursor: pointer">
                        <a-select v-model="form.posts[index].selectedItem" :style="{ width: '130px' }" disabled>
                          <a-option v-for="(item, index2) in form.posts[index].selectorItems" :key="index2" :value="item.selectorName" :label="item.selectorName" />
                        </a-select>
                      </div>
                    </a-space>
                  </div>
                  <div v-else>
                    <a-space align="center" :size="10">
                      <a-select v-model="form.posts[index].select" default-value="Variable" class="param-select" disabled>
                        <template #arrow-icon />
                        <a-option value="Variable">自定义</a-option>
                      </a-select>
                      <a-input v-model="post.value" :style="{ width: '220px' }" />
                    </a-space>
                  </div>
                </a-form-item>
              </a-form>
              <div class="footer">
                <a-button size="mini" style="border-radius: 4px" @click="() => (paramsVisible = false)"> 取消 </a-button>
                <a-button type="primary" size="mini" style="margin-left: 12px; border-radius: 4px" :disabled="!validateForm()" @click="applyParams"> 应用 </a-button>
              </div>
            </div>
          </template>
        </a-trigger>
      </div>
      <div class="card-config">
        <a-select v-model="selectValue.value" :style="{ width: '120px', padding: '0 0 0 13px' }" :bordered="false" @change="handleChange">
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/' + data?.value + '.svg'" alt="" />
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon> </template>
          <a-option v-show="isChart && !isOnlyDefault" :value="typeValue?.value">
            <template #icon>
              <img class="option-icon" src="/icon/trend-chart.svg" alt="" />
            </template>
            <template #default>{{ typeValue?.label }}</template>
          </a-option>
          <a-option v-show="!isChart && !isOnlyDefault" value="table-chart">
            <template #icon>
              <img class="option-icon" src="/icon/table-chart.svg" alt="" />
            </template>
            <template #default>{{ selectValue?.label }}</template>
          </a-option>
          <a-option value="default">
            <template #icon>
              <img class="option-icon" src="/icon/table-chart.svg" alt="" />
            </template>
            <template #default>详细数据</template>
          </a-option>
        </a-select>
      </div>
    </div>
    <a-alert v-if="reportAnalysisData?.result?.resultsExceedsLimit" style="margin: 5px 0; flex-shrink: 0"> 因数据条数过多，优先展示前1000条数据 </a-alert>
    <div class="data-chart" :style="{ height: chartHeight }">
      <div class="chart-box">
        <a-spin dot :loading="loading" :size="10" style="width: 100%; height: 100%">
          <TableChartCustom v-show="showSetTable" ref="tableRef" :size="254" :report-analysis-data="reportAnalysisData?.result" :page-size="Math.round((props.report?.h - 4.25) / 0.67)" @page-size-change="(ps) => emits('pageSizeChange', ps)" />
          <a-table v-if="showTable" :show-header="showTableHeader" :columns="columns" :data="filterData" :pagination="false" size="small" :scroll="{ y: '100%' }"> </a-table>
          <Chart v-if="showCharts" :option="chartOption" />
        </a-spin>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import '@/views/dashboard/style/card.less';
  .sql-params-content {
    box-sizing: border-box;
    min-width: 360px !important;
    max-width: 772px !important;
    max-height: 352px;
    padding: 12px 16px;
    overflow: hidden;
    overflow-y: auto;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    background-color: var(--color-bg-popup);
    border-radius: 4px;

    .param-select {
      margin: 0 20px;
    }

    .param-number {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      margin-right: 12px;
      color: rgb(126, 127, 128);
      line-height: 24px;
      text-align: center;
      background-color: var(--color-fill-2);
      border-radius: 4px;
    }

    .param-title {
      font-weight: 500;
      margin-right: 10px;
    }

    .footer {
      width: 100%;
      text-align: right;
    }
  }
</style>
