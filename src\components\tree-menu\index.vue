<template>
  <div class="leftMenu-container">
    <div v-if="loading" class="emptyList">
      <a-spin class="spin-icon" dot>
        <template #tip>
          <span class="spin-tip">加载中...</span>
        </template>
      </a-spin>
    </div>
    <div v-else style="height: 100%">
      <div class="header">
        <a-input
            ref="treeMenuInput"
            v-model="searchQuery" class="search-input" placeholder="搜索看板">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
        <a-tooltip content="看板管理" position="bottom">
          <a-button class="manage-btn" type="text" @click="handleShowDrawer">
            <template #icon>
              <icon-mind-mapping size="18"/>
            </template>
          </a-button>
        </a-tooltip>

        <a-dropdown
            position="bl"
            trigger="hover"
            @select="(value) => addEvent(value)"
        >
          <a-button>
            <template #icon>
              <icon-plus size="15"/>
            </template>
          </a-button>
          <template #content>
            <a-doption value="addDashboard">新建看板</a-doption>
            <a-doption value="addFolder">新建文件夹</a-doption>
            <a-doption value="addSpace">新建空间</a-doption>
            <a-divider :margin="2"/>
            <a-doption>导入看板</a-doption>
            <a-doption>导出看板</a-doption>
          </template>
        </a-dropdown>
      </div>
      <div v-if="mySpace||projectSpaces?.length>0" class="menu-list">
        <div class="menu-group">
          <div id="my-group-title" class="group-title">
            <a-tooltip :content="openBoardKey ? '点击收起':'点击打开'">
              <button class="title" @click="openBoard">我的看板</button>
            </a-tooltip>
            <a-tooltip content="新建文件夹" position="right">
              <a-button
                  id="my-create-btn"
                  type="text"
                  class="create-btn"
                  @click="addEvent('addFolder', mySpace?.spaceId)"
              >
                <template #icon>
                  <icon-plus size="15"/>
                </template>
              </a-button>
            </a-tooltip>
          </div>
          <folderComponent
              v-if="openBoardKey"
              :folders="mySpace?.folders"
              :space-id="mySpace?.spaceId"
              @add="addEvent"
          />
        </div>
        <div class="menu-group">
          <div id="project-group-title" class="group-title">
            <a-tooltip :content="openProjectKey ? '点击收起':'点击打开'">
              <button class="title" @click="openProject">项目空间</button>
            </a-tooltip>
            <a-tooltip content="新建空间" position="right">
              <a-button
                  id="project-create-btn"
                  type="text"
                  class="create-btn"
                  @click="addEvent('addSpace')"
              >
                <template #icon>
                  <icon-plus size="15"/>
                </template>
              </a-button>
            </a-tooltip>
          </div>
          <spaceComponent
              v-if="openProjectKey"
              :spaces="projectSpaces"
              @add="addEvent">

          </spaceComponent>
        </div>
      </div>
      <div v-if="projectSpaces?.length===0&& mySpace===undefined" class="emptyList">
        <a-empty/>
      </div>
    </div>
    <add-space v-model:visible="showAddSpace"/>
    <add-folder
        v-model:visible="showAddFolder"
        :my-space="mySpace"
        :project-spaces="projectSpaces"
        :add-in-space="addInSpace"
    />
    <add-dashboard
        v-model:visible="showAddDashboard"
        :my-space="mySpace"
        :project-spaces="projectSpaces"
        :add-in-space="addInSpace"
        :add-in-folder="addInFolder"
    />
    <drawer
        v-model:mySpace="mySpace"
        v-model:projectSpaces="projectSpaces"
        v-model:visible="drawerShow"
        @cancel-drawer="handleCancelDrawer"
    />
  </div>
</template>

<script lang="ts" setup>
import {defineExpose, nextTick, onMounted, ref, watch} from 'vue';
import {listSpace} from '@/api/space/api';
import {SpaceDto} from '@/api/space/type';
import _ from 'lodash';
import {ObjectPermissionType} from '@/api/enum';
import drawer from '@/components/tree-menu/components/management/index.vue';
import AddSpace from '@/components/tree-menu/components/modal/add/add-space.vue';
import AddFolder from '@/components/tree-menu/components/modal/add/add-folder.vue';
import AddDashboard from '@/components/tree-menu/components/modal/add/add-dashboard.vue';
import {useEventBus, useSessionStorage} from '@vueuse/core';
import {AddEvent, CacheEventBus, CopyEvent, DeleteEvent, MoveEvent, RefreshEvent, RenameEvent, TreeMenuEventBus} from '@/types/event-bus';
import {useDashboardStore} from "@/store";
import spaceComponent from './components/space/index.vue';
import folderComponent from './components/folder/index.vue';

const cacheEventBus = useEventBus(CacheEventBus)
// 搜索框的值
const treeMenuInput = ref(null);
const focusInput = async () => {
  await nextTick();
  if (treeMenuInput.value) {
    treeMenuInput.value.focus();
  }
}
/**
 * 我的看板
 */
const mySpace = ref<SpaceDto>();

/**
 * 项目空间
 */
const projectSpaces = ref<SpaceDto[]>();
// 看板目录开关key
const openProjectKey = ref(true)
const openBoardKey = ref(true)
const openOverAllKey = ref(true)
const openFinancialKey = ref(true)

const showAddSpace = ref<boolean>(false);
const showAddFolder = ref<boolean>(false);
const showAddDashboard = ref<boolean>(false);
const addInFolder = ref<string>();
const addInSpace = ref<string>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus);
const dashboardStore = useDashboardStore()
const loading = ref<boolean>(true);
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});

const drawerShow = ref(false);

function handleShowDrawer() {
  drawerShow.value = true;
  // 设置传递的参数
}

function handleCancelDrawer() {
  drawerShow.value = false;
}

function addEvent(addType: string, spaceId?: string, folderId?: string) {
  switch (addType) {
    case 'addDashboard':
      showAddDashboard.value = true;
      addInSpace.value = spaceId;
      addInFolder.value = folderId;
      break;
    case 'addFolder':
      showAddFolder.value = true;
      addInSpace.value = spaceId;
      addInFolder.value = undefined;
      break;
    case 'addSpace':
      showAddSpace.value = true;
      break;
    default:
  }
}

const searchQuery = ref<string>('');
//
// // ismatch
function isMatch(searchTerm: string, text: string): boolean {
  let i = 0;
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();
  for (const char of lowerSearchTerm) {
    i = lowerText.indexOf(char, i);
    if (i === -1) {
      return false;
    }
    i++;
  }
  return true;
}

function filterFolders(folders, searchTerm) {
  return folders.map(folder => {
    // 递归处理子文件夹
    const filteredSubFolders = folder.folders && folder.folders.length > 0
      ? filterFolders(folder.folders, searchTerm)
      : [];
    // 筛选当前文件夹下的 dashboards
    const filteredDashboards = folder.dashboards.filter(dashboard =>
      isMatch(searchTerm, dashboard.name)
    );
    // 如果当前文件夹、子文件夹或仪表盘有匹配项，保留该文件夹
    if (
      filteredDashboards.length > 0 ||
      filteredSubFolders.length > 0 ||
      isMatch(searchTerm, folder.name)
    ) {
      return {
        ...folder,
        dashboards: filteredDashboards,
        folders: filteredSubFolders
      };
    }
    return null;
  }).filter(Boolean);
}

function filterTree(nodes, searchTerm) {
  if (!searchTerm) return nodes;
  return nodes.map(node => {
    const filteredDashboards = node.dashboards.filter(dashboard =>
      isMatch(searchTerm, dashboard.name)
    );
    const filteredFolders = filterFolders(node.folders, searchTerm);
    if (
      filteredFolders.length > 0 ||
      filteredDashboards.length > 0 ||
      isMatch(searchTerm, node.name)
    ) {
      return {
        ...node,
        dashboards: filteredDashboards,
        folders: filteredFolders
      };
    }
    return null;
  }).filter(Boolean);
}


const originSpaces = ref()
/**
 * 刷新菜单
 */
// 定义 sleep 函数
const refreshMenu = async () => {

  loading.value = true
  listSpace(true).then((resp: SpaceDto[]) => {
        dashboardStore.spaceList = resp
        const spaces = dashboardStore.spaceList || [];
        const filterSpaces = filterTree(spaces, searchQuery.value)
        originSpaces.value = spaces
        const mySpaceList = filterSpaces
            .filter((spaceItem) => spaceItem.authority === ObjectPermissionType.OWNER)
            .map((spaceItem) => {
              spaceItem.folders.sort((a, b) => a.order - b.order);
              spaceItem.folders.forEach((folder) => {
                folder.dashboards.sort((a, b) => a.order - b.order);
              });
              return spaceItem;
            });

        const projectSpaceList = filterSpaces
            .filter((spaceItem) => spaceItem.authority !== ObjectPermissionType.OWNER)
            .map((spaceItem) => {
              const combinedItems = [
                ...spaceItem.folders.map((folder) => ({...folder, type: 'folder'})),
                ...spaceItem.dashboards.map((dashboard) => ({...dashboard, type: 'dashboard'})),
              ];
              // 按 order 进行排序
              combinedItems.sort((a, b) => a.order - b.order);
              // 分别还原回 folders 和 dashboards
              spaceItem.folders = combinedItems.filter((item) => item.type === 'folder');
              spaceItem.dashboards = combinedItems.filter((item) => item.type === 'dashboard');
              spaceItem.folders.forEach((folder) => {
                folder.dashboards.sort((a, b) => a.order - b.order);
              });
              return spaceItem;
            });
        if (!_.isEmpty(mySpaceList) && mySpaceList.length === 1) {
          // eslint-disable-next-line prefer-destructuring
          mySpace.value = mySpaceList[0];
        }
        if (!_.isEmpty(projectSpaceList) && projectSpaceList.length > 0) {
          projectSpaces.value = projectSpaceList;
        }
        loading.value = false

        let dashboardSelectedExist = false;

        resp.forEach(space => {
          space.folders?.forEach(folder => {
            folder.dashboards?.forEach(dashboard => {
              if (dashboard.dashboardId === dashboardSelected.value?.dashboardId) {
                dashboardSelectedExist = true
              }
            })
          })
          space.dashboards?.forEach(dashboard => {
            if (dashboard.dashboardId === dashboardSelected.value?.dashboardId) {
              dashboardSelectedExist = true
            }
          })
        })
        if (dashboardSelectedExist) {
          dashboardStore.dashboardSelected = dashboardSelected.value;
        }
      }
  );
};

treeMenuEventBus.on((event) => {
  if (event === RefreshEvent) {
    refreshMenu();
  }
});

watch(searchQuery, (newData) => {
  dashboardStore.dashboardSearchStore = newData.length > 0;
  mySpace.value = filterTree(originSpaces.value, newData).filter((spaceItem) => {
    return spaceItem.authority === ObjectPermissionType.OWNER;
  })[0]
  projectSpaces.value = filterTree(originSpaces.value, newData).filter((spaceItem) => {
    return spaceItem.authority !== ObjectPermissionType.OWNER;
  });
})
watch(() => dashboardStore.spaceList, (newVal) => {
  projectSpaces.value = newVal.filter(space => space.authority !== ObjectPermissionType.OWNER);
  mySpace.value = newVal.find(space => space.authority === ObjectPermissionType.OWNER)
}, {deep: true})

// watch()
// //   折叠侧边栏
// function handleCancel(){
//   handleSidebar('handleCancel',sidebar);
//   // sidebar.value=true
// }
// function handleCancels(){
//   sidebar.value=false
// }
const showIconFinancial = ref(false)
const showIconOverAll = ref(false)
const selectedDate = ref('')
const selectedFinancial = ref('')
const selectOverAll = (value: string) => {
  dashboardSelected.value = {
    spaceId: `spaceId${value}`,
    folderId: '',
    dashboardId: `dashboardId${value}`
  }
  dashboardStore.dashboardSelected = {
    spaceId: `spaceId${value}`,
    folderId: '',
    dashboardId: `dashboardId${value}`
  }
  selectedDate.value = value
}
const selectFinancial = (value: string) => {
  dashboardSelected.value = {
    spaceId: `spaceId${value}`,
    folderId: '',
    dashboardId: `dashboardId${value}`
  }
  dashboardStore.dashboardSelected = {
    spaceId: `spaceId${value}`,
    folderId: '',
    dashboardId: `dashboardId${value}`
  }
  selectedFinancial.value = value
}
watch(() => dashboardSelected, (newVal) => {
      selectedDate.value = newVal.value.spaceId?.slice(-2)
      selectedFinancial.value = newVal.value.spaceId?.slice(-2)
    },
    {immediate: true, deep: true}
);
/**
 * 控制看板目录折叠
 */
const openOverAll = () => {
  openOverAllKey.value = !openOverAllKey.value
}
const openFinancialAll = () => {
  openFinancialKey.value = !openFinancialKey.value
}
const openBoard = () => {
  openBoardKey.value = !openBoardKey.value
}
const openProject = () => {
  openProjectKey.value = !openProjectKey.value
}
defineExpose({focusInput, loading})

/**
 * 初始化
 */
onMounted(() => {
  refreshMenu()
});
// 缓存数据移动方法 todo 看板项目空间前段缓存修改
const moveDashboardEvent = (dashboardId: string, folderId: string, spaceId: string, order?: number) => {
  dashboardStore.spaceList?.some((space) => {
    if (space.spaceId === spaceId) {
      // 查找目标 folder（如果提供了 folderId）
      const targetFolder = folderId
          ? space.folders.find((folder: any) => folder.folderId === folderId)
          : null;
      // 如果目标是 folder，但找不到目标 folder，则提前退出
      if (folderId && !targetFolder) return false;
      // 在所有 space 中查找并移除 dashboard
      return dashboardStore.spaceList?.some((currentSpace) => {
        // 先在 folders 中查找 dashboard
        const foundInFolder = currentSpace.folders?.some((folder: any) => {
          const dashboardIndex = folder.dashboards.findIndex(
              (dashboard: any) => dashboard.dashboardId === dashboardId
          );
          if (dashboardIndex !== -1) {
            // 从原始 folder 中移除 dashboard
            const [dashboard] = folder.dashboards.splice(dashboardIndex, 1);
            // 更新 dashboard 的 spaceId 和 folderId

            dashboard.space = space;
            dashboard.folder = targetFolder || '';  // 如果没有 folderId，设为 null
            // 将 dashboard 插入到目标 folder 或 space 的指定位置
            if (targetFolder) {
              if (typeof order === 'number' && order >= 0 && order <= targetFolder.dashboards.length) {
                targetFolder.dashboards.splice(order, 0, dashboard);
              } else {
                targetFolder.dashboards.push(dashboard); // 末尾插入
              }
            } else if (typeof order === 'number' && order >= 0 && order <= space.dashboards.length) {
              space.dashboards.splice(order, 0, dashboard);

            } else {
              space.dashboards.push(dashboard); // 末尾插入
            }
            if (dashboardId === dashboardSelected.value.dashboardId) {
              dashboardSelected.value = {
                ...dashboardSelected.value,
                folderId: targetFolder.folderId || '',
                spaceId: targetFolder.space.spaceId || space.spaceId
              };
            }

            return true; // 找到后退出
          }
          return false;
        });
        if (!foundInFolder) {
          // 如果没在 folders 中找到，则检查 space 中的 dashboards
          const dashboardIndex = currentSpace.dashboards.findIndex(
              (dashboard: any) => dashboard.dashboardId === dashboardId
          );
          if (dashboardIndex !== -1) {
            // 从当前 space 中移除 dashboard
            const [dashboard] = currentSpace.dashboards.splice(dashboardIndex, 1);
            dashboard.space = space;
            dashboard.folder = targetFolder || '';
            // 将 dashboard 插入到目标 folder 或 space 的指定位置
            if (targetFolder) {
              if (typeof order === 'number' && order >= 0 && order <= targetFolder.dashboards.length) {
                targetFolder.dashboards.splice(order, 0, dashboard);
              } else {
                targetFolder.dashboards.push(dashboard); // 末尾插入
              }
            } else if (typeof order === 'number' && order >= 0 && order <= space.dashboards.length && space.name !== '我的看板') {
              space.dashboards.splice(order, 0, dashboard);

            } else {
              space.dashboards.push(dashboard); // 末尾插入
            }

            dashboardSelected.value = {
              ...dashboardSelected.value,
              folderId: targetFolder.folderId || '',
              spaceId: targetFolder.space.spaceId || space.spaceId
            };
            return true; // 找到后退出
          }
        }
        return false;
      });
    }
    return false;
  });
}
const moveSpaceEvent = (spaceId: string, order?: number) => {
  // 查找源空间的索引
  const spaceIndex = dashboardStore.spaceList.findIndex((space: any) => space.spaceId === spaceId);
  if (spaceIndex === -1) return; // 如果找不到源空间，则退出

  // 找到目标空间
  const [spaceToMove] = dashboardStore.spaceList.splice(spaceIndex, 1); // 从原列表中移除

  // 根据 order 插入到新的位置
  if (typeof order === 'number' && order >= 0 && order <= dashboardStore.spaceList.length) {
    dashboardStore.spaceList.splice(order, 0, spaceToMove); // 在指定位置插入
  } else {
    dashboardStore.spaceList.push(spaceToMove); // 默认插入到末尾
  }
};
const moveFolderEvent = (folderId: string, spaceId: string, order?: number) => {
  dashboardStore.spaceList?.some((space) => {
    // 查找源空间中要移动的目标文件夹
    const folderIndex = space.folders.findIndex((folder: any) => folder.folderId === folderId);
    if (folderIndex !== -1) {
      // 找到目标文件夹
      const [folderToMove] = space.folders.splice(folderIndex, 1); // 从源空间中移除

      // 查找目标空间
      const targetSpace = dashboardStore.spaceList.find((s) => s.spaceId === spaceId);
      if (targetSpace) {

        // 根据 order 插入到目标空间中
        if (typeof order === 'number' && order >= 0 && order <= targetSpace.folders.length) {
          targetSpace.folders.splice(order, 0, folderToMove); // 在指定位置插入
        } else {
          targetSpace.folders.push(folderToMove); // 末尾插入
          folderToMove.space = targetSpace
        }
        folderToMove.dashboards = folderToMove.dashboards.map(dashboard => {
          // 返回一个新的对象，更新 space
          return {
            ...dashboard, // 保留原有的其他属性
            space: targetSpace // 更新 space
          };
        });
        return true; // 找到并移动后退出循环
      }
    }

    return false; // 未找到则继续
  });
};
const copyDashboardEvent = (dashboard: any, spaceId: string, folderId?: string) => {
  // 找到目标空间
  const targetSpace = dashboardStore.spaceList.find((space) => space.spaceId === spaceId);

  if (!targetSpace) {
    console.error('Target space not found');
    return; // 如果没有找到目标空间，提前退出
  }

  // 如果指定了 folderId，则查找目标文件夹
  if (folderId) {
    const targetFolder = targetSpace.folders.find((folder: any) => folder.folderId === folderId);
    if (targetFolder) {
      // 将仪表板添加到目标文件夹的末尾
      targetFolder.dashboards.push(...dashboard);
    } else {
      console.error('Target folder not found');
    }
  } else {
    // 如果没有指定 folderId，插入到空间的顶层 dashboards
    targetSpace.dashboards.push(...dashboard);
  }
};
const deleteSpaceEvent = (spaceId: string) => {
  const spaceIndex = dashboardStore.spaceList.findIndex(space => space.spaceId === spaceId);
  // 在所有空间中查找“未分组”和“共享给我的”文件夹
  const unassignedFolder = dashboardStore.spaceList
      .flatMap((space: any) => space.folders)
      .find((folderFind: any) => folderFind.name === '未分组');

  const sharedFolder = dashboardStore.spaceList
      .flatMap(space => space.folders)
      .find((folderFind: any) => folderFind.name === '分享给我的');

  if (spaceIndex !== -1) {
    const FindSpace = dashboardStore.spaceList[spaceIndex];
    // 如果找到了目标文件夹，则将仪表板移动到相应的文件夹
    if (unassignedFolder && sharedFolder) {
      // 处理要删除的空间的仪表板
      FindSpace.dashboards.forEach((dashboard: any) => {
        if (dashboard.authority === 1 || dashboard.authority === 2) {
          unassignedFolder.dashboards.push(dashboard); // 推入未分组文件夹
          dashboard.space = dashboardStore.spaceList.find((space: any) => space.authority === 1);
          dashboard.folder = unassignedFolder
        } else if (dashboard.authority === 3 || dashboard.authority === 4) {
          sharedFolder.dashboards.push(dashboard); // 推入共享给我的文件夹
          dashboard.space = dashboardStore.spaceList.find((space: any) => space.authority === 1);
          dashboard.folder = sharedFolder
        }
      });
      // 处理要删除的空间的所有文件夹
      FindSpace.folders.forEach((folderFind: any) => {
        folderFind.dashboards.forEach((dashboard: any) => {
          if (dashboard.authority === 1 || dashboard.authority === 2) {
            unassignedFolder.dashboards.push(dashboard); // 推入未分组文件夹
          } else if (dashboard.authority === 3 || dashboard.authority === 4) {
            sharedFolder.dashboards.push(dashboard); // 推入共享给我的文件夹
          }
        });
      });
    }
    // 从 spaceList 中删除目标空间
    dashboardStore.spaceList.splice(spaceIndex, 1); // 删除空间
  } else {
    console.error('Target space not found');
  }
};
const renameSpaceEvent = (spaceId: string, name: string) => {
  const spaceIndex = dashboardStore.spaceList.findIndex(space => space.spaceId === spaceId);
  if (spaceIndex !== -1) {
    // 找到目标空间并修改其名称
    dashboardStore.spaceList[spaceIndex].name = name;
  } else {
    console.warn('Target space not found');
  }
};
const addSpaceEvent = (space: any) => {
  dashboardStore.spaceList.push(space)
}
const addFolderEvent = (folder: any, spaceId: string) => {
  dashboardStore.spaceList?.find(space => space.spaceId === spaceId).folders.push(folder)

}
// Add Space
cacheEventBus.on((event, {type, space}) => {
  if (event === AddEvent && type === 'space')
    addSpaceEvent(space)
})
// add folder
cacheEventBus.on((event, {type, folder, spaceId}) => {
  if (event === AddEvent && type === 'folder')
  addFolderEvent(folder, spaceId)
})
// rename Space
cacheEventBus.on((event, {type, spaceId, name}) => {
  if (event === RenameEvent && type === 'space')
    renameSpaceEvent(spaceId, name)
})
// Delete space
cacheEventBus.on((event, {type, spaceId,}) => {
  if (event === DeleteEvent && type === 'space')
    deleteSpaceEvent(spaceId)
})
// copy dashboard
cacheEventBus.on((event, {type, dashboard, spaceId, folderId}) => {
  if (event === CopyEvent && type === 'dashboard')
    copyDashboardEvent(dashboard, spaceId, folderId)
})
// move space
cacheEventBus.on((event, {type, spaceId, order}) => {
  if (event === MoveEvent && type === 'space')
    moveSpaceEvent(spaceId, order)
})
// move folder
cacheEventBus.on((event, {type, folderId, spaceId, order}) => {
  if (event === MoveEvent && type === 'folder')
    moveFolderEvent(folderId, spaceId, order)
})
// move dashboard
cacheEventBus.on((event, {type, dashboardId, folderId, spaceId, order}) => {
  if (event === MoveEvent && type === 'dashboard')
    moveDashboardEvent(dashboardId, folderId, spaceId, order)
})

</script>

<style scoped lang="less">
.leftMenu-container {
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64px;
  padding: 0 8px;
  background-color: var(--tant-bg-white-color-bg1-1);

  .search-input {
    width: 190px;
    margin-right: 8px;
    padding: 0 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid var(--tant-bg-white-color-bg1-1);
    border-bottom: 1px solid var(--tant-border-color-border1-1);
    border-radius: 8px;
    line-height: 32px;
    height: 32px;
  }

  .search-input:hover {
    border: 1px solid var(--tant-bg-white-color-bg1-1);
    border-bottom: 1px solid var(--tant-border-color-border1-1);
  }

  .search-input:focus-within {
    border-bottom: 1px solid var(--tant-border-color-border1-1);
  }

  .manage-btn {
    color: var(--tant-secondary-color-secondary-default);
    font: var(--tant-body-font-body-regular);
    text-transform: capitalize;
    border-radius: var(--tant-border-radius-medium);
  }
}

.menu-list {
  height: calc(100% - 64px);
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--tant-bg-white-color-bg1-1);

  .menu-group {

    padding: 0 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    margin-bottom: 12px;

    .group-title {
      max-width: 260px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;

      .title {
        border: none;
        background-color: var(--tant-bg-white-color-bg1-1);
        height: 22px;
        padding: 2px 6px;
        color: var(--tant-text-gray-color-text1-3);
        font: var(--tant-description-font-description-medium);
        cursor: pointer;
      }

      .title:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
        border-radius: 4px;
      }
    }

    .group-title:hover .title {
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-description-font-description-medium);
    }

    #my-group-title:hover #my-create-btn {
      visibility: visible;
    }

    #project-group-title:hover #project-create-btn {
      visibility: visible;
    }
  }

  .create-btn {
    height: 24px;
    width: 24px;
    color: var(--tant-secondary-color-secondary-default);
    font: var(--tant-body-font-body-regular);
    text-transform: capitalize;
    border-radius: var(--tant-border-radius-medium);
    visibility: hidden;
  }
}

.footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  height: 48px;
  padding: 8px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-top: 1px solid var(--tant-border-color-border1-1);
}

.emptyList {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .spin-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .spin-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--tant-slate-slate-50);
    }
  }

}

.space-item {
  padding-left: 12px;
}

:deep(.arco-menu-vertical .arco-menu-inner) {
  padding: 0;
}

.dashboard {
  padding-left: 22px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;

    .icon {
      display: flex;
      height: 16px;
      margin-right: 8px;
    }


  }

  &:hover {
    background-color: var(--tant-secondary-color-secondary-fill-hover);
  }
}

.dashboard-selected {
  background-color: var(--tant-primary-color-primary-fill) !important;
}
</style>
