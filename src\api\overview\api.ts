import {AxiosPromise} from "axios";
import {getRequest} from "@/api/request";

export function getOverviewSummary(type: string, startDate: string, endDate: string, country: string, aggregationType?: string): AxiosPromise<any> {
  return getRequest<any>('/api/overview/summary', {
    'type': type,
    'startDate': startDate,
    'endDate': endDate,
    'country': country,
    'aggregationType': aggregationType,
  });
}

export function getOverviewTopic(topic: string, indicator: indicator, startDate: string, endDate: string, country: string): AxiosPromise<any> {
  return getRequest<any>('/api/overview/topic', {
    'topic': topic,
    'indicator': indicator,
    'startDate': startDate,
    'endDate': endDate,
    'country': country,
  });
}

// 总览数据详情（新）
export function getOverviewDetail(params): AxiosPromise<any> {
  const queryParams = new URLSearchParams();
  
  // 处理普通参数
  if (params.type) queryParams.append('type', params.type);
  if (params.startDate) queryParams.append('start_date', params.startDate);
  if (params.endDate) queryParams.append('end_date', params.endDate);
  if (params.country) queryParams.append('country', params.country);
  if (params.aggregationType) queryParams.append('aggregation_type', params.aggregationType);
  if (params.timeParticleSize) queryParams.append('time_particle_size', params.timeParticleSize);
  
  // 处理数组参数
  if (params.team && Array.isArray(params.team)) {
    params.team.forEach(item => queryParams.append('team', item));
  } else if (params.team) {
    queryParams.append('team', params.team);
  }
  
  if (params.group && Array.isArray(params.group)) {
    params.group.forEach(item => queryParams.append('group', item));
  } else if (params.group) {
    queryParams.append('group', params.group);
  }
  
  if (params.appIds && Array.isArray(params.appIds)) {
    params.appIds.forEach(item => queryParams.append('app_ids', item));
  } else if (params.appIds) {
    queryParams.append('app_ids', params.appIds);
  }

  return getRequest<any>(`/api/overview/summary/detail?${queryParams.toString()}`, {});
}
// 主题数据详情（新）
export function getOverviewTopicDetail(params): AxiosPromise<any> {
  const queryParams = new URLSearchParams();
  
  // 处理普通参数
  if (params.topic) queryParams.append('topic', params.topic);
  if (params.startDate) queryParams.append('start_date', params.startDate);
  if (params.endDate) queryParams.append('end_date', params.endDate);
  if (params.country) queryParams.append('country', params.country);
  if (params.indicator) queryParams.append('indicator', params.indicator);
  if (params.timeParticleSize) queryParams.append('time_particle_size', params.timeParticleSize);
  
  // 处理数组参数
  if (params.team && Array.isArray(params.team)) {
    params.team.forEach(item => queryParams.append('team', item));
  } else if (params.team) {
    queryParams.append('team', params.team);
  }
  
  if (params.group && Array.isArray(params.group)) {
    params.group.forEach(item => queryParams.append('group', item));
  } else if (params.group) {
    queryParams.append('group', params.group);
  }
  
  if (params.appIds && Array.isArray(params.appIds)) {
    params.appIds.forEach(item => queryParams.append('app_ids', item));
  } else if (params.appIds) {
    queryParams.append('app_ids', params.appIds);
  }

  return getRequest<any>(`/api/overview/topic/detail?${queryParams.toString()}`, {});
}