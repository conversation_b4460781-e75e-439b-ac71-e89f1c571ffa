<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {computed, ref, watch} from "vue";
import {cloneDeep, range} from "lodash";
import {useSessionStorage} from '@vueuse/core';

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
}

const props = defineProps<Props>()
// 指标选择
const indicatorList = ref<string[]>([]);
const indicatorSelectedIndex = ref<number[]>([0]);
const indicatorSelectedNames = ref<any>([]);
const groups = ref<string[]>([]);
const option = ref<any>();
const indicatorsInput = ref('')
const indeterminate = ref(false)
const checkedAll = ref(true)
const groupsInput = ref('') // 分组搜索
const selectedGroups = ref<string[]>([]);
const checkedDefault = ref(true)
// 默认选中的分组数设置
const showSide = ref(false)
const limitCustom = ref(10)
const limitType = ref('less')
const sureType = ref('less')
const limitGroupNumber = ref(10)
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
groups.value = props.eventData?.groupQueryResult?.[0].resultData.map(data => data.group.join(','))
// selectedGroups.value = cloneDeep(groups.value).slice(0,limitGroupNumber.value) || []
const initGroups = () => {
  const validGroups = checkedSessionGroups.value.filter(item => groups.value.includes(item));
  const defaultLimit = Math.min(10, groups.value.length);

  selectedGroups.value = validGroups.length > 0
      ? validGroups
      : groups.value.slice(0, defaultLimit);
  checkedDefault.value = validGroups.length === 0;
}
initGroups()
const renderChart = (newData) => {
  if (newData === undefined) {
    return
  }
  const {groupsDesc, groupQueryResult, timeSpan, newUserCount} = newData;
  // 横坐标
  let timeSpanPrefix;
  switch (timeSpan.unit) {
    case 'DAY':
      timeSpanPrefix = 'D'
      break
    case 'WEEK':
      timeSpanPrefix = 'W'
      break
    case 'MONTH':
      timeSpanPrefix = 'M'
      break
    default:
      timeSpanPrefix = ''
      break;
  }
  const xData = range(0, timeSpan.number + 1).map(i => `${timeSpanPrefix}${i}`)
  const {chartOption} = useChartOption(() => {
    return {
      grid: {
        left: '0%',
        right: '1%',
        top: '20',
        bottom: '50',
        containLabel: true
      },
      legend: {
        data: indicatorSelectedIndex.value.flatMap((indicatorIdx) =>
            groups.value
                .filter(groupName => selectedGroups.value.some(selected => selected === groupName))
                .map(groupName => `${indicatorList.value[indicatorIdx]}-${groupName}`)
        ),
        bottom: '0',
        type: 'scroll'
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        className: 'echarts-tooltip-diy',
      },
      xAxis: {
        type: 'category',
        data: xData,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter(value: number) {
            if (value >= 1000000) {
              return `${value / 1000000}M`; // 转换为 k 单位
            }
            if (value >= 1000) {
              return `${value / 1000}k`; // 转换为 k 单位
            }
            return value;
          }
        }
      },
      series: indicatorSelectedIndex.value.flatMap((indicatorIdx) =>
          groups.value
              .map((groupName: any, groupIdx: number) => ({
                groupName,
                groupIdx,
                data: groupQueryResult[indicatorIdx].resultData[groupIdx]
              }))
              .filter(({groupName}) => selectedGroups.value.some(selected => selected === groupName))
              .map(({groupName, data}) => ({
                name: `${indicatorList.value[indicatorIdx]}-${groupName}`,
                type: 'line',
                data: data.values,
                label: {
                  show: true,
                  position: 'top',
                  formatter(params) {
                    if (!params.value || typeof params.value !== 'number') {
                      return params.value;
                    }
                    if (params.value >= 1000000) {
                      return `${Math.round(params.value / 1000000)}M`;
                    }
                    if (params.value >= 1000) {
                      return `${Math.round(params.value / 1000)}k`;
                    }
                    return Math.round(params.value);
                  }
                }
              }))
      )
    };
  });
  option.value = chartOption.value
}

watch(() => props.eventData, (newData) => {
  indicatorList.value = props.eventData.groupQueryResult?.map(result => result.displayName)
  indicatorSelectedIndex.value = indicatorList.value.map((item, index) => index);
  indicatorSelectedNames.value = [...indicatorList.value];
  renderChart(newData);
}, {immediate: true, deep: true})

watch(() => selectedGroups.value, () => {
  renderChart(props.eventData);
}, {deep: true})

watch(() => indicatorSelectedIndex.value, () => {
  renderChart(props.eventData);
}, {deep: true})
// renderChart(props.eventData);

const handleDefaultGroupNum = () => {
  limitType.value = sureType.value
  showSide.value = !showSide.value
}
const limitChange = (v) => {

}
const checkedChangeDefault = (value) => {
  if (value) {
    const copyGroups = cloneDeep(groups.value);
    checkedDefault.value = true;
    selectedGroups.value = copyGroups.slice(0, limitGroupNumber.value);
  } else {
    checkedDefault.value = false
    selectedGroups.value = []
  }
}
const limitCancel = () => {
  showSide.value = false
}
const limitSure = () => {
  sureType.value = limitType.value
  if (sureType.value === 'less') {
    limitGroupNumber.value = 10
    limitCustom.value = 10
  } else if (sureType.value === 'middle') {
    limitGroupNumber.value = 20
    limitCustom.value = 20
  } else if (sureType.value === 'more') {
    limitGroupNumber.value = 30
    limitCustom.value = 30
  } else {
    limitCustom.value = Math.min(limitCustom.value, 50);
    limitGroupNumber.value = limitCustom.value
  }
  selectedGroups.value = groups.value.slice(0, limitGroupNumber.value);
  showSide.value = false
}
const popChange = () => {
  showSide.value = false
}
// 全选
const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    indicatorSelectedIndex.value = indicatorList.value.map((item, index) => index);
    indicatorSelectedNames.value = [...indicatorList.value];
  } else {
    checkedAll.value = false;
    indicatorSelectedIndex.value = [];
    indicatorSelectedNames.value = [];
  }
}
const handleIndicatorChange = (values) => {
  // 根据选中的名称获取对应的索引
  indicatorSelectedIndex.value = values.map(name =>
      indicatorList.value.findIndex(item => item === name)
  ).filter(index => index !== -1);

  if (indicatorSelectedIndex.value.length === indicatorList.value.length) {
    checkedAll.value = true;
    indeterminate.value = false;
  } else if (indicatorSelectedIndex.value.length === 0) {
    checkedAll.value = false;
    indeterminate.value = false;
  } else {
    checkedAll.value = false;
    indeterminate.value = true;
  }
}
const handleChangeGroups = (values) => {
  checkedSessionGroups.value = values
}
const filteredGroups = computed(() => {
  const str = groupsInput.value.trim().toLowerCase();
  return groups.value.filter(item => item.toLowerCase().includes(str));
});
const filteredIndicators = computed(() => {
  const str = indicatorsInput.value.trim().toLowerCase();
  return indicatorList.value.filter(item => item.toLowerCase().includes(str));
});
</script>

<template>
  <div class="view-content">
    <a-spin :loading="!props?.eventData?.groups?.length" style="width: 100%;height: 100%">
      <Chart :option="option"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.view-content {
  width: 100%;
  height: 100%;

  .header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .chart {
    width: 100%;
    height: calc(100% - 20px);
  }
}

.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}

:deep(.arco-dropdown-option) {
  padding: 0;
}

:deep(.arco-dropdown-option-content) {
  max-width: 200px;
}

:deep(.arco-checkbox) {
  padding: 0 12px;

  &:hover {
    background-color: var(--color-fill-2);
  }
}

.ta-default-group-side-left {
  position: absolute;
  top: 0;
  left: -285px;
  width: 280px;

  height: 0;

  .group-side {
    width: 100%;
    padding: 16px 16px 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: none;
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-bottom);

    .group-side-head {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 30px;
      margin-bottom: 16px;
      padding-left: 0;
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
    }

    .group-side-body {
      min-height: 220px;
      padding-left: 0;

      .radio-desc {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .limit-set {
          color: var(--tant-text-gray-color-text1-3);
        }
      }

      :deep(.arco-radio-label) {
        width: 100%;
      }

      .group-info-wrap {
        margin-top: 24px;
        margin-bottom: 24px;

        .group-info {
          width: 100%;
          margin-bottom: 8px;
          margin-left: 0;
          color: var(--tant-text-gray-color-text1-3);
          font-size: 14px;
          line-height: 22px;

          .bold {
            margin: 0 4px;
            color: var(--tant-text-gray-color-text1-2);
          }
        }

        .group-info:before {
          position: relative;
          top: 3px;
          padding-right: 10px;
          font-weight: 500;
          font-size: 20px;
          content: "\b7";
        }
      }
    }

    .group-side-foot {
      margin-right: -16px;
      margin-left: -16px;
      padding: 8px 16px 0;
      text-align: right;
      border-top: 1px solid var(--tant-border-color-border1-1);

      button {
        border-radius: 4px;
      }
    }
  }
}

.icon-set {
  padding-right: 12px;
  cursor: pointer;
  color: var(--tant-primary-color-primary-hover);
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}
</style>
