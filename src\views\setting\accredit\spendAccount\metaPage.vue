

<template>
    <div style="width: 100%;height: 100%;display: flex;flex-direction: column;">
        <div class="tab">
            <div class="title">类型</div>
            <a-radio-group v-model:model-value="metaType" type="button" @change="metaTypeChange">
                <a-radio value="1">Facebook账户</a-radio>
                <a-radio value="2">广告账户</a-radio>
            </a-radio-group>
        </div>
        <div v-if="metaType === '1'" class="setting">
            <div class="label">筛选</div>
            <div class="setting-content">
              <a-select
                  v-model:model-value="fbParams.autoBind"
                  placeholder="请选择开关"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>开关-{{ data?.label }}</span>
                  </template>
                  <a-option :value="1">开</a-option>
                  <a-option :value="0">关</a-option>
              </a-select>
              <a-select
                  v-model:model-value="fbParams.userName"
                  placeholder="请选择FB个人号"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                  <span>FB个人号-{{ data?.label }}</span>
                  </template>
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-cascader v-model:model-value="fbParams.authMember" placeholder="授权人" style="width: 240px;margin-right: 24px;"/>
              <DateRangePicker
                v-model="fbParams.issuedAt"
                :placeholder="['授权开始日期','授权结束日期']"
                @change="dateChange"
              />
              <a-select
                  v-model:model-value="fbParams.issuedStatus"
                  placeholder="请选择授权状态"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>授权状态-{{ data?.label }}</span>
                  </template>
                  <a-option :value="3">已解绑</a-option>
                  <a-option :value="1">已授权</a-option>
                  <a-option :value="2">授权失效</a-option>
                  <a-option :value="0">已删除</a-option>
              </a-select>
            </div>
        </div>
        <div v-if="metaType === '2'" class="setting">
            <div class="label">
              筛选
              <!-- <icon-settings class="label-icon"/> -->
            </div>
            <div class="setting-content">
              <a-select
                  v-model:model-value="advertParams.advertAccount"
                  placeholder="请选择广告账户"
                  multiple
                  allow-clear
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;"
                  @popup-visible-change="visible => {if (!visible) advertChange()}">
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-select
                  v-model:model-value="advertParams.userName"
                  placeholder="请选择FB个人号"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>FB个人号-{{ data?.label }}</span>
                  </template>
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-select
                  v-model:model-value="advertParams.bm"
                  placeholder="请选择bm"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                  <span>bm-{{ data?.label }}</span>
                  </template>
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-select
                  v-model:model-value="advertParams.accountType"
                  placeholder="请选择账号状态"
                  multiple
                  allow-clear
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;"
                  @remove="advertChange"
                  @clear="advertChange"
                  @popup-visible-change="v => {if (!v) advertChange()}">
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-select
                  v-model:model-value="advertParams.main"
                  placeholder="请选择主体"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                  <span>主体-{{ data?.label }}</span>
                  </template>
                  <a-option value="1" label="111"></a-option>
                  <a-option value="2" label="222"></a-option>
              </a-select>
              <a-cascader v-model:model-value="advertParams.belongUser" placeholder="所属人员" style="width: 240px;margin-right: 24px;"/>
              <a-select
                  v-model:model-value="advertParams.authStatus"
                  placeholder="请选择授权状态"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>授权状态-{{ data?.label }}</span>
                  </template>
                  <a-option :value="3">已解绑</a-option>
                  <a-option :value="1">已授权</a-option>
                  <a-option :value="2">授权失效</a-option>
                  <a-option :value="0">已删除</a-option>
              </a-select>
            </div>
        </div>
        <a-divider />
        <div v-if="metaType === '2'" class="table-wrap">
            <div class="wrap-left">
                <a-button type="primary" class="br4" style="margin-left: 12px;" :loading="addLoading" @click="addAuth">
                    <template #icon>
                        <icon-plus />
                    </template>
                    <template #default>添加账户</template>
                </a-button>
                <a-dropdown>
                    <a-button class="br4" style="margin-left: 12px;" :disabled="!selectedKeys.length">
                        <template #icon>
                            <icon-tags />
                        </template>
                        <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                    </a-button>
                    <template #content>
                        <a-doption @click="batchEdit">编辑</a-doption>
                        <a-doption>解绑</a-doption>
                        <a-doption>删除</a-doption>
                        <a-doption @click="batchEditFb">修改FB个人号</a-doption>
                    </template>
                </a-dropdown>
            </div>
            <div class="wrap-right">
                <a-button class="br4" @click="updateData">
                    <template #icon>
                        <icon-refresh />
                    </template>
                    <template #default>同步数据</template>
                </a-button>
                <!-- <a-button class="br4" style="margin-left: 12px;" @click="handleColumns">
                    <template #icon>
                        <icon-menu />
                    </template>
                    <template #default>自定义列</template>
                </a-button> -->
            </div>
        </div>
        <div class="table-area">
          <a-table
              v-show="metaType === '1'"
              :columns="fbColumns"
              :loading="fbLoading"
              :data="fbTableData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :table-layout-fixed="true"
              :column-resizable="true"
              :scroll="scroll"
              :pagination="true"
          >
            <template #autoBindHeader>
              <span>新账号自动绑定开关
                <a-tooltip placement="top" content="打开该开关后，除已授权、已解绑或已删除的广告账户外，该媒体用户下所有新广告账户都会被自动绑定">
                  <icon-question-circle style="margin-left: 4px;"/>
                </a-tooltip>
              </span>
            </template>
            <template #issuedStatus="{ record }">
              <a-tag :color="getStatusColor(record.issuedStatus)">
                {{ getStatusText(record.issuedStatus) }}
              </a-tag>
            </template>
            <template #autoBind="{ record }">
                <a-switch v-model="record.autoBind" :checked-value="1" :unchecked-value="0" size="small" :before-change="() => autoBindChange(record)"/>
            </template>
            <template #issuedAt="{ record }">
              {{dayjs(record.issuedAt).format('YYYY-MM-DD HH:MM:ss')}}
            </template>
            <template #action="{ record }">
                <a-tooltip content="只有广告数量为0且开关关闭时可以删除">
                  <span v-if="record.autoBind || record.mediaAccountCount">删除</span>
                </a-tooltip>
                <a-button v-if="!record.autoBind && !record.mediaAccountCount" type="text">删除</a-button>
            </template>
          </a-table>
          <a-table
              v-show="metaType === '2'"
              v-model:selectedKeys="selectedKeys"
              :columns="columns"
              :loading="loading"
              :data="tableData"
              :bordered="false"
              :hoverable="true"
              sticky-header
              :table-layout-fixed="true"
              :column-resizable="true"
              :scroll="scroll"
              :pagination="true"
              :row-selection="rowSelection"
              row-key="accountId"
          >
            <template #authStatus="{ record }">
              <a-tag :color="getStatusColor(record.authStatus)">
                {{ getStatusText(record.authStatus) }}
              </a-tag>
            </template>
            <template #accountStatus="{ record }">
              {{getAccountStatusText(record.accountStatus)}}
            </template>
            <template #createTime="{ record }">
              {{dayjs(record.createTime).format('YYYY-MM-DD HH:MM:ss')}}
            </template>
            <template #action="{ record }">
                <a-button type="text" @click="editItem(record)">编辑</a-button>
                <a-button v-if="record.authStatus === 1" type="text" @click="unBind(record)">解绑</a-button>
                <a-button v-if="record.authStatus === 2 || record.authStatus === 3" type="text" @click="deleteItem(record)">删除</a-button>
            </template>
          </a-table>
          <!-- <div v-show="metaType === '1'" class="pagination">
              <a-pagination :total="fbTotal" show-total @change="fbPageChange"/>
          </div> -->
          <!-- <div v-show="metaType === '2'" class="pagination">
              <a-pagination :total="total" show-total @change="pageChange"/>
          </div> -->
        </div>
        <!-- 自定义列表 -->
        <CustomColumnModal v-model:visible="columnsVisible"/>
        <!-- 编辑广告账户 -->
        <HandleAdvertModal ref="advertRef"/>
        <!-- 批量编辑广告账户 -->
        <BatchEditAdvert ref="batchRef"/>
        <!-- 编辑fb个人号 -->
        <EditFbModal ref="fbRef"/>
        <!-- 操作确认弹窗 -->
        <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
        <!-- 授权账号 -->
        <SelectAuthModal ref="authRef" @update-data="getAdvertData"/>
    </div>
  
  </template>
  
  <script setup lang="ts">
  import { reactive, ref,h,onMounted,nextTick, watch} from "vue";
  import CustomColumnModal from "@/views/launch/material/components/CustomColumnModal.vue";
  import dayjs from 'dayjs';
  import {useSessionStorage} from '@vueuse/core';
  import { useRoute,useRouter} from 'vue-router';
  import {getAuthFbUrl,getAllAuthFbUserList,getAllAuthFbAccountList} from "@/api/setting/api";
  import {Message} from '@arco-design/web-vue';
  import DateRangePicker from "@/components/date-quick-picker/index.vue";
  import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
  import HandleAdvertModal from "./components/HandleAdvertModal.vue";
  import BatchEditAdvert from "./components/BatchEditAdvert.vue";
  import EditFbModal from "./components/EditFbModal.vue";
  import SelectAuthModal from "./components/SelectAuthModal.vue";
  
  const route = useRoute();
  const router = useRouter();
  const metaType = ref('2')
  const facebookUserId = useSessionStorage("facebook-user-id", '')
  const fbPageParams = reactive({
    current: 1,
    pageSize: 10,
  })
  // 查询数据
  const queryParams = reactive({
    current: 1,
    pageSize: 10,
  })
  // 设置facebook筛选数据
  const fbParams = reactive({
    autoBind:undefined,
    userName: '',
    advertAccount:'',
    authMember:'',
    issuedStatus:undefined,
    issuedAt:[]
  })
  // 设置广告账户筛选数据
  const advertParams = reactive({
    advertAccount: '',
    userName:'',
    bm:'',
    main:'',
    accountType:'',
    belongUser:'',
    authStatus:undefined,
  })
  const selectedKeys = ref([]);

  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  const fbTotal = ref(0)
  const total = ref(0)
  // 模拟table数据
  const loading = ref(false)
  const fbLoading = ref(false)
  const fbTableData = ref<any>([])
  const tableData = ref<any>([])
  const fbColumns = ref<any>([
    { title: '新账号自动绑定开关', dataIndex: 'autoBind',slotName: 'autoBind',titleSlotName: `autoBindHeader`, width: 200 },
    { title: 'FB个人号', dataIndex: 'userName', minWidth: 120 },
    { title: '操作', dataIndex: 'action',slotName: 'action',align:'center',width: 180 },
    { title: '已绑定账户', dataIndex: 'mediaAccountCount', minWidth: 180 },
    { title: '授权人', dataIndex: 'authMember', minWidth: 180 },
    { title: '最近授权时间', dataIndex: 'issuedAt',slotName: 'issuedAt', width: 180 },
    { title: '授权状态', dataIndex: 'issuedStatus',slotName: 'issuedStatus', minWidth: 120 },
    { title: '授权失效原因', dataIndex: 'deletedReason',slotName: 'deletedReason', minWidth: 180,ellipsis: true,tooltip: true },
  ])
  const columns = ref<any>([
    { title: '广告账户ID', dataIndex: 'accountId', width: 200, fixed: 'left',ellipsis: true,tooltip: true, },
    { title: '广告账户名称', dataIndex: 'accountName', width: 200, fixed: 'left',ellipsis: true,tooltip: true, },
    { title: 'BM', dataIndex: 'bm', width: 200, fixed: 'left',ellipsis: true,tooltip: true, },
    { title: '操作', dataIndex: 'action',slotName: 'action',align:'center',width: 180 },
    { title: 'FB个人号数量', dataIndex: 'fbAccountCount', minWidth: 120 },
    { title: 'FB个人号', dataIndex: 'userName', minWidth: 120 },
    { title: '授权状态', dataIndex: 'authStatus',slotName: 'authStatus', minWidth: 120 },
    { title: '账号状态', dataIndex: 'accountStatus',slotName: 'accountStatus', minWidth: 120 },
    { title: '所属人员', dataIndex: 'owner', minWidth: 120 },
    { title: '协助人员', dataIndex: 'assistant', minWidth: 120 },
    { title: '授权时间', dataIndex: 'createTime',slotName: 'createTime', width: 180 }
  ])
  
  // Table页面滑动
  const scroll = {
    y:'100%'
  };
  // 自定义列
  const columnsVisible = ref(false)
  const handleColumns = () => {
    columnsVisible.value = true
  }
  const getStatusColor = (status: string) => {
    const colorMap = {
      0: 'red',      // 已删除
      1: 'green',    // 已授权
      2: 'orange',   // 授权失效
      3: 'gray'      // 已解绑
    };
    return colorMap[status] || 'gray';
  };
  const getStatusText = (status: string) => {
    const statusTextMap = {
      0: '已删除',
      1: '已授权',
      2: '授权失效',
      3: '已解绑'
    };
    return statusTextMap[status] || '未知状态';
  };
  const getAccountStatusText = (status) => {
    const statusMap = {
      1: 'ACTIVE',
      2: 'DISABLED',
      3: 'UNSETTLED',
      7: 'PENDING_RISK_REVIEW',
      8: 'PENDING_SETTLEMENT',
      9: 'IN_GRACE_PERIOD',
      100: 'PENDING_CLOSURE',
      101: 'CLOSED',
      201: 'ANY_ACTIVE',
      202: 'ANY_CLOSED'
    };
    return statusMap[status] || 'UNKNOWN_STATUS';
  };
  const dateChange = (val) => {
    console.log(val)
  }
  const advertChange = () => {
    // console.log(v)
  }
  // 自动绑定开关
  const autoBindChange = (item:any) => {
    console.log(item)
  }
  // fb删除
  const advertRef = ref()
  // 广告账户编辑
  const editItem = (item:any) => {
    console.log(item)
    advertRef.value.openModal(item)
  }
  // 批量编辑
  const batchRef = ref()
  const batchEdit = () => {
    batchRef.value.openModal()
  }
  // 批量修改FB个人号
  const fbRef = ref()
  const batchEditFb = () => {
    fbRef.value.openModal()
  }
  const confirmVisible = ref(false)
  const confirmType = ref('')
  // 广告账户解绑
  const unBind = (item:any) => {
    confirmType.value = 'unbind'
    confirmVisible.value = true
  }
  // 广告账户删除 
  const deleteItem = (item:any) => {
    confirmType.value = 'delete'
    confirmVisible.value = true
  }
  // 获取facebook数据
  const getFbData = async () => {
    try {
      fbLoading.value = true
      const res = await getAllAuthFbUserList()
      fbTableData.value = res
    }catch (e) {
      console.error('获取数据失败', e)
    }finally {
      fbLoading.value = false
    }
  }
  // 获取广告账户数据
  const getAdvertData = async () => {
    try {
      loading.value = true
      const res = await getAllAuthFbAccountList()
      tableData.value = res
    }catch (e) {
      console.error('获取数据失败', e)
    }finally {
      loading.value = false
    }
  }
  // 同步数据
  const updateData = async () => {
    await getAdvertData()
    Message.success('数据同步成功')
  }
  const metaTypeChange = (v:any) => {
    if(v === '1'){
      fbPageParams.current = 1
      getFbData()
    }
  }
  // 添加授权
  const addLoading = ref(false)
  const authRef = ref()
  const addAuth = async () => {
    try {
      addLoading.value = true
      const authUrl = await getAuthFbUrl()
      window.open(authUrl, '_blank')
    }catch (e) {
      console.log(e)
    }finally {
      addLoading.value = false
    }
  }
  const init = () => {
    getAdvertData()
  }
  init()
  // 分页
  const fbPageChange = (v) => {
    fbPageParams.current = v
    getFbData()
  }
  const pageChange = (v) => {
    queryParams.current = v
    getAdvertData()
  }
  onMounted(() => {
    // 监听路由参数，处理 Facebook 重定向
    if (route.query.from === 'facebook') {
      facebookUserId.value = route.query.facebook_user_id as string;
      router.replace({
        path: route.path
      })
    }
  })
  watch(() => facebookUserId.value, (newVal) => {
    if (newVal) {
        nextTick(() => {
          authRef.value.openModal()
        });
    }
  },{immediate: true,deep:true})
  </script>
  
  <style scoped lang="less">
  .pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
  }
  .table-area {
    flex: 1 1 0%;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
  .tab{
    display: flex;
    margin-bottom: 16px;
    align-items: center;
    .title{
        width: 60px;
        margin-right: 10px;
    }
  }
  .setting{
    display: flex;
    .setting-content{
      flex-wrap: wrap;
      display: flex;
      row-gap: 16px;
    }
    .label{
      flex-shrink: 0;
      width: 60px;
      margin-right: 10px;
    }
    .label-icon{
      margin-left: 4px;
      cursor: pointer;
      &:hover{
        color: rgb(var(--primary-6));
      }
    }
  }
  .table-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .br4{
    border-radius: 4px;
  }
  </style>