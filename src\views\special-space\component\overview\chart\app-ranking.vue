<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {onMounted, ref, watch} from "vue";

interface Props {

  /**
   * 分组
   */
  group: string[]

  /**
   * 数据
   */
  data: number[][]

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()

const option = ref<any>();

const refresh = () => {
  const colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#235894']
  const nameStyle = {}
  colorList.forEach((color, i) => {
    nameStyle[`name${i}`] = {
      color,
      padding: [0, 4, 0, 6],
      fontSize: 14,
      fontWeight: 900,
      lineHeight: 33
    }
  })
  const xData = props.group || []
  
  const yData = props.data?.map(item => {
    const totalArray = item.total;
    if (!totalArray || totalArray.length === 0) {
      return 0;
    }
    // 取最后一个索引的值
    return totalArray[totalArray.length - 1] || 0;
  }) || [];
  
  const {chartOption} = useChartOption(() => {
    return {
      baseOption: {
        grid: {
          left: '36',
          right: '36',
          top: '16',
          bottom: '118'
        },
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const {value, dataIndex} = params;
            const diffRateArray =  props.data[dataIndex].totalDiffRate;
            const diffRate = diffRateArray && diffRateArray.length > 0 
              ? diffRateArray[diffRateArray.length - 1] 
              : 0;
            const diffRateStr = diffRate < 0 ? `下降 ${-diffRate}%` : `上涨 ${diffRate}%`
            return `${props.group[dataIndex]}<br/>${props.dataName}: ${value}<br/>日环比: ${diffRateStr}<br/>`;
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter(value: number) {
              if (value >= 1000000) {
                return `${value / 1000000}M`; // 转换为 k 单位
              }
              if (value >= 1000) {
                return `${value / 1000}k`; // 转换为 k 单位
              }
              return value;
            }
          }
        },
        xAxis: {
          type: 'category',
          data: xData.slice(0, 10).map((item, i) => {
            if (item?.length > 16) {
              item = item.slice(0, 13) + '...';
            }
            return `${item}`;
          }),
          axisLabel: {
            interval: 0, // 设置为0确保所有的标签都会显示
            align: 'center', // 调整文字对齐方式
            rotate: 30,  // 标签倾斜的角度（比如45度）
            margin: 36   // 调整与轴线的距离，单位是像素
          }
        },
        timeline: {
          axisType: 'category',
          realtime: true,
          loop: true,
          left: 0,
          right: 36,
          autoPlay: true,
          playInterval: 15000,
          data: ['前十名', '第11~20名', '第21~30名', '第31~40名', '第41~50名'],
          lineStyle: {
            type: 'dashed'
          },
          controlStyle: {
            showPlayBtn: true,
            showPrevBtn: false,
            showNextBtn: false,
            itemGap: 30
          }
        },
        series: [
          {
            name: 'Direct',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'top',
              formatter(params) {
                if (!params.value || typeof params.value !== 'number') {
                  return params.value;
                }
                if (params.value >= 1000000) {
                  return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
                }
                if (params.value >= 1000) {
                  return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
                }
                return Math.round(params.value); // 保留整数
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: yData.slice(0, 10)
          }
        ]
      },
      options: [
        {
          xAxis: {
            data: xData.slice(0, 10).map((item, i) => {
              if (item?.length > 16) {
                item = item.slice(0, 13) + '...';
              }
              return `${item}`;
            })
          },
          series: [
            {
              data: yData.slice(0, 10)
            }
          ]
        },
        {
          xAxis: {
            data: xData.slice(10, 20).map((item, i) => {
              if (item?.length > 16) {
                item = item.slice(0, 13) + '...';
              }
              return `${item}`;
            })
          },
          series: [
            {
              data: yData.slice(10, 20)
            }
          ]
        },
        {
          xAxis: {
            data: xData.slice(20, 30).map((item, i) => {
              if (item?.length > 16) {
                item = item.slice(0, 13) + '...';
              }
              return `${item}`;
            })
          },
          series: [
            {
              data: yData.slice(20, 30)
            }
          ]
        },
        {
          xAxis: {
            data: xData.slice(30, 40).map((item, i) => {
             if (item?.length > 16) {
                item = item.slice(0, 13) + '...';
              }
              return `${item}`;
            })
          },
          series: [
            {
              data: yData.slice(30, 40)
            }
          ]
        },
        {
          xAxis: {
            data: xData.slice(40, 50).map((item, i) => {
              if (item?.length > 16) {
                item = item.slice(0, 13) + '...';
              }
              return `${item}`;
            })
          },
          series: [
            {
              data: yData.slice(40, 50)
            }
          ]
        }
      ]
    };
  });
  option.value = chartOption.value
}

watch(props, () => {
  refresh()
})

onMounted(() => {
  refresh()
})

</script>

<template>
  <Chart :option="option"/>
</template>

<style scoped lang="less">


</style>