<template>
  <div class="card-content">
    <div class="card-toolbar">
      <div class="card-filter">
        <dateSet :time-particle="timeParticle" :no-extend="true" :show-border="false" @time-change="timeChange"/>
        <a-divider :margin="8" direction="vertical"/>
        <a-dropdown :update-at-scroll="true" @select="handleSelect">
          <span v-if="groupsDesc.length>0 && selectValue !== 'table'" style="line-height: 24px;cursor: pointer;">{{ groupName }}</span>
          <template #content>
            <div style="min-width: 100px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
              <a-input v-model:model-value="groupsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                <template #prefix>
                  <icon-search/>
                </template>
              </a-input>
            </div>
            <a-doption v-for="(item,index) in filteredGroups" :key="index" :value="item" style="min-width: 160px;">{{ item }}</a-doption>
          </template>
        </a-dropdown>
        <a-divider v-if="!isTableView && groupsDesc.length>0" :margin="8" direction="vertical"/>
        <date-picker :date-range="boardDate" position="tl" @date-pick="datePick"/>
      </div>
      <div class="card-config">
        <a-tooltip class="show-chart-label" :content="secondaryIndicatorOnly ? '取消只看同时展示数据':'点击只看同时展示数据' ">
          <icon-paste v-if="secondaryList.length" class="icon" style="cursor: pointer;margin-right: 12px;" @click="clikOnlyChange"/>
        </a-tooltip>
        <a-tooltip class="show-chart-label" :content="showLabel ? '隐藏数值':'显示数值' ">
          <div v-if="!showLabel && !isTableView" class="operation-icon" @click="()=>{showLabel=!showLabel}">
            <icon-eye-invisible class="icon"/>
          </div>
          <div v-if="showLabel && !isTableView" class="operation-icon" @click="()=>{showLabel=!showLabel}">
            <icon-eye class="icon"/>
          </div>
        </a-tooltip>
        <a-select
            id="select"
            v-model="selectValue"
            :bordered="false"
            :style="{width:'120px',padding:'0 0 0 13px',position:'relative'}"
            default-value="trend-chart"
            @change="handleChange"
        >
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/'+data?.value+'-chart.svg'" alt=""/>
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon>
          </template>
          <a-popover v-if="secondaryIndicatorOnly" title="趋势图" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option :value="ChartType.TREND">
              <template #icon>
                <img class="option-icon" src="/icon/trend-chart.svg" alt=""/>
              </template>
              <template #default>趋势图</template>
            </a-option>
            <template #content>
              展示“同时展示指标”在各个区间的变化趋势 <br>
              <img :src="trend" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover v-if="!secondaryIndicatorOnly" title="数值分布" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option value="stacked">
              <template #icon>
                <img class="option-icon" src="/icon/stack-chart.svg" alt=""/>
              </template>
              <template #default>数值分布</template>
            </a-option>
            <template #content>
              展示各区间人数的变化趋势 <br>
              <img :src="stacked" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover v-if="!secondaryIndicatorOnly" title="百分比分布" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option value="stackedRate">
              <template #icon>
                <img class="option-icon" src="/icon/stack-chart.svg" alt=""/>
              </template>
              <template #default>百分比分布</template>
            </a-option>
            <template #content>
              展示各区间人数占比的变化趋势 <br>
              <img :src="stacked" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="表格" position="left" :content-style="{width: '260px'}" popup-container="#select">
            <a-option :value="ChartType.TABLE">
              <template #icon>
                <img class="option-icon" src="/icon/table-chart.svg" alt=""/>
              </template>
              <template #default>表格</template>
            </a-option>
            <template #content>
              <div>
                <p>展示各个区间的分布详情</p>
                <img :src="table" alt="" style="width: 90%;height: 90%;"/>
              </div>
            </template>
          </a-popover>
        </a-select>
      </div>
    </div>
    <a-alert v-if="reportData?.resultsExceedsLimit" style="margin-top: 5px;flex-shrink: 0;">
      因数据条数过多，优先展示前1000条数据
    </a-alert>
    <div class="data-chart" :style="{height: chartHeight}">
      <div class="chart-box">
        <TableView v-show="isTableView" ref="tableRef" :size="254" :secondary-indicator-only="secondaryIndicatorOnly" :report-analysis-data="reportData"/>
        <ChartView v-show="!isTableView" ref="chartRef" :show-label="showLabel" :select-group-name="groupName" :chart-type="selectValue" :report-analysis-data="reportData"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watch} from "vue";
import {stacked, table, trend} from '@/views/dashboard/components/img';
import {ReportQueryResponse} from "@/api/type";
import {ChartType, TimeParticleSize} from "@/api/enum";
import DatePicker from "@/components/date-picker/index.vue";
import useReportDataStore from "@/store/modules/report";
import ChartView from "@/views/dashboard/components/table-chart/scatter/ScatterChart.vue";
import TableView from "@/views/dashboard/components/table-chart/scatter/ScatterTable.vue";
import dateSet from "@/views/analyse/components/dateSet.vue";
import {DateRange} from "@/api/analyse/type";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  report: any;
  name: string
  dataRangeData: any
}

// css
const props = defineProps<Props>();

const chartRef = ref()
const reportData = ref<any>();
const emits = defineEmits(['changeParams', 'update:modelValue', 'dateChange', 'queryParamsChange']);
// 报表状态
const reportDataStore = useReportDataStore()

const timeParticle = ref({
  timeParticleSize: TimeParticleSize.DAY1,
  firstDayDfWeek: null
})
const timeChange = (v) => {
  timeParticle.value = v
  emits('queryParamsChange', {timeParticleSize: timeParticle.value.timeParticleSize, firstDayDfWeek: timeParticle.value.firstDayDfWeek})
}
// 监听报表状态
watch(() => reportDataStore, (newVal) => {
  if (!props.report.objectCode) {
    return
  }
  const newValues = {
    timeParticleSize: reportDataStore.getTimeParticleSizes(props.report.objectCode) || TimeParticleSize.DAY1, // 默认值
    firstDayDfWeek: reportDataStore.getFirstDayDfWeeks(props.report.objectCode) // 允许为null
  }
  timeParticle.value = {...timeParticle.value, ...newValues}
}, {immediate: true, deep: true})
const selectValue = ref<ChartType>(ChartType.TABLE);
const boardDate = ref<DateRange>({...props.dataRangeData});
// 添加对 dataRangeData 的监听
watch(() => props.dataRangeData, (newVal) => {
  boardDate.value = newVal
}, {deep: true})

const groupName = ref('总体')
const groupsData = ref<any>([])
const groupsDesc = ref<any>([])
const handleSelect = (v) => {
  groupName.value = v
}
const secondaryIndicatorOnly = ref<boolean>(false);
const showLabel = ref<boolean>(false);
const isTableView = ref<boolean>(false)
const secondaryList = ref<any>([])

const handleChange = (value: any) => {
  isTableView.value = value === ChartType.TABLE
  emits('changeParams', {chartType: ChartType.TABLE})
  // switch (value) {
  //   case ChartType.TABLE:
  //     if (isTableView.value) {
  //       emits('changeParams', {chartType: ChartType.TABLE})
  //     }
  //     break;
  //   case ChartType.TREND:
  //     if (!isTableView.value) {
  //       emits('changeParams', {chartType: ChartType.TREND})
  //     }
  //     break;
  //   case 'distribution':
  //     if (!isTableView.value) {
  //       emits('changeParams', {chartType: 'distribution'})
  //     }
  //     break;
  //   case 'stacked':
  //     if (!isTableView.value) {
  //       emits('changeParams', {chartType: 'stacked'})
  //     }
  //     break;
  //   case 'stackedRate':
  //     if (!isTableView.value) {
  //       emits('changeParams', {chartType: 'stackedRate'})
  //     }
  //     break;
  //   case 'zfTrend':
  //     if (!isTableView.value) {
  //       emits('changeParams', {chartType: 'zfTrend'})
  //     }
  //     break;
  //   default:
  //     break;
  // }
};
const clikOnlyChange = () => {
  secondaryIndicatorOnly.value = !secondaryIndicatorOnly.value
  selectValue.value = ChartType.TABLE
  handleChange('table')
}
const transformData = (data: any) => {
  selectValue.value = data
  handleChange(data)
}

watch(() => props.reportAnalysisData, (newData) => {
  if (newData === undefined) {
    return
  }
  const result = newData?.result;
  reportData.value = result;
  timeParticle.value = result?.timeParticleSize
  groupsData.value = result?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }).filter(item => item !== '总体') || [];
  groupsDesc.value = result?.groupsDesc || []
  secondaryList.value = result?.scatterQueryResult?.[1] || []
})
const groupsInput = ref('')
const filteredGroups = computed(() => {
  // 将总体添加到分组列表的开头
  const list = ['总体', ...groupsData.value]
  const str = groupsInput.value.trim()
  return list.filter(item => item.includes(str));
})
const datePick = (date: any) => {
  boardDate.value = date
  reportDataStore.setDateRanges(props.report.objectCode, date);
  emits('dateChange', boardDate.value)
};
const init = () => {
  transformData(props.report.configParams?.chartType || ChartType.TABLE)
}

init()

const tableRef = ref()
const exportXlsx = (date: any, name?: string) => {
  tableRef.value?.exportXlsx(date, name)
}
// 图标高度
const chartHeight = computed(() => reportData.value?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)')
defineExpose({
  exportXlsx
})
</script>

<style scoped lang="less">
@import "@/views/dashboard/style/card.less";
</style>
