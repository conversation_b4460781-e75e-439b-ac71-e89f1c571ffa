import {defineStore} from 'pinia';
import {getUserProfile, login as userLogin, LoginData, logout as userLogout, logout, phoneLogin} from '@/api/authorize/api';
import {clearToken, setToken} from '@/utils/auth';
import {removeRouteListener} from '@/utils/route-listener';
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {LogEventBus, LoginEvent} from "@/types/event-bus";
import {getAppPageList} from "@/api/marketing/api";
import {UserState} from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    name: undefined,
    code: undefined,
    avatar: undefined,
    accountNo: undefined,
    gender: undefined,
    email: undefined,
    mobile: undefined,
    note: undefined,
    group: undefined,
    role: undefined,
    nickname: undefined,
    // organization: undefined,
    // location: undefined,
    // email: undefined,
    // introduction: undefined,
    // personalWebsite: undefined,
    // jobName: undefined,
    // organizationName: undefined,
    // locationName: undefined,
    // phone: undefined,
    // registrationDate: undefined,
    // accountId: undefined,
    // certification: undefined,
  }),

  getters: {
    userInfo(state: UserState): UserState {
      return {...state};
    },
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.role = this.role === 'user' ? 'admin' : 'user';
        resolve(this.role);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {
      const res = await getUserProfile();

      this.setInfo(res);
    },
    async refreshAppData() {
      const appId = useSessionStorage("app-id", "");
      const appIdList = useSessionStorage("app-id-list", []);
      const appData = useSessionStorage("app-data", {});
      const appDataList = useSessionStorage("app-data-list", []);
      if (appId?.value?.length <= 0 || appIdList?.value?.length <= 0) {
        try {
          const res = await getAppPageList({
            current: 1,
            pageSize: 1
          });
          appData.value = res?.items?.[0];
          appId.value = res?.items?.[0]?.code;
          appDataList.value = [];
          appIdList.value = [];
        } catch (error) {
          console.log(error);
        }
      }
    },
    initDataSource() {
      const dataSource = useSessionStorage("data-source", '');
      dataSource.value = 'event'
    },
    clearAppData() {
      const appId = useSessionStorage("app-id", "");
      const appIdList = useSessionStorage("app-id-list", []);
      const appData = useSessionStorage("app-data", {});
      const appDataList = useSessionStorage("app-data-list", []);
      const dataSource = useSessionStorage("data-source", '');
      const menuList = useSessionStorage("menu-list", '');
      appId.value = null
      appIdList.value = null
      appData.value = null
      appDataList.value = null
      dataSource.value = null
      menuList.value = null
    },
    // Login
    async login(loginForm: LoginData) {
      try {
        const res = await userLogin(loginForm.email, loginForm.password);
        setToken(res);
        useEventBus(LogEventBus).emit(LoginEvent);
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    async phoneLogin(params: any) {
      try {
        const res = await phoneLogin(params);
        setToken(res);
        useEventBus(LogEventBus).emit(LoginEvent);
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    async ddLogin(params: any) {
      try {
        setToken(params);
        useEventBus(LogEventBus).emit(LoginEvent);
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      removeRouteListener();
      appStore.clearServerMenu();
      useEventBus(LogEventBus).emit(logout());
    },
    // Logout
    async logout() {
      try {
        userLogout();
        this.clearAppData();
      } finally {
        this.logoutCallBack();
      }
    },
  },
});

export default useUserStore;
