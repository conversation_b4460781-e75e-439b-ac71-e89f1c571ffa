<template>
    <a-drawer :width="600" :visible="visible" :footer="false" unmountOnClose @cancel="handleCancel">
        <template #title>
            {{ visibleTitle }}
        </template>
        <a-spin :loading="dataLoading" class="content" style="width: 100%;">
            <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="border-bottom: 1px solid var(--color-neutral-3);">
                <div class="types">
                    <a-form-item field="name" label="参数名称（键）" :hide-asterisk="true" tooltip="这是您要传递至 Remote Config SDK 的键，例如：config.getBoolean(holiday_promo_enabled)">
                        <a-input v-model="form.name" :disabled="visibleType === 'edit'"/>
                    </a-form-item>
                    <a-form-item field="valueType" label="数据类型" style="width: 133px;margin-left: 12px;" class="non-shrink-item">
                        <a-select v-model:model-value="form.valueType" @change="dataTypeChange">
                            <template #label="{ data }">
                                <div class="option-item">
                                    <svg height="24px" viewBox="0 0 24 24" width="24px" fit="" preserveAspectRatio="xMidYMid meet" focusable="false">
                                        <path :d="form.svgPath" fill-rule="evenodd"></path>
                                    </svg>
                                    {{data?.label}}
                                </div>
                            </template>
                            <a-option v-for="(item,index) in dataTypeList" :key="index" :value="item.value">
                                <div class="option-item">
                                    <svg height="24px" viewBox="0 0 24 24" width="24px" fit="" preserveAspectRatio="xMidYMid meet" focusable="false">
                                        <path :d="item.svgPath" fill-rule="evenodd"></path>
                                    </svg>
                                    <span>{{ item.label }}</span>
                                </div>
                            </a-option>
                        </a-select>
                    </a-form-item>
                </div>
                <div class="logic">
                    <div class="line" style="flex-grow: 1;"></div>
                    <a-form-item field="note" label="Description">
                        <a-input v-model="form.note" placeholder="可选"/>
                    </a-form-item>
                </div>
                <div v-if="form.conditionsItem.length>0" class="handle-condition-box">
                    <span class="label-word">条件</span>
                    <draggable 
                        v-model="form.conditionsItem" 
                        handle=".drag-handle"
                        item-key="code"
                        animation="200"
                        ghost-class="ghost-item"
                    >
                        <template #item="{element, index}">
                            <div class="handle-content">
                                <div class="handle-header">
                                    <div class="drag-handle">
                                        <icon-drag-dot-vertical style="cursor: grab; margin-right: 8px; color: var(--color-text-3);" />
                                    </div>
                                    <a-select v-model="element.name" :disabled="!element.editable" :style="{width:'220px',background: element.editable ? element.color : '',borderRadius:'16px'}" @change="itemChange($event,index)">
                                        <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                                            <a-input v-model="conditionInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                                                <template #prefix>
                                                <icon-search />
                                                </template>
                                            </a-input>
                                        </div>
                                        <div class="create-condition" @click="createCondition"><icon-plus style="margin-right: 12px;"/>创建条件</div>
                                        <a-option v-for="(el,elIndex) in filteredConditionList" :key="elIndex" :value="el.name">{{ el.name }}</a-option>
                                    </a-select>
                                    <a-tooltip content="修改" position="bottom">
                                        <div class="handle-btn" :style="{ marginLeft: '8px', cursor: element.editable ? 'pointer' : 'default'}" @click="editCondition(element)">
                                            <icon-edit size="24"/>
                                        </div>
                                    </a-tooltip>
                                    <a-tooltip content="删除" position="bottom">
                                        <div v-if="element.editable" class="handle-btn" style="cursor: pointer;" @click="() => element.editable = false">
                                            <icon-close-circle size="24"/>
                                        </div>
                                    </a-tooltip>
                                    <a-tooltip content="恢复删除" position="bottom">
                                        <div v-if="!element.editable" class="handle-btn" style="cursor: pointer;" @click="() => element.editable = true">
                                            <icon-plus-circle size="24"/>
                                        </div>
                                    </a-tooltip>
                                </div>
                                <div class="handle-item">
                                    <div class="line"></div>
                                    <div class="default" style="flex-grow: 1;">
                                        <div class="use-default">
                                            <a-switch v-model="element.isDefault" :checked-value="true" :unchecked-value="false" type="line" style="margin-right: 8px;" @change="isDefaultItemChange(index)"/>
                                            <span>使用应用内默认值</span>
                                        </div>
                                        <a-form-item
                                            :field="`defaultValue_${index}`"
                                            label="value"
                                            style="position: relative;"
                                            validate-trigger="blur"
                                            :rules="[
                                                {
                                                    validator: (value, cb) => {
                                                        if(element.isDefault){
                                                            cb()
                                                            return
                                                        }
                                                        if((form.valueType === 'int' ||form.valueType === 'float') && !isConvertibleToNumber(element.defaultValue)){
                                                            cb('数值无效')
                                                        }
                                                        if (form.valueType === 'boolean' && 
                                                            element.defaultValue !== 'true' && 
                                                            element.defaultValue !== 'false') {
                                                            cb('布尔值无效')
                                                        } else {
                                                            cb()
                                                        }
                                                        if (form.valueType === 'json' && value && !isValidJson(value)) {
                                                            cb('无效的 JSON 格式');
                                                        } else {
                                                            cb()
                                                        }
                                                    }
                                                }
                                            ]">
                                            <a-input v-if="form.valueType !== 'boolean' || element.isDefault" v-model="element.defaultValue" :disabled="element.isDefault || !element.editable" :placeholder="element.isDefault ? '应用内默认值' : ''"/>
                                            <a-select v-if="form.valueType === 'boolean' && !element.isDefault" v-model="element.defaultValue" :disabled="!element.editable" style="width: 133px;">
                                                <a-option value="true">true</a-option>
                                                <a-option value="false">false</a-option>
                                            </a-select>
                                            <div v-if="form.valueType === 'json' && element.editable" class="globalEdit" @click="jsonModal(element.defaultValue,element.code)">
                                                <a-tooltip content="全局编辑器">
                                                    <icon-expand />
                                                </a-tooltip>
                                            </div>
                                        </a-form-item>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>
                <div class="default">
                    <div class="use-default">
                        <a-switch v-model="form.isDefault" :checked-value="true" :unchecked-value="false" type="line" style="margin-right: 8px;" @change="isDefaultChange"/>
                        <span>使用应用内默认值</span>
                    </div>
                    <a-form-item field="defaultValue" label="Default value" style="position: relative;" validate-trigger="blur">
                        <a-input
                            v-if="form.valueType !== 'boolean' || form.isDefault"
                            v-model="form.defaultValue"
                            :disabled="form.isDefault"
                            :placeholder="form.isDefault ? '应用内默认值' : ''"/>
                        <a-select
                            v-if="form.valueType === 'boolean' && !form.isDefault"
                            v-model:model-value="form.defaultValue"
                            style="width: 133px;">
                            <a-option value="true">true</a-option>
                            <a-option value="false">false</a-option>
                        </a-select>
                        <div v-if="form.valueType === 'json'" class="globalEdit" @click="jsonModal(form.defaultValue,'default')">
                            <a-tooltip content="全局编辑器">
                                <icon-expand />
                            </a-tooltip>
                        </div>
                    </a-form-item>
                </div>
            </a-form>
            <div class="actions">
                <a-dropdown v-model:popup-visible="popupVisible" position="bl" :popup-offset="-40">
                    <a-button type="outline" shape="round">
                        <template #icon>
                            <icon-plus />
                        </template>
                        <template #default>新增<icon-caret-down /></template>
                    </a-button>
                    <template #content>
                        <a-dropdown position="br" :popup-translate="[220, -100]" :hide-on-select="false" :update-at-scroll="true" trigger="hover" @select="conditionSelect">
                            <a-doption>
                                <div class="new-add-item">
                                    <div class="item-info">
                                        <div class="item-icon">
                                            <icon-align-left />
                                            条件值
                                        </div>
                                        <div class="item-word">向应用实例的目标组发送值。</div>
                                    </div>
                                    <div class="item-right">
                                        <icon-caret-right />
                                    </div>
                                </div>
                            </a-doption>
                            <template #content>
                                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                                    <a-input v-model="conditionInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                                        <template #prefix>
                                        <icon-search />
                                        </template>
                                    </a-input>
                                </div>
                                <a-doption value="create"><icon-plus style="margin-right: 12px;"/>创建条件</a-doption>
                                <a-doption v-for="(item,index) in filteredConditionList" :key="index" @click="selectConditionItem(item)">{{ item.name }}</a-doption>
                            </template>
                        </a-dropdown>
                        
                    </template>
                </a-dropdown>
                <div class="right">
                    <a-button style="margin-right: 10px;" class="cancel" @click="handleCancel">取消</a-button>
                    <a-button type="primary" :loading="saveLoading" @click="saveParams">
                        保存
                    </a-button>
                </div>
            </div>
        </a-spin>
        <a-modal v-model:visible="modalVisible" :width="1000" title-align="start" :hide-title="true" :footer="false" modal-class="json-modal" body-style="padding:0;">
            <div class="modal-title" :style="!editAble ? 'background: rgb(78, 78, 224)' : ''">
                <div v-if="editAble" class="title">
                    <span class="word">JSON编辑器</span>
                </div>
                <div v-else class="modal-action-content">
                    <span>尚未保存的更改</span>
                    <span class="division"></span>
                    <a-button class="btn" :disabled="errors>0" @click="configSave">保存</a-button>
                    <a-button class="cancel" @click="handleModalCancel">舍弃</a-button>
                </div>
                <icon-close class="close-icon" @click="handleModalCancel"/>
            </div>
            <div>
                <a-alert v-if="!errors" type="success" style="height: 48px;">JSON有效</a-alert>
                <a-alert v-else type="warning" style="height: 48px;">JSON无效</a-alert>
                <json-editor-vue v-model="config" class="editor" style="height: 600px; position: relative" @update="updataModel" @validationError="editError"/>
            </div>
        </a-modal>
        <createConditionModal ref="conditionModalRef" :condition-param="form.name" condition-scope="app_param_specific" :condition-list="conditionList" @condition-change="conditionChange"/>
    </a-drawer>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import JsonEditorVue from 'json-editor-vue3'
import {getConditionList, getConfigParamList, saveRemoteConfig} from "@/api/marketing/api"
import {Message} from '@arco-design/web-vue';
import draggable from 'vuedraggable'
import createConditionModal from "./createConditionModal.vue";

const formRef = ref()

const props = defineProps({
    appId:{
        type:String,
        default:''
    },
    paramsList:{
        type:Array,
        default:() => []
    }
})
const emits = defineEmits(['updateData'])
const visible = ref(false)
const modalVisible = ref(false)
const visibleTitle = ref('')
const visibleType = ref('')
interface FormItem {
  isDefault: boolean;
  editable:boolean;
  defaultValue: string
  name:string
  color:string
  code?:string
  rules:object
}
const form = reactive({
    name: '',
    valueType: 'string',
    note: '',
    svgPath:'',
    defaultValue:'',
    isDefault:false,
    conditionsItem:[] as FormItem[]
})

const isConvertibleToNumber = (value: any): boolean => {
  // 只允许数字类型和字符串类型
  if (typeof value !== 'number' && typeof value !== 'string') {
    return false;
  }

  // 如果是数字类型，检查是否为有效数字
  if (typeof value === 'number') {
    return !Number.isNaN(value) && Number.isFinite(value);
  }

  // 如果是字符串，去除空格后检查
  const trimmed = value.trim();
  if (trimmed === '') {
    return false;
  }

  // 检查是否为有效的数字格式
  // 允许整数、小数、负数、科学计数法
  const numberRegex = /^-?\d*\.?\d+(?:[eE][-+]?\d+)?$/;
  // 如果 valueType 是 'int'，则不允许小数
  if (form.valueType === 'int') {
    const integerRegex = /^-?\d+$/; // 只允许整数
    return integerRegex.test(trimmed);
  }
  return numberRegex.test(trimmed);
};
const copyAbleValid = (name) => {
    if(visibleType.value !== 'edit'){
        return props.paramsList.findIndex(item => item.name === name) === -1
    }
    return true
}
const isValidJson = (str) => {
  if (typeof str !== 'string') return false;
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}
const rules = {
    name: [
        {
            required: true,
            message:'必须提供参数名称',
        },
        {
            validator: (value: string, cb: (error?: string) => void) => {
                const regex = /^[A-Za-z_][A-Za-z0-9_]*$/;
                if (!regex.test(value)) {
                    cb('您的参数键包含无效字符。参数键必须以下划线或英文字母 [A-Z、a-z] 开头，可以包含数字。');
                }else if(!copyAbleValid(value)){
                    cb('参数键必须是唯一的')
                }else {
                    cb();
                }
            },
            trigger: ['change', 'blur'],
        }
    ],
    defaultValue: [
        {
          validator: (value, cb) => {
            if(form.isDefault){
                cb()
                return
            }
            if((form.valueType === 'int' ||form.valueType === 'float') && !isConvertibleToNumber(value)){
                cb('数值无效')
            }
            if (form.valueType === 'boolean' && value !== 'true' && value !== 'false') {
              cb('布尔值无效')
            } else {
              cb()
            }
            if (form.valueType === 'json' && value && !isValidJson(value)) {
                cb('无效的 JSON 格式');
            } else {
              cb()
            }
          }
        }
    ],
}
const conditionInput = ref('')
const conditionList = ref<any>([])
const filteredConditionList = computed(() => {
  const str = conditionInput.value.trim();
  const existingNames = form.conditionsItem.filter(item => item.editable).map(item => item.name);
  return conditionList.value.filter(item => item.name.includes(str) && !existingNames.includes(item.name));
});

const conditionModalRef = ref()
const popupVisible = ref(false)

const createCondition = () => {
  // 1. 同步验证必填
  if (!form.name?.trim()) {
    Message.warning('请先填写参数名称');
    return;
  }
  if (!/^[A-Za-z_][A-Za-z0-9_]*$/.test(form.name)) {
    Message.warning('参数名称只能包含字母、数字和下划线，且以字母开头');
    return;
  }
  // 3. 所有验证通过才打开弹窗
  conditionModalRef.value.openModal();
};

const conditionSelect = (v: string) => {
  if (v !== 'create') return;

  // 同步验证
  if (!form.name?.trim()) {
    Message.warning('请先填写参数名称');
    popupVisible.value = false;
    return;
  }

  if (!/^[A-Za-z_][A-Za-z0-9_]*$/.test(form.name)) {
    Message.warning('参数名称只能包含字母、数字和下划线，且以字母开头');
    popupVisible.value = false;
    return;
  }

  conditionModalRef.value.openModal();
  popupVisible.value = false;
};

const dataTypeList = ref<any>([
    {
        value:'string',
        label:'字符串',
        svgPath:'M 6 7 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 8 H 6 v -2 H 4 v 2 H 2 V 9 c 0 -0.553 0.195 -1.025 0.585 -1.415 C 2.975 7.195 3.447 7 4 7 h 2 Z M 4 9 v 4 h 2 V 9 H 4 Z m 11 1.5 c 0 0.413 -0.147 0.767 -0.44 1.06 c -0.293 0.293 -0.647 0.44 -1.06 0.44 c 0.413 0 0.767 0.147 1.06 0.44 c 0.293 0.293 0.44 0.647 0.44 1.06 V 15 c 0 0.553 -0.195 1.025 -0.585 1.415 c -0.39 0.39 -0.862 0.585 -1.415 0.585 H 9 V 7 h 4 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 1.5 Z M 11 13 v 2 h 2 v -2 h -2 Z m 0 -4 v 2 h 2 V 9 h -2 Z m 5 6 V 9 c 0 -0.553 0.195 -1.025 0.585 -1.415 c 0.39 -0.39 0.862 -0.585 1.415 -0.585 h 2 c 0.553 0 1.025 0.195 1.415 0.585 c 0.39 0.39 0.585 0.862 0.585 1.415 v 1.5 h -2 V 9 h -2 v 6 h 2 v -1.5 h 2 V 15 c 0 0.547 -0.197 1.017 -0.59 1.41 c -0.393 0.393 -0.863 0.59 -1.41 0.59 h -2 a 1.924 1.924 0 0 1 -1.41 -0.59 A 1.924 1.924 0 0 1 16 15 Z")'
    },
    {
        value:'int',
        label:'整数',
        svgPath:'M3 9V7h4v10H5V9H3zm5-2h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v2c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-2v2h4v2H8v-4c0-.553.195-1.025.585-1.415.39-.39.862-.585 1.415-.585h2V9H8V7zm7 0h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v1.5c0 .413-.147.767-.44 1.06-.293.293-.647.44-1.06.44.413 0 .767.147 1.06.44.293.293.44.647.44 1.06V15c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-4v-2h4v-2h-2v-2h2V9h-4V7z',
    },
    {
        value:'float',
        label:'小数',
        svgPath:'M3 9V7h4v10H5V9H3zm5-2h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v2c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-2v2h4v2H8v-4c0-.553.195-1.025.585-1.415.39-.39.862-.585 1.415-.585h2V9H8V7zm7 0h4c.547 0 1.017.197 1.41.59.393.393.59.863.59 1.41v1.5c0 .413-.147.767-.44 1.06-.293.293-.647.44-1.06.44.413 0 .767.147 1.06.44.293.293.44.647.44 1.06V15c0 .547-.197 1.017-.59 1.41-.393.393-.863.59-1.41.59h-4v-2h4v-2h-2v-2h2V9h-4V7z',
    },
    {
        value:'boolean',
        label:'布尔值',
        svgPath:'M 7 7 h 10 a 5 5 0 0 1 0 10 H 7 A 5 5 0 0 1 7 7 Z m 10 8 a 3 3 0 1 0 0 -6 a 3 3 0 0 0 0 6 Z'
    },
    {
        value:'json',
        label:'JSON',
        svgPath:'M 8 8 v 1.68 c 0 0.4 -0.075 0.795 -0.225 1.185 a 3.62 3.62 0 0 1 -0.625 1.055 c 0.567 0.58 0.85 1.273 0.85 2.08 v 2 c 0 0.273 0.098 0.508 0.295 0.705 A 0.962 0.962 0 0 0 9 17 h 1 v 2 H 9 a 2.89 2.89 0 0 1 -2.12 -0.88 A 2.89 2.89 0 0 1 6 16 v -2 a 0.962 0.962 0 0 0 -0.295 -0.705 A 0.962 0.962 0 0 0 5 13 H 4 v -2 h 1 c 0.26 0 0.492 -0.147 0.695 -0.44 c 0.203 -0.293 0.305 -0.587 0.305 -0.88 V 8 c 0 -0.827 0.293 -1.533 0.88 -2.12 A 2.89 2.89 0 0 1 9 5 h 1 v 2 H 9 a 0.962 0.962 0 0 0 -0.705 0.295 A 0.962 0.962 0 0 0 8 8 Z m 11 3 h 1 v 2 h -1 a 0.962 0.962 0 0 0 -0.705 0.295 A 0.962 0.962 0 0 0 18 14 v 2 a 2.89 2.89 0 0 1 -0.88 2.12 A 2.89 2.89 0 0 1 15 19 h -1 v -2 h 1 a 0.962 0.962 0 0 0 0.705 -0.295 A 0.962 0.962 0 0 0 16 16 v -2 c 0 -0.807 0.283 -1.5 0.85 -2.08 a 3.62 3.62 0 0 1 -0.625 -1.055 A 3.277 3.277 0 0 1 16 9.68 V 8 a 1.008 1.008 0 0 0 -1 -1 h -1 V 5 h 1 a 2.89 2.89 0 0 1 2.12 0.88 A 2.89 2.89 0 0 1 18 8 v 1.68 c 0 0.293 0.102 0.587 0.305 0.88 c 0.203 0.293 0.435 0.44 0.695 0.44 Z'
    }
])
const config = ref<any>()
// 是否存在错误
const errors = ref(0);
// 错误行数
const line = ref();
const isEqual = (value1: any, value2: any): boolean => {
  // 处理基本类型
  if (value1 === value2) {
    return true;
  }
  // 处理 null 和 undefined
  if (value1 == null || value2 == null) {
    return value1 === value2;
  }
  // 获取类型
  const type1 = typeof value1;
  const type2 = typeof value2;
  // 类型不同直接返回 false
  if (type1 !== type2) {
    return false;
  }
  // 处理数组
  if (Array.isArray(value1) && Array.isArray(value2)) {
    if (value1.length !== value2.length) {
      return false;
    }
    return value1.every((item, index) => isEqual(item, value2[index]));
  }

  // 处理对象
  if (type1 === 'object') {
    const keys1 = Object.keys(value1);
    const keys2 = Object.keys(value2);
    if (keys1.length !== keys2.length) {
      return false;
    }
    return keys1.every(key => 
      Object.prototype.hasOwnProperty.call(value2, key) && 
      isEqual(value1[key], value2[key])
    );
  }
  return false;
};
const editJsonValue = ref('')
// 是否可保存舍弃
const editAble = computed(() => {
  return isEqual(editJsonValue.value, config.value);
});
const editCode = ref()
const jsonModal = (value,code) => {
    editCode.value = code
    if (value && typeof value === 'string') {
        try {
            config.value = JSON.parse(value);
        } catch (error) {
            config.value = null;
        }
    }else{
        config.value = null
    }
    editJsonValue.value = value
    modalVisible.value = true
}
// 数据更新时触发
const updataModel = (val: any) => {
    console.log(val,'-------------------------------update');
    
    config.value = val;
};
// 数据错误时触发
const editError = (a: any, error: any) => {
  errors.value = error.length;
  console.log(errors.value,'errors.value');
  
  if (error[0]) {
    line.value = error[0].line;
  }
}
const getTabList = async () => {
  const data = {
    appId:props.appId,
    paramName:form.name,
    conditionScope:'app_param_specific',
    isLinkValue:false
  }
  await getConditionList(data).then(res => {
    conditionList.value = res.items || []
    
  })
}
const dataLoading = ref(false)
const handleDrawer = async (record?:any,type?:string) => {
    visibleTitle.value = type ? '修改参数' : '创建参数'
    visibleType.value = type || 'create'
    visible.value = true;
    dataLoading.value = true

    if(record?.name){
      form.name = record?.name

      await getTabList()

        const data = {
            appId:props.appId,
            paramName:record.name
        }
        await getConfigParamList(data).then(res => {
            const detailData  = res?.items[0]
            const defaultData = detailData?.conditions.find(item => item.code === 'default')
            form.defaultValue = defaultData?.value || ''
            form.valueType = detailData.valueType
            form.name = detailData?.name
            form.note = detailData?.description || ''
            const conditions = detailData?.conditions.filter(item => item.code !== 'default')
            conditions.forEach(item => {
                item.editable = true
                item.defaultValue = item.value
            })
            form.conditionsItem = conditions
        })
    }else{
        form.conditionsItem = []
        form.defaultValue = ''
        form.valueType = 'string'
        form.name = ''
        form.note = ''
      await getTabList()
    }
    form.svgPath = dataTypeList.value.find(item => item.value === form.valueType)?.svgPath;
    dataLoading.value = false
    if(visibleType.value === 'copy'){
        formRef.value.validateField('name')
    }
};

const handleCancel = () => {
    visible.value = false;
}

const saveLoading = ref(false)
// 保存参数
const saveParams = () => {
    formRef.value.validate(async (error:any) => {
        if (!error) {
           if (form.defaultValue === undefined || form.defaultValue === null) {
             Message.warning('请填写默认值');
             return;
           }
            saveLoading.value = true
            const list = [] as any

            list.push({
                code:'default',
                value:form.defaultValue
            })

            const conditions = form.conditionsItem.filter(item => item.editable)
            conditions.forEach(item => {
                list.push({
                    code:item.code,
                    value:item.defaultValue
                })
            })
            // console.log(list,'ccccccc');
            try {
                const params = {
                    appId:props.appId,
                    name:form.name,
                    valueType:form.valueType,
                    note:form.note,
                    dataVersion:'default',
                    conditions:list
                }
                await saveRemoteConfig(params).then(res => {
                    Message.success('保存成功')
                    emits('updateData')
                })
                visible.value = false;

            } catch (error) {
                console.log(error);
            } finally {
                saveLoading.value = false
            }
        }
    })
}
// josn编辑器取消
const handleModalCancel = () => {
    modalVisible.value = false
}
// json编辑器保存
const configSave = () => {
    if(editCode.value === 'default'){
        form.defaultValue = JSON.stringify(config.value)
    }else{
        const index = form.conditionsItem.findIndex(item => item.code === editCode.value)
        if(index !== -1){
            form.conditionsItem[index].defaultValue = JSON.stringify(config.value)
        }
    }
    modalVisible.value = false
}

const dataTypeChange = (v) => {
    const matchedItem = dataTypeList.value.find(item => item.value === v);
    form.svgPath = matchedItem.svgPath
    if(v === 'boolean' && form.defaultValue !=='false' && form.defaultValue !== 'true'){
        form.defaultValue = ''
        form.conditionsItem.forEach((item, index) => {
            item.defaultValue = ''
        });
    }
    formRef.value.validateField('defaultValue')
    form.conditionsItem.forEach((item, index) => {
        formRef.value.validateField(`defaultValue_${index}`);
    });
}

// 新增条件
const conditionChange = (v) => {
    // 编辑
    const vIndex = form.conditionsItem.findIndex(item => item.code === v.code)
    if(vIndex !== -1){
        form.conditionsItem[vIndex].color = v.color
        form.conditionsItem[vIndex].name = v.name
        form.conditionsItem[vIndex].rules = v.rules
        form.conditionsItem[vIndex].code = v.code
    }else{
        // 新增
        const list = []
        list.push({
                isDefault:false,
                editable:true,
                defaultValue:'',
                name:v.name,
                color:v.color,
                rules:v.rules,
                code:v.code
            }
        )
        form.conditionsItem = form.conditionsItem.concat(list);
    }
    getTabList()
}
const selectConditionItem = (v) => {
  if (!form.name?.trim()) {
    Message.warning('请先填写参数名称');
    popupVisible.value = false;
    return;
  }

  if (!/^[A-Za-z_][A-Za-z0-9_]*$/.test(form.name)) {
    Message.warning('参数名称只能包含字母、数字和下划线，且以字母开头');
    popupVisible.value = false;
    return;
  }

    const list = []
    const nameIndex = form.conditionsItem.findIndex(item => item.name === v.name)
    if(nameIndex !== -1){
        form.conditionsItem[nameIndex].editable = true
    }else{
        list.push({
            isDefault:false,
            editable:true,
            defaultValue:'',
            name:v.name,
            color:v.color,
            code:v.code,
            rules:v.rules
        })
    }
    
    form.conditionsItem = form.conditionsItem.concat(list);
}
const isDefaultChange = () => {
    if(form.isDefault){
        form.defaultValue = ''
        formRef.value.clearValidate('defaultValue')
    }
}
const isDefaultItemChange = (index:number) => {
    if(form.conditionsItem[index].isDefault){
        form.conditionsItem[index].defaultValue = ''
        formRef.value.clearValidate(`defaultValue_${index}`)
    }
}
const itemChange = (e,index) => {
    const itemData = conditionList.value.find(item => item.name === e)
    console.log(itemData,'uuu');
    form.conditionsItem[index].code = itemData.code
    form.conditionsItem[index].color = itemData.color
    form.conditionsItem[index].rules = itemData.rules
}
const editCondition = (item:FormItem) => {
    if(item.editable){
        conditionModalRef.value.openModal(item,'edit')
    }
}
const deleteCondition = (index:number) => {
    form.conditionsItem[index].editable = false
}
defineExpose({
    handleDrawer
})
</script>

<style scoped lang="less">
.types{
    display: flex;
}
.option-item{
    width: 100%;
    display: flex;
    align-items: center;
}
.logic{
    display: flex;
}
.line{
    border-left: 2px dashed #ccc;
    // flex-grow: 1;
    margin-left: 24px;
    margin-right: 24px;
    width: 2px;
    height: 96px;
}
.non-shrink-item {
  flex: 0 0 auto;
}
.default{
    margin-top: 12px;
    position: relative;
    .use-default{
        position: absolute;
        z-index: 888;
        right: 0;
        top: 0;
    }
    .globalEdit{
        position: absolute;
        right: 0;
        right: 12px;
        height: 32px;
        width: 32px;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        &:hover{
            background: #faf9f9;
        }
    }
}
.actions{
    padding: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right{
        button{
            border-radius: 8px;
        }
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
:deep(.jsoneditor-statusbar){
    display: none;
}
:deep(.jsoneditor-menu){
    display: none;
}
:deep(.jsoneditor){
    border: none;
}
:deep(.full-screen){
    display: none !important;
}
.modal-title{
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px 0 16px;
    .title{
        .word{
            font-size: 20px;
            line-height: 28px;
            font-weight: 500;
            margin-left: 16px;
        }
    }
    .close-icon{
        font-size: 24px;
        cursor: pointer;
    }
    .modal-action-content{
        color: #fff;
        display: flex;
        align-items: center;
        .division{
            width: 1px;
            height: 24px;
            background: #fff;
            margin: 0 24px;
        }
        .btn{
            margin-right: 24px;
        }
        button{
            border-radius: 8px;
        }
    }
}
.new-add-item{
    width: 200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item-info{
        .item-info{

        }
        .item-word{
            font-size: 12px;
            line-height: 16px;
            color: var(--color-text-2);
            text-align: left;
            white-space: normal;
        }
    }
}
.handle-condition-box{
    .handle-content{
        .handle-header{
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        .handle-btn{
            width: 42px;
            height: 42px;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 100%;
            &:hover{
                background: var(--color-secondary);
            }
        }
    }
    .label-word{
        display: block;
        color: var(--color-text-2);
        font-size: 13px;
        width: 100%;
        padding: 8px 0;
    }
    .handle-item{
        display: flex;
    }
}
.create-condition{
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    padding: 0 12px;
    color: var(--color-text-1);
    font-size: 14px;
    line-height: 36px;
    text-align: left;
    background-color: var(--color-bg-popup);
    cursor: pointer;
}
.drag-handle {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--color-text-3);
  
  &:hover {
    color: var(--color-text-1);
  }
}

.ghost-item {
  opacity: 0.5;
  background: var(--color-fill-2);
  border: 1px dashed var(--color-primary);
}

.handle-header {
  display: flex;
  align-items: center;
}
</style>