<template>
  <a-layout class="layout">
    <a-layout-header class="layout__header">
      <div class="header-title">
        <button class="title-button" @click="goBack">
          <span role="img">
            <icon-left size="13"/>
          </span>
        </button>
        <span style="align-items: center">维表数据</span>
      </div>
    </a-layout-header>

    <a-layout-content class="layout__content">
      <div class="page">
        <div class="page-head">
          <div class="title">
              <!-- 维度表管理 -->
          </div>
          <div class="filter">
            <div class="filter-item">
              <a-input v-model="searchValue" class="title-input" placeholder="请输入需要检索的名称">
                <template #prefix>
                  <icon-search/>
                </template>
              </a-input>
            </div>
          </div>
        </div>
        <div class="page-body">
          <a-table
            :columns="columns"
            :loading="loading"
            :data="filteredTableData"
            :bordered="false"
            :hoverable="true"
            sticky-header
            :table-layout-fixed="true"
            :filter-icon-align-left="true"
            :column-resizable="true"
            :scroll="scroll"
            :scrollbar="scrollbar"
            :pagination="{
                showPageSize:true,
                defaultPageSize:20,
                showJumper: true,
                autoAdjust: true,
            }"
        >
            <template #pagination-left>
              <div class="pagination-left">
                  共{{ filteredTableData.length }}条记录
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </a-layout-content>
    <attr-edit-modal ref="editModalRef" @success="init"/>
  </a-layout>
</template>
  
<script setup lang="ts">
import {computed, ref} from "vue";
import router from "@/router";
import {useRoute} from 'vue-router'
import {getDimensionDataList} from "@/api/setting/api";
import {ROUTE_NAME} from "@/router/constants";

const route = useRoute();
const tableCode = ref(route.query.tableCode as string);
const tableData = ref<any>([]);
const loading = ref<boolean>(false);
const scrollbar = ref(true);
// input搜索
const searchValue = ref('');
// Table页面滑动
const scroll = {
  y: 'calc(100vh - 186px)'
};

const filteredTableData = computed(() => {
  const str = searchValue.value.trim();
  if (!str) return tableData.value;
  
  return tableData.value.filter(item => {
    // 遍历对象的所有值
    return Object.values(item).some(value => {
      // 将任何类型的值转换为字符串后进行包含判断
      const valueStr = String(value).toLowerCase();
      return valueStr.includes(str.toLowerCase());
    });
  });
});

const columns = computed(() => {
  if (!tableData.value || tableData.value.length === 0) return [];
  
  // 获取第一条数据的所有键
  const firstItem = tableData.value[0];
  return Object.keys(firstItem).map(key => ({
    title: key,
    dataIndex: key,
    ellipsis: true,
    tooltip: true,
    minWidth: 120,
  }));
});


const init = async () => {
  loading.value = true
  try {
    await getDimensionDataList(tableCode.value).then(res => {
      tableData.value = res;
    })
  } catch (err) {
    console.log(err)
  } finally {
    loading.value = false
  }

};
init()

// 模拟数据
const goBack = () => {
    router.push({
        name: ROUTE_NAME.SETTING_DIMENSION,
    });
}
</script>
  
<style scoped lang="less">
.layout {
background-color: var(--tant-bg-gray-color-bg2-1);
width: 100%;
display: flex;
flex-direction: column;
height: 100%;
min-height: 100vh;

.layout__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 12px 24px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .header-title {
    flex: 1 1;
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-header-font-header4-medium);
    align-items: center;

    .title-button {
        color: var(--tant-text-gray-color-text1-2);
        background-color: transparent;
        border: none;
        margin-right: 4px;
        padding: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
    }

    .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;
        transition: .5s;

    }

    .title-button > span {
        display: flex;
        align-items: center;
        line-height: normal;
    }

    }

    .header-oper {
    display: flex;

    .title-button {
        background-color: transparent;
        border: none;
        margin-right: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        color: var(--tant-secondary-color-secondary-default);
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
    }

    .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;

    }

    .title-button-next {
        background-color: var(--tant-primary-color-primary-hover);
        border-color: var(--tant-primary-color-primary-hover);
        color: var(--tant-white-white-100);
        border: none;
        margin-left: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
    }

    .title-button-next:disabled {
        background-color: var(--tant-primary-color-primary-disable);
        border-color: var(--tant-primary-color-primary-disable);
        cursor: not-allowed;
    }
    }
}

.layout__content {
    display: flex;
    flex: 1 1;
    height: calc(100vh - 100px);
    // margin: 24px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
}
}
.content{
    width: 100%;
    height: 100%;
    padding: 20px 70px;
}
.pagination-left {
  flex: 1 1;
  color: var(--tant-text-gray-color-text1-3);
  word-break: break-all;
}
</style>