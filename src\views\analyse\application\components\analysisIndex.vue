<template>
  <div class="guide">
    <div class="stickyBar">
      <div class="modal" style="width: calc(100% - 78px);">
        <img src="/icon/analysis/performAnalysis.svg" alt="">
        <!-- <span v-if="flattenedList?.length<2" class="title">分析指标</span> -->
        <span v-if="flattenedList?.length" class="title">对</span>
        <opAnalysisSubjectSelect v-if="flattenedList?.length" :subject-data="subjectShowData" :style="{width:'120px',margin:'0 8px'}" @subject-change="subjectChange"/>
        <span v-if="flattenedList?.length" class="title">进行分析</span>
        <div class="model-btn">
          <a-button @click="addAnalysisIndex">
            <template #icon>
              <icon-plus class="nav-icon"/>
            </template>
          </a-button>
        </div>
      </div>
      <a-tooltip content="添加公式" position="top">
        <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="addFormula">
          <template #icon>
            <icon-formula/>
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip content="重置" position="top">
        <a-button style="width: 36px;height: 40px;" class="btn-bg" @click="resetParams">
          <template #icon>
            <icon-loop style="font-size: 16px;"/>
          </template>
        </a-button>
      </a-tooltip>
    </div>
    <div class="event-filter-box">
      <draggable
          :list="indicatorListData || []"
          group="indicatorListDataDrag"
          item-key="index"
          handle=".left-handle"
          ghost-class="drag-ghost"
      >
        <template #item="{ element,index }">
          <div class="action-row">
            <div class="filter-row-eventRow">
              <div class="action-left">
                <i class="left-index">{{ index + 1 }}</i>
                <i class="left-handle">
                  <icon-drag-dot-vertical/>
                  <a-tooltip v-if="!element.ignoreCalc" content="忽略计算">
                    <icon-eye style="margin-top: 12px;cursor: pointer;" @click="() => element.ignoreCalc = true"/>
                  </a-tooltip>
                  <a-tooltip v-if="element.ignoreCalc" content="参与计算">
                    <icon-eye-invisible style="margin-top: 12px;cursor: pointer;" @click="() => element.ignoreCalc = false"/>
                  </a-tooltip>
                </i>
                <div class="row-content">
                  <div v-if="element.ignoreCalc" class="mask"></div>
                  <div class="rename">
                    <a-tooltip content="点击修改指标名称" position="top">
                      <a-input v-model="element.displayName" :style="!element.isBasic ? {minWidth: '80px',maxWidth: 'calc(100% - 190px)'} :  {minWidth: '80px',maxWidth: 'calc(100% - 100px)'}"/>
                      <a-dropdown v-if="!element.isBasic" @select="handleUnitSelect(index,$event)">
                        <div class="filter-btn">
                          <span class="filter-label">{{ element.unitName }}</span>
                        </div>
                        <template #content>
                          <a-doption :value="{obj:{type:'default',decimalNum:2,thousandSep: 1},name:'2位小数'}">2位小数</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:3,thousandSep: 1},name:'3位小数'}">3位小数</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:4,thousandSep: 1},name:'4位小数'}">4位小数</a-doption>
                          <a-doption :value="{obj:{type:'percent',decimalNum:2,thousandSep: 1},name:'百分比'}">百分比</a-doption>
                          <a-doption :value="{obj:{type:'default',decimalNum:0,thousandSep: 1},name:'取整'}">取整</a-doption>
                        </template>
                      </a-dropdown>
                    </a-tooltip>
                  </div>
                  <div v-if="element.isBasic && element.eventList?.length" class="event-element">
                    <EventIndicatorSelect :ref="el => evtIndRefs[index] = el" :no-fetch-attr="true" :panel-data="element.eventList[0]" @analysis-index-change="panelSelectChange(index,0,$event)"/>
                    <a-tooltip content="切换自定义公式" position="top">
                      <a-button class="custom-edit-btn btn-26" @click="editCustomIndicator(index)">
                        <template #icon>
                          <icon-formula/>
                        </template>
                      </a-button>
                    </a-tooltip>
                  </div>
                  <a-tooltip content='点击编辑自定义指标' position="tl">
                    <custom-indicator-display v-if="!element.isBasic && element.eventList?.length" :allow-hover="true" :indicator="element" @click="editCustomIndicator(index)"/>
                  </a-tooltip>
                </div>
              </div>
              <div class="action-right">
                <a-space align="center">
                  <a-tooltip content="添加筛选" position="top">
                    <a-button class="btn-bg btn-26" @click="add(index)">
                      <template #icon>
                        <icon-filter/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-tooltip content="复制" position="top">
                    <a-button class="btn-bg btn-26" @click="copyItem(index)">
                      <template #icon>
                        <icon-copy/>
                      </template>
                    </a-button>
                  </a-tooltip>
                  <a-dropdown v-if="indicatorListData.length>1" trigger="hover" @select="handleSelect(index,$event)">
                    <a-button class="btn-bg btn-26">
                      <template #icon>
                        <icon-more-vertical/>
                      </template>
                    </a-button>
                    <template #content>
                      <a-doption value="delete">
                        <template #icon>
                          <icon-close-circle/>
                        </template>
                        <template #default>删除</template>
                      </a-doption>
                    </template>
                  </a-dropdown>
                </a-space>
              </div>
            </div>
            <eventQueryFilter
                :ref="el => element.queryFilterRef = el"
                :filter="element.filter"
                :show-detail-filter="true"
                @query-filters-change="queryFiltersChange(index,$event)"/>
            <div v-if="element.filter && element.filter.filters && element.filter.filters.length" class="row-foot">
              <div class="ta-filter-button" @click="add(index)">
                <icon-plus class="action"/>
                <span class="label">筛选条件</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
    <edit-custom-indicator-modal
        v-if="editCustomIndicatorVisible"
        v-model:visible="editCustomIndicatorVisible"
        :indicator="indicatorListData[operateIndicatorIndex]"
        :no-fetch-attr="true"
        :subject-code="subjectShowData.subject"
        @confirm="editCustomIndicatorConfirm"/>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, ref, watch} from "vue";
import {Indicator} from "@/api/analyse/type";
import {cloneDeep, omit} from "lodash";
import draggable from 'vuedraggable'
import opAnalysisSubjectSelect from "@/views/analyse/components/operation/opAnalysisSubjectSelect.vue";
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import EditCustomIndicatorModal from "@/views/analyse/components/operation/EditCustomIndicatorModal.vue";
import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import eventQueryFilter from "@/views/analyse/components/operation/eventQueryFilter.vue"
import {IndicatorType} from "@/api/enum";


const props = defineProps({
  subjectData: {
    type: Object,
    default() {
      return {};
    },
  },
  indicatorList: {
    type: Array,
    default() {
      return [];
    },
  },
  analysisIndexData: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['indicatorsChange', 'analysisSubjectChange', 'subjectChange', 'resetParams'])
const flattenedList = computed(() => {
  return Object.values(props.subjectData).flat()
})
const subject = ref('')
const subjectChange = (v) => {
  subject.value = v.subject
  emits('subjectChange', v.subject)
}

const subjectShowData = computed(() => {
  const subjectCode = sessionStorage.getItem('application-subject-code');
  if (subjectCode) {
    const obj = flattenedList.value.find(item => item.code === subjectCode)
    return {
      subject: subjectCode,
      subjectName: obj.name
    }
  }
  return {
    subject: '',
    subjectName: ''
  }
})

const indicator = ref<Indicator[]>([])

const indicatorListData = ref<any>([])
const indicatorFlatList = ref<any>([])
// 当前操作指标
const operateIndicatorIndex = ref<number>(0);

// 自定义指标编辑显示
const editCustomIndicatorVisible = ref<boolean>(false)

watch(() => props.analysisIndexData, () => {
  indicatorListData.value = cloneDeep(props.analysisIndexData)

}, {immediate: true, deep: true})
// 监听传入事件列表
watch(() => props.indicatorList, () => {
  indicatorFlatList.value = cloneDeep(props.indicatorList)
}, {immediate: true, deep: true})
const handleUnitSelect = (index: number, v) => {
  indicatorListData.value[index].unitName = v.name
  indicatorListData.value[index].displayType = v.obj
}

const panelSelectChange = (index: number, eventIndex: number, e: any) => {
  const filter = {
    ...indicatorListData.value[index].eventList[eventIndex],
    ...e
  }
  const fieldsToRemove = e.type === 'indicator'
      ? ['eventCode', 'eventName', 'eventDisplayName']
      : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];

  const cleanedFilter = omit(filter, fieldsToRemove);
  indicatorListData.value[index].eventList[eventIndex] = cleanedFilter;
  indicatorListData.value[index].name = e.eventName || e.indicatorName
  indicatorListData.value[index].code = e.eventCode || e.indicatorCode
  indicatorListData.value[index].displayName = `${e.eventDisplayName || e.indicatorDisplayName}`
}

const queryFiltersChange = (index: number, v) => {
  indicatorListData.value[index].filter = v;
}

const evtIndRefs = ref<any>([])
// 添加事件指标
const addAnalysisIndex = () => {
  indicatorListData.value.push({
    name: indicatorFlatList.value?.[0]?.name,
    type: IndicatorType.OPERATION,
    isBasic: true,
    code: indicatorFlatList.value?.[0]?.code,
    displayName: `${indicatorFlatList.value?.[0]?.displayName}`,
    displayType: {},
    unitName: '',
    filter: {},
    eventList: [
      {
        eventName: indicatorFlatList.value?.[0]?.name,
        eventDisplayName: indicatorFlatList.value?.[0]?.displayName,
        eventCode: indicatorFlatList.value?.[0]?.code,
        type: indicatorFlatList.value?.[0]?.objectType,
        eventType: indicatorFlatList.value?.[0]?.type,
        filter: {}
      }
    ],
    formula: '',
    ignoreCalc: false
  })
  nextTick(() => {
    if (evtIndRefs.value[indicatorListData.value.length - 1]) {
      evtIndRefs.value[indicatorListData.value.length - 1].handleTriggerVisible();
    }
  });
  // eventBus.emit(indicatorListData.value);
}
const addFormula = () => {
  indicatorListData.value.push({
    name: indicatorFlatList.value?.[0].name,
    type: IndicatorType.OPERATION,
    isBasic: false,
    displayName: '自定义指标',
    displayType: {},
    unitName: '两位小数',
    filter: {},
    eventList: [
      {
        eventName: indicatorFlatList.value?.[0]?.name,
        eventDisplayName: indicatorFlatList.value?.[0]?.displayName,
        eventCode: indicatorFlatList.value?.[0]?.code,
        type: indicatorFlatList.value?.[0]?.objectType,
        eventType: indicatorFlatList.value?.[0]?.type,
        filter: {}
      }
    ],
    formula: '',
    ignoreCalc: false
  })
}

const add = (index: number) => {
  indicatorListData.value[index].queryFilterRef.add()
}

// 复制
const copyItem = (index: number) => {
  const newItem = cloneDeep(indicatorListData.value[index]);
  indicatorListData.value.splice(index + 1, 0, newItem);
}

watch(indicatorListData, (newValue, oldValue) => {
  if (newValue) {
    indicator.value = newValue.map(item => {
      return {
        displayName: item.displayName,
        displayType: item.displayType,
        unitName: item.unitName,
        name: item.name,
        type: item.type,
        isBasic: item.isBasic,
        ...(!item.isBasic ? {} : {code: item.code}),
        formula: !item.isBasic ? item.formula : '',
        eventList: item.eventList,
        filter: item.filter,
        ignoreCalc: item.ignoreCalc
      }
    })
    emits('indicatorsChange', indicator.value)
  }
}, {immediate: true, deep: true})


const handleSelect = (index: number, v) => {
  if (v === 'delete') {
    indicatorListData.value.splice(index, 1)
    indicatorListData.value.queryFilterRef.deleteAll()
  }
};
/**
 * 编辑自定义指标
 */
const editCustomIndicator = (index: number) => {
  // indicatorListData.value[index].isBasic = false
  if (!indicatorListData.value[operateIndicatorIndex.value].displayType.type) {
    indicatorListData.value[operateIndicatorIndex.value].displayType = {
      type: 'default',
      decimalNum: 2,
      thousandSep: 1
    }
  }
  indicatorListData.value[operateIndicatorIndex.value].unitName = indicatorListData.value[operateIndicatorIndex.value].unitName ? indicatorListData.value[operateIndicatorIndex.value].unitName : '2位小数'
  operateIndicatorIndex.value = index
  editCustomIndicatorVisible.value = true
}

/**
 * 自定义指标编辑确认
 */
const editCustomIndicatorConfirm = (customIndicator) => {
  const {eventList, formula, displayType} = customIndicator;
  indicatorListData.value[operateIndicatorIndex.value].isBasic = formula === 'A'
  indicatorListData.value[operateIndicatorIndex.value].eventList = eventList
  indicatorListData.value[operateIndicatorIndex.value].formula = formula
  indicatorListData.value[operateIndicatorIndex.value].displayType = displayType
  indicatorListData.value[operateIndicatorIndex.value].displayName = !indicatorListData.value[operateIndicatorIndex.value].isBasic ? '自定义指标' :indicatorListData.value[operateIndicatorIndex.value]?.eventList[0]?.indicatorDisplayName
}
const resetParams = () => {
  emits('resetParams')
}
</script>

<style scoped lang="less">
.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);
    display: flex;
    align-items: center;

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        //   flex-grow: 1;
        font-weight: 600;
        //   max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .left-index {
          margin-top: 4px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
        }

        .left-handle {
          cursor: pointer;
          font-size: 14px;
          color: var(--tant-secondary-color-secondary-default);
          background-color: transparent;
          margin-top: 4px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          transition: all .3s;
          display: none;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;
          position: relative;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            // font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }

            :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
              font-weight: 600 !important;
            }

            :deep(.arco-input-wrapper) {
              border: none;
              background-color: transparent;
              font-weight: 600;

              &:hover {
                border: none;
                background-color: transparent;
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;
          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }
      .event-element {
        display: flex;
        align-items: center;
      }
      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .action-left .row-content :deep(.filter-icon) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }

        .left-index {
          display: none;
        }

        .left-handle {
          display: block;
        }
      }
    }

    .drag-ghost {
      background-color: var(--tant-primary-color-primary-fill) !important;
    }

    .row-foot {
      margin: 0;
      padding-left: 34px;
      transition: all .3s;

      .ta-filter-button {
        padding: 6px;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        transition: all .3s;

        .action {
          border-radius: 4px;
          background-color: var(--tant-primary-color-primary-fill);
          color: var(--tant-primary-color-primary-default);
          margin-right: 8px;
          padding: 3px;
          font-size: 18px;
        }

        .label {
          color: var(--tant-primary-color-primary-default);
        }

        &:hover .action {
          background-color: var(--tant-primary-color-primary-fill-hover);
          color: var(--tant-primary-color-primary-hover);
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.custom-edit-btn {
  background-color: transparent;

  &:hover {
    background-color: transparent;
    color: var(--tant-primary-color-primary-hover);
  }
}

:deep(.arco-textarea) {
  border: none;
}
</style>