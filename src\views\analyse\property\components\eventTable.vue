<script setup lang="ts">
import {ref} from "vue";
import * as XLSX from 'xlsx';
import {cloneDeep} from 'lodash'

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
}

const props = defineProps<Props>()

const columns = ref<any>([
  {
    title:'指标',
    dataIndex:'name',
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'用户数',
    dataIndex:'group1',
    slotName:'group1',
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
]);
const tableData = ref<any>([
  {
    name:'总体',
    group1:12911,
  },
]);

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])

const calculateSum = (array:any) => {
  return array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0);

};

// 判断周几
const getDayOfWeek = (dateString) => {
  const date = new Date(dateString);
  const days = ['日', '一', '二', '三', '四', '五', '六'];
  return `${dateString}(${days[date.getDay()]})`;
}
const freshData = () => {

  copyColumns.value = cloneDeep(columns.value)
  copyTableData.value = cloneDeep(tableData.value)
};


freshData()

// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `属性分析.xlsx`);
};
const createVisible = ref(false)
const form = ref({
  name: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
const handleSaveCancel = () => {
  createVisible.value = false;
}
const saveReport = async () => {
}
defineExpose({
  freshData,
  exportXlsx
})
</script>

<template>
  <div class="chart-content" style="margin-bottom: 24px;">
    <div style="display: flex;justify-content: space-between;margin-bottom: 16px;">
      <div>
      </div>
      <div>
        <a-button style="margin-left: 12px;" @click="exportXlsx">导出</a-button>
      </div>
    </div>
    <a-spin style="width: 100%;height: 100%">
      <a-table :columns="columns" :data="tableData" :pagination="false" size="small" column-resizable :bordered="{cell:true}" >
        <template #group1="{ record }">
          <div class="hover-box">
            <span>{{record.group1}}</span>
            <svg class="add-group" @click="() => createVisible = true" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor"><svg width="16" height="16" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4.5a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0zm-4 0a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z"></path><path d="M1 10a2 2 0 012-2h3a2 2 0 012 2v3H7v-3a1 1 0 00-1-1H3a1 1 0 00-1 1v3H1v-3z"></path><path d="M13 10h-1V8h-2V7h2V5h1v2h2v1h-2v2z"></path></svg></svg>
          </div>
        </template>
      </a-table>
    </a-spin>
    <!-- 创建弹窗 -->
    <a-modal v-model:visible="createVisible" :on-before-ok="validateForm" width="480px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">
          创建结果分群
          <a-tooltip content="选择计算结果保存为分群，可用于进一步下钻分析。" position="top">
            <icon-info-circle/>
          </a-tooltip>
        </div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <div class="form-label">分群显示名</div>
        <a-form-item field="name" :hide-label="true" :hide-asterisk="true" required :rules="[{ required: true, message: '分群显示名不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <div class="form-label">分群备注(选填)</div>
        <a-form-item field="description" :hide-label="true">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:3, maxRows:10}" :show-word-limit="true" :max-length="200"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <div class="boe-foot">
          <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
          <a-button type="primary" @click="saveReport">创建</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.table-title {
    width: 100%;
    color: var(--tant-text-gray-color-text1-1);
    font-size: 14px;
    line-height: 24px;
    text-align: center;
}
.hover-box{
  display: flex;
  align-items: center;
  &:hover{
    .add-group{
      opacity: 1;
    }
  }
  .add-group{
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;
    &:hover{
      color: #8d0088;
    }
  }
}
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}
.boe-foot {
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
.form-label{
  line-height: 32px;
  font-weight: 500;
}
</style>