
<template>
    <!-- meta修改fb个人号 -->
    <a-modal v-model:visible="modalVisible" :width="900" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-spin :loading="loading" style="width:100%; height: 300px; min-height:300px;">
            <div class="filter">
                <a-input v-model:model-value="fbParams.name" placeholder="请输入账户名称" style="width: 240px; margin-right: 10px;" />
                <a-select
                    v-model:model-value="fbParams.authStatus"
                    placeholder="请选择授权状态"
                    allow-clear
                    multiple
                    :max-tag-count="1"
                    style="width: 240px;">
                    <template #label="{ data }">
                    <span>授权状态-{{ data?.label }}</span>
                    </template>
                    <a-option :value="3">已解绑</a-option>
                    <a-option :value="1">已授权</a-option>
                    <a-option :value="2">授权失效</a-option>
                    <a-option :value="0">未绑定</a-option>
                </a-select>
            </div>
            <div class="detail">
                <div class="selected">
                    已选：{{ selectedKeys.length  }}
                </div>
            </div>
            <div style="height: 500px;width: 100%;">
                <a-table
                    v-model:selectedKeys="selectedKeys"
                    :columns="columns"
                    :data="filteredTableData"
                    :row-selection="{
                        type: 'checkbox',
                        showCheckedAll: true,
                        onlyCurrent: false,
                    }"
                    row-key="accountId"
                    :scroll="{y:'100%'}"/>
            </div>
            <div class="footer">
                <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
                <a-button type="primary" :loading="saveLoading" @click="saveData">
                    保存
                </a-button>
            </div>
        </a-spin>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref,computed,watch} from "vue";
import {useSessionStorage} from '@vueuse/core';
import {getAuthFbAccountList,updateAuthFbAccount} from "@/api/setting/api";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('选择授权账户')
const facebookUserId = useSessionStorage("facebook-user-id", '')
const fbParams = reactive({
    authStatus: [],
    name:''
})
const tableData = ref<any>([])
const selectedKeys = ref<any>([])

// 新增：筛选后的数据
const filteredTableData = computed(() => {
    let data = tableData.value
    if (fbParams.name) {
        data = data.filter(item => item.accountName?.includes(fbParams.name))
    }
    if (fbParams.authStatus && fbParams.authStatus.length > 0) {
        data = data.filter(item => fbParams.authStatus.includes(item.authStatus))
    }
    return data
})
// 新增：已授权项默认勾选且不可取消
watch(tableData, (val) => {
    const authorized = val.filter(item => item.authStatus === 1).map(item => item.accountId)
    selectedKeys.value = Array.from(new Set([...selectedKeys.value, ...authorized]))
}, {immediate: true, deep: true})

const columns = [
    {
        title: '账户ID',
        dataIndex: 'accountId',
        minWidth: 120,
    },
    {
        title: '账户名称',
        dataIndex: 'accountName',
        ellipsis: true,
        tooltip: true,
        minWidth: 120,
    },
    {
        title: '账户状态',
        dataIndex: 'authStatus',
        minWidth: 120,
        render: (value) => {
            const {record} = value;
            let status = '已解绑';
            if (record.authStatus === 0) {
                status = '未绑定';
            } else if (record.authStatus === 1) {
                status = '已授权';
            } else if (record.authStatus === 2) {
                status = '授权失效';
            }
            return status;
        },
    },
]

const emits = defineEmits(['updateData']);
const loading = ref(false)

const openModal = async () => {
    modalVisible.value = true
    if(facebookUserId.value){
        try {
            loading.value = true
            const res = await getAuthFbAccountList(facebookUserId.value)
            tableData.value = res.map(item => {
                return {
                    ...item,
                    disabled: item.authStatus === 1
                }
            })
            facebookUserId.value = ''
        }catch (e) {
            console.error('获取数据失败', e)
        }finally {
            loading.value = false
        }
    }
}
const closeModal = () => {
    modalVisible.value = false
}
const saveLoading = ref(false)
const saveData = () => {
    if (selectedKeys.value.length === 0) {
        Message.error('请选择账户')
        return
    }
    try {
        saveLoading.value = true
        const params = {
            accountIdList: selectedKeys.value,
            authStatus: 1
        }
        updateAuthFbAccount(params).then(() => {
            Message.success('保存成功')
            emits('updateData')
            closeModal()
        })
    }catch (e) {
        console.error('保存失败', e)
    }finally {
        saveLoading.value = false
    }
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
.filter{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.detail{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}
</style>