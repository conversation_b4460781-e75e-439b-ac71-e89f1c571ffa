<template>
  <div :style="style" class="time-zone-select">
    <a-trigger
        v-model:popup-visible="timeTriggerVisible"
        position="bl"
        trigger="click"
        :unmount-on-close="false"
        :popup-translate="popupTranslate"
        :popup-offset="5">
      <div :style="buttonStyle" class="time-zone-select-btn" @click="()=>{timeTriggerVisible=!timeTriggerVisible}">
        <span class="btn-content">
          <img class="time-icon" src="/icon/earth.svg"/>{{ formatTimeZoneDisplay(timeZoneModel) }}
        </span>
      </div>
      <template #content>
        <div class="input-shadow">
          <a-input
              v-model:model-value="searchText"
              class="input-setting"
              placeholder="请输入搜索"
              allow-clear
              @input="filterTimeZones"
              @clear="clearTimeZones">
            <template #prefix>
              <icon-search/>
            </template>
          </a-input>
          <div class="list-set">
            <div class="list-setting">
              <div v-for="zone in nowTimeZones" :key="zone" @click="selectTimeZone(formatTimeZoneRequestParam(zone))">
                <div :class="searchText===zone?'list-self-choose':'list-self'">
                  <a-trigger
                      position="left"
                      popup-hover-stay
                      auto-fit-position
                      :popup-translate="[-15,0]"
                      :unmount-on-close="false"
                      content-class="contentClass">
                    <div class="list-date">
                      <div>{{ zone }}</div>
                      <div v-if="zone==='UTC+00'" class="list-el">默认</div>
                    </div>
                    <template #content>
                      <div class="trigger-zone">
                        <div class="trigger-body">
                          <div class="trigger-body-zone">{{ zone }}</div>
                          <div class="trigger-body-time" :data-info="getTimeInTimezone(zone)">
                          </div>
                        </div>
                      </div>
                    </template>
                  </a-trigger>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-trigger>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {formatTimeZoneDisplay, formatTimeZoneRequestParam, getTimeInTimezone, timezones} from "@/utils/timeUtil";

interface Props {
  style?: any;
  popupTranslate?: any;
  buttonStyle?: any;
  // 禁用缓存更新
  disableStorage?: boolean;
}

const props = defineProps<Props>()
// 绑定的时区
const timeZoneModel = defineModel<string>("timeZone", {default: undefined});
const searchText = ref();
const nowTimeZones = ref(timezones);

// 绑定方法，传给外部选择值
const emits = defineEmits(['selected']);

// 时区初始化，有绑定值以绑定值优先
if (timeZoneModel.value && !props.disableStorage) {
  sessionStorage.setItem('time-zone', timeZoneModel.value)
}

// 无绑定值以缓存优先
if (!timeZoneModel.value) {
  const initTimeZone = sessionStorage.getItem('time-zone');
  if (initTimeZone && initTimeZone.includes(':00')) {
    timeZoneModel.value = initTimeZone
  } else {
    // 无缓存时，赋初始值
    sessionStorage.setItem('time-zone', '+00:00')
    timeZoneModel.value = '+00:00'
  }
  emits('selected', initTimeZone)
}

const timeTriggerVisible = ref<boolean>(false);
// 过滤,统一大小写
const filterTimeZones = () => {
  const filterText = searchText.value.toLowerCase();
  nowTimeZones.value = timezones.filter(zone =>
      zone.toLowerCase().includes(filterText)
  );
};

// 点击
const selectTimeZone = (value) => {
  timeZoneModel.value = value;
  if (!props.disableStorage) {
    sessionStorage.setItem('time-zone', value);
  }
  timeTriggerVisible.value = false
  emits('selected', value);
};

// 清除功能，恢复列表
const clearTimeZones = () => {
  nowTimeZones.value = timezones
}
</script>

<style lang="less">

.time-zone-select {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: var(--tant-border-radius-medium);
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);

  &:hover {
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid var(--tant-primary-color-primary-hover);
  }

  .time-zone-select-btn {
    padding: 4px 8px;
    .btn-content {
      display: flex;
      align-items: center;
      height: 22px;
      gap: 3px;

      .time-icon {
        width: 14px;
        height: 14px;
      }
    }
  }
}

.input-setting {
  background-color: var(--tant-bg-white-color-bg1-1);
  width: 210px;
  height: 38px;
  border-radius: 4px;
  border: solid 1px white;
  padding: 4px 8px;
}

.input-setting:hover {
  background-color: var(--tant-bg-white-color-bg1-1);
}

.list-set {
  background-color: var(--tant-bg-white-color-bg1-1);
  height: 240px;
  overflow: auto;
  border-top: solid 1px var(--tant-border-color-border1-1);

  .list-setting {
    background-color: var(--tant-bg-white-color-bg1-1);

    padding: 4px 4px 0;

    .list-self {
      cursor: pointer;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: start;
      padding: 8px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      border-radius: 4px;

      .list-date {
        display: flex;
        width: 100%;
        justify-content: space-between;

        .list-text {
          display: flex;
          justify-content: space-between;
          color: red;
          background-color: var(--tant-bg-gray-color-bg2-1);
        }

        .list-el {
          float: right;
          padding: 0 8px;
          color: var(--tant-status-info-color-info-default);
          background-color: var(--tant-status-info-color-info-fill);
          border-radius: 2px;
        }
      }
    }

    .list-self-choose {
      cursor: pointer;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: start;
      padding: 8px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      border-radius: 4px;
      background-color: var(--tant-bg-gray-color-bg2-1);

      .list-date {
        display: flex;
        width: 100%;
        justify-content: space-between;

        .list-text {
          display: flex;
          justify-content: space-between;
          color: red;
          background-color: var(--tant-bg-gray-color-bg2-1);
        }


        .list-el {
          float: right;
          padding: 0 8px;
          color: var(--tant-status-info-color-info-default);
          background-color: var(--tant-status-info-color-info-fill);
          border-radius: 2px;
        }
      }
    }

    .list-self:hover {
      background-color: var(--tant-bg-gray-color-bg2-1);
      transition: .4s;

    }
  }
}

.input-shadow {
  max-width: 240px;
  background: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  box-shadow: var(--tant-small-shadow-small-overall);
}

.trigger-zone {
  width: 188px;
  padding: 12px 16px;
  background-color: var(--tant-bg-white-color-bg1-1);

  .trigger-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 180px;
    max-width: 320px;
    margin: -8px -12px;
    padding: 16px 29px;
    background-color: var(--tant-fill-color-fill1-1);

    .trigger-body-zone {
      margin-bottom: 16px;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
    }

    .trigger-body-time {
      position: relative;
      display: inline-block;
      width: 112px;
      height: 62px;
      text-align: center;

    }

    .trigger-body-time::after {
      position: absolute;
      top: 0;
      left: 0;
      width: 110px;
      height: 54px;
      background-color: var(--tant-bg-white-color-bg1-1);
      border-radius: 4px;
      box-shadow: var(--tant-small-shadow-small-bottom);
      content: attr(data-info);
      font-size: 28px;
    }

    .trigger-body-time::before {
      position: absolute;
      top: 24px;
      left: -10px;
      width: 132px;
      height: 38px;
      background-color: var(--tant-primary-color-primary-default);
      border-radius: 4px;
      content: "";
    }

    .trigger-body-set {
      display: inline-block;
      margin-top: 22px;

      .trigger-body-button {
        padding: 5px 0;
        color: var(--tant-primary-color-primary-hover);
        border: none;
        background-color: transparent;
        cursor: pointer;
      }
    }
  }
}

</style>

