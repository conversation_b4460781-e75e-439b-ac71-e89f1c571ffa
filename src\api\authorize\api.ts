// import axios from 'axios';
// import type { RouteRecordNormalized } from 'vue-router';
// import { UserState } from '@/store/modules/user/types';
import {getRequest, postRequest} from "@/api/request";
import {AxiosPromise} from "axios";

export function getUserProfile(): AxiosPromise<any> {
  return getRequest<any>('/api/user/profile', {});
}
export function login(email: string, password: string): AxiosPromise<string> {
  return postRequest<string>('/api/auth/login', {'email': email, 'password': password});
}

export function logout(): AxiosPromise<string> {
  return postRequest<string>('/api/auth/logout', {});
}
export function phoneLogin(params:any): AxiosPromise<string> {
  return postRequest<string>('/api/auth/login', {...params});
}
export function dingtalkLogin(code:string): AxiosPromise<string> {
  return getRequest<any>('/api/auth/login_by_dingtalk', {code});
}
export interface LoginData {
  email: string;
  password: string;
}

export function getMenuList() {
  return getRequest<any>('/api/user/menu', {});
}
// 获取短信验证码
export function getMsgCode(phone:string,smsType?:string): AxiosPromise<any> {
  return getRequest<any>(`/api/auth/message_auth_code?phone_number=${phone}&sms_type=${smsType || 'login'}`, {});
}
// 验证短信验证码
export function verifyMsgCode(phone:string, code:string): AxiosPromise<any> {
  return getRequest<any>(`/api/auth/verify_message_code?phone_number=${phone}&message_auth_code=${code}`, {});
}
