import {useWebSocket} from '@vueuse/core'
import {ReportQueryResponse} from "@/api/type";
import {computed} from "vue";
import {convertKeysToCamelCase, convertKeysToSnakeCase} from "@/utils/strUtil";

export const baseWsURL = process.env?.BASE_WS_URL

const token = localStorage.getItem('token');

// 暂ws仅用于传输报表数据，只有ws/dashboard唯一连接
// const {status, data, send, open, close} = useWebSocket(`${baseWsURL}/ws/dashboard?token=${token}`, {
const {status, data, send, open, close} = useWebSocket(`${baseWsURL}/api/v1/lm_websocket/query?token=${token}`, {
  autoReconnect: {
    retries: 3,
    delay: 5000,
    onFailed() {
      console.error('重试 3 次后无法连接 WebSocket！')
    },
  },
  onConnected(ws) {
    console.debug('websocket 连接成功！', ws)
  },
  onDisconnected(ws, event) {
    console.warn('websocket 连接断开连接！', ws)
  },
  onError(ws, event) {
    console.error('websocket 连接错误！', ws)
  },
  onMessage(ws, event) {
    // if (event.data && event.data !== 'pong') {
    // const info = JSON.parse(event.data)
    // console.log(info)
    // }
  },
  heartbeat: {
    message: 'ping',
    interval: 10000,
    pongTimeout: 10000,
  },
  autoClose: true,
})

export const wsSend = (msg: any) => {
  send(JSON.stringify(convertKeysToSnakeCase(msg)));
}

/**
 * websocket 返回值，去除心跳
 */
export const wsData: ReportQueryResponse = computed(() => {
  try {
    if (JSON.parse(data.value)){
      return convertKeysToCamelCase(JSON.parse(data.value))
    }else {
      return undefined
    }
  } catch (e) {
    return undefined
  }
})