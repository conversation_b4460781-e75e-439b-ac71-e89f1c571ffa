<template>
  <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
    <a-spin :loading="loading" style="width: 100%;height: 100%;">
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical" style="width: 100%;">
        <a-form-item field="name" label="名称">
          <a-input v-model="form.name"/>
        </a-form-item>
        <a-form-item field="categoryOrder" label="顺序">
          <a-input-number v-model="form.categoryOrder" :min="1"/>
        </a-form-item>
        <a-form-item field="relatedEventList" label="关联事件">
          <CustomSelectWithActions
            v-model="form.relatedEventList"
            :options="eventList"
            :max-tag-count="3"
            multiple
            allow-search
            placeholder="请选择关联事件"
          />
        </a-form-item>
        <a-form-item field="relatedIndicatorList" label="关联指标">
          <CustomSelectWithActions
            v-model="form.relatedIndicatorList"
            :options="indicatorList"
            :max-tag-count="3"
            multiple
            allow-search
            :show-regular-toggle="true"
            placeholder="请选择关联指标"
          />
        </a-form-item>
        <a-form-item field="note" label="描述">
          <a-textarea v-model="form.note" placeholder="请输入" style="height: 100px;"/>
        </a-form-item>
      </a-form>
      <div class="footer">
        <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
        <a-button type="primary" @click="saveData">
          保存
        </a-button>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import CustomSelectWithActions from "@/components/custom-select-with-actions/index.vue";
import {addRepCategoryList, getRepEventList, getRepIndicatorList} from "@/api/marketing/api";

const modalVisible = ref(false)
const modalTitle = ref('')
const loading = ref(false)
const form = reactive({
  code: '',
  name: '',
  note: '',
  relatedEventList: [],
  relatedIndicatorList: [],
  categoryOrder: undefined
})

const rules = {
  name: [
    {
      required: true,
      message: '请填写分组名称',
    }
  ],
  categoryOrder: [
    {
      required: true,
      message: '请填写顺序',
    }
  ],
}
const formRef = ref()

const eventList = ref<any>([])
const indicatorList = ref<any>([])
const getEventOptions = async (code?: string) => {
  await getRepEventList({categoryCode: code}).then(res => {
    eventList.value = res
    form.relatedEventList = eventList.value.filter(item => item.categoryCode).map(item => item.code)
  })
}
const getIndicatorOptions = async (code?: string) => {
  await getRepIndicatorList({categoryCode: code}).then(res => {
    indicatorList.value = res
    form.relatedIndicatorList = indicatorList.value.filter(item => item.categoryCode).map(item => item.code)
  })
}

const emits = defineEmits(['updateData']);

const openModal = async (obj?: any) => {
  loading.value = true
  formRef.value.resetFields()
  formRef.value.clearValidate()
  if (obj) {
    const {code, name, note, categoryOrder} = obj
    form.code = code
    form.name = name
    form.note = note
    form.categoryOrder = categoryOrder
    modalTitle.value = '编辑分组'
  } else {
    form.code = ''
    modalTitle.value = '创建分组'
  }
  modalVisible.value = true
  await Promise.all([
    getEventOptions(form.code),
    getIndicatorOptions(form.code)
  ]);
  loading.value = false
}


const closeModal = () => {
  modalVisible.value = false
}

const saveData = () => {
  formRef.value.validate(async (valid: any) => {
    if (!valid) {
      //
      try {
        await addRepCategoryList(form);
        if (form.code) {
          Message.success('保存成功')
        } else {
          Message.success('创建成功');
        }
        modalVisible.value = false;
        emits('updateData');
      } catch (error) {
        console.error('创建失败:', error);
      }
    }
  })
}


defineExpose(
    {
      openModal
    }
)
</script>

<style scoped lang="less">
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;

  button {
    border-radius: 4px;
  }
}

.cancel {
  background: transparent;
  margin-right: 8px;

  &:hover {
    background-color: var(--color-secondary);
  }
}

</style>