<template>
    <div style="width: 100%;height: 100%;">
        <a-spin :loading="loading" :style="!dataList.length ? 'width: 100%;min-height: 200px;' : 'width: 100%;'" class="card-box">
            <div v-for="(item,index) in dataList" :key="index" class="card-item">
                <div class="item-title">
                    <span>{{ item.name }}</span>
                    （单位：{{ item.unit || '-' }}）
                </div>
                <div class="item-num">
                    {{ formatNumber(item.value) }}
                </div>
                <div class="item-rate">
                    <span v-if="Number(item.qoq)>0" class="up"> ↑ {{item.qoq}}%</span>
                    <span v-else class="down"> ↓ {{item.qoq}}%</span>
                </div>
            </div>
            <a-empty v-if="!dataList.length" description="暂无数据" style="background: #fff;">
            </a-empty>
        </a-spin>
    </div>
</template>

<script setup lang="ts"> 
import { ref,watch } from 'vue'
import { getTeamOverview } from '@/api/analyse/api'

const props = defineProps({
    params: Object,
})

const loading = ref(false)
const dataList = ref<any>([])
const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}
const getData = async () => {
    const data: { teamCode?: string; appId?: string } = {};
    if (props.params?.team && props.params.pageType === 'team') {
        data.teamCode = props.params.team;
    }
    if (props.params?.appId && props.params.pageType === 'app') {
        data.appId = props.params.appId;
    }
    if (!data.teamCode && !data.appId) {
        dataList.value = [];
        return;
    }
    loading.value = true;
    try {
        const res = await getTeamOverview(data);
        dataList.value = res;
    } catch (error) {
        console.error('获取团队概览数据失败:', error);
        dataList.value = [];
    } finally {
        loading.value = false;
    }
}
watch(() => props.params, () => {
    getData()
},{deep: true})
</script>

<style scoped lang="less">
.card-box{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    .card-item{
        flex: 1 1 calc(16.666% - 14px);
        min-width: 180px;
        max-width: calc(16.666% - 14px); // 添加最大宽度限制
        padding: 12px;
        border-radius: 4px;
        background: #FFFFFF;
        box-sizing: border-box;
        .item-title{
            font-size: 14px;
            font-weight: bold;
            line-height: 30px;
            white-space: nowrap; 
        }
        .item-num{
            font-size: 32px;
            font-weight: bold;
            color: #242d41;
            line-height: 55px;
            text-align: center;
            white-space: nowrap;
        }
        .item-rate{
            font-size: 16px;
            text-align: center;
            .up {
                color: green;
            }
            .down {
                color: red;
            }
        }
    }
}
// 响应式设计
@media (max-width: 1400px) {
    .card-box .card-item .item-num {
        font-size: 28px;
    }
}

@media (max-width: 1200px) {
    .card-box .card-item .item-num {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .card-box .card-item .item-num {
        font-size: 20px;
    }
}
</style>