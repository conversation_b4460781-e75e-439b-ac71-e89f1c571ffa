<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          <select-app/>
        </div>
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          <a-select
              v-model:model-value="searchType"
              allow-clear
              :style="{width:'240px'}"
              placeholder="请选择指标类型"
              @change="refresh"
          >
            <a-option value="event">事件分析指标</a-option>
            <a-option value="retention">留存分析指标</a-option>
            <a-option value="operation">运营分析指标</a-option>
            <a-option value="group">群组分析指标</a-option>
            <a-option value="traffic">归因分析指标</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="refresh">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="batchEditGroup">
            分组批量编辑
          </a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="()=> {
            router.push({
              name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR_CREATE,
              query: {
                inApp: 1,
                pageType:'app'
              }
            })
          }">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #sourceChannel="{record}">
          <a-tag v-for="item in record.sourceChannel" :key="item">
            {{ getUserAttributeSourceChannelName(item) }}
          </a-tag>
        </template>
        <template #optional="{ record }">
          <a-button
              v-if="editIndicatorType.indexOf(record.type)>-1  && !_.isEmpty(record.appId)"
              type="text"
              @click="()=>{
                router.push({
                  name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR_CREATE,
                  query: {
                    code: record.code,
                    type:'edit',
                    inApp: 1,
                    pageType:'app'
                  }
                })
              }">
            编辑
          </a-button>
          <a-popconfirm  v-if="!record.isPredefined && !_.isEmpty(record.appId)" :content="`确认删除“${record.displayName}”?`" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm>
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal v-model:visible="editGroupShow" title-align="start" title="分组批量编辑" :footer="false" @cancel="closeModal">
      <a-form ref="groupFormRef" :model="groupForm" layout="vertical">
        <a-form-item field="code" label="关联分组" :rules="[{required:true,message:'请选择分组'}]">
          <a-select v-model="groupForm.code" allow-clear allow-search placeholder="请选择关联分组" @change="categoryCodeChange">
            <a-option v-for="item in repCategoryList" :key="item.code" :value="item.code">{{item.name}}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="relatedIndicatorList" label="关联事件">
          <CustomSelectWithActions
            v-model="groupForm.relatedIndicatorList"
            :options="indicatorList"
            :loading="relatedLoading"
            :max-tag-count="2"
            multiple
            allow-search
            placeholder="请选择关联事件"
          />
        </a-form-item>
      </a-form>
      <div class="footer" style="text-align: right;">
          <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
          <a-button type="primary" :loading="updateLoading" style="margin-right: 10px;" @click="batchChangeRepCategory('update')">
              更新
          </a-button>
          <a-button type="primary" :loading="updateLoading" @click="batchChangeRepCategory('apply')">
              应用
          </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import {getIndicatorList, removeIndicatorItem} from "@/api/setting/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {getIndicatorTypeName, getUserAttributeSourceChannelName, IndicatorType} from "@/api/enum";
import router from "@/router";
import {useRoute} from "vue-router";
import selectApp from "@/components/selected-game-app/index.vue"
import {useEventBus} from "@vueuse/core";
import {LocalStorageEventBus} from "@/types/event-bus";
import {ROUTE_NAME} from "@/router/constants";
import {getRepCategoryList, getRepIndicatorList, updateRepCategoryGroups} from "@/api/marketing/api";
import CustomSelectWithActions from "@/components/custom-select-with-actions/index.vue";


const localStorageEventBus = useEventBus(LocalStorageEventBus);
const route = useRoute();
const data = ref<any>([]);
const editIndicatorType = ref<IndicatorType[]>([IndicatorType.EVENT, IndicatorType.RETENTION, IndicatorType.OPERATION, IndicatorType.GROUP, IndicatorType.TRAFFIC]);
const deleteIndicatorType = ref<IndicatorType[]>([IndicatorType.EVENT, IndicatorType.RETENTION, IndicatorType.OPERATION]);
const loading = ref<boolean>(true);
const searchName = ref<string>(route.query.text as string || '');
const searchType = ref('')
const repCategoryList = ref<any>([]);
const columns = ref<any>([
  {
    title: '显示名',
    dataIndex: 'displayName',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '指标类型',
    dataIndex: 'type',
    render: (value) => {
      const {record} = value;
      return getIndicatorTypeName(record.type)
    },
    width: 180,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'description',
    width: 180,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '最后更新人',
    dataIndex: 'creator',
    slotName: 'creator',
    width: 120,
    align: 'center',
    render: (value) => {
      const {record} = value;
      return record?.updateUser?.name ? record.updateUser?.name : record.creator?.name
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    slotName: 'updateTime',
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 160
  },
]);

const refresh = () => {
  const params = {
    name: searchName.value,
  };
  loading.value = true
  getIndicatorList({...params, inApp: 1}).then(res => {
    const searchRegex = searchName.value ? new RegExp(searchName.value, 'i') : null;
    const list = _.isEmpty(searchName.value) ? res : res?.filter(item => {
      return searchRegex ? (searchRegex.test(item.name) || searchRegex.test(item.displayName)) : true;
    })
    data.value = _.isEmpty(searchType.value) ? list : list?.filter(item => {
      return item.type === searchType.value
    })
  }).finally(() => {
    loading.value = false
  })
}

localStorageEventBus.on((name, value) => {
  if (name === "app-id") refresh()
})

onMounted(() => {
  getRepCategoryList().then(res => {
    repCategoryList.value = res
  })
  refresh()
})

const searchData = () => {
  // 更新路由参数
  const query = { ...route.query };
  if (searchName.value) {
    query.text = searchName.value;
  } else {
    delete query.text;
  }
  router.replace({ query });
}

const deleteData = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除指标编码不能为空！")
  }
  removeIndicatorItem(code).then(res => {
    Message.info("指标数据删除成功！")
    refresh()
  }).catch(e => {
    Message.error("指标数据删除失败！", e)
  })
}
const groupForm = ref<any>({})
const editGroupShow = ref<boolean>(false);
const groupFormRef = ref()
const updateLoading = ref(false)
const indicatorList = ref<any>([])
const relatedLoading = ref(false)

const getIndicatorOptions = async (code?:string) => {
    relatedLoading.value = true
    await getRepIndicatorList({categoryCode:code,inApp:1}).then(res => {
        indicatorList.value = res
        groupForm.value.relatedIndicatorList = indicatorList.value.filter(item => item.categoryCode).map(item => item.code)
    })
    relatedLoading.value = false
}
const categoryCodeChange = () => {
  getIndicatorOptions(groupForm.value.code)
}
const batchEditGroup = async () => {
  groupFormRef.value.clearValidate()
  groupFormRef.value.resetFields()
  indicatorList.value = []
  editGroupShow.value = true;
}
const closeModal = () => {
  editGroupShow.value = false;
}
const batchChangeRepCategory =  (type:string) => { 
  groupFormRef.value.validate().then(async (valid) => {
    if (!valid) {
      try {
        updateLoading.value = true;
        await updateRepCategoryGroups(groupForm.value);
        Message.info("分组批量编辑成功！");
        if (type === 'update') {
          editGroupShow.value = false;
        }
      } catch (e) {
        Message.error("分组批量编辑失败！" + e);
      } finally {
        updateLoading.value = false;
      }
    }
  })
}
</script>

<style scoped lang="less">

</style>