<template>
    <a-spin :loading="dataLoading" class="form-content">
        <a-form ref="formRef" :model="form" :rules="rules" label-align="left" style="width: 100%;">
            <a-form-item field="name" label="实验名称" validate-trigger="blur">
                <a-input v-model="form.name" max-length="200" placeholder="请输入专属名称，最长不超过200字符，例：App端场景实验-版本3.0"/>
            </a-form-item>
            <a-form-item field="description" label="实验描述">
                <a-textarea v-model="form.description" max-length="1000" placeholder="请输入描述信息，可简述当前实验的含义、目的、内容等信息，便于后期查看和管理，支持1000个字符以内  例：测试苹果和安卓手机App端双十二运营场景推送形式及策略"/>
            </a-form-item>
            <a-form-item field="tags" label="实验标签" tooltip="在实验列表中可以通过实验标签快速搜索到相关实验，一般采用业务线、优化方向等实验内容关键词作为标签。">
                <a-select v-model:model-value="form.tags" :loading="tagLoading" placeholder="请选择" multiple allow-search allow-create>
                    <a-option v-for="item of options" :key="item" :value="item">{{item}}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="duration" label="实验时长" validate-trigger="blur">
                <a-input-number v-model="form.duration" :step="1" :min="1" style="width: 100px;">
                    <template #suffix>
                       天
                    </template>
                </a-input-number>
                <template #extra>
                    <div>
                        建议至少覆盖两个完整的实验周期(14天)
                    </div>
                </template>
            </a-form-item>
            <a-form-item field="dataSource" label="数据来源">
                <selectDataSource v-model:data-source="form.dataSource" disable-storage/>
            </a-form-item>
            <a-form-item label="">
                <a-button
                    type="primary"
                    style="border-radius: 4px;margin-right: 24px;"
                    :loading="loading"
                    @click="handleSubmit">
                    下一步
                </a-button>
                <a-button style="border-radius: 4px;" :loading="loading" @click="cancleEdit">
                    取消
                </a-button>
            </a-form-item>
        </a-form>
    </a-spin>
</template>

<script setup lang="ts">
import {ref,reactive} from "vue";
import {saveExperiment,getExperimentTagList,getExperimentDetail} from "@/api/ab/api";
import selectDataSource from "@/components/selected-data-source/index.vue"
import {Message} from '@arco-design/web-vue';

const props = defineProps({
    code: {
        type: String,
        default: ''
    }
})
const form = reactive({
    code:'',
    name: '',
    description:'',
    tags:[],
    duration: 30,
    type:1,
    dataSource:''
})
const rules = {
    name: [
        {
            required: true,
            message:'请填写实验名称',
        }
    ],
    duration: [
        {
            required: true,
            message:'请填写实验时长',
        }
    ]
}
const loading = ref(false)
const formRef = ref();
const options = ref<any>([]);
const tagLoading = ref(false);
const emits = defineEmits(['toNext','cancleEdit'])
const getTags = async () => {
    tagLoading.value = true;
    try {
      const res = await getExperimentTagList();
      options.value = res || [];
    } catch (e) {
    //   options.value = [];
    } finally {
      tagLoading.value = false;
    }
};
getTags()
const handleSubmit = async () => {
    try {
        await formRef.value.validate(async (valid:any) => {
            if (!valid) {
                loading.value = true
                try {
                    form.code = props.code || ''
                    const res = await saveExperiment({
                        ...form,
                        remainEditStep: 3
                    })
                    emits('toNext',res.code)
                } catch (err) {
                    console.error('保存实验失败', err)
                }
                loading.value = false
            }
        })
    } catch (e) {
        // 校验异常处理
        console.error('表单校验异常', e)
    }
};
const cancleEdit = () => {
    emits('cancleEdit')
}
const dataLoading = ref(false)
const init = async () => {
    if (props.code) {
        dataLoading.value = true
        try {
            const res = await getExperimentDetail(props.code)
            form.name = res.name || ''
            form.description = res.description || ''
            form.tags = res.tags || []
            form.duration = res.duration || 30
            form.dataSource = res.dataSource || ''
        }catch (e) {
          console.log('获取实验详情失败',e);
        }finally {
            dataLoading.value = false
        }
    }
}
init()
</script>

<style scoped lang="less">
.form-content{
  width: 100%;
  height: 100%;
}
</style>