<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {computed, ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import {cloneDeep, debounce} from "lodash";
import dayjs from 'dayjs';
import {useSessionStorage} from '@vueuse/core';

interface Props {
  /**
   * 展示label
   */
  showlabel: boolean

  /**
   * 报表数据
   */
  eventData: any;
  // y最大值
  yMax: number | null
  // y最小值
  yMin: number | null
  retentionType:string // 留存、流失
  chartValueFormat:string // 比例、人数
}

const props = defineProps<Props>()


const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([{selected:{}}]);
const selectedAttribute = ref<string[]>([]);

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])

const indeterminate = ref(false)
const checkedAll = ref(false)

const groupsInput = ref('') // 分组搜索
const groups = ref<any>([]) // 分组
const selectedGroups = ref<string[]>([]);
const checkedDefault = ref(true)
const checkedSessionGroups = useSessionStorage('checkedSessionGroups', [])
groups.value = props.eventData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
}).filter(item => item !== '总体') || [];

const rateOrNum = ref('rate') // 比例、人数
const retentionWay = ref('retention') // 留存、流失
const chartTypeValue = ref('dTrend')

const formatNDate = (value) => {
  const [month, day] = value.split('/'); // 分割 MM 和 DD
  const currentYear = dayjs().year(); // 获取当前年份
  const date = dayjs(`${currentYear}-${month}-${day}`).format('YYYY-MM-DD'); // 使用当前年份构建日期
  const dateStr = new Date(date);
  const days = ['日', '一', '二', '三', '四', '五', '六'];
  return `${date}(${days[dateStr.getDay()]})`;
}
function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);

const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '6%',
      right: '2.6%',
      top: '60',
      bottom: '60',
    },
    legend: {
      data: legendData.value,
      bottom: '10',
      type: 'scroll', // 设置图例为滚动类型
      orient: 'horizontal', // 横向显示图例

    },
    xAxis: {
      type: 'category',
      data: xAxis.value
    },
    yAxis: {
      type: 'value',
      // max:100,
      min:props.yMin,
      axisLabel: {
        formatter: rateOrNum.value === 'rate' ? '{value} %' : '{value}'
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine:true,
      axisPointer: {
        type: axisPointerType.value
      },
      // valueFormatter: (value) => `${value}%`,
      formatter: (params) => {
          const seriesInfo = params.map((param, index) => {
            let valueDisplay;
            if (param.value!== undefined && param.value!== null) {
                if (rateOrNum.value === 'rate') {
                    valueDisplay = `${param.value}%`;
                } else {
                    valueDisplay = formatNumber(param.value);
                }
            } else {
                valueDisplay = '-';
            }
            return `
                  <div style="display: flex; align-items: center; margin-bottom: 5px;">
                    <span style="flex-shrink: 0;">${param.marker}</span>
                    <span style="flex: 1; min-width: 0; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; margin-right: 10px;" title="${param.seriesName}">${param.seriesName}</span>
                    <span style="flex-shrink: 0;">${valueDisplay}</span>
                  </div>
              `;
          }).join('');
          const tooltipHtml = `
            <div style="width: 300px; max-height: 200px; overflow-y: auto; padding-right: 10px;">
              <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-weight: bold;margin-bottom: 10px;width: 100%;" title="${params[0].axisValue}">${chartTypeValue.value === 'nTrend' ? formatNDate(params[0].axisValue) : params[0].axisValue}</div>
              ${seriesInfo}
            </div>
          `;
          return tooltipHtml;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

const showTimeGroup = ref(false)
const freshData =  debounce((type?:string) => {
  legendData.value = []
  chartTypeValue.value = type || chartTypeValue.value
  axisPointerType.value = 'line'
  const timeSpanNumber = props.eventData?.timeSpan?.number
  const retentionRateData = props.eventData?.retentionQueryResult.find(item => item.type === `${retentionWay.value}_rate`).resultData;
  const retentionData = props.eventData?.retentionQueryResult.find(item => item.type === `${retentionWay.value}_num`).resultData;
  const handleData = rateOrNum.value === 'rate' ? cloneDeep(retentionRateData) : cloneDeep(retentionData)

  const unit = props.eventData?.timeSpan?.unit
  let unitName = '' // 单位
  if(unit === 'DAY'){
    unitName = '日'
  }else if(unit === 'WEEK'){
    unitName = '周'
  }else{
    unitName = '月'
  }
  let dateList = []
  if(retentionWay.value==='churn'){
    dateList = Array.from({ length: timeSpanNumber }, (_, index) => `${index + 1}${unitName}`);
  }else{
    dateList = Array.from({ length: timeSpanNumber+1 }, (_, index) => {
        return index === 0 ? `当${unitName}` : `第${index}${unitName}`;
    });
  }
  const result = [] as any;
  // 从 index=1 开始遍历数据  取第timeSpanNumber天数据
  handleData.slice(1).forEach(item => {
      const nValue = rateOrNum.value === 'rate' ? Number((item.summaryData[timeSpanNumber] * 100).toFixed(2)) : item.summaryData[timeSpanNumber];
      if (nValue !== undefined && nValue !== null && !Number.isNaN(nValue)) {
          result.push({
              date: item.date,
              values: nValue,
              groupData:item.groupData
          });
      }
  });
  if(chartTypeValue.value === 'nTrend'){
    xAxis.value = result.map(item =>  dayjs(item.date).format('MM/DD'))
    const yData = [{
      name: '总体',
      data: result.map(item =>  item.values),
      type: 'line',
      label: {
        show: props.showlabel
      },
    }]
    if(groups.value.length>0){
      const list  = result.map(item => item.groupData).flat()
      const groupResult = [] as any;
      // 遍历数据
      list.forEach(item => {
        const groupKey = item.group.join(','); // 获取分组键
        const nValue = item.values[timeSpanNumber]
        // 仅在 nValue 存在时进行处理
        if (nValue !== undefined && nValue !== null) {
            // 查找是否已存在该组
            const existingGroup = groupResult.find(g => g.group === groupKey);
            if (existingGroup) {
                // 如果组已存在，添加 nValue 到 values 数组
                existingGroup.values.push(Number((nValue * 100).toFixed(2)));
            } else {
                // 如果组不存在，创建新组并添加 nValue
                groupResult.push({
                    group: groupKey,
                    values: [Number((nValue * 100).toFixed(2))]
                });
            }
        }
      });
      const yGroupData = groupResult.map((item) => {
        return {
          name: item.group,
          data: item.values,
          type: 'line',
          label: {
            show: props.showlabel
          },
        }
      })
      ySeries.value = yData.concat(yGroupData)
    }else{
      ySeries.value = yData
    }
  }else{
    xAxis.value = dateList
    ySeries.value = []
    const startIndex = rateOrNum.value === 'rate' ? 0 : 1;
    if(groups.value.length>0){
      const sumData = [{
        name: '总体',
        data: handleData[0]?.summaryData.slice(startIndex).map(item => rateOrNum.value === 'rate' ? Number((item * 100).toFixed(2)) : item),
        type: 'line',
        label: {
          show: props.showlabel
        },
      }]
      const groupSeries = handleData[0]?.groupData.map(item => {
        return {
          name: item.group.join(','),
          data: item.values.slice(startIndex).map(ele =>rateOrNum.value === 'rate' ? Number((ele * 100).toFixed(2)) : ele),
          type: 'line',
          label: {
            show: props.showlabel
          },
        }
      })
      ySeries.value = sumData.concat(groupSeries)
    }else{
      ySeries.value = handleData.map(item => {
        return {
          name:item.date,
          data:item.summaryData.slice(startIndex).map(ele => rateOrNum.value === 'rate' ? Number((ele * 100).toFixed(2)) : ele),
          type: 'line',
          label: {
              show: props.showlabel // 假设 props.showlabel 是一个布尔值
          }
        }
      })
      showTimeGroup.value = true
    }
  }
  if(ySeries.value.length>1){
    legendData.value = ySeries.value.map(item => item.name)
    // selectedGroups.value = legendData.value.slice(0,10) || []
    // checkedDefault.value = true;
    const validGroups = checkedSessionGroups.value.filter(item => legendData.value.includes(item));
    const defaultLimit = Math.min(10, legendData.value.length);
    
    selectedGroups.value = validGroups.length > 0 
      ? validGroups 
      : legendData.value.slice(0, defaultLimit);
    checkedDefault.value = validGroups.length === 0;
  }
  ySeriesData.value = cloneDeep(ySeries.value)
}, 500);

watch(() => props.chartValueFormat,(newValue) => {
    rateOrNum.value = newValue
    freshData()
},{immediate:true,deep:true})
watch(() => props.retentionType,(newValue) => {
    retentionWay.value = newValue
    freshData()
},{immediate:true,deep:true})
const changeLegend = (value:any,ev:any)=>{

}

const updateYAxisSeries = () => {
  ySeries.value = ySeriesData.value.filter(range => selectedAttribute.value.includes(range.name))
};


freshData()

watch(selectedAttribute, (newValue:any,oldValue:any) => {
  if(newValue){
    updateYAxisSeries();
  }
})

watch(() => props.showlabel,(newValue:any,oldValue:any) => {
  ySeries.value.forEach((item:any,index:number)=>{
    item.label = {
      show: props.showlabel,
      formatter: (params) => formatNumber(params.value)
    }
  })
  
},{immediate:true})


// 全选

const handleChangeAll = (value) => {
  indeterminate.value = false;
  if (value) {
    checkedAll.value = true;
    selectedAttribute.value = legendData.value
  } else {
    checkedAll.value = false;
    selectedAttribute.value = []
  }
}
const handleChange = (values) => {
  if (values.length === legendData.value.length) {
    checkedAll.value = true
    indeterminate.value = false;
  } else if (values.length === 0) {
    checkedAll.value = false
    indeterminate.value = false;
  } else {
    checkedAll.value = false
    indeterminate.value = true;
  }
}
const attributeInput = ref('')
const filteredAttribute = computed(() => {
  const str = attributeInput.value.trim();
  return legendData.value.filter(item => item.includes(str));
});

// 默认选中的分组数设置
const showSide = ref(false)
const limitCustom = ref(10)
const limitType = ref('less')
const sureType = ref('less')
const limitGroupNumber = ref(10)
const handleDefaultGroupNum = () => {
  limitType.value = sureType.value
  showSide.value = !showSide.value
}
const limitChange = (v) => {
}
const limitCancel = () => {
  showSide.value = false
}
const limitSure = () => {
  sureType.value = limitType.value
  if(sureType.value === 'less') {
    limitGroupNumber.value = 10
    limitCustom.value = 10
  }else if( sureType.value ==='middle'){
    limitGroupNumber.value = 20
    limitCustom.value = 20
  }else if( sureType.value ==='more'){
    limitGroupNumber.value = 30
    limitCustom.value = 30
  }else{
    limitGroupNumber.value = limitCustom.value
  }
  const copyGroups = cloneDeep(legendData.value);
  selectedGroups.value = copyGroups.slice(0, limitGroupNumber.value);
  showSide.value = false
}
const popChange = () => {
  showSide.value = false
}
const filteredGroups = computed(() => {
  const str = groupsInput.value.trim().toLowerCase()
  return legendData.value.filter(item => item.toLowerCase().includes(str));
});
const checkedChangeDefault = (value) => {
  if(value){
    const copyGroups = cloneDeep(legendData.value);
    checkedDefault.value = true;
    selectedGroups.value = copyGroups.slice(0, limitGroupNumber.value);
  }else{
    checkedDefault.value = false
    selectedGroups.value = []
  }
}
const handleChangeGroups = (values) => {
  checkedSessionGroups.value = values
}
watch(selectedGroups, (newValue:any,oldValue:any) => {
  if(newValue){
    ySeries.value = ySeriesData.value.filter(range => selectedGroups.value.some(item => item === range.name))
  }
})

defineExpose({
  freshData
})

</script>

<template>
  <div class="chart-content">
    <a-spin style="flex: 1 1 0; display: flex; flex-direction: column; height: 100%;">
      <div style="display: flex;justify-content: space-between;margin-top: 5px;">
        <div>
        </div>
        <div style="display: flex;">
          <!-- 关联属性 -->
          <!-- <a-dropdown :hide-on-select="false" :update-at-scroll="true">
            <div style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;">关联属性( {{selectedAttribute.length}}/{{legendData.length}})<icon-down/></div>
            <template #content>
                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                  <a-input v-model="attributeInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                    <template #prefix>
                      <icon-search />
                    </template>
                  </a-input>
                </div>
                <a-checkbox-group v-model="selectedAttribute" direction="vertical" style="width: 100%;" @change="handleChange">
                    <a-checkbox v-for="(item,index) in filteredAttribute" :key="index" :value="item">
                      <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                    </a-checkbox>
                </a-checkbox-group>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);display: flex;align-items: center;">
                <a-checkbox :model-value="checkedAll" :indeterminate="indeterminate" style="width: 100%;" @change="handleChangeAll">全选</a-checkbox>
              </div>
            </template>
          </a-dropdown> -->
          <!-- 分组项 -->
          <a-dropdown :hide-on-select="false" :update-at-scroll="true" @popup-visible-change="popChange">
            <div
              v-if="legendData.length>0 || showTimeGroup"
              style="min-height: 32px;margin-left: 16px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;cursor: pointer;"
              >
              <span v-if="showTimeGroup">时间</span>
              <span v-else>分组</span>
              ( {{ selectedGroups.length }}/{{legendData.length}})<icon-down/>
            </div>
            <template #content>
                <div style="min-width: 160px;border-bottom: 1px solid var(--tant-border-color-border1-1);">
                  <a-input v-model:model-value="groupsInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                    <template #prefix>
                      <icon-search />
                    </template>
                  </a-input>
                </div>
                <a-checkbox-group v-if="filteredGroups.length" v-model="selectedGroups" direction="vertical" style="width: 100%;" @change="handleChangeGroups">
                    <a-checkbox v-for="(item,index) in filteredGroups" :key="index" :value="item">
                      <a-doption style="min-width: 160px;">{{ item }}</a-doption>
                    </a-checkbox>
                </a-checkbox-group>
                <a-empty v-else/>
            </template>
            <template #footer>
              <div style="width: 100%;line-height: 32px;background-color: var(--color-fill-2);display: flex;align-items: center;">
                <a-checkbox :model-value="checkedDefault" style="width: 100%;" @change="checkedChangeDefault">默认</a-checkbox>
                <div class="icon-set" @click="handleDefaultGroupNum">
                  <icon-settings />
                </div>
              </div>
              <!-- 默认设置 -->
              <div v-if="showSide" class="ta-default-group-side-left">
                <div class="group-side">
                  <p class="group-side-head">默认选中的分组数</p>
                  <div class="group-side-body">
                    <a-radio-group v-model:model-value="limitType" direction="vertical" style="width: 100%;" @change="limitChange">
                      <a-radio value="less">
                        <div class="radio-desc">
                          <span>少</span>
                          <span class="limit-set">10</span>
                        </div>
                      </a-radio>
                      <a-radio value="middle">
                        <div class="radio-desc">
                          <span>中</span>
                          <span class="limit-set">20</span>
                        </div>
                      </a-radio>
                      <a-radio value="more">
                        <div class="radio-desc">
                          <span>多</span>
                          <span class="limit-set">30</span>
                        </div>
                      </a-radio>
                      <a-radio value="custom">
                        <div class="radio-desc">
                          <span>自定义分组</span>
                          <a-input-number v-model:model-value="limitCustom" :disabled="limitType !== 'custom'" :style="{width:'66px',height:'24px'}" :precision="0" :min="1" :hide-button="true"/>
                        </div>
                      </a-radio>
                    </a-radio-group>
                    <div class="group-info-wrap">
                      <p class="group-info">默认选中前<b class="bold">{{limitGroupNumber}}</b>个分组项</p>
                      <p class="group-info">分组项与当前排序设置一致</p>
                      <p class="group-info">仅柱图支持 50 个分组项以上的展示</p>
                    </div>
                  </div>
                  <div class="group-side-foot">
                    <a-button class="cancel" @click="limitCancel">取消</a-button>
                    <a-button type="primary" @click="limitSure">确定</a-button>
                  </div>
                </div>
              </div>
            </template>
          </a-dropdown>
        </div>
      </div>
      <Chart :option="chartOption" style="flex: 1 1 0; height: 100%;"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
:deep(.arco-dropdown-option){
  padding: 0;
}
:deep(.arco-checkbox){
  padding: 0 12px;
  &:hover{
    background-color: var(--color-fill-2);
  }
}

.icon-set{
  padding-right: 12px;
  cursor: pointer;
  color: var(--tant-primary-color-primary-hover);
}
.cancel {
  background: transparent;
  margin-right: 8px;
  &:hover {
    background-color: var(--color-secondary);
  }
}
.ta-default-group-side-left{
  position: absolute;
  top: 0;
  left: -285px;
  width: 280px;
  
  height: 0;
  .group-side{
    width: 100%;
    padding: 16px 16px 8px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: none;
    border-radius: 4px;
    box-shadow: var(--tant-small-shadow-small-bottom);
    .group-side-head {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 30px;
        margin-bottom: 16px;
        padding-left: 0;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }
    .group-side-body {
        min-height: 220px;
        padding-left: 0;
        .radio-desc{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .limit-set {
              color: var(--tant-text-gray-color-text1-3);
          }
        }
        :deep(.arco-radio-label){
          width: 100%;
        }
        .group-info-wrap {
          margin-top: 24px;
          margin-bottom: 24px;
          .group-info {
            width: 100%;
            margin-bottom: 8px;
            margin-left: 0;
            color: var(--tant-text-gray-color-text1-3);
            font-size: 14px;
            line-height: 22px;
            .bold {
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
            }
          }
          .group-info:before {
              position: relative;
              top: 3px;
              padding-right: 10px;
              font-weight: 500;
              font-size: 20px;
              content: "\b7";
          }
      }
    }
    .group-side-foot{
      margin-right: -16px;
      margin-left: -16px;
      padding: 8px 16px 0;
      text-align: right;
      border-top: 1px solid var(--tant-border-color-border1-1);
      button{
        border-radius: 4px;
      }
    }
  }
}
</style>