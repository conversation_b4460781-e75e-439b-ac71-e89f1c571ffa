/** -------------------------------------------------- * */
// eventbus常量
/** -------------------------------------------------- * */

/**
 * 看板事件总线
 */
export const DashboardEventBus = "dashboard"

/**
 * 菜单事件总线
 */
export const TreeMenuEventBus = "tree-menu"

/**
 * 登录事件总线
 */
export const LogEventBus = "log"

/**
 * 数据管理事件总线
 */
export const DateManageEventBus = "mange-event"

/**
 * 缓存数据修改总线
 */
export const CacheEventBus = "cache-event"

/**
 * 本地缓存
 */
export const LocalStorageEventBus = "local-storage"

/** -------------------------------------------------- * */
// event常量
/** -------------------------------------------------- * */

/**
 * 登录事件
 */
export const LoginEvent = "login"

/**
 * 登出事件
 */
export const LogoutEvent = "logout"


/**
 * 刷新事件
 */
export const RefreshEvent = "refresh"

/**
 * 选中事件
 */
export const SelectedEvent = "selected-event"

/**
 * 保存事件
 */
export const SaveEvent = "save-event"
/**
 * 缓存重命名事件
 */
export const RenameEvent = "rename-event"
/**
 * 缓存删除事件
 */
export const DeleteEvent = "delete-event"
/**
 * 缓存移动事件
 */
export const MoveEvent = "move-event"
/**
 * 缓存复制事件
 */
export const CopyEvent = "copy-event"
/**
 * 缓存添加事件
 */
export const AddEvent = "add-event"
// 缓存报表数量
export const UpdateReportCountEvent = 'update-report-count-event'

/**
 * 看板选中事件载荷类型
 */
export interface DashboardSelectedEventPayload {
  /**
   * 所在空间
   */
  spaceId?: string,

  /**
   * 所处文件夹
   */
  folderId?: string,

  /**
   * 看板编码
   */
  dashboardId?: string,
}
