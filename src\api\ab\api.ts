import {AxiosPromise} from "axios";
import {getRequest, postRequest} from "@/api/request";
import {getRequestMock, postRequestMock} from "@/api/request.mock";

// 实验列表
export function getExperimentList(params: any): AxiosPromise<any> {
    // 处理tag为多参数格式
    const query = {...params};
    if (query.pageSize !== undefined) {
        query.page_size = query.pageSize;
        delete query.pageSize;
    }
    if (Array.isArray(query.tag)) {
        // 删除原有tag
        const tags = query.tag;
        delete query.tag;
        // 拼接tag参数
        const searchParams = new URLSearchParams();
        Object.entries(query).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                searchParams.append(key, value as string);
            }
        });
        tags.forEach(tag => {
            searchParams.append('tag', tag);
        });
        return getRequest<any>(`/api/ab/experiment/list?${searchParams.toString()}`, {});
    }
    return getRequest<any>('/api/ab/experiment/list', {...params});
}

// 实验详情
export function getExperimentDetail(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/experiment/detail', {code});
}

// 实验报告详情
export function getExperimentReportDetail(code:string): AxiosPromise<any> {
  return getRequest<any>('/api/ab/experiment/report/summary', {code});
}

// 实验报告详情
export function getExperimentReportDetailMock(code:string): AxiosPromise<any> {
  return getRequestMock<any>('/api/ab/experiment/report/summary', {code});
}

// 实验数据分析
export function postExperimentAnalyticsQuery(params: any): AxiosPromise<any> {
  return postRequest<any>('/api/ab/experiment/analytics/query', {...params});
}

// 实验数据分析
export function postExperimentAnalyticsQueryMock(params: any): AxiosPromise<any> {
  return postRequestMock<any>('/api/ab/experiment/analytics/query', {...params});
}

// 实验删除
export function deleteExperimentItem(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/experiment/delete', {code});
}
// 实验启动
export function startExperiment(code:string): AxiosPromise<any> {
    return postRequest<any>(`/api/ab/experiment/start?code=${code}`, {});
}
// 实验停止
export function stopExperiment(code:string): AxiosPromise<any> {
    return postRequest<any>(`/api/ab/experiment/stop?code=${code}`, {});
}
// 实验复制
export function copyExperiment(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/experiment/copy', {code});
}
// 实验保存
export function saveExperiment(params: any): AxiosPromise<any> {
    return postRequest<any>('/api/ab/experiment/save', {...params});
}
// 实验标签查询
export function getExperimentTagList(): AxiosPromise<any> {
    return getRequest<any>(`/api/ab/experiment/tag/list`, {});
}
// 实验变体参数冲突校验
export function checkExperimentConflict(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/experiment/conflict/check', {code});
}
// 流量层列表
export function getLayerList(params: any): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/layer/list', {...params});
}
// 流量层详情
export function getLayerDetail(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/layer/detail', {code});
}
// 流量层删除
export function deleteLayerItem(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/layer/delete', {code});
}
// 流量层保存
export function saveLayer(params: any): AxiosPromise<any> {
    return postRequest<any>('/api/ab/flow/layer/save', {...params});
}

// 互斥域列表
export function getDomainList(params: any): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/domain/list', {...params});
}

// 互斥域组列表
export function getDomainGroupList(params: any): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/domain/group/list', {...params});
}
// 互斥域组保存
export function saveDomainGroup(params: any): AxiosPromise<any> {
    return postRequest<any>('/api/ab/flow/domain/group/save', {...params});
}
// 互斥域组详情

export function getDomainGroupDetail(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/domain/group/detail', {code});
}
// 互斥域组删除
export function deleteDomainGroupItem(code:string): AxiosPromise<any> {
    return getRequest<any>('/api/ab/flow/domain/group/delete', {code});
}

// 用户白名单列表
export function getUserWhiteList(): AxiosPromise<any> {
  return getRequest<any>('/api/data/whitelist/list');
}
// 用户白名单保存
export function saveUserWhiteList(data: any): AxiosPromise<any> {
  return postRequest<any>('/api/data/whitelist/save', data);
}
// 用户白名单删除
export function deleteUserWhiteList(code: string): AxiosPromise<any> {
  return getRequest<any>('/api/data/whitelist/delete', {code});
}