<template>
  <div>
    <!-- 指标下拉筛选 -->
    <a-trigger
        v-model:popup-visible="triggerVisible"
        trigger="click"
        :unmount-on-close="false"
        position="bl"
        :update-at-scroll="true"
        style="z-index: 1002;"
        @click="handleTriggerVisible"
    >
      <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
        <div class="filter-btn">
          <svg
              class="btn-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
              fill="currentColor"
          >
            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
              <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
            </svg>
          </svg>
          <span class="filter-label">{{ selectObject.indicatorDisplayName }}</span>
        </div>
        <template #content>
          <div class="trigger-box">
            <div class="card-header">
              <div class="card-header-container">
                <div class="header-icon">
                  <svg xmlns="http://www.w3.org/2000/svg"
                       width="1em"
                       height="1em"
                       viewBox="0 0 24 24"
                       fill="currentColor"
                  >
                    <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                      <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                    </svg>
                  </svg>
                </div>
                <div class="header-title">
                  <div class="name">{{ selectObject.indicatorDisplayName }}</div>
                </div>
                <div v-if="selectObject.indicatorType === 'operation_app'" class="header-type">应用指标</div>
                <div v-if="selectObject.indicatorType === 'operation_revenue'" class="header-type">变现指标</div>
                <div v-if="selectObject.indicatorType === 'operation_spend'" class="header-type">投放指标</div>
              </div>
              <div class="title-sub">{{ selectObject.indicatorName }}</div>
            </div>
            <div class="card-desc">
              <div v-if="selectObject.indicatorNote" class="span-desc">{{ selectObject.indicatorNote }}</div>
              <div v-else class="span-desc">暂无备注</div>
            </div>
            <div class="card-footer">
              <div></div>
              <div class="action">
                <a-tooltip content="前往指标详情" position="top">
                  <div class="action-icon" @click="toIndicatorDetail">
                    <icon-launch/>
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <template #content>
        <div class="select-panel">
          <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
            <a-input v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
              <template #prefix>
                <icon-search/>
              </template>
            </a-input>
          </div>
          <div v-if="searchInput === ''" class="list-container">
            <div class="list-category">
              <div class="category-container">
                <div
                    v-for="(item, index) in panelList"
                    :key="index"
                    class="category-item"
                    :class="{
                      'item-active': activeIndex === index,
                    }"
                    @click="categoryChange(item.categoryName,index)"
                >{{ item.categoryName }}
                </div
                >
              </div>
              <a-button>管理分组</a-button>
            </div>
            <div class="item-list">
              <div ref="selectListRef" class="select-list">
                <a-list ref="virtualList" :virtual-list-props="{ height: 350 }" :data="virtualPanelList" @scroll="handleScroll">
                  <template #item="{ item, index }">
                    <a-list-item :key="index">
                      <div class="list-content">
                        <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <icon-star v-if="index == 0" class="star"/>
                          <span>{{ item.categoryName }}</span>
                        </div>
                        <div v-if="index == 0 && !panelList[0].itemData.length" class="favorite" style="height: 30px;width: 100%;">
                          点击选项右
                          <icon-star/>
                          侧添加收藏
                        </div>
                        <div v-if="item.indicatorDisplayName" class="list-box" :class="{ 'list-active': item.indicatorCode == selectObject.indicatorCode }"
                             style="height: 30px; width: calc(100% - 16px)"
                             @click="listChange(item)">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                            <div>
                              <span class="desc">{{ item.indicatorDisplayName }}</span>
                              <a-tooltip v-if="!item.isCollect" content="点击收藏" position="top">
                                <div class="icon" @click.stop="starClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                              <a-tooltip v-else content="取消收藏" position="top">
                                <div class="isStar" @click.stop="cancelClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24"
                                          fill="currentColor"
                                      >
                                        <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                          <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                        </svg>
                                      </svg>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.indicatorDisplayName }}</div>
                                    </div>
                                    <div v-if="item.indicatorType === 'operation_app'" class="header-type">应用指标</div>
                                    <div v-if="item.indicatorType === 'operation_revenue'" class="header-type">变现指标</div>
                                    <div v-if="item.indicatorType === 'operation_spend'" class="header-type">投放指标</div>
                                  </div>
                                  <div class="title-sub">{{ item.indicatorName }}</div>
                                </div>
                                <div class="card-desc">
                                  <div class="span-desc">{{ item.indicatorNote || '暂无备注' }}</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <a-tooltip content="前往指标详情" position="top">
                                      <div class="action-icon" @click="toIndicatorDetail">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </div>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
          <div v-else class="list-container" style="padding-top: 16px;">
            <div class="item-list">
              <div class="list-metric">
                <a-list :virtual-list-props="{ height: 350 }" :data="filteredLists">
                  <template #item="{ item, index }">
                    <a-list-item :key="index">
                      <div class="list-content">
                        <div v-if="item.categoryName" class="list-name" :style="index == 0 ?{width: '100%',borderTop:'none',marginTop:0} : {width: '100%'}">
                          <icon-star v-if="item.categoryName.includes('收藏')" class="star"/>
                          {{ item.categoryName }}
                        </div>
                        <div v-if="item.indicatorDisplayName" class="list-box" :class="{ 'list-active': item.indicatorDisplayName == selectObject.indicatorDisplayName, }"
                             style="height: 30px; width: calc(100% - 16px)" @click="itemChange(item)">
                          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true">
                            <div>
                              <span class="desc1">{{ item.indicatorDisplayName }}</span>
                              <span class="desc2">{{ item.indicatorName }}</span>
                              <a-tooltip v-if="!item.isCollect" content="点击收藏2" position="top">
                                <div class="icon" @click.stop="starClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                              <a-tooltip v-else content="取消收藏" position="top">
                                <div class="isStar" @click.stop="cancelClick(item)">
                                  <icon-star/>
                                </div>
                              </a-tooltip>
                            </div>
                            <template #content>
                              <div class="trigger-box">
                                <div class="card-header">
                                  <div class="card-header-container">
                                    <div class="header-icon">
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24"
                                          fill="currentColor"
                                      >
                                        <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M19 4H5v16h14v-2h2v2a2 2 0 01-2 2H5a2 2 0 01-2-2V4a2 2 0 012-2h14a2 2 0 012 2v2h-2V4z"></path>
                                          <path d="M22.142 11.071L11.536 7.536l3.535 10.606 2.121-4.95 4.95-2.121z"></path>
                                        </svg>
                                      </svg>
                                    </div>
                                    <div class="header-title">
                                      <div class="name">{{ item.indicatorDisplayName }}</div>
                                    </div>
                                    <div v-if="item.indicatorType === 'operation_app'" class="header-type">应用指标</div>
                                    <div v-if="item.indicatorType === 'operation_revenue'" class="header-type">变现指标</div>
                                    <div v-if="item.indicatorType === 'operation_spend'" class="header-type">投放指标</div>
                                  </div>
                                  <div class="title-sub">{{ item.indicatorName }}2222</div>
                                </div>
                                <div class="card-desc">
                                  <div class="span-desc">{{ item.indicatorNote || '暂无备注' }}</div>
                                </div>
                                <div class="card-footer">
                                  <div></div>
                                  <div class="action">
                                    <a-tooltip content="前往指标详情" position="top">
                                      <div class="action-icon" @click="toIndicatorDetail">
                                        <icon-launch/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </div>
                            </template>
                          </a-trigger>
                        </div>
                      </div>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-trigger>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {analyseStore} from '@/store';
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

const analyseData = analyseStore();

const triggerVisible = ref(false);
const emits = defineEmits(['analysisIndexChange']);
const handleTriggerVisible = () => {
  triggerVisible.value = !triggerVisible.value;
};
const props = defineProps({
  indicatorListStore: {
    type:Array,
    default() {
      return [];
    },
  },
  panelData: {
    type: Object,
    default() {
      return {};
    },
  },
});
// 前往指标详情
const toIndicatorDetail = () => {
  router.push({name: ROUTE_NAME.SETTING_ANALYSE_INDICATOR})
}
const selectObject = ref({
  indicatorName: props.panelData.indicatorName || '',
  indicatorDisplayName: props.panelData.indicatorDisplayName || '',
  indicatorCode: props.panelData.indicatorCode || '',
  indicatorType: props.panelData.indicatorType || '',
  type: props.panelData.type || 'indicator', // 事件 | 指标
  note: '' // 备注
});
watch(
    () => props.panelData,
    (newVal) => {
      selectObject.value.indicatorName = newVal.indicatorName || '';
      selectObject.value.indicatorDisplayName = newVal.indicatorDisplayName || '';
      selectObject.value.indicatorCode = newVal.indicatorCode || '';
      selectObject.value.indicatorType = newVal.indicatorType || '';
    },
    {immediate: true}
);

const panelList = ref<any[]>([]);

interface EventItem {
  indicatorCode: string;
  indicatorName: string;
  indicatorDisplayName: string;
  indicatorType: number;
  indicatorNote: string;
  isCollect: boolean;
}

interface CategoryItem {
  categoryName: string;
}

type PanelItem = CategoryItem | EventItem;

const searchInput = ref('')
// 虚拟列表  指标数组
const virtualPanelList = ref<PanelItem[]>([]);
const getVirtualPanelList = (arr) => {
  virtualPanelList.value = arr.flatMap((category) => {
    const categoryItem: CategoryItem = {
      categoryName: category.categoryName,
    };
    return [categoryItem, ...category.itemData];
  });
}
const getEventList = async () => {
  const pList = [
    {
      categoryName: '收藏的指标',
      itemData: [],
    },
    ...props.indicatorListStore?.map((item, index) => {
      return {
        categoryName: item.type,
        itemData: item.items.map(indicator => {
          return {
            indicatorCode: indicator.code,
            indicatorName: indicator.name,
            indicatorDisplayName: indicator.displayName,
            indicatorType: indicator.type,
            indicatorNote: indicator.note,
            isCollect: false,
          };
        })
      }
    })
  ];
  panelList.value = pList;
  getVirtualPanelList(pList)
};
// getEventList();
watch(
    () => props.indicatorListStore,
    (newVal) => {
      getEventList();
    },
    {immediate: true,deep:true}
);

//   指标容器，左右点击滚动效果联动
const activeIndex = ref(0)
const virtualList = ref(null);
const selectListRef = ref(null);
const categoryChange = async (categoryName: string, index: number) => {
  await nextTick()
  activeIndex.value = index;
  const targetIndex = virtualPanelList.value.findIndex(item => item.categoryName === categoryName);
  if (targetIndex !== -1) {
    virtualList.value.scrollIntoView({index: targetIndex});
  }

};

const handleScroll = () => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (!scrollContainer) return;

  const {scrollTop} = scrollContainer; // 获取 scrollTop
  const items = scrollContainer.querySelectorAll('.list-name');
  if (items && items.length > 0) {
    let sIndex = 0
    items.forEach((item, index) => {
      if (scrollTop + 60 >= item.offsetTop) {
        panelList.value.forEach((el, num) => {
          if (item.textContent.trim() === el.categoryName) {
            sIndex = num
          }
        })
      }
    })
    activeIndex.value = sIndex
  }
};
onMounted(() => {
  // 初始化 categoryName
  if (panelList.value && panelList.value.length > 0) {
    activeIndex.value = 0
  }

  // 添加滚动指标监听
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    scrollContainer.addEventListener('scroll', handleScroll);
  }
});
// 在组件卸载时移除指标监听
onUnmounted(() => {
  const scrollContainer = document.querySelector('.arco-virtual-list');
  if (scrollContainer) {
    scrollContainer.removeEventListener('scroll', handleScroll);
  }
});
//   指标点击
const listChange = (item: any) => {
  selectObject.value = {
    ...selectObject.value,
    indicatorName: item.indicatorName,
    indicatorDisplayName: item.indicatorDisplayName,
    type: 'indicator',
    indicatorType: item.indicatorType,
    indicatorCode: item.indicatorCode,
    indicatorNote: item.indicatorNote,
  }
  triggerVisible.value = false;
  emits('analysisIndexChange', selectObject.value);
};

// 搜索时数据
const filteredLists = computed(() => {
  const str = searchInput.value.trim();
  const listDate = []
  panelList.value.forEach((item, index) => {
    const filterData = item.itemData?.filter(item => item?.indicatorDisplayName?.includes(str) || item?.indicatorName?.includes(str));
    if (filterData?.length > 0) {
      listDate.push({categoryName: item.categoryName})
      listDate.push(...filterData)
    }
  })
  return listDate
});
// 搜索点击
const itemChange = (item: any) => {
  selectObject.value = {
    ...selectObject.value,
    indicatorName: item.indicatorName,
    indicatorDisplayName: item.indicatorDisplayName,
    type: item.type,
    indicatorCode: item.indicatorCode,
  }
  triggerVisible.value = false;
  emits('analysisIndexChange', selectObject.value);
}
// 指标收藏
const starClick = (item: object) => {
  const existingItem = panelList.value[0].itemData.find(
      (el) => el.indicatorCode === item.indicatorCode
  );
  if (!existingItem) {
    // item.isCollect = true; // 标记为已收藏
    panelList.value[1].itemData.forEach(pp => {
      if (pp.indicatorCode === item.indicatorCode) {
        pp.isCollect = true;
      }
    })
    panelList.value[0].itemData.push(item); // 仅在不存在时添加
    getVirtualPanelList(panelList.value)
  }
};
// 取消指标收藏
const cancelClick = (item: object) => {
  // item.isCollect = false;
  panelList.value[1].itemData.forEach(pp => {
    if (pp.indicatorCode === item.indicatorCode) {
      pp.isCollect = false;
    }
  })
  panelList.value[0].itemData = panelList.value[0].itemData.filter(
      (el) => el.indicatorCode !== item.indicatorCode
  );
  getVirtualPanelList(panelList.value)
};
</script>

<style scoped lang="less">
.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.select-panel {
  position: relative;
  width: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-bottom);
}

.list-container {
  position: relative;
  display: flex;
  flex-direction: row;

  .list-category {
    height: 350px;
    position: relative;
    flex-shrink: 0;
    width: 100px;
    padding: 10px 0 0 10px;
    border-right: 1px solid var(--tant-border-color-border1-1);

    .category-container {
      height: calc(100% - 32px);
      overflow-y: auto;

      .category-item {
        width: 100%;
        margin-bottom: 10px;
        padding: 2px 0 2px 2px;
        color: var(--tant-text-gray-color-text1-3);
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .item-active {
        font-weight: 500;
        border-right: 2px solid var(--tant-primary-color-primary-default);
      }
    }

    .arco-btn {
      margin-left: -10px;
      width: 100px;
      border-radius: 0 0 0 4px;
      font-size: 12px;
      color: var(--tant-text-gray-color-text1-2);
      text-shadow: none;
      background-color: var(--tant-bg-white-color-bg1-1);
      border: 1px solid var(--tant-border-color-border1-1);
      box-shadow: none;
      border-left: none;
      border-bottom: none;
    }
  }

  .item-list {
    flex: 1 1;

    .select-list {
      // position: relative;
      height: 350px;
      width: 260px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }

    .list-group {
      width: 100%;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    .favorite {
      height: 30px;
      width: 100%;
      padding-left: 18px;
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
    }

    .list-name {
      border-top: 1px solid var(--tant-border-color-border1-1);
      margin-top: 8px;
      padding: 8px 20px 0;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 500;
      font-size: 14px;
      background-color: #fff;
      height: 45px;

      .star {
        margin-right: 5px;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }
    }

    //.list-content:first-child .list-name{
    //    border-top: none;
    //    margin-top: 0;
    //}
    .list-box {
      margin: 0 8px;
      padding-left: 12px;
      border-radius: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      line-height: 28px;
      cursor: pointer;

      .desc {
        display: inline-block;
        width: 180px;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1, .desc2 {
        display: inline-block;
        margin-right: 10px;
        color: inherit !important;
        vertical-align: top;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .desc1 {
        width: 140px;
      }

      .desc2 {
        width: 120px;
      }

      .icon {
        opacity: 0;
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
      }

      .icon:hover {
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
      }

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);

        .icon {
          opacity: 100;
          pointer-events: all;
        }
      }

      .isStar {
        pointer-events: none;
        float: right;
        margin-right: 4px;
        line-height: 28px;
        cursor: pointer;
        color: var(--tant-decorative-yellow-color-decorative6-3) !important;
        opacity: 100;
        pointer-events: all;
      }
    }

    .list-active {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }

    .list-metric {
      position: relative;
      height: 350px;
      width: 360px;
      overflow: auto;
      will-change: transform;
      direction: ltr;

      :deep(.arco-list-item) {
        padding: 0;
        border: none;
      }

      :deep(.arco-list-bordered) {
        border: none;
      }
    }
  }
}

.trigger-box {
  display: flex;
  flex-direction: column;
  width: 280px;
  max-height: 360px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);

  .card-header {
    padding: 12px;
    background-color: var(--tant-bg-gray-color-bg2-1);
    border-radius: 4px 4px 0 0;

    .card-header-container {
      display: flex;
      flex-direction: row;

      .header-icon {
        margin-top: 2px;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-1);
      }

      .header-title {
        flex-grow: 1;
        color: var(--tant-text-gray-color-text1-1);

        .name {
          display: inline-block;
          width: 100%;
          padding: 0 4px;
          word-break: break-all;
          border-radius: 4px;
          color: var(--tant-text-gray-color-text1-1);
          font-weight: 500;
          line-height: 22px;
        }
      }

      .header-type {
        flex-shrink: 0;
        width: 62px;
        margin-left: 6px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        text-align: right;
      }
    }

    .title-sub {
      margin-left: 24px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      word-break: break-all;
    }
  }

  .card-desc {
    flex-grow: 1;
    height: 130px;
    padding: 12px 8px;
    overflow-y: auto;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;

    .span-desc {
      display: inline-block;
      width: 100%;
      padding: 0 4px;
      word-break: break-all;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }

  .card-footer {
    display: flex;
    flex-shrink: 0;
    justify-content: space-between;
    height: 34px;
    padding-top: 6px;
    padding-right: 12px;
    padding-left: 12px;
    color: var(--tant-text-gray-color-text1-3);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    border-radius: 0 0 4px 4px;
    box-shadow: inset 0 1px #e6e6e6;

    .action {
      .action-icon {
        display: inline-block;
        min-width: 24px;
        height: 24px;
        padding: 0 4px;
        line-height: 22px;
        text-align: center;
        background-color: transparent;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .action-icon:hover {
        background-color: var(--tant-primary-color-primary-fill-hover);
        color: var(--tant-primary-color-primary-default);
      }
    }
  }
}
</style>