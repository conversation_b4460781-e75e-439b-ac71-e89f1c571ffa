
<template>
    <!-- 定时开关 -->
    <a-modal v-model:visible="modalVisible" :width="650" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form">
            <a-form-item field="tasks" label="任务时间(UTC+8)">
                <div>
                    <div v-for="(task, index) in form.tasks" :key="index" class="task-item">
                        <div class="task-row">
                            <div class="date-picker-wrapper">
                                <a-date-picker 
                                    v-model="task.date" 
                                    :disabled-date="disablePastDates"
                                    allow-clear
                                    size="small"
                                    style="width: 100%;"
                                />
                            </div>
                            <div class="time-picker-wrapper">
                                <a-time-picker
                                    v-model="task.time" 
                                    size="small"
                                    format="HH:mm"
                                    style="width: 100%;"
                                    @change="validateTime(task, index)"
                                />
                            </div>
                            <div class="switch-wrapper">
                                <a-switch v-model="task.status" size="small"/>
                            </div>
                            <div v-show="form.tasks.length > 1" class="delete-wrapper">
                                <icon-delete @click="removeTask(index)"/>
                            </div>
                        </div>
                        <div v-if="task.error" class="msg">1234</div>
                    </div>
                    <div class="add-task">
                        <a-button type="text" @click="addTask">
                            <span>添加任务</span>
                        </a-button>
                    </div>
                </div>
            </a-form-item>
            <a-form-item field="templete" label="Excel模板">
                <a-button style="margin-right: 12px;">上传</a-button>
                <a-button>下载模板</a-button>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" :disabled="validateAllTasks" @click="saveData">
                确定
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';

const modalVisible = ref(false);
const modalTitle = ref('定时开关');
const loading = ref(false);
const formRef = ref();

// 表单数据
const form = reactive({
    tasks: [
        {
            date: undefined,
            time: undefined,
            status: true,
            error: ''
        }
    ]
});

// 禁用过去的日期
const disablePastDates = (current: Date) => {
    return current.getTime() < dayjs().startOf('day').valueOf();
};
// 验证时间是否晚于当前时间
const validateTime = (task: any, index: number) => {
    if (!task.date || !task.time) {
        task.error = '';
        return;
    }
    // 明确指定日期和时间的格式，避免时区干扰
    const dateStr = dayjs(task.date).format('YYYY-MM-DD');
    const timeStr = task.time; // "05:00"
    const taskDateTime = dayjs(`${dateStr} ${timeStr}`, 'YYYY-MM-DD HH:mm');
    const now = dayjs();
    console.log('当前时间:', now.format('YYYY-MM-DD HH:mm'));
    console.log('任务时间:', taskDateTime.format('YYYY-MM-DD HH:mm'));
    if (taskDateTime.isBefore(now)) {
        task.error = '定时任务时间必须晚于当前时间';
    } else {
        task.error = '';
    }
};

// 添加新任务
const addTask = () => {
    form.tasks.push({
        date: undefined,
        time: undefined,
        status: true,
        error: ''
    });
};

// 删除任务
const removeTask = (index: number) => {
    form.tasks.splice(index, 1);
};

// 验证所有任务
const validateAllTasks = () => {
    let isValid = true;
    form.tasks.forEach((task, index) => {
        // 检查必填项
        if (!task.date || !task.time) {
            task.error = '日期和时间不能为空';
            isValid = false;
            return;
        }
        // 验证时间是否晚于当前时间
        validateTime(task, index);
        if (task.error) {
            isValid = false;
        }
    });
    return isValid;
};

const emits = defineEmits(['updateData']);

const openModal = async (channel?: string) => {
    // 重置表单
    form.tasks = [
        {
            date: undefined,
            time: undefined,
            status: true,
            error: ''
        }
    ];
    if (formRef.value) {
        formRef.value.resetFields();
        formRef.value.clearValidate();
    }
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const saveData = () => {
    loading.value = true;
    try {
        // 格式化数据用于提交
        const tasksToSubmit = form.tasks.map(task => ({
            scheduledTime: dayjs(task.date)
                .hour(dayjs(task.time).hour())
                .minute(dayjs(task.time).minute())
                .format('YYYY-MM-DD HH:mm:ss'),
            status: task.status ? 1 : 0
        }));
        setTimeout(() => {
            Message.success('保存成功');
            modalVisible.value = false;
            emits('updateData');
            loading.value = false;
        }, 1000);
    } catch (error) {
        console.error('操作失败:', error);
        Message.error('操作失败，请重试');
        loading.value = false;
    }
};

defineExpose({
    openModal
});
</script>

<style scoped lang="less">
.task-item {
    margin-bottom: 16px;
    .task-row {
        display: flex;
        align-items: center;
        gap: 6px;
        .delete-wrapper {
            flex-shrink: 0;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }
        &:hover {
            .delete-wrapper {
                opacity: 1;
            }
        }
    }
    .msg{
        text-align: left;
        white-space: normal;
        color: #ed4014;
        padding-top: 4px;
        line-height: 14px;
    }
    .date-picker-wrapper, .time-picker-wrapper {
        display: flex;
        flex-direction: column;
        flex: 1;
        .field-label {
            margin-bottom: 4px;
            color: var(--color-text-3);
            font-size: 13px;
        }
    }
    
    .switch-wrapper {
        margin-left: 8px;
    }
}

.footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}

.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>
