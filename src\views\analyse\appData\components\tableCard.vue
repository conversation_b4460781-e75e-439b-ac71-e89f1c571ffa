<template>
  <div ref="cardRef" class="card">
    <div class="drag-allow-from card-title">
      <div class="title">
        <div class="edit-select" style="width: 280px; border-radius: 4px; margin-right: 8px">
          <a-select v-model="indicatorsGroup" placeholder="请选择指标组" allow-search :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" @change="getData">
            <a-option v-for="item in props.indicatorsViewList" :key="item.code" :value="item.code">{{ item.name }}</a-option>
          </a-select>
          <a-tooltip content="编辑">
            <icon-edit size="16" class="edit" @click="editIndicators" />
          </a-tooltip>
        </div>
      </div>
      <div class="operation">
        <a-tooltip content="导出">
          <div class="operation-icon" @click="exportXlsx">
            <icon-download class="icon" />
          </div>
        </a-tooltip>
        <a-dropdown :popup-max-height="false" :popup-container="cardRef">
          <a-tooltip content="更多">
            <div class="operation-icon">
              <icon-more class="icon" />
            </div>
          </a-tooltip>
          <template #content>
            <a-doption @click="deleteItem">删除</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <div class="card-content">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <a-table v-if="totalNum > 0" :key="tableKey" :columns="columns" :data="tableData" :pagination="pagination" size="small" column-resizable :filter-icon-align-left="true" style="white-space: nowrap" @page-size-change="pageSizeChange">
          <template #last-group-title>
            <div style="display: flex; align-items: center">
              <a-tooltip v-if="isColumnsFixed" content="取消冻结">
                <icon-lock
                  :style="{
                    marginRight: '4px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    color: '#165dff',
                  }"
                  @click.stop="toggleColumnsFixed"
                />
              </a-tooltip>
              <a-tooltip v-else content="冻结分组列">
                <icon-unlock
                  :style="{
                    marginRight: '4px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    color: '#86909c',
                  }"
                  @click.stop="toggleColumnsFixed"
                />
              </a-tooltip>
              {{ resultData?.groupsDesc?.[resultData.groupsDesc.length - 1]?.name }}
            </div>
          </template>
          <template v-for="(_, index) in resultData?.groupsDesc" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
            <div class="custom-filter">
              <a-space direction="vertical" :size="8">
                <a-select
                  :model-value="filterValue"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :options="getFilterOptionsByIndex(index)"
                  :style="{ width: '200px' }"
                  :trigger-props="{
                    position: 'top',
                    autoFitPopupMinWidth: true,
                    updateAtScroll: true,
                  }"
                  @update:model-value="(val) => setFilterValue(val)"
                >
                </a-select>
                <div class="custom-filter-footer">
                  <a-button size="mini" @click="handleFilterReset">重置</a-button>
                  <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
                </div>
              </a-space>
            </div>
          </template>
          <template v-for="(column, index) in columns.filter(col => col.code)" :key="`header-${index}`" #[`${column.dataIndex}-title`]>
            <IndicatorTooltip 
              :indicator-code="column.code" 
              :indicator-data="indicatorDetailsMap.get(column.code)"
            >
              <span>{{ column.title }}</span>
            </IndicatorTooltip>
          </template>
        </a-table>
        <a-empty v-else> 无搜索结果<br />请尝试不同的筛选组合 </a-empty>
      </a-spin>
    </div>
    <indicatorsSelect ref="selectRefs" page-type="comprehensive" :filter-params="{ subjectCode: 'application', appView: indicatorsGroup }" :app-view-data="viewData" :indicator-list="indicatorList" @update-view="updateView" @update-indicator="updateIndicator" />
    <!-- 折线图弹窗 -->
    <LineChartModal
      v-model:visible="lineChartModalVisible"
      :title="chartModalData.title"
      :dates="chartModalData.dates"
      :values="chartModalData.values"
      :indicator-name="chartModalData.indicatorName"
      :group-info="chartModalData.groupInfo"
    />
  </div>
</template>

<script setup lang="ts">
import {computed, h, inject, ref, resolveComponent, watch} from 'vue';
import * as XLSX from 'xlsx';
import {queryComprehensiveReportData, reportQueryResponseData} from '@/api/analyse/analyse';
import {getAttributionViewDetail} from '@/api/analyse/api';
import indicatorsSelect from '@/views/analyse/appData/components/viewSelect/index.vue';
// import indicatorsSelect from './indicatorsSelect.vue';
import {getAppPageList} from '@/api/marketing/api';
import {getDateRangeEndDate, getDateRangeStartDate} from '@/utils/dateUtil';
import {createCustomSorter, getCustomStringLength} from '@/utils/strUtil';
import {Message} from "@arco-design/web-vue";
import IndicatorTooltip from '@/components/indicator-tooltip/index.vue';
import useIndicatorDetailHelper from '@/utils/indicator-detail-helper';
import LineChartModal from './LineChartModal.vue';

interface Props {
    report: any;
    groupList: any;
    indicatorsViewList: any;
    indicatorList: any;
    filterParams: any;
    viewData: any;
  }

  const props = defineProps<Props>();
  const emits = defineEmits(['deleteItem', 'updateParams', 'updateView', 'updateIndicator', 'pageSizeChange']);
  const dashboardId = inject('dashboardCode', ref(''));
  const cardRef = ref<HTMLElement>();
  const indicatorsGroup = ref('');
  const columns = ref<any>([]);
  const tableData = ref<any>([]);
  const isColumnsFixed = ref(false); // 添加列固定状态
  const tableKey = ref(0); // 添加表格重新渲染的key
  // 添加指标详情辅助工具
  const { createIndicatorMap } = useIndicatorDetailHelper();
  const indicatorDetailsMap = ref(new Map());

  // 折线图弹窗相关状态
  const lineChartModalVisible = ref(false);
  const chartModalData = ref({
    title: '',
    dates: [] as string[],
    values: [] as number[],
    indicatorName: '',
    groupInfo: '',
  });

  const pagination = ref<any>({
    pageSize: 20,
    pageSizeOptions: [30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
    showPageSize: 'true',
    showTotal: 'true',
  });
  const pageSizeChange = (size) => {
    pagination.value.pageSize = size;
    emits('pageSizeChange', size, tableData.value.length);
  };
  // 导出
  const exportXlsx = () => {
    // 获取表头
    const headers = columns.value.map((item) => item.title);
    const columnsList = [headers];
    tableData.value.forEach((item: any) => {
      const row = headers.map((header) => {
        const dataIndex = columns.value.find((col) => col.title === header)?.dataIndex;
        return dataIndex ? item[dataIndex] : '';
      });
      columnsList.push(row);
    });

    const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
    const newWorkbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
    XLSX.writeFile(newWorkbook, `应用数据_${getDateRangeStartDate(props.filterParams.date)}-${getDateRangeEndDate(props.filterParams.date)}.xlsx`);
  };
  // 图表指标选择
  const checkList = ref([]);

  // 删除
  const deleteItem = () => {
    const id = props.report.i;
    emits('deleteItem', id);
  };
  const requestId = ref('');
  const totalNum = ref(0);
  const loading = ref(false);
  const timeOut = ref();
  const resultData = ref();

  // 判断周几
  const getDayOfWeek = (dateString) => {
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  };
  const formatNumber = (num: number, displayType?: { type: string; decimalNum: number }) => {
    if (num === null || num === undefined) return '0';
    let formattedValue = '';
    if (displayType && displayType.type) {
      if (displayType.type === 'default') {
        formattedValue = num.toFixed(displayType.decimalNum);
      } else if (displayType.type === 'percent') {
        // 乘以 100
        formattedValue = (num * 100).toFixed(displayType.decimalNum);
      }
    } else {
      formattedValue = num.toFixed(2);
    }
    return formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',') || 0;
  };

  // 根据指标组code获取详情，checkList
  const getIndicatorsDetail = async () => {
    if (indicatorsGroup.value) {
      await getAttributionViewDetail(indicatorsGroup.value, dashboardId.value).then((res) => {
        checkList.value =
          res?.map((item) => {
            return {
              ...item,
              isBasic: true,
            };
          }) || [];
      });
    }
  };
  const isAppIdGroupAndDaily = computed(() => {
    const selectedGroup = [] as any;
    props.groupList.forEach((item) => {
      props.filterParams.groupCodes?.forEach((el) => {
        if (item.code === el) {
          selectedGroup.push({
            ...item
          });
        }
      });
    });
    return selectedGroup.some(item => item.name === 'app_id') && props.filterParams.timeParticleSize === 'D1'
  });
  const computedData = async () => {
    loading.value = true;
    timeOut.value = setTimeout(() => {
      if (loading.value) {
        loading.value = false;
      }
    }, 20000);
    const aggregatesList = [] as any;
    props.groupList.forEach((item) => {
      props.filterParams.groupCodes?.forEach((el) => {
        if (item.code === el) {
          aggregatesList.push({
            aggregateType: 'operation',
            objectDisplayName: item.displayName,
            objectName: item.name,
            objectId: item.code,
            objectType: 'string',
          });
        }
      });
    });
    const filterData = {
      logicalOperation: 'and',
      filters: [
        {
          calcuSymbol: 'eq',
          displayName: '国家',
          filterType: 'operation',
          logicalOperation: 'and',
          objectId: 'operation_attribute202412210011',
          objectName: 'country',
          objectType: 'string',
          subFilters: [],
          thresholds: props.filterParams.country,
        },
      ],
    };
    await getIndicatorsDetail();
    let aggregateAttributeParams = [] as any;
    if(isAppIdGroupAndDaily.value){
      aggregateAttributeParams = [
        {
          code:'update_logs',
          name:'更新日志'
        },
        {
          code:'online_days',
          name:'上线天数'
        }
      ]
    }
    const queryParams = {
      aggregates: aggregatesList,
      indicators: checkList.value,
      filter: props.filterParams?.country?.length ? filterData : {},
      subject: 'application',
      timeParticleSize: props.filterParams.timeParticleSize,
      firstDayDfWeek:props.filterParams.firstDayDfWeek,
      dateRange: props.filterParams.date,
      aggregateAttribute: aggregateAttributeParams,
      teamCodes: props.filterParams.teamCodes,
    };
    requestId.value = queryComprehensiveReportData(queryParams);
  };
  // 添加应用跳转功能
  const handleAppClick = async (appCode: string) => {
    try {
      // 调用getAppPageList接口查找应用信息
      const res = await getAppPageList({
        current: 1,
        pageSize: 10,
        text: appCode,
      });
      const appInfo = res?.items?.find((item) => item.code === appCode);
      if (appInfo && (appInfo.storeLinkUrl || appInfo.platformLink)) {
        // 优先使用storeLinkUrl，如果没有则使用platformLink
        const url = appInfo.storeLinkUrl || appInfo.platformLink;
        window.open(url, '_blank');
      } else {
        Message.warning(`未找到应用 ${appCode} 的跳转链接`);
      }
    } catch (error) {
      console.error('获取应用信息失败:', error);
      Message.error('获取应用信息失败');
    }
  };

  // 判断是否为应用分组列
  const isAppGroupColumn = (groupIndex: number) => {
    if (!resultData.value?.groupsDesc || !resultData.value?.groupsDesc[groupIndex]) {
      return false;
    }
    const groupDesc = resultData.value.groupsDesc[groupIndex];
    return groupDesc.name === '应用'
  };

  // 处理数据列点击事件
  const handleDataClick = (record: any, column: any) => {
    // 获取指标名称
    const indicatorName = column.title;
    const dataIndex = column.dataIndex;
    // 获取该指标在所有日期的数据
    const indicatorIndex = parseInt(dataIndex.replace('num', '')) - 1;
    const indicatorData = resultData.value?.y?.[indicatorIndex];
    if (!indicatorData) {
      Message.warning('暂无数据');
      return;
    }
    // 构建折线图数据
    const dates = resultData.value?.x || [];
    const groupValue = record.groupValue;
    // 找到对应分组的数据
    const targetGroup = indicatorData.yData?.find((item: any) => 
      item.group.map((v: any) => v === null ? 'null' : v).join(',') === groupValue
    );
    if (!targetGroup) {
      Message.warning('暂无该分组的数据');
      return;
    }
    // 构建分组信息
    const groupInfo = record.groupList?.map((groupValue: any, index: number) => {
      const groupDesc = resultData.value?.groupsDesc?.[index];
      return groupDesc ? `${groupDesc.name}: ${groupValue}` : '';
    }).join(', ') || '';

    // 设置弹窗数据
    chartModalData.value = {
      title: indicatorName,
      dates: dates,
      values: targetGroup.values || [],
      indicatorName: indicatorName,
      groupInfo: groupInfo
    };
    lineChartModalVisible.value = true;
  };
  const handleTableData = () => {
    tableData.value = [];
    const data = resultData.value?.y?.flatMap((item) =>
      item?.yData?.map((el) => ({
        code: item.eventCode || item.indicatorCode,
        name: item.displayName,
        displayType: item.displayType,
        groupValue: el.group.map((value) => (value === null ? 'null' : value)).join(','),
        groupList: el.group.map((value) => (value === null ? 'null' : value)),
        values: el.values,
      }))
    );
    const uniqueData = Array.from(new Set(data?.map((item) => item.name)))?.map((name) => data.find((item) => item.name === name)); // 根据 name 去重
    // 创建指标详情映射
    indicatorDetailsMap.value = createIndicatorMap(uniqueData);
    const xDateList = resultData.value?.x;
    const columnsDate = xDateList && xDateList.length > 0 ? xDateList?.map((item) => item) : [];

    const spliceList = [] as any;
    const groupsList =
      resultData.value?.groups?.map((item) => {
        return item?.map((value) => (value === null ? 'null' : value)).join(','); // 将 null 转换为字符串 'null'
      }) || [];
    // 创建属性数据映射（更新日志、上线天数等）
    // attributeDataMap 结构: Map<attrCode, Map<groupKey, Map<dateIndex, value>>>
    const attributeDataMap = new Map();
    if (isAppIdGroupAndDaily.value && resultData.value?.yAttribute) {
      resultData.value.yAttribute.forEach((attr) => {
        const groupMap = new Map();
        // yAttributeData 是每个属性对象内的数组
        attr.yAttributeData?.forEach((item) => {
          const key = item.group.map((value) => (value === null ? 'null' : value)).join(',');
          // 创建日期索引到值的映射
          const dateValueMap = new Map();
          item.values?.forEach((value, dateIndex) => {
            dateValueMap.set(dateIndex, value);
          });
          groupMap.set(key, dateValueMap);
        });
        attributeDataMap.set(attr.code, groupMap);
      });
    }

    columns.value = [
      {
        title: '日期',
        dataIndex: 'date',
        sortable: { sortDirections: ['ascend', 'descend'], defaultSortOrder: 'descend' },
        fixed: 'left',
        width: 140,
        // 为日期列添加自定义渲染，显示更新日志
        render: ({ record }) => {
          const dateText = record.date;
          const updateLogs = record.update_logs;
          const ATooltip = resolveComponent('a-tooltip');
          
          // 只有当有更新日志时才显示悬浮框
          if (updateLogs) {
            return h(ATooltip,
              {
                contentClass: 'update-logs-tooltip'
              },
              {
                default: () => h(
                  'span',
                  {
                    style: {
                      color: updateLogs ? '#ff9900' : '',
                      cursor: 'pointer',
                    }
                  },
                  dateText
                ),
                content: () => h(
                  'div',
                  {
                    innerHTML: updateLogs,
                    style: {
                      maxWidth: '400px',
                      maxHeight: '300px',
                      overflow: 'auto'
                    }
                  }
                )
              }
            );
          }
          return h(
            'span',
            {
              style: {
                color: updateLogs ? '#ff9900' : '',
              }
            },
            dateText
          );
        }
      },
      ...uniqueData.map((item, index) => ({
        code: item.code,
        title: item.name,
        dataIndex: `num${index + 1}`,
        slotName: `num${index + 1}`,
        titleSlotName: `num${index + 1}-title`,
        minWidth: isColumnsFixed.value ? 140 : undefined,
        displayType: item.displayType,
        sortable: { sortDirections: ['ascend', 'descend'] },
        render: ({ record, column }: any) => {
          const value = formatNumber(record[column.dataIndex], column.displayType);
          const percentSymbol = column.displayType?.type === 'percent' ? '%' : '';
          const groupInfo = record.groupList?.map((groupValue: any, index: number) => {
            const groupDesc = resultData.value?.groupsDesc?.[index];
            if (groupDesc) {
              return `${groupDesc.name}: ${groupValue}`;
            }
            return ''
          }).join(', ') || '';
          const ATooltip = resolveComponent('a-tooltip');
          return h(ATooltip,
            {
              content: groupInfo || '无分组信息'
            },
            {
              default: () => h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
                  },
                  onClick: () => handleDataClick(record, column)
                },
                `${value}${percentSymbol}`
              )
            }
          );
        }
      })),
    ];
    const result = [] as any;
    const dates = columnsDate.map((item) => getDayOfWeek(item));
    const yLength = resultData.value?.y?.length;
    dates.forEach((date, dateIndex) => {
      groupsList.forEach((groupValue) => {
        const entry = {
          date,
          groupValue,
          groupList: [],
        };

        // 初始化所有 num 值为 null
        for (let i = 0; i < yLength; i++) {
          entry[`num${i + 1}`] = null;
        }
        // 遍历数据,填充对应的 num 值
        data.forEach((item) => {
          if (item.groupValue === groupValue) {
            for (let i = 0; i < yLength; i++) {
              if (entry[`num${i + 1}`] === null) {
                entry[`num${i + 1}`] = item.values[dateIndex];
                entry.groupList = item.groupList;
                break; // 找到第一个为null的num并赋值后,跳出循环
              }
            }
          }
        });

        // 添加属性数据（更新日志、上线天数等）
        if (isAppIdGroupAndDaily.value && attributeDataMap.size > 0) {
          attributeDataMap.forEach((groupMap, attrCode) => {
            const dateValueMap = groupMap.get(groupValue);
            // 从日期索引映射中获取当前日期的值
            const attrValue = dateValueMap?.get(dateIndex);
            entry[attrCode] = attrValue !== undefined && attrValue !== null ? attrValue : null;
          });
        }

        result.push(entry);
      });
    });
    tableData.value = [...result];

    // 当有分组项的情况下
    if (resultData.value?.groupsDesc && resultData.value?.groupsDesc?.length > 0) {
      resultData.value.groupsDesc.forEach((item, index) => {
        const isLastGroup = index === resultData.value.groupsDesc.length - 1;
        const isAppGroup = isAppGroupColumn(index);
        spliceList.push({
          title: item.name,
          dataIndex: `group-${index}`,
          sortable: {
            sortDirections: ['ascend', 'descend'],
            sorter: (a, b, extra) => {
              const { direction } = extra;
              const format = item.format;
              const valueA = a[`group-${index}`];
              const valueB = b[`group-${index}`];
              return createCustomSorter(format)(valueA, valueB, direction);
            },
          },
          filterable: {
            filter: (value, row) => {
              if (!value || value.length === 0) return true;
              return value.includes(row.groupList[index]);
            },
            slotName: `group-filter-${index}`,
            multiple: true,
          },
          fixed: isColumnsFixed.value ? 'left' : undefined,
          width: isColumnsFixed.value ? getCustomStringLength(item.name) + 120 : undefined,
          titleSlotName: isLastGroup ? 'last-group-title' : undefined,
          ellipsis: isColumnsFixed.value,
          tooltip: isColumnsFixed.value,
          // 为应用分组列添加自定义渲染
          ...(isAppGroup
            ? {
                slotName: `app-group-${index}`,
                render: ({ record }) => {
                  const appCode = record[`group-${index}`];
                  return h(
                    'span',
                    {
                      style: {
                        color: '#165dff',
                        cursor: 'pointer',
                      },
                      onClick: () => handleAppClick(appCode),
                    },
                    appCode
                  );
                },
              }
            : {}),
        });
        tableData.value.forEach((el) => {
          el[`group-${index}`] = el.groupList[index];
        });
      });
      columns.value.splice(1, 0, ...spliceList);

      // 在应用分组且时间粒度为D1的情况下，添加上线天数列
      if (isAppIdGroupAndDaily.value && attributeDataMap.has('online_days')) {
        const onlineDaysColumn = {
          title: '上线天数',
          dataIndex: 'online_days',
          sortable: { sortDirections: ['ascend', 'descend'] },
          fixed: isColumnsFixed.value ? 'left' : undefined,
          width: isColumnsFixed.value ? 120 : undefined,
          render: ({ record }: any) => {
            const days = record.online_days;
            return days !== null && days !== undefined ? days : '-';
          },
        };
        // 在分组列之后插入上线天数列
        columns.value.splice(1 + spliceList.length, 0, onlineDaysColumn);
      }
    }
    if (tableData.value.length > 9) {
      emits('pageSizeChange', pagination.value.pageSize, tableData.value.length);
    }
  };

  // 添加切换列固定的方法
  const toggleColumnsFixed = () => {
    isColumnsFixed.value = !isColumnsFixed.value;
    tableKey.value++; // 强制重新渲染表格
    handleTableData(); // 重新生成列配置
  };
  // 添加一个获取筛选选项的函数
  const getFilterOptionsByIndex = (index: number) => {
    if (!tableData.value?.length) return [];
    return Array.from(new Set(tableData.value.map((row) => row.groupList[index])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value: value,
      }));
  };
  watch(reportQueryResponseData, async (newData, oldData) => {
    if (newData === undefined || newData.requestId !== requestId.value) {
      return;
    }
    if (timeOut.value) {
      clearTimeout(timeOut.value);
    }
    resultData.value = newData.result;
    totalNum.value = newData?.result?.totalNum;
    handleTableData();
    loading.value = false;
  });
  const selectRefs = ref();
  const editIndicators = () => {
    selectRefs.value.openModal(indicatorsGroup.value);
  };
  // 指标组change

  watch(
    [indicatorsGroup],
    () => {
      const params = {
        indicatorsGroup: indicatorsGroup.value,
      };
      emits('updateParams', props.report.i, params);
    },
    { deep: true }
  );

  const getData = () => {
    if (indicatorsGroup.value) {
      computedData();
    }
  };
  const init = () => {
    const obj = props.report.configParams;
    indicatorsGroup.value = obj?.indicatorsGroup || '';
    if (indicatorsGroup.value) {
      computedData();
    }
  };
  init();
  // watch(() => props.filterParams, (newValue) => {
  //   if(newValue && indicatorsGroup.value){
  //     computedData()
  //   }
  // },{deep:true});

  const updateView = (v) => {
    indicatorsGroup.value = v ? v : indicatorsGroup.value;
    emits('updateView');
  };
  watch(
    () => props.indicatorsViewList,
    () => {
      // 检查更新后的appView是否还在数组中
      const viewExists = props.indicatorsViewList.find((item) => item.code === indicatorsGroup.value);
      if (!viewExists) {
        const defaultObj = props.indicatorsViewList.find((item) => item.isDefault);
        indicatorsGroup.value = defaultObj?.code || props.indicatorsViewList[0]?.code;
      }
      if (indicatorsGroup.value) {
        computedData();
      }
    },
    { deep: true }
  );
  const updateIndicator = () => {
    emits('updateIndicator');
  };
  defineExpose({
    init,
  });
</script>

<style scoped lang="less">
  .card {
    width: 100%;
    height: 100%;
    border: 1px solid transparent;
    transition: all 0.3s;
    background: #fff;
  }

  .card-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 46px;
    margin-bottom: 0;
    padding: 16px 24px 8px;
    line-height: 20px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-bottom: none;
    cursor: grab;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    .title-text {
      display: inline-block;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header5-medium);
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;
      -webkit-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
    .title {
      display: flex;
    }
    .operation {
      display: none;
      align-items: center;
      transition: opacity 0.3s;
    }

    .operation-icon {
      padding: 4px;
      display: inline-block;
      margin-left: 8px;
      cursor: pointer;
      border-radius: 4px;
      &:hover {
        background-color: var(--tant-bg-gray-color-bg2-1);
      }
      .icon {
        display: flex;
        align-items: center;
        line-height: normal;
        font-size: 16px;
      }
    }
  }
  .card:hover .card-title .operation {
    display: flex;
    unicode-bidi: isolate;
  }

  .card:hover {
    box-shadow: var(--tant-medium-shadow-medium-overall);
    border: 1px solid var(--tant-primary-color-primary-default);
    border-radius: 4px;
  }
  .card-content {
    width: 100%;
    height: calc(100% - 46px);
    padding: 12px 24px 16px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .edit-select {
    width: 100%;
    position: relative;
    .edit {
      position: absolute;
      right: 30px;
      top: 8px;
      opacity: 0;
      cursor: pointer;
    }
    &:hover {
      .edit {
        opacity: 1;
      }
    }
  }
  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
</style>
