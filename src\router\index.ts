import {createRouter, createWebHashHistory} from 'vue-router';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css';
import {registerRoutes} from "@/router/dynamic";
import {ROUTE_NAME} from "@/router/constants";
import {getMenuFilterConfig} from '@/api/setting/api';
import {REDIRECT_MAIN} from './routes/base';
import createRouteGuard from './guard';

NProgress.configure({showSpinner: false}); // NProgress Configuration

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/login',
      name: ROUTE_NAME.LOGIN,
      component: () => import('@/views/login/index.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    REDIRECT_MAIN,
  ],
  scrollBehavior() {
    return {top: 0};
  },
});

// 防止路由无限循环
let routeFlag = false;

/**
 * 获取页面筛选条件配置
 * @param routeName 路由名称
 * @param urlParams URL参数字符串
 */
async function usePageConfig(routeName: string, urlParams?: string) {
  try {
    const response = await getMenuFilterConfig(routeName, urlParams);
    return response?.configJson || null;
  } catch (error) {
    console.warn(`获取页面 ${routeName} 筛选条件失败:`, error);
    return null;
  }
}

router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token');
  if (token) {
    if (routeFlag) {
      // 需要鉴权的页面 或者 显示在菜单的页面
      if (to.meta.requiresAuth || !to.meta.hideInMenu) {
        localStorage.setItem('last-visited-page', JSON.stringify({
          name: to.name,
          fullPath: to.fullPath,
        }));
        
        // 获取页面筛选条件配置
        if (to.name) {
          // 通用逻辑：将所有URL查询参数转换为urlParams字符串
          let urlParams: string | undefined;
          if (Object.keys(to.query).length > 0) {
            // 过滤掉一些系统参数，只保留业务参数
            const businessParams = { ...to.query };
            delete businessParams.redirect; // 排除重定向参数
            delete businessParams.timestamp; // 排除时间戳参数

            // 过滤掉空值参数
            const filteredParams: any = {};
            Object.keys(businessParams).forEach(key => {
              const value = businessParams[key];
              if (value !== null && value !== undefined && value !== '') {
                filteredParams[key] = value;
              }
            });
            if (Object.keys(filteredParams).length > 0) {
              urlParams = JSON.stringify(filteredParams);
            }
          }
          const pageConfig = await usePageConfig(to.name as string, urlParams);
          if (pageConfig) {
            to.meta.pageConfig = pageConfig;
          }
        }
      }
      next();
    } else {
      // 注册动态路由
      registerRoutes(router).then(() => {
        routeFlag = true;
        next({...to, replace: true});
      }).catch(() => {
        // 处理异常事件
      })
    }
  } else {
    routeFlag = false;
    if (to.name === "login") {
      next();
    } else {
      next({
        name: "login",
        query: {redirect: to.fullPath},
      });
    }
  }
});

createRouteGuard(router);

export default router;
