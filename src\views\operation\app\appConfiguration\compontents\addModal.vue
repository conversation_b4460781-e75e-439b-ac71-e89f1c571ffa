<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="显示名">
                <a-input v-model="form.name"/>
            </a-form-item>
            <a-form-item v-if="props.showDesc" field="description" label="备注">
                <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {addOpCategoryList, addOpDeveloperList, addOpTagList, addOpTeamList, adSourceAdd, versionAdd} from "@/api/marketing/api";

const props = defineProps({
    showDesc:{
        type:Boolean,
        default:false
    }
})

const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    name: '',
    description:''
})
const rules = {
    name: [
        {
            required: true,
            message:'请填写显示名',
        }
    ],
}
const formRef = ref()
const emits = defineEmits(['updateData']);
const openModal = (name:string) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalTitle.value = name
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            //
            const operationMap = {
                '新增广告源': adSourceAdd,
                '新增应用类型':addOpCategoryList,
                '新增应用标签':addOpTagList,
                '新增项目组':addOpTeamList,
                '新增开发商':addOpDeveloperList,
            };
            const addFunction = operationMap[modalTitle.value];
            try {
                const params = modalTitle.value === '新增广告源' ? form.name : form
                await addFunction(params);
                Message.success('创建成功');
                modalVisible.value = false;
                emits('updateData', modalTitle.value);
            } catch (error) {
                console.error('创建失败:', error);
            }
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>
