<template>
    <!-- 日周月下拉选择 -->
    <a-dropdown :update-at-scroll="true" position="bl"  @select="windowSelect">
        <div class="select-label" :style="{border: props.showBorder ? '1px solid var(--tant-border-color-border1-1)' : 'none',padding : props.showBorder? '0 8px' :'0'}">
            {{ windowName }}<icon-down v-if="props.showBorder"/>
        </div>
        <template #content>
            <a-dsubmenu>
                <template #default>按日</template>
                <template #content>
                    <a-doption value="d">当日</a-doption>
                    <a-doption value="1d">次日</a-doption>
                    <a-doption value="7d">7日</a-doption>
                    <a-doption value="14d">14日</a-doption>
                    <a-doption :value="`${dValue}d`" style="width: 150px;">
                        <div class="doption-item">
                            <a-input-number
                                v-model:model-value="dValue" :style="{width:'60px',height:'24px'}"
                                :default-value="30"
                                :precision="0"
                                :min="0"
                                @click="(event) => event.stopPropagation()"/>
                            <div>日</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-dsubmenu>
                <template #default>按周</template>
                <template #content>
                    <a-doption v-for="(item,index) in weekItem" :key="item.value" :value="item.value">
                        <div class="doption-item">
                            <span>{{ item.label }}</span>
                            <a-trigger 
                                v-model:popup-visible="item.visible" trigger="click" position="bl"
                                :update-at-scroll="true" @click="(event) => event.stopPropagation()">
                                <a-tooltip content="自定义周" position="right">
                                    <icon-calendar class="hover-icon" />
                                </a-tooltip>
                                <template #content>
                                    <div class="week-set-body">
                                        <div class="week-set-title">自定义周</div>
                                        <div class="week-set-desc">
                                            按周统计时，将
                                            <span>星期一</span>
                                            作为周起始日
                                        </div>
                                        <div class="week-set-weeks">
                                            <div 
                                                v-for="(week,num) in weekDayList" :key="num" class="week-set-week"
                                                :class="{'week-active': weekName == week.label}"
                                                @click="weekChange(week)">{{ week.label }}</div>
                                        </div>
                                        <div class="week-set-btns">
                                            <a-button @click="() => item.visible = false">取消</a-button>
                                            <a-button type="primary" @click="firstWeekSure(index)">确定</a-button>
                                        </div>
                                    </div>
                                </template>
                            </a-trigger>
                        </div>
                    </a-doption>
                    <a-doption :value="`${wValue}w`" style="width: 150px;">
                        <div class="doption-item">
                            <a-input-number v-model:model-value="wValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0" @click="(event) => event.stopPropagation()"/>
                            <div>周</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-dsubmenu>
                <template #default>按月</template>
                <template #content>
                    <a-doption value="m">当月</a-doption>
                    <a-doption value="1m">次月</a-doption>
                    <a-doption value="3m">3月</a-doption>
                    <a-doption value="6m">6月</a-doption>
                    <a-doption :value="`${mValue}m`" style="width: 150px;">
                        <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                            <a-input-number
                                v-model:model-value="mValue"
                                :style="{width:'60px',height:'24px'}"
                                :default-value="30"
                                :precision="0"
                                :min="0"
                                @click="(event) => event.stopPropagation()"
                            />
                            <div>月</div>
                        </div>
                    </a-doption>
                </template>
            </a-dsubmenu>
        </template>
    </a-dropdown>
</template>

<script setup lang="ts">
import {ref, watch} from "vue";

const props = defineProps({
    timeSpanData:{
        type:Object,
        default:() => {}
    },
    showBorder:{
        type:Boolean,
        default:true
    }
})
const emits = defineEmits(['timeSpanChange'])
const dValue = ref(30)
const wValue = ref(16)
const mValue = ref(12)
const windowName = ref('7日')
const timeSpan = ref({
    number: 7,
    unit: "DAY",
    firstDayDfWeek: 1
})

const windowSelect = (v) => {
    const numbers = v.match(/\d+/g)?.join(',');
    timeSpan.value.number = numbers ? Number(numbers) : 0
    if(v.includes('d')){
        windowName.value = numbers ? `${numbers}日` : '当日'
        timeSpan.value.unit = 'DAY'
    }else if(v.includes('w')){
        windowName.value = numbers ? `${numbers}周` : '当周'
        timeSpan.value.unit = 'WEEK'
    }else if(v.includes('m')){
        windowName.value = numbers ? `${numbers}月` : '当月'
        timeSpan.value.unit = 'MONTH'
    }
    emits('timeSpanChange', timeSpan.value)

}
const weekItem = ref([
    {
        label:'当周',
        value:'w',
        visible:false
    },
    {
        label:'次周',
        value:'1w',
        visible:false
    },
    {
        label:'4周',
        value:'4w',
        visible:false
    },
    {
        label:'8周',
        value:'8w',
        visible:false
    }
])
const weekDayList = ref([
    {
        value:1,
        label:'星期一'
    },
    {
        value:2,
        label:'星期二'
    },
    {
        value:3,
        label:'星期三'
    },
    {
        value:4,
        label:'星期四'
    },
    {
        value:5,
        label:'星期五'
    },
    {
        value:6,
        label:'星期六'
    },
    {
        value:7,
        label:'星期天'
    }
])
const weekName = ref('星期一')
watch(() => props.timeSpanData,() => {
    timeSpan.value.number = props.timeSpanData?.number !== undefined ? props.timeSpanData?.number : timeSpan.value.number
    timeSpan.value.unit = props.timeSpanData?.unit ? props.timeSpanData?.unit : timeSpan.value.unit
    timeSpan.value.firstDayDfWeek = props.timeSpanData?.firstDayDfWeek ? props.timeSpanData?.firstDayDfWeek : timeSpan.value.firstDayDfWeek
    const numbers = timeSpan.value.number;
    if (timeSpan.value.unit === 'DAY') {
        windowName.value = numbers ? `${numbers}日` : '当日';
    } else if (timeSpan.value.unit === 'WEEK') {
        windowName.value = numbers ? `${numbers}周` : '当周';
    } else if (timeSpan.value.unit === 'MONTH') {
        windowName.value = numbers ? `${numbers}月` : '当月';
    }
    const dayWeek = timeSpan.value.firstDayDfWeek
    weekDayList.value.forEach(element => {
        if(dayWeek === element.value){
            weekName.value = element.label
        }
    });
},{immediate:true})
const weekChange = (item:any) => {
    weekName.value = item.label
    timeSpan.value.firstDayDfWeek = item.value
}
const firstWeekSure = (index) => {
    weekItem.value[index].visible = false
    emits('timeSpanChange', timeSpan.value)
}
</script>

<style lang="less" scoped>
.select-label{
    display: inline-block;
    height: 32px;
    color: var(--tant-text-gray-color-text1-2);
    line-height: 32px;
    background-color: var(--tant-bg-white-color-bg1-1);
    // border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    white-space: nowrap;
}
.arco-dropdown-open .arco-icon-down {
    transform: rotate(180deg);
}
.doption-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 120px;
    .hover-icon{
        opacity: 0;
    }
    &:hover{
        .hover-icon{
            opacity: 1;
        }
    }
}
.week-set-body{
    width: 352px;
    padding: 16px 16px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-bottom);
    .week-set-title{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }
    .week-set-desc{
        display: block;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;
        span{
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            padding: 0 4px;
        }
    }
    .week-set-weeks {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-right: -16px;
        padding-bottom: 16px;
        .week-set-week {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 74px;
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 5px 16px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            white-space: nowrap;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-active);
                border-color: var(--tant-secondary-color-secondary-fill-active);
            }
        }
        .week-active{
                background-color: var(--tant-secondary-color-secondary-fill-active);
                border-color: var(--tant-secondary-color-secondary-fill-active);
        }
        
    }
    .week-set-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: -16px;
        margin-left: -16px;
        padding-top: 8px;
        padding-right: 16px;
        border-top: 1px solid var(--tant-border-color-border1-1);
        button{
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
}
</style>
