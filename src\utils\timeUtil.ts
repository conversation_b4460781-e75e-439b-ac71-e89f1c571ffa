export const timezones = [
  'UTC-12', 'UTC-11', 'UTC-10', 'UTC-09', 'UTC-08',
  'UTC-07', 'UTC-06', 'UTC-05', 'UTC-04', 'UTC-03',
  'UTC-02', 'UTC-01', 'UTC+00', 'UTC+01', 'UTC+02',
  'UTC+03', 'UTC+04', 'UTC+05', 'UTC+06', 'UTC+07',
  'UTC+08', 'UTC+09', 'UTC+10', 'UTC+11', 'UTC+12',
  'UTC+13', 'UTC+14'
];

export function getTimeInTimezone(targetTimezone, dateTime = new Date(), format = 'HH:mm') {
  // 验证目标时区是否有效
  if (!timezones.includes(targetTimezone)) {
    throw new Error('Invalid timezone provided.');
  }

  // 解析目标时区的偏移量
  const offset = parseInt(targetTimezone.replace('UTC', ''), 10);

  // 获取当前时间的 UTC 时间
  const utcTime = dateTime.getTime() + dateTime.getTimezoneOffset() * 60000;

  // 计算目标时区的时间
  const targetTime = new Date(utcTime + offset * 3600000);

  // 格式化时间
  return formatTime(targetTime, format);
}

// 格式化时间函数
export function formatTime(date, format = 'HH:mm:ss') {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始
  const year = date.getFullYear();

  // 替换格式字符串中的占位符
  return format
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
    .replace('dd', day)
    .replace('MM', month)
    .replace('yyyy', year);
}

/**
 * 格式化请求参数中的时区
 * UTC+00 -> +00:00
 *
 * @param timezone
 */
export function formatTimeZoneRequestParam(timezone: string) {
  if (!timezone?.includes('UTC')) {
    return timezone;
  }
  return `${timezone.replace('UTC', '')}:00`;
}

/**
 * 格式化时区的展示样式
 *  +00:00 -> UTC+00
 *
 * @param timezone
 */
export function formatTimeZoneDisplay(timezone: string) {
  if (!timezone?.includes(':00')) {
    return timezone;
  }
  return `UTC${timezone.replace(':00', '')}`;
}


