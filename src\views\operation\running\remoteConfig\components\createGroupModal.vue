<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" title="命名群组" :footer="false">
        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="name" label="组名称" :hide-asterisk="true">
                <a-input v-model="form.name"/>
            </a-form-item>
            <a-form-item field="instructions" label="说明（可选）">
                <a-textarea v-model="form.instructions" :auto-size="{ minRows:2, maxRows:5}"/>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :disabled="!saveAble" @click="saveCondition">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";

const modalVisible = ref(false)
const form = reactive({
    name: '',
    instructions: '',
})
const rules = {
    name: [
        {
            required: true,
            message:'必须填写组名称',
        }
    ],
}
const formRef = ref()
const isValid = ref(false);

const saveAble = computed(() => isValid.value);
// 在需要验证的地方
const validateForm = () => {
  formRef.value.validate((errors: any) => {
    isValid.value = !errors;
    console.log(isValid.value,'isValid.value');
  });
};

// 监听表单变化时触发验证
watch(() => form, () => {
  validateForm();
}, { deep: true });
const emits = defineEmits(['conditionChange']);

const openModal = () => {
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
    formRef.value.resetFields()
    formRef.value.clearValidate()
}
const saveCondition = () => {
    emits('conditionChange',form)
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>