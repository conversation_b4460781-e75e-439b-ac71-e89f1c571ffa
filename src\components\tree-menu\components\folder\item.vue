<script setup lang="ts">
import {onMounted, ref} from "vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {removeFolder, saveFolder} from "@/api/folder/api";
import {Message} from "@arco-design/web-vue";
import {CacheEventBus, DeleteEvent, RefreshEvent, RenameEvent, TreeMenuEventBus} from "@/types/event-bus";
import _ from "lodash";
import {useDashboardStore} from "@/store";
import moveFloder from "./move-folder.vue"
import Dashboard from "../dashboard/index.vue";
import Folder from "./index.vue";


interface Props {

  /**
   * 空间编码
   */
  spaceId?: string

  /**
   * 看板空间
   */
  folder?: any
}

const props = defineProps<Props>()
const emit = defineEmits(['add']);
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const unfoldFolders = useSessionStorage("unfoldFolders", new Set<string>());
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})
const folderFold = ref<boolean>(true)
const cacheEventBus = useEventBus(CacheEventBus)
const dashboardStore = useDashboardStore()
// 看板开关操作
const foldFolder = () => {
  folderFold.value = !folderFold.value
  if (props.folder && !folderFold.value) {
    unfoldFolders.value.add(props.folder.folderId)
    return;
  }
  if (props.folder && folderFold.value) {
    unfoldFolders.value.delete(props.folder.folderId)
  }
  
}

// 监听已打开的文件夹
// watch(unfoldFolders, (newVal) => {
//   folderFold.value = !newVal.has(props.folder.folderId)

// },
// {immediate: true},)

// 挂载时根据已选择看板打开文件夹
onMounted(() => {
  // console.log(props.folder,'props.folder');
  // console.log(unfoldFolders.value,'unfoldFolders.value');
  if (!props.folder) {
    return
  }
  
  folderFold.value = !unfoldFolders.value.has(props.folder.folderId)
  if (props.folder.folderId === dashboardStore.dashboardSelected?.folderId) {
    unfoldFolders.value.delete(props.folder)
    folderFold.value = false
  }
})
const moveModalShow = ref(false)
/*
 * 重命名
 */
const renameModalShow = ref(false);
const myRenameSelected = ref<string | undefined>(props.folder?.name)
/**
 * 删除操作
 */

const deleteModalShow = ref(false);
const handleFolderOperation = (value: string) => {
  switch (value) {
    case "move":
      moveModalShow.value = true
      break
    case "rename":
      renameModalShow.value = true
      break
    case "delete":
      deleteModalShow.value = true
      break
    default:
  }
}
const deleteFolder = (folderId: string) => {
  dashboardStore.spaceList.some(space => {
    // 查找“未分组”和“共享给我的”文件夹
    const unassignedFolder = space.folders.find((folder: any) => folder.name === '未分组');
    const sharedFolder = space.folders.find((folder: any) => folder.name === '分享给我的');

    // 查找目标文件夹
    const folderIndex = space.folders.findIndex((folder: any) => folder.folderId === folderId);

    if (folderIndex !== -1) {
      const targetFolder = space.folders[folderIndex];

      // 判断是否为“我的看板”space
      if (space.name === '我的看板' && unassignedFolder && sharedFolder) {
        // 将目标文件夹中的 dashboards 推入到“未分组”和“共享给我的”文件夹中
        targetFolder.dashboards.forEach((dashboard: any) => {
          if (dashboard.authority === 1 || dashboard.authority === 2) {
            unassignedFolder.dashboards.push(dashboard);
          } else if (dashboard.authority === 3 || dashboard.authority === 4) {
            sharedFolder.dashboards.push(dashboard);
          }
          // 可在此处添加更多的 authority 处理逻辑
        });
      } else {
        // 非“我的看板”space，将 dashboards 移动到 space 的 dashboards 中
        space.dashboards = space.dashboards.concat(targetFolder.dashboards);
      }

      // 从 folders 中删除目标 folder
      space.folders.splice(folderIndex, 1);
      return true; // 找到并处理后退出循环
    }

    return false; // 未找到则继续
  });
};

const renameFolder = (folderId: string, name: string) => {
  dashboardStore.spaceList.some(space => {
    // 查找目标文件夹
    const foundInFolder = space.folders.find((folder: any) => folder.folderId === folderId);

    if (foundInFolder) {
      // 如果找到目标文件夹，修改其名称
      foundInFolder.name = name;
      return true; // 找到后退出循环
    }

    return false; // 未找到则继续
  });
}
cacheEventBus.on((event, {type, folderId,}) => {
  if (event === DeleteEvent && type === 'folder')
    deleteFolder(folderId)
})
cacheEventBus.on((event, {type, folderId, name}) => {
  if (event === RenameEvent && type === 'folder')
    renameFolder(folderId, name)
})
const deleteConfirm = () => {
  if (!props.folder?.folderId) {
    return
  }
  removeFolder(props.folder?.folderId).then((resp) => {
    if (props.folder?.dashboards?.find(item => item.dashboardId === dashboardSelected.value.dashboardId)) {
      treeMenuEventBus.emit(RefreshEvent);
    }
    // 缓存删除
    cacheEventBus.emit('delete-event', {
      type: 'folder',
      folderId: props.folder?.folderId,
    })
  }).catch((e) => {
    Message.error("删除文件夹失败！", e)
  })
  if (props.folder.space.authority === 1 && props.folder.folderId === dashboardSelected.value.folderId) {

    dashboardSelected.value = {
      ...dashboardSelected.value,
      folderId: dashboardStore.spaceList.find((space: any) => {
        // 找到具有“未分组”文件夹的空间，并返回该文件夹的 folderId
        const folder = space.folders.find((folder: any) => folder.name === '未分组');
        return folder ? folder.folderId : null; // 如果找到了文件夹，返回其 folderId，否则返回 null
      })?.folderId || null, // 使用 Optional chaining 和默认值，以防找不到文件夹
    };
  }
  if (props.folder.space.authority !== 1 && props.folder.folderId === dashboardSelected.value.folderId) {
    dashboardSelected.value = {
      ...dashboardSelected.value,
      folderId:'',
    }
  }
  deleteModalShow.value = false;

}

const deleteCancel = () => {
  deleteModalShow.value = false;
}

const renameConfirm = () => {
  if (props.folder?.folderId) {
    if (_.isEmpty(myRenameSelected.value)) {
      Message.warning('文件夹名称不能为空');
      return;
    }
    saveFolder({name: myRenameSelected.value, folderId: props.folder.folderId})
        .then((resp) => {
          cacheEventBus.emit('rename-event', {
            type: 'folder',
            folderId: props.folder?.folderId,
            name: myRenameSelected.value
          });
          Message.success('编辑成功')
        })
        .catch((e) => {
          Message.error("重命名失败！", e);
        });
  } else {
    Message.error(' ID 无效');
  }
  renameModalShow.value = false;

};

const renameCancel = () => {
  myRenameSelected.value = props.folder?.name
  renameModalShow.value = false
}
const showIcon = ref(false)
const popupVisibleChange = (v: boolean) => {
  showIcon.value = v
}
</script>

<template>
  <div class="container">
    <div
        class="folder"
        :class="props.folder?.space.authority!==1?'my-space-folder':''"
        :style="{backgroundColor:showIcon?'var(--tant-secondary-color-secondary-fill-hover)':''}"
        @click="foldFolder">
      <div class="name">
        <div class="arrow">
          <img :class="folderFold?'':'transform90'" src="/icon/arrow-right.svg" alt=""/>
        </div>
        <div class="icon">
          <img v-if="folderFold" src="/icon/folder.svg" alt=""/>
          <img v-else src="/icon/folder-open.svg" alt=""/>
        </div>
        <div>
          {{ folder?.name }}
        </div>
      </div>
      <div v-if="folder?.allowAdd!==false" class="operation" :style="showIcon?'visibility: visible;':''">
        <a-tooltip content="新建看板">
          <a-button
              v-if="props.folder?.space.authority!==4"
              type="text" class="create-btn"
              @click.stop
              @click="()=>{emit('add','addDashboard',spaceId,folder?.folderId)}">
            <template #icon>
              <icon-plus size="15"/>
            </template>
          </a-button>
        </a-tooltip>

        <a-dropdown
            v-if="folder?.isDeletable" position="bl"
            @click.stop @select="handleFolderOperation" @popup-visible-change="popupVisibleChange">
          <a-button
              v-if="props.folder?.space.authority!==4"
              type="text" class="create-btn"
              :style="showIcon?'background-color: var(--tant-secondary-color-secondary-transp-hover);':''">
            <template #icon>
              <icon-more-vertical size="15"/>
            </template>
          </a-button>
          <template #content>
            <a-doption value="rename">重命名</a-doption>
            <a-doption value="move">移动至</a-doption>
            <a-doption class="delete-option" value="delete">删除</a-doption>
          </template>
        </a-dropdown>

      </div>
    </div>
    <Folder
        v-show="!folderFold" :folders="folder?.folders" :space-id="spaceId"
        @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"/>
    <dashboard
        v-show="!folderFold" :dashboards="folder?.dashboards" :space-id="spaceId" :folder-id="folder?.folderId"
        :allow-add="folder?.allowAdd"
        @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"/>
    <move-floder
        v-if="moveModalShow"
        v-model:visible="moveModalShow"
        :folder="folder"/>
    <a-modal
        v-model:visible="deleteModalShow"
        :ok-button-props="{status:'danger'}"
        :mask-closable="false"
        ok-text="删除" title-align="start"
        @ok="deleteConfirm" @cancel="deleteCancel">
      <template #title>
        删除文件夹
      </template>
      <div v-if="props.folder?.space.authority===1">删除后文件夹下的自建看板将移动到【未分组】下，他人共享看板将移动到【共享给我的】下，该操作不可恢复。
        确定要删除【{{ folder?.name }}】文件夹吗？
      </div>
      <div v-else>文件夹下的看板将移动至空间根目录，该操作不可恢复。 确认删除【{{ folder?.name }}】？</div>
    </a-modal>
    <a-modal
        :visible="renameModalShow"
        width="452px"
        :closable="false" ok-text="重命名" title-align="start"
        @ok="renameConfirm"
        @cancel="renameCancel">
      <template #title>
        重命名文件夹
      </template>
      <div class="rename-input">
        <a-input
            v-model="myRenameSelected"
            :style="{width:'388px'}"
            placeholder="请输入文件夹名称"
            allow-clear/>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.my-space-folder {
  padding: 0 8px 0 16px !important;
}

.folder {
  max-width: 260px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  //padding: 0 8px 0 16px;
  border-radius: 4px;
  cursor: pointer;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .name {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;

    .arrow {
      height: 16px;
      margin-right: 8px;
      color: var(--tant-text-gray-color-text1-3);
      visibility: visible;
    }

    .transform90 {
      transform: rotate(90deg);
    }

    .icon {
      height: 16px;
      margin-right: 8px;
    }
  }

  .operation {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-right: 8px;
    visibility: hidden;

    .create-btn {
      height: 24px;
      width: 24px;
      color: var(--tant-secondary-color-secondary-default);
      font: var(--tant-body-font-body-regular);
      text-transform: capitalize;
      border-radius: var(--tant-border-radius-medium);
      margin-left: 8px;
    }
  }
}

.folder:hover {
  background-color: var(--tant-secondary-color-secondary-fill-hover);
}

.create-btn:hover {
  background-color: var(--tant-secondary-color-secondary-transp-hover);
}

.folder:hover .operation {
  visibility: visible;
}

.delete-option:hover {
  background-color: var(--tant-status-danger-color-danger-fill);
  color: var(--tant-status-danger-color-danger-default);
}

.rename-input {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>