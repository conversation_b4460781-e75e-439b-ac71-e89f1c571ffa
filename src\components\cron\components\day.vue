<template>
  <div :val="computedValue">
    <div class="mb-8">
      <a-radio v-model="type" value="1" size="small">每日</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="5" size="small">不指定</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="2" size="small">周期</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">从</span>
      <a-input-number v-model="cycle.start" :min="1" :max="31" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">至</span>
      <a-input-number v-model="cycle.end" :min="2" :max="31" size="small" style="width: 100px;" @change="type = '2'"></a-input-number>
      日
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="3" size="small">循环</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">从</span>
      <a-input-number v-model="loop.start" :min="1" :max="31" size="small" style="width: 100px;" @change="type = '3'"></a-input-number>
      <span style="margin-left: 5px; margin-right: 5px;">日开始，每</span>
      <a-input-number v-model="loop.end" :min="1" :max="31" size="small" style="width: 100px;" @change="type = '3'"></a-input-number>
      日执行一次
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="8" size="small">工作日</a-radio>
      <span style="margin-left: 10px; margin-right: 5px;">本月</span>
      <a-input-number v-model="work" :min="1" :max="7" size="small" style="width: 100px;" @change="type = '8'"></a-input-number>
      号，最近的工作日
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="6" size="small">本月最后一天</a-radio>
    </div>
    <div class="mb-8">
      <a-radio v-model="type" value="4" size="small">指定</a-radio>
      <a-checkbox-group v-model="appoint">
        <div v-for="(row, i) in getDayOptions" :key="i" style="margin-left: 10px; line-height: 25px;">
          <a-checkbox 
            v-for="option in row" 
            :key="option.value"
            :value="option.value"
            @change="type = '4'"
          >
            {{option.label}}
          </a-checkbox>
        </div>
      </a-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '?'
  }
})

const emit = defineEmits(['update:modelValue'])

const type = ref('1') // 类型
const cycle = ref({ // 周期
  start: 1,
  end: 2
})
const loop = ref({ // 循环
  start: 0,
  end: 1
})
const work = ref(1)
const appoint = ref<string[]>([]) // 指定

const computedValue = computed(() => {
  let result = ''
  switch (type.value) {
    case '1': // 每日
      result = '*'
      break
    case '2': // 周期
      result = `${cycle.value.start}-${cycle.value.end}`
      break
    case '3': // 循环
      result = `${loop.value.start}/${loop.value.end}`
      break
    case '4': // 指定
      result = appoint.value.join(',')
      break
    case '5': // 不指定
      result = '?'
      break
    case '6': // 本月最后一天
      result = 'L'
      break
    case '8': // 工作日
      result = `${work.value}W`
      break
    default:
      result = '*'
      break
  }
  emit('update:modelValue', result)
  return result
})
const getDayOptions = computed(() => {
  const options = [];
  for (let i = 1; i <= 4; i++) {
    const row = [];
    for (let j = 1; j <= 10; j++) {
      const value = parseInt((i - 1) + '' + (j - 1));
      if (value < 32 && !(i === 1 && j === 1)) {
        row.push({
          value: value.toString().padStart(2, '0'),
          label: value.toString().padStart(2, '0')
        });
      }
    }
    options.push(row);
  }
  return options;
});
watch(() => props.modelValue, (val) => {
  console.log('val', props.modelValue)
  if (!val) {
    val = '*'
  }
  if (val === '*') {
    type.value = '1'
  } else if (val === '?') {
    type.value = '5'
  } else if (val === 'L') {
    type.value = '6'
  } else if (val.indexOf('-') !== -1) { // 2周期
    if (val.split('-').length === 2) {
      type.value = '2'
      cycle.value.start = parseInt(val.split('-')[0])
      cycle.value.end = parseInt(val.split('-')[1])
    }
  } else if (val.indexOf('/') !== -1) { // 3循环
    if (val.split('/').length === 2) {
      type.value = '3'
      loop.value.start = parseInt(val.split('/')[0])
      loop.value.end = parseInt(val.split('/')[1])
    }
  } else if (val.indexOf('W') !== -1) { // 8工作日
    if (val.split('W').length === 2) {
      type.value = '8'
      work.value = parseInt(val.split('W')[0])
    }
  } else if (val.indexOf(',') !== -1) { // 4指定
    type.value = '4'
    appoint.value = val.split(',')
  } else {
    type.value = '1'
  }
}, { immediate: true })
</script>

<style lang="less" scoped>
.mb-8 {
  margin-bottom: 8px;
}
</style>