<template>
    <div class="page">
        <div class="page-head">
            <div class="title">
                {{ route.meta.locale }}
            </div>
            <div class="filter">
                <div class="filter-item">
                    <a-select
                        :options="devicesOptions"
                        v-model:model-value="selectedDevices"
                        :style="{width:'200px'}"
                        placeholder="请选择设备"
                        :field-names="{value: 'id', label: 'label'}"
                        :loading="devicesLoading"
                        @focus="getDevices"
                        @change="deviceChange"
                        allow-search />
                </div>
                <div class="filter-item">
                    <a-select
                        :options="logLvlOptions"
                        v-model:model-value="selectedLogLvl"
                        :style="{width:'200px'}"
                        :field-names="{value: 'id', label: 'label'}"
                        allow-search />
                </div>
                <div class="filter-item">
                    <a-input-search :style="{width:'200px'}" v-model:model-value="keyword" allow-clear placeholder="关键词搜索"/>
                </div>
            </div>
        </div>
        <div class="page-body ">
            <div class="cross-content">
                <a-list
                    ref="logListRef"
                    :virtualListProps="{
                        fixedSize: true,
                      height: 900,
                    }"
                    :data="showLogs"
                >
                    <template #item="{ item, index }">
                        <a-list-item :key="index">
                            <a-list-item-meta
                            >
                                <template #description>
                                    <p :style="{color: item.color}">{{item.c}}</p>
                                </template>
                            </a-list-item-meta>
                        </a-list-item>
                    </template>
                </a-list>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {useRoute} from "vue-router";
import {useWebSocket} from '@vueuse/core'
import {computed, ref, nextTick} from "vue";
import {Message} from '@arco-design/web-vue';

const route = useRoute();
const baseWsURL = process.env?.BASE_PACK_URL
const token = localStorage.getItem('token');
const retriesNum = process.env.NODE_ENV === 'development' ? 1 : -1
const {status, data, send, open, close} = useWebSocket(`${baseWsURL}/api/log/ws?token=${token}`, {
    autoReconnect: {
        retries: retriesNum,
        delay: 2000,
        onFailed() {
            console.error('重试后无法连接 WebSocket！')
        },
    },
    onConnected(ws) {
        console.debug('websocket 连接成功！')
    },
    onDisconnected(ws, event) {
        console.warn('websocket 连接断开连接！', ws)
    },
    onError(ws, event) {
        Message.error('websocket 连接错误！')
        console.error('websocket 连接错误！', ws)
    },
    onMessage(ws, event) {
        if (event.type === 'message') {
            const message = JSON.parse(event.data)
            if (message.method) {
                switch (message.method) {
                    case 'error':
                        Message.error(message.message)
                        break
                    case 'device_list':
                        devicesLoading.value = false
                        devicesOptions.value = []
                        if (message.data.devices) {
                            message.data.devices.forEach(v => {
                                devicesOptions.value.push({id: v, label: v})
                            })
                        }
                        break
                    case 'switch_device':
                        logList.value = []
                        break
                    case 'log_content':
                        if (message.data) {
                            logList.value.push(...message.data)
                        }
                }
            } else {
                console.error(message)
            }
        }
    }
})

const logListRef = ref()
const devicesLoading = ref(false)
const devicesOptions = ref([])
const logLvlOptions = [
    {id: '', label:'所有日志'},
    {id: 'I', label:'INFO日志'},
    {id: 'D', label:'DEBUG日志'},
    {id: 'V', label:'VERBOSE日志'},
    {id: 'W', label:'WARN日志'},
    {id: 'E', label:'ERROR日志'}
]
const selectedDevices = ref('')
const selectedDevicesOld = ref('')
const selectedLogLvl = ref('')
const keyword = ref('')
const logList = ref([])

const getDevices = () => {
    if (devicesLoading.value) {
        Message.warning('正在刷新，请稍后')
        return
    }

    devicesLoading.value = true
    send(JSON.stringify({
        method: 'device_list',
        data: {}
    }))
}

const showLogs = computed(() => {
    let list = []

    if (logList.value.length) {
        logList.value.forEach(l => {
            let allow = true
            const lInfo = l.split(' ')
            if (selectedLogLvl.value) {
                allow = lInfo[4] === selectedLogLvl.value
            }

            if (allow && keyword.value) {
                allow = l.includes(keyword.value)
            }

            if (allow) {
                switch (lInfo[4]) {
                    case 'I':
                        list.push({c: l, color: '#668922'})
                        break;
                    case 'D':
                        list.push({c: l, color: '#1f627b'})
                        break;
                    case 'V':
                        list.push({c: l, color: ''})
                        break;
                    case 'W':
                        list.push({c: l, color: 'orange'})
                        break;
                    case 'E':
                        list.push({c: l, color: '#f53f3f'})
                        break;
                }
            }
        })
    }

    scrollToBottom(list.length)
    return list
})

const deviceChange = (value: string) => {
    const data = {
        old_device: selectedDevicesOld.value,
        new_device: value
    }
    send(JSON.stringify({
        method: 'switch_device',
        data: data
    }))
    selectedDevicesOld.value = value
}

const scrollToBottom = async (index) => {
    await nextTick(); // 等待 DOM 更新

    // 查找虚拟列表容器
    if (logListRef.value) {
        logListRef.value.scrollIntoView({
            index: index > 0 ? index -1 : 0,
            align: 'bottom'
        })
    }
};

const init = () => {
    getDevices()
}
init()
</script>

<style scoped lang="less">
.page {
    .page-body {
        .cross-content {
            height: 100%;

            .arco-list-wrapper {
                height: 100%;
            }
            :deep(.arco-scrollbar-type-embed){
                height: 100%;
            }
            :deep(.arco-list){
                height: 100%;
                line-height: 1;
            }
            :deep(.arco-list-content-wrapper){
                height: 100%;
            }
            :deep(.arco-virtual-list){
                height: 100% !important;
            }
            :deep(.arco-list-item) {
                padding: 0 15px;
            }
            :deep(.arco-list-item-meta-description) {
                line-height: 1.5;
            }
            :deep(.arco-list-split .arco-list-item:not(:last-child)) {
               border-bottom: unset;
            }
        }
    }
}
</style>
