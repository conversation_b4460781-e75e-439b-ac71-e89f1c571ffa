<template>
  <a-layout class="layout">
    <a-layout-header class="layout__header">
      <div class="header-title">
        <button class="title-button" @click="goBack">
            <span role="img">
              <icon-left size="13"/>
            </span>
        </button>
        <span style="align-items: center;margin-right: 10px">{{ detailData?.name }}</span>
        <a-tag :color="getStatusColor(detailData?.status)">
          {{ getStatusLabel(detailData?.status) }}
        </a-tag>
      </div>
    </a-layout-header>

    <a-layout-content class="layout__content">
      <a-spin :loading="loading" class="container">
        <a-tabs v-model:active-key="tabsKey" @change="handleTabChange">
          <template v-if="tabsKey === '1' && detailData.status === 4" #extra>
            <a-button v-if="!editStatus" class="handle-btn" type="primary" @click="editData">编辑</a-button>
            <a-button v-if="editStatus" class="handle-btn" type="primary" :loading="saveLoading" @click="saveData">保存</a-button>
            <a-button v-if="editStatus" class="handle-btn" style="margin-left: 24px;" @click="cancelEdit">取消</a-button>
          </template>
          <a-tab-pane key="1" title="实验详情">
            <DetailTab v-if="!editStatus" ref="detailTabRef" :detail-data="detailData"/>
            <EditTab v-else ref="editTabRef" :detail-data="detailData" @update-detail="updateDetail"/>
          </a-tab-pane>
          <a-tab-pane v-if="detailData.status > 0" key="2" title="实验报告">
            <ReportTab :experiment-detail="detailData"/>
          </a-tab-pane>
          <a-tab-pane v-if="detailData.status > 0" key="3" title="事件指标分析">
            <event-analyse-panel :experiment-detail="detailData"/>
          </a-tab-pane>
        </a-tabs>
      </a-spin>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
import {ref} from "vue";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import {useRoute} from 'vue-router';
import {getExperimentDetail} from "@/api/ab/api";
import {Message} from "@arco-design/web-vue";
import DetailTab from "./tabs/DetailTab.vue";
import ReportTab from "./tabs/ReportTab.vue";
import EventAnalysePanel from "./tabs/EventAnalysePanel.vue";
import EditTab from "./tabs/EditTab.vue";


const route = useRoute()
const tabsKey = ref('1')
const experimentCode = ref('');
const goBack = () => {
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT,
  });
}
// 查看报告
const handleTabChange = () => {
  router.push({
    name: ROUTE_NAME.AB_TEST_EXPERIMENT_DETAIL,
    query: {
      code: experimentCode.value,
      tab: tabsKey.value
    }
  })
}
const STATUS_OPTIONS = [
  {value: 0, label: '草稿'},
  {value: 1, label: '进行中'},
  {value: 2, label: '已终止'},
  {value: 3, label: '已结束'},
  {value: 4, label: '调试中'},
] as const;
const getStatusColor = (status: string) => {
  const colorMap = {
    0: 'lime',    // 预支付
    1: 'blue', // 进行中
    2: 'red',      // 已终止
    3: 'gray',    // 已结束
    4: 'orange'     // 调试中
  };
  return colorMap[status] || 'gray';
};
const getStatusLabel = (status: string) => {
  return STATUS_OPTIONS.find(option => option.value === status)?.label || '未知状态';
};

const editStatus = ref(false)
const editData = () => {
  editStatus.value = true
}
const saveLoading = ref(false)
const editTabRef = ref()

const updateDetail = async() => { 
  try {
    const res = await getExperimentDetail(experimentCode.value)
    if (!res) {
      // 实验数据请求失败
      Message.warning("实验数据请求失败，请重新选择！")
      goBack()
    }
    Message.success('保存成功')
    detailData.value = res;
    editStatus.value = false
  } catch (error) {
    console.error('保存失败:', error);
  }
}
const saveData = async() => {
  await editTabRef.value.checkSubmit();
}
const cancelEdit = () => {
  editStatus.value = false
}
// 获取详情
const detailData = ref({})
const loading = ref(false)
const init = async () => {
  tabsKey.value = (route.query.tab as string) || '1'
  loading.value = true;
  // 编辑时,接口获取详情
  experimentCode.value = route.query.code as string || '';
  try {
    const res = await getExperimentDetail(experimentCode.value)
    if (!res) {
      // 实验数据请求失败
      Message.warning("实验数据请求失败，请重新选择！")
      goBack()
    }
    detailData.value = res;
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false;
  }
}
init()
</script>

<style scoped lang="less">
.layout {
  background-color: var(--tant-bg-gray-color-bg2-1);
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;

  .layout__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 12px 24px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .header-title {
      flex: 1 1;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header4-medium);
      align-items: center;

      .title-button {
        color: var(--tant-text-gray-color-text1-2);
        background-color: transparent;
        border: none;
        margin-right: 4px;
        padding: 8px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }

      .title-button:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
        cursor: pointer;
        transition: .5s;

      }

      .title-button > span {
        display: flex;
        align-items: center;
        line-height: normal;
      }

    }
  }

  .layout__content {
    display: flex;
    flex: 1 1;
    height: calc(100vh - 100px);
    margin: 24px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border-radius: 4px;
  }
}

.container {
  width: 100%;
  height: calc(100vh - 120px);
  // overflow: auto;
  padding: 20px 20px;
}

:deep(.arco-tabs-content) {
  height: calc(100vh - 160px);
  overflow: auto;
}
.handle-btn{
  border-radius: var(--tant-border-radius-medium);
}
</style>