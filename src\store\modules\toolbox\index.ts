import {defineStore} from 'pinia';
import {getEvtCategoryList, getFilterCategoryList} from "@/api/analyse/api";
import {IndicatorType} from "@/api/enum";
import router from '@/router';
import { computed} from "vue";


const pageType = computed(() => router.currentRoute.value.query?.pageType);
const toolStore = defineStore('toolData', {
  state: () => ({
    toolModelList: [] as any, // 分析事件||指标列表
    temporaryList: [] as any, // 临时列表
    attrFlatList: [] as any, // 属性分类平铺列表
    totalEvtIndCodes: [] as any, // 事件指标code列表
    allAttrList: [] as any, // 属性分类列表
  }),
  actions: {
    /**
     * API参数说明:
     * @param {string | null} label - 可选，枚举值: event | indicator
     * @param {0 | 1} inApp - 可选，默认值: 0
     * @param {string[]} eventTypes - 可选，事件类型列表
     * @param {string[]} indicatorTypes - 可选，指标类型列表
     * @param {string} subjectCode - 可选，查询运营分析指标时必传
     */
    async fetchData(apiFunc: Function, stateKey: string, ...args: any) {
      try {
        const res = await apiFunc(...args);
        if (res) {
          this[stateKey] = res;
          return res;
        }
        console.log(`${stateKey} 数据为空:`, res);
        return [];
      } catch (error) {
        console.error(`获取${stateKey}失败:`, error);
        throw error;
      }
    },
    // 获取事件指标组合模型列表
    fetchAllModalList() {
      return this.fetchData(getEvtCategoryList, 'toolModelList', {
        inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
        indicatorTypes:[IndicatorType.EVENT, IndicatorType.RETENTION]
      });
    },
    // 获取事件模型列表
    fetchEventModalList() {
      return this.fetchData(getEvtCategoryList, 'toolModelList', {
        objectType: 'event',
        inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
        indicatorTypes:[IndicatorType.EVENT, IndicatorType.RETENTION]
      });
    },
    // 获取运营模型列表
    fetchOperationModalList(subjectCode:string) {
      return this.fetchData(getEvtCategoryList, 'toolModelList', {
        objectType: 'indicator',
        inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
        indicatorTypes:[IndicatorType.OPERATION],
        subjectCode
      });
    },
    // 获取群组模型列表
    fetchGroupModalList() {
      return this.fetchData(getEvtCategoryList, 'toolModelList', {
        isGroup: 1,
        inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
        indicatorTypes:[IndicatorType.EVENT, IndicatorType.GROUP,IndicatorType.RETENTION]
      });
    },
    // 获取归因模型列表
    fetchTrafficModalList(subjectCode:string) {
      return this.fetchData(getEvtCategoryList, 'toolModelList', {
        inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
        indicatorTypes:[IndicatorType.TRAFFIC,IndicatorType.EVENT],
        subjectCode
      });
    },

    // 更新临时列表
    updateTemporaryList(list: any[]) {
      this.temporaryList = list;
    },
    // 获取属性分类列表
    async fetchAttrFlatList(params: any) {
      try {
          const res = await getFilterCategoryList(
            {
              ...params,
              attributeType: ['event','user','cluster','tag']
            });
          if (res) {
              // 处理数据结构
              this.attrFlatList = res.map((category) => {
                  let items = [];
                  if (category.items?.length > 0) {
                      if (category.category === '事件属性') {
                          const eventCodes = params.event;
                          // 获取所有事件的属性交集
                          const allEventAttributes = category.items
                              .map(item => {
                                  const eventCode = Object.keys(item)[0];
                                  return eventCodes.includes(eventCode) ? item[eventCode] : [];
                              })
                              .filter(attrs => attrs.length > 0);

                          if (allEventAttributes.length > 0) {
                              // 获取所有事件属性的交集
                              items = allEventAttributes.reduce((common, current) => {
                                  return common.filter(attr => 
                                      current.some(currentAttr => currentAttr.code === attr.code)
                                  );
                              });
                          }
                      } else {
                          items = category.items;
                      }
                  }
                  return {
                      categoryName: category.category,
                      itemData: items
                  };
              });
              return this.attrFlatList;
          }
          return [];
      } catch (error) {
          console.error('获取属性分类列表失败:', error);
          throw error;
      }
    },
    // 添加codes列表
    async addEvtIndCodes(code:string){
      const oldCodes = [...this.totalEvtIndCodes];
      // 如果code不存在才添加
      if (!this.totalEvtIndCodes.includes(code)) {
        this.totalEvtIndCodes.push(code);
      }
      // 比较新旧数组是否相同
      const isEqual = oldCodes.length === this.totalEvtIndCodes.length && 
      oldCodes.every((item, index) => item === this.totalEvtIndCodes[index]);
      if(!isEqual){
        await this.fetchAllAttrList();
        return true; // 返回true表示数据已更新
      }
      return false; // 返回false表示数据未更新
    },
    async addMultipleEvtIndCodes(codes: any) {
      const oldCodes = [...this.totalEvtIndCodes];
      codes.forEach(code => {
        // 如果code不存在才添加
        if (!this.totalEvtIndCodes.includes(code)) {
          this.totalEvtIndCodes.push(code);
        }
      });

      // 比较新旧数组是否相同
      const isEqual = oldCodes.length === this.totalEvtIndCodes.length && 
        oldCodes.every((item, index) => item === this.totalEvtIndCodes[index]);
      if (!isEqual) {
        await this.fetchAllAttrList();
        return true; // 返回true表示数据已更新
      }
      return false; // 返回false表示数据未更新
    },
    // 移除codes列表
    // removeEvtIndCodes(code) {
    //   const oldCodes = [...this.totalEvtIndCodes];
    //   const index = this.totalEvtIndCodes.indexOf(code);
    //   if (index > -1) {
    //     this.totalEvtIndCodes.splice(index, 1);
    //     // 如果移除后数组发生变化，重新获取属性列表
    //     if (this.totalEvtIndCodes.length !== oldCodes.length) {
    //       this.fetchAllAttrList();
    //     }
    //   }
    // },
    // 获取所有属性分类列表
    async fetchAllAttrList() {
      try {
        const res = await getFilterCategoryList(          
          {
            event: this.totalEvtIndCodes,
            inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1,
            attributeType: ['event','user','cluster','tag','abtest']
          });
        if (res) {
            this.allAttrList = res
        }
      } catch (error) {
          console.error('获取所有属性分类列表失败:', error);
          throw error;
      }
    },
    // 更新所有属性列表中的收藏状态
    updateAllAttrFavoriteStatus(objectId: string, isFavorite: boolean) {
      this.allAttrList = this.allAttrList.map(category => {
        if (category.items?.length > 0) {
          return {
            ...category,
            items: category.items.map(item => {
              // 对于事件属性，需要处理嵌套结构
              if (category.category === '事件属性') {
                // 创建一个新对象，复制所有原始键值
                const newItem = { ...item };
                // 遍历所有事件代码键
                Object.keys(item).forEach(eventCode => {
                  if (Array.isArray(item[eventCode])) {
                    // 更新匹配的属性
                    newItem[eventCode] = item[eventCode].map((attr: any) =>
                      attr.code === objectId ? { ...attr, isFavorite } : attr
                    );
                  }
                });
                return newItem;
              }
              // 对于其他类型的属性，直接更新
              return item.code === objectId ? { ...item, isFavorite } : item;
            })
          };
        }
        return category;
      });
    },
    // 更新属性收藏状态
    updateAttrFavoriteStatus(objectId: string, isFavorite: boolean) {
      this.attrFlatList = this.attrFlatList.map(category => ({
          ...category,
          itemData: category.itemData.map(item => 
              item.code === objectId 
                  ? { ...item, isFavorite } 
                  : item
          )
      }));
    },
  },
});

export default toolStore;
