<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <a-table
        v-if="tableData?.length>0"
        :columns="columns"
        :data="tableData"
        style="white-space: nowrap"
        size="small"
        column-resizable
        :scroll="scroll"
        :pagination="tableData?.length>50"
        :filter-icon-align-left="true"
        :bordered="{cell:true}">
        
        <!-- 添加筛选器插槽 -->
        <template v-for="(_, idx) in props.reportAnalysisData?.groupsDesc" :key="idx" #[`group-filter-${idx}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions(idx)"
                :virtual-list-props="{height:200}"
                :style="{ width: '180px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true
                }"
                @update:model-value="(val) => setFilterValue(val)">
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        
        <!-- 动态处理所有列 -->
        <template v-for="(column, index) in columns" :key="index" #[column.slotName]="{ record, rowIndex }">
          <div class="cell-class" :style="column.rateIndex && record[column.rateIndex] ? `background: ${getBackgroundColor(record[column.rateIndex])}` : ''">
            <!-- 日期列 -->
            <div v-if="column.dataIndex === 'date'">
              <span>{{formatTimeColumn(record[column.dataIndex])}}</span>
            </div>
            
            <!-- 分组列 -->
            <div v-else-if="column.dataIndex.startsWith('group')">
              <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? record[column.dataIndex] : '(空)' }}</span>
            </div>
            
            <!-- 用户数列 -->
            <div v-else-if="column.dataIndex === 'userNum'">
              <div v-if="!secondaryIndicatorOnly">
                <a-tooltip
                  :content="`
                    ${formatTimeColumn(record.date)}
                    参与
                    ${indicatorData.eventDisplayName}
                    的用户总共有
                    ${record[column.dataIndex] || '-'}个
                    ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record.secondaryUserNum:''}`"
                  position="top">
                  <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
                </a-tooltip>
                <!-- 同时展示数据 -->
                <div v-if="hasSecondaryIndicator">
                  {{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}
                </div>
              </div>
              <div v-else-if="hasSecondaryIndicator">
                <a-tooltip
                  :content="`
                    ${formatTimeColumn(record.date)}
                    ${hasGroupData ? '该分组' : ''}参与
                    ${indicatorData.eventDisplayName}
                    的用户的
                    ${secondaryIndicatorData.eventDisplayName}.
                    ${secondaryIndicatorData.eventAttrName}为
                    ${record.secondaryUserNum}`"
                  position="top">
                  <span>{{ record.secondaryUserNum !== undefined && record.secondaryUserNum !== null ? formatNumber(record.secondaryUserNum) : '-' }}</span>
                </a-tooltip>
              </div>
            </div>
            
            <!-- 阶段数据列 -->
            <div v-else class="hover-box">
              <div v-if="!secondaryIndicatorOnly">
                <a-tooltip
                  :content="`
                    ${formatTimeColumn(record.date)}
                    参与
                    ${indicatorData.eventDisplayName}
                    的用户中，有
                    ${record[column.dataIndex] || '-'}个用户的
                    ${indicatorData.eventAttrName}等于${column.title}
                    ,占比${record[column.rateIndex]?.toFixed(2)}%
                    ${hasSecondaryIndicator ? ',且这些用户的'+secondaryIndicatorData.eventDisplayName+'.'+secondaryIndicatorData.eventAttrName+'为'+record[column.secondaryIndex]:''}`"
                  position="top">
                  <span>{{ record[column.dataIndex] !== undefined && record[column.dataIndex] !== null ? formatNumber(record[column.dataIndex]) : '-' }}</span>
                </a-tooltip>
                <!-- 百分比显示 -->
                <div>{{ record[column.rateIndex] !== undefined && record[column.rateIndex] !== null ? `${record[column.rateIndex]?.toFixed(2)}%` : '-' }}</div>
              </div>
              
              <div v-else-if="hasSecondaryIndicator">
                <a-tooltip
                  :content="`
                    ${formatTimeColumn(record.date)}
                    参与
                    ${indicatorData.eventDisplayName}
                    ，且
                    ${indicatorData.eventAttrName}等于${column.title}
                    的用户的
                    ${secondaryIndicatorData.eventDisplayName}.
                    ${secondaryIndicatorData.eventAttrName}为
                    ${record[column.secondaryIndex]}`"
                  position="top">
                  <span>{{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}</span>
                </a-tooltip>
              </div>
              
              <!-- 同时展示数（非只展示模式） -->
              <div v-if="hasSecondaryIndicator && !secondaryIndicatorOnly">
                {{ record[column.secondaryIndex] !== undefined && record[column.secondaryIndex] !== null ? formatNumber(record[column.secondaryIndex]) : '-' }}
              </div>
            </div>
          </div>
        </template>
      </a-table>
      <a-empty v-else-if="!loading" style="width: 100%;height: 100%"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref, watch} from "vue";
import useLoading from '@/hooks/loading';
import * as XLSX from 'xlsx';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {DateRange} from "@/api/analyse/type";
import {createCustomSorter, getCustomStringLength} from "@/utils/strUtil";
import { TimeParticleSize } from '@/api/enum';


interface Props {
  /**
   * 表格大小
   */
  size: number
  /**
   * 报表数据
   */
  reportAnalysisData: object;
  secondaryIndicatorOnly: boolean;
}

const props = defineProps<Props>()

const {loading, setLoading} = useLoading(true);
const columns = ref()
const tableData = ref<any>([])
const formatNumber = (num) => {
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
const getBackgroundColor = (value) => {
  if (props.secondaryIndicatorOnly) {
    return 'rgb(255, 255, 255)';
  }
  // 如果没有值，返回白色
  if (value === null || value === undefined || value === '') {
    return 'rgb(255, 255, 255)'; // 白色
  }
  // 确保值是数字
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return 'rgb(255, 255, 255)';
  }
  // 使用 rgba 格式设置背景颜色，透明度根据 rate 计算
  const alpha = Math.min(Math.max(value / 100, 0), 1); // 确保 alpha 在 0 到 1 之间
  return `rgba(11, 163, 255, ${alpha})`; // 使用 rgba 格式
}
// 获取筛选选项
const getFilterOptions = (columnIndex: number) => {
  if (!tableData.value?.length) return [];
  return Array.from(new Set(tableData.value.map(row => row[`group${columnIndex}`])))
    .filter(value => value !== undefined)
    .map(value => ({
      label: value === null ? '(空)' : value,
      value: value === null ? null : value
    }));
};

// 格式化时间列
const formatTimeColumn = (dateString: string) => {
  const {timeParticleSize} = props.reportAnalysisData
  if (timeParticleSize === TimeParticleSize.DAY1) {
    const date = new Date(dateString);
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `${dateString}(${days[date.getDay()]})`;
  }
  if (timeParticleSize === TimeParticleSize.WEEK1) {
    return `${dateString}当周`;
  }
  if (timeParticleSize === TimeParticleSize.MONTH1) {
    return `${dateString}月`;
  }
  return `${dateString}`;
}
const indicatorData = ref({})
const secondaryIndicatorData = ref({})

function renderChart(wsResultData: any) {
  if (!wsResultData) {
    return
  }
  if (!wsResultData?.scatterQueryResult?.length) {
    tableData.value = []
    columns.value = []
    setLoading(false)
    return
  }
  const {scatterQueryResult, stages, indicator, secondaryIndicator, groupsDesc} = wsResultData;
  indicatorData.value = indicator?.eventList?.[0]
  secondaryIndicatorData.value = secondaryIndicator?.eventList?.[0]
  
  // 生成列配置
  const baseColumns = [
    {
      title: '事件发生时间',
      dataIndex: 'date',
      slotName: 'date',
      fixed: 'left',
      width: 140,
      sortable: {sortDirections: ['ascend', 'descend']}
    }
  ];

  // 如果有分组项，添加分组列
  const groupColumns = [];
  if (groupsDesc && groupsDesc.length > 0) {
    groupsDesc.forEach((item, idx) => {
      groupColumns.push({
        title: item.name,
        dataIndex: `group${idx}`,
        slotName: `group${idx}`,
        width: getCustomStringLength(item.name) + 80,
        fixed: 'left',
        sortable: {
          sortDirections: ['ascend', 'descend'],
          sorter: (a, b, extra) => {
            const { direction } = extra;
            const format = item.format;
            const valueA = a[`group${idx}`];
            const valueB = b[`group${idx}`];
            return createCustomSorter(format)(valueA, valueB, direction);
          }
        },
        filterable: {
          filter: (value, row) => !value?.length || value.includes(row[`group${idx}`] ?? null),
          slotName: `group-filter-${idx}`,
          multiple: true
        }
      });
    });
  }

  const userColumns = [
    {
      title: '全部用户',
      dataIndex: 'userNum',
      slotName: 'userNum',
      fixed: 'left',
      width: 140,
      sortable: {sortDirections: ['ascend', 'descend']}
    }
  ];

  // 根据stages生成列
  const stageColumns = [];
  if (stages && stages.length > 0) {
    stages.forEach((stage, index) => {
      stageColumns.push({
        title: stage,
        dataIndex: `stage${index + 1}`,
        rateIndex: `rate${index + 1}`,
        secondaryIndex: `secondaryStage${index + 1}`,
        slotName: `stage${index + 1}`,
        minWidth: 140,
        sortable: {sortDirections: ['ascend', 'descend']}
      });
    });
  }

  columns.value = [...baseColumns, ...groupColumns, ...userColumns, ...stageColumns];

  // 处理表格数据
  const processedData = [];
  
  // 如果有scatterQueryResult数据，则处理数据
  if (scatterQueryResult && scatterQueryResult.length > 0) {
    const firstData = scatterQueryResult[0];
    const secondaryData = scatterQueryResult[1];
    
    // 如果有分组数据，展开显示每个分组
    if (groupsDesc && groupsDesc.length > 0) {
      // 处理每个日期的数据
      for (let i = 0; i < firstData.length; i++) {
        const firstItem = firstData[i];
        const secondaryItem = secondaryData?.[i];
        if (!firstItem) continue;

        // 处理分组数据
        if (firstItem.groupData && firstItem.groupData.length > 0) {
          firstItem.groupData.forEach(groupItem => {
            const rowData = {
              date: firstItem.eventDate,
              userNum: groupItem.valueSum,
              // 添加分组信息
              ...groupItem.group.reduce((acc, value, idx) => ({
                ...acc,
                [`group${idx}`]: value
              }), {}),
            };

            // 处理每个阶段的数据
            if (groupItem.values && groupItem.values.length > 0) {
              groupItem.values.forEach((value, index) => {
                const stageIndex = index + 1;
                const numValue = value || 0;
                // 计算百分比 = value / valueSum * 100
                const percent = groupItem.valueSum ? (numValue / groupItem.valueSum * 100) : 0;
                rowData[`stage${stageIndex}`] = numValue;
                rowData[`rate${stageIndex}`] = percent;
              });
            }

            // 查找对应的同时展示数据
            const secondaryGroup = secondaryItem?.groupData?.find(sg =>
              JSON.stringify(sg.group) === JSON.stringify(groupItem.group)
            );
            if (secondaryGroup) {
              rowData.secondaryUserNum = secondaryGroup.valueSum;
              // 添加 secondary 阶段数据
              secondaryGroup.values?.forEach((value, idx) => {
                rowData[`secondaryStage${idx+1}`] = value;
              });
            }

            processedData.push(rowData);
          });
        }
      }
    } else {
      // 没有分组数据时的处理逻辑（原来的逻辑）
      for (let i = 0; i < firstData.length; i++) {
        const firstItem = firstData[i];
        const secondaryItem = secondaryData?.[i];
        if (!firstItem) {
          return;
        }
        const rowData = {
          date: firstItem.eventDate,
          userNum: firstItem.summaryDataSum || 0,
          secondaryUserNum: secondaryItem?.summaryDataSum || 0,
          groupData: firstItem?.groupData || [],
          secondaryGroupData: secondaryItem?.groupData || []
        };
        // 处理每个阶段的数据
        if (firstItem.summaryData && firstItem.summaryData.length > 0) {
          firstItem.summaryData.forEach((value, index) => {
            const stageIndex = index + 1;
            const numValue = value || 0;
            // 计算百分比 = summaryData / summaryDataSum * 100
            const percent = firstItem.summaryDataSum ? (numValue / firstItem.summaryDataSum * 100) : 0;
            rowData[`stage${stageIndex}`] = numValue;
            rowData[`rate${stageIndex}`] = percent;
          });
        }
        // 处理同时展示指标的阶段数据
        if (secondaryItem?.summaryData && secondaryItem.summaryData.length > 0) {
          secondaryItem.summaryData.forEach((value, index) => {
            const stageIndex = index + 1;
            const numValue = value || 0;
            rowData[`secondaryStage${stageIndex}`] = numValue;
          });
        }
        processedData.push(rowData);
      }
    }
  }
  
  // 如果没有数据，使用默认数据
  if (processedData.length === 0) {
    processedData.push({
      date: '暂无数据',
      userNum: 0,
      secondaryUserNum: 0
    });
  }

  // 按分组项拼接+日期排序
  tableData.value = processedData.sort((a, b) => {
    // 拼接分组项
    const groupsDescList = wsResultData?.groupsDesc || [];
    const groupStrA = groupsDescList.map((_, i) => a[`group${i}`] || '').join(',');
    const groupStrB = groupsDescList.map((_, i) => b[`group${i}`] || '').join(',');
    // 先按分组项排序
    const groupCompare = groupStrA.localeCompare(groupStrB);
    if (groupCompare !== 0) return groupCompare;
    // 分组相同时，按日期排序
    return a.date.localeCompare(b.date);
  });
  
  setLoading(false)
}

// 是否有同时展示指标
const hasSecondaryIndicator = computed(() => {
  const {scatterQueryResult} = props.reportAnalysisData;
  return scatterQueryResult[1] !== undefined && scatterQueryResult[1].length;
});
watch(() => props.reportAnalysisData, async (newData, oldData) => {
  renderChart(newData);
})

const scroll = computed(() => {
  return {
    y: '100%'
  }
})
onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

const exportXlsx = (date: DateRange, name?: string) => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  const dateString = date ? `${getDateRangeStartDate(date)}-${getDateRangeEndDate(date)}` : ''
  XLSX.writeFile(newWorkbook, `分布分析${name ? `_${name}` : ''}${dateString ? `_${dateString}` : ''}.xlsx`);
};
defineExpose({
  exportXlsx
})
</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  padding-top: 8px;
  width: 100%;
  height: 100%;
}

.custom-filter {
  padding: 12px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: 4px;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  
  .custom-filter-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }
}

:deep(tbody .arco-table-cell) {
  padding: 0;
}

.cell-class {
  padding: 5px 16px;
  height: 54px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  div{
    line-height: 1.1;
  }
}

.hover-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &:hover {
    .add-group {
      opacity: 1;
    }
  }

  .add-group {
    cursor: pointer;
    opacity: 0;
    margin-left: 5px;

    &:hover {
      color: #8d0088;
    }
  }
}
</style>
  
