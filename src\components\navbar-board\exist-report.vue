<script setup lang="ts">
import {computed, CSSProperties, onMounted, ref, watch} from "vue";
import {GridItem, GridLayout, Layout} from "grid-layout-plus";
import {getAnalyseReportList} from "@/api/analyse/api";
import {detailDashboard, saveDashboardUI, saveNote} from "@/api/dashboard/api";
import {DashboardDto} from "@/api/dashboard/type";
import {useDashboardStore} from "@/store";
import {DashboardEventBus, RefreshEvent, UpdateReportCountEvent} from "@/types/event-bus";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {ReportAnalyseModel} from "@/api/analyse/type"
import note from "./note.vue"

const bodyCss: CSSProperties = {
  padding: 0,
  width: '100%',
  minWidth: '350px'
}

const dashboardEventBus = useEventBus<string>(DashboardEventBus)
const updateCountBus = useEventBus(UpdateReportCountEvent)
const appData = useSessionStorage('app-data', {});
const appFilter = ref<boolean>(true);

const visible = ref(false)
const layout = ref<Layout>([])
const dashboardStore = useDashboardStore()
const originListData = ref()
const items = ref([])
const originItems = ref([])
const isIconVisible = ref<Array<boolean>>(Array(0)); //
const drapdownVisible = ref<Array<boolean>>(Array(0));
const draggedItemIndex = ref(null)
const noteVisible = ref<boolean>(false)
const colNum = ref(20)

const selectOptions = [
  {label: '全部报表', value: 'all'},
  {label: '事件分析', value: ReportAnalyseModel.EVENT},
  {label: '留存分析', value: ReportAnalyseModel.RETENTION},
  {label: '漏斗分析', value: ReportAnalyseModel.FUNNEL},
  {label: '属性分析', value: ReportAnalyseModel.PROPERTY},
  {label: '路径分析', value: ReportAnalyseModel.TRACE},
  {label: '分布分析', value: ReportAnalyseModel.SCATTER},
  {label: 'SQL分析', value: ReportAnalyseModel.CUSTOM},
  {label: '间隔分析', value: ReportAnalyseModel.INTERVAL},
  {label: '归因分析', value: ReportAnalyseModel.ATTRIBUTION},
  {label: '运营分析', value: ReportAnalyseModel.APPLICATION},
  {label: '群组分析', value: ReportAnalyseModel.GROUP},
  // {label: '热力地图', value: ReportAnalyseModel.HEATMAP},
  // {label: '排行榜', value: ReportAnalyseModel.RANKLIST},
]
const selectOption = ref('all')
const isItemVisible = (item) => {
  return selectOption.value === item.model ||
      selectOption.value === '全部报表' ||
      selectOption.value === 'all';
};
// 响应式搜索查询
const searchQuery = ref('');
const userSearchQuery = ref('')
const selectedKeys = ref(['1'])
const filterItem = computed(() => {
  const codesToExclude = layout.value?.map(val => val.i) || [];
  return items.value?.filter(item => !appFilter.value || item.appId === appData.value?.code)?.filter(item => !codesToExclude.includes(item.code));
});

function isMatch(item: any, query: string, selectedOption: string) {
  const lowerCaseQuery = query.toLowerCase();

  // 将查询字符串转换为正则表达式模式，以便匹配不相连的字符
  const regexPattern = lowerCaseQuery.split('').join('.*?');
  const regex = new RegExp(regexPattern);

  // 判断名称或描述是否匹配查询
  const nameMatches = regex.test(item.name.toLowerCase());
  // const descriptionMatches = regex.test(item.description.toLowerCase());

  // 判断选项是否匹配
  const selectMatches = selectedOption === 'all' ||
      selectedOption === '全部报表' ||
      item.model === selectedOption;

  // 返回综合匹配结果
  return (nameMatches) && selectMatches;
}


const filteredItems = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  const selectedOption = selectOption.value;
  return filterItem.value.filter(item => isMatch(item, query, selectedOption));
});

const filteredUserItems = computed(() => {
  const query = userSearchQuery.value.trim().toLowerCase();
  return filterItem.value.filter(item => isMatch(item, query, 'all'));
});
isIconVisible.value = Array(filterItem.value.length).fill(false);
drapdownVisible.value = Array(layout.value?.length).fill(false);
const showIcon = (index: number, value: boolean) => {
  isIconVisible.value[index] = value;

};

// 改变大小
// const resize = (size: string, code: number) => {
//   let width: number
//   let height: number
//   switch (size) {
//     case 'small':
//       width = 5
//       height = 5
//       break
//     case 'middle':
//       width = 10
//       height = 10
//       break
//     case 'large':
//       width = 20
//       height = 10
//       break
//     default:
//       width = 5
//       height = 5
//   }

//   layout.value = layout.value?.map(item => {
//     if (item.i === code) {
//       let xValue = item.x;
//       if (xValue + width > 40) {
//         xValue = 40 - width
//       }
//       return {
//         ...item,
//         w: width,
//         h: height,
//         x: xValue
//       }
//     }
//     return item;
//   })
// };
// 改变大小
const resize = (size: string, code: number) => {
  let width: number
  let height: number
  switch (size) {
    case 'small':
      width = 5
      height = 5
      break
    case 'middle':
      width = 10
      height = 10
      break
    case 'large':
      width = 20
      height = 10
      break
    default:
      width = 5
      height = 5
  }

  // 找到当前项
  const currentItem = layout.value?.find(item => item.i === code);
  if (!currentItem) return;

  // 找出同一行的其他项
  const itemsInSameRow = layout.value?.filter(item =>
      item.i !== code &&
      item.y === currentItem.y
  );

  // 如果有同行项目且当前项要变大，则需要移动这些项目
  if (itemsInSameRow?.length > 0 && width > currentItem.w) {
    // 计算当前行下方的最大Y坐标
    const maxY = Math.max(...layout.value.map(item => item.y + item.h));

    // 检查当前项是否是同行中的第二个（或更后面的）
    const isSecondOrLater = itemsInSameRow.some(item => item.x < currentItem.x);

    // 更新布局
    layout.value = layout.value?.map(item => {
      if (item.i === code) {
        // 当前项调整大小
        return {
          ...item,
          w: width,
          h: height,
          // 如果是第二个或更后面的元素，且变成大图，则x坐标重置为0
          x: isSecondOrLater && width === 20 ? 0 : item.x
        };
      } else if (itemsInSameRow.some(rowItem => rowItem.i === item.i)) {
        // 同行的其他项移到下一行
        return {
          ...item,
          y: maxY
        };
      }
      return item;
    });
  } else {
    // 如果没有同行项目或不会造成覆盖，直接调整大小
    layout.value = layout.value?.map(item => {
      if (item.i === code) {
        let xValue = item.x;
        if (xValue + width > 40) {
          xValue = 40 - width;
        }
        return {
          ...item,
          w: width,
          h: height,
          x: xValue
        };
      }
      return item;
    });
  }
};

const findAvailablePosition = () => {
  // 获取所有已占用的区域
  const occupiedAreas = layout.value?.map(item => ({
    x1: item.x,
    y1: item.y,
    x2: item.x + item.w,
    y2: item.y + item.h
  })) || [];

  // 从左到右，从上到下查找可用位置
  for (let y = 0; y <= Math.max(100, (layout.value?.length || 0) * 10); y += 10) {
    for (let x = 0; x <= 10; x += 10) { // 只考虑左侧和右侧起点
      // 检查当前位置是否与任何已占用区域重叠
      const isOverlap = occupiedAreas.some(area =>
          x < area.x2 && x + 10 > area.x1 && y < area.y2 && y + 10 > area.y1
      );

      if (!isOverlap) {
        return {x, y};
      }
    }
  }

  // 如果没有找到空位，就放在最下面
  const maxY = Math.max(...(layout.value?.map(item => item.y + item.h) || [0]));
  return {
    x: 0,
    y: maxY
  };
};
// 拖拽开始
const onDragStart = (itemId) => {
  originListData.value = itemId;
  const position = findAvailablePosition();

  draggedItemIndex.value = {
    objectType: 1,
    objectCode: itemId.code,
    x: position.x,
    y: position.y,
    h: 10,
    w: 10,
    i: itemId.code,
    configParams: {},
    isDraggable: true,
    move: false,
  };
};
// 结束拖拽
const onDrop = () => {
  const index = filterItem.value?.map(item => item.code).indexOf(originListData.value.code);

  // 再次检查位置是否合适
  const newItem = draggedItemIndex.value;
  const overlappingItems = layout.value?.filter(item =>
      item.x < newItem.x + newItem.w &&
      item.x + item.w > newItem.x &&
      item.y < newItem.y + newItem.h &&
      item.y + item.h > newItem.y
  );

  // 如果有重叠，调整位置到最下方
  if (overlappingItems?.length > 0) {
    const maxY = Math.max(...layout.value.map(item => item.y + item.h));
    newItem.y = maxY;
  }

  layout.value?.push(newItem);
  // filterItem.value.splice(index, 1);
};
const addReport = (itemId: any) => {
  onDragStart(itemId)
  onDrop()
}
// 删除
const deleteCard = (itemId: any) => {
  const index = layout.value?.map(item => item.i).indexOf(itemId);
  layout.value?.splice(index, 1);
  // const deleteData = originItems.value.map(item => item.code).indexOf(itemId);
  // filterItem.value.splice(deleteData, 0, originItems.value[deleteData])
}

// 请求数据
const fetchDashboard = async (dashboardId?: string) => {
  await detailDashboard(dashboardStore.dashboardSelected?.dashboardId).then((response: DashboardDto) => {
    layout.value = response.uiConfig?.map(config => {
      return {
        ...config,
        i: config.objectCode
      }
    }) || []

  }).catch(e => {
    console.error(e)
  })
}
const layoutUpdated = (newLayout: Layout) => {
  // 新布局保存
  saveDashboardUI({
    dashboardId: dashboardStore.dashboardSelected?.dashboardId,
    uiConfig: newLayout.map(config => {
      return {
        ...config,
        code: config.i,
        type: config.objectType,
        configParams: config.configParams
      }
    })
  })
}

const handleOk = () => {
  layoutUpdated(layout.value)
  fetchDashboard()
  visible.value = false;
  // 更新当前选中看板的报表数量
  const countData = {
    dashboardId: dashboardStore.dashboardSelected.dashboardId,
    count: layout.value.length
  }
  updateCountBus.emit(UpdateReportCountEvent, countData)
  dashboardEventBus.emit(RefreshEvent)

};
const handleCancel = () => {
  visible.value = false;
}

const gotoReport = () => {
  window.open('/#/management', '_blank');
}

const handleData = (payload: any) => {
  const newNote = {
    content: payload,
    title: '',
    type: '#fff',
    dashboardId: dashboardStore.dashboardSelected?.dashboardId,
  }
  // 偶发界面重叠问题
  saveNote(newNote).then(res => {
    const newLayout = {
      objectType: 0,
      objectCode: res.id,
      x: 10,
      y: layout.value?.length * 10,
      h: 10, w: 10,
      i: res.id,
      configParams: {},
      isDraggable: true,
      move: false,
    };
    layout.value?.push(newLayout)
  }).catch(e => {
    console.error(e)
  })

  noteVisible.value = false
}
watch(() => dashboardStore.dashboardSelected, (newdashboardSelected) => {
      if (newdashboardSelected) {
        // fetchDashboard()
      }
    },
    {immediate: true, deep: true}
);
onMounted(() => {
  getAnalyseReportList().then(res => {
    items.value = res
    originItems.value = JSON.parse(JSON.stringify(res))
    localStorage.setItem('analyseReportList', JSON.stringify(res))
  })
})
const openModal = async () => {
  await fetchDashboard()
  visible.value = true;
}
defineExpose({
  fetchDashboard,
  openModal
})
</script>

<template>
  <a-modal :visible="visible" :body-style="bodyCss" :closable="false" width="auto">
    <template #footer>
      <div style="display: flex;justify-content: space-between;">
        <div style="cursor: pointer;">
          <span
              style="
            color: var(--tant-primary-color-primary-default);
            font-weight: 500;
            line-height: 35px;
            padding-left: 12px;"
              @click="gotoReport"
          >
             <icon-share-internal/> 报表管理</span>
        </div>
        <div>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" style="margin-left: 12px;" @click="handleOk">确定</a-button>
        </div>
      </div>
    </template>
    <div class="tant-next-commonHead">
      <div class="tant-next-commonHead-titleWrapper">
        <div class="tant-next-commonHead-title">已存报表</div>
      </div>
      <div class="tant-next-commonHead-closeIcon">
        <a-button class="tant-next-button-text" @click="handleCancel"><span role="img"><icon-close/></span></a-button>
      </div>
    </div>
    <div class="tant-next-dialog-content">
      <div class="root___pBPsT">
        <div class="left">
          <div class="head">
            <div class="title">调整布局</div>
            <div class="count">报表数{{ layout?.length }}/30</div>
          </div>
          <div
              v-if="layout?.length>0"
              class="root"
              @drop="onDrop"
              @dragover.prevent>
            <grid-layout
                v-model:layout="layout"
                :col-num="colNum"
                :row-height="2"
                :margin="[5,5]"
                :is-draggable="true"
                :is-resizable="false"
                :vertical-compact="true"
                :use-css-transforms="true"
            >
              <grid-item
                  v-for="item in layout"
                  :key="item.i"
                  :class="drapdownVisible[item.i]?'girdItemHover':'girdItem'"
                  :x="item.x"
                  :y="item.y"
                  :w="item.w"
                  :h="item.h"
                  :i="item.i"
              >
                <div class="left-content">
                  <span class="name">{{ originItems.find(value => value.code === item.i)?.name || '便签' }}</span>
                </div>
                <a-dropdown v-model:popup-visible="drapdownVisible[item.i]">
                  <div :class="drapdownVisible[item.i]?'action-hover':'action'">
                    <span role="img" style="color:var(--tant-text-white-color-text2-1) ">
                      <icon-up v-if="drapdownVisible[item.i]"/>
                      <icon-down v-else/>
                    </span>
                  </div>
                  <template #content>
                    <a-doption v-if="item.w!==5" class="normal-doption" @click="resize('small',item.i)">小图
                    </a-doption>
                    <a-doption v-if="item.w!==10" class="normal-doption" @click="resize('middle',item.i)">中图
                    </a-doption>
                    <a-doption v-if="item.w!==20" class="normal-doption" @click="resize('large',item.i)">大图
                    </a-doption>
                    <a-doption class="delete-doption" @click="deleteCard(item.i)">移除</a-doption>
                  </template>
                </a-dropdown>

              </grid-item>
            </grid-layout>
          </div>

          <div class="foot">
            <a-popover
                v-model:popup-visible="noteVisible"
                update-at-scroll
                position="tl" trigger="click">
              <a-button class="button" style="margin-top: 16px;margin-left: 24px;">
                <span role="img"><icon-drive-file :rotate="180"/></span>
                <span>添加便签</span>
              </a-button>
              <template #content>
                <note @data-sent="handleData"/>
              </template>
            </a-popover>
          </div>
        </div>
        <div class="right">
          <div class="head">
            <span class="title">添加图表</span>
          </div>
          <div class="body">
            <div class="menu">
              <a-menu v-model:selected-keys="selectedKeys" mode="horizontal" :default-selected-keys="['1']">
                <div style="display: flex;justify-content:center;gap: 100px;">
                  <a-menu-item key="1">分析</a-menu-item>
                  <a-menu-item key="2">用户</a-menu-item>
                </div>
              </a-menu>
            </div>
            <div v-if="selectedKeys[0]==='1'" class="choose-list">
              <div class="drawer-input" style="display: flex;padding: 4px;">
                <a-input
                    v-model="searchQuery" class="drawer-input"
                    placeholder="请搜索">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
                <a-tooltip :content="`仅展示应用 ${appData?.code}-${appData?.name} 的报表`">
                  <a-checkbox v-model:model-value="appFilter"/>
                </a-tooltip>
                <a-select
                    v-model:model-value="selectOption"
                    style="width:108px;"
                    :bordered="false"
                    :options=selectOptions
                >
                </a-select>
              </div>
              <div class="List">
                <div class="v-list">
                  <div
                      v-for="(item, index) in filteredItems" :key="index" class="itemList"
                      @mouseover="showIcon(index, true)" @mouseleave="showIcon(index, false)">
                    <div
                        v-if="isItemVisible(item) "
                        class="lists"
                        :style="layout?.length<30?'':{cursor:'not-allowed'}"
                        :draggable="layout?.length<30?'true':'false'"
                        @dragstart="onDragStart(item)">
                      <div class='list-name'>
                        <span class="icon" role="img"><icon-drag-dot-vertical/></span>
                        <span class="model">
                         <span class="img">
                          <img :src="`/icon/topMenu/${item.model}.svg`" style="width: 14px; height: 14px;" alt=""/>
                         </span>
                          <span class="model-title">
                            <span v-for="(val) in selectOptions" :key="val">
                              <span v-if="item.model===val.value">{{ val.label }}</span>
                            </span>
                          </span>
                        </span>
                        <span class="name">{{ item.name }}{{ item?.appId ? ` (${item?.appId})` : '' }}</span>
                      </div>
                      <div class="action">
                        <a-tooltip content="点击或拖拽至左侧添加" position="top">
                          <a-button class="button" @click="addReport(item)">
                            <icon-plus/>
                          </a-button>
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="choose-list">
              <div class="drawer-input" style="padding: 4px;">
                <a-input
                    v-model="userSearchQuery" class="drawer-input"
                    placeholder="请搜索">
                  <template #prefix>
                    <icon-search/>
                  </template>
                </a-input>
              </div>
              <div class="List">
                <div class="v-list">
                  <div
                      v-for="(item, index) in filteredUserItems" :key="index" class="itemList"
                      @mouseover="showIcon(index, true)" @mouseleave="showIcon(index, false)">
                    <div
                        class="lists"
                        :style="layout?.length<30?'':{cursor:'not-allowed'}"
                        :draggable="layout?.length<30?'true':'false'"
                        @dragstart="onDragStart(item)">
                      <div class='list-name'>
                        <span class="icon" role="img"><icon-drag-dot-vertical/></span>
                        <span class="model">
                         <span class="img">
                          <img src="/icon/topMenu/userLabel.svg" style="width: 14px; height: 14px;" alt=""/>
                         </span>
                          <span class="model-title">
                              <span>用户标签</span>
                          </span>
                        </span>
                        <span class="name">
                      {{ item.name }}
                      </span>
                      </div>
                      <div class="action">
                        <a-tooltip content="点击或拖拽至左侧添加" position="top">
                          <a-button class="button" @click="addReport(item)">
                            <icon-plus/>
                          </a-button>
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </a-modal>
</template>

<style scoped lang="less">
.tant-next-commonHead {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 16px 32px;

  .tant-next-commonHead-titleWrapper {
    flex: 1 1;
    width: 0;

    .tant-next-commonHead-title {
      width: 100%;
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 16px;
    }
  }

  .tant-next-commonHead-closeIcon {
    display: flex;
    align-items: center;
    height: 24px;
    color: var(--tant-text-gray-color-text1-2);

    .tant-next-button-text {
      color: var(--tant-text-gray-color-text1-2);
      background-color: transparent;
      border: none;
      height: 24px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      padding: 0 5px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-transp-hover);
      }

    }

  }
}

.tant-next-dialog-content {
  padding: 8px 32px 32px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;

  .root___pBPsT {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 712px;
    height: calc(80vh - 154px);
    min-height: 500px;
    overflow: hidden;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 4px;

    .left {
      display: flex;
      flex-direction: column;
      width: 352px;
      height: 100%;
      border-right: 1px solid var(--tant-border-color-border1-1);

      .head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 46px;
        padding: 12px 24px;

        .title {
          color: var(--tant-text-gray-color-text1-2);
          font: var(--tant-body-font-body-regular);
        }

        .count {
          color: var(--tant-text-gray-color-text1-3);
          font: var(--tant-description-font-description-regular);
        }

      }

      .root {
        height: calc(100% - 118px);
        border: 1px solid var(--tant-bg-white-color-bg1-1);
        -webkit-user-select: none;
        -ms-user-select: none;
        user-select: none;
        overflow-y: auto;

        .girdItemHover {
          display: flex;
          width: 100%;
          //align-items: center;
          //justify-content: center;
          background-color: var(--tant-primary-color-primary-fill);
          border: 1px solid var(--tant-primary-color-primary-fill);
          border-radius: 2px;
          border-color: var(--tant-primary-color-primary-default);

        }

        .girdItem {
          display: flex;
          width: 100%;
          //align-items: center;
          //justify-content: center;
          background-color: var(--tant-primary-color-primary-fill);
          border: 1px solid var(--tant-primary-color-primary-fill);
          border-radius: 2px;

          &:hover {
            border-color: var(--tant-primary-color-primary-default);
          }

          &:hover .action {
            opacity: 1;
          }
        }

        .left-content {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          -webkit-user-select: none;
          -ms-user-select: none;
          user-select: none;

          .name {
            display: inline-block;
            width: 100%;
            padding-left: 8px;
            overflow: hidden;
            color: var(--tant-text-gray-color-text1-2);
            font: var(--tant-description-font-description-regular);
            white-space: nowrap;
            text-overflow: ellipsis;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
          }
        }

        .action {
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 29px;
          height: 100%;
          background-color: var(--tant-primary-color-primary-default);
          cursor: pointer;
          opacity: 0;

          &:hover {
            background-color: var(--tant-primary-color-primary-hover);
          }
        }

        .action-hover {
          background-color: var(--tant-primary-color-primary-hover);
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 29px;
          height: 100%;
          cursor: pointer;
          opacity: 1;
        }

      }

      .foot {
        height: 72px;

        .button {
          color: var(--tant-text-gray-color-text1-2);
          text-shadow: none;
          background-color: var(--tant-bg-white-color-bg1-1);
          border: 1px solid var(--tant-border-color-border1-1);
          border-radius: var(--tant-border-radius-medium);
          box-shadow: none;

          &:hover {
            background-color: var(--tant-secondary-color-secondary-fill-hover);
          }
        }
      }
    }

    .right {
      width: calc(100% - 352px);
      height: 100%;

      .head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 46px;
        padding: 12px 24px;

        .title {
          color: var(--tant-text-gray-color-text1-2);
          font: var(--tant-body-font-body-regular);
        }
      }

      .body {
        height: calc(100% - 46px);
        overflow: hidden;

        .menu {
          border-bottom: 1px solid var(--tant-border-color-border1-1);
        }

        .choose-list {
          height: calc(100% - 46px);
          display: flex;
          flex-direction: column;
          background-color: var(--tant-fill-color-fill1-1);

          .List {
            width: 100%;
            height: 100%;

            .v-list {
              margin: 16px 0 16px 24px;
              overflow: hidden !important;
              height: calc(100% - 94px);

              &:hover {
                overflow-y: auto !important;
              }

              .itemList {
                width: 100%;
                margin-bottom: 10px;


                .lists {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: calc(100% - 18px);
                  height: 42px;
                  line-height: 40px;
                  background-color: var(--tant-bg-white-color-bg1-1);
                  border-radius: 2px;
                  cursor: grab;
                  transition: all .3s;

                  &:hover {
                    background-color: var(--tant-bg-white-color-bg1-1);
                  }

                  &:hover .list-name > .icon {
                    opacity: 1;
                  }

                  &:hover .action {
                    opacity: 1;
                  }

                  .list-name {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: flex-end;

                    .icon {
                      padding-left: 3px;
                      color: var(--tant-text-gray-color-text1-3);
                      font-size: 16px;
                      transition: all .3s;
                      opacity: 0;

                    }

                    .model {
                      display: flex;
                      align-items: center;
                      justify-content: flex-start;
                      width: 74px;
                      margin-right: 12px;
                      color: var(--tant-text-gray-color-text1-3);
                      font: var(--tant-description-font-description-regular);

                      .img {
                        flex: 0 1 auto;
                        margin-right: 8px;
                        font-size: 14px;
                      }

                      .model-title {
                        flex: 1 1 auto;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      }
                    }

                    .name {
                      display: inline-block;
                      flex: 1 1 162px;
                      min-width: 162px;
                      max-width: 162px;
                      overflow: hidden;
                      color: var(--tant-text-gray-color-text1-2);
                      font: var(--tant-description-font-description-regular);
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    }
                  }

                  .action {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 12px;
                    opacity: 0;
                    transition: all .3s;

                    .button {
                      color: var(--tant-text-gray-color-text1-2);
                      background-color: transparent;
                      border: none;
                      padding: 4px;
                      height: 24px;
                      font: var(--tant-body-font-body-regular);
                      border-radius: var(--tant-border-radius-medium);

                      &:hover {
                        background-color: var(--tant-secondary-color-secondary-transp-hover);
                      }
                    }
                  }

                }
              }

            }

          }
        }
      }


    }
  }
}

.normal-doption {
  width: auto;
  margin: 0 3px;
}

.delete-doption {
  width: auto;
  margin: 0 3px;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill);
  }
}


</style>