/**
 * 系统发布版本实体
 */
export interface SystemReleaseVersionDto {

  /**
   * 发布平台
   */
  platform: string;

  /**
   * 版本号
   */
  versionCode: string;

  /**
   * 版本名称
   */
  versionName: string;

  /**
   * 更新内容和发布说明
   */
  releaseNotes: string;

  /**
   * 版本类型：major、minor、patch、hotfix
   */
  releaseType: string;

  /**
   * 版本状态：draft（草稿）、published（已发布）、deprecated（已下线）
   */
  status: string;

  /**
   * 安装包或版本快照文件路径
   */
  snapshotFilePath: string;

  /**
   * 发布日期
   */
  releaseDate: string;

  /**
   * 版本创建人或发布人
   */
  creator: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新人
   */
  updateUser: string;

  /**
   * 更新时间
   */
  updateTime: string;
}