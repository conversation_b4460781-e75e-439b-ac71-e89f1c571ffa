<template>
  <div class="report-card">
    <div class="report-card-header">
      <div class="report-card-title">
        {{ props.dataName }}
      </div>
      <div class="report-card-toolbox">
        <a-tooltip content="取消全屏">
          <div
            class="operation-icon"
            @click="
              () => {
                emits('exitFullScreen');
              }
            "
          >
            <icon-fullscreen-exit class="icon" />
          </div>
        </a-tooltip>
      </div>
    </div>
    <div class="report-card-chart">
      <a-spin :loading="loading" style="width: 100%; height: 100%">
        <AppRankingLargeFull :data-name="dataName" :chart-data="resultData" />
      </a-spin>
    </div>
    <div class="report-card-table">
      <a-table :loading="loading" :columns="columns" :data="tableData" :scroll="{ y: '100%' }" :filter-icon-align-left="true" size="small">
        <template v-for="(_, index) in resultData?.groupsDesc" :key="index" #[`group-filter-${index}`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptionsByIndex(index)"
                :style="{ width: '200px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true,
                }"
                @update:model-value="(val) => setFilterValue(val)"
              >
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template #total="{ record }">
          {{ record.total?.toLocaleString('en-US') }} (
          <span v-if="record.totalDiff === 0"> -- ( -- %) </span>
          <span v-if="record.totalDiff && record.totalDiff >= 0" class="up"> ↑ +{{ record.totalDiff?.toLocaleString('en-US') }} (+{{ record.totalDiffRate }}%) </span>
          <span v-if="record.totalDiff && record.totalDiff < 0" class="down"> ↓ {{ record.totalDiff?.toLocaleString('en-US') }} ({{ record.totalDiffRate }}%) </span>)
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { createCustomSorter, getCustomStringLength } from '@/utils/strUtil';
  import { getOverviewTopicDetail } from '@/api/overview/api';
  import AppRankingLargeFull from '../chart/AppRankingLargeFull.vue';

  interface Props {
    /**
     * 日期
     */
    date: any;
    /**
     * 详细数据名
     */
    dataName: string;

    /**
     * 参数
     */
    queryParams: any;
    indicator: string;

    /**
     * 刷新触发器
     */
    refreshTrigger?: number;
  }

  const props = defineProps<Props>();
  const emits = defineEmits(['exitFullScreen']);

  const loading = ref(true);
  const tableData = ref<any[]>();
  const columns = ref<any[]>([]);
  const resultData = ref<any>();

  const refreshData = () => {
    loading.value = true;
    const params = {
      topic: 'app_rank',
      startDate: props.date[0],
      endDate: props.date[1],
      country: props.queryParams.countrySelected,
      appIds: props.queryParams.appIds,
      team: props.queryParams.teamSelected,
      timeParticleSize: props.queryParams.timeParticleSize,
      group: props.queryParams.dataGroup,
      indicator: props.indicator,
    };
    getOverviewTopicDetail(params)
      .then((res) => {
        resultData.value = res;
        // 动态生成列配置
        const baseColumns = [
          {
            title: '日期',
            dataIndex: 'date',
            sortable: { sortDirections: ['ascend', 'descend'] },
          },
        ];

        // 根据groupsDesc添加分组列（用于筛选）
        const groupColumns =
          res.groupsDesc?.map((group, index) => ({
            title: group.name,
            dataIndex: `group${index}`,
            align: 'left',
            sortable: {
              sortDirections: ['ascend', 'descend'],
              sorter: (a, b, extra) => {
                const { direction } = extra;
                const format = group?.format;
                const valueA = a[`group${index}`];
                const valueB = b[`group${index}`];
                return createCustomSorter(format)(valueA, valueB, direction);
              },
            },
            filterable: {
              filter: (value, row) => {
                if (!value || value.length === 0) return true;
                return value.includes(row.groupList[index]);
              },
              slotName: `group-filter-${index}`,
              multiple: true,
            },
          })) || [];

        // 添加总数据列（使用total相关数据）
        const totalColumn = {
          title: `${props.dataName}`,
          dataIndex: 'total',
          slotName: 'total',
          align: 'left',
          sortable: { sortDirections: ['ascend', 'descend'] },
        };

        columns.value = [...baseColumns, ...groupColumns, totalColumn];

        // 处理表格数据
        const processedData = [] as any[];
        const dates = res.x || [];

        dates.forEach((date, dateIndex) => {
          res.y?.forEach((groupData) => {
            const rowData = {
              date,
              groupList: groupData.group.map((value) => (value === null ? 'null' : value)),
            } as any;

            // 添加分组信息（用于筛选）
            groupData.group?.forEach((groupValue, groupIndex) => {
              rowData[`group${groupIndex}`] = groupValue;
            });

            // 添加总数据（使用total相关数据）
            rowData.total = groupData.total?.[dateIndex];
            rowData.totalDiff = groupData.totalDiff?.[dateIndex];
            rowData.totalDiffRate = groupData.totalDiffRate?.[dateIndex];

            processedData.push(rowData);
          });
        });

        tableData.value = processedData.reverse();
      })
      .catch(() => {
        console.log('主题查询错误！');
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const getFilterOptionsByIndex = (index: number) => {
    if (!tableData.value?.length) return [];
    return Array.from(new Set(tableData.value.map((row) => row.groupList[index])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value: value,
      }));
  };

  onMounted(() => {
    refreshData();
  });

  // 监听刷新触发器
  watch(() => props.refreshTrigger, () => {
    if (props.refreshTrigger && props.refreshTrigger > 0) {
      refreshData();
    }
  });
</script>

<style scoped lang="less">
  .report-card {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .report-card-header {
      height: 50px;
      display: flex;
      justify-content: space-between;
      padding: 18px 24px 12px;
      align-items: center;

      .report-card-title {
        font-weight: bold;
        font-size: 14px;
      }

      .report-card-toolbox {
        padding: 0 4px;
        cursor: pointer;

        .operation-icon {
        }

        border-radius: 4px;
      }

      .report-card-toolbox:hover {
        background-color: var(--tant-bg-gray-color-bg2-1);
      }

      .report-card-indicator {
        display: flex;
        align-items: center;

        .report-card-indicator-item {
          display: flex;
          align-items: end;

          .report-card-indicator-title {
            font-size: 12px;
          }

          .report-card-indicator-data {
            font-size: 16px;
          }
        }
      }
    }

    .report-card-chart {
      height: calc(50% - 25px);
      margin: 0 24px;
      padding-bottom: 20px;
    }

    .report-card-table {
      height: calc(50% - 25px);
      margin: 0 24px;
      padding-bottom: 10px;
    }
  }

  .up {
    color: green;
  }

  .down {
    color: red;
  }

  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
</style>
