<template>
    <a-modal v-model:visible="modalVisible" :width="700" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules" label-align="left" :auto-label-width="true" style="width: 100%;">
            <a-form-item field="name" label="名称">
                <a-input v-model="form.name" placeholder="请输入团队名称"/>
            </a-form-item>
            <a-form-item field="appIds" label="应用">
                <a-select
                v-model:model-value="form.appIds"
                :max-tag-count="3"
                multiple
                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                :filter-option="false"
                 :show-extra-options="false"
                 placeholder="搜索添加应用"
                 @search="handleSearch"
                >
                    <a-option v-for="item in appIdsList" :key="item.code" :value="item.code">
                        {{ item.code }}-{{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="relatedTeam" label="关联组">
                <a-select
                    v-model:model-value="form.relatedTeam"
                    :max-tag-count="3"
                    multiple
                    allow-search
                    >
                    <a-option v-for="item in groupsList" :key="item.code" :value="item.code" :disabled="item.code === form.code">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="profitTarget" label="年度总利润目标">
                <a-input-number v-model="form.profitTarget" :min="0"/>
            </a-form-item>
            <a-form-item field="incomeTarget" label="年度分成后收入目标">
                <a-input-number v-model="form.incomeTarget" :min="0"/>
            </a-form-item>
            <a-form-item field="indProfit" label="年度累计利润">
                <a-spin :loading="currentLoading" style="flex: 1;">
                    <a-input-number v-model="form.indProfit" :min="0" disabled/>
                </a-spin>
                <a-select v-model="form.currentProfitYear" style="width: 100px;margin-left: 8px;" @change="yearChange">
                    <a-option v-for="year in recentYears" :key="year" :value="year.toString()">{{ year }}</a-option>
                </a-select>
            </a-form-item>
            <a-form-item field="indIncome" label="年度累计分成后收入">
                <a-spin :loading="currentLoading" style="flex: 1;">
                    <a-input-number v-model="form.indIncome" :min="0" disabled/>
                </a-spin>
                <div style="width: 100px;margin-left: 8px;"></div>
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';
import {useSessionStorage} from "@vueuse/core";
import {getTeamYearProfit} from "@/api/analyse/api";
import {addOpTeamList, getAppPageList, getTeamDetail} from "@/api/marketing/api";

const props = defineProps({
    groupsList: {
        type: Array,
        default: () => ([])
    },
})
const appId = useSessionStorage('app-id', '')?.value
const modalVisible = ref(false)
const modalTitle = ref('')
const form = reactive({
    code:'',
    name: '',
    profitTarget:undefined,
    incomeTarget: undefined,
    indIncome: undefined,
    indProfit: undefined,
    appIds:[],
    relatedTeam:[],
    currentProfitYear:''
})
const pageParams = reactive({
    current: 1,
    pageSize: 10,
    text: '',
    teamStatus:'no'
})

const rules = {
    name: [
        {
            required: true,
            message:'请填写团队名称',
        }
    ],
    // appIds: [
    //     {
    //         required: true,
    //         message:'请选择应用',
    //     }
    // ]
}
const formRef = ref()
const appPageList = ref<any>([])
const appSelectList = ref<any>([])
const emits = defineEmits(['updateData']);

const recentYears = computed(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({length: 5}, (_, i) => currentYear - i);
});
const appIdsList = computed(() => {
    const combinedList = appSelectList.value.concat(appPageList.value);
    const uniqueList = Array.from(new Map(combinedList.map(item => [item.code, item])).values()) as any;
    return uniqueList;
})
const appLoading = ref(false)

const currentLoading = ref(false)
const getCurrentProfit = async () => {
    currentLoading.value = true;
    try {
        const params = {
            teamCode: form.code,
            appId: appId,
            year: form.currentProfitYear
        };
        const res = await getTeamYearProfit(params);
        const {indProfit,indIncome} = res
        form.indProfit = indProfit;
        form.indIncome = indIncome;
    } catch (error) {
        console.error('获取年度累计利润失败:', error);
    } finally {
        currentLoading.value = false;
    }
};
const getPageList = async () => {
    appLoading.value = true
    appPageList.value = []
    await getAppPageList(pageParams).then(res => {
        appPageList.value = res.items || []
    })
    appLoading.value = false
}
const openModal = async (obj?:any) => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    pageParams.text = ''
    pageParams.current = 1
    pageParams.pageSize = 10
    appPageList.value = []
    appSelectList.value = []
    form.currentProfitYear = new Date().getFullYear().toString()
    if(obj){
        const {code,name} = obj
        form.code = code
        form.name = name
        modalTitle.value = '编辑团队'
        await getTeamDetail(code).then(res => {
            appSelectList.value = res.appIds || []
            form.appIds = res.appIds.map(item => item.code)
            form.relatedTeam = res.relatedTeam || []
            form.profitTarget = res.profitTarget
            form.incomeTarget = res.incomeTarget
        })
        getCurrentProfit()
    }else{
        form.code =''
        modalTitle.value = '新建团队'
    }
    modalVisible.value = true
}
const handleSearch = async (v) => {
  pageParams.text = v
  if(pageParams.text){
    await getPageList()
  }else{
    appPageList.value = []
  }
}

const closeModal = () => {
    modalVisible.value = false
}
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            try {
                await addOpTeamList(form);
                if(form.code){
                    Message.success('保存成功')
                }else{
                    Message.success('创建成功');
                }
                modalVisible.value = false;
                emits('updateData');
            } catch (error) {
                console.error('创建失败:', error);
            }
        }
    })
}
const yearChange = (v) => {
    form.currentProfitYear = v
    if(form.code){
        getCurrentProfit()
    }
}
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>