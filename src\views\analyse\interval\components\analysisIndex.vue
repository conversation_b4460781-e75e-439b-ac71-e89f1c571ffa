

<template>
    <div class="guide">
        <div class="stickyBar">
            <div class="modal" style="width: 100%">
                <img src="/icon/analysis/performAnalysis.svg" alt="">
                <span class="title">对</span>
                <analysisSubjectSelect :style="{width:'72px',margin:'0 8px'}" @subject-change="subjectChange"/>
                <span class="title">进行分析</span>
            </div>
        </div>
        <div class="event-filter-box">
            <div class="subTitle">起点事件</div>
            <handleFilterList/>
            <div class="subTitle">终点事件</div>
            <handleFilterList/>
            <div class="subTitle">使用关联属性</div>
            <div style="padding: 6px 4px 0 24px;margin-bottom: 16px;">
                <a-switch v-model:model-value="relevance"/>
            </div>
            <div v-if="relevance" class="relevance-box">
                <div>
                    和起点事件的
                    <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
                        <tabsSelect :info="beginInfo" :only-event="true"/>
                    </div>
                    相比<br>
                </div>
                <div>
                    终点事件的
                    <div style="display: inline-block;line-height: 26px;padding: 0 2px;">
                        <tabsSelect :info="endInfo" :only-event="true"/>
                    </div>
                    的值相等
                </div>
                
            </div>
            <div class="subTitle" style="margin-top: 16px;">间隔上限</div>
            <div class="action-row" style="padding: 8px 24px;">
                起点事件到终点事件的间隔不超过
                <a-dropdown @select="windowSelect">
                  <div class="filter-btn">
                    {{ windowName }}
                  </div>
                  <template #content>
                    <a-dsubmenu>
                      <template #default>天（即24小时）</template>
                      <template #content>
                        <a-doption value="1d">1天</a-doption>
                        <a-doption value="7d">7天</a-doption>
                        <a-doption value="14d">14天</a-doption>
                        <a-doption :value="`${dValue}d`" style="width: 150px;">
                            <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                <a-input-number v-model:model-value="dValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0" />
                                <div>天</div>
                            </div>
                        </a-doption>
                      </template>
                    </a-dsubmenu>
                    <a-dsubmenu>
                      <template #default>小时</template>
                      <template #content>
                        <a-doption value="1h">1小时</a-doption>
                        <a-doption value="3h">3小时</a-doption>
                        <a-doption value="12h">12小时</a-doption>
                        <a-doption :value="`${hValue}d`" style="width: 150px;">
                            <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                <a-input-number v-model:model-value="hValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0" />
                                <div>小时</div>
                            </div>
                        </a-doption>
                      </template>
                    </a-dsubmenu>
                    <a-dsubmenu>
                      <template #default>分钟</template>
                      <template #content>
                        <a-doption value="1m">1分钟</a-doption>
                        <a-doption value="5m">5分钟</a-doption>
                        <a-doption value="60m">30分钟</a-doption>
                        <a-doption :value="`${mValue}m`" style="width: 150px;">
                            <div style="display: flex;justify-content: space-between;align-items: center;width: 120px;">
                                <a-input-number v-model:model-value="mValue" :style="{width:'60px',height:'24px'}" :default-value="30" :precision="0" :min="0" />
                                <div>分钟</div>
                            </div>
                        </a-doption>
                      </template>
                    </a-dsubmenu>
                  </template>
                </a-dropdown>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {useEventBus} from '@vueuse/core';
import tabsSelect from "@/views/analyse/components/tabsSelect.vue"
import handleFilterList from "@/views/analyse/components/HandleFilterList.vue";
import analysisSubjectSelect from "@/views/analyse/components/analysisSubjectSelect.vue"


const eventBus = useEventBus('eventList');
const subject = ref('')

const beginData = ref<any>([])

const endData = ref<any>([])

const relevance = ref(false)

const subjectChange = (v) => {
  subject.value = v.subject
}
const beginInfo = reactive({
    name:'app版本',
    objectType:'string'
})
const endInfo = reactive({
    name:'app版本',
    objectType:'string'
})
const dValue = ref(30)
const hValue = ref(24)
const mValue = ref(60)
const windowName = ref('1小时')
// 间隔上限选项
const windowSelect = (v) => {
    const numbers = v.match(/\d+/g)?.join(',');
    if(v.includes('d')){
        windowName.value = `${numbers}天`
    }else if(v.includes('h')){
        windowName.value = `${numbers}小时`
    }else if(v.includes('m')){
        windowName.value = `${numbers}分钟`
    }
}
</script>

<style scoped lang="less">
.guide{
    width: 100%;
    margin-bottom: 16px;
    box-sizing: border-box;
    color: var(--tant-text-gray-color-text1-2);
    .stickyBar{
        position: sticky;
        top: 0;
        z-index: 999;
        width: 100% !important;
        margin: 0 !important;
        padding: 12px;
        background-color: var(--tant-bg-white-color-bg1-1);
        .modal{
            width: 100%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            // color: var(--tant-text-gray-color-text1-4);
            font-size: 16px;
            line-height: 18px;
            vertical-align: top;
            background-color: var(--tant-text-white-color-text2-1);
            border-radius: 4px;
            &:hover{
                background-color: var(--tant-secondary-color-secondary-fill-hover);
            }
            img{
                width: 16px;
                height: 16px;
                margin-right: 8px;
            }
            .title{
                font-weight: 600;
            }
            .model-btn{
                margin-left: auto;
                button{
                    width: 24px;
                    height: 24px;
                    background-color: transparent;
                }
            }
        }
    }
    .event-filter-box{
        
        box-sizing: border-box;
        min-height: 32px;
        font-size: 14px;
        transition: all .3s;
        .subTitle{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            font: var(--tant-description-font-description-medium);
            line-height: 20px;
        }
        .relevance-box{
            padding-left: 24px;
            color: var(--tant-text-gray-color-text1-3);
            line-height: 38px;
            &:hover{
                background-color: var(--tant-fill-color-fill1-2);
                :deep(.filter-btn){
                    background: #fff;
                }
            }
        }
        .action-row{
            position: relative;
            height: auto;
            width: 100%;
            min-height: 24px;
            line-height: 24px;
            padding-right: 24px;
            padding-left: 24px;
            .action-left{
                align-items: flex-start;
                height: auto;
                display: flex;
                .row-content{
                    flex-grow: 1;
                    box-sizing: border-box;
                    padding: 4px 0;
                    .rename{
                        min-width: 80px;
                        max-width: calc(100% - 50px);
                        height: 24px;
                        padding: 0;
                        line-height: 24px;
                        background: inherit;
                        margin-bottom: 6px;
                        // font-weight: 600;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        .placeholder{
                            max-width: 260px;
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            padding: 0 10px;
                            overflow: hidden;
                            font-size: 14px;
                            // white-space: pre;
                            // vertical-align: middle;
                            &:hover{
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                        :deep(.arco-input-wrapper .arco-input.arco-input-size-medium){
                            font-weight: 600!important;
                        }
                        :deep(.arco-input-wrapper){
                            border: none;
                            background-color: transparent;
                            font-weight: 600;
                            &:hover{
                                border: none;
                                background-color: transparent;
                                color: var(--tant-primary-color-primary-default);
                            }
                        }
                    }
                    .event-item{
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;
                        padding: 4px 0;
                        overflow: hidden;
                        line-height: 32px;
                        white-space: normal;
                    }
                }
            }
            .action-right{
                position: absolute;
                top: 0;
                right: 24px;
                min-width: 40px;
                height: 36px;
                padding-top: 0 !important;
                display: flex;
                align-items: center;
                opacity: 0;
                transition: opacity .3s;
            }
            .filter-btn{
                display: inline-flex;
                align-items: center;
                min-width: 40px;
                max-width: 200px;
                height: 26px;
                padding: 0 8px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                background-color: var(--tant-secondary-color-secondary-fill);
                border: 1px solid transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all .3s;
                box-sizing: border-box;
                .btn-icon{
                    color: var(--tant-text-gray-color-text1-2);
                    font-size: 14px;
                    margin-right: 5px;
                }
                .filter-label{
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    color: var(--tant-text-gray-color-text1-2);
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: top;
                }

                &:hover{
                    border-color: var(--tant-primary-color-primary-hover);
                }
            }
            .row-word{
                display: inline-block;
                margin: 0 4px;
                color: var(--tant-text-gray-color-text1-2);
                vertical-align: top;
            }
            &:hover{
            background-color: var(--tant-fill-color-fill1-2);
            .action-left .row-content :deep(.filter-btn){
                background: #fff;
            }
            .action-left .row-content :deep(.filter-icon){
                background: #fff;
            }
            .sub-action-left :deep(.filter-btn){
                background: #fff;
            }
            .action-right{
                opacity: 1;
            } 
        }
        }
        .row-foot{
            margin: 0;
            padding-left: 34px;
            transition: all .3s;
            .ta-filter-button{
                padding: 6px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                font-size: 14px;
                transition: all .3s;
                .action{
                    border-radius: 4px;
                    background-color: var(--tant-primary-color-primary-fill);
                    color: var(--tant-primary-color-primary-default);
                    margin-right: 8px;
                    padding: 3px;
                    font-size: 18px;
                }
                .label{
                    color: var(--tant-primary-color-primary-default);
                }
                &:hover .action{
                    background-color: var(--tant-primary-color-primary-fill-hover);
                    color: var(--tant-primary-color-primary-hover);
                }
            }
        }
    }
}
.btn-bg{
    background-color: transparent;
    &:hover{
        background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
}
.btn-26{
    width: 26px;
    height: 26px;
    border-radius: 4px;
}
.btn-bg-delete{
    background-color: transparent;
    &:hover{
        color: var(--tant-status-danger-color-danger-default);
        background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
}

</style>