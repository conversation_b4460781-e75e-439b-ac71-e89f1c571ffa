import {UserDto} from "@/api/authorize/type";
import {DashboardDto} from "@/api/dashboard/type";
import {ObjectPermissionType} from "@/api/enum";

/**
 * 看板目录实体
 */
export interface FolderDto {

  /**
   * 对象权限
   */
  authority: ObjectPermissionType;

  /**
   * 目录编码
   */
  folderId: string;

  /**
   * 目录名称
   */
  name: string;

  /**
   * 目录图标
   */
  icon: string;

  /**
   * 次序
   */
  order: number;

  /**
   * 上层目录
   * 用于支持多层目录，暂不使用
   */
  parentFolder?: FolderDto;

  /**
   * 所属空间
   */
  space: DashboardDto;

  /**
   * 创建人员
   */
  creator: UserDto;

  /**
   * 文件夹列表
   * 用于支持多层目录，暂不使用
   */
  folders?: FolderDto[];

  /**
   * 看板列表
   */
  dashboards?: DashboardDto[];

  /**
   * 是否允许删除
   */
  isDeletable:boolean;

  /**
   * 创建时间，毫秒时间戳
   */
  createTime: number;
}


/**
 * 看板目录实体
 */
export interface FolderSaveDto {

  /**
   * 目录编码
   */
  folderId?: string;

  /**
   * 目录名称
   */
  name?: string;

  /**
   * 目录图标
   */
  icon?: string;

  /**
   * 次序
   */
  order?: number;

  /**
   * 上层目录
   * 用于支持多层目录，暂不使用
   */
  parentFolder?: FolderDto;

  /**
   * 所属空间
   */
  spaceId: string;

  /**
   * 文件夹列表
   * 用于支持多层目录，暂不使用
   */
  folders?: string[];

  /**
   * 看板列表
   */
  dashboards?: string[];

  /**
   * 是否允许删除
   */
  isDeletable?:boolean;
}