<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import {cloneDeep} from 'lodash'

interface Props {
  /**
   * 展示label
   */
  showlabel: boolean

  /**
   * 报表数据
   */
  eventData: any;
  // y最大值
  yMax: number | null
  // y最小值
  yMin: number | null
}

const props = defineProps<Props>()


const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([])

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])
const chartType = ref('hxTrend')


function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);


const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '0%',
      top: '40',
      bottom: '40',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      bottom: '60',
      type: 'scroll', // 设置图例为滚动类型
      orient: 'horizontal', // 横向显示图例

    },
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      max:props.yMax,
      min:props.yMin,
      splitLine:{
        lineStyle:{
          type:'dashed'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
      axisPointer: {
        type: axisPointerType.value
      },
      formatter: (param:any) => {
        if(chartType.value === 'hxTrend') {
            return  `<div>${param[0].axisValue}</div>
              <div style="display:flex;align-items: center;">
              <div style="width:10px;height:10px;background: ${param[0].color};border-radius:50%;margin-right:5px;"></div>
              <div>${param[0].seriesName}</div>
              </div>
              <div>最大值:  ${param[0].data[5]}</div>
              <div>上四分位:  ${param[0].data[4]}</div>
              <div>中位数:  ${param[0].data[3]}</div>
              <div>下四分位:  ${param[0].data[2]}</div>
              <div>最小值:  ${param[0].data[1]}</div>
              `
        }
        if(chartType.value === 'zfTrend'){
          return  `<div>${param[0].axisValue}</div>
              <div style="display:flex;align-items: center;">
              <div style="width:10px;height:10px;background: ${param[0].color};border-radius:50%;margin-right:5px;"></div>
              <div style="width:100%;display:flex;align-items: center;justify-content: space-between;">
              <div>${param[0].seriesName}</div>
              <span>${param[0].value? param[0].value : '-'}</span>
              </div>
              </div>`
        }
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
  };
});

// 数组相加
const calculateSum = (array:any) => {
  return array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
};

const freshData = () => {
  axisPointerType.value = 'shadow'
  xAxis.value = ['2024-9-16','2024-9-17','2024-9-18','2024-9-19','2024-9-20','2024-9-21','2024-9-22']
  ySeries.value = [
    {
      name:'总体',
      data:[
        [82,83,82,83,82,83,83.11],
        [42,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11],
        [82,83,82,83,82,83,83.11]
      ],
      type: 'boxplot',
      label: {
        show: props.showlabel
      }
    }
  ]
  ySeriesData.value = cloneDeep(ySeries.value)
};


const freshDistributionData = () => {
  axisPointerType.value = 'shadow'
  xAxis.value = ['(0,300)秒','[600,900)秒','[900,1200)秒','[1200,1500)秒','[1500,1800)秒','[1800,2100)秒','[2100,2400)秒','[2400,2700)秒','[2700,3000)秒','[3000,3300)秒','[3300,3600)秒']
  ySeries.value = [
    {
      name:'人数',
      data:[0,0,0,0,0,17,31,47,56,61,83],
      type: 'bar',
      barWidth: 40,
      label: {
        show: props.showlabel,
        position: 'top'
      }
    },
  ]
  xAxisData.value = cloneDeep(xAxis.value)

};


const changeType = (type:string)=>{
  switch (type){
    case 'hxTrend':
      chartType.value = 'hxTrend'
      freshData()
      break;
    case 'zfTrend':
      chartType.value = 'zfTrend'
      freshDistributionData()
      break;
    default:
      break;
  }
}

const updateYAxisSeries = () => {
  
};


freshData()
// freshDistributionData()

watch(() => props.showlabel,(newValue:any,oldValue:any) => {
  ySeries.value.forEach((item:any,index:number)=>{
    item.label = {
      show: props.showlabel,
      position: 'top'
    }
  })
  
},{immediate:true})


const dropList = ref<any>(['合计','2024-9-16','2024-9-17','2024-9-18','2024-9-19','2024-9-20','2024-9-21','2024-9-22'])
const dropValue = ref('2024-9-16')

const dropSelect = (v) => {
  dropValue.value = v
}
defineExpose({
  changeType
})

</script>

<template>
  <div class="chart-content">
    <a-spin  style="width: 100%;height: 500px;">
      <div v-if="chartType === 'zfTrend'" class="filter">
        <div style="max-width: 180px;cursor: pointer;">
          <a-dropdown :update-at-scroll="true" @select="dropSelect">
            <div style="min-height: 32px;padding: 4px 8px;border: 1px solid var(--tant-border-color-border1-1);border-radius: 4px;">
              <span style="color: #ccc;">时间:</span>
              {{ dropValue }}
              <icon-down/>
            </div>
            <template #content>
              <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input placeholder="请输入搜索" style="border: none;">
                  <template #prefix>
                    <icon-search />
                  </template>
                </a-input>
              </div>
              <a-doption v-for="(item,index) in dropList" :key="index">{{ item }}</a-doption>
            </template>
          </a-dropdown>
        </div>
        
      </div>
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
  
}
.filter{
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}

</style>