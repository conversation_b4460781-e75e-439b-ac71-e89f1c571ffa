<template>
    <a-modal v-model:visible="modalVisible" :width="1000" title-align="start" :hide-title="true" :footer="false" modal-class="json-modal" body-style="padding:0;">
        <div class="modal-title" :style="!editAble ? 'background: rgb(78, 78, 224)' : ''">
            <div v-if="editAble" class="title">
                <span class="word">JSON编辑器</span>
            </div>
            <div v-else class="modal-action-content">
                <span>尚未保存的更改</span>
                <span class="division"></span>
                <a-button class="btn" :disabled="errors>0" @click="configSave">保存</a-button>
                <a-button class="cancel" @click="handleModalCancel">舍弃</a-button>
            </div>
            <icon-close class="close-icon" @click="handleModalCancel"/>
        </div>
        <div>
            <a-alert v-if="!errors" type="success" style="height: 48px;">JSON有效</a-alert>
            <a-alert v-else type="warning" style="height: 48px;">JSON无效</a-alert>
            <json-editor-vue v-model="config" class="editor" style="height: 600px; position: relative" @update="updataModel" @validationError="editError"/>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import JsonEditorVue from 'json-editor-vue3'

const emits = defineEmits(['updateJson'])
const modalVisible = ref(false)
const config = ref<any>()
const errors = ref(0);
const line = ref();
const isEqual = (value1: any, value2: any): boolean => {
  // 处理基本类型
  if (value1 === value2) {
    return true;
  }
  // 处理 null 和 undefined
  if (value1 == null || value2 == null) {
    return value1 === value2;
  }
  // 获取类型
  const type1 = typeof value1;
  const type2 = typeof value2;
  // 类型不同直接返回 false
  if (type1 !== type2) {
    return false;
  }
  // 处理数组
  if (Array.isArray(value1) && Array.isArray(value2)) {
    if (value1.length !== value2.length) {
      return false;
    }
    return value1.every((item, index) => isEqual(item, value2[index]));
  }

  // 处理对象
  if (type1 === 'object') {
    const keys1 = Object.keys(value1);
    const keys2 = Object.keys(value2);
    if (keys1.length !== keys2.length) {
      return false;
    }
    return keys1.every(key => 
      Object.prototype.hasOwnProperty.call(value2, key) && 
      isEqual(value1[key], value2[key])
    );
  }
  return false;
};
const editJsonValue = ref('')
// 是否可保存舍弃
const editAble = computed(() => {
  return isEqual(editJsonValue.value, config.value);
});
// 数据更新时触发
const updataModel = (val: any) => {
    config.value = val;
};
// 数据错误时触发
const editError = (a: any, error: any) => {
  errors.value = error.length;
  if (error[0]) {
    line.value = error[0].line;
  }
}
const handleModalCancel = () => {
    modalVisible.value = false
}
const openModal = (json: any) => {
  modalVisible.value = true
  if(json){
    if (json && typeof json === 'string') {
        try {
            config.value = JSON.parse(json);
        } catch (error) {
            config.value = null;
        }
    }else{
        config.value = null
    }
    editJsonValue.value = JSON.parse(JSON.stringify(json))
  }else{
    config.value = {}
    editJsonValue.value = ''
  }
}
const configSave = () => {
  emits('updateJson', JSON.stringify(config.value))
  modalVisible.value = false
}
defineExpose({
    openModal
})
</script>

<style lang="less" scoped>
.modal-title{
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px 0 16px;
    .title{
        .word{
            font-size: 20px;
            line-height: 28px;
            font-weight: 500;
            margin-left: 16px;
        }
    }
    .close-icon{
        font-size: 24px;
        cursor: pointer;
    }
    .modal-action-content{
        color: #fff;
        display: flex;
        align-items: center;
        .division{
            width: 1px;
            height: 24px;
            background: #fff;
            margin: 0 24px;
        }
        .btn{
            margin-right: 24px;
        }
        button{
            border-radius: 8px;
        }
    }
}
</style>