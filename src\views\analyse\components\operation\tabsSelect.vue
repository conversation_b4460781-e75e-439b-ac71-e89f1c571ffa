
<template>
  <div>
  <!-- 全部 事件属性 用户属性 用户分群 用户标签下拉选 -->
    <div class="screen-body">
      <a-trigger
          v-model:popup-visible="triggerVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          :update-at-scroll="true"
          style="z-index: 1002;"
          @click="handleTriggerVisible">
          <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
            <a-button v-if="!props.disabled" class="filter-btn">
                <img src="/icon/eventAttr.svg" class="btn-icon">
                <span v-if="filterData.objectName" class="filter-label">{{ filterData.objectDisplayName }}</span>
                <span v-else class="filter-label un-filter-label">选择属性</span>
                <span v-if="filterData.type == '4'" class="virtual-sign">*</span>
            </a-button>
            <template #content>
              <div class="trigger-box">
                <div class="card-header">
                  <div class="card-header-container">
                    <div class="header-icon"><icon-launch/></div>
                    <div class="header-title">
                      <div class="name">{{ filterData.objectDisplayName }}</div>
                    </div>
                    <div class="header-type">{{ getAttrName(filterData) }}</div>
                  </div>
                  <div class="title-sub">{{ filterData.objectName }}</div>
                </div>
                <div v-if="filterData.note" class="card-desc">
                  <div class="span-desc">{{ filterData.note }}</div>
                </div>
                <div v-else class="prop-bubble-card-desc">
                  <a-button v-if="!showTextArea" style="background-color: transparent;" @click="() => showTextArea = true">
                    <template #icon>
                      <icon-plus />
                    </template>
                    <template #default>添加备注</template>
                  </a-button>
                  <a-textarea v-if="showTextArea" :max-length="200" allow-clear show-word-limit />
                </div>
                <div class="card-footer">
                  <div></div>
                  <div class="action">
                    <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                    <a-tooltip content="前往属性详情" position="top">
                      <div class="action-icon" @click="toAttrDetail(filterData)">
                        <icon-launch/>
                      </div>
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </template>
          </a-trigger>
          <template #content>
            <div class="screen-content">
              <div style="border-bottom: 1px solid var(--tant-border-color-border1-1);">
                <a-input v-model:model-value="searchInput" placeholder="请输入搜索" style="border: none;height: 40px;">
                  <template #prefix>
                    <icon-search />
                  </template>
                </a-input>
              </div>
              <div class="screen-content-body">
                <a-list :virtual-list-props="{ height: 350 }" :data="filteredAllList">
                  <template #item="{ item, index }">
                    <a-list-item :key="index">
                      <div v-if="index == 0" style="border-bottom: 1px solid var(--tant-border-color-border1-1);padding-bottom: 8px;">
                        <div class="affix-eventName">
                          <img src="/icon/collect.svg">
                          收藏
                        </div>
                        <div v-if="!collectLists.length" class="favorite" style="height: 30px;width: 100%;">
                          点击选项右<icon-star/>侧添加收藏
                        </div>
                        <div v-for="(event,eventIndex) in collectLists" :key="eventIndex" ref="collect">
                          <a-doption :disabled='ChooseLists.includes(event.code)' :style="filterData.objectDisplayName ===event.displayName?'backgroundColor: var(--color-fill-2);' : ''">
                            <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                              <div class="screen-option">
                                <div class="screen-option" @click="handleEvent(event)">
                                  <div class="screen-option-name">
                                    {{ event.displayName }}
                                  </div>
                                  <div class="screen-option-attr">
                                    {{ getAttrName(event) }}
                                  </div>
                                  <div class="screen-option-type">
                                    <div class="type-text"></div>
                                  </div>
                                </div>
                                <div class="screen-option-collect">
                                    <a-tooltip content="取消收藏" position="top" mini>
                                    <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(event)">
                                    </a-tooltip>
                                </div>
                              </div>
                              <template #content>
                                <div class="trigger-box">
                                    <div class="card-header">
                                      <div class="card-header-container">
                                        <div class="header-icon"><icon-launch/></div>
                                          <div class="header-title">
                                            <div class="name">{{ event.displayName }}</div>
                                          </div>
                                          <div class="header-type">{{ getAttrName(event) }}</div>
                                      </div>
                                      <div class="title-sub">{{ event.name }}</div>
                                    </div>
                                    <div class="card-desc">
                                        <div v-if="event.note" class="span-desc">{{event.note}}</div>
                                        <div v-else class="span-desc">暂无备注</div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="action">
                                          <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                          <a-tooltip content="前往属性详情" position="top">
                                            <div class="action-icon" @click="toAttrDetail(event)">
                                              <icon-launch/>
                                            </div>
                                          </a-tooltip>
                                        </div>
                                    </div>
                                </div>
                              </template>
                            </a-trigger>
                          </a-doption>
                        </div>
                      </div>
                      <a-doption :disabled='ChooseLists.includes(item.code)' :style="filterData.objectDisplayName ===item.displayName?'backgroundColor: var(--color-fill-2);' : ''">
                        <a-trigger :trigger="['hover']" position="right" :update-at-scroll="true" style="z-index: 1003;">
                          <div v-if="item.displayName" class="screen-option">
                            <div class="screen-option" @click="handleEvent(item)">
                              <div class="screen-option-name">
                                {{ item.displayName }}
                              </div>
                              <div class="screen-option-attr">
                                {{ getAttrName(item) }}
                              </div>
                              <div class="screen-option-type">
                                <div class="type-text"></div>
                              </div>
                            </div>
                            <div class="screen-option-collect">
                              <div v-if="!item.collectType">
                                <a-tooltip content="点击收藏" position="top" mini>
                                  <img src="/icon/uncollect.svg" class="uncollect-button" @click="collectEvent(item)">
                                </a-tooltip>
                              </div>
                              <div v-else>
                                <a-tooltip content="取消收藏" position="top" mini>
                                  <img src="/icon/collect.svg" class="collect-button" @click="uncollectEvent(item)">
                                </a-tooltip>
                              </div>
                            </div>
                          </div>
                          <template #content>
                            <div class="trigger-box">
                              <div class="card-header">
                                <div class="card-header-container">
                                  <div class="header-icon"><icon-launch/></div>
                                  <div class="header-title">
                                    <div class="name">{{ item.displayName }}</div>
                                  </div>
                                  <div class="header-type">{{ getAttrName(item) }}</div>
                                </div>
                                <div class="title-sub">{{ item.name }}</div>
                              </div>
                              <div class="card-desc">
                                <div v-if="item.note" class="span-desc">{{item.note}}</div>
                                <div v-else class="span-desc">暂无备注</div>
                              </div>
                              <div class="card-footer">
                                <div></div>
                                <div class="action">
                                  <span class="tip">点击按钮进行快速编辑 <icon-arrow-right /></span>
                                  <a-tooltip content="前往属性详情" position="top">
                                    <div class="action-icon" @click="toAttrDetail(item)">
                                      <icon-launch/>
                                    </div>
                                  </a-tooltip>
                                </div>
                              </div>
                            </div>
                          </template>
                        </a-trigger>
                      </a-doption>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </template>
      </a-trigger>
      <div v-if="props.disabled" class="filter-btn-disabled">
          <img src="/icon/eventAttr.svg" class="btn-icon">
          <span v-if="filterData.objectName" class="filter-label">{{ filterData.objectDisplayName }}</span>
          <span v-else class="filter-label un-filter-label">选择属性</span>
          <span v-if="filterData.type == '4'" class="virtual-sign">*</span>
      </div>
      <a-trigger
          v-if="props.showDetailFilter"
          v-model:popup-visible="subVisible"
          trigger="click"
          :unmount-on-close="false"
          position="bl"
          :update-at-scroll="true"
          @click="handleSubVisible">
          <a-button v-if="!props.disabled" class="filter-btn" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
            <span class="filter-label" style="white-space:wrap;text-align: left;">
              {{ filterData.calcuSymbolName }}
              <span v-if="showSelectData">{{ nameString }}</span>
              <span v-if="showSelectArr">{{ nameString }}</span>
            </span>
          </a-button>
          <template #content>
            <div class="builder-content">
              <div class="builder-panel-container">
                <span class="quot-name">{{ filterData.objectDisplayName }}</span>
                <!-- 第一级筛选条件 -->
                <div class="operator-select">
                    <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('textRangeList',$event)">
                      <template #arrow-icon>
                      </template>
                      <a-option v-for="(item,index) in textRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                    </a-select>
                </div>
                <div style="display: flex;align-items: center;">
                  <!-- 不同选择项，第二级筛选条件 -->
                  <div v-if="filterData.objectType" class="equal-search">
                  <!-- 多选时 -->
                    <a-select
                      v-if="['等于', '不等于'].includes(filterData.calcuSymbolName)"
                      v-model="filterData.selectArr"
                      multiple
                      allow-search
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      @change="selectArrChange"
                      @popup-visible-change="selectArrPopup">
                      <template #arrow-icon>
                      </template>
                      <template #search-icon>
                      </template>
                      <template #loading-icon>
                      </template>
                      <a-option v-for="(item,index) in sortedEnumList" :key="index" :value="item.code" style="width: 200px;">{{ item.name }}</a-option>
                    </a-select>
                    <!-- 单选时 -->
                    <a-select
                      v-if="filterData.calcuSymbolName == '包括' || filterData.calcuSymbolName == '不包括'"
                      v-model="filterData.selectData"
                      style="width: 82px;"
                      allow-search
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      @change="selectDataChange"
                      @popup-visible-change="selectArrPopup">
                      <template #arrow-icon>
                      </template>
                      <template #search-icon>
                      </template>
                      <template #loading-icon>
                      </template>
                      <a-option v-for="(item,index) in selectEnumList" :key="index" :value="item.code" style="width: 200px;">{{ item.name }}</a-option>
                    </a-select>
                    <div v-if="['等于', '不等于'].includes(filterData.calcuSymbolName)" class="option-edit" @click="handleAddClick">
                      <icon-edit />
                    </div>
                  </div>
                </div>
              </div>
              <div class="desc-foot">
                <div class="desc-tip">
                    <icon-bulb class="bulb"/>
                    <span>{{filterData.calcuSymbolName}}：{{getCalTips(filterData.calcuSymbolName)}}</span>
                </div>
              </div>
            </div>
          </template>
      </a-trigger>
      <div v-if="props.disabled && props.showDetailFilter" class="filter-btn-disabled" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
        <span class="filter-label" style="white-space:wrap;text-align: left;">
          {{ filterData.calcuSymbolName }}
          <span v-if="showSelectData">{{ nameString }}</span>
          <span v-if="showSelectArr">{{ nameString }}</span>
        </span>
      </div>
      <!-- 批量添加 -->
      <a-modal v-model:visible="addVisible" :closable="false" @ok="handleAddOk" @cancel="handleAddCancel">
        <div class="batch-option-edit">
          <div class="boe-title">
            <span>批量添加</span>
            <span class="boe-subtitle">使用回车换行分割</span>
          </div>
          <div class="boe-main">
            <a-textarea v-model="batchText" :auto-size="{ minRows: 8, maxRows: 15 }" placeholder="请输入内容，每行一个"/>
          </div>
        </div>
        <template #footer>
          <div class="boe-foot">
            <a-button type="primary" style="float: left;">全选复制</a-button>
            <a-button style="margin-right: 8px;" @click.stop="handleAddCancel">取消</a-button>
            <a-button type="primary" @click.stop="handleAddOk">确定</a-button>
          </div>
        </template>
      </a-modal>
    </div>
    <div class="fixed-modal" :style="{display: triggerVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import {analyseStore} from '@/store';
import {getSelectList} from "@/api/analyse/api";
import {cloneDeep} from "lodash";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";
import {sortSelectedFirst} from "@/utils/sortUtil";

const analyseData = analyseStore()
const showTextArea = ref(false)

interface screenConfig {
  name: string,
  type?: string,
  note?:string,
  objectDisplayName?:string,
  filterType?: string, // tab分类
  objectType?: string,
  code: string
  collectType: boolean,
}

const props = defineProps({
  // 禁用选择
  disabled:{
    type:Boolean,
    default:false
  },
  // 不可选数据
  disabledList:{
    type: Array,
    default: () => []
  },
  // 只有事件列表
  onlyEvent:{
    type:Boolean,
    default:false
  },
  // 只有用户属性列表
  onlyUser:{
    type:Boolean,
    default:false
  },
  // 不包括事件列表
  excludeEvent:{
    type:Boolean,
    default:false
  },
  // 是否提供选择范围
  showDetailFilter:{
    type:Boolean,
    default:false
  },
  info: {
    type: Object,
    default() {
      return {};
    },
  },
})
const emits = defineEmits(['tabsChange']);
const toAttrDetail = (record:any) => {
  router.push({name: ROUTE_NAME.OPERATION_ANALYSE_EVENT_ATTRIBUTE,query: {text: record.name || record.objectDisplayName}})
}

// 模拟数据
const EventLists = ref<screenConfig[]>([])

const getCalTips = (name:string) => {
  const labels: { [key: string]: string } = {
    '等于': '属性值等于任一设定值',
    '不等于': '属性值不等于任一设定值，且不为空',
    '包括': '属性值包括与设定值完全一致的部分',
    '不包括': '属性值没有与设定值完全一致的部分',
  };
  return labels[name] || '';
}

const ChooseLists = ref<any>([])
const collectLists = ref<any>([])
const searchInput = ref('')
interface FilterData {
    objectName: string;
    objectDisplayName:string;
    calcuSymbolName: string;
    calcuSymbol: string;
    thresholds?: (string | number)[];
    objectType: string | undefined;
    objectId:string;
    filterType:string;
    type: string; // 判断维度表等属性
    note: string;
    selectData: string;
    selectArr: any[];
    enumList:any[]
}
const filterData: FilterData = reactive({
  objectName:'',
  objectDisplayName:'',
  calcuSymbolName:'等于',
  calcuSymbol:'eq',
  thresholds:[],
  objectType:'',
  objectId:'',
  filterType:'',
  type:'',
  note:'',
  selectData:'',
  selectArr:[],
  enumList:[]
})
// 方法获取属性
const getAttrName = (value:any) => {
  const labels: { [key: number]: string } = {
    1: '预置属性',
    2: '自定属性',
    3: '虚拟属性',
    4: '维度表属性',
  };
  return labels[value.type] || '';
};
// 文本选项
const textRangeList = ref([
  {value:'eq',label:'等于'},
  {value:'neq',label:'不等于'},
  {value:'con',label:'包括'},
  {value:'ncon',label:'不包括'},
  // {value:'ex',label:'有值'},
  // {value:'nex',label:'无值'},
  // {value:'rm',label:'正则匹配'},
  // {value:'nrm',label:'正则不匹配',}
])

const getRangeType = (data?:string) => {
  // data是传入calcuSymbo值，用以筛选calcuSymbolName显示值
  const type = filterData.objectType || EventLists.value[0]?.objectType
  const rangeLists = {
    'string': textRangeList.value,
  };
  const matchedItem = rangeLists[type]?.find(item => item.value === data) || rangeLists[type]?.[0];
  return matchedItem
};


// 获取事件属性list;
// 如果传入name-list 则请求接口获取，否则从props.eventLists种获取
const nameString = ref('')
const selectLoading = ref(false)
const selectEnumList = ref<any>([])
const sortedEnumList = ref<any>([])

const handleNameString = () => {
  selectEnumList.value = props.info?.enumList?.length > 0 ? cloneDeep(props.info.enumList) : selectEnumList.value
  
  // 将 thresholds 中的值映射为显示值
  const displayValues = filterData.thresholds.map(threshold => {
    // 在枚举列表中查找匹配的项
    const enumItem = selectEnumList.value.find(item => item.code === threshold)
    // 如果找到匹配项则返回name，否则返回原始值
    return enumItem ? enumItem.name : threshold
  })

  nameString.value = displayValues.join(',')
}
const getEventList = async () => {
  const list  = props.showDetailFilter ? analyseData.$state.operationFilterLists : analyseData.$state.operationGroupLists;
  EventLists.value = list.map((item:any,index) => {
    return {
      ...item,
      filterType:'operation',
      objectType:item?.dataType,
      collectType:false
    }
  })
  const v = EventLists.value[0]
  filterData.objectName = props.info?.objectName
  filterData.objectDisplayName = props.info?.objectDisplayName || props.info?.displayName
  filterData.filterType = props.info?.filterType || props.info?.aggregateType || v.filterType
  filterData.objectType = props.info?.objectType || v?.objectType
  filterData.objectId = props.info?.code || props.info?.objectId || v.code
  filterData.calcuSymbolName = props.info.calcuSymbol ? getRangeType(props.info.calcuSymbol).label :  getRangeType().label
  filterData.calcuSymbol = props.info.calcuSymbol?props.info.calcuSymbol:getRangeType().value
  filterData.type = props.info?.type || v?.type
  filterData.note = props.info?.note || v?.note
  filterData.thresholds = props.info?.thresholds || []
  filterData.enumList = props.info?.enumList || []
  if(filterData.thresholds && filterData.thresholds.length>0){
    // filterData.selectArr = props.info?.thresholds || []
    const arr = cloneDeep(props.info?.thresholds || [])
      if(filterData.objectType && ['包括', '不包括'].includes(filterData.calcuSymbolName)){
        filterData.selectData = arr[0] || ''
        handleNameString()
      }
      if(filterData.objectType  && ['等于', '不等于'].includes(filterData.calcuSymbolName)){
        filterData.selectArr = arr
        handleNameString()
      }
  }
  emits('tabsChange',filterData)
}
getEventList()



const filteredAllList = computed(() => {
  const str = searchInput.value.trim();
  if(str){
    const list = EventLists.value.filter(item => item?.name?.includes(str) || item?.displayName?.includes(str));
    return list
  }
  
  return EventLists.value
});

const triggerVisible = ref<boolean>(false)
const subVisible = ref<boolean>(false)

const handleTriggerVisible = () => {
  // ChooseLists.value = props.disabledList.map(item => item.code)
  triggerVisible.value = !triggerVisible.value;
}
watch(() => props.disabledList, ()=> {
  if(props.disabledList.length>0){
    ChooseLists.value = props.disabledList.map(item => item.objectId)
  }
},{deep:true,immediate:true})
const handleSubVisible = () => {
    subVisible.value = !subVisible.value;
}


// 点击
const handleEvent = (v:any) => {
  filterData.objectName = v.name
  filterData.objectDisplayName = v.displayName
  filterData.filterType = v.filterType
  filterData.objectType =  v?.objectType || ''
  filterData.objectId = v.code
  filterData.calcuSymbolName = getRangeType().label
  filterData.calcuSymbol = getRangeType().value
  filterData.type = v?.type || ''
  filterData.note = v.note
  triggerVisible.value = false
  filterData.selectArr = []
  filterData.selectData = ''
  filterData.thresholds = []
  filterData.enumList = []
  subVisible.value = true
  emits('tabsChange',filterData)
}
// 收藏
const collectEvent = (e) => {
  collectLists.value.push(e)
  e.collectType = true
}
// 取消收藏
const uncollectEvent = (e) => {
  e.collectType = false
  const a = collectLists.value.indexOf(e)
  collectLists.value.splice(a, 1)
}
const rangeTypeChange = (name,v) => {
  const list = {
    'textRangeList': textRangeList.value,
  };
  const arr = list[name]
  filterData.calcuSymbol = v

  arr.forEach(item => {
    if(item.value === v){
      filterData.calcuSymbolName = item.label
    }
  })
  filterData.selectData = ''
  filterData.selectArr = []
  filterData.thresholds = []
  emits('tabsChange',filterData)
}

// 多选筛选下拉列表
const selectArrChange = (v) => {
  filterData.thresholds = v
  handleNameString()
  emits('tabsChange',filterData)
}

const selectArrPopup = async (v) => {
  if(filterData.enumList.length>0){
    selectEnumList.value = cloneDeep(filterData.enumList)
  }else{
    let url = ''
    EventLists.value.forEach(item => {
      if(item.code === filterData.objectId){
        url = item.dataUrl || ''
      }
    })
    if(v){
      selectLoading.value = true
      await getSelectList(url).then(res => {
        if(res){
          selectEnumList.value = res
          filterData.enumList = cloneDeep(selectEnumList.value)
        }
        selectLoading.value = false
      }).catch(error => {
        selectLoading.value = false;
      });
    }
  }
  sortedEnumList.value = sortSelectedFirst(selectEnumList.value, filterData.selectArr)

}
// 单选筛选下拉列表
const selectDataChange = (v) => {
  filterData.thresholds = []
  filterData.thresholds.push(v)
  handleNameString()
  emits('tabsChange',filterData)
}

// 批量添加
const addVisible = ref(false)
const batchText = ref('')
const copyText = ref('')
// 打开批量添加弹窗
const handleAddClick = () => {
  batchText.value = ''
  addVisible.value = true
  // 如果已有选中值，将其转换为文本
  if (filterData.selectArr?.length) {
    batchText.value = filterData.selectArr.join('\n')
    copyText.value = filterData.selectArr.join('\n')
  }
}
// 确认批量添加
const handleAddOk = () => {
  if (!batchText.value) {
    addVisible.value = false
    return
  }
  // 将文本按换行符分割并过滤空值
  const values = [...new Set(batchText.value.split('\n').filter(item => item.trim()))]
  // 更新选中值
  filterData.selectArr = values
  filterData.thresholds = values
  handleNameString()
  addVisible.value = false
  emits('tabsChange', filterData)
}
// 取消批量添加
const handleAddCancel = () => {
  batchText.value = ''
  addVisible.value = false
}

const showSelectArr = computed(() => {
  const flag = filterData.objectType && ['等于', '不等于'].includes(filterData.calcuSymbolName) && filterData.selectArr.length>0
  return flag
})
const showSelectData = computed(() => {
  const flag = filterData.objectType && ['包括', '不包括'].includes(filterData.calcuSymbolName) && filterData.selectData
  return flag
})

watch(() => props.info, (newVal) => {
  if(newVal){
    filterData.objectName = props.info?.objectName
    filterData.objectDisplayName = props.info?.objectDisplayName || props.info?.displayName
    filterData.filterType = props.info?.filterType || props.info?.aggregateType || EventLists.value[0]?.filterType
    filterData.objectType = props.info?.objectType || EventLists.value[0]?.objectType
    filterData.objectId = props.info?.objectId || EventLists.value[0]?.code
    filterData.calcuSymbolName = props.info.calcuSymbol ? getRangeType(props.info.calcuSymbol).label :  getRangeType().label
    filterData.calcuSymbol = props.info.calcuSymbol ? props.info.calcuSymbol : getRangeType().value
    filterData.type = props.info?.type || EventLists.value[0]?.type
    filterData.note = props.info?.note || EventLists.value[0]?.note
    filterData.thresholds = props.info?.thresholds || []
    filterData.enumList = props.info?.enumList || []
    sortedEnumList.value = props.info?.enumList || []
    if(filterData.thresholds && filterData.thresholds.length>0){
      // filterData.selectArr = props.info?.thresholds || []
      const arr = cloneDeep(props.info?.thresholds || [])
      if(filterData.objectType && ['包括', '不包括'].includes(filterData.calcuSymbolName)){
        filterData.selectData = arr[0] || ''
        handleNameString()
      }
      if(filterData.objectType&& ['等于', '不等于'].includes(filterData.calcuSymbolName)){
        filterData.selectArr = arr
        handleNameString()
      }
    }
  }
  // emits('tabsChange',filterData)
}, {deep: true,immediate:true });
</script>

<style scoped lang="less">
:deep(.arco-select-view-tag){
  white-space: nowrap!important;;
}
.fixed-modal{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}
.filter-btn,.filter-btn-disabled{
    display: inline-flex;
    align-items: center;
    min-width: 40px;
    max-width: 200px;
    height: 26px;
    padding: 0 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all .3s;
    box-sizing: border-box;
    margin:2px 0;
    .btn-icon{
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      margin-right: 5px;
    }
    .filter-label{
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
    }
    .un-filter-label{
      color: var(--tant-text-gray-color-text1-4);
    }
    .virtual-sign {
        color: var(--tant-primary-color-primary-default);
    }
}

.filter-btn{
  cursor: pointer;
  &:hover{
      border-color: var(--tant-primary-color-primary-hover);
  }
}
:deep(.arco-dropdown-option){
    border-radius: 4px;
}
.trigger-box{
    display: flex;
    flex-direction: column;
    width: 280px;
    max-height: 360px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-small-shadow-small-overall);
    .tip{
      opacity: 0;
      padding-right: 4px;
    }
    &:hover{
      .tip{
        opacity: 1;
      }
    }
    .card-header{
        padding: 12px;
        background-color: var(--tant-bg-gray-color-bg2-1);
        border-radius: 4px 4px 0 0;
        .card-header-container{
            display: flex;
            flex-direction: row;
            .header-icon{
                margin-right: 5px;
                color: var(--tant-text-gray-color-text1-1);
            }
            .header-title{
                flex-grow: 1;
                color: var(--tant-text-gray-color-text1-1);
                .name{
                    display: inline-block;
                    width: 100%;
                    padding: 0 4px;
                    word-break: break-all;
                    border-radius: 4px;
                    color: var(--tant-text-gray-color-text1-1);
                    font-weight: 500;
                    line-height: 22px;
                }
                
            }
            .header-type{
                flex-shrink: 0;
                width: 62px;
                margin-left: 6px;
                color: var(--tant-text-gray-color-text1-3);
                font-weight: 400;
                font-size: 12px;
                text-align: right;
            }
        }
        .title-sub{
            margin-left: 24px;
            color: var(--tant-text-gray-color-text1-2);
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            word-break: break-all;
        }
    }
    .card-desc{
        flex-grow: 1;
        height: 130px;
        padding: 12px 8px;
        overflow-y: auto;
        color: var(--tant-text-gray-color-text1-2);
        font-weight: 400;
        font-size: 14px;
        .span-desc{
            display: inline-block;
            width: 100%;
            padding: 0 4px;
            word-break: break-all;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    }
    .card-detail{
      padding: 6px 12px 4px;
      color: var(--tant-text-gray-color-text1-3);
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      box-shadow: inset 0 1px 0 var(--tant-border-color-border1-1);
      .detail-item{
        padding-bottom: 4px;
      }
    }
    .prop-bubble-card-desc{
      flex-grow: 1;
      height: 130px;
      padding: 12px 8px;
      overflow-y: auto;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 14px;
      :deep(.arco-textarea-wrapper){
        height: 100px;
        padding: 5px 8px;
        font-size: 14px;
        line-height: 22px;
        background-color: transparent;
        border-radius: var(--tant-border-radius-medium);
        resize: none;
        color: var(--tant-text-gray-color-text1-2);
        border: 1px solid var(--tant-border-color-border1-1);
      }
      :deep(.arco-textarea){
        border: none;
        padding: none;
      }
    }
    .card-footer{
        display: flex;
        flex-shrink: 0;
        justify-content: space-between;
        height: 34px;
        padding-top: 6px;
        padding-right: 12px;
        padding-left: 12px;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        border-radius: 0 0 4px 4px;
        box-shadow: inset 0 1px #e6e6e6;
        .action{
          .action-icon{
              display: inline-block;
              min-width: 24px;
              height: 24px;
              padding: 0 4px;
              line-height: 22px;
              text-align: center;
              background-color: transparent;
              border-radius: 2px;
              cursor: pointer;
              transition: background-color .3s;
          }
          .action-icon:hover {
              background-color: var(--tant-primary-color-primary-fill-hover);
              color: var(--tant-primary-color-primary-default);
          }
        }
    }
}
.builder-content{
  display: flex;
  flex-direction: column;
  width: 400px;
  min-width: 400px;
  max-height: 420px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: var(--tant-small-shadow-small-overall);
  .builder-panel-container{
    flex-grow: 1;
    padding: 24px 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    line-height: 40px;
    .quot-name{
      margin-right: 6px;
      font-weight: 500;
      line-height: 20px;
      vertical-align: text-top;
    }
    .operator-select{
      display: inline-block;
      margin-right: 10px;
    }
    .item-index-number{
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 100px;
      margin-right: 8px;
    }
    .absolute-date{
      display: inline-block;
      :deep(.arco-picker){
        border-radius: 4px;
      }
    }
    .relative-date{
      display: inline-flex;
      align-items: center;
      vertical-align: top;
    }
    .word{
        margin: 0 6px;
        color: var(--tant-text-gray-color-text1-2);
        font-weight: 400;
        font-size: 14px;
      }
      .next-input{
        display: inline-flex;
        align-items: center;
        vertical-align: top;
        margin-left:8px;
      }
      .next-input-number{
        width: 60px;
        margin-right: 8px;
      }
  }
  .equal-search{
      display: inline-block;
      max-width: 360px;
      min-width: 80px;
      position: relative;
      .option-edit{
        position: absolute;
        top: 0px;
        right: 4px;
        z-index: 9;
        // padding: 4px;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        opacity: 0;
        cursor: pointer;
        &:hover{
          color: var(--tant-primary-color-primary-hover);
        }
      }
      &:hover .option-edit{
        opacity: 1;
      }
    }
  .desc-foot{
        display: flex;
        align-items: center;
        padding: 12px 14px;
        font-size: 13px;
        line-height: 20px;
        background: var(--tant-bg-gray-color-bg2-1);
        border-top: 1px solid var(--tant-border-color-border1-1);
        .desc-tip{
            display: flex;
            align-items: center;
            color: var(--tant-text-gray-color-text1-3);
            .bulb{
                margin-right: 10px;
                color: var(--tant-decorative-yellow-color-decorative6-1);
                font-size: 18px;
                vertical-align: top;
            }
        }
    }
}
:deep(.arco-modal-footer){
  border: none!important;
}
.batch-option-edit{
  min-height: 200px;
  overflow: hidden;
  .boe-title{
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--tant-text-gray-color-text1-1);
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    .boe-subtitle {
        color: var(--tant-text-gray-color-text1-4);
        font-weight: 400;
        font-size: 12px;
    }
  }
  .boe-main{
    width: 100%;
    margin-top: 8px;
    :deep(.arco-textarea-wrapper){
      height: 164px;
      padding: 5px 8px;
      font-size: 14px;
      line-height: 22px;
      background-color: transparent;
      border-radius: var(--tant-border-radius-medium);
      resize: none;
      color: var(--tant-text-gray-color-text1-2);
      border: 1px solid var(--tant-border-color-border1-1);
    }
    :deep(.arco-textarea){
      border: none;
      padding: none;
    }
  }
  
}
.boe-foot{
  width: 100%;
  text-align: right;
  :deep(.arco-btn){
    height: 24px;
    padding: 1px 8px;
    font: var(--tant-body-font-body-regular);
    border-radius: var(--tant-border-radius-medium);
    border: none;
  }
}

.screen-bt {
  color: var(--tant-text-gray-color-text1-2);
  text-shadow: none;
  background-color: var(--tant-bg-white-color-bg1-1);
  border: 1px solid var(--tant-border-color-border1-1);
  border-radius: var(--tant-border-radius-medium);
  box-shadow: none;
  font-size: 14px;
  padding: 5px 16px;
}

.screen-bt:hover {
  cursor: pointer;
  background-color: var(--tant-bg-gray-color-bg2-1);
  transition: .3s;
}


.screen-content {
  width: 360px;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  .screen-content-body {
    margin: 0 8px;
    :deep(.arco-tabs-content){
      padding-top: 0;
    }
    :deep(.arco-list-item) {
      padding: 0;
      border: none;
    }
    :deep(.arco-list-bordered) {
      border: none;
    }
    :deep(.arco-list-content-wrapper .arco-list-content.arco-list-virtual .arco-list-item){
      padding: 0;
      border: none;
    }
    :deep(.arco-list-header){
      padding: 0;
    }
    .screen-tabHeight {
      height: 350px;
      overflow: auto;
      position: relative;
      .affix{
        width: 100%;
        position: sticky;
        top:0;
        z-index: 99;
      }
    }
    .affix-eventName {
          display: flex;
          align-self: center;
          background-color: var(--tant-bg-white-color-bg1-1);
          color: var(--tant-text-black-color-text1-1);
          padding: 12px;
          width: 100%;
          font-size: 14px;
        }
    .screen-option {
      display: flex;
      .screen-option-group {
        display: inline-block;
        padding-left: 60px;
        width: 130px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 10px;

      }
      .screen-option-name {
        display: inline-block;
        width: 150px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .screen-option-attr {
        display: inline-block;
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;
      }

      .screen-option-type {
        width: 65px;
        text-align: left;
        color: var(--tant-text-gray-color-text1-3);
        font-weight: 400;
        font-size: 12px;

        .type-text {
          display: flex;
          align-items: center
        }
        .typeIcon {
          width: 12px;
          height: 12px;
          margin-right: 4px;
        }
      }
      .screen-option-collect {
        display: flex;
        align-items: center;
        width: 30px;
        padding: 0;
        .uncollect-button {
          display: none;

        }
        .collect-button {
          display: flex;
          align-self: center;
          border: none;
          margin-right: 5px;
          color: var(--tant-text-gray-color-text1-3);
        }
      }
    }
    .screen-option:hover {
      .uncollect-button {
        display: flex;
        align-self: center;
        border: none;
        margin-right: 5px;
        color: var(--tant-text-gray-color-text1-3);

      }
    }
  }
}
.affix-eventName {
  display: flex;
  align-self: center;
  background-color: var(--tant-bg-white-color-bg1-1);
  color: var(--tant-text-black-color-text1-1);
  padding: 12px;
  width: 100%;
}
.favorite {
  height: 30px;
  width: 100%;
  padding-left: 18px;
  color: var(--tant-text-gray-color-text1-4);
  font-weight: 400;
}
</style>