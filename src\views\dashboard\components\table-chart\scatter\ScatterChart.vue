<template>
    <div class="chart-content">
      <a-spin :loading="loading" style="width: 100%;height: 100%">
        <Chart :option="chartOption"/>
      </a-spin>
    </div>
  </template>
  
  <script lang="ts" setup>
  import {onMounted, ref, watch} from 'vue';
  import useLoading from '@/hooks/loading';
  import useChartOption from '@/hooks/chart-option';
  import {AnyObject} from '@/types/global';
  import { TimeParticleSize } from '@/api/enum';


  interface Props {
    /**
     * 报表数据
     */
    reportAnalysisData: object;
    showLabel:boolean;
    selectGroupName:string;
    chartType:string;
  }
  
  const props = defineProps<Props>()
  
  function graphicFactory(side: AnyObject) {
    return {
      type: 'text',
      bottom: '8',
      ...side,
      style: {
        text: '',
        textAlign: 'center',
        fill: '#4E5969',
        fontSize: 12,
      },
    };
  }
  
  const {loading, setLoading} = useLoading(true);
  const xAxis = ref<string[]>([]);
  const ySeries = ref<any>([]);
  const legendData = ref<any>([]);
  const axisPointerType = ref('')
  const indicatorData = ref({})
  const graphicElements = ref([
    graphicFactory({left: '2.6%'}),
    graphicFactory({right: 0}),
  ]);
  // 千分位
  const formatNumber = (num) => {
      return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
  }
  // 格式化时间列
  const formatTimeColumn = (dateString:string) => {
    const {timeParticleSize}  = props.reportAnalysisData
    if(timeParticleSize === TimeParticleSize.DAY1){
      const date = new Date(dateString);
      const days = ['日', '一', '二', '三', '四', '五', '六'];
      return `${dateString}(${days[date.getDay()]})`;
    }
    if(timeParticleSize === TimeParticleSize.WEEK1){
      return `${dateString}当周`;
    }
    if(timeParticleSize === TimeParticleSize.MONTH1){
      return `${dateString}月`;
    }
    return `${dateString}`;
  }
  const handleLineChart = (wsResultData: any) => {
    axisPointerType.value = 'line'
    const { scatterQueryResult,stages } = wsResultData
    const secondaryData = props.chartType ==='trend' ? scatterQueryResult?.[1] : scatterQueryResult?.[0]
    xAxis.value = secondaryData.map(item => item.eventDate)
    legendData.value = stages
    // 处理每个阶段的数据
    const commonSeries = stages.map((stageName, index) => {
      // 根据是否选择总体来决定使用哪个数据源
      const getData = (item) => {
        let value;
        if (props.selectGroupName === '总体') {
          value = item.summaryData[index]
        } else {
          // 在 groupData 中查找匹配的分组
          const groupItem = item.groupData.find(g => g.group.join(',') === props.selectGroupName)
          value = groupItem ? groupItem.values[index] : 0
        }
        
        // 如果是堆叠百分比图，计算该值占总和的百分比
        if (props.chartType === 'stackedRate') {
          const total = props.selectGroupName === '总体' 
            ? item.summaryDataSum
            : item.groupData.find(g => g.group.join(',') === props.selectGroupName)?.valueSum || 0
          return total ? Number(((value / total) * 100).toFixed(2)) : 0
        }
        return value
      }
      return {
        name: stageName,
        type: 'line',
        data: secondaryData.map(item => getData(item)),
        label: {
          show: props.showLabel,
          formatter: props.chartType === 'stackedRate' ? '{c}%' : '{c}'
        }
      }
    })
    if (props.chartType === 'stacked' || props.chartType === 'stackedRate') {
      ySeries.value = commonSeries.map(series => ({
        ...series,
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        }
      }))
    } else {
      ySeries.value = commonSeries
    }
  }
  const handleShadowChart = (wsResultData: any) => {
    legendData.value = []
    axisPointerType.value = 'shadow'
    const { scatterQueryResult,stages } = wsResultData
    const secondaryData = props.chartType ==='distribution' ? scatterQueryResult?.[1] : scatterQueryResult?.[0]
    xAxis.value = stages
    const getSeriesData = (data) => {
        if (props.selectGroupName === '总体') {
          return data.summaryData
        }
        // 在 groupData 中查找匹配的分组
        const groupItem = data.groupData.find(g => g.group.join(',') === props.selectGroupName)
        return groupItem ? groupItem.values : []
      }
    const seriesData = getSeriesData(secondaryData[0])
    ySeries.value = [
      {
        name: `${indicatorData.value.eventDisplayName}.${indicatorData.value.eventAttrName}`,
        type: 'bar',
        data: seriesData,
        barWidth: props.chartType === 'zfTrend'? null : 40,
        barCategoryGap:props.chartType === 'zfTrend' ? 5 : null,
        label: {
          show: props.showLabel,
          position: 'insideTop'
        }
      }
    ]
  }
  function renderChart(wsResultData: any) {
    if (!wsResultData) {
      return
    }
    if(!wsResultData?.scatterQueryResult?.length){
      setLoading(false)
      return
    }
    if(props.chartType !== 'zfTrend' && props.chartType !== 'distribution'){
      handleLineChart(wsResultData)
    }else{
      handleShadowChart(wsResultData)
    }
    setLoading(false)
  }
  
  watch(() => props.reportAnalysisData, (newData, oldData) => {
    indicatorData.value = newData?.indicator?.eventList?.[0]
    renderChart(newData);
  })
  const {chartOption} = useChartOption(() => {
    return {
      grid: {
        left: '0%',
        right: '1%',
        top: '20',
        bottom: '50',
        containLabel: true
      },
      legend: {
        data: legendData.value,
        bottom: '0',
        type: 'scroll', // 设置图例为滚动类型
        orient: 'horizontal', // 横向显示图例
      },
      xAxis: {
        type: 'category',
        data: xAxis.value
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: props.chartType === 'stackedRate' ? '{value} %' : '{value}'
        },
        max:props.chartType === 'stackedRate' ? 100 : null,
        splitLine:{
          lineStyle:{
            type:'dashed'
          }
        }
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        className: 'echarts-tooltip-diy',
        axisPointer: {
          type: axisPointerType.value
        },
        enterable: true,
        confine: true,
        extraCssText: 'max-height: 300px; overflow-y: auto;width: 180px;',
        // valueFormatter: (value) => `${value}%`,
        formatter: (params) => {
          const date = params[0].axisValue;
          const formattedDate = formatTimeColumn(date);
          let result = `${formattedDate}<br/>`;
          params.forEach(param => {
            const marker = param.marker;
            const seriesName = param.seriesName;
            const value = props.chartType === 'stackedRate' ? `${param.value}%` : param.value;
            result += `${marker} ${seriesName}: ${formatNumber(value)}<br/>`;
          });
          return result;
        }
      },
      graphic: {
        elements: graphicElements.value,
      },
      series: ySeries.value,
    };
  });
  watch(() => props.showLabel,(newValue:any,oldValue:any) => {
    ySeries.value.forEach((item:any,index:number)=>{
      item.label = {
        show: props.showLabel,
        formatter: (params) => formatNumber(params.value)
      }
    })
  })
  watch(() => props.selectGroupName,(newValue:any,oldValue:any) => {
    renderChart(props.reportAnalysisData);
  })
  watch(() => props.chartType,(newValue:any,oldValue:any) => {
    renderChart(props.reportAnalysisData);
  })
  onMounted(() => {
    if (!props.reportAnalysisData) {
      return
    }
    renderChart(props.reportAnalysisData)
  })
  
  </script>
  
  <style scoped lang="less">
  .chart-content {
    position: relative;
    width: 100%;
    height: 100%;
  }
  </style>
  