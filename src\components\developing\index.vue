<template>
  <div class="content">
    <img src="/sketchy/1.png" class="img" alt="">
    <div class="operation-row">
      <a-button key="back" type="primary" size="large" @click="back">{{ t($route.meta.locale || '') }}页面正在开发中... 返回首页</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {useRouter} from 'vue-router';
import {useI18n} from "vue-i18n";
import {DEFAULT_ROUTE_NAME} from "@/router/routes";

const {t} = useI18n();
const router = useRouter();
const back = () => {
  router.push({name: DEFAULT_ROUTE_NAME});
};
</script>

<style scoped lang="less">
.content {
  text-align: center;
}

.img{
  width: 20%;
  padding-top: 5%;
}

.operation-row {
  padding-top: 60px;
}
</style>
