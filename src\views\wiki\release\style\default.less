.page-body {
  flex-direction: row !important;

  .empty {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  :deep(.arco-anchor-link) {
    padding-bottom: 10px;
  }

  #scroll-container {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding-left: 24px;

    .timeline-item {
      padding-bottom: 10px;
      padding-left: 8px;
      min-height: auto;

      .line {
        display: flex;
        align-items: center;
        padding-bottom: 4px;

        .version-code {
          margin-right: 10px;
          font-size: 16px;
          font-weight: bold;
        }

        .time {
          font-size: 14px;
          color: var(--tant-text-gray-color-text1-3)
        }

        .edit-btn {
          visibility: hidden;
          margin-left: 10px;

          &:hover {
            color: var(--tant-primary-color-primary-default);
          }

          cursor: pointer;
        }
      }
    }
  }

  #scroll-container:hover {
    .timeline-item:hover .edit-btn {
      visibility: visible;
    }
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;

  button {
    border-radius: 4px;
  }

  .cancel {
    background: transparent;
    margin-right: 8px;

    &:hover {
      background-color: var(--color-secondary);
    }
  }
}
