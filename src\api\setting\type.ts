
export interface tableDateConfig {
    eventCode: string,
    eventDisplayName: string,
    eventName: string,
    eventNote: string,
    eventStatus: string,
    eventType: number,
    eventVisibility: boolean,
    yesterdayCount: number,
}
export interface EventAttrConfig{
    code:string,
    name?:string,
    dataType:string,
    displayName:string,
    type:string,
    description:string,
    note:string,
    realAvailable:boolean,
    status:number,
    visibility:boolean,
    unit:string,
}
export interface UserAttrConfig{
    code:string,
    name?:string,
    dataType:string,
    displayName:string,
    type:string,
    description:string,
    note:string,
    realAvailable:boolean,
    status:number,
    visibility:boolean,
    unit:string,
}