<template>
  <div>
    <a-drawer
        v-model:visible="visible"
        :width="340"
        placement="left"
        :header="true"
        :mask-closable="true">
      <template #header>
        <div class="title">
          <span class="toptitle">看板管理</span>
          <div @click="handleCancel">
            <icon-double-left/>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="button">
          <a-checkbox v-model="allChecked" @click="toggleChecked">全选</a-checkbox>
          <a-space>
            <a-button class="cancel" @click="handleCancel">取消</a-button>
            <a-button
                v-if="projectPositioncheckKeys?.length === 0&&myPositioncheckedKeys?.length===0" disabled
                class="remove">
              移动至
            </a-button>
            <a-button v-else class="remove" @click="handleMove">移动至</a-button>
            <a-button
                v-if="projectPositioncheckKeys?.length === 0&&myPositioncheckedKeys?.length===0"
                type="primary"
                status="danger"
                disabled
            >
              删除
            </a-button>
            <a-button v-else class="delete" @click="handleDelete">删除</a-button>
          </a-space>
        </div>
      </template>
      <div class="content-container">
        <a-input v-model="searchQuery" placeholder="搜索看板" style="border: none">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
        <div v-if="filteredMyPosition?.length > 0">
          <div class="title">我的看板</div>
          <a-tree
              v-model:checked-keys="myPositioncheckedKeys"
              :block-node="true"
              :checkable="true"
              :default-expand-all="false"
              checked-strategy="child"
              :only-check-leaf="true"
              :data="filteredMyPosition"
              class="projectTree"
              @check="onMyPositionCheck"
              @expand="SpaceExpand"
          />
        </div>
        <div v-if="filteredProjectPosition?.length > 0">
          <div class="title">项目空间</div>
          <a-tree
              ref="treeModelRef"
              v-model:checked-keys="projectPositioncheckKeys"
              :block-node="true"
              :default-expand-all="false"
              :checkable="true"
              checked-strategy="child"
              :only-check-leaf="true"
              :data="filteredProjectPosition"
              class="projectTree"
              @check="onprojectPositionCheck"
              @expand="projectSpaceExpand"
          />
        </div>
        <div v-if="filteredMyPosition?.length === 0 && filteredProjectPosition?.length === 0" class="no-results">
          无匹配结果
        </div>
      </div>
    </a-drawer>

    <!--  移动弹窗-->

    <a-modal
        :visible="visibles"
        :mask-closable="false"
        :width="392"
        title-align="start"
        ok-text="移动"
        title="移动至"
        @ok="handleOk"
        @cancel="handleCancels">

      <a-row class="item" align="center">

        <a-col :span="24">
          <a-radio-group
              type="button" :model-value="dashboardPosition"
              @change="value => {dashboardPosition=value}">
            <a-radio value="my">我的看板</a-radio>
            <a-radio value="other">项目空间</a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
      <a-row v-if="dashboardPosition==='my'" class="item" align="center">

        <a-col :span="24">
          <a-tree-select
              v-model="myPositionSelected"
              :data="firstTwoLevelsMyPosition"
              placeholder="请选择添加位置">
            <template #label="{ data }">
              <div class="space-select-option">
                <img class="icon" src="/icon/folder.svg" alt=""/>
                <div class="label">
                  {{ data.label }}
                </div>
              </div>
            </template>
          </a-tree-select>
        </a-col>
      </a-row>
      <a-row v-if=" dashboardPosition==='other'" class="item" align="center">
        <a-col :span="24">
          <a-tree-select
              v-model="projectPositionSelected"
              :data="firstTwoLevelsProjectPosition"
              placeholder="请选择添加位置">
            <template #label="{ data }">
              <div class="space-select-option">
                <img
                    v-if="projectPositionSelected?.split('-')[0]==='space'" class="icon" src="/icon/cube-open.svg"
                    alt=""/>
                <img v-else class="icon" src="/icon/folder.svg" alt=""/>
                <div class="label">
                  {{ data.label }}
                </div>
              </div>
            </template>
          </a-tree-select>
        </a-col>
      </a-row>
      <div class="prompt">* 仅移动选中的看板，不移动文件夹</div>
    </a-modal>


    <!--  删除-->
    <a-modal
        v-model:visible="visibleDelete"
        :width="320"
        title-align="start"
        ok-text="删除"
    >
      <template #title> 删除看板</template>
      <div class="prompt">确认删除选中的看板吗？ 该操作不可恢复。</div>
      <template #footer>
        <a-button @click="handleDeleteCancels">取消</a-button>
        <a-button type="primary" status="danger" @click="handleDeleteOk">删除</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {computed, defineEmits, defineProps, h, ref, watch} from 'vue';
import {SpaceDto} from "@/api/space/type";
import cubeImg from '/public/icon/cube.svg';
import cubeOpenImg from '/public/icon/cube-open.svg';
import folderImg from '/public/icon/folder.svg';
import folderOpenImg from '/public/icon/folder-open.svg';
import fileImg from '/public/icon/file.svg'
import {Message, TreeNodeData} from "@arco-design/web-vue";
import {moveDashboard, removeDashboard} from "@/api/dashboard/api";
import {useEventBus} from "@vueuse/core";
import {RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {useDashboardStore} from "@/store";


interface Props {

  /**
   * 我的看板
   */
  mySpace: SpaceDto

  /**
   * 项目空间
   */
  projectSpaces: SpaceDto[]
}

const props = defineProps<Props>();
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const projectOpenKey = ref<string[]>([]);
const spaceOpenKey = ref<string[]>([]);
// 搜索框的值
const dashboardStore = useDashboardStore()
const searchQuery = ref<string>('');
const SpaceExpand = (key) => {
  spaceOpenKey.value = key;
}
const projectSpaceExpand = (key) => {
  projectOpenKey.value = key;
}
// 搜索框过滤
// ismatch
function isMatch(searchTerm: string, text: string): boolean {
  let i = 0;
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();
  // eslint-disable-next-line no-restricted-syntax
  for (const char of lowerSearchTerm) {
    i = lowerText.indexOf(char, i);
    if (i === -1) {
      return false;
    }
    // eslint-disable-next-line no-plusplus
    i++;
  }
  return true;
}

// 过滤树节点的函数
function filterNodes(nodes: TreeNodeData[], searchQuery: string): TreeNodeData[] {
  if (!searchQuery) return nodes;

  function filterNode(node: TreeNodeData): TreeNodeData | null {
    // 检查当前节点标题是否匹配
    const titleMatches = isMatch(searchQuery, node.title || '');
    // 检查子节点
    const filteredChildren = node.children
        ? node.children.map(child => filterNode(child)).filter(child => child !== null) as TreeNodeData[]
        : [];

    // 如果当前节点或其子节点匹配，则返回当前节点
    if (titleMatches || filteredChildren.length > 0) {
      return {
        ...node,
        children: filteredChildren, // 确保 children 被正确赋值
      };
    }
    return null;
  }

  return nodes?.map(node => filterNode(node)).filter(node => node !== null) as TreeNodeData[];
}


/**
 * 项目空间树
 */
const projectPosition = ref()
/**
 * 我的看板树
 */
const myPosition = ref();
// watch(()=>dashboardStore.spaceList,(newVal)=>{
//   =newVal.filter(space=>space.authority !== ObjectPermissionType.OWNER);
// })

watch(() => props.projectSpaces, (newData) => {
  projectPosition.value = newData?.map(space => {
    // 处理空间的 dashboards
    const dashboards = space.dashboards?.map(dashboard => {
      return {
        'key': dashboard.dashboardId,
        'title': dashboard.name,
        'icon': () => h(fileImg),
        'disabled': space.name === 'DEMO空间',
      };
    }) || [];

    return {
      'key': `${space.spaceId}`,
      'title': space.name,
      'icon': () => {
        return projectOpenKey.value.includes(space.spaceId) ? h(cubeOpenImg) : h(cubeImg);
      },
      'disabled': space.name === 'DEMO空间' ||
          (!space.folders?.map(folder => folder.dashboards?.length === 0).includes(false) && space.dashboards?.length === 0),// 禁用 DEMO 空间
      'children': [
        ...space.folders?.map(folder => {
          const folderDashboards = folder.dashboards?.map(dashboard => {
            return {
              'key': dashboard.dashboardId,
              'title': dashboard.name,
              'icon': () => h(fileImg),
              'disabled': space.name === 'DEMO空间',
            };
          }) || [];

          return {
            'key': `${folder.folderId}`,
            'title': folder.name,
            'icon': () => {
              return projectOpenKey.value.includes(folder.folderId) ? h(folderOpenImg) : h(folderImg);
            },
            'disabled': folderDashboards.length === 0 || space.name === 'DEMO空间', // 如果没有子节点，禁用父节点
            'children': folderDashboards
          };
        }) || [],
        ...dashboards // 添加 space 直接下的 dashboards
      ]
    } as TreeNodeData;
  }) || [];
}, {deep: true})

/**
 * 我的看板树
 */
watch(() => props.mySpace, (newData) => {
  myPosition.value = newData?.folders?.filter(folder => folder?.allowAdd !== false).map(folder => {
    const children = folder.dashboards?.map(dashboard => {
      return {
        'key': `${dashboard.dashboardId}`,
        'title': dashboard.name,
        'icon': () => h(fileImg)
      }
    }) || [];

    return {
      'key': folder.folderId,
      'title': folder.name,
      'icon': () => {
        return spaceOpenKey.value.includes(folder.folderId) ? h(folderOpenImg) : h(folderImg);
      },
      'children': children,
      'disabled': children.length === 0  // 如果没有子节点，则禁用父节点
    } as TreeNodeData;
  }) || []
}, {deep: true})

// 过滤后的项目空间树
const filteredProjectPosition = computed(() => filterNodes(projectPosition.value, searchQuery.value));

// 过滤后的我的看板树
const filteredMyPosition = computed(() => filterNodes(myPosition.value, searchQuery.value));
// 移动-弹窗
const dashboardPosition = ref<string>('my');
const projectPositionSelected = ref<string>();
const myPositionSelected = ref<string>();
// checkbox索引
const myPositioncheckedKeys = ref<string[]>([]);
const projectPositioncheckKeys = ref<string[]>([]);

// 全选判断
const myPositionChecked = ref(false);
const projectPositionChecked = ref(false);
const allChecked = ref(false);
// 移动弹窗visible
const visibles = ref(false);
const visibleDelete = ref(false)
// 移动弹窗数据树
const firstTwoLevelsMyPosition = computed(() => keepOnlyParentNodes(myPosition.value));
const firstTwoLevelsProjectPosition = computed(() => keepFirstTwoLevels(projectPosition.value));
const treeModelRef = ref(null)

/**
 *check统计函数
 */
function getTotalNodesLength(nodes: TreeNodeData[]): number {
  let count = 0;

  function traverse(nodeList: TreeNodeData[]) {
    nodeList.forEach(node => {
      if (!node.disabled) { // 仅计算启用的节点
        count++;
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  }

  traverse(nodes);
  return count;
}

const projectPositionLength = computed(() => getTotalNodesLength(projectPosition.value));
const myPositionLength = computed(() => getTotalNodesLength(myPosition.value))


// 点击全选
function toggleChecked() {
  if (!allChecked.value) {
    // 全选时排除禁用节点
    myPositioncheckedKeys.value = myPosition.value.flatMap(folder =>
        folder.disabled ? [] : [folder.key, ...folder.children?.filter(child => !child.disabled).map(child => child.key) || []]
    );

    projectPositioncheckKeys.value = projectPosition.value.flatMap(space =>
        space.disabled ? [] : [
          space.key,
          ...space.children?.flatMap(folder =>
              folder.disabled ? [] : [folder.key, ...folder.children?.filter(child => !child.disabled).map(child => child.key) || []]
          ) || []
        ]
    );
  } else {
    // 取消全选
    myPositioncheckedKeys.value = [];
    projectPositioncheckKeys.value = [];
  }
}

// 判断是否全选
function onMyPositionCheck(newCheckedKeys, event) {

  if (newCheckedKeys.length === 0 && myPositionLength.value === 0 || newCheckedKeys.length !== myPositionLength.value) {
    myPositionChecked.value = false
  } else if (newCheckedKeys.length === myPositionLength.value) {
    myPositionChecked.value = true
  }
}

function onprojectPositionCheck(newCheckedKeys, event) {
  if (newCheckedKeys.length === 0 && projectPositionLength.value || newCheckedKeys.length !== projectPositionLength.value) {
    projectPositionChecked.value = false
  } else if (newCheckedKeys.length === projectPositionLength.value) {
    projectPositionChecked.value = true
  }
}

// 处理树数据以保留前两级节点
function keepFirstTwoLevels(nodes: TreeNodeData[]): TreeNodeData[] {
  console.log("nodes", nodes)
  return nodes?.map(node => {
    const children = node.children ? node.children.map(child => {
      const grandChildren = child.children ? [] : undefined;
      const isDisabled = !child.children
      return {
        key: child.key,
        title: child.title,
        icon: child.icon,
        disabled: isDisabled,
        children: grandChildren
      };
    }) : [];
    return {
      key: node.key,
      title: node.title,
      icon: node.icon,
      children
    };
  });
}

// 处理树数据以保留仅父节点
function keepOnlyParentNodes(nodes: TreeNodeData[]): TreeNodeData[] {
  return nodes?.map(node => ({
    key: node.key,
    title: node.title,
    icon: node.icon,
    children: undefined // 不包含任何子节点
  }));
}

watch([myPositionChecked, projectPositionChecked], ([new1, new2]) => {
  allChecked.value = (new1 && new2)
})
const visible = defineModel<boolean>("visible", {default: false});
const emit = defineEmits(['cancelDrawer']);

function handleCancel() {
  emit('cancelDrawer');
}

//   移动弹窗
const handleMove = () => {
  visibles.value = true;
};
const handleOk = () => {
  let list
  if (dashboardPosition.value === 'my') {
    const myKeys = myPositioncheckedKeys.value.map(item => {
      return {
        spaceId: props.mySpace.spaceId,
        folderId: myPositionSelected.value,
        dashboardId: item
      }
    })
    const projectKeys = projectPositioncheckKeys.value.map(item => {
      return {
        spaceId: props.mySpace.spaceId,
        folderId: myPositionSelected.value,
        dashboardId: item
      }
    })
    list = [
      ...myKeys,
      ...projectKeys
    ]
  }
  if (dashboardPosition.value === 'other') {
    const selectProjectSpace = props.projectSpaces.find(space =>
        space.folders?.some(folder => folder.folderId === projectPositionSelected.value)
    );
    const myKeys = myPositioncheckedKeys.value.map(item => {
      return {
        spaceId: selectProjectSpace?.spaceId,
        folderId: projectPositionSelected.value,
        dashboardId: item
      }
    })
    const projectKeys = projectPositioncheckKeys.value.map(item => {
      return {
        spaceId: selectProjectSpace?.spaceId,
        folderId: projectPositionSelected.value,
        dashboardId: item
      }
    })
    list = [
      ...myKeys,
      ...projectKeys
    ]
  }
  moveDashboard(list)
      .then((resp) => {
        treeMenuEventBus.emit(RefreshEvent);
      })
      .catch((e) => {
        Message.error("移动失败！", e);
      });
  visibles.value = false;
};

const handleCancels = () => {
  visibles.value = false;
};

//   删除
const handleDelete = () => {
  visibleDelete.value = true;
};
const handleDeleteOk = () => {
  if (myPositioncheckedKeys.value || projectPositioncheckKeys.value) {
    removeDashboard([...myPositioncheckedKeys.value, ...projectPositioncheckKeys.value]).then((resp) => {
      treeMenuEventBus.emit(RefreshEvent)
    }).catch((e) => {
      Message.error("删除看板失败！", e)
    })
  }
  visibleDelete.value = false;
};
const handleDeleteCancels = () => {
  visibleDelete.value = false;
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10px;
}

.toptitle {
  font-size: var(--font-size-title-small);
  font-weight: 500;
  color: #212121;
}

.title {
  margin-bottom: 10px;
}

.arco-tree :deep(.arco-tree-node) {
  height: 40px;
}

.arco-tree :deep(.arco-tree-node):hover {
  background-color: #f6f6f9;
}

.button {
  display: flex;
  justify-content: space-between;
}

.cancel {
  &:hover {
    background-color: #f6f6f9;
  }
}

.arco-btn-secondary[type='button'].remove {
  background-color: #f6f6f9;
}

.arco-btn-secondary[type='button'].delete {
  background-color: #e84d37;
  color: #fff;
}

:deep(.arco-modal-body) {
  padding: 0;
}

.item {
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  padding-bottom: 16px;

  .label {
    color: var(--tant-text-gray-color-text1-1);
    font: var(--tant-body-font-body-regular);
    text-align: right;
  }

  .no-results {
    position: absolute;
    left: 40%;
    top: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--tant-text-gray-color-text1-3);
    font: var(--tant-description-font-description-regular);
  }
}

.space-select-option {
  display: flex;
  align-items: center;

  .icon {
    height: 14px;
    width: 14px;
    margin-bottom: 2px;
    margin-right: 4px;
  }
}

.projectTree :deep(.arco-tree-node-selected .arco-tree-node-title) {
  color: var(--tant-text-gray-color-text1-1);
}
</style>