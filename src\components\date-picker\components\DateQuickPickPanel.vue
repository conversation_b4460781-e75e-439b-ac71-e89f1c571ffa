<template>
  <div class="pick-panel">
    <div class="button-group">
      <a-button class="button-item" :class="{ 'active': dateRangeText === '昨日' }" @click="selectRange('昨日')">昨日</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '今日' }" @click="selectRange('今日')">今日</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '上周' }" @click="selectRange('上周')">上周</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '本周' }" @click="selectRange('本周')">本周</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '上月' }" @click="selectRange('上月')">上月</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '本月' }" @click="selectRange('本月')">本月</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去7天' }" @click="selectRange('过去7天')">过去7天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近7天' }" @click="selectRange('最近7天')">最近7天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去14天' }" @click="selectRange('过去14天')">过去14天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近14天' }" @click="selectRange('最近14天')">最近14天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去30天' }" @click="selectRange('过去30天')">过去30天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近30天' }" @click="selectRange('最近30天')">最近30天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去60天' }" @click="selectRange('过去60天')">过去60天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近60天' }" @click="selectRange('最近60天')">最近60天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去90天' }" @click="selectRange('过去90天')">过去90天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近90天' }" @click="selectRange('最近90天')">最近90天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '过去180天' }" @click="selectRange('过去180天')">过去180天</a-button>
      <a-button class="button-item" :class="{ 'active': dateRangeText === '最近180天' }" @click="selectRange('最近180天')">最近180天</a-button>
    </div>
    <a-divider margin="5px"/>
    <a-button class="button-item-bigger" :class="{ 'active': dateRangeText === '自某日至昨日' }" @click="selectRange('自某日至昨日')">自某日至昨日</a-button>
    <a-button class="button-item-bigger" :class="{ 'active': dateRangeText === '自某日至今' }" @click="selectRange('自某日至今')">自某日至今</a-button>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import {ref, watch} from "vue";
import {DateRange} from "@/api/analyse/type";
import _ from "lodash";
import {dateStaticToDynamic} from "@/utils/dateUtil";

const dateRange = defineModel<DateRange>("dateRange", {default: {}});
const dateRangeText = ref<string>();

const emits = defineEmits(['quickPick']);

/**
 * 监听日期范围
 */
watch(() => dateRange.value, (newValue) => {
  if (newValue) {
    const {dateText, recentEndDate, startDate} = newValue
    if (!_.isEmpty(startDate) && recentEndDate === 0) {
      dateRangeText.value = '自某日至今'
      return
    }

    if (!_.isEmpty(startDate) && recentEndDate === 1) {
      dateRangeText.value = '自某日至昨日'
      return
    }

    dateRangeText.value = dateText
  }
})


/**
 * 选择日期范围
 */
const selectRange = (rangeText) => {
  const now = dayjs(dayjs().format('YYYY-MM-DD'));
  const newDateRange: DateRange = {}
  switch (rangeText) {
    case '昨日':
      newDateRange.recentStartDate = 1
      newDateRange.recentEndDate = 1
      break;
    case '今日':
      newDateRange.recentStartDate = 0
      newDateRange.recentEndDate = 0
      break;
    case '上周':
      newDateRange.recentStartDate = dateStaticToDynamic(now.subtract(1, 'week').startOf('week'));
      newDateRange.recentEndDate = dateStaticToDynamic(now.subtract(1, 'week').endOf('week'));
      break;
    case '本周':
      newDateRange.recentStartDate = dateStaticToDynamic(now.startOf('week'));
      newDateRange.recentEndDate = dateStaticToDynamic(now.endOf('week'));
      break;
    case '上月':
      newDateRange.recentStartDate = dateStaticToDynamic(now.subtract(1, 'month').startOf('month'));
      newDateRange.recentEndDate = dateStaticToDynamic(now.subtract(1, 'month').endOf('month'));
      break;
    case '本月':
      newDateRange.recentStartDate = dateStaticToDynamic(now.startOf('month'));
      newDateRange.recentEndDate = dateStaticToDynamic(now.endOf('month'));
      break;
    case '最近7天':
      newDateRange.recentStartDate = 6
      newDateRange.recentEndDate = 0
      break;
    case '过去7天':
      newDateRange.recentStartDate = 7
      newDateRange.recentEndDate = 1
      break;
    case '最近14天':
      newDateRange.recentStartDate = 13
      newDateRange.recentEndDate = 0
      break;
    case '过去14天':
      newDateRange.recentStartDate = 14
      newDateRange.recentEndDate = 1
      break;
    case '过去30天':
      newDateRange.recentStartDate = 30
      newDateRange.recentEndDate = 1
      break;
    case '最近30天':
      newDateRange.recentStartDate = 29
      newDateRange.recentEndDate = 0
      break;
    case '过去60天':
      newDateRange.recentStartDate = 60
      newDateRange.recentEndDate = 1
      break;
    case '最近60天':
      newDateRange.recentStartDate = 59
      newDateRange.recentEndDate = 0
      break;
    case '过去90天':
      newDateRange.recentStartDate = 90
      newDateRange.recentEndDate = 1
      break;
    case '最近90天':
      newDateRange.recentStartDate = 89
      newDateRange.recentEndDate = 0
      break;
    case '过去180天':
      newDateRange.recentStartDate = 180
      newDateRange.recentEndDate = 1
      break;
    case '最近180天':
      newDateRange.recentStartDate = 179
      newDateRange.recentEndDate = 0
      break;
    case '自某日至今':
      newDateRange.startDate = dateRange.value.startDate
      newDateRange.recentStartDate = undefined
      newDateRange.recentEndDate = 0
      break;
    case '自某日至昨日':
      newDateRange.startDate = dateRange.value.startDate
      newDateRange.recentStartDate = undefined
      newDateRange.recentEndDate = 1
      break;
    default:
      break;
  }
  dateRange.value = newDateRange;
  emits('quickPick', newDateRange)
}

</script>

<style scoped lang="less">
.button-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  float: left;
  width: 100%;
  margin-top: 10px;

  .button-item {
    margin-bottom: 10px;
    text-align: center;
    width: 72px;
    height: 28px;
    font-size: 12px;
    display: flex;
    border-radius: 10px;
    cursor: pointer;
    color: var(--tant-text-gray-color-text1-1);
    background-color: var(--tant-bg-white-color-bg1-1);
  }

  .button-item:focus {
    background-color: dodgerblue;
    color: white;
    outline: none;
  }
  .active {
    background-color: dodgerblue;
    color: white;
    outline: none;
  }
}

.button-item-bigger {
  text-align: center;
  width: 154px;
  height: 28px;
  font-size: 12px;
  display: flex;
  border-radius: 10px;
  cursor: pointer;
  color: var(--tant-text-gray-color-text1-1);
  background-color: var(--tant-bg-white-color-bg1-1);
}

.active {
  background-color: dodgerblue;
  color: white;
  outline: none;
}
</style>
