<template>
    <a-spin :loading="dataLoading" class="info-content">
        <a-form ref="formRef" :model="form" :rules="rules" label-align="left" style="width: 100%;">
            <div class="form-label">
                <div class="title">实验版本参数配置</div>
                <div class="extra"></div>
            </div>
            <a-form-item field="associatedRemote" label="是否关联远程配置" tooltip="可在下方输入框输入远程配置的名称，搜索已创建的远程配置，并按需将该实验关联至远程配置">
                <a-radio-group v-model:model-value="form.associatedRemote" @change="associatedChange">
                    <a-radio :value="0">不关联</a-radio>
                    <a-radio :value="1">关联</a-radio>
                </a-radio-group>
            </a-form-item>
            <a-form-item v-if="form.associatedRemote" field="remoteConfigCode" label="关联的远程配置">
                <a-select
                    v-model:model-value="form.remoteConfigCode"
                    placeholder="可以通过输入远程配置的名称，搜索已创建的远程配置，默认最多展示20条"
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                     :filter-option="false"
                     allow-search
                    @search="handleSearch"
                    @change="handleChange">
                    <a-option v-for="item in remoteList" :key="item.name" :value="item.name">
                        {{ item.name }}
                    </a-option>
                </a-select>
            </a-form-item>
            <!-- 实验版本卡片 -->
            <a-form-item field="domains" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
                <div class="domains-content" style="width: 100%">
                    <div v-for="(item,index) in versionList" :key="index" class="domain-item">
                        <div class="head">
                            <div class="justify" style="height: 100%;">
                                <div class="content">
                                    <a-space>
                                        <icon-up v-if="item.showContent" class="icon-style" @click="() => item.showContent = false"/>
                                        <icon-down v-else class="icon-style" @click="() => item.showContent = true"/>
                                        <span v-if="!item.showNameInput">{{ item.name }}</span>
                                        <a-input v-else v-model="item.name" max-length="50" style="width: 100px;" @blur="item.showNameInput = false"/>
                                        <icon-edit class="icon-style" @click="() => item.showNameInput = !item.showNameInput"/>
                                    </a-space>
                                </div>
                                <div class="extra">
                                    <icon-copy v-if="versionList.length>1" style="margin-right: 8px;cursor: pointer" @click="copyVersion(index)"/>
                                    <a-popconfirm content="确认删除吗?" @ok="deleteList(item,index)">
                                        <icon-delete v-if="versionList.length>1" style="cursor: pointer"/>
                                    </a-popconfirm>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.showContent" class="content-info">
                            <div class="content-box">
                                <div class="item-left">
                                    <a-form-item field="type" label="版本类型">
                                        <a-radio-group v-model:model-value="item.type" @change="versionTypeChange">
                                            <a-radio :value="0">对照组</a-radio>
                                            <a-radio :value="1">实验组</a-radio>
                                        </a-radio-group>
                                    </a-form-item>
                                    <a-form-item field="description" label="描述">
                                        <a-textarea v-model="item.description"/>
                                    </a-form-item>
                                    <a-form-item field="testUserCodes" label="测试用户">
                                        <a-textarea v-model="item.testUserCodes" placeholder="多个用户请用,逗号分割"/>
                                    </a-form-item>
                                    <a-form-item field="whitelistCodes" label="白名单">
                                        <a-select
                                            v-model:model-value="item.whitelistCodes"
                                            placeholder="请选择测试白名单"
                                            multiple
                                            allow-search
                                            :max-tag-count="2"
                                            :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                                            <a-option v-for="white in whiteList" :key="white.code" :value="white.code">
                                                {{ white.name }}
                                            </a-option>
                                        </a-select>
                                    </a-form-item>
                                </div>
                                <div class="item-right">
                                    <div style="color: var(--color-text-2);font-size: 14px;line-height: 32px;">参数配置</div>
                                    <div class="form-content">
                                        <div v-for="(config,configIndex) in item.configParams" :key="configIndex" class="config-box">
                                            <div class="wrapper">
                                                <a-select
                                                    v-model:model-value="config.name"
                                                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                                    allow-create
                                                    style="width: 180px;"
                                                    :disabled="form.associatedRemote ===1"
                                                    @change="nameChange(configIndex,$event)">
                                                    <a-option v-for="remote in remoteList" :key="remote.name" :value="remote.name">
                                                        {{ remote.name }}
                                                    </a-option>
                                                </a-select>
                                            </div>
                                            <div class="wrapper">
                                                <a-select
                                                    v-model:model-value="config.dataType"
                                                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                                    style="width: 120px;"
                                                    :disabled="form.associatedRemote ===1"
                                                    @change="dataTypeChange(configIndex,$event)">
                                                    <a-option v-for="type in dataTypeList" :key="type.value" :value="type.value">
                                                        {{ type.label }}
                                                    </a-option>
                                                </a-select>
                                            </div>
                                            <div class="wrapper flex1">
                                                <a-input v-if="config.dataType === 'string'" v-model="config.value"/>
                                                <a-input-number
                                                    v-if="config.dataType === 'int'"
                                                    v-model="config.value"
                                                    :min="0"
                                                    :max="9999999999"
                                                    :step="1"
                                                    :precision="0"
                                                />
                                                <a-input-number
                                                    v-if="config.dataType === 'float'"
                                                    v-model="config.value"
                                                    :min="0"
                                                    :max="9999999999"
                                                    :step="0.00001"
                                                    :precision="5"
                                                />
                                                <!-- <a-input-number v-if="config.dataType === 'int' || config.dataType === 'float'" v-model="config.value" :step="1" :precision="0"/> -->
                                                <a-select
                                                    v-if="config.dataType === 'boolean'"
                                                    v-model:model-value="config.value"
                                                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                                                    <a-option value="true">True</a-option>
                                                    <a-option value="false">False</a-option>
                                                </a-select>
                                                <a-input v-if="config.dataType === 'json'" v-model="config.value"/>
                                                <div v-if="config.dataType === 'json'" class="globalEdit" @click="openJsonModal(index,configIndex)">
                                                    <a-tooltip content="全局编辑器">
                                                        <icon-expand />
                                                    </a-tooltip>
                                                </div>
                                            </div>
                                            <div v-if="item.configParams.length >1" class="delete-icon" @click="deleteConfigParam(configIndex)">
                                                <icon-delete/>
                                            </div>
                                        </div>
                                        <div v-if="form.associatedRemote ===0" class="ta-filter-button" @click="addConfigParam">
                                            <div class="ta-filter-button-icon">
                                                <icon-plus/>
                                            </div>
                                            添加配置参数
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: left;">
                        <a-button type="outline" style="border-radius: 4px;" @click="addList">
                            <template #icon>
                                <icon-plus/>
                            </template>
                            添加实验版本
                        </a-button>
                        <div v-if="validateText" class="error-text">{{ validateText }}</div>
                    </div>
                </div>
            </a-form-item>
            <div class="form-label">
                <div class="title">流量分配</div>
                <div class="extra">
                    <span style="color: #8b8ba6;">流量均匀分配：</span>
                    <a-switch v-model:model-value="isEvenDistribution" @change="handleDistributionChange"/>
                </div>
            </div>
            <a-form-item field="ratios" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }" validate-trigger="change">
                <a-table :key="tableKey" :columns="columns" :data="tableData" :pagination="false" :style="{ width: '100%' }">
                    <template #flowPercentage="{ record,rowIndex }">
                        <a-input-number
                            v-model="record.flowPercentage"
                            :min="0"
                            :max="100"
                            :step="0.1"
                            :precision="1"
                            :disabled="isEvenDistribution || rowIndex === tableData.length - 1"
                            :style="{ width: '120px' }"
                            @change="(value) => handleRatioChange(value, rowIndex)"
                        >
                            <template #suffix>%</template>
                        </a-input-number>
                    </template>
                    <template #actualFlow="{ record }">
                        {{ `${record.flowPercentage}% 实验流量 * 流量分配占比` }}
                    </template>
                </a-table>
            </a-form-item>
            <a-form-item label="" :label-col-props="{ span: 0 }">
                <a-button style="border-radius: 4px;" :loading="loading" @click="backCurrent">
                    上一步
                </a-button>
                <a-button
                    type="primary"
                    style="border-radius: 4px;margin: 0 24px;"
                    :loading="loading"
                    @click="handleSubmit">
                    下一步
                </a-button>
                <a-button style="border-radius: 4px;" :loading="loading" @click="cancleEdit">
                    取消
                </a-button>
            </a-form-item>
        </a-form>
        <JsonEditModal ref="jsonRef" @update-json="updateJson"/>
    </a-spin>
</template>

<script setup lang="ts">
import {computed, reactive, ref} from "vue";
import {useSessionStorage} from "@vueuse/core";
import {getConfigParamList} from "@/api/marketing/api";
import {getExperimentDetail, getUserWhiteList, saveExperiment} from "@/api/ab/api";
import JsonEditModal from "./JsonEditModal.vue";

const props = defineProps({
    code: {
        type: String,
        default: ''
    }
})
const appId = ref(useSessionStorage('app-id', '')?.value)
const isEvenDistribution = ref(false); // 是否开启均匀分配
const columns = [
    {
        title: '版本名称',
        dataIndex: 'name',
        width: 200,
        slotName: 'name'
    },
    {
        title: '实验流量',
        dataIndex: 'flow',
        width: 200,
        slotName: 'flow'
    },
    {
        title: '流量分配占比',
        dataIndex: 'flowPercentage',
        width: 200,
        slotName: 'flowPercentage'
    },
    {
        title: '实际生效流量',
        dataIndex: 'actualFlow',
        width: 200,
        slotName: 'actualFlow'
    }
];
const pageParams = reactive({
  current: 1,
  pageSize: 20,
  text: '',
})
// 版本数据
const versionList = ref<any>([])
// 白名单数据
const whiteList = ref<any>([])
// 表格数据
const tableData = computed(() => {
    return versionList.value.map((item, index) => ({
        key: String(index + 1),
        name: item.name,
        flow: '100%',
        flowPercentage: item.flowPercentage,
        actualFlow: ''
    }));
});
const form = reactive({
    associatedRemote:0,
    remoteConfigCode:'',
    ratios: computed(() => tableData.value),
    domains: versionList
})
const remoteList = ref<any>([])
const dataTypeList = [
    {
        value:'string',
        label:'字符串',
    },
    {
        value:'int',
        label:'整数',
    },
    {
        value:'float',
        label:'小数',
    },
    {
        value:'boolean',
        label:'布尔值',
    },
    {
        value:'json',
        label:'JSON',
    }
]
// const verifyStatus = ref(false)
const rules = {
    associatedRemote: [
        {
            required: true,
            message:'请选择是否关联Feature',
        }
    ],
    remoteConfigCode: [
        {
            required: true,
            message:'请选择远程配置',
        }
    ],
    ratios: [
        {
            required: true,
            validator: (value, cb) => {
                // return new Promise((resolve) => {
                //     const sum = value.reduce((acc, item) => acc + item.flowPercentage, 0);
                //     if (sum !== 100) {
                //         cb('流量分配总和应该等于100%');
                //     }
                //     if (value.some(item => item.flowPercentage < 0)) {
                //         cb('流量分配不能小于0%');
                //     }
                //     resolve(value);
                // });
                return new Promise((resolve) => {
                    const sum = value.reduce((acc, item) => acc + item.flowPercentage, 0);
                    if (Math.abs(Number(sum.toFixed(1)) - 100) > 0.1) {
                        cb('流量分配总和应该等于100%');
                    }
                    if (value.some(item => item.flowPercentage < 0)) {
                        cb('流量分配不能小于0%');
                    }
                    resolve(value);
                });
            }
        }
    ],
}

const formRef = ref();

// 获取参数配置列表
const getList = async () => {
  const data = {
    appId:appId.value,
    ...pageParams
  }
  try{
    await getConfigParamList(data).then(res => {
      remoteList.value = res.items
    })
  }catch (error) {
    console.error('失败:', error);
  }
}
const handleSearch = (value) => {
    pageParams.text  = value
    getList()
}
const associatedChange = (value) => {
    if(value === 0){
        form.remoteConfigCode = ''
    }
}
const handleChange = (value) => {
    const matchedRemote = remoteList.value.find(remote => remote.name === value);
    versionList.value.forEach(version => {
        version.configParams.forEach(config => {
            config.name = value;
            config.dataType = matchedRemote?.valueType ||'string';
            config.value = matchedRemote?.conditions?.[0]?.value;
        });
    });
    
}
// 校验文字
const validateText = ref('')
// 校验版本类型
const validateType = () => {
    const hasControlGroup = versionList.value.some(item => item.type === 0);
    const hasExperimentGroup = versionList.value.some(item => item.type === 1);
    if (!hasControlGroup || !hasExperimentGroup) {
        validateText.value = '至少需要一个对照组或实验组';
        return false; // 校验失败
    }
    validateText.value = ''
    return true; // 校验成功
}

// 类型change
const versionTypeChange = () => {
    validateType()
}
// 校验版本配置
const validateConfigParams = () => {
    let isValid = true;
    versionList.value.forEach(version => {
        version.configParams.forEach(config => {
            if (form.associatedRemote === 1) {
                if (String(config.value) === '' || config.value === undefined || config.value === null) {
                    isValid = false;
                    validateText.value = '参数配置的值不能为空';
                }
            }
            if (form.associatedRemote === 0) {
                if (!config.name || String(config.value) === '' || config.value === undefined || config.value === null) {
                    isValid = false;
                    validateText.value = '参数配置的名称和值不能为空';
                }
            }
            
        });
    });
    if (isValid) {
        validateText.value = '';
    }
    return isValid;
};
const tableKey = ref(0)
 // 如果开启了均匀分配，需要重新计算所有版本的比例
 const evenDistributionComputed = () => {
    if (isEvenDistribution.value) {
        const evenRatio = 100 / versionList.value.length;
        const ratioValue = Number(evenRatio.toFixed(1));
        // 用新数组整体替换，确保响应式
        versionList.value = versionList.value.map(item => ({
            ...item,
            flowPercentage: ratioValue
        }));
    }
    tableKey.value++; // 强制刷新
    formRef.value.validate(['ratios']);
};
// 添加配置参数

const addConfigParam = () => {
    versionList.value.forEach(version => {
        version.configParams.push({
            name: '',
            dataType: 'string',
            value: ''
        });
    });
}
// 删除配置参数
const deleteConfigParam = (configIndex: number) => {
    versionList.value.forEach(version => {
        version.configParams.splice(configIndex, 1);
    });
}
const nameChange = (configIndex: number,value: any) => {
    // console.log(value,'value');
    const matchedRemote = remoteList.value.find(remote => remote.name === value);
    versionList.value.forEach(version => {
        version.configParams[configIndex].name = value
        version.configParams[configIndex].dataType = matchedRemote?.valueType || 'string'
        version.configParams[configIndex].value = matchedRemote?.conditions?.[0]?.value
    });
}
const dataTypeChange = (configIndex: number,value:any) => {
    versionList.value.forEach(version => {
        version.configParams[configIndex].dataType = value
        version.configParams[configIndex].value = undefined
    });
}
// 添加实验版本
const addList = () => {
    const lastVersion = versionList.value[versionList.value.length - 1];
    const newConfigParams = lastVersion ? JSON.parse(JSON.stringify(lastVersion.configParams)) : [{
        name: '',
        dataType: 'string',
        value: ''
    }];
    versionList.value.push({
        name: `版本${versionList.value.length + 1}`,
        type:1,
        description:'',
        showContent: true,
        showNameInput: false,
        flowPercentage: 0,
        whitelistCodes: [],
        testUserCodes:'',
        configParams:newConfigParams
    });
     // 如果开启了均匀分配，需要重新计算所有版本的比例
     evenDistributionComputed()
}
// 复制实验版本
const copyVersion = (index: number) => {
    const targetVersion = versionList.value[index];
    const copyItem = {
        ...targetVersion,
        code:'',
        name: `${targetVersion.name}-copy`,
        flowPercentage: 0,
        showContent: true,
        showNameInput: false
    };
    versionList.value.splice(index + 1, 0, copyItem);
    // 如果开启了均匀分配，需要重新计算所有版本的比例
    evenDistributionComputed()
};
const deleteList = async (data:any,index: number) => {
    versionList.value.splice(index, 1);
    if(versionList.value.length === 1){
        versionList.value[0].flowPercentage = 100
    }
    evenDistributionComputed()
}
// 处理均匀分配开关
const handleDistributionChange = (value) => {
    evenDistributionComputed()
};

// 处理比例变化
const handleRatioChange = (value: number, index: number) => {
    // 计算其他非禁用选项的总和
    const sum = versionList.value.reduce((acc, item, idx) => {
        if (idx !== versionList.value.length - 1) {
            return acc + (idx === index ? value : item.flowPercentage || 0);
        }
        return acc;
    }, 0);
    // 更新值
    if (sum <= 100) {
        versionList.value[index].flowPercentage = value;
        // 更新最后一个版本的 flowPercentage
        versionList.value[versionList.value.length - 1].flowPercentage = Number((100 - sum).toFixed(1));
    }
    // console.log(versionList.value,'111');
    formRef.value.validate(['ratios']);
};
const loading = ref(false)
const emits = defineEmits(['toNext','toPrev','cancleEdit'])
// 提交，下一步
const handleSubmit = async () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            if (!validateType() || !validateConfigParams()) {
                return; // 阻止提交
            }
            loading.value = true
            try {
                const versionData = versionList.value.map(item => {
                    return {
                        name:item.name,
                        type:item.type,
                        code:item.code,
                        experimentCode:props.code,
                        description:item.description,
                        whitelistCodes: item.whitelistCodes,
                        testUserCodes: item.testUserCodes ? item.testUserCodes.split(',').map(code => code.trim()).filter(Boolean) : [],
                        flowPercentage:item.flowPercentage,
                        configParams: item.configParams.map(config => {
                            let value = config.value;
                            if (
                                (config.dataType === 'int' || config.dataType === 'float') &&
                                typeof value === 'number'
                            ) {
                                value = value.toString();
                            }
                            return {
                                ...config,
                                name:config.name.trim(),
                                value
                            };
                        })
                    }
                })
                const params = {
                    code:props.code,
                    remoteConfigCode:form.remoteConfigCode,
                    variants:versionData,
                    remainEditStep: 1
                }
                await saveExperiment(params)
                emits('toNext')
            } catch (e) {
                // Message.error('保存失败，请重试')
                console.error(e)
            } finally {
                loading.value = false
            }
        }
    })
};
const cancleEdit = () => {
    emits('cancleEdit')
}
const getWhiteList = async () => { 
    try {
        const res = await getUserWhiteList()
        whiteList.value = res.filter(item => item.status === 1)
    } catch (e) {
        console.error(e)
    }
}
const dataLoading = ref(false)
const init = async () => {
    dataLoading.value = true
    try {
        await getList()
        getWhiteList()
        if(props.code){
            const res = await getExperimentDetail(props.code)
            form.remoteConfigCode = res?.remoteConfig?.name || ''
            form.associatedRemote = form.remoteConfigCode? 1 : 0
            if(res?.variants?.length){
                versionList.value = res.variants.map(item => {
                    return {
                        code:item.code,
                        name:item.name,
                        type:item.type,
                        description:item.description,
                        whitelistCodes: item.whitelistCodes,
                        testUserCodes:item.testUserCodes?.length ? item.testUserCodes.join(',') : '',
                        showContent:true,
                        showNameInput:false,
                        flowPercentage: item.flowPercentage,
                        configParams: item.configParams.map(config => {
                            let value = config.value;
                            if (config.dataType === 'int' && typeof value ==='string') {
                                value = parseInt(value)
                            }
                            if (config.dataType === 'float' && typeof value ==='string') {
                                value = parseFloat(value)
                            }
                            return {
                                ...config,
                                value
                            }
                        })
                    }
                })
            }else{
                versionList.value = [
                    {
                        name: '版本1',
                        type:0,
                        description:'',
                        whitelistCodes: [],
                        testUserCodes:'',
                        showContent: true,
                        showNameInput: false,
                        flowPercentage: 50,
                        configParams: [{
                            name: '',
                            dataType:'string',
                            value: ''
                        }]
                    },
                    {
                        name: '版本2',
                        type:1,
                        description:'',
                        whitelistCodes: [],
                        testUserCodes:'',
                        showContent: true,
                        showNameInput: false,
                        flowPercentage: 50,
                        configParams: [{
                            name: '',
                            dataType:'string',
                            value: ''
                        }]
                    }
                ]
            }
        }
    } catch (e) {
        console.error(e)
    } finally {
        dataLoading.value = false
    }
}
init()

// json编辑器
const jsonRef = ref()
// 卡片index
const versionIndex = ref(0)
// 参数值index
const valueIndex = ref(0)
const openJsonModal = (index:number,configIndex:number) => {
    versionIndex.value = index
    valueIndex.value = configIndex
    jsonRef.value.openModal(versionList.value[index].configParams[configIndex].value)
}
const updateJson = (val:any) => {
    versionList.value[versionIndex.value].configParams[valueIndex.value].value = val
}
const backCurrent = () => {
    emits('toPrev')
}
</script>

<style scoped lang="less">
.info-content{
  width: 100%;
  height: 100%;
}
.error-text{
    min-height: 20px;
    color: rgb(var(--danger-6));
    font-size: 12px;
    line-height: 20px;
}
.form-label{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .title{
        color: #141414e6;
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
    }
    .extra{
        margin-left: 16px;
    }
}
.domain-item{
    margin-bottom: 20px;
    overflow: hidden;
    .justify{
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow: hidden;
        width: 100%;
    }
    .icon-style{
        cursor: pointer;
    }
    .head{
        background-color: var(--color-fill-2);
        border-radius: 8px 8px 0 0;
        box-sizing: border-box;
        height: 40px;
        padding: 0 20px;
        .content{
            flex-grow: 1;
            flex-shrink: 1;
        }
        .extra {
            flex-shrink: 0;
        }
    }
    .content-info{
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: 0 0 8px 8px;
        border-top: none;
        overflow: hidden;
        .content-box{
            display: flex;
            .item-left{
                flex-basis: 40%;
                max-width: 600px;
                padding: 16px 20px 24px;
                width: 0;
            }
            .item-right{
                border-left: 1px solid var(--tant-border-color-border1-1);
                flex-basis: 60%;
                max-height: 960px;
                overflow-y: auto;
                padding: 16px 20px;
            }
        }
    }
}
.form-content{
    border-radius: 4px;
    display: block;
    min-height: 44px;
    flex-grow: 1;
    max-width: 100%;
    .config-box{
        display: flex;
        align-items: flex-start;
        .wrapper{
            margin-right: 8px;
            margin-bottom: 16px;
            position: relative;
        }
        .globalEdit{
            position: absolute;
            top: 0;
            right: 12px;
            height: 32px;
            width: 32px;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &:hover{
                background: #faf9f9;
            }
        }
        .flex1{
            flex: 1 1;
            max-width: 100%;
        }
        .delete-icon{
            flex-shrink: 0;
            cursor: pointer;
            visibility: visible;
            font-size: 18px;
            height: 32px;
            width: 32px;
            opacity: 0;
        }
        &:hover{
            .delete-icon{
                opacity: 1;
            }
        }
    }

}
.ta-filter-button {
    display: inline-flex;
    color: var(--tant-primary-color-primary-default);
    align-items: center;
    padding: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all .3s;

    .ta-filter-button-icon {
    background-color: #eaeefd;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    margin-right: 6px;
    }

    &:hover {
    background-color: var(--tant-bg-white-color-bg1-1);
    color: var(--tant-primary-color-primary-hover);
    }
}
.word {
    margin-right: 6px;
    color: var(--tant-text-gray-color-text1-2);
    font-weight: 400;
    font-size: 14px;
    flex-shrink: 0;
}

</style>