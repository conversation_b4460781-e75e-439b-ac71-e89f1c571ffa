

<template>
    <div class="pageCss">
      <div class="head">
        <h2>留存模型</h2>
      </div>
      <div class="body">
      </div>
    </div>
  </template>
  
  <script setup lang="ts"></script>
  
  <style scoped lang="less">
  .pageCss {
    width: 100%;
    height: calc(100vh - 108px);
    display: flex;
    flex-direction: column;
  
    .head {
      display: flex;
      min-height: 50px;
      align-items: center;
      justify-content: space-between;
  
      
    }
  
    .body {
      padding: 10px 10px 0 10px;
      height: 100%;
      width: 100%;
      background-color: var(--tant-bg-white-color-bg1-1);
  
    }
  }

  </style>