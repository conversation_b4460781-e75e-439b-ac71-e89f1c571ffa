<script setup lang="ts">
import draggable from "vuedraggable";
import {DashboardDto} from "@/api/dashboard/type";
import {moveDashboard} from "@/api/dashboard/api";
import {CacheEventBus, RefreshEvent, TreeMenuEventBus} from "@/types/event-bus";
import {Message} from "@arco-design/web-vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import {useDashboardStore} from "@/store";
import dashboardItem from "./item.vue";

interface Props {

  /**
   * 所属空间
   */
  spaceId?: string

  /**
   * 所属文件夹
   */
  folderId?: string
  allowAdd?:boolean | undefined
  /**
   * 所属空间名称
   */
   folderName?: string
  /**
   * 看板列表
   */
  dashboards?: DashboardDto[]
}
const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""})
const dashboardStore=useDashboardStore()
const treeMenuEventBus = useEventBus<string>(TreeMenuEventBus)
const cacheEventBus = useEventBus(CacheEventBus)
const props = defineProps<Props>()
const emit = defineEmits(['add']);
const draggableDashboard = (e) => {
  const index=e.newIndex;
const list=[{folderId:props.folderId,spaceId:props.spaceId, dashboardId: props.dashboards[index].dashboardId,order:index+1}]
  moveDashboard(list)
      .then((resp) => {
        cacheEventBus.emit('move-event', {
          type:'dashboard',
          dashboardId: props.dashboards[index].dashboardId,
          folderId:props.folderId,
          spaceId:props.spaceId,
          order:index
        });
      })
      .catch((e) => {
        Message.error("重命名失败！", e);
      });
}
const add = (e) => {
  const index=e.newIndex;
  const list=[{folderId:props.folderId,spaceId:props.spaceId, dashboardId: props.dashboards[index].dashboardId,order:index+1}]
  moveDashboard(list)
      .then((resp) => {
        if(props.dashboards[index].dashboardId===dashboardSelected.value.dashboardId){
          treeMenuEventBus.emit(RefreshEvent);
        }
        cacheEventBus.emit('move-event', {
          type:'dashboard',
          dashboardId: props.dashboards[index].dashboardId,
          folderId:props.folderId,
          spaceId:props.spaceId,
          order:index,
        });
      })
      .catch((e) => {
        Message.error("重命名失败！", e);
      });
}

</script>

<template>
  <draggable
      :disabled="dashboardStore.dashboardSearchStore"
      :list="dashboards || []"
      :group="props.folderName==='分享给我的'?'分享给我的':spaceId+'_dashboards'"
      item-key="dashboardId"
      @update="draggableDashboard"
      @add="add"
  >
    <template #item="{ element }">
      <dashboard-item
          :dashboard="element"
          :spaceId="spaceId"
          :folder-id="folderId"
          :allow-add="allowAdd"
          @add="(addType: string, spaceId?: string, folderId?: string)=>{emit('add',addType,spaceId,folderId)}"
          />
    </template>
  </draggable>
</template>

<style scoped lang="less">
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}
</style>