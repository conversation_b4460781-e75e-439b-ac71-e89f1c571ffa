<template>
  <div id="scatterRoot">
    <div class="analyse-content">
      <analyse-header
        :model="ReportAnalyseModel.SCATTER"
        :query-param="queryParam"
        :report-data="reportData"
        :header-info="headerInfo"
        @call-computed-data="computedData"
        @update-report-form="updateReportForm"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="dataLoading" class="box">
                <div v-if="!dataLoading" class="query-condition">
                  <analysisIndex
                    :indicator-data="indicatorData"
                    :secondary-data="secondaryData"
                    @indicator-change="indicatorChange"
                    @secondary-indicator-change="secondaryIndicatorChange"
                    @analysis-subject-change="analysisSubjectChange"
                    @reset-params="reset"/>
                  <!-- 全局筛选 -->
                  <globalFilter :filter="globalFilterData" @filters-change="filtersChange"/>
                  <!-- 分组项 -->
                  <groupItem :group-item="groupData" @aggregates-change="aggregatesChange"/>
                  <div style="width: 100%;padding: 0 24px;">
                    <a-divider :margin="5" type="dashed"/>
                  </div>
                  <!-- 用户筛选、 -->
                  <userFilter :user-filter="userFilterData" @filters-change="userFiltersChange"/>
                </div>
                <div v-if="showBodyLeft" class="left-footer">
                  <a-button @click="toSave">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="{ width:'calc(100% - 24px)'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="pickDate"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        颗粒度：
                        <dateSet v-if="!dataLoading" :time-particle="timeParticle" :no-extend="true" @time-change="timeChange"/>
                      </div>
                      <div style="display: flex;align-items: center;">
                        分布区间：
                        <a-tooltip position="top" :content="formatterNumberScope(numberScope.numberSummaryType)">
                          <div v-if="!dataLoading">
                            <numberGroupSet :set-data="numberScope" @number-set="numberSet"/>
                          </div>
                        </a-tooltip>
                      </div>
                    </a-space>
                    <div style="display: flex;">
                      <div v-if="eventData.scatterQueryResult?.length && eventData.scatterQueryResult?.[1]?.length" style="display: flex;align-items: center;">
                        <a-switch v-model="secondaryIndicatorOnly" size="small" @change="whileChange"/>
                        <div style="margin-left: 4px;">只看同时展示数据</div>
                      </div>
                      <a-button-group style="background-color: #fff;margin: 0 16px;">
                        <a-popover title="表格" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('table')" @click="selectChart('table')"
                          >
                            <template #icon><img class="option-icon" src="/icon/table-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            <div>
                              <p>展示各个区间的分布详情</p>
                              <img :src="table" alt="" style="width:100%;height: 90%;"/>
                            </div>
                          </template>
                        </a-popover>
                        <a-popover title="趋势图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('trend') || !(queryParam.timeParticleSize !== TimeParticleSize.TOTAL && secondaryIndicatorOnly)" @click="selectChart('trend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/trend-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示“同时展示指标”在各个区间的变化趋势<br>
                            <img :src="trend" alt="" style="width:100%;height: 90%;"/><br/>
                            *当时间粒度非合计，且“只看同时展示数据”时，可用
                          </template>
                        </a-popover>
                        <a-popover title="柱状图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('distribution') || !(queryParam.timeParticleSize === TimeParticleSize.TOTAL && secondaryIndicatorOnly)" @click="selectChart('distribution')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示“同时展示指标”在各个区间的分布<br>
                            <img :src="distribution" alt="" style="width:100%;height: 90%;"/><br/>
                            *当时间粒度为合计，且“只看同时展示数据”时，可用
                          </template>
                        </a-popover>
                        <a-popover title="数值分布" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType === 'stacked' || !(queryParam.timeParticleSize !== TimeParticleSize.TOTAL && !secondaryIndicatorOnly)" @click="selectChart('stacked')"
                          >
                            <template #icon><img class="option-icon" src="/icon/stack-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示各区间人数的变化趋势 <br>
                            <img :src="stacked" alt="" style="width:100%;height: 90%;"/>
                            *当时间粒度非合计，且非“只看同时展示数据”时，可用
                          </template>
                        </a-popover>
                        <a-popover title="百分比分布" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('stackedRate') || !(queryParam.timeParticleSize !== TimeParticleSize.TOTAL && !secondaryIndicatorOnly)" @click="selectChart('stackedRate')"
                          >
                            <template #icon><img class="option-icon" src="/icon/stack-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示各区间人数占比的变化趋势 <br>
                            <img :src="stacked" alt="" style="width:100%;height: 90%;"/>
                            *当时间粒度非合计，且非“只看同时展示数据”时，可用
                          </template>
                        </a-popover>
                        <a-popover title="直方图" position="bottom" :content-style="{width: '260px'}">
                          <a-button :disabled="isChartType.includes('zfTrend') || !(queryParam.timeParticleSize === TimeParticleSize.TOTAL && !secondaryIndicatorOnly)" @click="selectChart('zfTrend')"
                          >
                            <template #icon><img class="option-icon" src="/icon/distribution-chart.svg" alt=""/></template>
                          </a-button>
                          <template #content>
                            展示各个间隔区间的人数分布 <br>
                            <img :src="zfTrend" alt="" style="width: 100%;height: 90%;"/><br/>
                            *当时间粒度为合计，且非“只看同时展示数据”时，可用
                          </template>
                        </a-popover>
                      </a-button-group>
                      <a-button :disabled="!eventData.scatterQueryResult?.length" @click="importTable()">导出</a-button>
                    </div>
                  </div>
                  <div v-if="!loading && eventData.scatterQueryResult?.length" class="right-content">
                    <a-alert v-if="eventData?.resultsExceedsLimit" style="margin-bottom: 5px;">
                      因数据条数过多，优先展示前1000条数据。建议修改分组项或添加更严格的过滤项以减少数据条数，或点击右上角下载全量数据。
                    </a-alert>
                    <event-table-vue v-show="isChartType == 'table'" ref="tableViewRef" :event-data="eventData" :secondary-indicator-only="secondaryIndicatorOnly"/>
                    <event-echars-vue v-show="isChartType !== 'table'" ref="chartsViewRef" :event-data="eventData" :graph-type="isChartType"/>
                  </div>
                  <div v-if="!loading && !eventData.scatterQueryResult?.length" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
    <!-- 保存弹窗 -->
    <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
      <template #title>
        <div class="modal-title">保存报表</div>
      </template>
      <a-form ref="saveFormRef" :model="form">
        <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
          <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
        </a-form-item>
        <a-form-item field="dashboard" label="保存至看板">
          <dashboard-select v-model:selected="form.dashboard" type="dashboard" @change="dashboardChange"/>
        </a-form-item>
        <a-form-item field="description" label="备注">
          <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button class="cancel" @click.stop="handleSaveCancel">取消</a-button>
        <a-button type="primary" @click="saveReport">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {h, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {useEventBus, useLocalStorage, useSessionStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {useRoute} from 'vue-router';
import router from "@/router";
import {saveAnalyseReportList} from "@/api/analyse/api";
import {distribution, stacked, table, trend, zfTrend} from '@/views/dashboard/components/img';
import {ChartType, TimeParticleSize} from "@/api/enum";
import {cloneDeep, debounce, isEmpty} from "lodash";
import {ReportAnalyseModel} from "@/api/analyse/type";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import {ROUTE_NAME} from "@/router/constants";
import {LocalStorageEventBus} from "@/types/event-bus";
import {toolStore} from '@/store';
import {queryScatterReportData, reportQueryResponseData} from "@/api/analyse/analyse";
import {detailReport} from "@/api/report/api";
import userFilter from "@/views/analyse/components/UserFilter.vue"
import {compressDateRangeToEndDate} from "@/utils/dateUtil";
import numberGroupSet from "@/views/analyse/components/numberGroupSet.vue"
import {removeEnumList} from "@/views/analyse/components/util/verify";
import dateSet from "../components/dateSet.vue"
import groupItem from "../components/groupItem.vue"
import analyseHeader from "../components/analyseHeader.vue"
import analysisIndex from "./components/analysisIndex.vue"
import eventTableVue from "./components/TableViewFlat.vue";
import eventEcharsVue from "./components/EcharsView.vue";
import globalFilter from "../components/globalFilter.vue"

const dashboardSelected = useSessionStorage("dashboardSelected", {spaceId: "", folderId: "", dashboardId: ""});
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const paramsDataStorage = useSessionStorage("scatter-params-data", {});
const checkedSessionGroup = useSessionStorage('checkedSessionGroup', '总体')
const headerInfo = reactive({
  title: '分布分析',
  img: '/icon/topMenu/scatter.svg',
  tips: '一段时间内，指定用户参与某一事件的总完成次数或属性值按个人聚合后的全员分布情况',
  root: '#scatterRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true,
  showAppSelect: true
})
const route = useRoute();
const reportId = ref(route.query.code);
const toolData = toolStore()
const eventData = ref({
  y: [],
  groupsDesc: [],
  scatterQueryResult: []
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// const splitSize = ref('460px')
// ------left start
// 查询暂无requestId返回
const requestId = ref()
const timeOut = ref()
const loading = ref(false)
const isReset = ref(false)
// 传参数组
const queryParam = reactive({
  analyzeSubjectCode: '',
  analyzeSubjectName: '',
  indicator: {}, // 查询指标
  secondaryIndicator:{}, // 同时展示指标
  filter: {}, // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: {
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
  }, // 查询日期范围
  timeParticleSize: TimeParticleSize.DAY1, // 时间粒度
  firstDayDfWeek: null,
  userFilter: {},// 用户筛选条件
  numberSummaryType:'st12',// 数值汇总方式
  numberSummaryScope:[],// 数值区间
})
const secondaryIndicatorOnly = ref(false) // 只看同时展示数据
const numberScope = ref({
  numberSummaryType:'st12',// 数值汇总方式
  numberSummaryScope:[]// 数值区间
})
const formatterNumberScope = (v) => {
  const dataMap = {
    'st12': '默认区间',
    'discrete': '离散数字',
    'custom': '自定义区间',
  }
  return dataMap[v]
}
const timeParticle = ref({
  timeParticleSize: '',
  firstDayDfWeek: null
})
const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const dataLoading = ref(false)
const eventBus = useEventBus('eventList');
const indicatorData = ref({}) // 分析指标传参
const secondaryData = ref({}) // 同时展示指标传参
const globalFilterData = ref({}) // 全局筛选传参
const groupData = ref<any>([]) // 分组项传参
const userFilterData = ref({}) // 用户筛选传参
const handleAttrBus = debounce(async () => {
  const list = queryParam.indicator?.eventList.concat(queryParam.secondaryIndicator?.eventList || [])
  if (list.length > 0) {
    form.value.name = `${list[0]?.eventDisplayName}的${queryParam.analyzeSubjectName}分布情况`
  }
  eventBus.emit(list);
}, 300)
const indicatorChange = (v) => {
  const [indicator] = v;
  queryParam.indicator = indicator;
  paramsDataStorage.value = queryParam;
  handleAttrBus()
}
const secondaryIndicatorChange = (v) => {
  if (v) {
    const [secondaryIndicator] = v;
    queryParam.secondaryIndicator = secondaryIndicator || {};
    paramsDataStorage.value = queryParam;
    handleAttrBus()
  }
}
const analysisSubjectChange = (v) => {
  const {subject, subjectName} = v
  queryParam.analyzeSubjectCode = subject
  queryParam.analyzeSubjectName = subjectName
  paramsDataStorage.value = queryParam
}
// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
  paramsDataStorage.value = queryParam;
}
// 用户筛选传参
const userFiltersChange = (v) => {
  queryParam.userFilter = v
  paramsDataStorage.value = queryParam;
}
// 分组项传参
const aggregatesChange = (v) => {
  queryParam.aggregates = v
  paramsDataStorage.value = queryParam;
}
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取三个需要验证的列表
  const firstList = params.indicator?.eventList
      .filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  const secondList = params.filter?.filters || [];
  const thirdList = params.secondaryIndicator?.eventList
      ?.filter(item => item.filter?.filters?.length > 0)
      .map(item => item.filter.filters);
  // 添加对 userFilter 的校验
  const userFilterList = params?.userFilter?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.userFilter?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userFilter?.userCondition?.filters || [];
  // 验证所有列表
  return verifyFilterList(firstList) &&
      verifyFilterList(secondList) &&
      verifyFilterList(thirdList) &&
      verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};
// 计算
const computedData = async () => {
  loading.value = true
  timeOut.value = setTimeout(() => {
    if (loading.value) {
      loading.value = false
      Message.warning('事件查询超时，请重试！')
    }
  }, 60*1000)
  if (paramsVerify(queryParam)) {
    requestId.value = queryScatterReportData(removeEnumList(queryParam));
  } else {
    Message.error('筛选条件参数错误')
    loading.value = false
  }
}
watch(reportQueryResponseData, async (newData, oldData) => {
  if (newData === undefined || newData.requestId !== requestId.value) {
    return
  }
  if (newData.result === null) {
    timeOut.value && clearTimeout(timeOut.value)
    loading.value = false
    return
  }
  timeOut.value && clearTimeout(timeOut.value)
  eventData.value = newData.result
  paramsDataStorage.value = queryParam
  loading.value = false
})

const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
onMounted(() => {
  // eventBus.on((event: any) => {
  //   form.value.name = `${event[0]?.displayName}等${event.length}个`
  // });
})
const handleSaveCancel = () => {
  saveVisible.value = false;
}
const saveLoading = ref(false)
const editReportData = reactive({
  name: '',
  description: ''
})

const dashboardData = ref()
const dashboardChange = (v) => {
  dashboardData.value = v
}

// 保存报表
const saveReport = debounce(async () => {
  if (reportId.value && !editReportData?.name) {
    Message.error('报表名称不能为空');
    return;
  }
  if (!reportId.value && !form.value.name) {
    Message.error('报表名称未填写');
    return;
  }
  saveLoading.value = true;
  try {
    const saveParams = {
      model: ReportAnalyseModel.SCATTER,
      reportId: reportId.value || undefined,
      dashboard: form.value.dashboard,
      name: editReportData?.name || form.value.name,
      description: editReportData?.description || form.value.description,
      queryParam,
      chartParams: [],
      chartType: ChartType.TABLE
    };
    await saveAnalyseReportList(
        saveParams.model,
        saveParams.reportId,
        saveParams.dashboard,
        saveParams.name,
        saveParams.description,
        saveParams.queryParam,
        saveParams.chartParams,
        saveParams.chartType
    );
    dashboardSelected.value = { ...dashboardData.value }
    const successMsg = isEmpty(form.value.dashboard)
        ? h('span', [
          '报表已保存'
        ])
        : h('span', [
          '报表已保存并添加至看板，',
          h('a', {
            style: {color: 'rgb(var(--primary-6))', cursor: 'pointer'},
            onClick: () => router.push({
              name: ROUTE_NAME.DASHBOARD,
            }),
          }, '去看板查看')
        ]);
    Message.success({
      content: () => successMsg,
      duration: 3000
    });
    eventBus.emit('saveReport');
    if (!reportId.value) {
      saveVisible.value = false;
    }
  } catch (error) {
    console.error('保存报表失败:', error);
  } finally {
    saveLoading.value = false;
  }
}, 300)
const updateReportForm = (v) => {
  editReportData.name = v.name
  editReportData.description = v.description
  saveReport()
}
const toSave = () => {
  if (reportId.value) {
    saveReport()
  } else {
    saveVisible.value = true
  }
}
const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
provide('boardDate', boardDate);
const pickDate = (date: any) => {
  boardDate.value = date
  queryParam.dateRange = date;
  computedData()
};

const chartsViewRef = ref()
const isChartType = ref('table');

const selectChart = (type: string) => {
  isChartType.value = type
  nextTick(() => {
    if (type !== 'table' && chartsViewRef.value) {
      chartsViewRef.value.changeType(type)
    }
  });
}
// 按小时 只能选一天
const minutesVerification = () => {
  const timeType = queryParam.timeParticleSize
  if (timeType === 'm1' || timeType === 'm5' || timeType === 'm10') {
    Message.warning('按分钟查看，时间范围一次最多展示1天')
    const compressDateRange = compressDateRangeToEndDate(boardDate.value);
    boardDate.value = compressDateRange;
    queryParam.dateRange = compressDateRange;
  }
}
const timeChange = (v) => {
  queryParam.timeParticleSize = v.timeParticleSize
  if (v.timeParticleSize === TimeParticleSize.TOTAL) {
    selectChart('table')
  }
  queryParam.firstDayDfWeek = v.firstDayDfWeek
  timeParticle.value = v
  minutesVerification()
  computedData()
}
const numberSet = (v) => {
  numberScope.value = v
  queryParam.numberSummaryType = v.numberSummaryType
  queryParam.numberSummaryScope = v.numberSummaryScope
  computedData()
}
const whileChange = (v) => {
  secondaryIndicatorOnly.value = v
  selectChart('table')
  // computedData()
}
// ---right end
const tableViewRef = ref()
const importTable = (name?: string) => {
  tableViewRef.value.exportXlsx(queryParam.dateRange, name)
}
const showBodyLeft = ref(true)

const evtLists = ref<any>([])
const reportData = ref()
const init = async () => {
  if (dataLoading.value){
    // 正在初始化中
    return
  }
  dataLoading.value = true
  checkedSessionGroup.value = '总体'
  let parsedData = {} as any
  if (reportId.value) {
    await detailReport(reportId.value as string).then(res => {
      parsedData = res.queryParam
      parsedData.dateRange = boardDate.value
      reportData.value = res
      editReportData.name = res?.name
      editReportData.description = res?.description
    })
  }
  // 如果没有从报表获取到数据，则尝试从会话存储获取
  if (Object.keys(parsedData).length === 0) {
    parsedData = paramsDataStorage.value
  }
  // 处理指标
  if (parsedData.indicator && Object.keys(parsedData.indicator).length) {
    queryParam.indicator = parsedData.indicator
    indicatorData.value = parsedData.indicator
  }
  // 处理同时展示指标
  if (parsedData.secondaryIndicator && Object.keys(parsedData.secondaryIndicator).length) {
    queryParam.secondaryIndicator = parsedData.secondaryIndicator
    secondaryData.value = parsedData.secondaryIndicator
  }
  if(parsedData.analyzeSubjectCode){
    queryParam.analyzeSubjectCode = parsedData.analyzeSubjectCode
  }
  if(parsedData.analyzeSubjectName){
    queryParam.analyzeSubjectName = parsedData.analyzeSubjectName
  }
  // 处理事件筛选
  if (parsedData.filter) {
    globalFilterData.value = parsedData.filter
    queryParam.filter = parsedData.filter
  }
  // 处理分组项
  if (parsedData.aggregates && parsedData.aggregates.length > 0) {
    groupData.value = cloneDeep(parsedData.aggregates)
    queryParam.aggregates = cloneDeep(parsedData.aggregates)
  }
  // 处理用户筛选条件
  if (parsedData.userFilter) {
    userFilterData.value = parsedData.userFilter
    queryParam.userFilter = parsedData.userFilter
  }
  // 处理时间
  if (parsedData.dateRange) {
    boardDate.value = parsedData.dateRange
    queryParam.dateRange = parsedData.dateRange
  }else{
    const date = {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    boardDate.value = date
    queryParam.dateRange = date
  }
  // 处理dateSet
  timeParticle.value = {
    timeParticleSize: parsedData.timeParticleSize ? parsedData.timeParticleSize : TimeParticleSize.DAY1,
    firstDayDfWeek: parsedData.firstDayDfWeek ? parsedData.firstDayDfWeek : null
  }
  queryParam.timeParticleSize = timeParticle.value.timeParticleSize
  queryParam.firstDayDfWeek = timeParticle.value.firstDayDfWeek
  // 处理numberSet
  numberScope.value = {
    numberSummaryType: parsedData.numberSummaryType? parsedData.numberSummaryType : 'st12',
    numberSummaryScope: parsedData.numberSummaryScope? parsedData.numberSummaryScope : []
  }
  queryParam.numberSummaryType = numberScope.value.numberSummaryType
  queryParam.numberSummaryScope = numberScope.value.numberSummaryScope
  toolData.updateTemporaryList([])
  evtLists.value = (await toolData.fetchAllModalList()).flatMap(category => category.items || []);
  dataLoading.value = false
  setTimeout(() => {
    computedData()
  }, 1000)
}
init()
localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    init()
  }
})
// 重置
const reset = () => {
  isReset.value = true
  paramsDataStorage.value = {}
  globalFilterData.value = {}
  indicatorData.value = {}
  secondaryData.value = {}
  queryParam.indicator = {}
  queryParam.filter = {}
  queryParam.secondaryIndicator = {}
  groupData.value = []
  userFilterData.value = {}
  queryParam.userFilter = {}
  init()
}
</script>

<style scoped lang="less">
#scatterRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;
}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  align-items: center;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  min-height: 0;
  flex: 1;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding-top: 24px;
  //padding-bottom: 18px;
  // border-bottom: 1px solid var(--tant-border-color-border1-1);
}


.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;
}

.body-right-content {
  width: 100%;
  // height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}
</style>