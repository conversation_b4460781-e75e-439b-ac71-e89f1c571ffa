<template>
  <a-modal
    v-model:visible="visible"
    title="自定义列"
    title-align="start"
    width="850px"
    :footer="false"
    class="custom-column-modal"
    @cancel="handleCancel"
  >
    <div class="custom-column-content">
      <div class="column-left">
        <a-input v-model="searchKeyword" placeholder="请输入列名称" allow-clear style="margin-bottom: 12px;" />
        <div v-for="group in filteredColumnGroups" :key="group.name" class="group-block">
          <div class="group-list">
            <div class="check-head">
              <a-checkbox
                :indeterminate="checkedKeys[group.key]?.length > 0 && checkedKeys[group.key]?.length < group.children.length"
                :checked="checkedKeys[group.key]?.length === group.children.length"
                @change="toggleGroupCheck(group)"
              >{{ group.name }}</a-checkbox>
            </div>
            <a-checkbox-group v-model="checkedKeys[group.key]">
              <a-checkbox
                v-for="item in group.children"
                :key="item.key"
                :value="item.key"
                @change="onCheck(item, group.key)"
              >{{ item.label }}</a-checkbox>
            </a-checkbox-group>
          </div>
        </div>
      </div>
      <div class="column-right">
        <div class="selected-header">
          <div class="selected-title">已选 {{defaultFixedColumns.length + customColumns.length + dynamicFixedColumns.length }} 列</div>
          <div class="btn" @click="clearColumns">清除</div>
        </div>
        <div v-for="item in defaultFixedColumns" :key="item.label" class="selected-item">
          <icon-menu />
          <span class="item-label">{{ item.label }}</span>
        </div>
        <draggable
          v-model="dynamicFixedColumns"
          :group="{ name: 'columns', pull: true, put: true }"
          item-key="key"
          animation="200"
          handle=".selected-item"
          @change="onFixedDragChange"
        >
          <template #item="{element, index}">
            <div class="selected-item">
              <icon-menu />
              <span class="item-label">{{ element.label }}</span>
              <icon-close v-if="element.notDefault" style="cursor: pointer;" @click.stop="moveToSelected(element)" />
            </div>
          </template>
        </draggable>
        <a-divider>拖动到上方的列将固定显示</a-divider>
        <draggable
          v-model="customColumns"
          :group="{ name: 'columns', pull: true, put: true }"
          item-key="key"
          animation="200"
          handle=".selected-item"
          @change="onSelectedDragChange"
        >
          <template #item="{element, index}">
            <div class="selected-item">
              <icon-menu />
              <span class="item-label">{{ element.label }}</span>
              <icon-close style="cursor: pointer;" @click.stop="removeColumn(element.key)"/>
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <div class="modal-footer">
      <a-button style="margin-right: 24px;" @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import draggable from 'vuedraggable';

// 假数据定义
const mockColumnGroups = [
  {
    name: '属性',
    key: 'property',
    children: [
      { key: 'designer', label: '设计师' },
      { key: 'creator', label: '创意人' },
      { key: 'tag', label: '标签' },
      { key: 'size', label: '尺寸' },
      { key: 'type', label: '类型' },
      { key: 'uploadTime', label: '上传时间' },
      { key: 'auditStatus', label: '审核状态' },
      { key: 'materialNote', label: '素材备注' },
      { key: 'cost', label: '制作费用' },
      { key: 'score', label: '评分' },
      { key: '投放渠道', label: '投放渠道' }
    ]
  },
  {
    name: '数据',
    key: 'data',
    children: [
      { key: '花费', label: '花费' },
      { key: '展示数', label: '展示数' },
      { key: '千次展示成本', label: '千次展示成本' },
      { key: '点击数', label: '点击数' },
      { key: '点击成本', label: '点击成本' },
      { key: '点击率', label: '点击率' },
      { key: '转化数', label: '转化数' },
      { key: '转化成本', label: '转化成本' }
    ]
  }
];

const props = defineProps({
  visible: Boolean,
  selectedColumnList: Array,
  // columnGroups: Array
});
const emit = defineEmits(['update:visible', 'save']);

const visible = ref(props.visible);
watch(() => props.visible, v => {
  visible.value = v;
});

// 选中项分组
const checkedKeys = reactive<Record<string, string[]>>({});
const defaultFixedColumns = [
  { key: 'id', label: 'Local ID' },
  { key: 'name', label: '名称' },
  { key: 'status', label: '缩略图' }
];
const dynamicFixedColumns = ref<any>([]);
const customColumns = ref<any>([]);
// 使用假数据初始化
const columnGroups = ref(mockColumnGroups);

// 搜索关键字
const searchKeyword = ref('');

// 过滤后的分组
const filteredColumnGroups = computed(() => {
  if (!searchKeyword.value) return columnGroups.value;
  return columnGroups.value.map(group => {
    const filteredChildren = group.children.filter(item => item.label.includes(searchKeyword.value));
    return { ...group, children: filteredChildren };
  }).filter(group => group.children.length > 0);
});

// 监听 checkedKeys，动态生成 customColumns
watch(checkedKeys, (val) => {
  const allColumns = columnGroups.value?.flatMap(g => g.children) || [];
  const checked = Object.values(checkedKeys).flat();

  // 1. 保持 dynamicFixedColumns 顺序，仅保留 checked 中存在或默认固定列
  dynamicFixedColumns.value = dynamicFixedColumns.value.filter(col =>
    defaultFixedColumns.some(originCol => originCol.key === col.key) || checked.includes(col.key)
  );
  // 2. 保持 customColumns 顺序，仅保留 checked 中存在且不在 dynamicFixedColumns 里的项
  customColumns.value = customColumns.value.filter(col =>
    checked.includes(col.key) && !dynamicFixedColumns.value.some(fixedCol => fixedCol.key === col.key)
  );
  // 3. checked 中有但 customColumns 里没有的，按 checked 顺序 push 进 customColumns
  checked.forEach(key => {
    if (
      !dynamicFixedColumns.value.some(fixedCol => fixedCol.key === key) &&
      !customColumns.value.some(col => col.key === key)
    ) {
      const col = allColumns.find(col => col.key === key);
      if (col) customColumns.value.push(col);
    }
  });
}, { deep: true });

// 初始化 checkedKeys
watch(() => columnGroups.value, (groups) => {
  if (groups) {
    groups.forEach(group => {
      checkedKeys[group.key] = customColumns.value.filter(col => group.children.some(item => item.key === col.key)).map(col => col.key);
    });
  }
}, { immediate: true });

function onCheck(item, groupKey) {
  // 只需触发 checkedKeys 的变化，selectedColumns 会自动同步
}

function toggleGroupCheck(group) {
  const allKeys = group.children.map(item => item.key);
  if (checkedKeys[group.key]?.length === group.children.length) {
    checkedKeys[group.key] = [];
  } else {
    checkedKeys[group.key] = allKeys;
  }
}

function removeColumn(key) {
  // 从已选列移除
  customColumns.value = customColumns.value.filter(col => col.key !== key);
  // 同步左侧勾选
  Object.keys(checkedKeys).forEach(groupKey => {
    checkedKeys[groupKey] = checkedKeys[groupKey].filter(k => k !== key);
  })
}


function handleOk() {
  emit('save', customColumns.value);
  emit('update:visible', false);
}
function handleCancel() {
  emit('update:visible', false);
}

function onDragChange(e) {
  // 拖拽后同步左侧勾选状态
  const allKeys = customColumns.value.map(col => col.key);
  Object.keys(checkedKeys).forEach(groupKey => {
    checkedKeys[groupKey] = checkedKeys[groupKey].filter(k => allKeys.includes(k));
  });
}
// 清除所有列
function clearColumns() {
  dynamicFixedColumns.value = [];
  customColumns.value = [];
  Object.keys(checkedKeys).forEach(key => {
    checkedKeys[key] = [];
  });
}
function onFixedDragChange(e) {
  // 拖拽到固定区
  if (e.added) {
    const item = e.added.element;
    if (!defaultFixedColumns.find(col => col.key === item.key)) {
      // 不是原始固定列，标记来源
      item.notDefault = true;
      // 从下方移除
      customColumns.value = customColumns.value.filter(col => col.key !== item.key);
    }
  }
  if (e.removed) {
    const item = e.removed.element;
    if (item.notDefault) {
      // 允许拖回下方
      customColumns.value.push({ ...item });
    }
  }
}
function onSelectedDragChange(e) {
  // 拖拽到下方
  if (e.added) {
    const item = e.added.element;
    if (item.notDefault) {
      // 从固定区拖回来的
      item.notDefault = false;
      dynamicFixedColumns.value = dynamicFixedColumns.value.filter(col => col.key !== item.key);
    }
  }
}
function moveToSelected(item) {
  // 固定区移除到下方
  dynamicFixedColumns.value = dynamicFixedColumns.value.filter(col => col.key !== item.key);
  // customColumns.value.push({ ...item, notDefault: false });
  // 同步左侧勾选
  Object.keys(checkedKeys).forEach(groupKey => {
    checkedKeys[groupKey] = checkedKeys[groupKey].filter(k => k !== item.key);
  })
}

</script>

<style scoped>
.custom-column-content {
  display: flex;
  height: 680px;
}
.column-left {
  flex: 1;
  border-right: 1px solid #eee;
  padding-right: 24px;
  overflow-y: auto;
  .check-head{
    padding: 5px 10px;
    background-color: #f9fafc;
    margin-bottom: 8px;
  }
}
.column-right {
  width: 210px;
  margin-left: 24px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px 10px;
  :deep(.arco-divider-text){
    font-size: 12px;
    white-space: nowrap;
    font-weight: normal;
    padding: 0;
  }
}
.group-block {
  margin-bottom: 24px;
}
.group-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  .btn{
    color: rgb(var(--primary-6));
    cursor: pointer;
    font-size: 12px;
    padding: 4px;
  }
}
.selected-title {
  font-weight: bold;
}
.selected-item,.fixed-item {
  display: flex;
  align-items: center;
  margin: 2px 4px 8px 0;
  padding: 0 8px;
  cursor: pointer;
  border: 1px solid #e8eaec;
  border-radius: 3px;
  background: #f5f6f7;
  line-height: 32px;
  min-height: 32px;
  height: auto;
  overflow: visible;
  font-size: 12px;
  .item-label{
    margin-left: 8px;
  }
}
.drag-handle {
  cursor: grab;
  margin-right: 8px;
  color: #888;
}
.item-label {
  flex: 1;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}
</style>