<template>
    <a-modal v-model:visible="modalVisible" :width="800" title-align="start" title="ECPM详情" :footer="false" @cancel="closeModal">
        <div class="handle-header">
            <div class="left">
                <a-range-picker
                v-model:model-value="detailParams.date"
                :allow-clear="false"
                style="width: 240px;"
                :disabled-date="(current: Date) => current.getTime() > new Date().getTime()"
                @change="getData"/>
            </div>
            <div class="right">
                <div style="flex-shrink: 0;min-width: 120px;margin-right: 8px;">
                    <a-select
                        v-model:model-value="detailParams.placement"
                        :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                        @change="getData">
                        <a-option :value="0">媒体渠道</a-option>
                        <a-option :value="1">广告版位</a-option>
                    </a-select>
                </div>
                <!-- <a-select
                    v-model:model-value="detailParams.type"
                    :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                    <a-option value="table">数据表</a-option>
                    <a-option value="line">折线图</a-option>
                </a-select> -->
                <a-radio-group v-model:model-value="detailParams.type" type="button">
                    <a-radio value="table">数据表</a-radio>
                    <a-radio value="line">折线图</a-radio>
                </a-radio-group>
            </div>
        </div>
        <a-spin :loading="loading" style="width: 100%;min-height: 400px;">
            <a-table v-if="detailParams.type === 'table'" :columns="realizationColumns" :data="formatData" :pagination="false" size="small" :scroll="scroll">
            </a-table>
            <div v-else class="chart-content">
                <Chart :option="chartOption"/>
            </div>
        </a-spin>
        
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import dayjs from 'dayjs';
import useChartOption from "@/hooks/chart-option";
import {getPlacementEcpm} from "@/api/analyse/api"
import {useSessionStorage} from '@vueuse/core'

const appIds = useSessionStorage("app-id-list", []);
interface Props {
    filterParams:any
}
const props = defineProps<Props>();
const scroll = {
  y: '400px',
  x: '100%'
}
const detailParams = reactive({
    type:'table',
    date:[dayjs().subtract(29, 'day').startOf('day').format('YYYY-MM-DD'),dayjs().subtract(0, 'day').endOf('day').format('YYYY-MM-DD')],
    placement:0

})
const modalVisible = ref(false)

const realizationData = ref()



const ySeries = ref<any>([])


// 获取日期范围的函数
const getDaysBetween = (startDate, endDate) => {
  const dates = [];
  const currentDate = new Date(startDate);
  const end = new Date(endDate);
  
  while (currentDate <= end) {
    dates.push(currentDate.toISOString().split('T')[0]);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dates;
}
const xAxis = ref<any>([])
watch(
  () => detailParams.date,
  (newDate) => {
    xAxis.value = getDaysBetween(newDate[0], newDate[1]);
  },
  { immediate: true }
);
const legendData = ref<any>([])
const {chartOption} = useChartOption(() => {
  return {
      grid: {
        left: '0%',
        right: '0%',
        top: '40',
        bottom: '20',
        containLabel: true
      },
      legend: {
        data: legendData.value,
        bottom: '0',
        type: 'scroll', // 设置图例为滚动类型
      },
      //  
      xAxis: {
        type: 'category',
        data: xAxis.value,
        axisLine:{
          show:true
        },
        axisLabel:{
          color:'#8E8E8E'
        },
        axisTick:{
          show:false
        },
      },
      yAxis: {
        type: 'value',
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        enterable: true,
        confine:true,
      },
      series:ySeries.value
    };
});
const groupValue = ref('')
const loading = ref(false)
const groupByPlatform = (data) => {
  // 过滤掉没有 platform 的数据并按 platform 分组
  return data
    .filter(item => item.platform)
    .reduce((groups, item) => {
      const platform = item.platform;
      if (!groups[platform]) {
        groups[platform] = [];
      }
      groups[platform].push(item);
      return groups;
    }, {});
};
const groupByPlatformAndPlacement = (data) => {
  return data
    .filter(item => item.platform)
    .reduce((groups, item) => {
      const platform = item.platform;
      const placement = item.placement || '';
      const key = `${platform}${placement ? `-${placement}` : ''}`;
      
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {});
};
const realizationColumns = ref<any>([])
const formatData = ref<any>([])

const getData = async () => {
    loading.value = true
    realizationColumns.value = [
        {
            title: '日期',
            dataIndex: 'rpDate',
            minWidth: 140,
            slotName: 'rpDate',
        },
    ]
    const data = {
        appId:appIds.value.join(','),
        country:props.filterParams.country.join(','),
        startDate:detailParams.date[0],
        endDate:detailParams.date[1],
        group:props.filterParams.group,
        groupValue:groupValue.value,
        placement:detailParams.placement
    }
    try {
        await getPlacementEcpm(data).then(res => {
            realizationData.value = res
            realizationData.value.forEach(item => {
                if(item.ecpm){
                    item.ecpm = Number((item.ecpm).toFixed(4))
                }
            })
            const grouped = detailParams.placement === 0 ? groupByPlatform(realizationData.value) :groupByPlatformAndPlacement(realizationData.value);
            const columnsList = Object.keys(grouped).map(key => {
                return {
                    title: key,
                    dataIndex: key,
                    minWidth: 140,
                    slotName: key,
                }
            })
            const maList = [
                {
                    title: '变现ECPM',
                    dataIndex: 'maEcpm',
                    minWidth: 140,
                    slotName: 'maEcpm',
                }
            ]
            // 根据日期合并
            const mergedData = realizationData.value.reduce((acc, curr) => {
                const date = curr.rpDate;
                const ma = curr?.maEcpm
                if (!acc[date]) {
                    acc[date] = { rpDate: date,maEcpm:ma };
                }
                // 使用 platform-placement 或 platform 作为 key
                const key = curr.placement ? `${curr.platform}-${curr.placement}` : curr.platform;
                acc[date][key] = curr?.ecpm;
                acc[date].maEcpm = ma;
                return acc;
            }, {});
            formatData.value = Object.values(mergedData);
            realizationColumns.value.push(...columnsList,...maList)
            const groupList = Object.keys(grouped).map(key => {
                return {
                    name:key,
                    type: 'line',
                    stack: 'Total',
                    data: grouped[key].map(item => item.ecpm).reverse()
                }
            })
            const maData = {
                name:'变现ECPM',
                type: 'line',
                stack: 'Total',
                data:realizationData.value.map(item =>item?.maEcpm).reverse()
            }
            ySeries.value = [...groupList, maData];
            legendData.value = ySeries.value.map(item => item.name)
        })
    } catch (error) {
      console.error('获取数据失败:', error);
    }finally {
      loading.value = false
    }
}

const openModal = async (name:string) => {
    detailParams.placement = 0
    detailParams.type = 'table'
    detailParams.date = [dayjs().subtract(29, 'day').startOf('day').format('YYYY-MM-DD'),dayjs().subtract(0, 'day').endOf('day').format('YYYY-MM-DD')]
    groupValue.value = name || ''
    modalVisible.value = true
    await getData()
}

const closeModal = () => {
    modalVisible.value = false
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.handle-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    .right{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
.chart-content{
    width: 100%;
    height: 400px;
}
</style>