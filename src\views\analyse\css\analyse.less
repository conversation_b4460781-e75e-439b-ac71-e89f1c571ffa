#analyse-root {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.analyse-content {
  height: 100%;
  overflow: hidden;
  background: var(--dark-bg-color);
  .analyse-body {
    position: relative;
    width: 100%;
    height: calc(100vh - 182px);
    margin: 0;
    overflow: hidden;
    .body-left {
      height: 100%;
      background: #fff;
      position: relative;
      .query-condition {
        width: 100%;
        height: calc(100% - 56px);
        overflow-x: hidden;
        overflow-y: auto;
        box-sizing: border-box;
        position: relative;
      }
      .left-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 56px;
        padding: 12px 48px 12px 20px;
        display: flex;
        justify-content: flex-end;
        background-color: #fff;
        border-top: 1px solid var(--tant-border-color-border1-1);
        align-items: center;

        .footer-btn {
          margin-left: 12px;
          min-width: 80px;
        }
      }
    }
    .body-right {
      height: 100%;
      outline: none;
      background: #fff;
      position: relative;
      display: flex;
      transition: all .3s ease-in-out;
    }

    .arrow {
      position: absolute;
      bottom: 15px;
      right: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 24px;
      color: var(--widget-color);
      font-size: 16px;
      background-color: #fff;
      border: 1px solid var(--tant-border-color-border1-1);
      border-right: 0;
      border-radius: 12px 0 0 12px;
      cursor: pointer;
      transition: all .3s;
    }

    .arrow:hover {
      color: var(--tant-primary-color-primary-default);
      border-color: var(--tant-primary-color-primary-default);
      box-shadow: var(--input-shadow);
      width: 34px;
    }

    .deg {
      left: 0;
      transform: rotate(180deg)
    }
  }
}

// 分割线
.arco-split-trigger-icon-wrapper {
  display: inline-block;
  width: 4px;
  height: 100%;
  vertical-align: top;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-left: 1px solid var(--tant-border-color-border1-1);
  cursor: col-resize;
  transition: all;
  transition-timing-function: cubic-bezier(var(--tant-motion-easing-standard-ease-in-out));
  transition-duration: var(--tant-motion-duration-macro-moderate);
  &:hover {
    background: var(--tant-primary-color-primary-hover);
    border: 0;
  }
}

.arco-split-trigger-icon {
  display: none;
}

// 保存弹窗
.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

// 分析选择器按钮
.select-btn,.select-btn-disabled{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  max-width: 100%;
  height: 26px;
  padding: 0 8px;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;
  .btn-icon{
    color: var(--tant-text-gray-color-text1-2);
    margin-right: 5px;
    flex-shrink: 0;
  }
  .btn-label{
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  
}
.select-btn{
  cursor: pointer;
  &:hover{
    background-color: var(--tant-bg-white-color-bg1-1);
    border-color: var(--tant-primary-color-primary-hover);
  }
}
.download-txt {
    margin: 0 4px;
    color: var(--tant-primary-color-primary-default);
    cursor: pointer;
}
.mask{
  position: absolute;
  top: 0;
  left: 0;
  height: calc(100% - 3px);
  width: calc(100% + 24px);
  z-index: 99;
  background: repeating-linear-gradient(
      45deg,
      #d3d3d3,
      #d3d3d3 20px,
      #888888 20px,
      #888888 40px
  );
  opacity: 0.25;
}
// 无数据显示样式
.empty {
  height: 85%;
  width: 100%;
  min-height: 400px;
  margin: 0 24px 32px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;

  .empty-body {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);

    .empty-img {
      width: 400px;
      height: 256px;
    }

    .empty-description {
      color: var(--tant-text-gray-color-text1-3);
      font-size: 12px;
      width: 400px;
      word-break: break-all;
      text-align: center;
      margin: 0 auto;

      .title {
        margin-bottom: 12px;
        font-weight: 500;
        font-size: 16px;
        color: var(--tant-text-gray-color-text1-2);
      }

      .button-add {
        margin-top: 12px;
        color: var(--tant-white-white-100);
        background-color: var(--tant-primary-color-primary-default);
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }
    }

  }
}
// 右侧可视化设置样式
.drawer {
  height: 100%;
  overflow: auto;
  border-left: 1px solid var(--tant-border-color-border1-1);
  border-top-right-radius: 4px;
  transition: all;
  transition-timing-function: cubic-bezier(var(--tant-motion-easing-standard-ease-in-out));
  transition-duration: var(--tant-motion-duration-macro-moderate);

  .drawer-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    padding: 16px 12px;
  }

  :deep(.arco-btn-secondary) {
    background: #fff;
    border-radius: var(--tant-border-radius-medium);

    &:hover {
      background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
  }
}