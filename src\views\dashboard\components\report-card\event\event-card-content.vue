<script setup lang="ts">
import TrendChart from "@/views/dashboard/components/table-chart/event/event-trend-chart.vue";
import TableChart from "@/views/dashboard/components/table-chart/event/event-table-item.vue";
import Total from "@/views/dashboard/components/table-chart/event/event-total-chart.vue";
import Distribution from "@/views/dashboard/components/table-chart/event/event-distribution-chart.vue";
import Stacked from "@/views/dashboard/components/table-chart/event/event-stacked-chart.vue";
import {computed, reactive, ref, watch} from "vue";
import {distribution, stacked, table, total, trend} from "@/views/dashboard/components/img"
import {ReportQueryResponse, TimeParticleSize} from "@/api/type";
import {ChartType} from "@/api/enum";
import DatePicker from "@/components/date-picker/index.vue";
import _ from "lodash";
import {IndicatorQueryResultData, IndicatorStageSummary, StageResultData, WsEventAnalysisResultData} from "@/api/report/type";
import useReportDataStore from "@/store/modules/report";
import dateSet from "@/views/analyse/components/dateSet.vue"
import {DateRange} from "@/api/analyse/type";

interface Props {
  /**
   * 报表分析数据
   */
  reportAnalysisData: ReportQueryResponse;
  // 报表信息
  report: any;
  // 日期范围
  dataRangeData: any;
  // 事件分析 / 运营分析
  modelType: any
  // 卡片大小
  size: string
}

// 获取props和emits
const props = defineProps<Props>();
const emits = defineEmits(['changeParams', 'update:modelValue', 'dateChange', 'queryParamsChange', 'pageSizeChange']);

// 主要数据ref和状态管理
const innerReportAnalysisData = ref<WsEventAnalysisResultData>(); // 当前报表分析数据
const trendChartRef = ref() // 趋势图ref
const totalRef = ref() // 累计图ref
const distributionRef = ref() // 分布图ref
const stackedRef = ref() // 堆积图ref
const groups = ref<any>([]) // 分组信息
const tableRef = ref() // 表格ref
// 报表状态
const reportDataStore = useReportDataStore()
// 时间粒度相关状态
const timeParticle = reactive<{
  timeParticleSize: string,
  firstDayDfWeek: number | null
}>({
  timeParticleSize: '',
  firstDayDfWeek: null
})
const tableTypeName = ref('按事件') // 表格类型名称
const tableType = ref('event') // 表格类型
const showLabel = ref<boolean>(false); // 是否显示数值标签
const showTable = ref<boolean>(false); // 是否显示表格
const showTotal = ref<boolean>(false); // 是否显示累计图
const showTrend = ref<boolean>(true); // 是否显示趋势图
const selectValue = ref<ChartType>(ChartType.TREND); // 当前选中的图表类型
const showDistribution = ref<boolean>(false); // 是否显示分布图
const showStacked = ref<boolean>(false); // 是否显示堆积图
const lastData = ref<StageResultData>(); // 最新数据
const huanBiData = ref<StageResultData>(); // 环比数据
const weekTongBiData = ref<StageResultData>(); // 周同比数据
const summaryValue = ref<IndicatorStageSummary>(); // 汇总数据
const huanBi = ref<number>(); // 环比百分比
const hourTongBi = ref<number>(); // 小时同比
const dayTongBi = ref<number>(); // 日同比
const weekTongBi = ref<number>(); // 周同比
const monthTongBi = ref<number>(); // 月同比
const yearTongBi = ref<number>(); // 年同比
const boardDate = ref<DateRange>({...props.dataRangeData}); // 当前选中的日期范围
// 计算属性：显示周几
const lastDataDayOfWeek = computed(() => {
  const numMap = {
    1: "一",
    2: "二",
    3: "三",
    4: "四",
    5: "五",
    6: "六",
    7: "七"
  };
  return numMap[new Date(lastData.value?.xValue).getDay()]
})
// 计算属性：对比日期列表
const comparedDateList = computed(() => reportDataStore.getComparedDateLists(props.report.objectCode))
// 计算属性：是否显示汇总信息（仅一个指标且一个分组时）
const showSummaryData = computed(() => {
  const reportData = props.reportAnalysisData?.result as WsEventAnalysisResultData;
  return reportData?.y.length === 1 && // 只有一个指标
      reportData?.groups.length === 1 &&// y 数据只有一个分组
      props.report.h > 8.5
});
const compareDataConfigs = ref([...comparedDateList.value]); // 对比日期配置

// 表格类型切换
const tableTypeChange = (v) => {
  tableType.value = v
  const typeName = props.modelType === 'event' ? '按事件' : '按指标'
  tableTypeName.value = v === 'event' ? typeName : '按日期'
}
// 图表类型切换处理
const handleChange = (value: ChartType, frash?: boolean) => {
  switch (value) {
    case ChartType.TABLE:
      // 切换为表格
      showTable.value = true;
      showTrend.value = false
      showTotal.value = false
      showDistribution.value = false
      showStacked.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TABLE})
      }
      break;
    case ChartType.TREND:
      // 切换为趋势图
      showTable.value = false;
      showTrend.value = true
      showTotal.value = false
      showDistribution.value = false
      showStacked.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TREND})
      }
      break;
    case  ChartType.TOTAL:
      // 切换为累计图
      showTable.value = false;
      showTrend.value = false
      showTotal.value = true
      showDistribution.value = false
      showStacked.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.TOTAL})
      }
      break;
    case ChartType.DISTRIBUTION:
      // 切换为分布图
      showTable.value = false;
      showTrend.value = false
      showTotal.value = false
      showDistribution.value = true
      showStacked.value = false
      if (!frash) {
        emits('changeParams', {chartType: ChartType.DISTRIBUTION})
      }
      break;
    case ChartType.STACK:
      // 切换为堆积图
      showTable.value = false;
      showTrend.value = false
      showTotal.value = false
      showDistribution.value = false
      showStacked.value = true
      if (!frash) {
        emits('changeParams', {chartType: ChartType.STACK})
      }
      break;
    default:
      break;
  }
};
// 根据图表类型切换数据
const transformData = (data: ChartType) => {
  switch (data) {
    case ChartType.TABLE:
      selectValue.value = ChartType.TABLE
      handleChange(ChartType.TABLE, true)
      break
    case ChartType.TREND:
      selectValue.value = ChartType.TREND
      handleChange(ChartType.TREND, true)
      break
    case ChartType.TOTAL:
      selectValue.value = ChartType.TOTAL
      handleChange(ChartType.TOTAL, true)
      break
    case ChartType.DISTRIBUTION:
      selectValue.value = ChartType.DISTRIBUTION
      handleChange(ChartType.DISTRIBUTION, true)
      break
    case ChartType.STACK:
      selectValue.value = ChartType.STACK
      handleChange(ChartType.STACK, true)
      break
    default:
      break
  }
}
// 选择日期回调
const pickDate = (date: any) => {
  boardDate.value = date
  reportDataStore.setDateRanges(props.report.objectCode, date);
  emits('dateChange', boardDate.value)
};
// 初始化函数，设置初始图表类型
const init = () => {
  const type = timeParticle.timeParticleSize === TimeParticleSize.TOTAL ? ChartType.DISTRIBUTION : props.report.configParams?.chartType
  transformData(type || ChartType.TREND)
}
// 显示/隐藏图表数值标签
const showLabelHandle = () => {
  trendChartRef.value.showLabels()
  totalRef.value.showLabels()
  distributionRef.value.showLabels()
  stackedRef.value.showLabels()
}
// 格式化Y轴数值显示
const formatYValue = (value: any) => {
  if (value === undefined || value === null || Number.isNaN(value)) {
    return '--'; // 如果值未加载或为 NaN，返回 '--'
  }

  const absoluteValue = Math.abs(value);
  const decimalStr = absoluteValue.toString().split(".")[1];

  // 检查小数位数
  if (!decimalStr) {
    return absoluteValue.toString(); // 整数直接返回
  }
  if (decimalStr.length === 1) {
    return absoluteValue.toFixed(1); // 一位小数，保留一位
  }
  return absoluteValue.toFixed(2); // 两位或更多小数，保留两位

};
// 时间粒度变化回调
const timeChange = (v) => {
  timeParticle.timeParticleSize = v.timeParticleSize
  timeParticle.firstDayDfWeek = v.firstDayDfWeek
  emits('queryParamsChange', timeParticle)
}
// 导出表格为xlsx
const exportXlsx = (date: any, name?: string) => {
  tableRef.value?.exportXlsx(date, name)
}
// 初始化
init()
// 监听报表状态变化，更新相关状态
watch(() => reportDataStore, (newVal) => {
  timeParticle.timeParticleSize = reportDataStore.getTimeParticleSizes(props.report.objectCode)
  timeParticle.firstDayDfWeek = reportDataStore.getFirstDayDfWeeks(props.report.objectCode)
  tableType.value = reportDataStore.getArrangeType(props.report.objectCode)
  const typeName = props.modelType === 'event' ? '按事件' : '按指标'
  tableTypeName.value = tableType.value === 'event' ? typeName : '按日期'
}, {immediate: true, deep: true})
// 监听日期范围变化
watch(() => props.dataRangeData, (newVal) => {
  boardDate.value = newVal
}, {deep: true})
// 监听对比日期列表变化
watch(() => comparedDateList.value, (newVal) => {
  compareDataConfigs.value = comparedDateList.value
})
// 监听报表分析数据变化，处理同比环比等数据
watch(() => props.reportAnalysisData, (newData) => {
  if (newData === undefined) {
    return
  }
  const result = newData?.result as WsEventAnalysisResultData;
  innerReportAnalysisData.value = result;
  groups.value = result?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(',');
  }).filter(item => item !== '总体') || [];
  // 同比环比数据
  if (result?.y?.length === 1 && result?.groupNum === 1) {
    const yData = result?.y[0]?.yData[0] as IndicatorQueryResultData;
    summaryValue.value = yData.summaryValue;
    huanBiData.value = yData.huanBi;
    lastData.value = yData.lastData;
    const tongBiData = yData.tongBi;
    const lastValue = yData.lastData?.yValue;
    const huanBiValue = yData.huanBi?.yValue;
    tongBiData?.forEach(tongBiDataItem => {
      const tongBiValue = tongBiDataItem.yValue;
      if (!lastValue || tongBiValue === undefined) {
        return;
      }
      const tongBi = _.round((lastValue - tongBiValue) / tongBiValue * 100, 2);
      switch (tongBiDataItem.timeParticleSize) {
        case TimeParticleSize.HOUR1:
          // 小时同比，按分钟查询时存在
          hourTongBi.value = tongBi
          break;
        case TimeParticleSize.DAY1:
          // 日同比，按小时查询时存在
          dayTongBi.value = tongBi
          break;
        case TimeParticleSize.WEEK1:
          // 周同比，按天查询时存在
          weekTongBi.value = tongBi
          weekTongBiData.value = tongBiDataItem
          break;
        case TimeParticleSize.MONTH1:
          // 月同比，按天、周查询时存在
          monthTongBi.value = tongBi
          break;
        case TimeParticleSize.YEAR1:
          // 年同比，按天、月、季度查询时存在
          yearTongBi.value = tongBi
          break;
        default:
      }
    })
    if (lastValue && huanBiValue !== undefined) {
      huanBi.value = _.round((lastValue - huanBiValue) / huanBiValue * 100, 2)
    }
  }
})
// 图标高度
const chartHeight = computed(() => innerReportAnalysisData.value?.resultsExceedsLimit ? 'calc(100% - 72px)' : 'calc(100% - 32px)')
// 对外暴露导出方法
defineExpose({
  exportXlsx
})

</script>

<template>
  <div class="card-content">
    <div class="card-toolbar">
      <div class="card-filter">
        <dateSet :time-particle="timeParticle" :show-border="false" @time-change="timeChange"/>
        <a-divider :margin="8" direction="vertical"/>
        <a-dropdown position="bl" @select="tableTypeChange">
          <span v-if="selectValue === ChartType.TABLE" style="line-height: 24px;cursor: pointer;display: flex;align-items: center;">
            {{ tableTypeName }}
            <img v-if="tableType == 'dateUp'" src="/icon/analysis/dateUp.svg" alt="">
            <img v-if="tableType == 'dateDown'" src="/icon/analysis/dateDown.svg" alt="">
          </span>
          <template #content>
            <a-doption value="event">{{ props.modelType === 'event' ? '按事件' : '按指标' }}</a-doption>
            <a-dsubmenu trigger="hover">
              <template #default>按日期</template>
              <template #content>
                <a-doption value="dateUp" style="width: 80px;">
                  <div
                      style="width: 100%;display: flex;justify-content:space-between;align-items: center;">
                    <span>升序</span>
                    <img src="/icon/analysis/dateUp.svg" alt="">
                  </div>
                </a-doption>
                <a-doption value="dateDown">
                  <div
                      style="width: 100%;display: flex;justify-content:space-between;align-items: center;">
                    <span>降序</span>
                    <img src="/icon/analysis/dateDown.svg" alt="">
                  </div>
                </a-doption>
              </template>
            </a-dsubmenu>
          </template>
        </a-dropdown>
        <a-divider v-if="selectValue === ChartType.TABLE" :margin="8" direction="vertical"/>
        <date-picker :date-range="boardDate" position="tl" @date-pick="pickDate"/>
      </div>
      <div class="card-config">
        <a-tooltip class="show-chart-label" :content="showLabel ? '隐藏数值':'显示数值' ">
          <div v-if="!showTable && !showLabel" class="operation-icon" @click="()=>{showLabel=!showLabel;showLabelHandle()}">
            <icon-eye-invisible class="icon"/>
          </div>
          <div v-if="!showTable && showLabel" class="operation-icon" @click="()=>{showLabel=!showLabel;showLabelHandle()}">
            <icon-eye class="icon"/>
          </div>
        </a-tooltip>
        <a-select
            id="select"
            v-model="selectValue"
            :bordered="false"
            :style="{width:'92px',padding:'0 0 0 13px',position:'relative'}"
            default-value="trend-chart"
            @change="handleChange"
        >
          <template #label="{ data }">
            <div class="chart-type-select-label">
              <img class="select-icon" :src="'/icon/'+data?.value+'-chart.svg'" alt=""/>
              <span>{{ data?.label }}</span>
            </div>
          </template>
          <template #arrow-icon>
          </template>
          <a-popover title="趋势图" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option v-if="timeParticle.timeParticleSize !== TimeParticleSize.TOTAL" :value="ChartType.TREND">
              <template #icon>
                <img class="option-icon" src="/icon/trend-chart.svg" alt=""/>
              </template>
              <template #default>趋势图</template>
            </a-option>
            <template #content>
              展示指针随时间变化的趋势 <br>
              <img :src="trend" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="堆积图" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option v-if="timeParticle.timeParticleSize !== TimeParticleSize.TOTAL && groups.length" :value="ChartType.STACK">
              <template #icon>
                <img class="option-icon" src="/icon/stack-chart.svg" alt=""/>
              </template>
              <template #default>堆积图</template>
            </a-option>
            <template #content>
              展示指标整体以及各个分组的变化趋势 <br>
              <img :src="stacked" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="累计图" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option v-if="timeParticle.timeParticleSize !== TimeParticleSize.TOTAL" :value="ChartType.TOTAL">
              <template #icon>
                <img class="option-icon" src="/icon/total-chart.svg" alt=""/>
              </template>
              <template #default>累计图</template>
            </a-option>
            <template #content>
              展示指标累计值变化的趋势 <br>
              <img :src="total" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="分布图" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option :value="ChartType.DISTRIBUTION">
              <template #icon>
                <img class="option-icon" src="/icon/distribution-chart.svg" alt=""/>
              </template>
              <template #default>分布图</template>
            </a-option>
            <template #content>
              展示各个指标的分布情况 <br>
              <img :src="distribution" alt="" style="width: 90%;height: 90%;"/>
            </template>
          </a-popover>
          <a-popover title="表格" position="left" :content-style="{width:'260px'}" popup-container="#select">
            <a-option :value="ChartType.TABLE">
              <template #icon>
                <img class="option-icon" src="/icon/table-chart.svg" alt=""/>
              </template>
              <template #default>表格</template>
            </a-option>
            <template #content>
              <div>
                <p>展示详细数据</p>
                <img :src="table" alt="" style="width: 90%;height: 90%;"/>
              </div>
            </template>
          </a-popover>
        </a-select>
      </div>
    </div>
    <a-alert v-if="innerReportAnalysisData?.resultsExceedsLimit" style="margin-top: 5px;flex-shrink: 0;">
      因数据条数过多，优先展示前1000条数据
    </a-alert>
    <div :class="size=='middle'?'data-chart-middle':'data-chart-large'" :style="{height: chartHeight}">
      <div v-if="!showTable && showSummaryData" class="data-summary">
        <div class="date"> {{ lastData?.xValue || '--' }}({{ lastDataDayOfWeek }})</div>
        <div class="statistic">
          <div class="left-info">
            <div class="key">{{ formatYValue(lastData?.yValue) }}</div>
            <div class="compare">
              <a-popover
                  :content="huanBi?`对比 ${huanBiData?.startTime} 到 ${huanBiData?.endTime}，${huanBi && huanBi<0?'下降':'上涨'}了${Math.abs(huanBi)}%`:'无数据'"
                  :content-style="{ maxWidth: '260px' }"
                  position="tl"
              >
                <div class="compare-item">
                  <div class="cp_title">
                    <span class="title-label">日环比</span>
                  </div>
                  <div v-if="huanBi && huanBi<0" class="cp_value down">
                    <icon-caret-down class="icon"/>
                    <span>{{ huanBi !== undefined && formatYValue(huanBi) || '--' }}%</span>
                  </div>
                  <div v-else class="cp_value up">
                    <icon-caret-up class="icon"/>
                    <span>{{ huanBi !== undefined && formatYValue(huanBi) || '--' }}%</span>
                  </div>
                </div>
              </a-popover>
              <a-popover
                  :content="weekTongBi?`对比 ${weekTongBiData?.startTime} 到 ${weekTongBiData?.endTime}，${weekTongBi && weekTongBi<0?'下降':'上涨'}了${Math.abs(weekTongBi)}%`:'无数据'"
                  :content-style="{ maxWidth: '260px' }"
                  position="bl"
              >
                <div class="compare-item">
                  <div class="cp_title">
                    <span class="title-label">周同比</span>
                  </div>
                  <div v-if="weekTongBi && weekTongBi<0" class="cp_value down">
                    <icon-caret-down class="icon"/>
                    <span>{{ weekTongBi !== undefined && formatYValue(weekTongBi) || '--' }}%</span>
                  </div>
                  <div v-else class="cp_value up">
                    <icon-caret-up class="icon"/>
                    <span>{{ weekTongBi !== undefined && formatYValue(weekTongBi) || '--' }}%</span>
                  </div>
                </div>
              </a-popover>
            </div>
          </div>
          <div class="right-info">
            <div class="indicator">
              <div class="label"> 均值</div>
              <div class="value"> {{ formatYValue(summaryValue?.mean) }}</div>
            </div>
            <div class="indicator">
              <div class="label"> 总和</div>
              <div class="value"> {{ formatYValue(summaryValue?.sum) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="size==='middle' && !showTable && showSummaryData" class="chart-box" :style="{ width:'100%' ,height:'calc(100% - 80px)' }">
        <TrendChart v-show="showTrend" ref="trendChartRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <TableChart v-show="showTable" ref="tableRef" :model-type="modelType" :table-type="tableType" :time-format="timeParticle.timeParticleSize" :report-analysis-data="innerReportAnalysisData" :page-size="Math.round((props.report?.h - 4.25) / 0.67)"
                    @page-size-change="ps => emits('pageSizeChange',ps )"/>
        <Total v-show="showTotal" ref="totalRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <Distribution v-show="showDistribution" ref="distributionRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <Stacked v-show="showStacked" ref="stackedRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
      </div>
      <div v-else class="chart-box" :style="{ width: (size==='middle' || showTable || !showSummaryData) ? '100%' : 'calc(100% - 220px)',height: (size==='large' || showTable || !showSummaryData) ? '100%' : 'calc(100% - 80px)' }">
        <TrendChart v-show="showTrend" ref="trendChartRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <TableChart v-show="showTable" ref="tableRef" :model-type="modelType" :table-type="tableType" :time-format="timeParticle.timeParticleSize" :report-analysis-data="innerReportAnalysisData" :page-size="Math.round((props.report?.h - 4.25) / 0.67)"
                    @page-size-change="ps => emits('pageSizeChange',ps )"/>
        <Total v-show="showTotal" ref="totalRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <Distribution v-show="showDistribution" ref="distributionRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
        <Stacked v-show="showStacked" ref="stackedRef" :show-label="showLabel" :report-analysis-data="innerReportAnalysisData"/>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@import "@/views/dashboard/style/card.less";
.card-content {
  .data-chart-large {
    display: flex;
    flex: 1 1;
    align-items: flex-start;
    justify-content: space-between;

    .data-summary {
      width: 220px;
      height: 100%;
      padding-bottom: 12px;
      padding-top: 30px;

      .date {
        margin-bottom: 4px;
        color: var(--widget-color);
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
      }

      .statistic {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        .left-info {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          width: 100%;

          .key {
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            font-size: 40px;
            line-height: 40px;
          }

          .compare {
            margin-top: 20px;

            .compare-item {
              line-height: 20px;

              .cp_title {
                display: inline-block;
                text-align: left;

                .title-label {
                  color: var(--tant-text-gray-color-text1-2);
                  font-weight: 400;
                  font-size: 12px;
                  line-height: 12px;
                }
              }

              .cp_value {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                line-height: 22px;
                margin-left: 4px;

                .icon {
                  font-size: 12px;
                }
              }

              .up {
                color: var(--tant-status-success-color-success-default);
              }

              .down {
                color: var(--tant-status-danger-color-danger-default);
              }
            }
          }
        }

        .right-info {
          margin-top: 40px;

          .indicator {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            height: 40px;
            color: var(--tant-text-gray-color-text1-2);

            .label {
              margin-right: 8px;
              color: var(--tant-text-gray-color-text1-2);
              font-weight: 400;
              font-size: 12px;
              line-height: 12px;
              align-self: flex-end;
              padding-bottom: 4px;
            }

            .value {
              font-size: 32px;
            }
          }
        }
      }
    }
  }

  .data-chart-middle {
    .data-summary {
      height: 80px;
      padding: 12px 0;

      .date {
        margin-bottom: 4px;
        color: var(--widget-color);
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
      }

      .statistic {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .left-info {
          display: flex;
          align-items: flex-end;

          .key {
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            font-size: 40px;
            line-height: 40px;
          }

          .compare {
            display: table;
            margin-left: 16px;

            .compare-item {
              display: table-row;
              line-height: 20px;

              .cp_title {
                display: table-cell;
                text-align: right;

                .title-label {
                  color: var(--tant-text-gray-color-text1-2);
                  font-weight: 400;
                  font-size: 12px;
                  line-height: 12px;
                }
              }

              .cp_value {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                line-height: 22px;
                margin-left: 4px;

                .icon {
                  font-size: 12px;
                }
              }

              .up {
                color: var(--tant-status-success-color-success-default);
              }

              .down {
                color: var(--tant-status-danger-color-danger-default);
              }
            }
          }
        }

        .right-info {
          display: table;
          margin-right: 20px;
          padding-bottom: 4px;

          .indicator {
            display: table-row;
            color: var(--tant-text-gray-color-text1-1);
            font-size: 20px;
            line-height: 20px;

            .label {
              display: table-cell;
              text-align: right;
              padding-right: 8px;
              color: var(--tant-text-gray-color-text1-2);
              font-weight: 400;
              font-size: 12px;
              line-height: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
