<template>
  <div class="page">
    <div class="page-head">
      <div class="title">
        {{ route.meta.locale }}
      </div>
      <div class="filter">
        <div class="filter-item">
          <select-app />
        </div>
        <div class="filter-item">
          <a-select
            v-model:model-value="params.groupIds"
            :style="{ width: '200px', borderRadius: '4px' }"
            :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
            placeholder="选择分组"
            multiple
            allow-clear
            :tag-nowrap="true"
            :max-tag-count="2"
            :loading="groupLoading"
            @clear="groupChange"
            @remove="groupChange"
            @popup-visible-change="
              (v) => {
                if (!v && params.groupIds.length) groupChange();
              }
            "
          >
            <a-option v-for="item in groupList" :key="item.groupId" :value="item.groupId">{{ item.groupId }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <a-select v-model:model-value="params.indicator" :style="{ width: '200px', borderRadius: '4px' }" :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }" placeholder="选择指标" allow-clear @change="indicatorChange">
            <template #label="{ data }">
              <span>指标-{{ data?.label }}</span>
            </template>
            <a-option value="ltv">LTV</a-option>
            <a-option value="game_start">开局数</a-option>
            <a-option value="valid_call">有效调用次数</a-option>
            <a-option value="real_call">真实调用次数</a-option>
            <a-option value="retention">留存</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <!-- <a-input v-model:model-value="params.appVersion" placeholder="请输入版本号" style="width: 180px;" @change="versionChange"/> -->
          <a-select
            v-model:model-value="params.appVersion"
            :style="{ width: '200px', borderRadius: '4px' }"
            :trigger-props="{ autoFitPopupMinWidth: true, updateAtScroll: true }"
            placeholder="选择版本号"
            multiple
            allow-clear
            :tag-nowrap="true"
            :max-tag-count="2"
            :loading="versionLoading"
            @clear="versionChange"
            @remove="versionChange"
            @popup-visible-change="
              (v) => {
                if (!v && params.appVersion.length) versionChange();
              }
            "
          >
            <a-option v-for="item in versionList" :key="item.appVersion" :value="item.appVersion">{{ item.appVersion }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          <DateDropdown :time-span-data="params.timeSpan" @time-span-change="timeSpanChange" />
        </div>
        <div class="filter-item">
          <date-picker :date-range="params.dateRange" disabled-after-today @date-pick="datePick" />
        </div>
      </div>
    </div>
    <div class="page-body">
      <div class="table-content">
        <div class="group-content">
          <GroupTable ref="groupRef" />
        </div>
        <div class="indicator-content">
          <IndicatorTable ref="indicatorRef" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, watch} from 'vue';
import DatePicker from '@/components/date-picker/index.vue';
import {useEventBus,useSessionStorage} from '@vueuse/core';
import {LocalStorageEventBus} from '@/types/event-bus';
import {useRoute} from 'vue-router';
import DateDropdown from '@/views/analyse/components/dateDropdown.vue';
import selectApp from '@/components/selected-game-app/index.vue';
import {getStrategyGroupList, getStrategyVersionList} from '@/api/analyse/api';
import {usePageFilter} from '@/utils/filterConfigUtil';
import GroupTable from './components/GroupTable.vue';
import IndicatorTable from './components/IndicatorTable.vue';

const localStorageEventBus = useEventBus(LocalStorageEventBus);
const appIdRef = useSessionStorage('app-id', '');
  const route = useRoute();
  const groupLoading = ref(false);
  const groupList = ref<any>();
  const versionLoading = ref(false);
  const versionList = ref<any>();

  const params = reactive({
    appId: appIdRef,
    dateRange: {
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天',
    },
    timeSpan: {
      firstDayDfWeek: 1,
      number: 7,
      unit: 'DAY',
    },
    appVersion: [],
    groupIds: [],
    indicator: 'ltv',
  });
  // 筛选条件保存工具
  const { saveFilter, saveFilterDebounced } = usePageFilter(params);
  const getGroupList = async () => {
    groupLoading.value = true;
    try {
      const { groupIds, ...restParams } = params;
      const res = await getStrategyGroupList({
        ...restParams,
      });
      groupList.value = res;
    } catch (e) {
      console.error(e);
    } finally {
      groupLoading.value = false;
    }
  };
  getGroupList();
  const getVersionList = async () => {
    versionLoading.value = true;
    try {
      const { groupIds, appVersion, ...restParams } = params;
      const res = await getStrategyVersionList({
        ...restParams,
      });
      versionList.value = res;
    } catch (e) {
      console.error(e);
    } finally {
      versionLoading.value = false;
    }
  };
  getVersionList();
  const groupRef = ref();
  const indicatorRef = ref();

  const indicatorChange = () => {
    indicatorRef.value.getData(params);
  };
  
  const refresh = async() => { 
    const { indicator, ...restParams } = params;
    await Promise.all([groupRef.value.getData(restParams), indicatorRef.value.getData(params)]);
  };
  const datePick = (date: any) => {
    params.dateRange = date;
    getGroupList();
    getVersionList();
    refresh();
  };
  const init = async () => {
    // 优先从路由配置中获取已保存的筛选条件
    const savedConfig = route.meta?.pageConfig;
    if (savedConfig) {
      // 应用保存的筛选条件
      Object.assign(params, { ...savedConfig });
    }
    refresh();
  };
  const timeSpanChange = (v) => {
    params.timeSpan = v;
    getGroupList();
    getVersionList();
    refresh();
  };
  const versionChange = () => {
    getGroupList();
    refresh();
  };
  const groupChange = () => {
    refresh();
  };
  onMounted(() => {
    init();
  });
  localStorageEventBus.on((name, value) => {
    if (name === 'app-id') {
      params.appId = value
      getGroupList();
      getVersionList();
      refresh();
    }
  });
  // 参数变化时自动保存（防抖）
  watch(
    params,
    () => {
      saveFilterDebounced();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .table-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    .group-content {
      width: 100%;
      margin-bottom: 24px;
      min-height: 120px;
      max-height: 260px;
      flex-shrink: 0;
    }
    .indicator-content {
      flex: 1;
      min-height: 0;
    }
  }
</style>
