<template>
  <div class="date-picker">
    <div class="header">
      <div class="title">日期范围</div>
      <div class="preview">
        <span>
          {{ dateText }}
        </span>
        <span v-if="startRangeType==='dynamic' || endRangeType==='dynamic'">
          <span v-if="dateStringList[1] === dateStringList[0]" class="date-range">
            ({{ dateStringList[0] }})
          </span>
          <span v-else class="date-range">
            ({{ dateStringList[0] }}<icon-arrow-right/>{{ dateStringList[1] }})
          </span>
        </span>
      </div>
    </div>
    <div class="piker">
      <date-quick-pick-panel
          class="quick-pick-panel"
          :date-range="dateRangeComputed"
          @quick-pick="quickPickDate"/>
      <div class="picker-box">
        <div class="picker-body">
          <div class="range-box">
            <div class="range-radio-group">
              <div class="range-radio">
                <a-radio-group type="button" :model-value="startRangeType" @change="startDateTypeChange">
                  <a-radio v-if="endRangeType ==='dynamic'" value="dynamic">动态日期</a-radio>
                  <a-radio value="static">静态日期</a-radio>
                </a-radio-group>
              </div>
              <div class="range-radio">
                <a-radio-group type="button" :model-value="endRangeType" @change="endRDateTypeChange">
                  <a-radio value="dynamic">动态日期</a-radio>
                  <a-radio value="static">静态日期</a-radio>
                </a-radio-group>
              </div>
            </div>
            <div class="range-input-group">
              <div class="range-input">
                <a-input
                    v-if="startRangeType == 'dynamic'" v-model:model-value="dynamicDateList[0]"
                    class="range-input-box"
                    @input="handleDynamicDateInput(0, $event)"></a-input>
                <a-input
                    v-if="startRangeType == 'static'" v-model:model-value="staticDateList[0]"
                    class="range-input-box"
                    @blur="handleStaticDateInput(0)"></a-input>
                <div class="range-suffix">
                  <span v-if="startRangeType == 'dynamic' && dayjs(staticDateList[0]).unix()<dayjs().unix()" class="range-suffix-span">天前</span>
                  <a-tag v-if="dayjs(staticDateList[0]).unix()>dayjs().unix()" color="blue" class="range-suffix-tag">未来</a-tag>
                </div>
                <div class="arrow">
                  <icon-arrow-right/>
                </div>
              </div>
              <div class="range-input">
                <a-input
                    v-if="endRangeType == 'dynamic'" v-model:model-value="dynamicDateList[1]"
                    class="range-input-box"
                    @input="handleDynamicDateInput(1, $event)"></a-input>
                <a-input
                    v-if="endRangeType == 'static'" v-model:model-value="staticDateList[1]"
                    class="range-input-box"
                    @blur="handleStaticDateInput(1)"></a-input>
                <div class="range-suffix">
                  <span v-if="endRangeType == 'dynamic' && dayjs(staticDateList[0]).unix()<dayjs().unix()" class="range-suffix-span">天前</span>
                  <a-tag v-if="dayjs(staticDateList[1]).unix()>dayjs().unix()" color="blue" class="range-suffix-tag">未来</a-tag>
                </div>
              </div>
            </div>
          </div>
          <div class="pick-date">
            <a-range-picker
                hide-trigger
                :model-value="dateStringList.slice(0,2)"
                :picker-value="dateStringList.slice(0,2)"
                :day-start-of-week="1"
                :disabled-date="current => (endRangeType ==='dynamic' || props.disabledAfterToday) && dayjs(current).isAfter(dayjs())"
                format={“YYYY-MM-DD”}
                @select="datePickerSelect"
                @change="datePickerChange"
                @picker-value-change="datePickerPageChange"
            />
          </div>
        </div>
        <div class="picker-footer">
          <div class="tips">
            <a-alert
                v-if="dayjs(dateStringList[0]).unix()>dayjs().unix() && dayjs(dateStringList[0]).unix()<=dayjs(dateStringList[1]).unix()"
                type="info" show-icon>开始日期晚于今天，可能会导致查询无数据
            </a-alert>
            <a-alert
                v-if="dayjs(dateStringList[1] && dateStringList[0]).unix()>dayjs(dateStringList[1]).unix()"
                message="Warning" type="warning" show-icon>结束日期早于开始日期，{{ diffDate }}天后才会查询
            </a-alert>
          </div>
          <a-button type="primary" @click="datePick">确定</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue';
import dayjs from 'dayjs';
import DateQuickPickPanel from "@/components/date-picker/components/DateQuickPickPanel.vue";
import {DateRange} from "@/api/analyse/type";
import {dateDynamicToStatic, dateStaticToDynamic} from "@/utils/dateUtil";

interface Props {
  dateRange: DateRange
  // 是否禁用今天以后的日期
  disabledAfterToday?: boolean
}

const emits = defineEmits(['datePick']);

const props = withDefaults(defineProps<Props>(), {
  dateRange: {}, // 传入日期
  disabledAfterToday: false
})

// 当天日期
const nowDateString = dayjs().format('YYYY-MM-DD');
// 选择器日期范围，默认当天
const dateStringList = ref([nowDateString, nowDateString]);
// 静态日期范围，默认当天
const staticDateList = ref([nowDateString, nowDateString]);
// 动态日期范围，默认当天
const dynamicDateList = ref<any>([0, 0])
// 是否展示动态开始日期选择
const showStartDynamicRadio = ref(true)
// 日期范围类型
const startRangeType = ref<string>('static')
const endRangeType = ref<string>('dynamic')
// 日期文本
const dateText = ref('')

/**
 * 日期文本处理
 */
const formatDateText = () => {
  // 动态时间特殊处理
  if (endRangeType.value === 'dynamic') {
    // 自某日至今
    if (dynamicDateList.value[1] === 0) {
      if (dynamicDateList.value[0] && dynamicDateList.value[0] > 0) {
        if (dynamicDateList.value[0] === 0) {
          dateText.value = '今日'
          return
        }
        dateText.value = `最近${dynamicDateList.value[0] + 1}天`
        return
      }
    }

    // 自某日至昨日
    if (dynamicDateList.value[1] === 1) {
      if (dynamicDateList.value[0] && dynamicDateList.value[0] > 0) {
        if (dynamicDateList.value[0] === 1) {
          dateText.value = '昨日'
          return
        }
        dateText.value = `过去${dynamicDateList.value[0]}天`
        return
      }
    }
  }

  const startText = startRangeType.value === 'dynamic' ?
      dynamicDateList.value[0] === 0 ? '今日' :
          dynamicDateList.value[0] === 1 ? '昨日' :
              `${dynamicDateList.value[0]}天前`
      : staticDateList.value[0]
  const endText = endRangeType.value === 'dynamic' ?
      dynamicDateList.value[1] === 0 ? '今日' :
          dynamicDateList.value[1] === 1 ? '昨日' :
              `${dynamicDateList.value[1]}天前`
      : staticDateList.value[1]
  dateText.value = startText === endText ? startText : `${startText} -> ${endText}`
}

// 时间范围
const dateRangeComputed = computed<DateRange>(() => {
  formatDateText()
  return {
    startDate: startRangeType.value === 'static' ? staticDateList.value[0] : undefined,
    endDate: endRangeType.value === 'static' ? staticDateList.value[1] : undefined,
    recentStartDate: startRangeType.value === 'dynamic' ? dynamicDateList.value[0] : undefined,
    recentEndDate: endRangeType.value === 'dynamic' ? dynamicDateList.value[1] : undefined,
    dateText: dateText.value
  }
})

/**
 * 验证输入框日期合法性
 * @param input 输入值
 */
const isValidDateFormat = (input: string): boolean => {
  const regex = /^\d{4}[/]\d{1,2}[/]\d{1,2}$/;
  if (!regex.test(input)) return false;
  const [year, month, day] = input.split(/[/]/).map(Number);
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year &&
      date.getMonth() === month - 1 &&
      date.getDate() === day;
};

/**
 * 日期范围相差数
 */
const diffDate = computed(() => {
  const startDiff = dayjs(dateStringList.value[0]);
  const endDiff = dayjs(dateStringList.value[1]);
  const difference = startDiff.diff(endDiff, 'day');
  // 返回绝对值，确保结果始终为正数
  return Math.abs(difference);
})

/**
 * 处理输入  静态日期  框日期格式
 * @param index
 */
const handleStaticDateInput = (index: number) => {
  const input = staticDateList.value[index];
  if (isValidDateFormat(input)) {
    staticDateList.value[index] = input
    dateStringList.value[index] = input; // 更新原始日期字符串
  } else {
    staticDateList.value[index] = dateStringList.value[index]; // 恢复为有效值
  }
};

/**
 * 处理 动态日期 只限制输入数字
 * @param index
 * @param value
 */
const handleDynamicDateInput = (index: number, value: string) => {
  if (!/^\d+$/.test(value) && value) {
    dynamicDateList.value[index] = dateStaticToDynamic(dateStringList.value[index]);
  } else {
    // 如果是纯数字，保持原值
    dynamicDateList.value[index] = value;
    dateStringList.value[index] = dateDynamicToStatic(value)
  }
}

/**
 * 开始日期类型改变
 */
const startDateTypeChange = (value: any) => {
  startRangeType.value = value
  // 开始日期大于当前日期时，切换日期状态恢复默认值今天
  if (dayjs(dateStringList.value[0]).unix() > dayjs().unix()) {
    const dateString = dayjs().format('YYYY-MM-DD');
    dateStringList.value[0] = dateString
    staticDateList.value[0] = dateString
    dynamicDateList.value[0] = dateStaticToDynamic(dateString)
  }
}

/**
 * 结束日期类型改变
 */
const endRDateTypeChange = (value: any) => {
  endRangeType.value = value
  // 结束日期大于当前日期时，切换日期状态恢复默认值今天
  if (value === 'dynamic' && dayjs(dateStringList.value[1]).unix() > dayjs().unix()) {
    const dateString = dayjs().format('YYYY-MM-DD');
    dateStringList.value[1] = dateString
    staticDateList.value[1] = dateString
    if (dayjs(dateStringList.value[0]).unix() > dayjs().unix()) {
      dateStringList.value[0] = dateString
      staticDateList.value[0] = dateString
    }
  }
  if (value === 'static') {
    startRangeType.value = 'static'
  }
  dynamicDateList.value[1] = dateStaticToDynamic(dateStringList.value[1])
}

/**
 * 开始日期选择
 */
const startChange = (v) => {
  dateStringList.value[0] = v;
  staticDateList.value[0] = dayjs(v).format('YYYY-MM-DD')
  // 动态日期
  dynamicDateList.value[0] = dateStaticToDynamic(v)
  dateText.value = ''
  if (dayjs(dateStringList.value[0]).unix() > dayjs(dateStringList.value[1]).unix() && startRangeType.value === 'static' && endRangeType.value === 'static') {
    staticDateList.value[1] = dayjs(v).format('YYYY-MM-DD')
    // 动态日期
    dynamicDateList.value[1] = dateStaticToDynamic(v)
    dateStringList.value[1] = v
  }
}

/**
 * 结束日期选择
 * @param v
 */
const endChange = (v) => {
  dateStringList.value[1] = v;
  staticDateList.value[1] = dayjs(v).format('YYYY-MM-DD')
  dynamicDateList.value[1] = dateStaticToDynamic(v)
  dateText.value = ''
}

/**
 * arco日期选择面板选择
 */
const datePickerSelect = (dateStr) => {
  if (dateStr?.length === 1) {
    startChange(dateStr[0]);
    endChange(dateStr[0])
  }
  if (dateStr?.length === 2 && dateStr[0] === undefined) {
    startChange(dateStr[1]);
    endChange(dateStr[1])
  }
}

/**
 * arco日期选择面板值变化
 */
const datePickerChange = (dateStr) => {
  if (dateStr?.length === 2) {
    startChange(dateStr[0]);
    endChange(dateStr[1]);
  }
}

/**
 * arco日期选择面板页面变化
 */
const datePickerPageChange = (dateStr) => {
  if (dateStr?.length === 2) {
    startChange(dateStr[0]);
    endChange(dateStr[1]);
    if (dayjs(dateStr[1]).unix() > dayjs().unix()) {
      endRDateTypeChange('static')
    }
  }
}

/**
 * 刷新日期范围
 */
function refreshDateRange(newDateRange) {
  const recentStartDate = newDateRange?.recentStartDate;
  const recentEndDate = newDateRange?.recentEndDate;
  const startDate = newDateRange?.startDate || nowDateString;
  const endDate = newDateRange?.endDate || nowDateString;

  if (recentEndDate === null || recentEndDate === undefined) {
    // 无动态日期
    staticDateList.value[0] = startDate
    staticDateList.value[1] = endDate
    dateStringList.value[0] = startDate
    dateStringList.value[1] = endDate
    dynamicDateList.value[0] = dateStaticToDynamic(startDate)
    dynamicDateList.value[1] = dateStaticToDynamic(endDate)
    startRangeType.value = 'static'
    endRangeType.value = 'static'
    showStartDynamicRadio.value = false
  } else {
    // 动态结束日期
    dynamicDateList.value[1] = recentEndDate
    staticDateList.value[1] = dateDynamicToStatic(recentEndDate)
    dateStringList.value[1] = dateDynamicToStatic(recentEndDate)
    endRangeType.value = 'dynamic'
    showStartDynamicRadio.value = true
    if (recentStartDate === null || recentStartDate === undefined) {
      // 静态开始日期
      staticDateList.value[0] = startDate
      dateStringList.value[0] = startDate
      dynamicDateList.value[0] = dateStaticToDynamic(startDate)
      startRangeType.value = 'static'
    } else {
      // 动态开始日期
      staticDateList.value[0] = dateDynamicToStatic(recentStartDate)
      dateStringList.value[0] = dateDynamicToStatic(recentStartDate)
      dynamicDateList.value[0] = recentStartDate
      startRangeType.value = 'dynamic'
    }
  }
}

/**
 * 快速选择日期范围
 */
const quickPickDate = (newDateRange) => {
  refreshDateRange(newDateRange)
}

/**
 * 初始化
 */
onMounted(() => {
  refreshDateRange(props.dateRange);
})

// 日期范围确认
const datePick = () => {
  emits('datePick', dateRangeComputed.value)
};
</script>

<style scoped lang="less">
.date-picker {
  width: 800px;
  min-height: 380px;
  color: var(--tant-text-gray-color-text1-2);
  font-size: 14px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 2px;
  box-shadow: var(--tant-small-shadow-small-overall);

  .header {
    background-color: #fff;
    width: 100%;
    height: 62px;
    padding: 13px 24px;
    overflow: hidden;
    border-bottom: 1px solid var(--tant-border-color-border1-1);

    .title {
      color: var(--tant-text-gray-color-text1-3);
      font-size: 14px;
    }

    .preview {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: var(--tant-text-gray-color-text1-1);
      font: var(--tant-header-font-header5-medium);

      .date-range {
        margin-left: 4px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;
      }
    }
  }

  .piker {
    width: 100%;
    display: grid;
    grid-template-columns: 23% 77%;

    .quick-pick-panel {
      display: flex;
      gap: 7px;
      flex-flow: row wrap;
      align-content: flex-start;
      background-color: var(--tant-bg-gray-color-bg2-1);
      width: 184px;
      padding: 16px;
    }

    .picker-box {

      width: 100%;
      background-color: var(--tant-bg-white-color-bg1-1);

      .bgColor :deep(.arco-picker-cell-selected .arco-picker-date-value) {
        background-color: var(--tant-status-warning-color-warning-default) !important;
      }

      :deep(.arco-radio-button) {
        background: #fff;
      }

      :deep(.arco-radio-checked) {
        color: var(--tant-text-gray-color-text1-2);
        background: transparent;
        font-weight: bolder;
      }

      .picker-body {
        padding: 16px 12px;
        width: 100%;
        .range-box {
          width: 100%;
          box-sizing: border-box;
          margin-bottom: 8px;

          .range-radio-group {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding-bottom: 16px;

            .range-radio {
              width: 50%;

              &:last-child {
                padding-left: 16px;
              }
            }
          }

          .range-input-group {
            display: flex;
            align-items: center;

            .range-input {
              display: flex;
              align-items: center;
              width: 50%;

              .range-input-box {
                width: 70%;
              }

              .range-suffix {
                width: 30%;

                .range-suffix-span {
                  display: block;
                  margin-left: 8px;
                  // line-height: 28px;
                  font-size: 14px;
                  color: var(--tant-text-gray-color-text1-2);
                }

                .range-suffix-tag {
                  background-color: var(--tant-status-info-color-info-fill);
                  color: var(--tant-status-info-color-info-default);
                  font: var(--tant-body-font-body-regular);
                  margin-left: 8px;
                  height: 22px;
                  padding: 0 8px;
                  line-height: 22px;
                  display: inline-block;
                }
              }

              .arrow {
                //margin: 0 16px;
                font-size: 20px;
              }

              &:last-child {
                padding-left: 16px;
              }
            }
          }
        }
        .pick-date {
          display: flex;
          width: 100%;
          height: 300px;
          .content-picker {
            width: 100%;
            // min-height: 385px;
            // padding: 5px;
            box-sizing: border-box;

            &:nth-child(1) {
              padding-right: 16px;
            }

            :deep(.arco-picker-footer-now-wrapper) {
              display: none;
            }

            :deep(.arco-picker-container, .arco-picker-range-container) {
              border: none;
            }
          }

          .arco-picker-range-container {
            width: 100%;
          }
        }

        :deep(.arco-panel-date) {
          margin-right: 0;

          &:last-child {
            margin-right: 0;
          }
        }

        :deep(.arco-picker-header) {
          border-bottom: 0;
        }

        :deep(.arco-picker-header) {
          border-bottom: 0;
        }

        :deep(.arco-picker-header) {
          padding-left: 0;
        }

        :deep(.arco-picker-week-list) {
          padding-left: 0;
        }

        :deep(.arco-panel-date) {
          &:first-child {
            .arco-picker-body, .arco-picker-week-list, .arco-picker-header {
              padding-right: 16px;
              padding-left: 0;
            }
          }

          &:last-child {
            .arco-picker-body, .arco-picker-week-list, .arco-picker-header {
              padding-right: 0;
              padding-left: 16px;
            }
          }
        }
      }
    }
  }
}

.picker-footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 52px;
  padding: 8px 12px;
  border-top: 1px solid var(--tant-border-color-border1-1);
}

.arco-picker-container, .arco-picker-range-container {
  border: none;
}

:deep(.arco-picker-range-container-panel-only .arco-panel-date ) {
  margin-right: 20px;
}
</style>

