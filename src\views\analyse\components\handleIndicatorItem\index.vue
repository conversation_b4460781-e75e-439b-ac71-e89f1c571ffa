<template>
    <div v-for="(item,index) in eventListData" :key="index" class="action-row">
      <div class="filter-row-eventRow">
        <div class="action-left">
          <div class="row-content">
            <div v-if="item.rename" class="rename">
              <a-input v-model="item.displayName" placeholder="请输入" @blur="reNameBlur(index)"/>
            </div>
            <div v-if="item.isBasic && item.eventList?.length" class="event-element">
                <EventIndicatorSelect :disabled="disabled" :panel-data="item.eventList[0]" @analysis-index-change="panelSelectChange(index,0,$event)"/>
                <span v-if="props.showSub && item.eventList[0].type == 'event'" class="row-word">的</span>
                <subSelect v-if="props.showSub && item.eventList[0].type == 'event'" :disabled="disabled" :is-group="isGroup" :panel-data="item.eventList[0]" :is-scatter="props.isScatter" @sub-change="subChange(index,0,$event)"/>
                <a-tooltip content="切换自定义公式" position="top">
                  <a-button v-if="props.isCustom && !disabled" class="custom-edit-btn btn-26" style="margin-left: 8px;" @click="editCustomIndicator(index)">
                    <template #icon>
                        <icon-formula/>
                    </template>
                    </a-button>
                </a-tooltip>
            </div>
            <!-- 自定义 -->
            <a-tooltip content='点击编辑自定义指标' position="tl">
                <custom-indicator-display v-if="!item.isBasic && item.eventList?.length" :allow-hover="true" :indicator="item" @click="editCustomIndicator(index)"/>
            </a-tooltip>
          </div>
        </div>
        <div class="action-right">
          <a-space v-if="!disabled" align="center">
            <numberGroupSet v-if="props.showNumerSet" @number-set="numberSet(index,$event)"/>
            <a-tooltip content="重命名" position="top">
              <a-button v-if="props.showRename" class="btn-bg btn-26" @click="() => item.rename = true">
                <template #icon>
                  <icon-pen/>
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip content="添加筛选" position="top">
              <a-button class="btn-bg btn-26" @click="add(index)">
                <template #icon>
                  <icon-filter/>
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip content="开启指标编辑" position="top">
              <a-button v-if="props.showSub && props.showEdit" class="btn-bg btn-26" @click="editIndex">
                <template #icon>
                  <icon-formula/>
                </template>
              </a-button>
            </a-tooltip>
            <a-tooltip v-if="props.showDel" content="删除" position="top">
              <a-button class="btn-bg-delete btn-26" @click="deleteEventList(index)">
                <template #icon>
                  <icon-delete/>
                </template>
              </a-button>
            </a-tooltip>
          </a-space>
        </div>
      </div>
      <a-dropdown v-if="!item.isBasic && props.showDisplayType" @select="handleUnitSelect(index,$event)">
        <div class="filter-btn">
          <span class="filter-label">{{ item.unitName }}</span>
        </div>
        <template #content>
          <a-doption :value="{obj:{type:'default',decimalNum:2,thousandSep: 1},name:'2位小数'}">2位小数</a-doption>
          <a-doption :value="{obj:{type:'default',decimalNum:3,thousandSep: 1},name:'3位小数'}">3位小数</a-doption>
          <a-doption :value="{obj:{type:'default',decimalNum:4,thousandSep: 1},name:'4位小数'}">4位小数</a-doption>
          <a-doption :value="{obj:{type:'percent',decimalNum:2,thousandSep: 1},name:'百分比'}">百分比</a-doption>
          <a-doption :value="{obj:{type:'default',decimalNum:0,thousandSep: 1},name:'取整'}">取整</a-doption>
        </template>
      </a-dropdown>
      <eventQueryFilter
        :ref="el => queryFilterRefs[index] = el"
        :filter="item.filter"
        :disabled="disabled"
        :code-list="item.eventList"
        :show-detail-filter="true"
        :only-event="props.onlyEvent"
        @query-filters-change="queryFiltersChange(index,$event)"/>
      <div v-if="item.filtersList&&item.filtersList.length" class="row-foot">
        <div class="ta-filter-button" @click="add(index)">
          <icon-plus class="action"/>
          <span class="label">筛选条件</span>
        </div>
      </div>
      <edit-custom-indicator-modal
          v-if="editCustomIndicatorVisible"
          v-model:visible="editCustomIndicatorVisible"
          :indicator="eventListData[operateIndicatorIndex]"
          @confirm="editCustomIndicatorConfirm"/>
    </div>
  </template>
  
  <script setup lang="ts">
  import '@/views/analyse/css/analyse.less'
  import {ref, watch} from "vue";
  import _ from 'lodash';
  import {Indicator} from "@/api/analyse/type";
  import {toolStore} from '@/store';
  import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
  import subSelect from "@/views/analyse/components/SubSelect.vue"
  import eventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"
  import CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
  import numberGroupSet from "@/views/analyse/components/numberGroupSet.vue"
  import {getDefaultObj} from "@/views/analyse/components/util/verify";
  import EditCustomIndicatorModal from "./EditCustomIndicatorModal.vue";

  const toolData = toolStore();
  const props = defineProps({
    handleList: {
      type: Array,
      default: () => []
    },
    isCustom: {
      type: Boolean,
      default: false
    },
    showSub: {
      type: Boolean,
      default: false
    },
  //  是否重命名
    showRename: {
      type: Boolean,
      default: false
    },
  //   是否开启指标编辑
    showEdit: {
      type: Boolean,
      default: false
    },
    onlyEvent: {
      type: Boolean,
      default: false
    },
    showDel: {
      type: Boolean,
      default: false
    },
  //   是否提供选择displaytype
    showDisplayType: {
      type: Boolean,
      default: false
    },
    showNumerSet:{
      type:Boolean,
      default:false
    },
    isScatter:{
      type:Boolean,
      default:false
    },
    isGroup:{
      type:Boolean,
      default:false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  })
  
  const eventListData = ref<any>([])
  const evtLists = ref<any>([])
  // 当前操作指标
  const operateIndicatorIndex = ref<number>(0);
  
  // 自定义指标编辑显示
  const editCustomIndicatorVisible = ref<boolean>(false)
  
  // 内部更新标记，避免监听循环
  let isInternalUpdate = false;
  
  
  const queryFiltersChange = (index: number, v: any) => {
    eventListData.value[index].filter = v;
  }
  const init = async () => {
    evtLists.value = toolData.toolModelList.length > 0 
    ? toolData.toolModelList.flatMap((category: any) => category.items || [])
    : (await toolData.fetchAllModalList()).flatMap((category: any) => category.items || []);
    const attrName = props.isScatter ? '次数' : '总次数'
    const appOpenData = getDefaultObj(evtLists.value)
    const list = [
      {
        name: '',
        rename: false,
        type: 'event',
        isBasic: true,
        displayType: {type: 'default', decimalNum: 2, thousandSep: 1},
        displayName: '',
        eventList: [
          {
            eventName: appOpenData.name,
            eventCode: appOpenData.code,
            eventDisplayName: appOpenData.displayName,
            eventType:appOpenData.type,
            type: appOpenData.objectType,
            eventAttrCode: '',
            eventAttrName: appOpenData.objectType === 'event' ? attrName : '',
            filter: {}
          },
        ],
        formula: '',
        filter: {},
        description: ''
      }
    ]
    if (props.handleList.length > 0) {
      const pList = _.cloneDeep(props.handleList).map((item: any) => {
        return{
          ...item,
          eventList: item.eventList.map((el: any) => {
            return {
              ...el,
              eventDisplayName: el.eventDisplayName || el.indicatorDisplayName,
              eventName: el.eventName || el.indicatorName,
              eventCode: el.eventCode || el.indicatorCode,
            }
          }),
        }
      })
      eventListData.value = _.cloneDeep(pList)
    } else {
      eventListData.value = _.cloneDeep(list)
    }
  }
  // init()
  watch(
      () => props.handleList,
      (newVal) => {
        if (isInternalUpdate) {
          isInternalUpdate = false;
          return;
        }
        init()   
      },
      { immediate: true,deep:true }
    );
  
  const emits = defineEmits(['indicatorsChange', 'deleteList', 'openEditModal'])
  
  const indicator = ref<Indicator[]>([])
  
  const reNameBlur = (index: number) => {
    if (eventListData.value[index].displayName === '') {
      eventListData.value[index].rename = false
    }
  }
  const handleUnitSelect = (index: number, v: any) => {
    eventListData.value[index].unitName = v.name
    eventListData.value[index].displayType = v.obj
  }
  const panelSelectChange = (index: number, eventIndex: number, e: any) => {
    const filter = {
      ...eventListData.value[index].eventList[eventIndex],
      ...e
    }
    const fieldsToRemove = e.type === 'indicator'
      ? ['eventCode', 'eventName', 'eventDisplayName']
      : ['indicatorCode', 'indicatorName', 'indicatorDisplayName'];
  
    // 从filter中移除指定的字段，包括额外需要移除的属性
    const cleanedFilter = _.omit(filter, [
      ...fieldsToRemove,
      'eventAttrCode',
      'eventAttrDisplayName',
      'statisticalName',
      'statisticalType'
    ]);
    eventListData.value[index].eventList[eventIndex] = cleanedFilter;
    eventListData.value[index].name = e.eventName || e.indicatorName
    const attrName = props.isScatter ? '次数' : '总次数'
    eventListData.value[index].eventList[eventIndex].eventAttrName = filter.type === 'event' ? attrName : ''
  }
  
  const subChange = (index: number, eventIndex: number, e: any) => {
    const {eventAttrName, eventAttrCode, eventAttrDisplayName, statisticalName, statisticalType, numRule,objectType} = e
    const filter = {...eventListData.value[index].eventList[eventIndex]};
    filter.objectType = objectType
    filter.eventAttrName = eventAttrName;
    filter.eventAttrCode = eventAttrCode;
    filter.eventAttrDisplayName = eventAttrDisplayName
    if (statisticalName) {
      filter.statisticalName = statisticalName;
    } else {
        delete filter?.statisticalName;
    }
    if (statisticalType) {
      filter.statisticalType = statisticalType;
    } else {
      delete filter?.statisticalType;
    }
    eventListData.value[index].eventList[eventIndex] = filter;
  
  }
  const numberSet = (index:number,e:any) => {
    eventListData.value[index].numberSummaryType = e.numberSummaryType
    eventListData.value[index].numberSummaryScope = e.numberSummaryScope
  }
  // 添加并列条件
  const queryFilterRefs = ref<any>([])
  const add = (index1: number) => {
    queryFilterRefs.value[index1].add()
  }
  
  const editIndex = () => {
    emits('openEditModal')
  }
  const deleteEventList = (index: number) => {
    eventListData.value.splice(index, 1)
    emits('deleteList', index)
  
  }

  watch(eventListData, (newValue, oldValue) => {
    indicator.value = newValue.map((item: any) => {
      return {
        displayName: item.displayName,
        displayType: item.displayType,
        name: item.name,
        type: item.type,
        isBasic: item.isBasic,
        formula: item.isBasic ? '' : item.formula,
        eventList: item.eventList.map((event: any) => {
          const processedEvent = { ...event };
          if (processedEvent.type === 'indicator') {
            delete processedEvent.eventCode;
            delete processedEvent.eventName;
            delete processedEvent.eventDisplayName;
          } else {
            delete processedEvent.indicatorCode;
            delete processedEvent.indicatorName;
            delete processedEvent.indicatorDisplayName;
          }
          return processedEvent;
        }),
        filter: item.filter
      }
    })
    // 设置内部更新标记，避免监听循环
    isInternalUpdate = true;
    emits('indicatorsChange', indicator.value)
  }, {deep: true, immediate: true})
    /**
   * 编辑自定义指标
   */
   const editCustomIndicator = (index: number) => {
    if ( props.disabled ) return
    operateIndicatorIndex.value = index
    editCustomIndicatorVisible.value = true
  }
  
  /**
   * 自定义指标编辑确认
   */
  const editCustomIndicatorConfirm = (customIndicator: any) => {
    const {eventList, formula, displayType} = customIndicator;
    eventListData.value[operateIndicatorIndex.value].isBasic = formula === 'A'
    eventListData.value[operateIndicatorIndex.value].eventList = eventList
    eventListData.value[operateIndicatorIndex.value].formula = formula
    eventListData.value[operateIndicatorIndex.value].displayType = displayType
  }
  </script>
  
  <style scoped lang="less">
  .action-row {
    position: relative;
    height: auto;
    width: 100%;
    min-height: 24px;
    line-height: 24px;
    padding-right: 24px;
    padding-left: 24px;
  
    .action-left {
      align-items: flex-start;
      height: auto;
      display: flex;
  
      .row-content {
        flex-grow: 1;
        box-sizing: border-box;
        padding: 4px 0;
  
        .rename {
          min-width: 80px;
          max-width: calc(100% - 50px);
          height: 24px;
          padding: 0;
          line-height: 24px;
          background: inherit;
          margin-bottom: 6px;
          // font-weight: 600;
          font-size: 14px;
          display: flex;
          align-items: center;
  
          .placeholder {
            max-width: 260px;
            display: inline-block;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
            overflow: hidden;
            font-size: 14px;
            // white-space: pre;
            // vertical-align: middle;
            &:hover {
              color: var(--tant-primary-color-primary-default);
            }
          }
  
          :deep(.arco-input-wrapper .arco-input.arco-input-size-medium) {
            font-weight: 600 !important;
          }
  
          :deep(.arco-input-wrapper) {
            border: none;
            background-color: transparent;
            font-weight: 600;
  
            &:hover {
              border: none;
              background-color: transparent;
              color: var(--tant-primary-color-primary-default);
            }
          }
        }
  
        .event-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          padding: 4px 0;
          overflow: hidden;
          line-height: 32px;
          white-space: normal;
        }
        .event-element {
            display: flex;
            align-items: center;
        }
      }
    }
  
    .action-right {
      position: absolute;
      top: 0;
      right: 24px;
      min-width: 40px;
      height: 36px;
      padding-top: 0 !important;
      display: flex;
      align-items: center;
      opacity: 0;
      transition: opacity .3s;
    }
  
    .filter-btn {
      display: inline-flex;
      align-items: center;
      min-width: 40px;
      max-width: 200px;
      height: 26px;
      padding: 0 8px;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: top;
      background-color: var(--tant-secondary-color-secondary-fill);
      border: 1px solid transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: all .3s;
      box-sizing: border-box;
  
      .btn-icon {
        color: var(--tant-text-gray-color-text1-2);
        font-size: 14px;
        margin-right: 5px;
      }
  
      .filter-label {
        display: inline-block;
        max-width: 300px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: top;
      }
  
      &:hover {
        border-color: var(--tant-primary-color-primary-hover);
      }
    }
  
    .row-word {
      display: inline-block;
      margin: 0 4px;
      color: var(--tant-text-gray-color-text1-2);
      vertical-align: top;
    }
  
    &:hover {
      background-color: var(--tant-fill-color-fill1-2);
  
      :deep(.filter-btn) {
        background: #fff;
      }
  
      :deep(.filter-icon) {
        background: #fff;
      }
  
      .sub-action-left :deep(.filter-btn) {
        background: #fff;
      }
      :deep(.select-btn) {
          background: #fff;
      }
      .action-right {
        opacity: 1;
      }
    }
  }
  
  .btn-bg {
    background-color: transparent;
  
    &:hover {
      background-color: var(--tant-secondary-color-secondary-transp-hover);
    }
  }
  
  .btn-26 {
    width: 26px;
    height: 26px;
    border-radius: 4px;
  }
  .custom-edit-btn {
    background-color: transparent;
  
    &:hover {
      background-color: transparent;
      color: var(--tant-primary-color-primary-hover);
    }
  }
  .btn-bg-delete {
    background-color: transparent;
  
    &:hover {
      color: var(--tant-status-danger-color-danger-default);
      background-color: var(--tant-status-danger-color-danger-fill-hover);
    }
  }
  </style>