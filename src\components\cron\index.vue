<template>
    <div class="cron" :val="computedValue">
      <a-tabs v-model:active-key="activeName">
        <a-tab-pane key="s" title="秒">
          <second-and-minute v-model="sVal" lable="秒"></second-and-minute>
        </a-tab-pane>
        <a-tab-pane key="m" title="分">
          <second-and-minute v-model="mVal" lable="分"></second-and-minute>
        </a-tab-pane>
        <a-tab-pane key="h" title="时">
          <hour v-model="hVal" lable="时"></hour>
        </a-tab-pane>
        <a-tab-pane key="d" title="日">
          <day v-model="dVal" lable="日"></day>
        </a-tab-pane>
        <a-tab-pane key="month" title="月">
          <month v-model="monthVal" lable="月"></month>
        </a-tab-pane>
        <a-tab-pane key="week" title="周">
          <week v-model="weekVal" lable="周"></week>
        </a-tab-pane>
        <a-tab-pane key="year" title="年">
          <year v-model="yearVal" lable="年"></year>
        </a-tab-pane>
      </a-tabs>
      <!-- table -->
      <a-table
         :data="tableData"
         :bordered="true"
         size="small"
         :pagination="false"
         style="width: 100%;margin-top:16px">
         <template #columns>
           <a-table-column title="秒" data-index="sVal" :width="70" />
           <a-table-column title="分" data-index="mVal" :min-width="70" />
           <a-table-column title="时" data-index="hVal" :min-width="70" />
           <a-table-column title="日" data-index="dVal" :min-width="70" />
           <a-table-column title="月" data-index="monthVal" :min-width="70" />
           <a-table-column title="周" data-index="weekVal" :min-width="70" />
           <a-table-column title="年" data-index="yearVal" />
         </template>
       </a-table>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, computed, watch, onMounted } from 'vue'
  import { Message } from '@arco-design/web-vue'
  import hour from './components/hour.vue'
  import day from './components/day.vue'
  import month from './components/month.vue'
  import week from './components/week.vue'
  import year from './components/year.vue'
  import SecondAndMinute from './components/secondAndMinute.vue'

  
  interface Props {
    modelValue: string
  }
  
  const props = withDefaults(defineProps<Props>(), {
    modelValue: ''
  })
  const emit = defineEmits<{
    'update:modelValue': [value: string]
  }>()
  
  const activeName = ref('s')
  const sVal = ref('')
  const mVal = ref('')
  const hVal = ref('')
  const dVal = ref('')
  const monthVal = ref('')
  const weekVal = ref('')
  const yearVal = ref('')
  
  const tableData = computed(() => {
    return [{
      sVal: sVal.value,
      mVal: mVal.value,
      hVal: hVal.value,
      dVal: dVal.value,
      monthVal: monthVal.value,
      weekVal: weekVal.value,
      yearVal: yearVal.value
    }]
  })
//   在 cron 表达式中，标准顺序应该是：秒 分 时 日 月 周 年
const computedValue = computed(() => {
  if (!dVal.value && !weekVal.value) {
    return ''
  }
//   if (dVal.value === '?' && weekVal.value === '?') {
//     Message.error('日期与星期不可以同时为"不指定"')
//   }
//   if (dVal.value !== '?' && weekVal.value !== '?') {
//     Message.error('日期与星期必须有一个为"不指定"')
//   }
  const v = `${sVal.value} ${mVal.value} ${hVal.value} ${dVal.value} ${monthVal.value} ${weekVal.value} ${yearVal.value}`
  if (v !== props.modelValue) {
    emit('update:modelValue', v)
  }
  return v
})

  
  const updateVal = () => {
    if (!props.modelValue) {
      return
    }
    let arrays = props.modelValue.split(' ')
    sVal.value = arrays[0]
    mVal.value = arrays[1]
    hVal.value = arrays[2]
    dVal.value = arrays[3]
    monthVal.value = arrays[4]
    weekVal.value = arrays[5]
    yearVal.value = arrays[6]
  }
//   console.log(props.modelValue,'props.modelValue11')
  updateVal()
//   watch(() => props.modelValue, () => {
//     console.log(props.modelValue,'props.modelValue')
//     updateVal()
//   })
  
//   onMounted(() => {
//     updateVal()
//   })
  </script>
  
  <style lang="less">
  .cron {
    text-align: left;
    // padding: 10px;
    background: #fff;
    // border: 1px solid #dcdfe6;
    // box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);
    height:100%;
    overflow: auto;
  }
  </style>