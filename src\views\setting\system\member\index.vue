<template>
  <div class="page">
    <div class="page-head">
      <div class="title">{{ route.meta.locale }}</div>
      <div class="filter">
        <div class="filter-item">
          应用分组：
          <a-tree-select
              v-model:model-value="searchGroup"
              allow-clear
              allow-search
              :style="{width:'240px'}"
              placeholder="请选择应用分组"
              :data="groupTreeData"
          />
        </div>
        <div class="filter-item">
          角色：
          <a-select
              v-model:model-value="searchRole"
              allow-clear
              :max-tag-count="1"
              :tag-nowrap="true"
              tree-checkable
              :style="{width:'240px'}"
              placeholder="请选择用户角色"
          >
            <a-option v-for="item of roleData" :key="item.code" :value="item.code">{{ item.name }}</a-option>
          </a-select>
        </div>
        <div class="filter-item">
          名称检索：
          <a-input
              v-model:model-value="searchName"
              :style="{width:'240px'}"
              allow-clear
              placeholder="请输入需要检索的名称"
              @change="searchData"/>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="refresh">搜索</a-button>
        </div>
        <div class="filter-item">
          <a-button type="primary" @click="addData">
            <icon-plus/>
          </a-button>
        </div>
      </div>
    </div>
    <div class="page-body">
      <a-table
          :loading="loading"
          :columns="columns"
          :data="data"
          :scrollbar="true"
          :pagination="{
            showPageSize:true,
            defaultPageSize:20,
            showJumper: true,
            autoAdjust: true,
          }"
          :scroll="{x:'100%', y:'100%'}"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
      >
        <template #apps="{ record }" style="display:flex">
          <div v-if="record.apps?.indexOf('all') >= 0">
            <a-tag>
              全部应用
            </a-tag>
          </div>
          <div v-else>
            <a-tag v-for="(app,index) in record.apps" :key="index" style="margin-right:5px;">
              {{ app }} - {{ fullAppData.find(item => item.code === app)?.name }}
            </a-tag>
          </div>
        </template>
        <template #roles="{ record }" style="display:flex">
          <a-tag v-for="(role,index) in record.roles" :key="index" style="margin-right:5px;">
            {{ roleData.find(item => item.code === role)?.name }}
          </a-tag>
        </template>
        <template #groups="{ record }" style="display:flex">
          <a-tag v-for="(group,index) in record.groups" :key="index" style="margin-right:5px;">
            {{ groupData.find(item => item.code === group)?.name }}
          </a-tag>
        </template>
        <template #optional="{ record }">
          <a-button type="text" @click="editData(record)">编辑</a-button>
          <!-- <a-popconfirm :content="`确认删除“${record.name}”?`" @ok="deleteData(record)">
            <a-button type="text" status="danger">删除</a-button>
          </a-popconfirm> -->
        </template>
        <template #pagination-left>
          <div class="pagination-left">
            共{{ data.length }}条记录
          </div>
        </template>
      </a-table>
    </div>
    <a-modal
        :visible="editModalShow"
        title-align="start"
        :title="formData.code?'编辑应用分组':'新增应用分组'"
        :ok-text="formData.code?'更新':'保存'"
        @cancel="editCancel"
        @ok="editConfirm"
    >
      <a-form ref="formRef" :model="formData" layout="vertical" style="max-height: 680px;overflow: auto;">
        <a-form-item v-show="formData.code" :required="formData.code" field="code" label="用户编码">
          <a-input v-model="formData.code" disabled/>
        </a-form-item>
        <a-form-item field="name" :rules="[{required:true,message:'用户名称不能为空'}]" label="用户名称">
          <a-input v-model="formData.name" placeholder="请输入用户名称"/>
        </a-form-item>
        <a-form-item field="roles" label="角色" :rules="[{ required: true, message: '请选择用户角色' }]">
          <a-select
              v-model:model-value="formData.roles"
              allow-clear
              multiple
              :max-tag-count="3"
              tree-checkable
              placeholder="请选择用户角色"
          >
            <a-option v-for="item of sortedRoleData" :disabled="userStore.role.indexOf('admin') < 0 && userStore.role.indexOf(item.code) < 0" :value="item.code" :key="item.code">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="avatar" label="用户头像">
          <a-input v-model="formData.avatar" placeholder="请输入用户头像地址"/>
        </a-form-item>
        <!-- <a-form-item field="accountNo" label="用户账号">
          <a-input v-model="formData.accountNo" placeholder="请输入用户账号"/>
        </a-form-item> -->
        <a-form-item field="email" label="用户邮箱">
          <a-input v-model="formData.email" placeholder="请输入用户邮箱"/>
        </a-form-item>
        <a-form-item 
          field="password" 
          :required="isPasswordRequired" 
          :rules="[
            { 
              required: isPasswordRequired, 
              message: '请输入用户密码'
            }
          ]" label="用户密码">
          <a-input v-model="formData.password" placeholder="请输入用户密码"/>
        </a-form-item>
        <a-form-item field="mobile" label="电话号码">
          <a-input v-model="formData.mobile" placeholder="请输入用户电话号码"/>
        </a-form-item>
       
        <a-form-item field="groups" label="应用分组">
          <a-tree-select
              v-model:model-value="formData.groups"
              allow-clear
              allow-search
              tree-check-strictly
              :filter-tree-node="(searchValue, nodeData) =>  nodeData.title.toLowerCase().indexOf(searchValue.toLowerCase()) > -1"
              tree-checkable
              :max-tag-count="3"
              placeholder="请选择应用分组"
              :data="groupTreeData"
          />
        </a-form-item>
        <a-form-item field="apps" label="应用权限" tooltip="输入关键词搜索">
          <a-select
              v-model:model-value="formData.apps"
              allow-clear
              :loading="selectedAppDataLoading"
              :allow-search="true"
              :filter-option="false"
              :options="selectedAppData"
              multiple
              :max-tag-count="3"
              tree-checkable
              placeholder="请选择应用权限"
              @search="(searchValue) => {
                selectedAppDataLoading = true
                if(_.isEmpty(searchValue)) {
                  selectedAppData = []
                  selectedAppDataLoading = false
                  return
                }
                selectedAppData = appData.filter(item => item.code.toLowerCase().indexOf(searchValue.toLowerCase()) > -1 ||  item.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1)
                ?.map(item => {
                  return {
                    value: item.code,
                    label: item.code+'-'+item.name
                  }
                });
                selectedAppDataLoading = false
              }"
          >
            <a-option value="all">所有应用</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="status" label="用户状态">
          <a-switch v-model="formData.status" :checked-value="1" :unchecked-value="0"/>
        </a-form-item>
        <a-form-item field="note" label="用户备注">
          <a-textarea v-model="formData.note" placeholder="请输入用户备注"/>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from "vue";
import {getSystemMember, getSystemMemberGroup, getSystemMemberGroupTree, getSystemRole, removeSystemMember, saveSystemMember} from "@/api/setting/api";
import _ from "lodash";
import {Message} from "@arco-design/web-vue";
import {getAppPageList} from "@/api/marketing/api";
import {useUserStore} from "@/store";
import router from "@/router";
import {useRoute} from "vue-router";
import {sortSelectedFirst} from "@/utils/sortUtil";

const userStore = useUserStore();

const route = useRoute();
const data = ref<any>([]);
const appData = ref<any>([]);
const selectedAppData = ref<any>([]);
const selectedAppDataLoading = ref<boolean>(false);
const fullAppData = ref<any>([]);
const roleData = ref<any>([]);
const sortedRoleData = ref<any>([]);
const groupData = ref<any>([]);
const groupTreeData = ref<any>([]);
const loading = ref<boolean>(true);
const searchCode = ref<string>();
const searchName = ref<string>(route.query.text as string || '');
const searchRole = ref<string>();
const searchGroup = ref<string>();
const formData = ref<any>({});
const formRef = ref()
const editModalShow = ref<boolean>(false);
const columns = ref<any>([
  {
    title: '姓名',
    dataIndex: 'name',
    fixed: 'left',
    width: 160,
  },
  {
    title: '头像',
    dataIndex: 'avatar',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '电话',
    dataIndex: 'mobile',
    width: 200,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 200,
  },
  {
    title: '角色',
    slotName: 'roles',
    width: 300,
  },
  {
    title: '应用分组',
    slotName: 'groups',
    width: 300,
  },
  {
    title: '应用权限',
    slotName: 'apps',
    width: 600,
  },
  {
    title: '状态',
    dataIndex: 'status',
    render: (value) => {
      const {record} = value;
      return record.status ? '启用' : '禁用'
    },
    width: 120,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'note',
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '操作',
    slotName: 'optional',
    align: 'center',
    fixed: 'right',
    width: 160
  },
]);
const isPasswordRequired = computed(() => !formData.value.code && !!formData.value.email);

const getChildrenInfo = (group, disabled) => {
  if (group && group.children) {
    return group.children.map(child => {
      const childDisabled = disabled && userStore.group.indexOf(child.code) < 0
      return {
        ...child,
        key: child.code,
        title: child.name,
        disabled: childDisabled,
        children: getChildrenInfo(child, childDisabled)
      }
    })
  }
  return []
}

const refresh = () => {
  const params = {
    code: searchCode.value,
    name: searchName.value,
    group: searchGroup.value,
    role: searchRole.value,
  };
  loading.value = true
  getSystemMember(params).then(res => {
    data.value = res
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  getSystemRole().then(res => {
    roleData.value = res
  }).finally(() => {
    loading.value = false
  })

  getSystemMemberGroupTree().then(res => {
    groupTreeData.value = res?.map(item => {
      const disabled = userStore.role.indexOf('admin') < 0 && userStore.group.indexOf(item.code) < 0;
      return {
        ...item,
        key: item.code,
        title: item.name,
        disabled: disabled,
        children: getChildrenInfo(item, disabled)
      }
    }) || []
  })

  getSystemMemberGroup().then(res => {
    groupData.value = res
  }).finally(() => {
    loading.value = false
  })

  getAppPageList({
    byUser: 0,
    pageSize: 10000,
    current: 1,
  }).then(res => {
    fullAppData.value = res?.items || []
  })

  getAppPageList({
    pageSize: 10000,
    current: 1,
  }).then(res => {
    appData.value = res?.items || []
  })
  refresh()
})

const searchData = () => {
  // 更新路由参数
  const query = { ...route.query };
  if (searchName.value) {
    query.text = searchName.value;
  } else {
    delete query.text;
  }
  router.replace({ query });
}

const addData = () => {
  sortedRoleData.value = sortSelectedFirst(roleData.value,formData.value.roles)
  editModalShow.value = true;
}
const editData = (record: any) => {
  formData.value = {...record};
  sortedRoleData.value = sortSelectedFirst(roleData.value,formData.value.roles)
  editModalShow.value = true;
}

const formSubmit = (value) => {
  if (_.isEmpty(value)) {
    Message.warning("用户数据不能为空！")
  }
  saveSystemMember(value).then(res => {
    Message.info("用户数据保存成功！")
    editModalShow.value = false;
    formRef.value.resetFields();
    refresh()
  }).catch(e => {
    Message.error("用户数据保存失败！", e)
  })
}

const editConfirm = () => {
  formRef.value.validate().then((valid) => {
    if (!valid) {
      formSubmit(formData.value);
    }
  })
}

const editCancel = () => {
  editModalShow.value = false;
  formRef.value.resetFields();
}

const deleteData = (record: any) => {
  const {code} = record
  if (_.isEmpty(code)) {
    Message.warning("删除用户编码不能为空！")
  }
  removeSystemMember(code).then(res => {
    Message.info("用户数据删除成功！")
    refresh()
  }).catch(e => {
    Message.error("用户数据删除失败！", e)
  })
}
</script>

<style scoped lang="less">

</style>