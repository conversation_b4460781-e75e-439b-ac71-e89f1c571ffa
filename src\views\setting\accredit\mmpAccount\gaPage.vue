<template>
    <div style="width: 100%;height: 100%;">
        <a-tabs type="text" @change="tabsChange">
            <a-tab-pane key="1" title="按账号">
                <a-table
                    :loading="loading"
                    :columns="columns"
                    :data="tableData"
                    :bordered="false"
                    sticky-header
                    :table-layout-fixed="true"
                    :scrollbar="scrollbar"
                    :scroll="scroll">
                    <template #isEnabled="{record}">
                      <span v-if="record.isEnabled ===1">开启</span>
                      <span v-else>关闭</span>
                    </template>
                    <template #createTime="{record}">
                      {{ dayjs(record.createTime).format('YYYY-MM-DD') }}
                    </template>
                    <template #action="{ record }">
                        <a-dropdown position="bl">
                          <a-button class="setting">
                              <template #icon>
                              <icon-more-vertical/>
                              </template>
                          </a-button>
                          <template #content>
                              <a-doption value="config" @click="handleForm(record.clientEmail)">
                                <icon-settings class="mr8"/>
                                修改
                              </a-doption>
                              <a-doption value="data" @click="removeAccount(record.clientEmail)">
                                <icon-delete class="mr8"/>
                                删除
                              </a-doption>
                          </template>
                        </a-dropdown>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane key="2" title="按应用">
                <div class="search">
                  <a-input v-model="searchValue" class="title-input" placeholder="搜索应用ID/名称">
                    <template #prefix>
                      <icon-search/>
                    </template>
                  </a-input>
                </div>
                <a-table
                    :loading="appLoading"
                    :columns="appColumns"
                    :data="filteredAppData"
                    :bordered="false"
                    sticky-header
                    :table-layout-fixed="true"
                    :scrollbar="scrollbar"
                    :scroll="scroll">
                    <!-- <template #name="{ record }">
                      {{ record?.id }}-{{ record?.appName }}
                    </template> -->
                    <template #crawlReportingIsEnabled="{ record }">
                        <a-switch v-model="record.crawlReportingIsEnabled" :checked-value="1" :unchecked-value="0" size="small" :before-change="() => reportChange(record)"/>
                    </template>
                    <template #action="{ record }">
                        <a-dropdown position="bl">
                          <a-button class="setting">
                              <template #icon>
                              <icon-more-vertical/>
                              </template>
                          </a-button>
                          <template #content>
                              <a-doption value="config">
                                <icon-settings class="mr8"/>
                                修改
                              </a-doption>
                              <a-doption value="data">
                                <icon-delete class="mr8"/>
                                删除
                              </a-doption>
                          </template>
                        </a-dropdown>
                    </template>
                </a-table>
            </a-tab-pane>
        </a-tabs>
        <gaFormModal ref="gaRef" @update-data="init"/>
        <a-modal
            v-model:visible="deleteModalShow"
            width="auto"
            :ok-button-props="{status:'danger'}"
            :closable="true" ok-text="删除" title-align="start"
            @ok="deleteConfirm"
            @cancel="deleteCancel">
          <template #title>
            删除账号
          </template>
          确认删除【{{ deleteAccount }}】？ 该操作不可恢复。
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {computed, ref} from "vue";
import {deleteGa, getGaAppList, getGaList, updateGaApp} from "@/api/setting/api"
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import gaFormModal from "./components/gaFormModal.vue";

const loading = ref(false)

const columns = [
  {
    title: '账号名称',
    dataIndex: 'clientEmail',
    ellipsis: "true",
    tooltip: true,
    slotName: 'account',
    minWidth: 180,
  },
  {
    title: '授权状态',
    dataIndex: 'isEnabled',
    ellipsis: "true",
    slotName: 'isEnabled',
    minWidth: 100,
  },
  {
    title: '授权日期',
    dataIndex: 'createTime',
    ellipsis: "true",
    slotName: 'createTime',
    minWidth: 100,
  },
  {
    title: '',
    dataIndex: 'action',
    ellipsis: "true",
    slotName: 'action',
    width: 80,
    align: 'right',
    fixed: 'right'
  },
]
const tableData = ref<any>([])

const appLoading = ref(false)
const appColumns = [
{
    title: '应用ID',
    dataIndex: 'id',
    ellipsis: "true",
    tooltip: true,
    slotName: 'id',
    minWidth: 180,
  },
  {
    title: '应用名称',
    dataIndex: 'appName',
    ellipsis: "true",
    tooltip: true,
    slotName: 'appName',
    minWidth: 180,
  },
  {
    title: '昨日用户数',
    dataIndex: 'users',
    ellipsis: "true",
    slotName: 'users',
    sortable: { sortDirections: ['ascend', 'descend'] },
    minWidth: 100,
  },
  {
    title: '聚合数据抓取状态',
    dataIndex: 'crawlReportingIsEnabled',
    ellipsis: "true",
    slotName: 'crawlReportingIsEnabled',
    minWidth: 100,
  },
  {
    title: '货币单位',
    dataIndex: 'propertyCurrencyCode',
    ellipsis: "true",
    tooltip: true,
    slotName: 'propertyCurrencyCode',
    minWidth: 180,
  },
  {
    title: '时区',
    dataIndex: 'propertyTimeZone',
    ellipsis: "true",
    tooltip: true,
    slotName: 'propertyTimeZone',
    minWidth: 180,
  },
  {
    title: '账号地区',
    dataIndex: 'accountRegionCode',
    ellipsis: "true",
    tooltip: true,
    slotName: 'accountRegionCode',
    minWidth: 180,
  },
  {
    title: '',
    dataIndex: 'action',
    ellipsis: "true",
    slotName: 'action',
    width: 80,
    align: 'right',
    fixed: 'right'
  },
]
const appData = ref<any>([])
const searchValue = ref('')
const scrollbar = ref(true)
const scroll = {y: 'calc(100vh - 300px)', x: 1000}

const filteredAppData = computed(() => {
  const str = searchValue.value.trim();
  return appData.value.filter(item => item?.appName.includes(str) || item?.id.includes(str));
});
const getList = async () => {
  loading.value = true
  try{
    await getGaList().then(res => {
      tableData.value = res
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
}
const getAppList = async () => {
  appLoading.value = true
  try{
    await getGaAppList().then(res => {
      appData.value = res
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    appLoading.value = false
  }
}

// 删除账号
const deleteModalShow = ref(false)
const deleteAccount = ref('')

const deleteConfirm = async () => {
  await deleteGa(deleteAccount.value).then(res => {
    Message.success('删除成功')
    getList()
  })
}
const deleteCancel = () => {
  deleteModalShow.value = false
}
const removeAccount = (clientEmail:string) => {
  deleteAccount.value = clientEmail
  deleteModalShow.value = true
}


const reportChange = (record) => {
  const data = {
    clientEmail: record.clientEmail,
    id:record.id,
    crawlReportingIsEnabled:record.crawlReportingIsEnabled === 1 ? 0 :1
  }
  return new Promise((resolve, reject) => {
    // 模拟请求接口
    updateGaApp(data)
      .then((res) => {
        if (res) {
          Message.success('更改成功')
          resolve(true);
        } else {
          Message.error('更改失败')
          resolve(false);
        }
      })
      .catch((error) => {
        console.error('请求失败:', error);
        resolve(false);
      });
  });
}
const tabsChange = (v) => {
  console.log(v,'vvv');
}
const init = async () => {
  await Promise.all([
    getList(),
    getAppList(),
  ])
}
init()
const gaRef = ref()

const handleForm = (clientEmail?:string) => {
  gaRef.value.openModal(clientEmail)
}
defineExpose({
    handleForm
})
</script>

<style scoped lang="less">
.setting {
  width: 20px;
  height: 20px;
  background: transparent;
}

.setting:hover {
  background-color: var(--tant-dblue-dblue-20);
  border-radius: 3px;
}

.search{
  width: 1005;
  text-align: right;
  .title-input{
    width: 240px;
  }
}
</style>