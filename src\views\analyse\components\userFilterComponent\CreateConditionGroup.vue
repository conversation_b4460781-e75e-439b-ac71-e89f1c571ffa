<!-- 创建条件分群弹窗 -->
 <template>
    <a-modal v-model:visible="createVisible" width="520px" @cancel="handleCreateCancel">
        <template #title>
            <div class="modal-title">创建条件分群</div>
        </template>
        <a-form ref="createFormRef" :model="form" :rules="rules" layout="vertical">
            <a-form-item field="displayName" label="显示名">
                <a-input v-model="form.displayName" placeholder="80字以内" :max-length="80"/>
            </a-form-item>
            <a-form-item field="name" label="分群名">
                <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
            </a-form-item>
            <a-form-item field="description" label="备注">
                <a-textarea v-model="form.description" placeholder="请输入" :max-length="200" allow-clear show-word-limit style="height: 100px;"/>
            </a-form-item>
        </a-form>
        <div class="form-item">
            <div class="label">统一筛选周期</div>
            <div class="filter-date">
                <a-switch v-model:model-value="useOuterDateRange" @change="switchChange"/>
                <div v-if="useOuterDateRange" style="margin-left: 24px;">
                  <date-picker disabled-after-today :date-range="dateRange" @date-pick="dateRangeChange"/>
                </div>
            </div>
        </div>
        
        <div class="calculate-formula">
            <div class="head">
                <div class="title">分群条件</div>
            </div>
            <div class="indicator-content">
                <ClusterIndex ref="clusterRef" :disabled="true" :user-filter="props.userFilterData"/>
            </div>
        </div>
        <template #footer>
            <a-button class="cancel" @click.stop="handleCreateCancel">取消</a-button>
            <a-button type="primary" @click="saveForm">保存</a-button>
        </template>
    </a-modal>
 </template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue"
import DatePicker from "@/components/date-picker/index.vue";
import {DateRange} from "@/api/analyse/type";

const props = defineProps({
  // 回显传参
  userFilterData:{
    type:Object,
    default:() => {}
  }
})
// watch(() => props.userFilterData, (newVal) => {
// }, { immediate: true, deep: true });
const clusterRef = ref()
const dateRange = ref<DateRange>({
    recentStartDate: 7,
    recentEndDate: 1,
    dateText: '过去7天'
});
const useOuterDateRange = ref(false)
const createVisible = ref(false);
const createFormRef = ref()
const form = reactive({
    displayName: '',
    name: '',
    description: '' 
})
const rules = reactive({
    displayName: [{ required: true, message: '请输入显示名' }],
    name: [{ required: true, message: '请输入指标名' }]
});

const openModal = () => {
    createFormRef.value.resetFields()
    createFormRef.value.clearValidate()
    useOuterDateRange.value = false
    clusterRef.value.changeShowCycle(false)
    dateRange.value ={
      recentStartDate: 7,
      recentEndDate: 1,
      dateText: '过去7天'
    }
    createVisible.value = true;
};
const handleCreateCancel = () => {
    createVisible.value = false;
};

const saveForm = () => {
    createFormRef.value.validate((validate: any) => {
        if (!validate) {
            // 
        }
    })
};

const switchChange = (val: boolean) => {
    if (val) {
        clusterRef.value.changeShowCycle(true)
        clusterRef.value.handleUnifiedCycle(dateRange.value);
    }else{
        clusterRef.value.changeShowCycle(false)
    }
};
const dateRangeChange = (val: any) => {
    dateRange.value = val;
    clusterRef.value.handleUnifiedCycle(val);
    clusterRef.value.changeShowCycle(true)
};
defineExpose({
    openModal 
})
</script>

 <style scoped lang="less">
 .form-item{
    margin-bottom: 16px;
    .label{
        font-weight: 500;
        max-width: 100%;
        color: var(--color-text-2);
        font-size: 14px;
        white-space: normal;
    }
    .filter-date{
        padding: 12px 0;
        display: flex;
    }
 }

.calculate-formula {
  .head {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 0;
    line-height: 1.5715;
    white-space: normal;

    .title {
      font-weight: 500;
      max-width: 100%;
      color: var(--color-text-2);
      font-size: 14px;
      white-space: normal;
    }

    .note {
      padding-left: 8px;
      color: var(--color-gray-blue-7);
      font-size: 12px;
    }
  }

  .indicator-content {
    padding: 4px 12px;
    // background-color: var(--tant-fill-color-fill1-2);
    border-radius: 4px;
    max-height: 340px;
    overflow: auto
  }
}
 </style>