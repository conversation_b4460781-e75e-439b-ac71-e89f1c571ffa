<template>
  <!-- 实验报告 -->
  <div class="report-content">
    <div class="conclusion">
      <div v-if="reportDetail?.userNumber < 1000 || bestVariant?.pValue == undefined || !reportDetail?.confidenceLevel" class="conclusion-content">
        <div class="title">
          <div>样本量过少，尚未产出有效结论</div>
        </div>
        <div class="sub-title">建议扩大实验流量或延长实验时长，待收集更多数据后再查看结论</div>
      </div>
      <div v-else-if="bestVariant?.pValue >= 1 - reportDetail?.confidenceLevel" class="conclusion-content">
        <div class="title">
          暂无显著结论，但实验组 [{{ bestVariant?.name }}] 的核心指标差异最大
          <div v-if="bestVariant?.diffValue > 0" class="data-compare">
            （相对对照版本&nbsp;<span class="positive-color">+{{ formatPercentageValue(bestVariant?.diffRate) }}</span>
            <img class="icon" src="/icon/arrow-up-green.svg" alt=""/>
            ）
          </div>
          <span v-else class="data-compare">
            （相对对照版本显&nbsp;<span class="negative-color">{{ formatPercentageValue(bestVariant?.diffRate) }}</span>
            <img class="icon" src="/icon/arrow-down-red.svg" alt=""/>
            ）
          </span>
        </div>
        <div class="sub-title"> 建议改良实验方案得到显著的结论</div>
      </div>
      <div v-else class="conclusion-content">
        <div class="title">
          实验组 [{{ bestVariant?.name }}] 的核心指标
          <div v-if="bestVariant?.diffValue > 0" class="data-compare">
            正向显著（相对对照版本显著&nbsp;<span class="positive-color">+{{ formatPercentageValue(bestVariant?.diffRate) }}</span>
            <img class="icon" src="/icon/arrow-up-green.svg" alt=""/>
            ）
          </div>
          <span v-else class="data-compare">
            负向显著（相对对照版本显著&nbsp;<span class="negative-color">{{ formatPercentageValue(bestVariant?.diffRate) }}</span>
            <img class="icon" src="/icon/arrow-down-red.svg" alt=""/>
            ）
          </span>
        </div>
        <div class="sub-title">可结合实际业务情况决策是否可全量发布该版本</div>
      </div>
      <div class="footer-wrapper">
        <div class="note-item"
        >实验流量：{{ reportDetail?.flowPercentage }}% <span v-if="reportDetail?.flowLayer?.name">（{{ reportDetail?.flowLayer?.name }}）</span></div
        >
        <div class="note-item">总进组用户数：{{ formatIntegerValue(reportDetail?.userNumber) }}</div>
        <div class="note-item">总激活用户数：{{ formatIntegerValue(reportDetail?.activateUser) }}</div>
        <div class="note-item">实验观测建议覆盖完整自然周</div>
      </div>
    </div>
    <div class="variant-info">
      <div class="title">
        <img class="icon" src="/experiment/summary.png" alt=""/>
        实验分组结论概览
      </div>
      <a-table :loading="detailLoading" :data="tableData" :scrollbar="true" :scroll="{ x: '100%', y: '100%' }" :pagination="false" :bordered="false" :hoverable="true" sticky-header :table-layout-fixed="true" :filter-icon-align-left="true" :column-resizable="true">
        <template #columns>
          <a-table-column title="实验分组" data-index="name" fixed="left" width="180">
            <template #cell="{ record }">
              <div class="variant-name">
                <a-tag v-if="record?.type === 0" class="mini-tag" color="gray" bordered>对照组</a-tag>
                <a-tag v-if="record?.type === 1" class="mini-tag" color="green" bordered>实验组</a-tag>
                <div>{{ record?.name }}</div>
              </div>
              <div class="variant-id">
                  <div class="code">
                    ID: <a-tooltip :content="record?.code" position="tl"> <span>{{ record?.code }}</span> </a-tooltip>
                  </div>
                <icon-copy :style="{cursor:'pointer',fontSize:'12px'}" @click="copyVariantId(record?.code)"/>
              </div>
            </template>
          </a-table-column>
          <a-table-column title="流量权重" data-index="flowPercentage" width="150" align="right">
            <template #cell="{ record }">
              {{ record.flowPercentage && record.flowPercentage > 0 ? record.flowPercentage + '%' : (record.flowPercentage || '--') }}
            </template>
          </a-table-column>
          <a-table-column title="激活人数" data-index="userNumber" width="150" align="right">
            <template #cell="{ record }">
              {{ record.userNumber && record.userNumber > 0 ? formatIntegerValue(record.userNumber) : record.userNumber }}
            </template>
          </a-table-column>
          <a-table-column title="人数占比" data-index="userNumberPercentage" width="150" align="right">
            <template #cell="{ record }">
              {{ record.userNumberPercentage && record.userNumberPercentage > 0 ? record.userNumberPercentage + '%' : (record.userNumberPercentage || '--') }}
            </template>
          </a-table-column>
          <a-table-column data-index="coreIndicator" width="240" align="right">
            <template #title>
              <div class="table-title">
                <a-tag class="mini-tag" color="orange" bordered>核心</a-tag>
                <div>{{ coreIndicatorInfo?.displayName }}</div>
                <a-popover>
                  <icon-question-circle style="margin-left: 4px; cursor: pointer;"/>
                  <template #content>
                    <div class="indicator-content">
                      <div class="indicator-tip-title">{{ coreIndicatorInfo?.displayName }}</div>
                      <div class="indicator-tip-description">{{ coreIndicatorInfo?.description }}</div>
                      <a-divider :margin="6"/>
                      <component :is="CustomIndicatorDisplay.default" :indicator="coreIndicatorInfo || {}"/>
                      <component
                          v-if="coreIndicatorInfo?.filter?.filters?.length > 0"
                          :is="EventQueryFilter.default"
                          :filter="coreIndicatorInfo?.filter"
                          :disabled="true"
                          :show-detail-filter="true"
                          :code-list="coreIndicatorInfo?.eventList"
                      />
                    </div>
                  </template>
                </a-popover>
              </div>
            </template>
            <template #cell="{ record }">
              <div>{{ record.coreIndicator }}</div>
              <div v-if="record.diffValue > 0" class="diff-value"> +{{ record.diffValue }}</div>
              <div v-if="record.diffValue < 0" class="diff-value"> {{ record.diffValue }}</div>
            </template>
          </a-table-column>
          <a-table-column data-index="diffRate" width="200" align="right">
            <template #title>
              <span>差异相对值</span>
              <a-tooltip>
                <icon-question-circle style="margin-left: 4px; cursor: pointer;"/>
                <template #content>
                  当前实验版本相对基准版本（对照组）的绝对差异/基准版本值
                </template>
              </a-tooltip>
            </template>
            <template #cell="{ record }">
              <div v-if="record.diffRate > 0" :class="record.pValue == undefined || !reportDetail?.confidenceLevel || record.pValue > 1 - reportDetail?.confidenceLevel ? 'diff-rate' : 'diff-rate positive-background-color'">
                <div class="positive-color"> +{{ formatPercentageValue(record.diffRate) }}</div>
                <div style="margin-top: -4px">
                  <a-tooltip>
                    <span class="mde"> MDE:&nbsp;{{ formatPercentageValue(record.mde) }} </span>
                    <template #content>
                      最小可检测单位（检验灵敏度），指实验在当前条件下可检出置信度的最小指标变化幅度。若实验指标暂不显著，但MDE>预期提升值，则指标还有置信可能
                    </template>
                  </a-tooltip>
                  <a-tooltip v-if="record.pValue == undefined || !reportDetail?.confidenceLevel || record.pValue > 1 - reportDetail?.confidenceLevel" position="right">
                    <a-tag class="significant-tag"> 不显著</a-tag>
                    <template #content>
                      p-value: {{ record.pValue }}
                    </template>
                  </a-tooltip>
                  <a-tooltip v-else position="right">
                    <a-tag color="green" class="significant-tag">
                      正向显著
                    </a-tag>
                    <template #content>
                      p-value: {{ record.pValue }}
                    </template>
                  </a-tooltip>
                </div>
              </div>
              <div v-if="record.diffRate < 0" :class="record.pValue == undefined || !reportDetail?.confidenceLevel || record.pValue > 1 - reportDetail?.confidenceLevel ? 'diff-rate' : 'diff-rate negative-background-color'">
                <div class="negative-color">
                  {{ formatPercentageValue(record.diffRate) }}
                </div>
                <div style="margin-top: -4px">
                  <a-tooltip>
                    <span class="mde"> MDE:&nbsp;{{ formatPercentageValue(record.mde) }} </span>
                    <template #content>
                      最小可检测单位（检验灵敏度），指实验在当前条件下可检出置信度的最小指标变化幅度。若实验指标暂不显著，但MDE>预期提升值，则指标还有置信可能
                    </template>
                  </a-tooltip>
                  <a-tooltip v-if="record.pValue == undefined || !reportDetail?.confidenceLevel || record.pValue > 1 - reportDetail?.confidenceLevel" position="right">
                    <a-tag class="significant-tag"> 不显著</a-tag>
                    <template #content>
                      p-value: {{ record.pValue }}
                    </template>
                  </a-tooltip>
                  <a-tooltip v-else position="right">
                    <a-tag color="green" class="significant-tag">
                      负向显著
                    </a-tag>
                    <template #content>
                      p-value: {{ record.pValue }}
                    </template>
                  </a-tooltip>
                </div>
              </div>
            </template>
          </a-table-column>
          <a-table-column data-index="confidenceInterval" width="340" align="right">
            <template #title>
              <span>置信区间</span>
              <a-tooltip>
                <icon-question-circle style="margin-left: 4px; cursor: pointer;"/>
                <template #content>
                  由样本统计量构成的总体参数的估计区间，用于描述实验组指标相比于对照组在设定置信度水平上的涨跌幅度取值范围
                </template>
              </a-tooltip>
            </template>
            <template #cell="{ record }">
              <div v-if="record.type > 0" class="confidence-interval">
                <div class="header-value">
                  {{ formatPercentageValue(record.diffRate) }}
                </div>
                <div class="axis">
                  <div class="interval-line" style="background-color: rgb(7, 163, 90)">
                    <div class="interval-left"></div>
                    <div class="interval-dot"></div>
                    <div class="interval-right"></div>
                  </div>
                </div>
                <div class="footer-value">
                  <div>
                    {{ formatPercentageValue(record.confidenceInterval?.[0]) }}
                  </div>
                  <div>
                    {{ formatPercentageValue(record.confidenceInterval?.[1]) }}
                  </div>
                </div>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <a-tabs v-model:active-key="chartType" type="card-gutter" class="chart-tabs">
      <a-tab-pane key="trend" class="chart-tab" title="趋势图">
        <div class="trend-chart-option">
          <div class="option-title">展示数据</div>
          <a-radio-group v-model:model-value="trendChartType" style="height: 22px" @change="trendChartTypeChange">
            <a-radio value="indicator">统计值</a-radio>
            <a-radio value="diffRate">差异相对值</a-radio>
            <a-radio value="pValue"> p-value</a-radio>
          </a-radio-group>
          <div class="option-title">范围展示</div>
          <a-switch v-model:model-value="trendScopeDisplay" :disabled="trendScopeDisplayDisable" @change="renderTrendChart">
            <template #checked> 开</template>
            <template #unchecked> 关</template>
          </a-switch>
          <div class="option-title" style="margin-left: 12px">显著性水平展示</div>
          <a-switch v-model:model-value="significanceLevelDisplay" :disabled="significanceLevelDisplayDisable" @change="renderTrendChart">
            <template #checked> 开</template>
            <template #unchecked> 关</template>
          </a-switch>
        </div>
        <a-spin :loading="chartLoading" class="trend-chart">
          <Chart :option="trendChartOption" @legendselectchanged="onTrendChartLegendSelectChanged"/>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane key="probability" class="chart-tab" title="概率图">
        <a-spin :loading="chartLoading" class="trend-chart">
          <Chart :option="probabilityChartOption" @legendselectchanged="onProbabilityChartLegendSelectChanged"/>
        </a-spin>
      </a-tab-pane>
    </a-tabs>
    <div v-if="chartType==='trend'" class="chart-tips">
      <div class="tip-content">
        <div class="tip-item">1. 可查看当日指标的统计值和p-value，以及当日统计值的范围。日均定义口径为已经进组的当日活跃用户的指标表现，并非当日新进组用户</div>
        <div class="tip-item">2. 当打开「范围展示」，出现范围，会产出重叠效果，并查看各个版本的核心指标范围随时间变化的情况</div>
        <div class="sub-tips">
          <div class="sub-tip-item">a. 理想情况下，组内每日活跃用户会随着时间趋于稳定，从而波动变小，即范围变小并趋于稳定，并且在图中反映出来</div>
          <div class="sub-tip-item">b. 如果范围忽大忽小，则表明日活指标数据波动大</div>
          <div class="sub-tip-item">c. 如果范围重叠，则表示不确定哪个版本效果更好</div>
        </div>
      </div>
    </div>
    <div v-if="chartType==='probability'" class="chart-tips">
      <div class="tip-content">
        <div class="tip-item">1. 概率分布，展示的是指标的取值及其出现的概率分布，横轴是指标值，纵轴是指标值出现的概率密度，通过均值和方差反映指标的分布情况。实验组和对照组的概率分布对比，可辅助判断实验组和对照组的差异情况</div>
        <div class="sub-tips">
          <div class="sub-tip-item">a. 默认对照版本采用灰色系，其他版本采用彩色系</div>
          <div class="sub-tip-item">b. 在不同实验版本的正态分布曲线上，鼠标悬浮会显示各个版本的“激活人数、p-value、指标方差、MDE、置信区间”信息</div>
        </div>
        <div class="tip-item">2. 适用范围：支持人均类、ctr类等支持计算置信度的指标</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue';
import {getExperimentReportDetail, postExperimentAnalyticsQuery} from '@/api/ab/api';
import {useRoute} from 'vue-router';
import {formatIntegerValue, formatPercentageValue} from '@/utils/number-util';
import {get16UUID} from '@/utils/strUtil';
import {formatTime} from '@/utils/timeUtil';
import {generateNormalDistribution, getZValue} from "@/utils/chartUtil";
import {getIndicatorDetail} from "@/api/setting/api";
import * as EventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue";
import * as CustomIndicatorDisplay from "@/views/analyse/components/CustomIndicatorDisplay.vue";
import {Message} from "@arco-design/web-vue";

const props = defineProps({
  experimentDetail: {
    type: Object,
    default: () => ({}),
  },
});
const route = useRoute();
// 报告详情
const reportDetail = ref();
// 分析数据
const reportAnalyseData = ref();
// 核心指标信息
const coreIndicatorInfo = ref({});
// 报告详情加载
const detailLoading = ref<boolean>(false);
// 图表加载
const chartLoading = ref<boolean>(false);
// 趋势图展示数据类型
const chartType = ref<string>('trend');
// 趋势图展示数据类型
const trendChartType = ref<string>('indicator');
// 表格数据
const tableData = ref([]);
// 趋势图配置
const trendChartOption = ref({});
// 趋势图范围展示
const trendScopeDisplay = ref(false);
// 趋势图范围展示是否可用
const trendScopeDisplayDisable = ref(false);
// 显著性水平展示
const significanceLevelDisplay = ref(false);
// 显著性水平展示是否可见
const significanceLevelDisplayDisable = ref(true);
// 概率图配置
const probabilityChartOption = ref({});

// 置性区间图例联动
const onTrendChartLegendSelectChanged = (params) => {
  // params.name 是主曲线名
  // params.selected 是所有图例的显示状态
  const selected = params.selected;
  // 遍历所有主曲线
  Object.keys(selected).forEach((key) => {
    // key 是主曲线名
    // 控制对应的 L/U 曲线
    trendChartOption.value.legend.selected[key] = selected[key];
    trendChartOption.value.legend.selected[key + '-L'] = selected[key];
    trendChartOption.value.legend.selected[key + '-U'] = selected[key];
  });
};

// 置性区间图例联动
const onProbabilityChartLegendSelectChanged = (params) => {
  // params.name 是主曲线名
  // params.selected 是所有图例的显示状态
  const selected = params.selected;
  // 遍历所有主曲线
  Object.keys(selected).forEach((key) => {
    // key 是主曲线名
    // 控制对应的 L/U 曲线
    probabilityChartOption.value.legend.selected[key] = selected[key];
    probabilityChartOption.value.legend.selected[key + '-L'] = selected[key];
  });
};

// 颜色数组，10组，超过则循环
const colorList = [
  {line: '#333333', area: 'rgba(51,51,51,0.15)'},
  {line: '#5470C6', area: 'rgba(84,112,198,0.15)'},
  {line: '#91CC75', area: 'rgba(145,204,117,0.15)'},
  {line: '#EE6666', area: 'rgba(238,102,102,0.15)'},
  {line: '#FAC858', area: 'rgba(250,200,88,0.15)'},
  {line: '#73C0DE', area: 'rgba(115,192,222,0.15)'},
  {line: '#3BA272', area: 'rgba(59,162,114,0.15)'},
  {line: '#FC8452', area: 'rgba(252,132,82,0.15)'},
  {line: '#9A60B4', area: 'rgba(154,96,180,0.15)'},
  {line: '#EA7CCC', area: 'rgba(234,124,204,0.15)'},
  {line: '#2A99C9', area: 'rgba(42,153,201,0.15)'},
];

/**
 * 统计值趋势图
 */
function renderIndicatorTrendChart() {
  // 图例，实验组名称
  const legend = reportAnalyseData.value?.groups?.map((item) => item?.[0]) || [];
  // x轴
  const x = reportAnalyseData.value?.x || [];
  // 核心指标数据
  const coreIndicatorData = reportAnalyseData.value?.y?.[0]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];

  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      // 实验组
      seriesData.push({
        name: item.group[0],
        type: 'line',
        data: item.values.map((v, i) => ({
          value: v,
          diffRate: item.diffRates ? item.diffRates[i] : undefined,
        })),
        itemStyle: {
          color: '#333',
        },
        showSymbol: false,
      });
      return;
    }
    // 对照组
    seriesData.push({
      name: item.group[0],
      type: 'line',
      data: item.values.map((v, i) => ({
        value: v,
        diffRate: item.diffRates ? item.diffRates[i] : undefined,
      })),
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    });
    // 范围展示
    if (trendScopeDisplay.value) {
      seriesData.push({
        name: item.group[0] + '-L',
        type: 'line',
        data: item.confidenceInterval.map((confidenceInterval) => confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
      seriesData.push({
        name: item.group[0] + '-U',
        type: 'line',
        data: item.confidenceInterval.map((confidenceInterval) => confidenceInterval[1] - confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        areaStyle: {
          color: colorList[index % colorList.length].area,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => {
                  const diffRate = item.data.diffRate;
                  const diffRateStr = !diffRate ? '' : diffRate > 0 ? formatPercentageValue(diffRate) + '↑' : formatPercentageValue(diffRate) + '↓';
                  let color = !diffRate ? '#222' : diffRate > 0 ? '#07a35a' : '#e33232';
                  return item.marker + ' ' + item.seriesName + ' <b>' + item.value + '</b> ' + (diffRate !== undefined ? `<b style="color:${color}">${diffRateStr}</b>` : '');
                })
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
    },
    series: seriesData,
  };
}

/**
 * 差异绝对值趋势图
 */
function renderDiffRateTrendChart() {
  // 图例，实验组名称
  const legend = reportAnalyseData.value?.groups?.filter((item, index) => index !== 0).map((item) => item?.[0]) || [];
  // x轴
  const x = reportAnalyseData.value?.x || [];
  // 核心指标数据
  const coreIndicatorData = reportAnalyseData.value?.y?.[0]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];

  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      return;
    }
    // 对照组
    seriesData.push({
      name: item.group[0],
      type: 'line',
      data: item.diffRates.map((v, i) => ({
        value: v,
        confidenceInterval: item.diffRateConfidenceInterval?.length > 0 ? item.diffRateConfidenceInterval[i] : undefined,
      })),
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    });
    // 范围展示
    if (trendScopeDisplay.value) {
      seriesData.push({
        name: item.group[0] + '-L',
        type: 'line',
        data: item.diffRateConfidenceInterval.map((confidenceInterval) => confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
      seriesData.push({
        name: item.group[0] + '-U',
        type: 'line',
        data: item.diffRateConfidenceInterval.map((confidenceInterval) => confidenceInterval[1] - confidenceInterval[0]),
        lineStyle: {
          opacity: 0,
        },
        areaStyle: {
          color: colorList[index % colorList.length].area,
        },
        stack: item.group[0] + 'confidence-band',
        symbol: 'none',
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
          formatter: function (params) {
            return formatPercentageValue(params.value);
          },
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => {
                  const confidenceInterval = item.data.confidenceInterval;
                  const confidenceIntervalStr = confidenceInterval?.length === 2 ? `[${formatPercentageValue(confidenceInterval[0])},${formatPercentageValue(confidenceInterval[1])}]` : '';
                  return item.marker + ' ' + item.seriesName + ' <b>' + formatPercentageValue(item.value) + '</b> ' + confidenceIntervalStr;
                })
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
      axisLabel: {
        formatter: function (value) {
          return formatPercentageValue(value);
        },
      },
    },
    series: seriesData,
  };
}

/**
 * pValue趋势图
 */
function renderPValueTrendChart() {
  // 图例，实验组名称
  const legend = reportAnalyseData.value?.groups?.filter((item, index) => index !== 0).map((item) => item?.[0]) || [];
  // x轴
  const x = reportAnalyseData.value?.x || [];
  // 核心指标数据
  const coreIndicatorData = reportAnalyseData.value?.y?.[0]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData = [];
  coreIndicatorData.forEach((item, index) => {
    if (index === 0) {
      return;
    }
    // 对照组
    const seriesItem = {
      name: item.group[0],
      type: 'line',
      data: item.pValues,
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      showSymbol: false,
    };
    // 显著性水平
    if (significanceLevelDisplay.value && reportDetail.value?.confidenceLevel) {
      seriesItem.markLine = {
        symbol: 'none',
        data: [
          {
            yAxis: 1 - reportDetail.value.confidenceLevel,
            lineStyle: {
              color: 'rgba(7,163,90,0.8)',
              type: 'dashed',
              width: 1,
            },
            label: {
              show: true,
              position: 'middle',
              formatter: function () {
                return `显著性水平 ${(1 - reportDetail.value.confidenceLevel).toFixed(2)}`;
              },
              color: '#07a35a',
              fontSize: 12,
              fontWeight: 'bold',
              padding: [0, 8, 0, 0],
            },
          },
        ],
      };
    }
    seriesData.push(seriesItem);
    if (significanceLevelDisplay.value && reportDetail.value?.confidenceLevel) {
      seriesData.push({
        name: '显著性水平参考线',
        type: 'line',
        data: x.map(() => 1 - reportDetail.value.confidenceLevel),
        lineStyle: {
          width: 0,
        },
        showSymbol: false,
        tooltip: {show: false},
        z: 0,
        areaStyle: {
          color: 'rgba(7,163,90,0.12)',
          origin: 'start',
        },
      });
    }
  });

  trendChartOption.value = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: '#222',
        },
      },
      formatter: function (params) {
        return (
            '<b>' +
            params[0].axisValueLabel +
            ' </b>' +
            '<br/>' +
            params
                .filter((item) => !item.seriesName?.endsWith('-U') && !item.seriesName?.endsWith('-L'))
                .map((item) => item.marker + ' ' + item.seriesName + ' <b>' + item.value + '</b> ')
                .join('<br/>')
        );
      },
    },
    legend: {
      data: legend,
      bottom: 0,
      selected: legend.reduce((acc, name) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        acc[name + '-U'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: x,
      axisLabel: {
        formatter: function (value, idx) {
          var date = new Date(value);
          return idx === 0 ? value : [date.getMonth() + 1, date.getDate()].join('-');
        },
      },
      boundaryGap: false,
    },
    yAxis: {
      splitNumber: 3,
    },
    series: seriesData,
  };
}

/**
 * 趋势图渲染
 */
const renderTrendChart = () => {
  switch (trendChartType.value) {
    case 'indicator':
      renderIndicatorTrendChart();
      break;
    case 'diffRate':
      renderDiffRateTrendChart();
      break;
    case 'pValue':
      renderPValueTrendChart();
      break;
    default:
  }
};

/**
 * 概率图渲染
 */
const renderProbabilityChart = () => {
  // 图例，实验组名称
  const legend = reportAnalyseData.value?.groups?.map((item: any) => item?.[0]) || [];
  // 核心指标数据
  const coreIndicatorData = reportAnalyseData.value?.y?.[0]?.yData;
  if (!coreIndicatorData?.length) {
    // 指标数据不存在
    return;
  }
  const seriesData: any[] = [];
  let globalMinX = Infinity;
  let globalMaxX = -Infinity;
  coreIndicatorData.forEach((item: any, index: number) => {
    const fullData = generateNormalDistribution(item.summaryValue.mean, item.summaryValue.standardDeviation);
    const zValue = getZValue(reportDetail.value.confidenceLevel);
    const minX = item.summaryValue.mean - zValue * item.summaryValue.standardDeviation;
    const maxX = item.summaryValue.mean + zValue * item.summaryValue.standardDeviation;
    const middle = fullData.filter(([x]) => x >= minX && x <= maxX);
    globalMinX = globalMinX > minX ? minX : globalMinX;
    globalMaxX = globalMaxX < maxX ? maxX : globalMaxX;
    // 主曲线
    seriesData.push({
      name: item.group[0],
      type: 'line',
      smooth: true,
      showSymbol: false,
      data: fullData.map(([x, y]) => ({value: [x, y]})), // 适配tooltip
      itemStyle: {
        color: colorList[index % colorList.length].line,
      },
      markPoint: {
        symbolSize: [120, 60],
        label: {
          formatter: (params: Object | Array) => {
            return params.data.coord[0].toFixed(3)
          }
        },
        data: [
          {type: 'max', name: 'Max'}
        ]
      },
      markLine: {
        data: [[
          {xAxis: item.summaryValue.mean, yAxis: '0', symbol: 'none'},
          {
            xAxis: item.summaryValue.mean, yAxis: 'max', symbol: 'none',
            lineStyle: {
              color: colorList[index % colorList.length].line,
              type: 'dashed',
              width: 3
            }
          }
        ]]
      }
    });
    // 中间阴影
    seriesData.push({
      name: item.group[0] + '-L',
      type: 'line',
      smooth: true,
      showSymbol: false,
      data: middle.map(([x, y]) => ({value: [x, y]})),
      lineStyle: {width: 0},
      areaStyle: {
        color: colorList[index % colorList.length].area
      },
    });
  });
  const range = globalMaxX - globalMinX;
  const xMin = Math.round((globalMinX - 0.8 * range) * 1000) / 1000;
  const xMax = Math.round((globalMaxX + 0.8 * range) * 1000) / 1000;
  probabilityChartOption.value = {
    legend: {
      bottom: 0,
      data: legend,
      selected: legend.reduce((acc: any, name: string) => {
        acc[name] = true;
        acc[name + '-L'] = true;
        return acc;
      }, {}),
    },
    grid: {
      left: '0',
      right: '10',
      bottom: '10%',
      top: '10%',
      containLabel: true,
      show: false,
    },
    xAxis: {
      type: 'value',
      boundaryGap: false,
      splitLine: {show: false},
      min: xMin,
      max: xMax,
      axisLabel: {
        margin: 12,
      },
    },
    yAxis: {
      name: '概率密度',
      splitNumber: 3,
      axisLine: {
        show: false,
        onZero: false
      },
      splitLine: {show: true, lineStyle: {type: 'dashed', color: '#d9d9d9'}},
    },
    series: seriesData,
  };
};

/**
 * 趋势图类型修改
 */
const trendChartTypeChange = (value) => {
  renderTrendChart();
  if (value === 'pValue') {
    trendScopeDisplayDisable.value = true;
    trendScopeDisplay.value = false;
    significanceLevelDisplayDisable.value = false;
  } else {
    trendScopeDisplayDisable.value = false;
    significanceLevelDisplayDisable.value = true;
    significanceLevelDisplay.value = false;
  }
};

/**
 * 最显著的实验组
 */
const bestVariant = computed(() => {
  let bestVariantTemp;
  if (reportDetail.value?.variants?.length > 0 && reportDetail.value.confidenceLevel > 0) {
    // 实验版本数据存在 并且 置信水平 > 0
    reportDetail.value?.variants.forEach((variant) => {
      if (variant.type === 0) {
        // 排除对照组
        return;
      }

      // bestVariantTemp为空 或者 p值最小的变体
      if (!bestVariantTemp || variant.pValue < bestVariantTemp.pValue) {
        bestVariantTemp = variant;
      }
    });
  }
  return bestVariantTemp;
});
const init = () => {
  // 获取实验报告摘要
  const experimentCode = (route.query.code as string) || '';
  chartLoading.value = true;
  detailLoading.value = true;
  getExperimentReportDetail(experimentCode).then((res) => {
    reportDetail.value = res;
    if (reportDetail.value?.variants?.length > 0) {
      let data = [];
      reportDetail.value.variants?.forEach((variant) => {
        data.push({
          name: variant.name,
          code: variant.code,
          type: variant.type,
          flowPercentage: variant.flowPercentage,
          userNumber: variant.userNumber,
          userNumberPercentage: variant?.userNumberPercentage ? Number((variant?.userNumberPercentage * 100).toFixed(1)) : undefined,
          coreIndicator: variant.indicatorValue,
          diffValue: variant.diffValue,
          diffRate: variant.diffRate,
          confidenceInterval: variant.confidenceInterval,
          mde: variant.mde,
          pValue: variant.pValue,
        });
      });
      tableData.value = data?.sort((a, b) => a.type - b.type);
    }
    detailLoading.value = false;
    const queryParams = {
      requestId: `request_${get16UUID()}`,
      experimentId: experimentCode,
      model: 'event',
      queryParam: {
        indicators: [
          {
            isBasic: true,
            eventList: [
              {
                type: 'indicator',
                indicatorCode: res?.coreIndicator?.code
              }
            ]
          },
        ],
        dateRange: {
          startDate: formatTime(new Date(res?.startTime), 'yyyy-MM-dd'),
          endDate: formatTime(res?.endTime ? new Date(res?.endTime) : new Date(), 'yyyy-MM-dd'),
        },
        time_particle_size: 'D1'
      },
    };
    postExperimentAnalyticsQuery(queryParams).then((analyticsRes) => {
      reportAnalyseData.value = analyticsRes;
      renderTrendChart();
      chartLoading.value = false;
      renderProbabilityChart();
    });
    getIndicatorDetail(res.coreIndicator.code).then((indicatorDetailRes) => {
      coreIndicatorInfo.value = indicatorDetailRes;
    })
  });
}
onMounted(() => {
  init()
});
watch(() => props.experimentDetail, (res) => {
  init()
}, {deep: true});

/**
 * 复制变体ID
 * @param code 变体ID
 */
const copyVariantId = async (code) => {
  await navigator.clipboard.writeText(code);
  Message.success('复制成功');
}
</script>

<style lang="less" scoped>
.report-content {
  width: 100%;
  height: 100%;

  .positive-color {
    color: #07a35a;
  }

  .positive-background-color {
    background-color: #f1fdef;
  }

  .negative-color {
    color: #e33232;
  }

  .negative-background-color {
    background-color: #fdefed;
  }

  .conclusion {
    box-sizing: border-box;
    padding: 24px;
    background: url(/experiment/summary-bg.jpg) center center / cover no-repeat rgb(255, 255, 255);
    box-shadow: none;
    border-radius: 6px;
    margin-bottom: 20px;
    position: relative;
    color: #10389f;
    flex: 1 1;
    z-index: 1;

    .conclusion-content {
      margin-bottom: 12px;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;

        .data-compare {
          display: flex;
          position: relative;
          align-items: center;

          .icon {
            width: 12px;
            margin-bottom: 1px;
          }
        }
      }

      .sub-title {
        font-size: 12px;
      }
    }

    .footer-wrapper {
      align-items: center;
      color: #606773;
      display: flex;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;

      .note-item {
        margin-right: 24px;
      }
    }
  }

  .variant-info {
    .title {
      color: #141414e6;
      font-size: 16px;
      font-weight: 500;
      line-height: 26px;
      display: flex;
      align-items: center;

      .icon {
        height: 24px;
        width: 24px;
        margin-right: 8px;
      }
    }

    :deep(.table-title) {
      display: flex;
      align-items: center;

      .mini-tag {
        height: 14px;
        font-size: 8px;
        line-height: 12px;
        margin-right: 8px;
        padding: 0 4px;
        display: flex;
        justify-content: center;
      }
    }

    .mini-tag {
      height: 14px;
      font-size: 8px;
      line-height: 12px;
      margin-right: 8px;
      padding: 0 4px;
      display: flex;
      justify-content: center;
    }

    .diff-value {
      color: #14141473;
      font-size: 12px;
      font-weight: 400;
      margin-top: -4px;
    }

    .diff-rate {
      margin: -6px -12px;
      padding: 6px 12px;

      .mde {
        display: inline-block;
        border-bottom: 1px dashed rgba(20, 20, 20, 0.45);
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        margin-right: 8px;
        color: #14141473;
        font-size: 10px;
      }

      .significant-tag {
        height: 14px;
        font-size: 8px;
        line-height: 12px;
        padding: 0 4px;
        justify-content: center;
      }
    }

    .confidence-interval {
      width: 90%;
      justify-self: self-end;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #666686;
      font-size: 12px;
      line-height: 12px;

      .axis {
        align-items: center;
        background-color: #d9d9e9;
        display: flex;
        height: 4px;
        margin: 4px 0;
        position: relative;
        width: 100%;

        .interval-line {
          height: 100%;
          margin: auto;
          position: relative;
          width: 66.66%;

          .interval-left {
            background-color: inherit;
            height: 8px;
            position: absolute;
            top: 0;
            width: 1px;
            left: 0;
          }

          .interval-dot {
            background-color: #2f2f3f;
            bottom: 0;
            height: 100%;
            left: 0;
            margin: auto;
            position: absolute;
            right: 0;
            top: 0;
            width: 1px;
          }

          .interval-right {
            background-color: inherit;
            height: 8px;
            position: absolute;
            top: 0;
            width: 1px;
            right: 0;
          }
        }
      }

      .footer-value {
        display: flex;
        justify-content: space-between;
        width: 75%;
      }
    }

    .variant-name {
      display: flex;
      align-items: center;
    }

    .variant-id {
      display: flex;
      font-size: 10px;
      color: #606773;
      align-items: center;

      .code {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .chart-tabs {
    margin: 20px 0 12px 0;
    width: 100%;
    height: 410px;

    .chart-tab {
      width: 100%;
      padding: 0 16px;

      .trend-chart-option {
        display: flex;
        align-items: center;

        .option-title {
          margin-right: 12px;
        }
      }

      .trend-chart {
        width: 100%;
        height: 325px;
      }
    }

    :deep(.arco-tabs-content) {
      height: calc(100% - 36px);
      overflow: auto;
    }
  }

  .chart-tips {
    background: linear-gradient(0deg, #dff7ff33, #dff7ff33), linear-gradient(90deg, #e6f8f2, #edf9f8 50%, #ddf0e9);
    border-radius: 8px;
    display: flex;
    padding: 24px;
    color: #037849;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 16px;

    .tip-content {
      .tip-item {
      }

      .sub-tips {
        margin-left: 12px;

        .sub-tip-item {
        }
      }
    }
  }
}

.indicator-tip-title {
  font-weight: bolder;
}

.indicator-tip-description {
  font-size: 12px;
  color: var(--tant-text-gray-color-text1-4);
}

</style>
