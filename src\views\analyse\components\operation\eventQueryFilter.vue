<template>
  <!-- 全局筛选 -->
  <div class="guide">
    <div v-if="filtersList.length" class="event-filter-box">
      <div class="action-row">
        <div class="row-filters">
          <div class="relation-editor">
            <div v-if="filtersList.length" class="relation-relation">
              <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
              <div v-if="filtersList.length>1" :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="logicalChange">
                <span v-if="isAnd">且</span>
                <span v-else>或</span>
              </div>
            </div>
            <div class="relation-main">
              <div v-for="(item,index) in filtersList" :key="index" class="relation-row">
                <div class="multi-filter-condition">
                  <div v-if="item.subFilters && item.subFilters.length>1" class="relation-editor">
                    <div class="relation-relation">
                      <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
                      <div :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="() => item.isAnd = !item.isAnd">
                        <span v-if="item.isAnd">且</span>
                        <span v-else>或</span>
                      </div>
                    </div>
                    <div class="relation-main">
                      <div class="relation-row">
                        <div class="multi-filter-condition">
                          <div v-for="(val,num) in item.subFilters" :key="num" class="sub-action-row">
                            <div class="sub-action-left">
                              <tabsSelect
                                  :disabled="props.disabled"
                                  :show-detail-filter="props.showDetailFilter" :exclude-event="props.excludeEvent"
                                  :info="val"
                                  @tabs-change="subChange(index,num,$event)"/>
                            </div>
                            <div class="sub-action-right">
                              <a-space v-if="!props.disabled" align="center">
                                <a-tooltip content="添加并列条件" position="top">
                                  <a-button
                                      class="btn-bg btn-26" style="margin-left: 8px;"
                                      @click="addItemFilters(index)">
                                    <template #icon>
                                      <icon-filter/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                                <a-tooltip content="删除" position="top">
                                  <a-button class="btn-bg-delete btn-26" @click="removeMultipleListItem(index,num)">
                                    <template #icon>
                                      <icon-close-circle/>
                                    </template>
                                  </a-button>
                                </a-tooltip>
                              </a-space>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="sub-action-row">
                    <div class="sub-action-left">
                      <tabsSelect :disabled="props.disabled" :show-detail-filter="props.showDetailFilter" :exclude-event="props.excludeEvent" :info="item" @tabs-change="filChange(index,$event)"/>
                    </div>
                    <div class="sub-action-right">
                      <a-space v-if="!props.disabled" align="center">
                        <a-tooltip content="添加并列条件" position="top">
                          <a-button class="btn-bg btn-26" style="margin-left: 8px;" @click="addFilters(item,index)">
                            <template #icon>
                              <icon-filter/>
                            </template>
                          </a-button>
                        </a-tooltip>
                        <a-tooltip content="删除" position="top">
                          <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index)">
                            <template #icon>
                              <icon-close-circle/>
                            </template>
                          </a-button>
                        </a-tooltip>
                      </a-space>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import tabsSelect from "./tabsSelect.vue"
import {FilterExpression} from "@/api/analyse/type";
import {analyseStore} from '@/store';

const emits = defineEmits(['queryFiltersChange'])
const isAnd = ref(true)
const props = defineProps({
  filter: {
    type: Object,
    default: () => {
    }
  },
  excludeEvent: {
    type: Boolean,
    default: false
  },
  showDetailFilter: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const analyseData = analyseStore();
const filtersList = ref<any>([])

const listChangeFromProps = ref<boolean>(false);

watch(() => props.filter, () => {
  isAnd.value = props.filter?.logicalOperation ? props.filter?.logicalOperation === 'and' : true
  if (props.filter?.filters && props.filter.filters.length > 0) {
    listChangeFromProps.value = true;
    filtersList.value = props.filter.filters.map(item => {
      return {
        objectName: item.objectName,
        objectDisplayName: item.objectDisplayName,
        type: item.type,
        isAnd: item.logicalOperation ? item.logicalOperation === 'and' : true,
        objectType: item.objectType,
        objectId: item.objectId,
        filterType: item.filterType,
        calcuSymbol: item.calcuSymbol,
        thresholds: item.thresholds,
        enumList: item.enumList,
        subFilters: item.subFilters || []
      }
    })
  }
}, {immediate: true})
const eventQueryfilter = reactive(
    {
      logicalOperation: 'and',
      filters: [] as FilterExpression[],
    }
)
const logicalChange = () => {
  if (!props.disabled) {
    isAnd.value = !isAnd.value
    eventQueryfilter.logicalOperation = isAnd.value ? 'and' : 'or'
  }

}

const add = async () => {
  const list = analyseData.$state.operationFilterLists || []
  filtersList.value.push({
    objectName: list[0]?.name || '',
    objectDisplayName: list[0]?.displayName || '',
    isAnd: true,
    objectType: list[0]?.dataType || '',
    objectId: list[0]?.code || '',
    filterType: 'operation',
    calcuSymbol: '',
    thresholds: [],
    enumList: [],
    subFilters: []
  } as never)
}
const deleteAll = () => {
  filtersList.value = []
}
// 添加并列条件
const addFilters = async (item: any, index: number) => {
  const list = analyseData.$state.operationFilterLists || []
  if (!filtersList.value[index].subFilters.length) {
    filtersList.value[index].subFilters.push({
      objectName: item.objectName,
      objectDisplayName: item.objectDisplayName,
      objectType: item.objectType,
      objectId: item.objectId,
      filterType: item.filterType,
      calcuSymbol: item.calcuSymbol,
      thresholds: item.thresholds,
      enumList: item.enumList,
    })
  }
  filtersList.value[index].subFilters.push({
    objectName: list[0]?.name || '',
    objectDisplayName: list[0]?.displayName || '',
    objectType: list[0]?.dataType || '',
    objectId: list[0]?.code || '',
    filterType: 'operation',
    calcuSymbol: '',
    thresholds: [],
    enumList: []
  })
}
const addItemFilters = async (index: number) => {
  const list = analyseData.$state.operationFilterLists || []
  filtersList.value[index].subFilters.push({
    objectName: list[0]?.name || '',
    objectDisplayName: list[0]?.displayName || '',
    objectType: list[0]?.dataType || '',
    objectId: list[0]?.code || '',
    filterType: 'operation',
    calcuSymbol: '',
    thresholds: [],
    enumList: []
  } as never)
}
const deleteFilters = (index: number) => {
  filtersList.value.splice(index, 1)
}
const removeMultipleListItem = (index: number, num: number) => {
  filtersList.value[index].subFilters.splice(num, 1)
  if (filtersList.value[index].subFilters.length === 1) {
    // 删的剩最后两个的时候，外层变为剩下的一个
    // const data = cloneDeep(filtersList.value[index].subFilters[0])
    const filter = {...filtersList.value[index].subFilters[0]};
    filter.isAnd = true;
    filter.subFilters = [];
    filtersList.value[index] = filter
  }
}
// tabselect组件传参改变值
const subChange = (index: number, num: number, e) => {
  const {objectName, type, objectDisplayName, objectType, calcuSymbol, thresholds, filterType, objectId, enumList} = e
  const filter = {...filtersList.value[index].subFilters[num]};
  filter.objectName = objectName;
  filter.objectDisplayName = objectDisplayName;
  filter.type = type;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  filter.enumList = enumList
  filtersList.value[index].subFilters[num] = filter;
}
const filChange = (index: number, e) => {
  const {objectName, type, objectDisplayName, objectType, calcuSymbol, thresholds, filterType, objectId, enumList} = e
  const filter = {...filtersList.value[index]};
  filter.objectName = objectName;
  filter.objectDisplayName = objectDisplayName;
  filter.type = type;
  filter.objectType = objectType;
  filter.objectId = objectId
  filter.filterType = filterType
  filter.calcuSymbol = calcuSymbol
  filter.thresholds = thresholds
  filter.enumList = enumList
  filtersList.value[index] = filter;
}

watch(filtersList, (newValue, oldValue) => {
  if (listChangeFromProps.value){
    listChangeFromProps.value = false;
    return;
  }
  eventQueryfilter.logicalOperation = isAnd.value ? 'and' : 'or'
  eventQueryfilter.filters = filtersList.value.map(item => {
    return {
      objectName: item.objectName,
      objectDisplayName: item.objectDisplayName,
      type: item.type,
      logicalOperation: item.isAnd ? 'and' : 'or',
      objectType: item.objectType,
      objectId: item.objectId,
      filterType: item.filterType,
      calcuSymbol: item.calcuSymbol,
      thresholds: item.thresholds,
      enumList: item.enumList,
      subFilters: item.subFilters
    }
  })
  emits('queryFiltersChange', eventQueryfilter)
}, {immediate: true, deep: true})
defineExpose({
  add, filtersList, deleteAll

})
</script>

<style scoped lang="less">
.filter-btn {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all .3s;
  box-sizing: border-box;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.guide {
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {

    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 50px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .filter-btn {
        display: inline-flex;
        align-items: center;
        min-width: 40px;
        max-width: 200px;
        height: 26px;
        padding: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: top;
        background-color: var(--tant-secondary-color-secondary-fill);
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all .3s;
        box-sizing: border-box;

        .btn-icon {
          color: var(--tant-text-gray-color-text1-2);
          font-size: 14px;
          margin-right: 5px;
        }

        .filter-label {
          display: inline-block;
          max-width: 300px;
          overflow: hidden;
          color: var(--tant-text-gray-color-text1-2);
          white-space: nowrap;
          text-overflow: ellipsis;
          vertical-align: top;
        }

        &:hover {
          border-color: var(--tant-primary-color-primary-hover);
        }
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);

        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }

        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }

        .action-right {
          opacity: 1;
        }
      }
    }

    .row-filters {
      // padding-left: 38px;
      .relation-editor {
        box-sizing: border-box;
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;

        .relation-relation {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          width: 24px;
          margin-right: 8px;
          flex-shrink: 0;

          .relation-relation-line, .relation-relation-disabled-line {
            position: absolute;
            left: 12px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
            top: 6px;
            height: calc(100% - 12px);
          }

          .relation-relation-value, .relation-relation-disabled-value {
            position: absolute;
            text-transform: uppercase;
            top: 50%;
            left: 50%;
            transform: translate(-50%);
            height: 24px;
            padding: 0 5px;
            margin-top: -12px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            line-height: 22px;
            text-align: center;
            background-color: #fff;
            border: 1px solid var(--tant-border-color-border1-2);
            border-radius: 4px;
            word-break: keep-all;
            transition: all .3s;
          }

          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }

          .relation-relation-value {
            cursor: pointer;
          }

          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }
        }

        .relation-main {
          flex: 1 1;

          .relation-row {
            box-sizing: border-box;

            .multi-filter-condition {
              .sub-action-row {
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;

                .sub-action-left {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                }

                .sub-action-right {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  display: flex;
                  align-items: center;
                  opacity: 0;
                  transition: opacity .3s;
                }

                &:hover .sub-action-right {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>