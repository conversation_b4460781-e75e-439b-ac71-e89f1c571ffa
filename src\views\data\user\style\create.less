.page-container {
  height: 100%;
  width: 100%;

  .header {
    background: var(--color-bg-2);
    margin-bottom: 24px;
  }

  .content {
    padding: 24px;

    .title {
      color: var(--tant-text-gray-color-text1-1);
      font-weight: 500;
      font-size: 28px;
      line-height: 32px;
      text-align: center;
    }

    .card-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      max-width: 1152px;
      margin: 40px auto 0;
      box-sizing: border-box;

      .card-item {
        display: flex;
        cursor: pointer;

        .card-wrapper {
          display: flex;
          justify-content: flex-start;
          width: 350px;
          margin: 17px;
          padding: 30px;
          text-align: left;
          background-color: var(--tant-bg-white-color-bg1-1);
          border-radius: 10px;
          transform: translateY(0);
          transition-duration: .2s;
          box-sizing: border-box;
          transition-property: transform, box-shadow;

          img {
            height: 64px;
            margin-right: 18px;
          }

          .card-left {
            .card-title {
              color: var(--tant-text-gray-color-text1-1);
              font-weight: 500;
              font-size: 20px;
            }

            .card-desc {
              margin-top: 8px;
              color: var(--color-gray-blue-5);
              font-size: 14px;
              line-height: 26px;
            }
          }
        }

        &:hover {
          box-shadow: 0 40px 130px rgba(10, 16, 50, .05), 0 30px 90px rgba(10, 16, 50, .03), 0 20px 50px rgba(10, 16, 50, .02);
          transform: translateY(-13px);
          filter: drop-shadow(0 40px 130px rgba(10, 16, 50, .05)) drop-shadow(0 30px 90px rgba(10, 16, 50, .03)) drop-shadow(0 20px 50px rgba(10, 16, 50, .02));
          transition-duration: .2s;
          transition-property: transform, box-shadow, filter;
        }
      }
    }
  }
}