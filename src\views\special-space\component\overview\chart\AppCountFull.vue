<script setup lang="ts">
import {computed} from "vue";
import useChartOption from "@/hooks/chart-option";

interface Props {
  chartData: any
}

const props = defineProps<Props>()

// 计算动态图例数据
const legendData = computed(() => {
  const legends = [] as any;
  // 添加分组的总收入图例
  props.chartData?.y?.forEach((groupData, groupIndex) => {
    const groupName = groupData.group?.join('-') || `-`;
    if(props.chartData?.y?.length > 1 || (groupData.group && groupData.group.length > 0)){
      legends.push(`产品数-${groupName}`);
    } else{ 
      legends.push(`产品数`);
    }
  });
  // 添加详细数据图例
  props.chartData?.detailNames?.forEach(detailName => {
    props.chartData.y?.forEach((groupData, groupIndex) => {
      const groupName = groupData.group?.join('-') || `-`;
      if(props.chartData?.y?.length > 1 || (groupData.group && groupData.group.length > 0)){
        legends.push(`${detailName}-${groupName}`);
      } else{ 
        legends.push(`${detailName}`);
      }
    });
  });
  
  return legends;
});

// 计算series数据
const seriesData = computed(() => {
  const series = [] as any;
  // 为每个分组创建总收入线图
  props.chartData?.y?.forEach((groupData, groupIndex) => {
    const groupName = groupData.group?.join('-') || `-`;
    const seriesItem: any = {
      type: 'line',
      data: groupData.total || [],
      label: {
        show: true,
        position: 'top',
        formatter(params) {
          if (!params.value || typeof params.value !== 'number') {
            return params.value;
          }
          if (params.value >= 1000000) {
            return `${Math.round(params.value / 1000000)}M`;
          }
          if (params.value >= 1000) {
            return `${Math.round(params.value / 1000)}k`;
          }
          return Math.round(params.value);
        }
      },
      markLine: {
        symbol: 'none',
        data: [{type: 'average', name: 'Avg'}],
        label: {
          show: true,
          position: 'end'
        }
      }
    };
    if (props.chartData?.y?.length > 1 || (groupData.group && groupData.group.length > 0)) {
      seriesItem.name = `产品数-${groupName}`;
    } else {
      seriesItem.name = '产品数';
    }
    series.push(seriesItem);
  });
  
  // 为每个detailName和分组组合创建柱状图
  props.chartData?.detailNames?.forEach((detailName, detailIndex) => {
    props.chartData.y?.forEach((groupData, groupIndex) => {
      const groupName = groupData.group?.join('-') || `-`;
      const seriesItem: any = {
        type: 'bar',
        stack: 'total',
        data: groupData.detail?.[detailIndex] || [],
      };
      if (props.chartData?.y?.length > 1 || (groupData.group && groupData.group.length > 0)) {
        seriesItem.name = `${detailName}-${groupName}`;
      } else {
        seriesItem.name = detailName;
      }
      series.push(seriesItem);
    });
  });
  
  return series;
});

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '0',
      top: '10',
      bottom: '48'
    },
    legend: {
      data: legendData.value,
      bottom: '0',
      type: 'scroll'
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      className: 'echarts-tooltip-diy',
      extraCssText: 'max-height: 300px; overflow-y: auto;'
    },
    xAxis: {
      type: 'category',
      data: props.chartData?.x || [],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`;
          }
          if (value >= 1000) {
            return `${value / 1000}k`;
          }
          return value;
        }
      }
    },
    series: seriesData.value
  };
});


</script>

<template>
  <Chart :option="chartOption"/>
</template>

<style scoped lang="less">

</style>
