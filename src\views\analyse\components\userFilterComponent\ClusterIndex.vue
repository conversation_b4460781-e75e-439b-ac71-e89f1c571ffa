<template>
  <div class="guide">
    <div
      v-show="gameUserCluster.condition.singleConditionExpressions.length ||
              gameUserCluster.condition.sequenceConditionExpressions.length ||
              gameUserCluster.condition.userCondition?.filters?.length ||
              gameUserCluster.condition.groupCondition?.filters?.length ||
              (gameUserCluster.condition.clientCondition && Object.keys(gameUserCluster.condition.clientCondition.rules).length)"
              class="event-filter-box">
      <div class="action-row">
        <div class="row-filters">
          <div class="relation-editor">
            <div
              v-if="getTotalConditionCount"
              class="relation-relation">
              <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
              <div :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'" @click="logicalChange">
                <span v-if="gameUserCluster.condition.logicalOperation == 'and'">且</span>
                <span v-else>或</span>
              </div>
            </div>

            <div style="display: flex;flex-direction: column;">
              <div class="relation-editor">
                <div
                  class="relation-relation">
                  <em :class="!props.disabled ? 'relation-relation-line' : 'relation-relation-disabled-line'"></em>
                  <div 
                    v-if="gameUserCluster.condition.sequenceConditionExpressions.length >1 ||
                    gameUserCluster.condition.singleConditionExpressions.length >1 ||
                    gameUserCluster.condition.sequenceConditionExpressions.length +gameUserCluster.condition.singleConditionExpressions.length >1"
                    :class="!props.disabled ? 'relation-relation-value' : 'relation-relation-disabled-value'"
                    @click="() => {
                      if(!props.disabled) {
                        gameUserCluster.condition.eventIsAnd = gameUserCluster.condition.eventIsAnd === 'and' ? 'or' : 'and'
                      }
                    }">
                    <span v-if="gameUserCluster.condition.eventIsAnd == 'and'">且</span>
                    <span v-else>或</span>
                  </div>
                </div>
                <div style="display: flex;flex-direction: column;">
              <!-- 做没做过的事件 -->
              <div class="relation-main">
                <div v-for="(item, index) in gameUserCluster.condition.singleConditionExpressions" :key="index" class="relation-row">
                  <div class="multi-filter-condition">
                    <div class="sub-action-row">
                      <div class="sub-action-left">
                        <a-space wrap size="small" style="width: 100%;">
                          <div v-if="!props.disabled">
                            <a-trigger
                              v-model:popup-visible="doSelectVisible[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="doSelectVisible[index] = !doSelectVisible[index];">
                              <div class="filter-btn" style="width: 60px;margin-left:4px; ">
                                <span class="filter-label">{{ item.isDone ? '做过' : '没做过' }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible[index] = false; }">
                                    <div>
                                      做过
                                    </div>
                                  </div>
                                  <div class="filter-select-item" @click="() => { item.isDone = false; doSelectVisible[index] = false; }">
                                    <div>
                                      没做过
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-else>
                            <div class="filter-btn-disabled" style="width: 60px;margin-left:4px; ">
                              <span class="filter-label">{{ item.isDone ? '做过' : '没做过' }}</span>
                            </div>
                          </div>
                          <div style="width: 100%;">
                            <EventIndicatorSelect
                              :disabled="props.disabled"
                              :panel-data="item"
                              :only-evt="props.onlyEvt"
                              @analysis-index-change="(event)=>{
                                if(event.type === 'indicator'){
                                  item.indicatorCode = event.indicatorCode;
                                  item.indicatorDisplayName = event.indicatorDisplayName;
                                  item.indicatorName = event.indicatorName;
                                  item.eventType = event.eventType;
                                  item.type = event.type;
                                  delete item?.eventCode
                                  delete item?.eventName
                                  delete item?.eventDisplayName
                                }else{
                                  item.eventCode = event.eventCode;
                                  item.eventDisplayName = event.eventDisplayName;
                                  item.eventName = event.eventName;
                                  item.eventType = event.eventType;
                                  item.type = event.type;
                                  delete item?.indicatorCode
                                  delete item?.indicatorName
                                  delete item?.indicatorDisplayName
                                }
                            }"/>
                          </div>
                          <div v-if="item.isDone" class="itemIndexTitle">
                            的
                          </div>
                          <div v-if="item.isDone && item.type == 'event'">
                            <EventAttrSelect
                              :disabled="props.disabled"
                              :panel-data="item"
                              @sub-change="(event) => {
                              item.eventAttrName = event.eventAttrName;
                              item.eventAttrCode = event.eventAttrCode;
                              item.eventAttrDisplayName = event.eventAttrDisplayName;
                              item.statisticalType = event?.statisticalType || undefined;
                              item.objectType = event.objectType
                              }
                              "/>
                          </div>
                          <div v-if="item.isDone && !props.disabled">
                            <a-trigger
                              v-model:popup-visible="symbolSelectVisible[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="symbolSelectVisible[index] = !symbolSelectVisible[index];">
                              <div class="filter-btn " style="width: 75px;">
                                <span class="filter-label">{{ transformSymbol(item.calcuSymbol) }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.GREATER_THAN, symbolSelectVisible[index] = false;}">
                                    <div>大于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.GREATER_THAN_OR_EQUAL, symbolSelectVisible[index] = false;}">
                                    <div>大于等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.LESS_THAN, symbolSelectVisible[index] = false; }">
                                    <div>小于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => {item.calcuSymbol = CalculateSymbol.LESS_THAN_OR_EQUAL, symbolSelectVisible[index] = false;}">
                                    <div>小于等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item" @click="() => { item.calcuSymbol = CalculateSymbol.EQUAL, symbolSelectVisible[index] = false; }">
                                    <div>等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item" @click="() => { item.calcuSymbol = CalculateSymbol.NOT_EQUAL, symbolSelectVisible[index] = false; }">
                                    <div>不等于</div>
                                  </div>
                                  <div
                                    class="filter-select-item"
                                    @click="() => { item.calcuSymbol = CalculateSymbol.SCOPE, symbolSelectVisible[index] = false;}">
                                    <a-tooltip position="left" :popup-translate="[-5, 0]" mini>
                                      <template #content>
                                        区间：属性值在设置的数值区间范 <br> 围内(左闭右闭)
                                      </template>
                                      <div style="display: flex;justify-content: space-between;">
                                        区间
                                        <icon-info-circle/>
                                      </div>
                                    </a-tooltip>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-if="item.isDone && props.disabled">
                            <div class="filter-btn-disabled" style="width: 75px;">
                                <span class="filter-label">{{ transformSymbol(item.calcuSymbol) }}</span>
                              </div>
                          </div>
                          <div v-show="item.isDone"  v-if="item.calcuSymbol !== CalculateSymbol.SCOPE">
                            <a-input-number v-model="item.thresholds[0]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => firsetThresholdChange(index, val)"/>
                          </div>
                          <div v-show="item.isDone" v-else style="display: flex;flex-direction: row;width: 100%;">
                            <a-input-number v-model="item.thresholds[0]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => handleThresholdChange(index, val)"/>
                            <div class="itemIndexTitle">
                              与
                            </div>
                            <a-input-number v-model="item.thresholds[1]" :disabled="props.disabled" style="cursor: text;width: 88px;height:26px" @change="(val) => handleThresholdChange(index, val)"/>
                            <div class="itemIndexTitle">
                              之间
                            </div>
                          </div>
                          <div style="color: var(--tant-text-gray-color-text1-3);line-height: 27px;width: 30px;">
                            ，在
                          </div>
                          <div v-if="!showUnifiedCycle && !props.disabled">
                            <date-picker disabled-after-today :date-range="item.dateRange" @date-pick="date => {item.dateRange = date;}"/>
                          </div>
                          <div v-else>
                            <button class="filter-btn-disabled">
                              {{ unifiedDateRange.dateText}}
                              <icon-calendar/>
                            </button>
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" :size="2">
                          <a-tooltip content="添加筛选条件" position="top">
                            <a-button class="btn-bg btn-26" style="margin-left: 8px;" @click="addFilters(index)">
                              <template #icon>
                                <icon-filter/>
                              </template>
                            </a-button>
                          </a-tooltip>
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" @click="deleteFilters(index)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>
                    <filterComponent :ref="item => queryFilterRefs[index] = item" :filter="item.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...item }]" @query-filters-change="queryFiltersChange(index,$event)"/>
                  </div>
                </div>
              </div>

              <!-- 依次做过事件 -->
              <div class="relation-main">
                <div v-for="(item, index) in gameUserCluster.condition.sequenceConditionExpressions" :key="index" class="relation-row">
                  <div class="multi-filter-condition">
                    <div class="sub-action-row">
                      <div class="sub-action-left">
                        <a-space wrap size="small" style="width: 100%;">
                          <div v-if="!props.disabled">
                            <a-trigger
                              v-model:popup-visible="doSelectVisible2[index]"
                              trigger="click"
                              :unmount-on-close="false"
                              position="bl"
                              :update-at-scroll="true"
                              @click="doSelectVisible2[index] = !doSelectVisible2[index];">
                              <div class="filter-btn" style="margin-left: 4px;">
                                <span class="filter-label">{{ item.isDone ? '依次做过' : '依次没做过' }}</span>
                              </div>
                              <template #content>
                                <div class="filter-select">
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible2[index] = false; }">
                                    <div>
                                      依次做过
                                    </div>
                                  </div>
                                  <div class="filter-select-item" @click="() => { item.isDone = true; doSelectVisible2[index] = false; }">
                                    <div>
                                      依次没做过
                                    </div>
                                  </div>
                                </div>
                              </template>
                            </a-trigger>
                          </div>
                          <div v-else>
                            <div class="filter-btn-disabled" style="margin-left: 4px;">
                              <span class="filter-label">{{ item.isDone ? '依次做过' : '依次没做过' }}</span>
                            </div>
                          </div>
                          <div class="itemIndexTitle">
                            以下事件，在
                          </div>
                          <date-picker :date-range="item.dateRange" disabled-after-today @date-pick="date => {item.dateRange = date;}"/>
                          <button v-if="showUnifiedCycle" class="filter-btn" :disabled="props.disabled">
                            {{ unifiedDateRange.dateText}}
                            <icon-calendar/>
                          </button>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <div class="sub-action-row">
                          <a-space align="center" :size="2">
                            <a-dropdown trigger="hover">
                              <a-button class="btn-bg btn-26" style="margin-left: 8px;">
                                <template #icon>
                                  <icon-more-vertical/>
                                </template>
                              </a-button>
                              <template #content>
                                <a-tooltip content="最多可添加5个关联属性">
                                  <a-doption v-show="item.sameAttributeList.length >= 5" disabled>添加关联属性</a-doption>
                                </a-tooltip>
                                <a-doption v-show="item.sameAttributeList.length < 5" @click="addItemSameEvent(index)">添加关联属性
                                </a-doption>
                                <a-doption v-show="item.timeWindow && Object.keys(item.timeWindow).length == 0" @click="addTimeWindow(index)">添加时间窗口
                                </a-doption>
                                <a-doption v-show="item.timeWindow && Object.keys(item.timeWindow).length !== 0" @click="deleteTimeWindow(index)">删除时间窗口
                                </a-doption>
                              </template>
                            </a-dropdown>
                            <a-tooltip content="添加事件" position="top">
                              <a-button v-show="item.eventList.length < 20" class="btn-bg btn-26" @click="addSeqEvent( index)">
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="最多可添加20个事件" position="top">
                              <a-button v-show="item.eventList.length >= 20" class="btn-bg btn-26" disabled>
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="删除" position="top">
                              <a-button class="btn-bg-delete btn-26" @click="deleteDone(index)">
                                <template #icon>
                                  <icon-close-circle/>
                                </template>
                              </a-button>
                            </a-tooltip>
                          </a-space>
                        </div>
                      </div>
                    </div>
                    <div v-for="(Same, SameIndex) in item.sameAttributeList" :key="Same.code" class="sub-action-row">
                      <div class="sub-action-left" style="padding-left: 80px;">
                        <a-space align="center" size="small" style="width: 100%;">
                          <div class="itemIndexTitle">
                            且
                          </div>
                          <div>
                            <attrEnumSelect
                              :disabled="props.disabled"
                              :only-event="true"
                              :show-detail-filter="false"
                              :info="{
                                objectName: Same.name,
                                objectId: Same.code,
                                objectDisplayName: Same.displayName,
                                type: Same.type,
                                objectType: Same.objectType,
                                filterType: Same.filterType
                              }"
                              @tabs-change="(event)=>{
                                Same.code = event.objectId;
                                Same.name = event.objectName;
                                Same.displayName = event.objectDisplayName;
                                Same.type = event.type;
                                Same.objectType = event.objectType;
                                Same.filterType = event.filterType;
                              }"/>
                          </div>
                          <div class="itemIndexTitle">
                            相同
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" size="mini">
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteSameEvent(index, SameIndex)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>

                    <div v-if="item.timeWindow && Object.keys(item.timeWindow).length !== 0" class="sub-action-row">
                      <div class="sub-action-left" style="padding-left: 80px;">
                        <a-space align="center" size="small" style="width: 100%;">
                          <div class="itemIndexTitle">
                            且全部在
                          </div>
                          <div>
                            <DateTimeDropdown :time-window-data="item.timeWindow" @change="(date) => { item.timeWindow.value = date.value;item.timeWindow.unit = date.unit}"/>
                          </div>
                          <div class="itemIndexTitle">
                            内完成
                          </div>
                        </a-space>
                      </div>
                      <div v-if="!props.disabled" class="sub-action-right">
                        <a-space align="center" size="mini">
                          <a-tooltip content="删除" position="top">
                            <a-button class="btn-bg-delete btn-26" style="margin-left: 8px;" @click="deleteTimeWindow(index)">
                              <template #icon>
                                <icon-close-circle/>
                              </template>
                            </a-button>
                          </a-tooltip>
                        </a-space>
                      </div>
                    </div>

                    <div v-for="(Same, SameIndex) in item.eventList" :key="SameIndex">
                      <div class="sub-action-row">
                        <div class="sub-action-left" style="padding-left: 80px;">
                          <a-space align="center" size="small" style="width: 100%;">
                            <div class="cluster-condition-indexName">
                              {{ SameIndex + 1 }}
                            </div>
                            <div>
                              <EventIndicatorSelect
                                :disabled="props.disabled"
                                :only-evt="props.onlyEvt"
                                :panel-data="Same" @analysis-index-change="(event)=>{
                                if(event.type === 'indicator'){
                                  Same.indicatorCode = event.indicatorCode;
                                  Same.indicatorDisplayName = event.indicatorDisplayName;
                                  Same.indicatorName = event.indicatorName;
                                  Same.eventType = event.eventType;
                                  Same.type = event.type;
                                  delete Same?.eventCode
                                  delete Same?.eventName
                                  delete Same?.eventDisplayName
                                }else{
                                  Same.eventCode = event.eventCode;
                                  Same.eventDisplayName = event.eventDisplayName;
                                  Same.eventName = event.eventName;
                                  Same.eventType = event.eventType;
                                  Same.type = event.type;
                                  delete Same?.indicatorCode
                                  delete Same?.indicatorName
                                  delete Same?.indicatorDisplayName
                                }
                              }"/>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              ，在
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && !props.disabled" class="itemIndexTitle">
                              <a-trigger
                                v-model:popup-visible="followIndexVisible[SameIndex]"
                                trigger="click"
                                :unmount-on-close="false"
                                position="bl"
                                :update-at-scroll="true"
                                @click="followIndexVisible[SameIndex] = !followIndexVisible[SameIndex];">
                                <div class="filter-btn" style="width: 60px;margin-left:4px; ">
                                  <span class="filter-label">{{ Same.timeWindow.afterIndex }}</span>
                                </div>
                                <template #content>
                                  <div class="filter-select">
                                    <div v-show="SameIndex !== 1" class="filter-select-item" @click="() => { Same.timeWindow.afterIndex = 1; followIndexVisible[SameIndex] = false; }">
                                      <div>
                                        1
                                      </div>
                                    </div>
                                    <div class="filter-select-item" @click="() => { Same.timeWindow.afterIndex = SameIndex; followIndexVisible[SameIndex] = false; }">
                                      <div>
                                        {{ SameIndex }}
                                      </div>
                                    </div>
                                  </div>
                                </template>
                              </a-trigger>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && props.disabled" class="itemIndexTitle">
                              <div class="filter-btn-diabled" style="width: 60px;margin-left:4px; ">
                                <span class="filter-label">{{ Same.timeWindow.afterIndex }}</span>
                              </div>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              后
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              <DateTimeDropdown :time-window-data="Same.timeWindow" disabled-pop @change="(date) => { Same.timeWindow.value = date.value;Same.timeWindow.unit = date.unit }"/>
                            </div>
                            <div v-if="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0" class="itemIndexTitle">
                              内
                            </div>
                          </a-space>
                        </div>
                        <div v-if="!props.disabled" class="sub-action-right">
                          <a-space align="center" :size="2">
                            <a-dropdown trigger="hover">
                              <a-button class="btn-bg btn-26" style="margin-left: 8px;">
                                <template #icon>
                                  <icon-more-vertical/>
                                </template>
                              </a-button>
                              <template #content>
                                <a-doption v-show="Same.timeWindow && Object.keys(Same.timeWindow).length == 0&&SameIndex > 0" @click="addSeqTimeWindow(index, SameIndex)">添加时间窗口
                                </a-doption>
                                <a-doption v-show="SameIndex+1 < item.eventList.length" @click="addItemNeverEvent(index, SameIndex)">添加之间没做过
                                </a-doption>
                                <a-doption v-show="Same.timeWindow && Object.keys(Same.timeWindow).length !== 0 && SameIndex > 0" @click="delSeqTimeWindow(index, SameIndex)">删除时间窗口
                                </a-doption>
                              </template>
                            </a-dropdown>
                            <a-tooltip content="添加事件" position="top">
                              <a-button v-show="item.eventList.length <20" class="btn-bg btn-26" @click="addSeqEvent(index)">
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="最多可添加20个事件" position="top">
                              <a-button v-show="item.eventList.length >= 20" class="btn-bg btn-26" style="margin-left: 8px;" disabled>
                                <template #icon>
                                  <icon-plus/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="添加筛选" position="top">
                              <a-button class="btn-bg btn-26" @click="addSeqFilters(SameIndex)">
                                <template #icon>
                                  <icon-filter/>
                                </template>
                              </a-button>
                            </a-tooltip>
                            <a-tooltip content="删除" position="top">
                              <a-button v-show="SameIndex > 1" class="btn-bg-delete btn-26" @click="deleteSeqEvent(index, SameIndex)">
                                <template #icon>
                                  <icon-close-circle/>
                                </template>
                              </a-button>
                            </a-tooltip>
                          </a-space>
                        </div>
                      </div>
                      <div class="sequence-content">
                        <filterComponent :ref="Same => sameFilterRefs[SameIndex] = Same" :filter="Same.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...Same }]" @query-filters-change="sameFiltersChange(index,SameIndex,$event)"/>
                      </div>
                      <div v-show="Same.notDownEventList?.length" class="relation-editor" style="padding-left: 80px;">
                        <div class="relation-relation">
                          <em class="relation-relation-line"></em>
                        </div>
                        <div class="relation-main">
                          <div style="display: flex;flex-direction: row;" class="sub-action-row">
                            <div class="cluster-condition-indexName">
                              {{ SameIndex + 1 }}
                            </div>
                            <div class="cluster-condition-indexName" style="margin:0 4px;">
                              {{ SameIndex + 2 }}
                            </div>
                            <div class="itemIndexTitle">
                              之间，没做过
                            </div>
                          </div>
                          <div v-for="(Never, NeverIndex) in Same.notDownEventList" :key="NeverIndex">
                            <div class="sub-action-row">
                              <div class="sub-action-left">
                                <a-space align="center" size="small" style="width: 100%;">
                                  <div>
                                  <EventIndicatorSelect
                                      :disabled="props.disabled"
                                      :panel-data="Never"
                                      :only-evt="props.onlyEvt"
                                      @analysis-index-change="(event)=>{
                                      if(event.type === 'indicator'){
                                        Never.indicatorCode = event.indicatorCode;
                                        Never.indicatorDisplayName = event.indicatorDisplayName;
                                        Never.indicatorName = event.indicatorName;
                                        Never.eventType = event.eventType;
                                        Never.type = event.type;
                                        delete Never?.eventCode
                                        delete Never?.eventName
                                        delete Never?.eventDisplayName
                                      }else{
                                        Never.eventCode = event.eventCode;
                                        Never.eventDisplayName = event.eventDisplayName;
                                        Never.eventName = event.eventName;
                                        Never.eventType = event.eventType;
                                        Never.type = event.type;
                                        delete Never?.indicatorCode
                                        delete Never?.indicatorName
                                        delete Never?.indicatorDisplayName
                                      }
                                  }"/>
                                  </div>
                                </a-space>
                              </div>
                              <div v-if="!props.disabled" class="sub-action-right">
                                <a-space align="center" :size="2">
                                  <a-tooltip content="添加事件" position="top">
                                    <a-button v-show="Same.notDownEventList?.length < 5" class="btn-bg btn-26" style="margin-left: 8px;" @click="addNeverEvent(index, SameIndex)">
                                      <template #icon>
                                        <icon-plus/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip position="top">
                                    <template #content>
                                      事件间最多可添加5个之间“没做 <br> 过事件”
                                    </template>
                                    <a-button v-show="Same.notDownEventList?.length >= 5" class="btn-bg btn-26" style="margin-left: 8px;" disabled>
                                      <template #icon>
                                        <icon-plus/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip content="添加筛选" position="top">
                                    <a-button class="btn-bg btn-26" @click="addNeverFilters(NeverIndex)">
                                      <template #icon>
                                        <icon-filter/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                  <a-tooltip content="删除" position="top">
                                    <a-button class="btn-bg-delete btn-26" @click="delNeverEvent(index, SameIndex, NeverIndex)">
                                      <template #icon>
                                        <icon-close-circle/>
                                      </template>
                                    </a-button>
                                  </a-tooltip>
                                </a-space>
                              </div>
                            </div>
                            <div class="user-content">
                              <filterComponent :ref="Never => neverFiltersRefs[NeverIndex] = Never" :filter="Never.filter" :disabled="props.disabled" :only-event="true" :show-detail-filter="true" :code-list="[{ ...Never}]" @query-filters-change="neverFiltersChange(index,SameIndex,NeverIndex,$event)"/>
                            </div>

                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
                </div>
              </div>
              <!-- 用户属性满足 -->
              <div class="user-content">
                <filterComponent
                  ref="userFilterRef"
                  :filter="gameUserCluster.condition.userCondition"
                  :disabled="props.disabled"
                  :only-user="true"
                  :show-detail-filter="true"
                  @query-filters-change="userFiltersChange"/>
              </div>
              <!-- 用户分群满足 -->
              <div class="user-content">
                <filterComponent
                  ref="groupFilterRef"
                  :filter="gameUserCluster.condition.groupCondition"
                  :disabled="props.disabled"
                  :only-group="true"
                  :show-detail-filter="true"
                  @query-filters-change="groupFiltersChange"/>
              </div>
               <!-- 客户端参数 -->
              <div v-if="props.isProvideClient" class="client-content">
                <ClientSelect ref="clientRef" :disabled="props.disabled" :client-params="gameUserCluster.condition.clientCondition" @client-change="clientChange"/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {computed, onMounted, reactive, ref, watch} from "vue";
import {debounce} from 'lodash'
import {Message} from "@arco-design/web-vue";
import DatePicker from "@/components/date-picker/index.vue";
import {CalculateSymbol, LogicalOperationType} from "@/api/enum";
import {DateRange, EventQueryFilter} from "@/api/analyse/type";
import EventAttrSelect from "@/views/analyse/components/userFilterComponent/EventAttrSelect.vue";
import DateTimeDropdown from "@/views/analyse/components/userFilterComponent/DateTimeDropdown.vue";
import EventIndicatorSelect from "@/views/analyse/components/EventIndicatorSelect.vue";
import filterComponent from "@/views/analyse/components/AttrCategoryFilter.vue"
import attrEnumSelect from "@/views/analyse/components/attrEnumSelect/index.vue"
import ClientSelect from '@/views/analyse/components/userFilterComponent/ClientSelect.vue';
import {toolStore} from '@/store';
import {handleAttrFlatData} from "@/views/analyse/components/util/handleAttrFlatData";


const toolData = toolStore();
// const attrBus = useEventBus('attrList');
const emits = defineEmits(['change'])
const doSelectVisible = ref<boolean[]>([])
const doSelectVisible2 = ref<boolean[]>([])
const symbolSelectVisible = ref<boolean[]>([])
const followIndexVisible = ref<boolean[]>([])
const props = defineProps({
  excludeEvent: {
    type: Boolean,
    default: false
  },
  codeList:{
      type:Array,
      default:() => []
  },
  // 回显传参
  userFilter:{
    type:Object,
    default:() => {}
  },
  // 禁用只读
  disabled:{
    type: Boolean,
    default: false
  },
  // 只选事件
  onlyEvt:{
    type: Boolean,
    default: false
  },
  // 只选指标
  onlyIndicator:{
    type: Boolean,
    default: false
  },
  // 提供客户端参数条件
  isProvideClient:{
    type: Boolean,
    default: false
  },
})


const panelData = reactive({
  eventAttrCode: "",
  eventAttrName: "次数",
  eventDisplayName: "",
  eventName: "",
  eventType: "",
  eventCode:'',
  type:''
})
const firstEvent = ref()
// watch(() => props.modelLists,() => {
//   if(props.modelLists.length === 0){
//     return
//   }
//   const [firstEventValue] = props.modelLists
//   firstEvent.value = firstEventValue

//   panelData.eventDisplayName = firstEvent.value.displayName
//   panelData.eventName = firstEvent.value.name
//   panelData.eventType = firstEvent.value.type
//   panelData.type = firstEvent.value.objectType
//   panelData.eventCode = firstEvent.value.code
// },{immediate:true,deep:true})
onMounted(() => {
  if(toolData.toolModelList.length){
    const [firstEventValue] = toolData.toolModelList.flatMap(category => category.items || []) 
    firstEvent.value = firstEventValue
    panelData.eventDisplayName = firstEvent.value.displayName
    panelData.eventName = firstEvent.value.name
    panelData.eventType = firstEvent.value.type
    panelData.type = firstEvent.value.objectType
    panelData.eventCode = firstEvent.value.code
  }
})
const getDefaultData = async () => {
  if(!toolData.toolModelList.length){
    await toolData.fetchAllModalList()
  }
  const [firstEventValue] = toolData.toolModelList.flatMap(category => category.items || []) 
  firstEvent.value = firstEventValue
}
interface Formatter {
  name: string // 标签值
  desc: string // 说明
  condition: {
    logicalOperation: string // 事件条件与用户条件且/或
    eventIsAnd: string // 事件条件且/或
    singleConditionExpressions: {
      isDone: boolean, // true 做过 false 未做
      eventName: string, // 事件名称
      eventCode: string, // 事件编码
      eventType: string, // 事件类型
      eventDisplayName: string,
      eventAttrName: string, // 事件属性名称
      eventAttrCode: string, // 事件属性编码
      eventAttrDisplayName: string, // 事件属性展示名称
      logicalOperation: string
      filter: EventQueryFilter,
      calcuSymbol: CalculateSymbol, // 计算符号
      dateRange: DateRange, // 时间范围
      thresholds: (string | number)[], // 阈值数组
      statisticalType: (string | undefined), // 统计类型
      objectType:(string | undefined), // 属性类型
    }[]
    sequenceConditionExpressions: {
      isDone: boolean, // true 做过 false 未做
      logicalOperation: string
      eventList: {
        eventName: string, // 事件名称
        eventCode: string, // 事件编码
        eventType: string, // 事件类型
        eventDisplayName: string,
        timeWindow: { // 时间窗口
          afterIndex: (string | number), // 跟随index
          value: number // 时间值
          unit: string // 时间单位
        },
        notDownEventList: { // 之前未做过的事件列表
          eventName: string, // 事件名称
          eventCode: string, // 事件编码
          eventType: string, // 事件类型
          eventDisplayName: string,
          filter: EventQueryFilter
        }[],
        filter: EventQueryFilter,
      }[]
      filter: EventQueryFilter,
      calcuSymbol: CalculateSymbol, // 计算符号
      dateRange: DateRange, // 时间范围
      timeWindow: { // 时间窗口
        value: number // 时间值
        unit: string // 时间单位
      },
      sameAttributeList: { // 关联相同属性
        name: string, // 属性名称
        code: string, // 属性编码
        displayName: string, // 展示名称
        objectType:string,
        filterType:string,
        type:string
      }[]
    }[]
    userCondition: EventQueryFilter,
    groupCondition: EventQueryFilter,
  }
}

// 添加一个本地状态，与props完全分离
const localCondition = ref({
  logicalOperation: 'and', // 事件条件与用户条件且/或
  eventIsAnd: 'and', // 事件条件且/或
  singleConditionExpressions: [],
  sequenceConditionExpressions: [],
  userCondition: {logicalOperation: LogicalOperationType.AND, filters: []},
  groupCondition: {logicalOperation: LogicalOperationType.AND, filters: []},
  clientCondition:{logicalOperation: LogicalOperationType.AND,rules:{}}
});

// 标记数据来源的唯一key
const updateKey = ref(0);

// 将gameUserCluster的引用指向localCondition
// 这样模板中的所有gameUserCluster.value.condition的引用都会自动使用localCondition
const gameUserCluster = computed<Formatter>(() => {
  return {
    name: "示例标签",
    desc: "这是一个示例说明",
    condition: localCondition.value
  }
})

interface params {
  logicalOperation: string,
  filters: { objectName: string, objectType: string, objectId: string, filterType: string }[],
  subFilters: {
    logicalOperation: string,
    filters: { objectName: string, objectType: string, objectId: string, filterType: string }[]
  }[]
}

const eventQueryfilter = reactive<params>(
    {
      logicalOperation: 'and',
      filters: [],
      subFilters: []
    }
)

const transformSymbol = (symbol: string) => {
  switch (symbol) {
    case CalculateSymbol.EQUAL:
      return '等于'
    case CalculateSymbol.NOT_EQUAL:
      return '不等于'
    case CalculateSymbol.GREATER_THAN:
      return '大于'
    case CalculateSymbol.LESS_THAN:
      return '小于'
    case CalculateSymbol.GREATER_THAN_OR_EQUAL:
      return '大于等于'
    case CalculateSymbol.LESS_THAN_OR_EQUAL:
      return '小于等于'
    case CalculateSymbol.SCOPE:
      return '区间'
    default:
      return '自定义'
  }
}

const logicalChange = () => {
  if(!props.disabled){
    const newValue = gameUserCluster.value.condition.logicalOperation === 'and' ? 'or' : 'and';
    gameUserCluster.value.condition.logicalOperation = newValue;
    eventQueryfilter.logicalOperation = newValue;
  }
}

const add = () => {
  getDefaultData()
  
  gameUserCluster.value.condition.singleConditionExpressions.push({
    isDone: true,
    type:firstEvent.value.objectType,
    eventName: firstEvent.value.name, // 事件名称
    eventCode: firstEvent.value.code, // 事件编码
    eventType: firstEvent.value.type, // 事件类型
    eventDisplayName: firstEvent.value.displayName,
    eventAttrName: '', // 事件属性名称
    eventAttrCode: '', // 事件属性编码
    eventAttrDisplayName: '次数', // 事件属性展示名称
    logicalOperation: 'and',
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []},
    calcuSymbol: CalculateSymbol.GREATER_THAN, // 计算符号
    dateRange: {
      dateText: '最近7天',
      recentStartDate: 6,
      recentEndDate: 0
    }, // 时间范围
    thresholds: [0, 0] // 阈值数组
  } as never)
}

const firsetThresholdChange = (index, value: number) => {
  gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1] = value;
}
// 处理阈值变化
const handleThresholdChange = (index: number, value: number) => {
  const num1 = gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[0]
  const num2 = gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1]
  if ( num1 > num2) {
    gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[0] = value
    gameUserCluster.value.condition.singleConditionExpressions[index].thresholds[1] = value
  }
}
const addDone = () => {
  getDefaultData()
  if(gameUserCluster.value.condition.sequenceConditionExpressions.length>4){
    Message.info('最多添加5个')
    return
  }

  gameUserCluster.value.condition.sequenceConditionExpressions.push({
    isDone: true, // 依次做过
    eventList: [{
      type:firstEvent.value.objectType,
      eventName: firstEvent.value.name, // 事件名称
      eventCode: firstEvent.value.code, // 事件编码
      eventType: firstEvent.value.type, // 事件类型
      eventDisplayName: firstEvent.value.displayName,
      timeWindow: {},
      notDownEventList: [],
      filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
    }, {
      type:firstEvent.value.objectType,
      eventName: firstEvent.value.name, // 事件名称
      eventCode: firstEvent.value.code, // 事件编码
      eventType: firstEvent.value.type, // 事件类型
      eventDisplayName: firstEvent.value.displayName,
      timeWindow: {},
      notDownEventList: [],
      filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
    }],
    sameAttributeList: [],
    timeWindow: {},
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []},
    calcuSymbol: CalculateSymbol.GREATER_THAN,
    dateRange: {
      dateText: '最近7天',
      recentStartDate: 6,
      recentEndDate: 0
    }, // 时间范围
  } as never)
}

// 添加并列条件
const queryFilterRefs = ref<any>([])
const addFilters = (index: number) => {
    queryFilterRefs.value[index].add()
}
const queryFiltersChange = (index:number,v) => {
  gameUserCluster.value.condition.singleConditionExpressions[index].filter = v;
}
const userFilterRef = ref()
const addUser = async () => {
  userFilterRef.value.add()
}
// 提供用户属性满足组件回显数据
const userFiltersChange = (v) => {
  gameUserCluster.value.condition.userCondition = v;
}
const groupFilterRef = ref()
const addGroup = async () => {
  groupFilterRef.value.add()
}
const groupFiltersChange = (v) => {
  gameUserCluster.value.condition.groupCondition = v;
}
const clientChange = (v) => {
  gameUserCluster.value.condition.clientCondition = v
}
const sameFilterRefs = ref<any>([])
const sameFiltersChange = (index:number,eventIndex:number,v) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].filter = v
}

const neverFiltersRefs  = ref<any>([])
const neverFiltersChange = (index:number,eventIndex:number,neverIndex:number,v) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].notDownEventList[neverIndex].filter = v
}

const getTotalConditionCount = computed(() => {
  const { singleConditionExpressions, sequenceConditionExpressions, userCondition, groupCondition, clientCondition } = gameUserCluster.value.condition;
  const eventConditionExists = singleConditionExpressions.length > 0 || sequenceConditionExpressions.length > 0;
  const userConditionExists = (userCondition?.filters?.length || 0) > 0;
  const groupConditionExists = (groupCondition?.filters?.length || 0) > 0;
  const clientConditionExists = Array.isArray(clientCondition?.rules)
    ? clientCondition.rules.length > 0
    : (clientCondition?.rules && Object.keys(clientCondition.rules).length > 0);

  // 统计存在的条件类型数量
  const existCount = [eventConditionExists, userConditionExists, groupConditionExists, clientConditionExists].filter(Boolean).length;
  // 只要有两种及以上类型的条件存在，就显示且/或
  return existCount >= 2;
});
const addSeqEvent = (index: number) => {
  getDefaultData()
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.push({
    type:firstEvent.value.objectType,
    eventName: firstEvent.value.name, // 事件名称
    eventCode: firstEvent.value.code, // 事件编码
    eventType: firstEvent.value.type, // 事件类型
    eventDisplayName: firstEvent.value.displayName,
    timeWindow: {},
    notDownEventList: [],
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
  })
}

const addSeqFilters = (eventIndex: number) => {
  sameFilterRefs.value[eventIndex].add()
}

const deleteSeqEvent = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.splice(eventIndex, 1)
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList.forEach((item: any) => {
    if (item.timeWindow.afterIndex > eventIndex) {
      item.timeWindow.afterIndex -= 1
    }
  })
}

const deleteFilters = (index: number) => {
  gameUserCluster.value.condition.singleConditionExpressions.splice(index, 1)
}

const deleteDone = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions.splice(index, 1)
}

const addItemSameEvent = async (index: number) => {
  if(toolData.allAttrList.length === 0){
    await toolData.fetchAllAttrList()
  }
  const flatList = handleAttrFlatData(toolData.allAttrList, props.codeList);
  const attrList = flatList.filter(category => category.categoryName === '事件属性')
  const defaultItem = attrList.filter(category => category.itemData?.length > 0).flatMap(category => category.itemData || [])[0];
  gameUserCluster.value.condition.sequenceConditionExpressions[index].sameAttributeList.push({
    name: defaultItem.name, // 属性名称
    code: defaultItem.code, // 属性编码
    displayName: defaultItem.displayName, // 展示名称
    objectType: defaultItem.dataType,
    filterType: defaultItem.attributeType,
    type: defaultItem.type
  })
}
const addTimeWindow = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].timeWindow = {
    value: 1, // 时间值
    unit: 'day' // 时间单位
  }
}

const addSeqTimeWindow = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].timeWindow = {
    value: 1, // 时间值
    unit: 'day', // 时间单位
    afterIndex: eventIndex
  }
}
const addItemNeverEvent = (index: number, eventIndex: number) => {
  const eventItem = gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex];
  eventItem.notDownEventList.push({
    type: firstEvent.value.objectType,
    eventName: firstEvent.value.name,
    eventCode: firstEvent.value.code,
    eventType: firstEvent.value.type,
    eventDisplayName: firstEvent.value.displayName,
    filter: { logicalOperation: LogicalOperationType.AND, filters: [], subFilters: [] }
  })
}

const addNeverEvent = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].notDownEventList.push({
    type:firstEvent.value.objectType,
    eventName: firstEvent.value.name, // 事件名称
    eventCode: firstEvent.value.code, // 事件编码
    eventType: firstEvent.value.type, // 事件类型
    eventDisplayName: firstEvent.value.displayName,
    filter: {logicalOperation: LogicalOperationType.AND, filters: [], subFilters: []}
  })
}

const addNeverFilters = (neverIndex: number) => {
  neverFiltersRefs.value[neverIndex].add()
}

// 删除之间未做过事件列表
const delNeverEvent = (index: number, eventIndex: number, NeverIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].notDownEventList.splice(NeverIndex, 1)
}
// 删除时间窗口
const delSeqTimeWindow = (index: number, eventIndex: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].eventList[eventIndex].timeWindow = {}
}
// 删除相同事件
const deleteSameEvent = (index: number, num: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].sameAttributeList.splice(num, 1)
}
// 删除时间窗口
const deleteTimeWindow = (index: number) => {
  gameUserCluster.value.condition.sequenceConditionExpressions[index].timeWindow = {}
}


// 导入已有条件
const originFilters = (v: any, b?: any) => {
  gameUserCluster.value.condition = v
}
// 添加客户端参数
const clientRef = ref()
const addClientParams = () => {
  clientRef.value.addItem()
}
// 创建一个防抖的emit函数，减少延迟时间
const debouncedEmit = debounce((value) => {
  const filterParams = {
    logicalOperation: value.logicalOperation,
    eventCondition: {
      logicalOperation: value.eventIsAnd,
      singleConditionExpressions: value.singleConditionExpressions.map(item => ({
        ...item,
        thresholds: item.calcuSymbol !== 'scope' ? [item.thresholds[0]] : item.thresholds
      })),
      sequenceConditionExpressions: value.sequenceConditionExpressions,
    },
    userCondition: value.userCondition,
    groupCondition: value.groupCondition,
    clientCondition: props.isProvideClient ? value.clientCondition : undefined,
  }
  emits('change', filterParams)
}, 300) // 减少延迟到300ms

// 监听本地状态变化
watch(localCondition, (newValue, oldValue) => {
  // 如果是从props更新的（有_updateKey标记），则不触发emit
  if (newValue._updateKey !== oldValue?._updateKey) {
    // 这是一次props更新，不需要emit
    return;
  }
  // 否则是用户操作导致的变化，需要emit
  debouncedEmit(newValue);
}, { deep: true })

// 监听props.userFilter回显
watch(() => props.userFilter, (newValue) => {
  if (newValue) {
    // 增加updateKey，标记这是一次props更新
    updateKey.value++;
    // 构建新的condition对象
    const newCondition = {
      _updateKey: updateKey.value, // 添加唯一标识
      logicalOperation: newValue?.logicalOperation || 'and',
      eventIsAnd: newValue?.eventCondition?.logicalOperation || 'and',
      singleConditionExpressions: newValue?.eventCondition?.singleConditionExpressions.map(item => ({
        ...item,
        thresholds: item.calcuSymbol !== 'scope' ? [item.thresholds[0], item.thresholds[0]] : item.thresholds
      })) || [],
      sequenceConditionExpressions: newValue?.eventCondition?.sequenceConditionExpressions || [],
      userCondition: newValue?.userCondition || {logicalOperation: LogicalOperationType.AND, filters: []},
      groupCondition: newValue?.groupCondition || {logicalOperation: LogicalOperationType.AND, filters: []},
      clientCondition: newValue?.clientCondition || {logicalOperation: LogicalOperationType.AND, rules: {}},
    };
    // 直接替换本地状态，而不是修改gameUserCluster
    localCondition.value = newCondition;
  }
}, { immediate: true }); // 移除deep: true，避免不必要的深度监听

// 显示统一筛选时间
const showUnifiedCycle = ref(false)
const unifiedDateRange = ref<DateRange>({
  dateText: '最近7天',
  recentStartDate: 6,
  recentEndDate: 0
})
// 控制显示统一时间，不改变原数据
const changeShowCycle = (flag) => {
  showUnifiedCycle.value = flag
}
const handleUnifiedCycle = (date:any) => {
  unifiedDateRange.value = date
}
// 改变原数据dateRange
// const changeDateRange = (date:any) => {
//   gameUserCluster.value.condition.singleConditionExpressions.forEach(item => {
//     item.dateRange.dateText = date.dateText
//     item.dateRange.startDate = date.startDate
//     item.dateRange.endDate = date.endDate
//   })
//   gameUserCluster.value.condition.sequenceConditionExpressions.forEach(item => {
//     item.dateRange.dateText = date.dateText
//     item.dateRange.startDate = date.startDate
//     item.dateRange.endDate = date.endDate
//   })
// }
defineExpose({
  add, originFilters, addDone, addUser,addGroup,handleUnifiedCycle,changeShowCycle,addClientParams
})
</script>

<style scoped lang="less">
:deep(.arco-space-item){
  margin-top: 2px !important;
  margin-bottom: 2px !important;
  // margin: 4px 0!important;
}
.user-content,.client-content{
  :deep(.action-row){
    padding-left: 0!important;
  }
}
.sequence-content{
  :deep(.action-row){
    padding-left: 80px!important;
  }
}
.filter-btn, .filter-btn-disabled {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;
  margin: 2px 0;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  .un-filter-label {
    color: var(--tant-text-gray-color-text1-4);
  }

  .virtual-sign {
    color: var(--tant-primary-color-primary-default);
  }
}

.filter-btn {
  cursor: pointer;

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.filter-input {
  all: unset;
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 100px;
  height: 26px;
  padding: 0 8px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;

  &:focus {
    border-color: var(--tant-primary-color-primary-default);
  }

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.guide {
  width: 100%;
  box-sizing: border-box;
  color: var(--tant-text-gray-color-text1-2);

  .stickyBar {
    position: sticky;
    top: 0;
    z-index: 999;
    width: 100% !important;
    margin: 0 !important;
    padding: 12px;
    background-color: var(--tant-bg-white-color-bg1-1);

    .modal {
      width: 100%;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      padding: 8px 12px;
      // color: var(--tant-text-gray-color-text1-4);
      font-size: 16px;
      line-height: 18px;
      vertical-align: top;
      background-color: var(--tant-text-white-color-text2-1);
      border-radius: 4px;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .title {
        flex-grow: 1;
        font-weight: 600;
        max-width: calc(100% - 48px);
      }

      .model-btn {
        margin-left: auto;

        button {
          width: 24px;
          height: 24px;
          background-color: transparent;
        }
      }
    }
  }

  .event-filter-box {
    box-sizing: border-box;
    min-height: 32px;
    font-size: 14px;
    transition: all .3s;

    .action-row {
      position: relative;
      height: auto;
      width: 100%;
      min-height: 24px;
      line-height: 24px;
      padding-right: 24px;
      padding-left: 24px;

      .action-left {
        align-items: flex-start;
        height: auto;
        display: flex;

        .drag-index {
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          color: var(--tant-bg-white-color-bg1-1);
          font-size: 12px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          background-color: var(--tant-secondary-color-secondary-default);
          border-radius: 4px;
          transition: all .3s;
          opacity: 1;
        }

        .hover-drag {
          position: absolute;
          left: 0;
          margin-top: 5px;
          margin-right: 12px;
          flex-shrink: 0;
          // width: 24px;
          // height: 24px;
          font-size: 16px;
          font-style: normal;
          line-height: 24px;
          text-align: center;
          border-radius: 4px;
          opacity: 0;
          transition: all .3s;
        }

        .row-content {
          flex-grow: 1;
          box-sizing: border-box;
          padding: 4px 0;

          .rename {
            min-width: 80px;
            max-width: calc(100% - 110px);
            height: 24px;
            padding: 0;
            line-height: 24px;
            background: inherit;
            margin-bottom: 6px;
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;

            .placeholder {
              max-width: 260px;
              display: inline-block;
              height: 32px;
              line-height: 32px;
              padding: 0 10px;
              overflow: hidden;
              font-size: 14px;
              // white-space: pre;
              // vertical-align: middle;
              &:hover {
                color: var(--tant-primary-color-primary-default);
              }
            }
          }

          .event-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 4px 0;
            overflow: hidden;
            line-height: 32px;
            white-space: normal;


          }
        }
      }

      .action-right {
        position: absolute;
        top: 0;
        right: 4px;
        min-width: 40px;
        height: 36px;
        padding-top: 0 !important;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: opacity .3s;
      }

      .row-word {
        display: inline-block;
        margin: 0 4px;
        color: var(--tant-text-gray-color-text1-2);
        vertical-align: top;
      }

      &:hover {
        background-color: var(--tant-fill-color-fill1-2);
        .action-left .drag-index {
          opacity: 0;
          transition: all .3s;
        }

        .action-left .hover-drag {
          opacity: 1;
          transition: all .3s;
        }

        .action-left .row-content :deep(.filter-btn) {
          background: #fff;
        }
        .sub-action-left :deep(.filter-btn) {
          background: #fff;
        }
        :deep(.select-btn) {
          background: #fff;
        }
        :deep(.cluster-condition-indexName){
          background: #fff;
        }
        .action-right {
          opacity: 1;
        }
      }
    }

    .row-filters {
      // padding-left: 38px;
      .relation-editor {
        box-sizing: border-box;
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;

        .relation-relation {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          width: 24px;
          margin-right: 8px;
          flex-shrink: 0;

          .relation-relation-line,.relation-relation-disabled-line {
            position: absolute;
            left: 12px;
            width: 1px;
            background-color: var(--tant-border-color-border1-2);
            transition: all .3s;
            top: 6px;
            height: calc(100% - 12px);
          }
          
          .relation-relation-value,.relation-relation-disabled-value{
            position: absolute;
            text-transform: uppercase;
            top: 50%;
            left: 50%;
            transform: translate(-50%);
            height: 24px;
            padding: 0 5px;
            margin-top: -12px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 12px;
            line-height: 22px;
            text-align: center;
            background-color: #fff;
            border: 1px solid var(--tant-border-color-border1-2);
            border-radius: 4px;
            word-break: keep-all;
            transition: all .3s;
          }
          .relation-relation-value{
              cursor: pointer;
          }
          &:hover .relation-relation-line {
            background-color: var(--tant-primary-color-primary-default);
          }

          &:hover .relation-relation-value {
            color: var(--tant-primary-color-primary-default);
            border-color: var(--tant-primary-color-primary-default);
          }
        }

        .relation-main {
          flex: 1 1;

          .relation-row {
            box-sizing: border-box;
            
            .multi-filter-condition {
              .sub-action-row {
                padding: 4px 0;
                align-items: center;
                display: flex;
                height: auto;

                .sub-action-left {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  line-height: 27px;
                }

                .sub-action-right {
                  align-items: flex-start;
                  height: auto;
                  display: flex;
                  display: flex;
                  align-items: center;
                  opacity: 0;
                  transition: opacity .3s;
                  line-height: 27px;
                }

                &:hover .sub-action-right {
                  opacity: 1;
                }
              }

              &:hover {
                // background-color: #f6f6f9;
              }
            }
          }
        }
      }
    }
  }
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.filter-select {
  width: 200px;
  min-width: 46px;
  left: 0px;
  top: 5px;
  max-width: 240px;
  background: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  box-shadow: var(--tant-small-shadow-small-overall);
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, .85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: "tnum", "tnum";
  position: absolute;
  z-index: 1050;
  display: block;
  height: auto;
}

.filter-select-item {
  position: relative;
  width: 100%;
  height: 42px;
  padding: 4px 4px 0;
  cursor: default;

  div {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    border-radius: 4px;

    &:hover {
      background-color: var(--tant-secondary-color-secondary-fill-hover);
    }
  }

}

.cluster-condition-indexName {
  background-color: var(--tant-disabled-color-disabled-fill);
  border-radius: 4px;
  width: 24px;
  height: 24px;
  color: var(--tant-text-gray-color-text1-2);
  font: var(--tant-description-font-description-medium);
  line-height: 24px;
  text-align: center;
}

.itemIndexTitle {
  color: var(--tant-text-gray-color-text1-3);
  line-height: 27px;

}
</style>