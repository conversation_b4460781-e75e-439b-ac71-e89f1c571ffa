<template>
  <div class="chart-content">
    <a-spin :loading="!props?.eventData?.groups?.length" style="width: 100%;height: 100%">
      <a-table
          :columns="columns"
          :data="tableData"
          style="white-space: nowrap"
          size="small"
          :pagination="pagination"
          column-resizable
          :filter-icon-align-left="true"
          @page-size-change="pageSizeChange"/>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import * as XLSX from 'xlsx';
import {computed, ref, watch} from "vue";
import {range} from "lodash";
import {formatIndicatorValue} from "@/utils/number-util";
import {createCustomSorter, getCustomStringLength} from "@/utils/strUtil";

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
  // 页面数量
  pageSize: number;
}

const props = withDefaults(defineProps<Props>(), {
  eventData: {
    groupsDesc: [],
    groups: [],
    groupQueryResult: [],
    timeSpan: {},
    newUserCount: []
  }
})
const emits = defineEmits(['pageSizeChange']);
const columns = ref<any[]>([])
const tableData = ref<any>([])
console.log("props.pageSize",props.pageSize)
const pagination = ref<any>({
  pageSize: props.pageSize || 10,
  pageSizeOptions: [30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
  showPageSize: 'true',
  showTotal: 'true'
})

const pageSizeChange = (size) => {
  pagination.value.pageSize = size;
  emits('pageSizeChange', size)
}

// 监听外部卡片大小变化
watch(() => props.pageSize, (newData, oldValue) => {
  pagination.value.pageSize = newData
})
const renderChart = (newData) => {
  const {groupsDesc, groups, groupQueryResult, timeSpan, newUserCount} = newData;
  let timeSpanPrefix;
  switch (timeSpan.unit) {
    case 'DAY':
      timeSpanPrefix = 'D'
      break
    case 'WEEK':
      timeSpanPrefix = 'W'
      break
    case 'MONTH':
      timeSpanPrefix = 'M'
      break
  }
  const tableDataTemp = []
  groups.forEach((groupData, groupIndex) => {
    // 分组遍历
    const data = {};
    groupData.forEach((group, index) => {
      data[`groupName${index}`] = group
    })
    data.newUserCount = newUserCount[groupIndex]
    groupQueryResult.forEach((result, resultIndex) => {
      // 查询结果指标遍历
      const {resultData, displayName, displayType} = result;
      data.indicatorName = displayName
      let indicatorNotInGroup = true;
      resultData.forEach((dataItem, index) => {
        // 指标中分组遍历
        let groupMatch = true;
        const dataItemGroup = dataItem.group;
        const {values} = dataItem;
        groupData.forEach((group, groupDataIndex) => {
          // eslint-disable-next-line no-bitwise
          groupMatch &= group === dataItemGroup?.[groupDataIndex]
        })
        // 分组全匹配
        if (groupMatch && indicatorNotInGroup) {
          range(0, timeSpan.number + 1).forEach(i => {
            data[`${timeSpanPrefix}${i}`] = values[i] === undefined ? '--' : values[i]
          })
          indicatorNotInGroup = false
        }
      })
      // 指标无该分组
      if (indicatorNotInGroup) {
        range(0, timeSpan.number + 1).forEach(i => {
          data[`${timeSpanPrefix}${i}`] = '--'
        })
        indicatorNotInGroup = false
      }
      tableDataTemp.push({...data, indicatorName: data.indicatorName, displayType: displayType})
    })
  })
  tableData.value = tableDataTemp


// 分组列
  const groupColumns = groupsDesc?.map((group, index) => {
    return {
      title: group.name,
      dataIndex: `groupName${index}`,
      fixed: 'left',
      sortable: {
        sortDirections: ['ascend', 'descend'],
        sorter: (a, b, extra) => {
          const { direction } = extra;
          const format = group.format;
          const valueA = a[`groupName${index}`];
          const valueB = b[`groupName${index}`];
          return createCustomSorter(format)(valueA, valueB, direction);
        }
      },
      filterable: {
        filters: Array.from(
          new Set(tableDataTemp.map(item => item[`groupName${index}`]))
        ).map(name => ({
          text: name,
          value: name
        })),
        filter: (value, row) => value.includes(row[`groupName${index}`]),
        multiple: true
      },
    }
  })
// 数据列
  const resultColumns = range(0, timeSpan.number + 1).map(i => {
    return {
      title: `${timeSpanPrefix}${i}`,
      dataIndex: `${timeSpanPrefix}${i}`,
      render: (value) => {
        const {record} = value;
        return formatIndicatorValue(record[timeSpanPrefix + i], record.displayType);
      },
      sortable: {
        sortDirections: ['ascend', 'descend']
      }
    }
  })
  columns.value = [...groupColumns,
    {
      title: '指标',
      dataIndex: 'indicatorName',
      width: 220,
      fixed: 'left',
      filterable: {
        filters: groupQueryResult.map((item, index) => {
          return {
            text: item.displayName,
            value: item.displayName
          }
        }),
        filter: (value, row) => value.includes(row.indicatorName),
        multiple: true
      },
    },
    {
      title: '激活数',
      dataIndex: 'newUserCount',
      width: 120,
      sortable: {
        sortDirections: ['ascend', 'descend']
      },
      fixed: 'left',
    },
    ...resultColumns
  ]
}

watch(() => props.eventData, (newData) => {
  renderChart(newData);
})

renderChart(props.eventData);


// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `群组分析.xlsx`);
};

defineExpose({
  exportXlsx
})

</script>

<style scoped lang="less">
.chart-content {
  padding-top: 8px;
  width: 100%;
  height: 100%;
}

:deep(.arco-table-tr) {
  height: auto;
}
</style>