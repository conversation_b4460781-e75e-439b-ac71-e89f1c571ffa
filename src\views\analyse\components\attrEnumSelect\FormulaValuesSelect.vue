<template>
  <div>
    <!-- 全部 事件属性 用户属性 用户分群 用户标签下拉选 -->
    <div class="screen-body">
      <a-trigger v-model:popup-visible="subVisible" trigger="click" :unmount-on-close="false" position="bl" :update-at-scroll="true" @click="handleSubVisible">
        <a-button v-if="!props.disabled" class="filter-btn" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
            <span class="filter-label" style="white-space:wrap;text-align: left;">
              {{ filterData.calcuSymbolName }}
              <span v-if="showSelectData && !filterData.selectArr.length">{{ nameString }}</span>
              <span v-if="(filterData.filterType === 'tag' || filterData.filterType === 'abtest') && !filterData.selectArr.length">{{ nameString }}</span>
              <span v-if="showSelectArr && (filterData.objectType === 'string' || filterData.type === 4)">{{ nameString }}</span>
              <span v-if="showInputData">{{ filterData.inputData }}</span>
              <span v-if="showNumber">{{ filterData.number1 }}至{{ filterData.number2 }}之间</span>
              <span v-if="showDateArr">{{ filterData.dateArr[0] }}至{{ filterData.dateArr[1] }}之间</span>
              <span v-if="showDateValue">{{ filterData.dateValue }}</span>
              <span v-if="(filterData.objectType == 'date' || filterData.objectType == 'datetime') && (filterData.calcuSymbolName == '相对当前日期' || filterData.calcuSymbolName == '相对事件发生时刻')">
                <span v-if="filterData.dateType == 'scope'">
                  在{{ filterData.dateNumber1 }}到{{ filterData.dateNumber2 }}
                  <span v-if="filterData.dateTypeUnit == 'd'">天</span>
                  <span v-if="filterData.dateTypeUnit == 'h'">小时</span>
                  <span v-if="filterData.dateTypeUnit == 'm'">分钟</span>
                  之间
                </span>
                <span v-if="filterData.dateType == 'past'">
                  在过去{{ filterData.dateNumber1 }}天前
                </span>
                <span v-if="filterData.dateType == 'cd'">在当天</span>
                <span v-if="filterData.dateType == 'cw'">在当周</span>
                <span v-if="filterData.dateType == 'cm'">在当月</span>
              </span>
            </span>
        </a-button>
        <template #content>
          <div class="builder-content">
            <div class="builder-panel-container">
              <span class="quot-name">{{ filterData.objectDisplayName }}</span>
              <!-- 第一级筛选条件 -->
              <div v-if="filterData.objectType == 'string'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('textRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in textRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'int' || filterData.objectType == 'float'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('valueRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in valueRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'array'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('valueRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in itemRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType && ['date', 'datetime'].includes(filterData.objectType)" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('dateRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in dateRangeList" :key="index" style="width: 200px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="filterData.objectType == 'boolean'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('booleanRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in booleanRangeList" :key="index" style="width: 120px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="!filterData.objectType && filterData.filterType === 'cluster'" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('groupingList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in groupingList" :key="index" style="width: 200px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div v-if="!filterData.objectType && (filterData.filterType === 'tag' || filterData.filterType === 'abtest')" class="operator-select">
                <a-select v-model:model-value="filterData.calcuSymbol" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="rangeTypeChange('tagRangeList',$event)">
                  <template #arrow-icon>
                  </template>
                  <a-option v-for="(item,index) in tagRangeList" :key="index" style="width: 200px;" :value="item.value">{{ item.label }}</a-option>
                </a-select>
              </div>
              <div style="display: flex;align-items: center;">
                <div v-if="filterData.objectType == 'boolean' && filterData.calcuSymbolName == '等于'" class="operator-select">
                  <a-select v-model="filterData.selectData" style="width:60px" :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }" @change="booleanChange">
                    <template #arrow-icon>
                    </template>
                    <a-option :value="true">真</a-option>
                    <a-option :value="false">假</a-option>
                  </a-select>
                </div>
                <!-- 不同选择项，第二级筛选条件 -->
                <div
                  v-if="filterData.objectType &&
                  ['string', 'int','float', 'array'].includes(filterData.objectType) ||
                  filterData.filterType === 'tag' ||
                  filterData.filterType === 'abtest'"
                  class="equal-search">
                  <!-- 多选时 -->
                  <a-select
                      v-if="['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName) && (filterData.objectType === 'string' || filterData.type === 4)"
                      v-model="filterData.selectArr"
                      multiple
                      allow-search
                      allow-create
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      :show-extra-options="false"
                      @change="selectArrChange"
                      @popup-visible-change="selectArrPopup">
                    <template #arrow-icon>
                    </template>
                    <template #search-icon>
                    </template>
                    <template #loading-icon>
                    </template>
                    <a-option v-for="(item,index) in sortedEnumList" :key="index" :value="item.code">{{ item.name }}</a-option>
                  </a-select>
                  <!-- 单选时 -->
                  <a-select
                      v-if="
                        (filterData.calcuSymbolName == '包括' || filterData.calcuSymbolName == '不包括') &&
                        (
                          filterData.objectType === 'string' ||
                          filterData.type === 4 ||
                          filterData.filterType === 'tag' ||
                          filterData.filterType === 'abtest'
                        ) ||
                        (
                          specialField.includes(filterData.objectName) &&
                          ['小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)
                        )"
                      v-model="filterData.selectData"
                      style="width: 82px;"
                      allow-search
                      allow-create
                      :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                      :loading="selectLoading"
                      @change="selectDataChange"
                      @popup-visible-change="selectArrPopup">
                    <template #arrow-icon>
                    </template>
                    <template #search-icon>
                    </template>
                    <template #loading-icon>
                    </template>
                    <a-option v-for="(item,index) in selectEnumList" :key="index" :value="item.code">{{ item.name }}</a-option>
                  </a-select>
                  <a-input
                      v-if="['等于', '不等于', '存在元素', '不存在元素','包括','不包括'].includes(filterData.calcuSymbolName)
                      && filterData.objectType !== 'string' 
                      && filterData.type !== 4
                      && filterData.filterType !== 'tag'
                      && filterData.filterType !== 'abtest'"
                      v-model="filterData.selectData"
                      style="width: 80px;"
                      @input="selectDataChange"/>
                  <div
                      v-if="['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)
                    && filterData.objectType === 'string'"
                      class="option-edit"
                      @click="handleAddClick">
                    <icon-edit/>
                  </div>
                  <a-input
                      v-if="['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName) && !specialField.includes(filterData.objectName)" v-model="filterData.inputData" style="width: 80px;"
                      @change="inputDataChange"/>
                  <div v-if="filterData.calcuSymbolName == '区间'" class="next-input">
                    <div class="next-input-number">
                      <a-input-number v-model="filterData.number1" @blur="value1Change"/>
                    </div>
                    <span class="word">与</span>
                    <div class="next-input-number">
                      <a-input-number v-model="filterData.number2" @blur="value2Change"/>
                    </div>
                    <span class="word">之间</span>
                  </div>
                </div>
              </div>
              <div
                  v-if="(filterData.objectType && ['date', 'datetime'].includes(filterData.objectType)) 
                  && (filterData.calcuSymbolName == '小于等于' || filterData.calcuSymbolName == '大于等于' || filterData.calcuSymbolName == '位于区间')"
                  class="absolute-date">
                <a-date-picker
                    v-if="filterData.calcuSymbolName == '小于等于' || filterData.calcuSymbolName == '大于等于'"
                    v-model="filterData.dateValue"
                    style="width: 150px;"
                    show-time
                    :time-picker-props="{ defaultValue: '00:00:00' }"
                    format="YYYY-MM-DD HH:mm:ss"
                    @change="onChange"
                    @select="onSelect"
                    @ok="onOk"
                />
                <a-range-picker
                    v-if="filterData.calcuSymbolName == '位于区间'"
                    v-model="filterData.dateArr"
                    style="width: 360px;"
                    show-time
                    :time-picker-props="{ defaultValue: ['00:00:00', '00:00:00'] }"
                    format="YYYY-MM-DD HH:mm"
                    @change="onRangeChange"
                    @select="onSelect"
                    @ok="onOk"
                />
              </div>
              <div
                v-if="filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) &&
                (filterData.calcuSymbolName == '相对当前日期' || filterData.calcuSymbolName == '相对事件发生时刻')"
                class="relative-date">
                <span v-if="filterData.dateType == 'scope'" class="word">在</span>
                <a-select v-model:model-value="filterData.dateType" :trigger-props="{ updateAtScroll: true }" style="width:60px" @change="dateTypeChange">
                  <template #arrow-icon>
                  </template>
                  <a-option value="scope">区间</a-option>
                  <a-option v-if="filterData.calcuSymbolName == '相对当前日期'" value="past">过去</a-option>
                  <a-option value="cd">当天</a-option>
                  <a-option value="cw">当周</a-option>
                  <a-option value="cm">当月</a-option>
                </a-select>
                <div v-if="filterData.dateType == 'past'" class="next-input">
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber1" :precision="0" :min="0" @blur="numberPastChange"/>
                  </div>
                  <span class="word">天前</span>
                </div>
                <div v-if="filterData.dateType == 'scope'" class="next-input">
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber1" :precision="0" @blur="number1Change"/>
                  </div>
                  <span class="word">到</span>
                  <div class="next-input-number">
                    <a-input-number v-model="filterData.dateNumber2" :precision="0" @blur="number2Change"/>
                  </div>
                  <a-select v-model:model-value="filterData.dateTypeUnit" :trigger-props="{ updateAtScroll: true }" style="width:60px" @change="dateUnitChange">
                    <template #arrow-icon>
                    </template>
                    <a-option value="d">天</a-option>
                    <a-option value="h">小时</a-option>
                    <a-option value="m">分钟</a-option>
                  </a-select>
                </div>
              </div>
            </div>
            <div class="desc-foot">
              <div class="desc-tip">
                <icon-bulb class="bulb"/>
                <span>{{ filterData.calcuSymbolName }}：{{ getCalTips(filterData.calcuSymbolName) }}</span>
              </div>
            </div>
          </div>
        </template>
      </a-trigger>
      <div v-if="props.disabled" class="filter-btn-disabled" style="margin-left: 8px;min-width: 76px;max-width:300px;height: auto;">
          <span class="filter-label" style="white-space:wrap;text-align: left;">
            {{ filterData.calcuSymbolName }}
            <span v-if="showSelectData && !filterData.selectArr.length">{{ nameString }}</span>
            <span v-if="(filterData.filterType === 'tag' || filterData.filterType === 'abtest') && !filterData.selectArr.length">{{ nameString }}</span>
            <span v-if="showSelectArr && (filterData.objectType === 'string' || filterData.type === 4)">{{ nameString }}</span>
            <span v-if="showInputData">{{ filterData.inputData }}</span>
            <span v-if="showNumber">{{ filterData.number1 }}至{{ filterData.number2 }}之间</span>
            <span v-if="showDateArr">{{ filterData.dateArr[0] }}至{{ filterData.dateArr[1] }}之间</span>
            <span v-if="showDateValue">{{ filterData.dateValue }}</span>
            <span v-if="(filterData.objectType == 'date' || filterData.objectType == 'datetime') && (filterData.calcuSymbolName == '相对当前日期' || filterData.calcuSymbolName == '相对事件发生时刻')">
              <span v-if="filterData.dateType == 'scope'">
                在{{ filterData.dateNumber1 }}到{{ filterData.dateNumber2 }}
                <span v-if="filterData.dateTypeUnit == 'd'">天</span>
                <span v-if="filterData.dateTypeUnit == 'h'">小时</span>
                <span v-if="filterData.dateTypeUnit == 'm'">分钟</span>
                之间
              </span>
              <span v-if="filterData.dateType == 'past'">
                在过去{{ filterData.dateNumber1 }}天前
              </span>
              <span v-if="filterData.dateType == 'cd'">在当天</span>
              <span v-if="filterData.dateType == 'cw'">在当周</span>
              <span v-if="filterData.dateType == 'cm'">在当月</span>
            </span>
          </span>
      </div>
      <!-- 批量添加 -->
      <a-modal v-model:visible="addVisible" :closable="false" @ok="handleAddOk" @cancel="handleAddCancel">
        <div class="batch-option-edit">
          <div class="boe-title">
            <span>批量添加</span>
            <span class="boe-subtitle">使用回车换行分割</span>
          </div>
          <div class="boe-main">
            <a-textarea v-model="batchText" :auto-size="{ minRows: 8, maxRows: 15 }" placeholder="请输入内容，每行一个"/>
          </div>
        </div>
        <template #footer>
          <div class="boe-foot">
            <a-button type="primary" style="float: left;" @click="handleCopyAll">全选复制</a-button>
            <a-button style="margin-right: 8px;" @click.stop="handleAddCancel">取消</a-button>
            <a-button type="primary" @click.stop="handleAddOk">确定</a-button>
          </div>
        </template>
      </a-modal>
    </div>
    <div class="fixed-modal" :style="{display: subVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {computed, inject, reactive, ref, watch} from "vue";
import {getAttributeEnum} from "@/api/analyse/api";
import {cloneDeep} from "lodash";
import {useEventBus} from '@vueuse/core';
import {LocalStorageEventBus} from "@/types/event-bus";
import {Message} from '@arco-design/web-vue';
import {getDateRangeEndDate, getDateRangeStartDate} from "@/utils/dateUtil";
import {sortSelectedFirst} from "@/utils/sortUtil";

const localStorageEventBus = useEventBus(LocalStorageEventBus);
const props = defineProps({
  // 禁用选择
  disabled: {
    type: Boolean,
    default: false
  },
  // 回显数据
  info: {
    type: Object,
    default() {
      return {};
    },
  },
  // 事件数组，用以筛选eventName请求事件对应属性
  codeList: {
    type: Array,
    default: () => []
  },
})
const emits = defineEmits(['formulaValuesChange']);
const getCalTips = (name: string) => {
  const labels: { [key: string]: string } = {
    '等于': '属性值等于任一设定值',
    '不等于': '属性值不等于任一设定值，且不为空',
    '包括': '属性值包括与设定值完全一致的部分',
    '不包括': '属性值没有与设定值完全一致的部分',
    '有值': '属性值不为空',
    '无值': '属性值为空',
    '正则匹配': '属性值满足正则匹配规则',
    '正则不匹配': '属性值不满足正则匹配规则',
    '小于': '属性值小于设定值',
    '小于等于': '属性值小于或者等于设定值',
    '大于': '属性值大于设定值',
    '大于等于': '属性值大于或者等于设定值',
    '区间': '属性值在设定的数值区间范围内(左闭右闭)',
    '为真': '属性值为 True',
    '为假': '属性值为 False',
    '位于区间': '属性值在设置的时间区间范围内(左闭右闭)',
    '相对当前日期': '将属性值与“今天”进行比较',
    '相对事件发生时刻': '将属性值与事件时间进行比较',
    '属于分群': '用户在选择的分群内',
    '不属于分群': '用户不在选择的分群内',
  };
  return labels[name] || '';
}


interface FilterData {
  filterType: string;
  objectName: string;
  objectDisplayName: string;
  calcuSymbolName: string;
  calcuSymbol: string;
  thresholds?: (string | number | boolean)[];
  objectType: string | undefined;
  objectId: string;
  type: string | number; // 判断维度表等属性
  note: string;
  number1: number;
  number2: number;
  dateType: string;
  dateNumber1: number;
  dateNumber2: number;
  dateTypeUnit: string;
  dateValue: string;
  dateArr: any[];
  selectData: string;
  selectArr: any[];
  inputData: string;
  enumList: any[]
}

const filterData: FilterData = reactive({
  filterType: '',
  objectName: '',
  objectDisplayName: '',
  calcuSymbolName: '等于',
  calcuSymbol: 'eq',
  thresholds: [],
  objectType: '',
  objectId: '',
  type: '',
  note: '',
  number1: 0,
  number2: 1,
  dateType: 'scope',
  dateNumber1: -1,
  dateNumber2: 1,
  dateTypeUnit: 'd',
  dateValue: '',
  dateArr: [],
  selectData: '',
  selectArr: [],
  inputData: '',
  enumList: []
})
// 枚举特殊处理选项选项（此数组支持在string类型下，支持大小比较，下拉请求数据选项）
const specialField = ['app_version', 'os_version']
// 文本选项
const textRangeList = computed(() => {
  // 基础选项
  const baseOptions = [
    {value: 'eq', label: '等于'},
    {value: 'neq', label: '不等于'},
    {value: 'con', label: '包括'},
    {value: 'ncon', label: '不包括'}
  ];
  const versionOptions = specialField.includes(filterData.objectName) 
    ? [
        {value: 'lt', label: '小于'},
        {value: 'lteq', label: '小于等于'},
        {value: 'gt', label: '大于'},
        {value: 'gteq', label: '大于等于'}
      ]
    : [];
  const endOptions = [
    ...versionOptions,
    {value: 'ex', label: '有值'},
    {value: 'nex', label: '无值'},
    {value: 'rm', label: '正则匹配'},
    {value: 'nrm', label: '正则不匹配'},
  ];
  return [...baseOptions, ...endOptions];
});
// 数值选项
const valueRangeList = ref([
  {value: 'eq', label: '等于'},
  {value: 'neq', label: '不等于'},
  {value: 'lt', label: '小于'},
  {value: 'lteq', label: '小于等于'},
  {value: 'gt', label: '大于'},
  {value: 'gteq', label: '大于等于'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'},
  {value: 'scope', label: '区间'}
])

// 列表选项
const itemRangeList = ref([
  {value: 'con', label: '存在元素'},
  {value: 'ncon', label: '不存在元素'},
  // {value:'scope',label:'元素位置'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 时间选项
const dateRangeList = ref([
  {value: 'scope', label: '位于区间'},
  {value: 'lteq', label: '小于等于'},
  {value: 'gteq', label: '大于等于'},
  {value: 'rct', label: '相对当前日期'},
  {value: 'ret', label: '相对事件发生时刻'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 布尔选项
const booleanRangeList = ref([
  // {value:'true',label:'为真'},
  // {value:'false',label:'为假'},
  {value: 'eq', label: '等于'},
  {value: 'ex', label: '有值'},
  {value: 'nex', label: '无值'}
])
// 分群
const groupingList = ref([
  {value: 'con', label: '属于分群'},
  {value: 'ncon', label: '不属于分群'},
])
// 标签
const tagRangeList = ref([
  {value: 'con', label: '包括'},
  {value: 'ncon', label: '不包括'},
])

const getRangeType = (data?: string) => {
  const type = filterData.objectType
  const rangeLists = {
    'string': textRangeList.value,
    'int': valueRangeList.value,
    'float': valueRangeList.value,
    'array': itemRangeList.value,
    'date': dateRangeList.value,
    'datetime': dateRangeList.value,
    'boolean': booleanRangeList.value,
    'variant': itemRangeList.value,
  };
  const matchedItem = filterData.filterType === 'tag' || filterData.filterType === 'abtest' ? tagRangeList.value?.find(item => item.value === data) || tagRangeList.value[0] : rangeLists[type]?.find(item => item.value === data) || rangeLists[type]?.[0];
  const groupItem = groupingList.value.find(item => item.value === data) || groupingList.value[0];

  return matchedItem || groupItem;
};
// 获取事件属性list;
// 如果传入name-list 则请求接口获取，否则从props.eventLists种获取
const separateNumberAndUnit = (str: string) => {
  const number = parseInt(str);
  const unit = str.replace(/^-?\d+/, '');
  return {number, unit};
};
const nameString = ref('')
const selectLoading = ref(false)
const selectEnumList = ref<any>([])
const sortedEnumList = ref<any>([])

const handleNameString = () => {
  selectEnumList.value = props.info?.enumList?.length > 0 ? cloneDeep(props.info.enumList) : selectEnumList.value
  // 将 thresholds 中的值映射为显示值
  const displayValues = filterData.thresholds?.map(threshold => {
    // 布尔类型特殊处理
    if (filterData.objectType === 'boolean') {
      return threshold === true ? '真' : threshold === false ? '假' : threshold
    }
    // 在枚举列表中查找匹配的项
    const enumItem = selectEnumList.value.find(item => item.code === threshold)
    // 如果找到匹配项则返回name，否则返回原始值
    return enumItem ? enumItem.name : threshold
  })
  nameString.value = displayValues?.join(',') || ''
}
const subVisible = ref<boolean>(false)

const handleSubVisible = () => {
  subVisible.value = !subVisible.value;
}


const rangeTypeChange = (name, v) => {
  const list = {
    'textRangeList': textRangeList.value,
    'valueRangeList': valueRangeList.value,
    'itemRangeList': itemRangeList.value,
    'dateRangeList': dateRangeList.value,
    'booleanRangeList': booleanRangeList.value,
    'groupingList': groupingList.value,
    'tagRangeList': tagRangeList.value,
  };
  const arr = list[name]
  filterData.calcuSymbol = v

  arr.forEach(item => {
    if (item.value === v) {
      filterData.calcuSymbolName = item.label
    }
  })
  filterData.dateType = 'scope'
  filterData.inputData = ''
  filterData.selectData = ''
  filterData.selectArr = []
  filterData.dateValue = ''
  filterData.dateArr = []
  if (filterData.calcuSymbolName === '区间') {
    filterData.thresholds = [filterData.number1, filterData.number2]
  } else if (filterData.calcuSymbolName === '相对当前日期' || filterData.calcuSymbolName === '相对事件发生时刻') {
    filterData.thresholds = ['-1d', '1d']
  } else {
    filterData.thresholds = []
  }
  emits('formulaValuesChange', filterData)
}

// 多选筛选下拉列表
const selectArrChange = (v) => {
  filterData.thresholds = []
  if (v) {
    filterData.thresholds = v
  }
  handleNameString()
  emits('formulaValuesChange', filterData)
}

// 日期
const boardDate = inject('boardDate', ref({}))
const selectArrPopup = async (v) => {
  if (filterData?.enumList?.length > 0) {
    selectEnumList.value = cloneDeep(filterData.enumList)
  }
  // 使用props.nameList中的code请求枚举
  const codeList = [...new Set(props.codeList.map(item => item?.eventCode || item?.indicatorCode))]
  selectLoading.value = true
  const data = {
    type: filterData.type,
    attributeType: filterData.filterType,
    event: codeList,
    attribute: filterData.objectId,
  }
  // 只有当 boardDate 存在且有相应的值时才添加日期字段
  if (boardDate && boardDate?.value) {
    data.startDate = getDateRangeStartDate(boardDate.value)
    data.endDate = getDateRangeEndDate(boardDate.value)
  }
  await getAttributeEnum(data).then(res => {
    if (res) {
      selectEnumList.value = res
      filterData.enumList = cloneDeep(selectEnumList.value)
    }
    selectLoading.value = false
  }).catch(error => {
    selectLoading.value = false;
  });
  sortedEnumList.value = sortSelectedFirst(selectEnumList.value, filterData.selectArr)
}
// 单选筛选下拉列表
const selectDataChange = (v) => {
  filterData.thresholds = []
  if (v) {
    filterData.thresholds.push(v)
  }
  handleNameString()
  emits('formulaValuesChange', filterData)
}
const inputDataChange = () => {
  filterData.thresholds = []
  if (filterData.inputData) {
    filterData.thresholds = [filterData.inputData]
  }
  emits('formulaValuesChange', filterData)
}
const value1Change = () => {
  const num1 = filterData.number1
  const num2 = filterData.number2
  if (num1 >= 0 && num1 > num2) {
    filterData.number2 = num1
  }
  filterData.thresholds = [filterData.number1, filterData.number2]
  emits('formulaValuesChange', filterData)
}

const value2Change = () => {
  const num1 = filterData.number1
  const num2 = filterData.number2
  if (num2 < num1) {
    filterData.number1 = num2
  }
  filterData.thresholds = [filterData.number1, filterData.number2]
  emits('formulaValuesChange', filterData)
}
const number1Change = () => {
  const num1 = filterData.dateNumber1
  const num2 = filterData.dateNumber2
  if (num1 >= 0 && num1 > num2) {
    filterData.dateNumber2 = num1
  }
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('formulaValuesChange', filterData)
}
const number2Change = () => {
  const num1 = filterData.dateNumber1
  const num2 = filterData.dateNumber2
  if (num2 < num1) {
    filterData.dateNumber1 = num2
  }
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('formulaValuesChange', filterData)
}
const booleanChange = (v) => {
  filterData.thresholds = [v]
  emits('formulaValuesChange', filterData)
}
const numberPastChange = () => {
  const value = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  filterData.thresholds = [value]
  emits('formulaValuesChange', filterData)
}
const dateTypeChange = (v) => {
  if (v === 'past') {
    filterData.dateNumber1 = 0
    filterData.dateTypeUnit = 'd'
    const value = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
    filterData.thresholds = [value]
    emits('formulaValuesChange', filterData)
  }
  if (v === 'cd' || v === 'cw' || v === 'cm') {
    filterData.thresholds = [v]
    emits('formulaValuesChange', filterData)
  }
}
const dateUnitChange = (v) => {
  const value1 = `${filterData.dateNumber1}${filterData.dateTypeUnit}`
  const value2 = `${filterData.dateNumber2}${filterData.dateTypeUnit}`
  filterData.thresholds = [value1, value2]
  emits('formulaValuesChange', filterData)
}
const onChange = (v) => {
  filterData.thresholds = [v]
  emits('formulaValuesChange', filterData)
}
const onRangeChange = (v) => {
  filterData.thresholds = v
  emits('formulaValuesChange', filterData)
}
const onSelect = () => {

}
const onOk = () => {

}
// 批量添加
const addVisible = ref(false)
const batchText = ref('')
const copyText = ref('')
// 打开批量添加弹窗
const handleAddClick = () => {
  batchText.value = ''
  addVisible.value = true
  // 如果已有选中值，将其转换为文本
  if (filterData.selectArr?.length) {
    batchText.value = filterData.selectArr.join('\n')
    copyText.value = filterData.selectArr.join('\n')
  }
}

// 确认批量添加
const handleAddOk = () => {
  if (!batchText.value) {
    addVisible.value = false
    return
  }
  // 将文本按换行符分割并过滤空值
  const values = [...new Set(batchText.value.split('\n').filter(item => item.trim()))]
  // 更新选中值
  filterData.selectArr = values
  filterData.thresholds = values
  handleNameString()
  addVisible.value = false
  emits('formulaValuesChange', filterData)
}

// 取消批量添加
const handleAddCancel = () => {
  batchText.value = ''
  addVisible.value = false
}

// 全选复制
const handleCopyAll = () => {
  if (!batchText.value) return
  navigator.clipboard.writeText(batchText.value)
  .then(() => {
    Message.success('复制成功')
  })
  .catch(() => {
    Message.error('复制失败')
  })
}

// 判断数据类型是否在指定范围内
const isValidDataType = (type: any, validTypes: string[]) => {
  return type && validTypes.includes(type);
};

// 判断计算符号是否在指定范围内
const isValidSymbol = (symbol: string, validSymbols: string[]) => {
  return validSymbols.includes(symbol);
};

const showSelectArr = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array'];
  const validSymbols = ['等于', '不等于', '存在元素', '不存在元素'];
  return isValidDataType(filterData.objectType, validTypes) &&
      isValidSymbol(filterData.calcuSymbolName, validSymbols) &&
      filterData.selectArr.length > 0;
});

const showSelectData = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array', 'boolean'];
  const validSymbols = ['等于', '不等于', '存在元素', '不存在元素', '包括', '不包括'];
   // 特殊字段的数值比较支持
  const isSpecialFieldComparison = specialField.includes(filterData.objectName) &&
    ['小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName);
  const hasValidTypeAndSymbol = isValidDataType(filterData.objectType, validTypes) &&
      isValidSymbol(filterData.calcuSymbolName, validSymbols);
  // 布尔类型特殊处理：只要有值就显示（包括 false）
  if (filterData.objectType === 'boolean') {
    return hasValidTypeAndSymbol && filterData.selectData !== undefined && filterData.selectData !== '';
  }
  // 特殊字段或其他类型正常判断
  return (hasValidTypeAndSymbol || isSpecialFieldComparison) && filterData.selectData;
});

const showInputData = computed(() => {
  const validSymbols = ['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'];
  // 特殊字段的数值比较应该显示单选框，不显示输入框
  const isSpecialFieldComparison = specialField.includes(filterData.objectName) &&
    ['小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName);
  return isValidSymbol(filterData.calcuSymbolName, validSymbols) && !isSpecialFieldComparison;
});

const showNumber = computed(() => {
  const validTypes = ['string', 'int', 'float', 'array'];
  return isValidDataType(filterData.objectType, validTypes) &&
      filterData.calcuSymbolName === '区间';
});

const showDateArr = computed(() => {
  const validTypes = ['date', 'datetime'];
  return isValidDataType(filterData.objectType, validTypes) &&
      filterData.calcuSymbolName === '位于区间' &&
      filterData.dateArr[0] &&
      filterData.dateArr[1];
});

const showDateValue = computed(() => {
  const validTypes = ['date', 'datetime'];
  const validSymbols = ['小于等于', '大于等于'];

  return isValidDataType(filterData.objectType, validTypes) &&
      isValidSymbol(filterData.calcuSymbolName, validSymbols) &&
      filterData.dateValue;
});
const formatFilterData = (params: any) => {
  filterData.type = params?.type
  filterData.objectName = params?.objectName
  filterData.objectDisplayName = params?.objectDisplayName
  filterData.objectId = params?.objectId
  filterData.filterType = params?.filterType
  filterData.objectType = params?.objectType
  filterData.calcuSymbolName = params.calcuSymbol ? getRangeType(params.calcuSymbol).label : getRangeType().label
  filterData.calcuSymbol = params.calcuSymbol ? params.calcuSymbol : getRangeType().value
  filterData.thresholds = params?.thresholds || []
  filterData.enumList = params?.enumList || []
  filterData.selectArr = params?.selectArr || []
  filterData.selectData = params?.selectData || ''
  sortedEnumList.value = params?.enumList || []
  nameString.value = ''
  if (["cluster", "tag", "abtest"].includes(params.filterType)) {
    filterData.objectType = undefined
    filterData.objectName = ''
  }
  if (filterData.thresholds && filterData.thresholds.length > 0) {
    // filterData.selectArr = params?.thresholds || []
    const arr = cloneDeep(params?.thresholds || [])
    if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && ['等于', '不等于', '包括', '不包括'].includes(filterData.calcuSymbolName)) {
      filterData.selectData = arr[0] || ''
      handleNameString()
    }
    // 特殊字段 app_version 和 os_version 的数值比较处理
    if (specialField.includes(filterData.objectName) && 
        ['小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)) {
      filterData.selectData = arr[0] || ''
      handleNameString()
    }
    if(!filterData.objectType && ['包括', '不包括'].includes(filterData.calcuSymbolName)){
      filterData.selectData = arr[0] || ''
      handleNameString()
    }
    // 布尔值数据处理
    if (filterData.objectType === 'boolean' && ['等于'].includes(filterData.calcuSymbolName)) {
      filterData.selectData = arr[0] !== undefined ? arr[0] : ''
      handleNameString()
    }
    if (filterData.objectType && ['string'].includes(filterData.objectType) && ['等于', '不等于', '存在元素', '不存在元素'].includes(filterData.calcuSymbolName)) {
      filterData.selectArr = arr
      handleNameString()
    }
    if (['正则匹配', '正则不匹配', '小于', '小于等于', '大于', '大于等于'].includes(filterData.calcuSymbolName)) {
      filterData.inputData = arr[0] || ''
    }
    if (filterData.objectType && ['string', 'int', 'float', 'array'].includes(filterData.objectType) && filterData.calcuSymbolName === '区间') {
      filterData.number1 = arr[0] || 0
      filterData.number2 = arr[1] || 1
    }
    // 日期数据处理
    if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && filterData.calcuSymbolName === '位于区间') {
      filterData.dateArr = arr
    }
    if ((filterData.objectType === 'date' || filterData.objectType === 'datetime') && (filterData.calcuSymbolName === '相对当前日期' || filterData.calcuSymbolName === '相对事件发生时刻')) {
      if (arr.length === 2) {
        const result = arr.map(separateNumberAndUnit);
        filterData.dateNumber1 = result[0].number || -1
        filterData.dateNumber2 = result[1].number || 1
        filterData.dateTypeUnit = result[0].unit
        filterData.dateType = 'scope'
      }
      if (arr.length === 1) {
        const result = arr.map(separateNumberAndUnit);
        if (result[0].number !== undefined && 
            result[0].number !== null && 
            !isNaN(result[0].number)) {
          filterData.dateNumber1 = result[0].number
          filterData.dateType = 'past'
        } else {
          filterData.dateType = result[0]?.unit
        }
      }
    }
    if (filterData.objectType && ['date', 'datetime'].includes(filterData.objectType) && (filterData.calcuSymbolName === '小于等于' || filterData.calcuSymbolName === '大于等于')) {
      filterData.dateValue = arr[0] || ''
    }
  }
}

watch(() => props.info, (newVal) => {
  if (newVal) {
    formatFilterData(props.info)
  }
  // emits('tabsChange',filterData)
}, {immediate: true, deep: true});

localStorageEventBus.on((name, value) => {
  if (name === "data-source") {
    filterData.enumList = []
  }
})
defineExpose({
  formatFilterData
});
</script>

<style scoped lang="less">
:deep(.arco-select-view-tag) {
  white-space: nowrap !important;;
}

.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.filter-btn, .filter-btn-disabled {
  display: inline-flex;
  align-items: center;
  min-width: 40px;
  max-width: 200px;
  height: 26px;
  padding: 0 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  background-color: var(--tant-secondary-color-secondary-fill);
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all .3s;
  box-sizing: border-box;
  margin: 2px 0;

  .btn-icon {
    color: var(--tant-text-gray-color-text1-2);
    font-size: 14px;
    margin-right: 5px;
  }

  .filter-label {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    color: var(--tant-text-gray-color-text1-2);
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }

  .un-filter-label {
    color: var(--tant-text-gray-color-text1-4);
  }

  .virtual-sign {
    color: var(--tant-primary-color-primary-default);
  }
}

.filter-btn {
  cursor: pointer;

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

:deep(.arco-dropdown-option) {
  border-radius: 4px;
}

.builder-content {
  display: flex;
  flex-direction: column;
  width: 400px;
  min-width: 400px;
  max-height: 420px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: var(--tant-small-shadow-small-overall);

  .builder-panel-container {
    flex-grow: 1;
    padding: 24px 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    line-height: 40px;

    .quot-name {
      margin-right: 6px;
      font-weight: 500;
      line-height: 20px;
      vertical-align: text-top;
    }

    .operator-select {
      display: inline-block;
      margin-right: 10px;
    }

    .item-index-number {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 100px;
      margin-right: 8px;
    }

    .absolute-date {
      display: inline-block;

      :deep(.arco-picker) {
        border-radius: 4px;
      }
    }

    .relative-date {
      display: inline-flex;
      align-items: center;
      vertical-align: top;
    }

    .word {
      margin: 0 6px;
      color: var(--tant-text-gray-color-text1-2);
      font-weight: 400;
      font-size: 14px;
    }

    .next-input {
      display: inline-flex;
      align-items: center;
      vertical-align: top;
      margin-left: 8px;
    }

    .next-input-number {
      width: 60px;
      margin-right: 8px;
    }
  }

  .equal-search {
    display: inline-block;
    max-width: 360px;
    min-width: 80px;
    position: relative;

    .option-edit {
      position: absolute;
      top: 0px;
      right: 4px;
      z-index: 9;
      // padding: 4px;
      color: var(--tant-text-gray-color-text1-2);
      font-size: 14px;
      opacity: 0;
      cursor: pointer;

      &:hover {
        color: var(--tant-primary-color-primary-hover);
      }
    }

    &:hover .option-edit {
      opacity: 1;
    }
  }

  .desc-foot {
    display: flex;
    align-items: center;
    padding: 12px 14px;
    font-size: 13px;
    line-height: 20px;
    background: var(--tant-bg-gray-color-bg2-1);
    border-top: 1px solid var(--tant-border-color-border1-1);

    .desc-tip {
      display: flex;
      align-items: center;
      color: var(--tant-text-gray-color-text1-3);

      .bulb {
        margin-right: 10px;
        color: var(--tant-decorative-yellow-color-decorative6-1);
        font-size: 18px;
        vertical-align: top;
      }
    }
  }
}

:deep(.arco-modal-footer) {
  border: none !important;
}

.batch-option-edit {
  min-height: 200px;
  overflow: hidden;

  .boe-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--tant-text-gray-color-text1-1);
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;

    .boe-subtitle {
      color: var(--tant-text-gray-color-text1-4);
      font-weight: 400;
      font-size: 12px;
    }
  }

  .boe-main {
    width: 100%;
    margin-top: 8px;

    :deep(.arco-textarea-wrapper) {
      height: 164px;
      padding: 5px 8px;
      font-size: 14px;
      line-height: 22px;
      background-color: transparent;
      border-radius: var(--tant-border-radius-medium);
      resize: none;
      color: var(--tant-text-gray-color-text1-2);
      border: 1px solid var(--tant-border-color-border1-1);
    }

    :deep(.arco-textarea) {
      border: none;
      padding: none;
    }
  }

}

.boe-foot {
  width: 100%;
  text-align: right;

  :deep(.arco-btn) {
    height: 24px;
    padding: 1px 8px;
    font: var(--tant-body-font-body-regular);
    border-radius: var(--tant-border-radius-medium);
    border: none;
  }
}
</style>
