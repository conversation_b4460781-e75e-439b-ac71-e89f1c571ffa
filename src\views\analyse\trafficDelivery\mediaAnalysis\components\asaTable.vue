<template>
  <div class="table-container">
    <a-spin :loading="loading" style="width: 100%; height: 100%">
      <a-table :columns="columns" :data="tableData" :pagination="pagination" size="small" column-resizable :filter-icon-align-left="true" :scroll="{ y: '100%' }" @page-size-change="pageSizeChange">
        <template v-for="column in filterColumns" :key="column.dataIndex" #[`${column.dataIndex}-filter`]="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset }">
          <div class="custom-filter">
            <a-space direction="vertical" :size="8">
              <a-select
                :model-value="filterValue"
                placeholder="请选择"
                allow-clear
                allow-search
                multiple
                :options="getFilterOptions(column.dataIndex)"
                :style="{ width: '200px' }"
                :trigger-props="{
                  position: 'top',
                  autoFitPopupMinWidth: true,
                  updateAtScroll: true,
                }"
                @update:model-value="(val) => setFilterValue(val)"
              >
              </a-select>
              <div class="custom-filter-footer">
                <a-button size="mini" @click="handleFilterReset">重置</a-button>
                <a-button size="mini" type="primary" @click="handleFilterConfirm">确定</a-button>
              </div>
            </a-space>
          </div>
        </template>
        <template v-for="(column, index) in columns.filter((col) => col.titleSlotName)" :key="`header-${index}`" #[`${column.dataIndex}-title`]>
          <a-popover trigger="hover" position="top" :popup-max-width="400" :content-style="{ padding: '12px' }">
            <span>{{ column.title }}</span>
            <template #content>
              <div v-if="indicatorsFormula[column.dataIndex]" class="indicator-content">
                <div class="indicator-title">{{ column.title }}</div>
                <div style="display: flex; align-items: center; margin-top: 8px; flex-wrap: wrap; gap: 4px">
                  <div v-for="(formula, formulaIndex) in indicatorsFormula[column.dataIndex]" :key="formulaIndex" style="margin-right: 4px">
                    <div class="select-btn-disabled">
                      <span class="btn-label"> {{ formula.name }}{{ formula.displayName ? ` -${formula.displayName}` : '' }} </span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </a-popover>
        </template>
        <!-- 格式化数字显示 -->
        <template #spend="{ record }">
          {{ formatCurrency(record.spend) }}
        </template>
        <template #tapInstalls="{ record }">
          {{ formatNumber(record.tapInstalls) }}
        </template>
        <template #avgCpt="{ record }">
          {{ formatCurrency(record.avgCpt) }}
        </template>
        <template #ttr="{ record }">
          {{ formatPercentage(record.ttr) }}
        </template>
        <template #impressions="{ record }">
          {{ formatNumber(record.impressions) }}
        </template>
        <template #clicks="{ record }">
          {{ formatNumber(record.clicks) }}
        </template>
        <template #avgCpm="{ record }">
          {{ formatCurrency(record.avgCpm) }}
        </template>
        <template #tapInstallRate="{ record }">
          {{ formatPercentage(record.tapInstallRate) }}
        </template>
        <template #tapInstallCpi="{ record }">
          {{ formatCurrency(record.tapInstallCpi) }}
        </template>
      </a-table>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from 'vue';
import {getCustomStringLength} from '@/utils/strUtil';
import * as XLSX from 'xlsx';
import {getDateRangeEndDate, getDateRangeStartDate} from '@/utils/dateUtil';

const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    // 指标列表
    indicatorList: {
      type: Array,
      default: () => [],
    },
    // 自定义列数据
    customColumnData: {
      type: Array,
      default: () => [],
    },
    // 分组数据
    groups: {
      type: Array,
      default: () => [],
    },
    // 指标数据详情
    indicatorsFormula: {
      type: Object,
      default: () => ({}),
    },
  });

  const columns = ref<any>([]);
  // 筛选列配置 - 根据 groups 动态生成
  const filterColumns = computed(() => {
    if (!props.groups) return [];
    const groupColumns = props.groups.map((item) => ({
        dataIndex: item.code,
      }));
    return groupColumns;
  });

  // 统一的筛选选项获取方法
  const getFilterOptions = (dataIndex: string) => {
    if (!props.tableData?.length) return [];
    return Array.from(new Set(props.tableData.map((row: any) => row[dataIndex])))
      .filter((value) => value !== undefined && value !== null)
      .map((value) => ({
        label: value,
        value,
      }));
  };

  // 分页配置
  const pagination = reactive({
    showPageSize: true,
    showTotal: true,
    pageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    hideOnSinglePage: false,
    autoAdjust: true,
  });
  const pageSizeChange = (size: number) => {
    pagination.pageSize = size;
  };
  // 数字格式化函数
  const formatNumber = (num: number): string => {
    if (num === null || num === undefined) return '0';
    return num.toLocaleString();
  };

  const formatCurrency = (num: number): string => {
    if (num === null || num === undefined) return '0.00';
    return num.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatPercentage = (num: number): string => {
    if (num === null || num === undefined) return '0.00%';
    return `${num.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}%`;
  };
  const handleColumns = () => {
    const baseColums = [
      {
        title: '日期',
        dataIndex: 'rpDate',
        width: 140,
        fixed: 'left',
        sortable: { sortDirections: ['ascend', 'descend'], defaultSortOrder: 'descend' },
      }
    ];
    const groupsColumns = props.groups
      ? props.groups.map((item) => {
            return {
              title: item.name,
              dataIndex: item.code,
              slotName: item.code,
              fixed: 'left',
              width: getCustomStringLength(item.name) + 80,
              sortable: { sortDirections: ['ascend', 'descend'] },
              filterable: {
                filter: (value: string[], row: any) => {
                  if (!value || value.length === 0) return true;
                  return value.includes(row[item.code]);
                },
                slotName: `${item.code}-filter`,
                multiple: true,
              },
              ellipsis: true,
              tooltip: true,
            };
          })
      : [];
    const indicators = props.customColumnData.length ? props.customColumnData : props.indicatorList;
    const indicatorColumns = indicators?.map((item: any) => {
      return {
        title: item?.name,
        dataIndex: item?.code,
        slotName: item?.code,
        titleSlotName: `${item?.code}-title`,
        width: getCustomStringLength(item?.name || '') + 70,
        sortable: { sortDirections: ['ascend', 'descend'] },
      };
    });
    columns.value = [...baseColums, ...groupsColumns, ...indicatorColumns];
    
  };
  watch(() => [props.customColumnData, props.groups, props.tableData], handleColumns);

  // 导出
  const exportXlsx = (dateRange) => {
    // 获取表头
    const headers = columns.value.map((item: any) => item.title);
    const columnsList = [headers];
    props.tableData.forEach((item: any) => {
      const row = headers.map((header: string) => {
        const column = columns.value.find((col: any) => col.title === header);
        if (!column) return '';

        const { dataIndex, slotName } = column;
        const value = item[dataIndex];

        // 根据slotName应用与表格显示一致的格式化
        if (slotName) {
          switch (slotName) {
            case 'spend':
            case 'avgCpt':
            case 'avgCpm':
            case 'tapInstallCpi':
              return formatCurrency(value);
            case 'tapInstalls':
            case 'impressions':
            case 'clicks':
              return formatNumber(value);
            case 'ttr':
            case 'tapInstallRate':
              return formatPercentage(value);
            default:
              return value || '';
          }
        }

        return value || '';
      });
      columnsList.push(row);
    });

    const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
    const newWorkbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
    XLSX.writeFile(newWorkbook, `媒体分析_${getDateRangeStartDate(dateRange)}-${getDateRangeEndDate(dateRange)}.xlsx`);
  };
  defineExpose({
    exportXlsx,
  });
</script>

<style scoped lang="less">
  .table-container {
    width: 100%;
    height: calc(100vh - 256px);
  }
  .custom-filter {
    padding: 12px;
    background: var(--color-bg-5);
    border: 1px solid var(--color-neutral-3);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

    .custom-filter-footer {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }
  }
  .indicator-content {
    max-width: 350px;

    .indicator-title {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 8px;
      color: var(--color-text-1);
    }

    .indicator-description {
      font-size: 12px;
      color: var(--color-text-3);
      margin-bottom: 8px;
      line-height: 1.4;
    }
  }
  .select-btn-disabled {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    max-width: 100%;
    height: 26px;
    padding: 0 8px;
    font-size: 14px;
    vertical-align: top;
    background-color: var(--tant-secondary-color-secondary-fill);
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.3s;
    box-sizing: border-box;

    .btn-label {
      display: inline-block;
      max-width: 300px;
      overflow: hidden;
      color: var(--tant-text-gray-color-text1-2);
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
    }
  }
</style>
