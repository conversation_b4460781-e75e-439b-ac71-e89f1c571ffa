<template>
  <div class="page-content">
      <div class="setting">
          <div class="label">筛选</div>
          <div class="setting-content">
              <a-select
                  v-model="filterParams.adIds"
                  placeholder="请选择广告"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdSearch"
                  @dropdown-reach-bottom="loadMoreAds"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adOptions" 
                    :key="item.adId" 
                    :value="item.adId" 
                    :label="item.adName">
                    {{ item.adName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.adsetIds"
                  placeholder="请选择广告组"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="adsetOptionsLoading"
                  :filter-option="false"
                  allow-search
                  @search="handleAdsetSearch"
                  @dropdown-reach-bottom="loadMoreAdsets"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告组-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in adsetOptions" 
                    :key="item.adsetId" 
                    :value="item.adsetId" 
                    :label="item.adsetName">
                    {{ item.adsetName }}
                  </a-option>
              </a-select>
              <a-select
                  v-model="filterParams.accountIds"
                  placeholder="请选择广告账户"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  :loading="accountOptionsLoading"
                  :filter-option="false"
                  allow-search
                  :tag-nowrap="true"
                  @search="handleAccountSearch"
                  @dropdown-reach-bottom="loadMoreAccounts"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>广告账户-{{ data?.label }}</span>
                  </template>
                  <a-option 
                    v-for="item in accountOptions" 
                    :key="item.accountId" 
                    :value="item.accountId" 
                    :label="item.accountName">
                    {{ item.accountName }}
                  </a-option>
              </a-select>
              <a-select
                  placeholder="请选择状态"
                  allow-clear
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>状态-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">投放中</a-option>
                  <a-option value="2">已暂停</a-option>
                  <a-option value="3">已删除</a-option>
                  <a-option value="4">超预算</a-option>
                  <a-option value="5">审核中</a-option>
                  <a-option value="6">审核不通过</a-option>
                  <a-option value="7">其他状态</a-option>
              </a-select>
              <!-- <a-select
                  placeholder="请选择渠道"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>渠道-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">Meta</a-option>
                  <a-option value="2">Mintegral</a-option>
              </a-select> -->
              <!-- <a-select
                  placeholder="请选择标签"
                  allow-clear
                  multiple
                  :max-tag-count="1"
                  style="width: 240px;margin-right: 24px;">
                  <template #label="{ data }">
                    <span>标签-{{ data?.label }}</span>
                  </template>
              </a-select> -->
          </div>
      </div>
      <a-divider />
      <div class="table-wrap">
          <div class="wrap-left">
            <a-dropdown>
                <a-button class="br4" style="margin-left: 12px;">
                    <template #icon>
                        <icon-tags />
                    </template>
                    <template #default>批量操作<icon-down style="margin-left: 12px;"/></template>
                </a-button>
                <template #content>
                    <a-doption style="width: 136px;" @click="excelEdit">Excel编辑</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchOpen">开启</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchPause">暂停</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchCopy">复制广告</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchReplaceMaterials">替换素材</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchTime">定时开关</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchDelete">删除</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditName">修改名称</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchEditTag">编辑标签</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchChangeUrl">修改网址参数</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="batchChangeHomepage">修改广告主页</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="changeMainText">修改正文</a-doption>
                    <a-doption :disabled="!selectedKeys.length" @click="changeTitle">修改标题</a-doption>
                </template>
            </a-dropdown>
          </div>
          <div class="wrap-right">
              <a-select
                  placeholder="请选择细分数据"
                  allow-clear
                  style="width: 240px;">
                  <template #label="{ data }">
                    <span>细分数据-{{ data?.label }}</span>
                  </template>
                  <a-option value="1">日期</a-option>
                  <a-option value="2">地区</a-option>
                  <a-option value="3">版位</a-option>
              </a-select>
              <a-tooltip placement="top" content="当前维度每行数据支持展开15行细分数据，更多细分数据请使用维度名右侧的数据下钻按钮查看。由于汇总和细分数据媒体接口不是同时更新，二者数据可能会有差异。">
                  <icon-question-circle style="color: var(--color-text-2);margin-left: 2px;"/>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;" @click="getList">
                  <template #icon>
                      <icon-refresh />
                  </template>
                  <template #default>刷新</template>
              </a-button>
              <a-tooltip placement="top" content="可手动同步近3天(含今天)的数据，其他时段系统将定期自动同步">
                <a-button class="br4" style="margin-left: 12px;" @click="batchSync">
                    <template #icon>
                        <icon-sync />
                    </template>
                    <template #default>同步数据</template>
                </a-button>
              </a-tooltip>
              <a-button class="br4" style="margin-left: 12px;">
                  <template #icon>
                      <icon-download />
                  </template>
                  <template #default>导出</template>
              </a-button>
          </div>
      </div>
      <div class="table-area">
        <a-table
          v-model:selectedKeys="selectedKeys"
          :columns="columns"
          :loading="loading"
          :data="tableData"
          :bordered="false"
          :hoverable="true"
          sticky-header
          :table-layout-fixed="true"
          :filter-icon-align-left="true"
          :column-resizable="true"
          :scroll="scroll"
          :pagination="false"
          :summary="true"
          :row-selection="rowSelection"
          row-key="adId"
        >
          <template #effectiveStatus="{ record }">
            <a-switch v-model="record.effectiveStatus" checked-value="ACTIVE" unchecked-value="PAUSED" size="small" :before-change="() => statusChange(record)"/>
          </template>
          <template #accountStatus="{ record }">
              <a-badge :status="record.accountStatus === 1 ? 'success' : 'normal'" :text="record.accountStatus === 1 ? 'ACTIVE' : 'DISABLED'"/>
          </template>
           <template #status="{ record }">
              <a-badge :status="record.status === 'ACTIVE' ? 'success' : 'normal'" :text="record.status"/>
          </template>
          <template #actions="{record}">
            <div class="actions-content">
              <a class="actions-btn">详情</a>
              <a class="actions-btn">复制</a>
              <a class="actions-btn">小时报表</a>
              <a-dropdown>
                <a class="actions-btn">更多<icon-down style="font-size: 12px;"/></a>
                <template #content>
                  <a-doption>广告日志</a-doption>
                  <a-doption>同步数据</a-doption>
                </template>
              </a-dropdown>
            </div>
          </template>
          <template #adName="{ record,rowIndex }">
            <div class="cell-content">
              <div class="title-box">
                <a-popover position="right">
                  <div v-if="record?.materialType === 'image'" style="width: 50px;height: 50px;margin-right: 10px;">
                    <a-spin v-if="record?.material?.permalinkUrl" :loading="!imageLoaded[record?.material?.permalinkUrl]" style="width: 100%;height: 100%;">
                      <img 
                        class="ad-img" 
                        :src="record?.material?.permalinkUrl" 
                        alt=""
                        @load="handleImageLoaded(record?.material?.permalinkUrl)"
                        @error="handleImageError(record?.material?.permalinkUrl)"
                      >
                    </a-spin>
                    <img 
                      v-else 
                      class="ad-img" 
                      src="/image/promotion/notImage.png" 
                      alt=""
                    >
                  </div>
                  <template #content>
                    <div style="width: 240px;height: 240px;">
                      <a-spin v-if="record?.material?.permalinkUrl" :loading="!imageLoaded[record?.material?.permalinkUrl + '_large']" style="width: 100%;height: 100%;">
                        <img 
                          style="width: 100%;height: 100%;object-fit: contain;" 
                          :src="record?.material?.permalinkUrl" 
                          alt=""
                          @load="handleImageLoaded(record?.material?.permalinkUrl + '_large')"
                          @error="handleImageError(record?.material?.permalinkUrl + '_large')"
                        >
                      </a-spin>
                      <img 
                        v-else 
                        style="width: 100%;height: 100%;object-fit: contain;" 
                        src="/image/promotion/notImage.png" 
                        alt=""
                      >
                    </div>
                  </template>
                </a-popover>
                <a-popover position="right">
                  <div v-if="record?.materialType === 'video'" style="position: relative;width: 50px;height: 50px;margin-right: 10px;">
                    <a-spin v-if="record?.material?.picture" :loading="!imageLoaded[record?.material?.picture]" style="width: 100%;height: 100%;">
                      <img 
                        class="ad-img" 
                        :src="record?.material?.picture" 
                        alt=""
                        @load="handleImageLoaded(record?.material?.picture)"
                        @error="handleImageError(record?.material?.picture)"
                      >
                    </a-spin>
                    <img 
                      v-else 
                      class="ad-img" 
                      src="/image/promotion/notVideo.png" 
                      alt=""
                    >
                    <div class="video-icon"></div>
                  </div>
                  <template #content>
                    <div style="width: 240px;height: 240px;">
                      <video v-if="record?.material?.source" width="100%" height="100%" controls loop autoplay muted>
                          <source :src="record?.material?.source" type="video/mp4">
                      </video>
                      <img v-else style="width: 100%;height: 100%;object-fit: contain;" src="/image/promotion/notVideo.png" alt="">
                    </div>
                  </template>
                </a-popover>
                <div class="text">{{ record.adName }}</div>
                <a-trigger position="right" auto-fit-position :unmount-on-close="false" :popup-visible="record.isEditing">
                  <icon-edit class="icon" @click="showEdit(rowIndex)"/>
                  <template #content>
                    <div class="edit-title-box">
                      <a-input v-model="record.adName" placeholder="请输入广告名称" maxlength="100" show-word-limit/>
                      <div class="edit-footer">
                        <a-button style="margin-right: 12px;" size="mini" @click="editCancel(rowIndex)">取消</a-button>
                        <a-button type="primary" size="mini" @click="editOk(rowIndex)">确定</a-button>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
              <div>
                <a-trigger trigger="hover" position="rt">
                  <icon-down-circle style="cursor: pointer;"/>
                  <template #content>
                    <div class="trigger-content">
                      <div class="tooltip-icon-list-header">
                        数据下钻
                      </div>
                      <div class="dropdown-list-content">
                        <div v-for="item in dropdownList" :key="item.value" class="dropdown-list-item-wrapper">
                          <div class="dropdown-list-item" @click="openDrill(record,item.value)">{{ item.label }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>
            </div>
          </template>
          <template #editLog="{ record }">
            <a-tooltip v-for="(item, index) in record.editLog" :key="index">
              <template #content>
                <div class="edit-log-box">
                  <div class="edit-log-item">
                    <div class="label">时间</div>
                    <div class="info">{{ item.dateTime }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">时区</div>
                    <div class="info">{{ item.timezone }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号ID</div>
                    <div class="info">{{ item.channelUserName }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">FB个人号</div>
                    <div class="info">{{ item.operationEmail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">操作类型</div>
                    <div class="info">{{ item.operationDetail }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑前</div>
                    <div class="info">{{ item.operationBefore }}</div>
                  </div>
                  <div class="edit-log-item">
                    <div class="label">编辑后</div>
                    <div class="info">{{ item.operationAfter }}</div>
                  </div>
                </div>
              </template>
              <img 
                :src="formatEditImg(item)" 
                style="width: 20px;height: 20px;margin-right: 8px;"
              />
            </a-tooltip>
          </template>
          <template #productName="{ record }"> 
            <div v-if="record?.application?.appOsName === 'ios'">
              <IconFont type="icon-ios" :size="14"/>
              <span style="padding-left: 4px;">{{record?.application?.applicationName}}</span>
            </div>
            <div v-if="record?.application?.appOsName ==='android'">
              <IconFont type="icon-android" :size="14"/>
              <span style="padding-left: 4px;">{{record?.application?.applicationName}}</span>
            </div>
            <div v-else>未指定</div>
          </template>
          <template #createdTime="{ record }">
            {{ record.createdTime ? dayjs(record.createdTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
          </template>
          <template #adReviewFeedback="{ record }">
            {{ record.adReviewFeedback || '-' }}
          </template>
          <template #body="{ record }">
            <div class="body-box">
              <a-tooltip position="top" :content="record.body"> 
                <div class="content">
                  {{ record.body || '-' }}
                </div>
              </a-tooltip>
              <icon-edit class="icon" @click="changeMainText"/>
            </div>
          </template>
          <template #title="{ record }">
            <div class="body-box">
              <a-tooltip position="top" :content="record.title"> 
                <div class="content">
                  {{ record.title || '-' }}
                </div>
              </a-tooltip>
              <icon-edit class="icon" @click="changeTitle"/>
            </div>
          </template>
          <template #postId="{ record }">
            <a class="actions-btn">{{ record.postId }}</a>
          </template>
          <template #summary-cell="{ column,record }">
            <div class="text">
              <template v-if="column.dataIndex === 'effectiveStatus'">汇总</template>
              <template v-else-if="noSumColumns.includes(column.dataIndex)">-</template>
              <template v-else>{{ record[column.dataIndex] ? record[column.dataIndex].toFixed(2) : '' }}</template>
            </div>
          </template>
        </a-table>
        <div class="pagination">
            <a-pagination :total="total" show-total @change="pageChange"/>
        </div>
      </div>
      <!-- 操作确认弹窗 -->
      <ConfirmModal v-model:visible="confirmVisible" :confirm-type="confirmType"/>
      <!-- 批量修改名称 -->
       <BatchEditName ref="nameRef"/>
      <!-- 数据下钻 -->
      <DrillDrawer ref="drillRef"/>
      <!-- 批量同步数据 -->
      <BatchSyncData ref="syncRef"/>
      <!-- 批量复制广告 -->
      <CopyAdModal ref="copyRef"/>
      <!-- 定时开关 -->
      <TimeSwitch ref="timeRef"/>
      <!-- 批量编辑标签 -->
      <BatchEditTags ref="tagRef"/>
      <!-- 更改主页 -->
      <BatchChangeHomepage ref="homepageRef"/>
      <!-- 修改网址参数 -->
      <BatchChangeUrl ref="urlRef"/>
      <!-- 修改正文 -->
      <ChangeMainText ref="mainTextRef"/>
      <!-- 修改标题 -->
      <ChangeTitle ref="titleRef"/>
      <!-- 批量替换素材 -->
      <BatchReplaceMaterials ref="materialsRef"/>
      <!-- Excel编辑 -->
      <ExcelEdit ref="excelRef"/>
      <!-- 删除确认 -->
      <a-modal
        v-model:visible="deleteVisible"
        width="416px"
        :hide-title="true"
        :footer="false"
        @cancel="deleteCancel"
      >
      <div class="modal-content">
        <div class="modal-header">
          <icon-question-circle-fill style="color: rgb(var(--warning-6));font-size: 28px;margin-right: 8px;"/>
          删除
        </div>
        <div class="modal-body">
          当前选中1个ad，确定删除这些ad？此操作不可逆，请谨慎操作。
        </div>
      </div>
      <div class="modal-footer">
        <a-button style="margin-right: 24px;" @click="deleteCancel">取消</a-button>
        <a-button type="primary" @click="deleteOk">确定</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,inject, watch } from "vue";
import dayjs from 'dayjs';
import {Message} from '@arco-design/web-vue';
import {getAdChannelList} from "@/api/launch/api";
import ConfirmModal from "@/views/launch/promotion/components/ConfirmModal.vue"
import BatchEditName from "@/views/launch/promotion/components/BatchEditName.vue";
import BatchSyncData from "@/views/launch/promotion/components/BatchSyncData.vue";
import TimeSwitch from "@/views/launch/promotion/components/TimeSwitch.vue";
import {metaDrillList} from "@/views/launch/promotion/components/promotionData"
import BatchEditTags from "@/views/launch/promotion/components/BatchEditTags.vue";
import ExcelEdit from "@/views/launch/promotion/components/ExcelEdit.vue";
import DrillDrawer from "../components/DrillDrawer.vue";
import CopyAdModal from "../components/CopyAdModal.vue";
import BatchChangeHomepage from "../components/BatchChangeHomepage.vue";
import BatchChangeUrl from "../components/BatchChangeUrl.vue";
import ChangeMainText from "../components/ChangeMainText.vue";
import ChangeTitle from "../components/ChangeTitle.vue";
import BatchReplaceMaterials from "../components/BatchReplaceMaterials.vue";

// 设置数据
const filterParams = reactive({
  accountIds:[],
  adsetIds:[],
  campaignIds:[],
  adIds:[]
})
const dateTime = ref(inject('dateTime') as any[])
const selectedKeys = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});
const total = ref(0)
const pageParams = reactive({
  current: 1,
  pageSize: 10,
})
// 模拟table数据
const loading = ref(false)
const dropdownList = metaDrillList.ad
const tableData = ref<any>([])
const noSumColumns = ref([
  'adName',
  'adId',
  'actions',
  'status',
  'editLog',
  'productName',
  'adsetId',
  'adsetName',
  'campaignId',
  'campaignName',
  'accountId',
  'accountName',
  'accountStatus',
  'createdTime',
  'formatCountries',
  'adReviewFeedback',
  'body',
  'title',
  'pageId',
  'postId',
])
const columns = ref<any>([
  { title: '', dataIndex: 'effectiveStatus',slotName:'effectiveStatus',width:100,fixed:'left' },
  { title: '广告名称', dataIndex: 'adName',slotName:'adName',width:250,fixed:'left', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '操作', dataIndex: 'actions',slotName:'actions',width:260,fixed:'left',align:'center' },
  { title: '广告ID', dataIndex: 'adId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '产品', dataIndex: 'productName',minWidth:180,slotName:'productName', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告组ID', dataIndex: 'adsetId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告组名称', dataIndex: 'adsetName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告系列ID', dataIndex: 'campaignId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告系列名称', dataIndex: 'campaignName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告账户ID', dataIndex: 'accountId',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '广告账户名称', dataIndex: 'accountName',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '账户状态', dataIndex: 'accountStatus',minWidth:180,slotName:'accountStatus' },
  { title: '创建时间', dataIndex: 'createdTime',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '状态', dataIndex: 'status',minWidth:180,slotName:'status' },
  { title: '地区（设置）', dataIndex: 'formatCountries',minWidth:180,slotName:'formatCountries',tooltip:true,ellipsis:true },
  { title: '审核建议', dataIndex: 'adReviewFeedback',minWidth:180,slotName:'adReviewFeedback', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '正文', dataIndex: 'body',minWidth:180,slotName:'body' },
  { title: '标题', dataIndex: 'title',minWidth:180,slotName:'title' },
  { title: '主页ID', dataIndex: 'pageId',minWidth:180,slotName:'pageId', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '帖子ID', dataIndex: 'postId',minWidth:180,slotName:'postId', sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '最近操作', dataIndex: 'editLog',slotName:'editLog',minWidth:180 },
  { title: '花费', dataIndex: 'cost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '展示数', dataIndex: 'impressions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '千次展示成本', dataIndex: 'cpm',minWidth:200, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击数', dataIndex: 'clicks',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击成本', dataIndex: 'cpc',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '点击率', dataIndex: 'ctr',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化数', dataIndex: 'conversions',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化成本', dataIndex: 'conversionCost',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
  { title: '转化率', dataIndex: 'conversionRate',minWidth:180, sortable: {sortDirections: ['ascend', 'descend']} },
])
const adOptions = ref<any>([])
const adOptionsLoading = ref(false);
const adPagination = reactive({
  current: 1,
  pageSize: 20,
});
const adsetOptions = ref<any>([])
const adsetOptionsLoading = ref(false);
const adsetPagination = reactive({
  current: 1,
  pageSize: 20,
});
const accountOptions = ref<any>([])
const accountOptionsLoading = ref(false);
const accountPagination = reactive({
  current: 1,
  pageSize: 20,
});
// Table页面滑动
const scroll = {
  y: 'calc(100% - 56px)'
};
const syncRef = ref()
const batchSync = () => {
  syncRef.value.openModal()
}
const formatEditImg = (val) => {
  const editImgMap = {
    "EDIT_BUDGET":"/icon/launch/operation-budget.svg",
    "EDIT_BID":"/icon/launch/operation-bid.svg",
    "EDIT_ON":"/icon/launch/operation-on.svg",
  }
  return editImgMap[val.operationType]
}
const confirmVisible = ref(false)
const confirmType = ref('')
const statusChange = (record) => {
  confirmType.value = record.authStatus ? 'pause' : 'open'
  confirmVisible.value = true
  return new Promise((resolve, reject) => {
    if(confirmVisible.value){
      resolve(false);
    }
    resolve(true);
  });
}
const drillRef = ref()
const openDrill = (record,val) => {
  drillRef.value.openModal({tab:'ad',value:val,recordData:record})
}
// 显示编辑弹出层
const showEdit = (index) => {
  tableData.value[index].isEditing = true;
};

// 取消编辑
const editCancel = (index) => {
  tableData.value[index].isEditing = false;
};

// 确认编辑
const editOk = (index) => {
  tableData.value[index].isEditing = false;
};
// 批量修改名称
const nameRef = ref()
const batchEditName = () => {
  nameRef.value.openModal()
}
const deleteVisible = ref(false)
const deleteCancel = () => {
  deleteVisible.value = false
}
const deleteOk = () => {
  
}
const batchDelete = () => {
  deleteVisible.value = true
}

// 开启
const batchOpen = () => {
  confirmVisible.value = true
  confirmType.value = 'open'
}
// 暂停
const batchPause = () => {
  confirmVisible.value = true
  confirmType.value = 'pause'
}
// 定时开关
const timeRef = ref()
const batchTime = () => {
  timeRef.value.openModal()
}
// 批量编辑标签
const tagRef = ref()
const batchEditTag = () => {
  tagRef.value.openModal('ad')
}
// 批量复制广告
const copyRef = ref()
const batchCopy = () => {
  copyRef.value.openModal()
}
// 更改主页
const homepageRef = ref()
const batchChangeHomepage = () => {
  homepageRef.value.openModal()
}
// 修改网址参数
const urlRef = ref()
const batchChangeUrl = () => {
  urlRef.value.openModal()
}
// 修改正文
const mainTextRef = ref()
const changeMainText = () => {
  mainTextRef.value.openModal()
}
// 修改标题
const titleRef = ref()
const changeTitle = () => {
  titleRef.value.openModal()
}
// 批量替换素材
const materialsRef = ref()
const batchReplaceMaterials = () => {
  materialsRef.value.openModal()
}
// Excel编辑
const excelRef = ref()
const excelEdit = () => {
  excelRef.value.openModal('ad')
}
// 加载广告选项
const loadAdOptions = async () => {
  adOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adPagination.pageSize,
      current:adPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adPagination.current === 1) {
      adOptions.value = items;
    } else {
      adOptions.value = [...adOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告选项失败:', error);
  } finally {
    adOptionsLoading.value = false;
  }
}
const handleAdSearch = (v) => {
}
const loadMoreAds = async () => {
  adPagination.current += 1;
  // 获取更多广告数据
  await loadAdOptions();
}
// 加载广告组选项
const loadAdsetOptions = async () => {
  adsetOptionsLoading.value = true;
  
  try {
    const params = {
      channel:'facebook',
      groupLabel:'adset',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:adsetPagination.pageSize,
      current:adsetPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    // 更新选项和分页信息
    if (adsetPagination.current === 1) {
      adsetOptions.value = items;
    } else {
      adsetOptions.value = [...adsetOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告组选项失败:', error);
  } finally {
    adsetOptionsLoading.value = false;
  }
}
const handleAdsetSearch = (v) => {
}
const loadMoreAdsets = async () => {
  adsetPagination.current += 1;
  // 获取更多广告组数据
  await loadAdsetOptions();
}
// 加载广告账户选项
const loadAccountOptions = async () => {
  accountOptionsLoading.value = true;
  try {
    const params = {
      channel:'facebook',
      groupLabel:'account',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:accountPagination.pageSize,
      current:accountPagination.current,
    };
    const response = await getAdChannelList(params);
    const { items } = response;
    if (accountPagination.current === 1) {
      accountOptions.value = items;
    } else {
      accountOptions.value = [...accountOptions.value, ...items];
    }
  } catch (error) {
    console.error('加载广告系列选项失败:', error);
  } finally {
    accountOptionsLoading.value = false;
  }
};   
const handleAccountSearch = (v) => {
}
const loadMoreAccounts = async () => {
  accountPagination.current += 1;
  // 获取更多广告账户数据
  await loadAccountOptions();
}
const getList = async () => {
  loading.value = true
  try{
    const params = {
      channel:'facebook',
      groupLabel:'ad',
      startDate:dateTime.value[0],
      endDate:dateTime.value[1],
      pageSize:pageParams.pageSize,
      current:pageParams.current,
    }
    await getAdChannelList(params).then(res => {
      tableData.value = res.items
      adOptions.value = res.items
      total.value = res.total
    })
  }catch (error) {
    console.error('失败:', error);
  }finally{
    loading.value = false
  }
}
const init = (filter?:any) => {
  if(filter?.type === 'fromAdset'){
    filterParams.adsetIds = filter?.ids || []
  }
  if(filter?.type === 'fromCampaign'){
    filterParams.campaignIds = filter?.ids || []
  }
  getList()
  loadAccountOptions()
  loadAdsetOptions()
}
// 分页
const pageChange = (v) => {
  pageParams.current = v
  getList()
}
defineExpose({
  init
})

// 图片加载状态管理
const imageLoaded = ref({});

// 图片加载完成处理函数
const handleImageLoaded = (url) => {
  if (url) {
    imageLoaded.value[url] = true;
  }
};

// 图片加载失败处理函数
const handleImageError = (url) => {
  if (url) {
    imageLoaded.value[url] = true; // 即使加载失败也标记为已完成，避免一直显示加载状态
  }
};

// 当表格数据更新时，重置图片加载状态
watch(() => tableData.value, () => {
  // 为新的图片URL创建加载状态
  const newImageLoaded = {};
  tableData.value.forEach(record => {
    // 处理图片类型的素材
    if (record?.material?.permalinkUrl) {
      newImageLoaded[record.material.permalinkUrl] = imageLoaded.value[record.material.permalinkUrl] || false;
      // 为大图预览添加加载状态
      newImageLoaded[record.material.permalinkUrl + '_large'] = imageLoaded.value[record.material.permalinkUrl + '_large'] || false;
    }
    // 处理视频类型的素材缩略图
    if (record?.material?.picture) {
      newImageLoaded[record.material.picture] = imageLoaded.value[record.material.picture] || false;
    }
  });
  imageLoaded.value = newImageLoaded;
}, { immediate: true });
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
:deep(.arco-select-view-tag){
  span:first-child {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
  }
}
</style>
