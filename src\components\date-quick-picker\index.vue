<template>
    <a-range-picker
      v-model:model-value="modelValue"
      :shortcuts-position="shortcutsPosition"
      :placeholder="placeholder"
      :shortcuts="rangeShortcuts"
      :disabled-date="disabledDate"
      :allow-clear="false"
      :style="{ width: width, marginRight: marginRight }"
      @change="handleChange"
    />
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import dayjs from 'dayjs';
  
  interface Props {
    placeholder?: string[];
    disabledDate?: (current: Date) => boolean;
    shortcutsPosition?: 'left' | 'bottom' | 'right';
    width?: string;
    marginRight?: string;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    placeholder: () => ['开始日期', '结束日期'],
    disabledDate: (current) => dayjs(current).isAfter(dayjs()),
    shortcutsPosition: 'left',
    width: '240px',
    marginRight: '24px'
  });
  const emits = defineEmits(['change']);
  const modelValue = defineModel<any[]>('modelValue', { default: [] });
  
  // 日期范围快捷选项
  const today = dayjs();
  const rangeShortcuts = [
    {
      label: '今天',
      value: () => [today, today],
    },
    {
      label: '昨天',
      value: () => [
        today.subtract(1, 'day'),
        today.subtract(1, 'day'),
      ],
    },
    {
      label: '最近3天',
      value: () => [today.subtract(2, 'day'), today],
    },
    {
      label: '最近7天',
      value: () => [today.subtract(6, 'day'), today],
    },
    {
      label: '最近14天',
      value: () => [today.subtract(13, 'day'), today],
    },
    {
      label: '最近30天',
      value: () => [today.subtract(29, 'day'), today],
    },
    {
      label: '本周',
      value: () => {
        const start = today.startOf('week');
        const end = today.endOf('week');
        return [start, end.isAfter(today) ? today : end];
      },
    },
    {
      label: '上周',
      value: () => [
        today.subtract(1, 'week').startOf('week'),
        today.subtract(1, 'week').endOf('week'),
      ],
    },
    {
      label: '本月',
      value: () => {
        const start = today.startOf('month');
        const end = today.endOf('month');
        return [start, end.isAfter(today) ? today : end];
      },
    },
    {
      label: '上月',
      value: () => [
        today.subtract(1, 'month').startOf('month'),
        today.subtract(1, 'month').endOf('month'),
      ],
    },
  ];
  
  // 处理日期变更
  const handleChange = (value) => {
    emits('change', value);
  };
  </script>
  
  <style scoped lang="less">
  /* 可以根据需要添加自定义样式 */
  </style>