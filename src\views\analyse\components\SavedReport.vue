<script setup lang="ts">
import {computed, CSSProperties, onMounted, ref} from "vue";
import {delAnalyseReportList, getAnalyseReportList} from "@/api/analyse/api";
import {Message} from "@arco-design/web-vue";
import {useEventBus, useSessionStorage} from "@vueuse/core";
import router from "@/router";
import {ROUTE_NAME} from "@/router/constants";

const visible = defineModel('visible', {default: false})
const items = ref([])
const isIconVisible = ref<Array<boolean>>(Array(0)); //
const eventBus = useEventBus('eventList');
const appData = useSessionStorage('app-data', {});
const appFilter = ref<boolean>(true);
const styles: CSSProperties = {
  boxShadow: 'var(--tant-medium-shadow-medium-left)',
  paddingTop: '60px',
  overflowY: "clip",
}
// select数据
const selectOptions = [
  {label: '全部报表', value: 'all'},
  {label: '事件分析', value: 'event'},
  {label: '留存分析', value: 'retention'},
  {label: '漏斗分析', value: 'funnel'},
  {label: '属性分析', value: 'property'},
  {label: '路径分析', value: 'trace'},
  {label: '分布分析', value: 'scatter'},
  {label: 'SQL分析', value: 'custom'},
  {label: '间隔分析', value: 'interval'},
  {label: '运营分析', value: 'application'},
  {label: '群组分析', value: 'group'},
]
const selectOption = ref(selectOptions[0].value)
const isItemVisible = (item) => {
  return selectOption.value === item.model ||
      selectOption.value === '全部报表' ||
      selectOption.value === 'all';
};
// 关闭抽屉
const closeVisible = () => {
  visible.value = false
}
const deleteModalShow = ref(false)
// 删除报表
const deleteReport = ref()
const deleteStatement = async (item: any) => {
  deleteModalShow.value = true
  deleteReport.value = item
}
const deleteConfirm = async () => {
  await delAnalyseReportList(deleteReport.value.code).then(async res => {
    if (res) {
      Message.success('删除成功')
      await getAnalyseReportList().then(res => {
        items.value = res
      })
    }
  })
  deleteModalShow.value = false
}
const deleteCancel = () => {
  deleteModalShow.value = false

}
// 响应式搜索查询
const searchQuery = ref('');

function isMatch(item, query, selectedOption) {
  const lowerCaseQuery = query.toLowerCase();

  // 将查询字符串转换为正则表达式模式，以便匹配不相连的字符
  const regexPattern = lowerCaseQuery.split('').join('.*?');
  const regex = new RegExp(regexPattern);

  // 判断名称或描述是否匹配查询
  const nameMatches = regex.test(item.name.toLowerCase());
  const descriptionMatches = regex.test(item.description.toLowerCase());

  // 判断选项是否匹配
  const selectMatches = selectedOption === 'all' ||
      selectedOption === '全部报表' ||
      item.model === selectedOption;

  // 返回综合匹配结果
  return (nameMatches || descriptionMatches) && selectMatches;
}

const filteredItems = computed(() => {
  const query = searchQuery.value.trim().toLowerCase();
  const selectedOption = selectOption.value;
  return items.value?.filter(item => !appFilter.value || item.appId === appData.value?.code)?.filter(item => isMatch(item, query, selectedOption));
});
isIconVisible.value = Array(items.value.length).fill(false);
const showIcon = (index: number, value: boolean) => {
  isIconVisible.value[index] = value;

};
const gotoItem = (value: string) => {
  router.push({
    name: value,
  });
};
interface ReportOption {
  model: string;
  path: string;
}

// 将 reportOptions 提取为常量
const REPORT_OPTIONS: ReportOption[] = [
  {model: 'event', path: ROUTE_NAME.ANALYSE_EVENT},
  {model: 'retention', path: ROUTE_NAME.ANALYSE_RETENTION},
  {model: 'funnel', path: ROUTE_NAME.ANALYSE_FUNNEL},
  {model: 'property', path: ROUTE_NAME.ANALYSE_PROPERTY},
  {model: 'trace', path: ROUTE_NAME.ANALYSE_TRACE},
  {model: 'scatter', path: ROUTE_NAME.ANALYSE_SCATTER},
  {model: 'custom', path: ROUTE_NAME.ANALYSE_CUSTOM_SQL},
  {model: 'interval', path: ROUTE_NAME.ANALYSE_INTERVAL},
  {model: 'attribution', path: ROUTE_NAME.ANALYSE_ATTRIBUTION},
  {model: 'application', path: ROUTE_NAME.ANALYSE_APPLICATION},
  {model: 'group', path: ROUTE_NAME.ANALYSE_GROUP},
] as const;

interface ReportItem {
  model: string;
  code: string;
  [key: string]: any;
}
// 报表点击
const selectLable = (v: ReportItem) => {
  try {
    const matchedOption = REPORT_OPTIONS.find(option => option.model === v.model);
    const routePath = matchedOption?.path || '';
    router.push({
      name: routePath,
      query: { code: v.code },
    });
  } catch (error) {
    console.error('路由跳转失败:', error);
    Message.error('页面跳转失败');
  }
}
eventBus.on(event => {
  if (event === 'saveReport') {
    getAnalyseReportList().then(res => {
      items.value = res
      localStorage.setItem('analyseReportList', JSON.stringify(res))
    })
  }
})
onMounted(() => {
  getAnalyseReportList().then(res => {
    items.value = res
    localStorage.setItem('analyseReportList', JSON.stringify(res))
  })
})


</script>

<template>
  <a-drawer
      v-model:visible="visible"
      :hide-cancel="true"
      :mask="true"
      :footer="true"
      :width="400"
      :closable="false"
      :drawer-style="styles"
      class="reportDrawer"
      style="z-index: 99"
      @cancel="visible = false"
      @ok="visible = false"
  >
    <template #header>
      <div class="drawer-headers">
        <div class="drawer-text">
          已存报表
        </div>
        <div class="draw-icon" @click="closeVisible">
          <icon-double-right size="14px"/>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer">
        <router-link to="/report/management">
          <icon-launch/>
          报表管理
        </router-link>
      </div>
    </template>

    <div style="display: flex;flex-direction: column;">
      <div class="drawer-content">
        <a-input v-model="searchQuery" :style="{width:'216px'}" class="report-input" placeholder="输入报表名称">
          <template #prefix>
            <icon-search/>
          </template>
        </a-input>
        <a-tooltip :content="`仅展示应用 ${appData?.code}-${appData?.name} 的报表`">
          <a-checkbox v-model:model-value="appFilter"/>
        </a-tooltip>
        <a-select
            v-model:model-value="selectOption"
            style="width:110px; font-size: 14px;flex-shrink: 0;"
            :options="selectOptions"
        >
        </a-select>
      </div>
      <div class="List">
        <div
          v-for="(item, index) in filteredItems"
          :key="index"
          class="itemList"
          @mouseover="showIcon(index, true)"
          @mouseleave="showIcon(index, false)"
          @click="selectLable(item)">
          <div v-if="isItemVisible(item)" class="lists">
            <div class='list-name'>
              <img src="/icon/topMenu/custom.svg" style="width: 15px;height: 15px;margin-right: 8px;"/>
              <span>{{ item.name }}{{ item?.appId ? ` (${item?.appId})` : '' }}</span>
            </div>
            <div v-show="isIconVisible[index]">
              <a-tooltip content="删除报表" position="top">
                <icon-delete :style="{fontSize:'16px'}" @click.stop="deleteStatement(item)"/>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal
        v-model:visible="deleteModalShow"
        width="auto"
        :ok-button-props="{status:'danger'}"
        :closable="true" ok-text="删除" title-align="start"
        @ok="deleteConfirm"
        @cancel="deleteCancel">
      <template #title>
        删除报表
      </template>
      确认删除【{{ deleteReport?.name }}】？ 该操作不可恢复。
    </a-modal>
  </a-drawer>
</template>

<style scoped lang="less">
.reportDrawer :global(.arco-drawer-body){
  padding:0 !important;
  background-color: var(--color-fill-2);
}
.drawer-headers {
  height: 32px;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;

  .drawer-text {
    display: flex;
    align-self: start;
    font-size: 16px;
    font-weight: 500;
    color: var(--tant-text-gray-color-text1-1);
  }

  .draw-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    cursor: pointer;
  }

  .draw-icon:hover {
    background-color: var(--tant-disabled-color-disabled-fill);

  }
}

.List {
  height: 100%;
  padding: 16px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--color-fill-2);
  .itemList {
    width: 100%;
    .lists {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      height: 36px;
      min-height: 30px;
      margin-bottom: 8px;
      padding: 0 14px;
      background-color: var(--tant-bg-white-color-bg1-1);
      border-radius: 2px;
      cursor: pointer;

      .list-name {
        display: flex;
        flex: 1 1;
        align-items: center;
        height: 36px;
        overflow: hidden;
        color: var(--tant-text-gray-color-text1-2);
        font-size: 12px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .list-name:hover {
        color: var(--tant-primary-color-primary-hover);

      }
    }
  }

  .itemList:hover {
    color: var(--tant-primary-color-primary-hover);
  }
}

.drawer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background-color: var(--tant-bg-white-color-bg1-1);

  .report-input {
    height: 30px;
    padding: 0;
    color: var(--tant-text-gray-color-text1-2);
    line-height: 28px;
    text-overflow: ellipsis;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: none;
    outline: none;
    box-shadow: none;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 5px;
  color: var(--tant-primary-color-primary-default);
  cursor: pointer;
}


</style>