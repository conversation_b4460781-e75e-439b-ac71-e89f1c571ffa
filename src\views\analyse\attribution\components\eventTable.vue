<script setup lang="ts">
import {ref} from "vue";
import * as XLSX from 'xlsx';
import {cloneDeep} from "lodash"

interface Props {
  /**
   * 计算数据
   */
  eventData: any;
}

const props = defineProps<Props>()

const columns = ref<any>([
  {
    title:'归因事件',
    dataIndex:'name',
    slotName:'name',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'总触发数',
    dataIndex:'totalTriggerCount',
    slotName:'totalTriggerCount',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'有效触发的次数/率',
    dataIndex:'effectiveTriggerCount',
    slotName:'effectiveTriggerCount',
    minWidth:200,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'有效触发用户数',
    dataIndex:'effectiveTriggerUser',
    slotName:'effectiveTriggerUser',
    minWidth:200,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'贡献值',
    dataIndex:'contributionValue',
    slotName:'contributionValue',
    titleSlotName:'valueTitle',
    minWidth:140,
    sortable: { sortDirections: ['ascend', 'descend'] }
  },
  {
    title:'贡献度',
    dataIndex:'contributionLevel',
    minWidth:200,
    slotName:'contributionLevel',
    titleSlotName:'levelTitle',
    sortable: { sortDirections: ['ascend', 'descend'] }
  }
]);
const tableData = ref<any>([
  {
    name:'直接转化',
    totalTriggerCount:0,
    effectiveTriggerCount:0,
    effectiveTriggerUser:0,
    contributionValue:74880,
    contributionLevel:100,
  }
]);

const copyColumns = ref<any>([])
const copyTableData = ref<any>([])



const freshData = () => {
  
  copyColumns.value = cloneDeep(columns.value)
  copyTableData.value = cloneDeep(tableData.value)
};
freshData()

// 导出
const exportXlsx = () => {
  // 获取表头
  const headers = columns.value.map(item => item.title);
  const columnsList = [headers];
  tableData.value.forEach((item: any) => {
    const row = headers.map(header => {
      const dataIndex = columns.value.find(col => col.title === header)?.dataIndex;
      return dataIndex ? item[dataIndex] : '';
    });
    columnsList.push(row);
  });

  const WorkSheet = XLSX.utils.aoa_to_sheet(columnsList);
  const newWorkbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(newWorkbook, WorkSheet, '第一页');
  XLSX.writeFile(newWorkbook, `归因分析.xlsx`);
};


defineExpose({
  freshData,
  exportXlsx
})
</script>

<template>
  <div class="chart-content" style="width: 100%;margin-bottom: 24px;">
    <a-spin  style="width: 100%;height: 100%">
      <a-table :columns="columns" :data="tableData" :pagination="false" size="small" column-resizable :bordered="{cell:true}">
        <template #totalTriggerCount="{ record }">
          <span v-if="record.totalTriggerCount">{{ record.totalTriggerCount }}</span>
          <span v-else>-</span>
        </template>
        <template #effectiveTriggerCount="{ record }">
          <span v-if="record.effectiveTriggerCount">{{ record.effectiveTriggerCount }}</span>
          <span v-else>-</span>
        </template>
        <template #effectiveTriggerUser="{ record }">
          <span v-if="record.effectiveTriggerUser">{{ record.effectiveTriggerUser }}</span>
          <span v-else>-</span>
        </template>
        <template #contributionValue="{ record }">
          <span v-if="record.contributionValue">{{ record.contributionValue }}</span>
          <span v-else>-</span>
        </template>
        <template #contributionLevel="{ record }">
          <div style="display: flex;align-items: center;">
            <span v-if="record.contributionLevel" style="flex-shrink: 0;">{{ record.contributionLevel }}%</span>
            <span v-else style="flex-shrink: 0;">-</span>
            <a-progress :percent="0.2" :show-text="false" style="margin-left: 8px;"/>
          </div>
        </template>
        <template #valueTitle>
          贡献值
          <a-tooltip content="归因事件为 在线数据.总次数 贡献了多少值" position="top">
              <icon-info-circle/>
          </a-tooltip>
        </template>
        <template #levelTitle>
          贡献度
          <a-tooltip content="归因事件的贡献值在总体贡献值中的占比" position="top">
              <icon-info-circle/>
          </a-tooltip>
        </template>
      </a-table>
    </a-spin>
  </div>
</template>

<style scoped lang="less">

</style>