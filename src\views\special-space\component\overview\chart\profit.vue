<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {getDays} from "@/utils/dateUtil";

interface Props {
  /**
   * 日期
   */
  date: string[] | undefined

  /**
   * 利润
   */
  profit: number[]

  /**
   * 收入
   */
  income: number[]

  /**
   * 利润
   */
  spend: number[]
}

const props = defineProps<Props>()

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '0',
      top: '10',
      // bottom: '24',
      bottom: '48'
    },
    legend: {
      data: ['总利润', '总收入', "总投放"],
      bottom: '0',
      type: 'scroll'
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      className: 'echarts-tooltip-diy',
      extraCssText: 'max-height: 400px; overflow-y: auto;',
    },
    xAxis: {
      type: 'category',
      data: getDays(props.date[0], props.date[1]),
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`; // 转换为 k 单位
          }
          if (value >= 1000) {
            return `${value / 1000}k`; // 转换为 k 单位
          }
          return value;
        }
      }
    },
    series: [
      {
        name: '总利润',
        type: 'line',
        data: props.profit,
        label: {
          show: true,
          position: 'top',
          formatter(params) {
            if (!params.value || typeof params.value !== 'number') {
              return params.value;
            }
            if (params.value >= 1000000) {
              return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
            }
            if (params.value >= 1000) {
              return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
            }
            return Math.round(params.value); // 保留整数
          }
        },
        markLine: {
          symbol: 'none',
          data: [{type: 'average', name: 'Avg'}],
          label: {
            show: true,
            position: 'end'
          }
        }
      },
      {
        name: '总收入',
        type: 'bar',
        data: props.income
      },
      {
        name: '总投放',
        type: 'bar',
        color: "#ee6666",
        data: props.spend,
      }
    ]
  };
});


</script>

<template>
  <Chart :option="chartOption"/>
</template>

<style scoped lang="less">

</style>