<template>
  <div>
    <a-select
          v-model:model-value="teams"
          :loading="loading"
          allow-search
          multiple
          :max-tag-count="1"
          placeholder="请选择团队"
          :style="{width:width,borderRadius:'4px',height: '32px', backgroundColor: 'var(--tant-bg-white-color-bg1-1)'}"
          :filter-option="customFilterOption"
          @clear="teamChange"
          @remove="teamChange"
          @search="handleSearch"
          @popup-visible-change="popupVisibleChange">
          <template #search-icon>
            <div class="select-suffix">
              <div class="select-suffix-item">
                <a-tag v-if="!selectAll" class="tag-btn" @click="selectAllClick"> 全选 </a-tag>
                <a-tag v-else class="tag-btn" bordered color="green" @click="selectAllClick"> 全选 </a-tag>
                <a-tag v-if="teams.length > 0" class="tag-btn" color="orange" style="margin-left: 3px;" @click="clearAllClick"> 清空 </a-tag>
              </div>
            </div>
          </template>
          <template #header>
              <div v-if="allowHandle" style="padding: 6px 12px;" >
                  <a-button type="text" @click="addTeam">
                      <template #icon>
                          <icon-plus />
                      </template>
                      新建团队
                  </a-button>
              </div>
          </template>
          <a-option v-for="item in sortedEventList" :key="item.code" :value="item.code">
              <div class="team-item" style="width: 100%;">
                  <span class="name">{{ item.name }}</span>
                  <div v-if="allowHandle" class="extra">
                      <icon-edit class="icon" style="margin-right: 8px;" @click.stop="editTeamItem(item)"/>
                      <icon-delete class="icon" @click.stop="removeTeamItem(item)"/>
                  </div>
              </div>
          </a-option>
      </a-select>
      <handleModal ref="handleRefs" :groups-list="teamList" @update-data="init"/>
    </div>
  </template>
  
  <script lang="ts" setup>
  import {ref, computed, watch} from 'vue';
  import {getTeamList} from "@/api/analyse/api";
  import {deleteTeam} from "@/api/marketing/api";
  import {Message, Modal} from '@arco-design/web-vue';
  import { sortSelectedFirst } from '@/utils/sortUtil';
  import handleModal from '../selected-team/handleModal.vue'

  const props = defineProps({
    allowHandle: {
      type: Boolean,
      default: false
    },
    width:{
        type: String,
        default: '280px'
    }
  })
  const teams = defineModel<string[]>('teams', { default: [] })
  const loading = ref(false)
  const teamList = ref()
  const emits = defineEmits(['change'])
  const sortedEventList = ref<any>([]);

  // 搜索相关状态
  const searchText = ref('')

  // 获取过滤后的团队列表（根据搜索条件）
  const getFilteredTeams = () => {
    if (!searchText.value || !teamList.value) return teamList.value || [];

    const searchValue = searchText.value.toLowerCase();
    return teamList.value.filter((item: any) => {
      const name = item.name?.toLowerCase() || '';
      const code = item.code?.toLowerCase() || '';
      return name.includes(searchValue) || code.includes(searchValue);
    });
  };

  // 计算是否全选（基于当前搜索结果）
  const selectAll = computed(() => {
    const filteredTeams = getFilteredTeams();
    if (!filteredTeams || filteredTeams.length === 0) return false;

    const filteredValues = filteredTeams.map((item: any) => item.code);
    return filteredValues.every((value: string) => teams.value.includes(value));
  });

  // 自定义过滤函数
  const customFilterOption = (inputValue: string, option: any) => {
    if (!inputValue) return true;
    const searchValue = inputValue.toLowerCase();
    return option?.value?.toLowerCase()?.includes(searchValue) ||
           option?.label?.toLowerCase()?.includes(searchValue);
  };

  // 全选点击处理
  const selectAllClick = () => {
    const filteredTeams = getFilteredTeams();
    const filteredValues = filteredTeams.map((item: any) => item.code);

    if (selectAll.value) {
      // 取消选中当前搜索结果中的团队
      const newValue = teams.value.filter((x: string) => !filteredValues.includes(x));
      teams.value = newValue;
    } else {
      // 选中当前搜索结果中的团队
      const newValue = [...new Set([...teams.value, ...filteredValues])];
      teams.value = newValue;
    }
    // emits('change', teams.value);
  };
  // 清空点击处理
  const clearAllClick = () => {
    teams.value = [];
  };
  // 搜索处理
  const handleSearch = (value: string) => {
    searchText.value = value;
  };

  const teamChange = () => {
    emits('change', teams.value)
  }
  
  // 下拉消失时触发change事件
  const popupVisibleChange = (visible: boolean) => {
    if (visible) {
      sortedEventList.value = sortSelectedFirst(teamList.value, teams.value);
    } else {
      emits('change', teams.value)
    }
  }

  const handleRefs = ref()
  const addTeam = () => {
      handleRefs.value.openModal()
  }
  // 编辑团队列表
  const editTeamItem = (record) => {
      handleRefs.value.openModal(record)
  }
  // 删除团队列表
  const removeTeamItem = (record) => {
      Modal.confirm({
          title: '确认删除',
          titleAlign: 'start',
          content: `确定要删除团队"${record.name}"吗？`,
          modalClass: 'custom-confirm-modal',
          onOk: async () => {
              await deleteTeam(record.code)
              const index = teamList.value.findIndex(item => item.code === record.code);
              if (index > -1) {
                  teamList.value.splice(index, 1);
              }
              // 如果当前已选中该团队，则从已选中列表中移除
              const selectedIndex = teams.value.indexOf(record.code);
              if (selectedIndex > -1) {
                const newTeams = [...teams.value];
                newTeams.splice(selectedIndex, 1);
                teams.value = newTeams;
                emits('change', teams.value);
              }
              Message.success('删除成功');
          }
      });
  }
  const init = async () => {
    loading.value = true
    await getTeamList().then(res => {
        teamList.value = res
        sortedEventList.value = sortSelectedFirst(teamList.value, teams.value);
    })
    loading.value = false
  }
  init()
  </script>
  
  <style scoped lang="less">
  :deep(.arco-select-option-content){
      width: 100%;
  }
  .team-item{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name{
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
      }
      .extra{
          .icon{
              cursor: pointer;
          }
      }
  }
  :global(.custom-confirm-modal .arco-modal-footer) {
    text-align: right;
  }

  // 复选功能样式
  .select-suffix {
    display: flex;

    .select-suffix-item {
      margin-left: 8px;
      height: 24px;

      .tag-btn {
        width: 30px;
        height: 16px;
        font-size: 10px;
        padding: 4px;
        cursor: pointer;
        user-select: none;
      }
    }
  }
  </style>
    
