// 校验实验保存受众规则参数
export const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };

  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };

  // 添加对 userFilter 的校验
  const userFilterList = params?.eventCondition?.singleConditionExpressions
      ?.flatMap(item => item.filter?.filters || []) || [];
  const userSequenceList = params?.eventCondition?.sequenceConditionExpressions
      ?.flatMap(item => [
        ...(item.filter?.filters || []),
        ...item.eventList.flatMap(event => event.filter?.filters || [])
      ]) || [];
  const userConditionList = params?.userCondition?.filters || [];

  // 验证所有列表
  return verifyFilterList(userFilterList) &&
      verifyFilterList(userSequenceList) &&
      verifyFilterList(userConditionList);
};

export const validateClientConditionRules = (clientRules: Record<string, any> | undefined): boolean => {
    // 不存在或为空对象直接通过
    if (!clientRules || typeof clientRules !== 'object' || Object.keys(clientRules).length === 0) {
        return true;
    }
    
    return Object.entries(clientRules).every(([key, value]) => {
        // 校验 key 不能为空
        if (!key || key.trim() === '') {
            return false;
        }
        
        // 校验 value 必须是对象且包含有效的 thresholds
        if (!value || typeof value !== 'object') {
            return false;
        }
        
        // 校验 thresholds 必须存在且为非空数组
        if (!Array.isArray(value.thresholds) || value.thresholds.length === 0) {
            return false;
        }
        
        return true;
    });
};

