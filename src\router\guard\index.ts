import type {Router} from 'vue-router';
import {setRouteEmitter} from '@/utils/route-listener';
import setupUserLoginInfoGuard from './userLoginInfo';
import setupPermissionGuard from './permission';

function setupPageGuard(router: Router) {

  router.beforeEach((to) => {
    // emit route change
    setRouteEmitter(to);

    /* 路由发生变化修改页面title */
    if (to.meta.title) {
      document.title = to.meta.title
    }
  })
}

export default function createRouteGuard(router: Router) {
  setupPageGuard(router);
  setupUserLoginInfoGuard(router);
  setupPermissionGuard(router);
}
