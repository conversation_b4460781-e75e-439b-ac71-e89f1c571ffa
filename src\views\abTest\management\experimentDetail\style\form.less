.form-block-label{
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    .form-block-label-title{
        color: #141414e6;
        display: inline-block;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
    }
}
.domain-item{
    margin-bottom: 20px;
    overflow: hidden;
    .justify{
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow: hidden;
        width: 100%;
    }
    .icon-style{
        cursor: pointer;
    }
    .head{
        background-color: var(--color-fill-2);
        border-radius: 8px 8px 0 0;
        box-sizing: border-box;
        height: 40px;
        padding: 0 20px;
        .content{
            flex-grow: 1;
            flex-shrink: 1;
        }
        .extra {
            flex-shrink: 0;
        }
    }
    .content-info{
        border: 1px solid var(--tant-border-color-border1-1);
        border-radius: 0 0 8px 8px;
        border-top: none;
        overflow: hidden;
        .content-box{
            display: flex;
            .item-left{
                flex-basis: 40%;
                max-width: 600px;
                padding: 16px 20px 24px;
                width: 0;
            }
            .item-right{
                border-left: 1px solid var(--tant-border-color-border1-1);
                flex-basis: 60%;
                max-height: 960px;
                overflow-y: auto;
                padding: 16px 20px;
            }
        }
    }
}
.form-content{
    border-radius: 4px;
    display: block;
    min-height: 44px;
    flex-grow: 1;
    max-width: 100%;
    .config-box{
        display: flex;
        align-items: flex-start;
        .wrapper{
            margin-right: 8px;
            margin-bottom: 16px;
            position: relative;
        }
        .globalEdit{
            position: absolute;
            top: 0;
            right: 12px;
            height: 32px;
            width: 32px;
            border-radius: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            &:hover{
                background: #faf9f9;
            }
        }
        .flex1{
            flex: 1 1;
            max-width: 100%;
        }
        .delete-icon{
            flex-shrink: 0;
            cursor: pointer;
            visibility: visible;
            font-size: 18px;
            height: 32px;
            width: 32px;
            opacity: 0;
        }
        &:hover{
            .delete-icon{
                opacity: 1;
            }
        }
    }

}
.group-select{
    padding: 16px 25px 0px 46px;
    border-radius: 4px;
    background-color: var(--color-primary-light-1);
    margin-bottom: 24px;
}
.indicator-config{
    border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 6px;
    font-size: 13px;
    min-width: 800px;
    width: 100%;
    padding: 20px;
    margin-bottom: 24px;
    max-height: 300px;
    overflow: auto;
}