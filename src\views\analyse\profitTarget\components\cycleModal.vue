<template>
    <a-modal v-model:visible="modalVisible" :width="456" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
    </a-modal>
</template>

<script setup lang="ts">
import {ref} from "vue";


const modalVisible = ref(false)
const modalTitle = ref('达成周期')
const openModal = async (obj?:any) => {
    // if(obj){}
    modalVisible.value = true
}


const closeModal = () => {
    modalVisible.value = false
}
defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    button{
        border-radius: 4px;
    }
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}

</style>