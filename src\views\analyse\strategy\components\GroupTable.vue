<template>
    <a-table :columns="columns" :data="tableData" :loading="loading" :pagination="false" :style="{ width: '100%' }" :scroll="{ y: '100%' }">
    </a-table>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {getStrategyGroupData} from "@/api/analyse/api";

const columns = [
    {
        title: 'group',
        dataIndex: 'groupId',
        width: 100,
    },
    {
        title: '新增人数',
        dataIndex: 'newUserCount',
        width: 100,
        sortable: { sortDirections: ['ascend', 'descend'] },
    },
    {
        title:'总收入',
        dataIndex: 'income',
        width: 100,
        sortable: { sortDirections: ['ascend', 'descend'] },
    },
    {
        title:'ARPU',
        dataIndex: 'arpu',
        width: 100,
        sortable: { sortDirections: ['ascend', 'descend'] },
    }
]
const tableData = ref<any>([])
const loading = ref(false)
const getData = async (data:any) => {
    loading.value = true
    try {
        const res = await getStrategyGroupData(data)
        tableData.value = res
    }catch (e) {
        console.error(e)
    }finally {
        loading.value = false
    }
}
defineExpose({
    getData
})
</script>