# Dashboard 可视逻辑修复

## 问题描述
1. **看板切换时请求全部接口**：切换看板时所有组件都会发起请求
2. **刷新浏览器没有内容**：页面刷新后组件不显示

## 问题原因
1. 使用 `v-if="visibleMap[item.i]"` 导致组件在可见性未记录时不渲染
2. 看板切换时立即重置 `visibleMap` 导致组件消失
3. 刷新逻辑过于严格，只刷新已明确标记为可见的组件

## 修复方案

### 1. 改用 v-show 替代 v-if
```vue
<!-- 修改前 -->
<ReportCard v-if="item.objectType === 1 && visibleMap[item.i]" />

<!-- 修改后 -->
<ReportCard 
  v-if="item.objectType === 1"
  v-show="visibleMap[item.i] !== false" />
```

### 2. 优化可见性检测
```javascript
// 添加可见性变化处理函数
const handleVisibilityChange = (itemId: string, isVisible: boolean) => {
  visibleMap.value[itemId] = isVisible;
};

// 在模板中使用
v-viewport="(v) => handleVisibilityChange(item.i, v)"
```

### 3. 改进刷新逻辑
```javascript
const refreshVisibleComponents = () => {
  layout.value?.forEach((item, index) => {
    // 如果 visibleMap 中没有记录，则认为是可见的
    if (visibleMap.value[item.i] !== false) {
      const componentRef = getComponentRef(item, index);
      if (componentRef && typeof componentRef.freshData === 'function') {
        componentRef.freshData();
      }
    }
  });
};
```

### 4. 智能刷新策略
```javascript
const refreshAllReports = _.debounce(async () => {
  lastRefreshTime.value = dayjs().format('YYYY-MM-DD hh:mm:ss')
  
  // 检查是否有可见的组件
  const hasVisibleComponents = layout.value?.some(item => visibleMap.value[item.i] !== false);
  
  if (hasVisibleComponents) {
    // 只刷新可见的组件
    refreshVisibleComponents();
  } else {
    // 如果没有可见组件记录，则刷新所有组件
    const promises = reportCardRefs.value
        .filter(ref => ref)
        .map(reportCardRef => reportCardRef.freshData())
    await Promise.all(promises)
  }
}, 500)
```

### 5. 看板切换优化
```javascript
// 在看板数据加载完成后重置可见性映射
layout.value = response.uiConfig?.map(config => { ... })

// 重置可见性映射，让组件重新检测可见性
visibleMap.value = {};

loading.value = false
```

## 修复效果
1. **页面刷新正常显示**：组件默认可见，不依赖可见性记录
2. **看板切换优化**：只有真正可见的组件才会发起请求
3. **渐进式加载**：组件进入视口时才标记为可见并可能触发刷新
4. **兼容性好**：既支持可见性优化，又保证基本功能正常
