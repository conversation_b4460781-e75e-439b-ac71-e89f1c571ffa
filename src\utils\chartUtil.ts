import {probit} from 'simple-statistics';

/**
 * 根据置信水平获取正态分布z值（分位点）
 * 常用置信水平直接查表，其他置信水平用近似算法计算
 * @param confidenceLevel 置信水平（0~1之间）
 * @returns z值
 */
export function getZValue(confidenceLevel: number) {
  if (confidenceLevel <= 0 || confidenceLevel >= 1) return 0;
  return probit(1 - (1 - confidenceLevel) / 2); // 双侧
}

/**
 * 生成正态分布（高斯分布）数据点
 * @param mu 均值（正态分布的中心）
 * @param sigma 标准差（分布的宽度）
 * @param confidenceLevel 置信水平（决定 x 轴范围，默认 0.9999）
 * @param smoothLevel 平滑程度（点的数量，默认 200）
 * @returns 返回 [x, y] 数组，y 为对应 x 的概率密度值
 */
export function generateNormalDistribution(
  mu: number,
  sigma: number,
  confidenceLevel = 0.9999,
  smoothLevel = 200
): [number, number][] {
  // 存储结果数据点
  const data: [number, number][] = [];
  // 计算正态分布常数项 sqrt(2π)
  const sqrtTwoPi = Math.sqrt(2 * Math.PI);
  // 计算对应置信水平的z值，决定x轴范围
  const z = getZValue(confidenceLevel);
  // x轴起止范围
  const xStart = mu - z * sigma;
  const xEnd = mu + z * sigma;
  // 步长由点数决定
  const step = (xEnd - xStart) / (smoothLevel - 1);
  let i = 0;
  // 生成每个x对应的概率密度y
  while (i < smoothLevel) {
    const x = xStart + i * step;
    // 正态分布概率密度函数公式
    const exponent = -((x - mu) ** 2) / (2 * (sigma ** 2));
    const y = (1 / (sigma * sqrtTwoPi)) * Math.exp(exponent);
    data.push([x, y]);
    i += 1;
  }
  return data;
}
