import _ from "lodash";

const layoutModules = import.meta.glob('/src/layout/**/*.vue');
const viewModules = import.meta.glob('/src/views/**/*.vue');

export function menuToRouter(menu: any[], parentPath: string, meta: any) {
  if (menu.length === 0) return [];
  return menu?.sort((a, b) => a.sort - b.sort)?.map(item => {
    const path = (`${parentPath || ""}/${item.path}`).replace('//', '/');
    const router = {
      path,
      name: item.routeName,
      // component: _.isEmpty(item.component) ? null : () => import(item.component),
      component: _.isEmpty(item.component) ? null : (layoutModules[item.component] || viewModules[item.component]),
      meta: {
        ...(meta || {}),
        locale: item.name,
        icon: item.icon,
        order: item.sort,
        hideInMenu: item.hideInMenu,
        requiresAuth: item.requiresAuth || meta?.requiresAuth,
        hasSideMenu: item.hasSideMenu || meta?.hasSideMenu,
        activeMenu: item.activeMenu || meta?.activeMenu || item.routeName,
      }
    }
    if (item?.children?.length > 0) {
      const sortedChildren = item.children?.sort((a, b) => a.sort - b.sort);
      router.redirect = (`${path}/${sortedChildren[0].path}`).replace('//', '/')
      router.children = menuToRouter(sortedChildren, path, router.meta);
    }
    return router
  });

}