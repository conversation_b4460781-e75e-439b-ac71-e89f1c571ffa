<template>
  <div id="attributionRoot">
    <div class="analyse-content">
      <analyse-header :header-info="headerInfo"/>
      <div class="analyse-body">
        <a-split v-model:size="splitSize" style="height: 100%;" min="400px" max="0.9" :disabled="splitSize === '0px'">
          <template #first>
            <div class="body-left">
              <a-spin :loading="dataLoading" class="box">
                <div class="query-condition">
                  <analysisIndex/>
                  <!-- 全局筛选 -->
                  <globalFilter @filters-change="filtersChange"/>
                </div>
                <div v-if="showBodyLeft" class="left-footer">
                  <a-button @click="() => saveVisible = true">保存</a-button>
                  <a-button type="primary" :loading="loading" @click="computedData">计算</a-button>
                </div>
                <!-- 保存弹窗 -->
                <a-modal v-model:visible="saveVisible" :on-before-ok="validateForm" width="466px" @cancel="handleSaveCancel">
                  <template #title>
                    <div class="modal-title">保存报表</div>
                  </template>
                  <a-form ref="saveFormRef" :model="form">
                    <a-form-item field="name" label="报表名称" required :rules="[{ required: true, message: '报表名称不能为空' }]">
                      <a-input v-model="form.name" placeholder="80字以内" :max-length="80"/>
                    </a-form-item>
                    <a-form-item field="dashboard" label="保存至看板">
                      <dashboard-select v-model:selected="form.dashboard" type="dashboard"/>
                    </a-form-item>
                    <a-form-item field="description" label="备注">
                      <a-textarea v-model="form.description" placeholder="请输入" :auto-size="{ minRows:2, maxRows:5}"/>
                    </a-form-item>
                  </a-form>
                  <template #footer>
                    <a-button @click.stop="handleSaveCancel">取消</a-button>
                    <a-button type="primary" @click="saveReport">保存</a-button>
                  </template>
                </a-modal>
              </a-spin>
              <div v-if="splitSize != '0px'" class="arrow" @click="() => splitSize = '0px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
          <template #second>
            <div id="body-right" class="body-right">
              <div id="wrap" class="wrap" :style="{ width:'calc(100% - 24px)'}">
                <a-spin :loading="loading" :size="100" class="body-right-content">
                  <div class="right-header">
                    <a-space direction="horizontal" size="large">
                      <div style="display: flex;align-items: center;">
                        筛选周期：
                        <date-picker :date-range="boardDate" @date-pick="pickDate"/>
                      </div>
                    </a-space>
                    <div style="display: flex;">
                      <a-button @click="importTable">导出</a-button>
                    </div>
                  </div>
                  <div v-if="!loading && eventData?.attributionQueryResult?.length" class="right-content">
                    <event-table-vue ref="eventtable" :event-data="eventData"/>
                  </div>
                  <div v-if="!loading && !eventData?.attributionQueryResult?.length" class="empty">
                    <div class="empty-body">
                      <div style="box-sizing: border-box">
                        <div class="empty-img">
                          <img alt="empty" src="/src/assets/images/empty.png" width="400px" height="256px"/>
                        </div>
                        <div class="empty-description">
                          <div class="title">当前查询无数据</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-spin>
              </div>
              <div v-if="splitSize == '0px'" class="arrow deg" @click="() => splitSize = '488px'">
                <icon-double-left/>
              </div>
            </div>
          </template>
        </a-split>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import '@/views/analyse/css/analyse.less'
import {onMounted, reactive, ref} from "vue";
import {useEventBus, useLocalStorage} from '@vueuse/core';
import {Message} from '@arco-design/web-vue';
import {saveAnalyseReportList} from "@/api/analyse/api";
import {ChartType} from "@/api/enum";
import {toolStore} from '@/store';
import {LocalStorageEventBus} from "@/types/event-bus";
import _ from "lodash";
import {ReportAnalyseModel} from "@/api/analyse/type";
import dashboardSelect from '@/components/dashboard-select/index.vue'
import DatePicker from "@/components/date-picker/index.vue";
import globalFilter from "../components/globalFilter.vue"
import analyseHeader from "../components/analyseHeader.vue"
import analysisIndex from "./components/analysisIndex.vue"
import eventTableVue from "./components/eventTable.vue";

const headerInfo = reactive({
  title: '归因分析',
  img: '/icon/topMenu/attribution.svg',
  tips: '按照指定方式，基于某段时间内的目标事件，找到窗口期内的归因事件，以查看归因事件的发生对目标转化的贡献占比',
  root: '#attributionRoot',
  showUpdateTime: true,
  showDownloadData: true,
  showMoreValue: true
})
const localStorageEventBus = useEventBus(LocalStorageEventBus);
const toolData = toolStore()
const eventData = ref({
  y: [],
  groupsDesc: []
})
const splitSize = useLocalStorage("analyse-view-split-size", '488px')
// ------left start
const loading = ref(false)
// 传参数组
const queryParam = reactive({
  indicators: [], // 查询指标
  filter: [], // 查询筛选条件
  aggregates: [], // 聚合分组
  dateRange: [], // 查询日期范围
  timeParticleSize: '', // 时间粒度
  chartType: '', // 图标类型
})
const dataLoading = ref(false)
const isReset = ref(false)
const eventBus = useEventBus('eventList');

// 全局筛选传参
const filtersChange = (v) => {
  queryParam.filter = v
}
// 计算
const computedData = () => {
}
const saveVisible = ref(false)
const form = ref({
  name: '',
  dashboard: '',
  description: ''
})
const saveFormRef = ref();
const validateForm = async () => {
  return !await saveFormRef.value?.validate();
}
onMounted(() => {
  eventBus.on((event: any) => {
    form.value.name = `${event[0]?.displayName}等${event.length}个`
  });
})
const handleSaveCancel = () => {
  saveVisible.value = false;
}
// 保存报表
const saveReport = async () => {
  if (!form.value.name) {
    Message.error('报表名称未填写')
    return
  }
  await saveAnalyseReportList(ReportAnalyseModel.EVENT, undefined, form.value.dashboard, form.value.name, form.value.description, queryParam, undefined, ChartType.TABLE).then(res => {
    Message.info(_.isEmpty(form.value.dashboard) ? "报表已保存" : "报表已保存并添加至看板")
  })
  saveVisible.value = false;
}

const boardDate = ref<any>({
  recentStartDate: 7,
  recentEndDate: 1,
  dateText: '过去7天'
});
const pickDate = (date: any) => {
  boardDate.value = date
};

// ---right end
const eventtable = ref()
const importTable = () => {
  eventtable.value.exportXlsx()
}
const showBodyLeft = ref(true)

const init = async () => { 
  if (dataLoading.value){
    // 正在初始化中
    return
  }
  dataLoading.value = true
  toolData.updateTemporaryList([])
  await toolData.fetchAllModalList()
  dataLoading.value = false
  setTimeout(() => {
    computedData()
  }, 1000)
}

init()
localStorageEventBus.on((name, value) => {
  if (name === "app-id" || name === "data-source") {
    init()
  }
})
// 重置
const reset = () => {
  isReset.value = true
  init()
}
</script>

<style scoped lang="less">
#attributionRoot {
  width: 100%;
  min-width: 800px;
  padding: 24px;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: var(--color-fill-2);
}

.span {
  padding: 2px 8px;
  font-weight: 500;
  font-size: 20px;
}

.button {
  color: #202241;
  text-decoration: none;
  height: 32px;
  padding: 5px 16px;
  background-color: #fff;
  border: 0;
  cursor: pointer;
  border-radius: 4px;
}

.button:hover {
  color: #747c94;
}

.box {
  width: 100%;
  height: 100%;
}

.query-condition {
  width: 100%;
  height: calc(100% - 56px);
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}

.left-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 56px;
  padding: 12px 48px 12px 20px;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-top: 1px solid var(--tant-border-color-border1-1);
  align-items: center;

  button {
    margin-left: 12px;
    border-radius: 4px;
  }

  :deep(.arco-btn-primary) {
    min-width: 80px;
  }
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--tant-text-gray-color-text1-1);
  text-align: left;
  width: 100%;
}

:deep(.arco-modal .arco-modal-header) {
  border-bottom: none !important;
}

.right-header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: -8px;
  padding-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
}

.right-header::before, .right-header::after {
  position: absolute;
  bottom: -1px;
  left: -24px;
  width: 24px;
  border-bottom: 1px solid var(--tant-border-color-border1-1);
  content: "";
}

.right-header::after {
  right: -24px;
  left: auto;
}


.right-content {
  width: calc(100% - 24px);
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding-top: 24px;
  //padding-bottom: 18px;
  // border-bottom: 1px solid var(--tant-border-color-border1-1);
}


.wrap {
  position: relative;
  // width: calc(100% - 24px);
  flex: 2;
  height: 100%;
  // margin: 0 16px 16px 0;
  padding-bottom: 24px;
  overflow: overlay;
  overflow-x: hidden;
  overflow-y: auto;
  white-space: normal;
  background-color: #fff;
  border-top-right-radius: 4px;
  transition: width .3s;
  display: flex;

}

.body-right-content {
  width: 100%;
  height: calc(100% - 54px);
  padding: 16px 24px 0;
  border-radius: 2px;
}

.trans {
  width: 200px;
  height: 100%;
}

.right100 {

  width: 100%;

}

.btn {
  background-color: #fff;
  border: 1px solid #e3e4e5;
  border-radius: 4px;
  box-shadow: 0;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 5px 16px;
  cursor: pointer;
}

.btn:hover {
  background-color: #f6f6f9;
}

.customStyle {
  border-radius: 6px;
  border: none;
  overflow: hidden;
}

.customStyle:hover {
  background-color: #f6f6f9;
  border-radius: 4px;
}

.spanitem {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  padding: 16px !important;
  background-color: var(--tant-fill-color-fill1-2) !important;
  border-radius: 4px;

  .label {
    margin-right: 16px;

    .title {
      display: flex;
      align-items: center;
      height: 32px;
      color: var(--tant-text-gray-color-text1-2);
      font: var(--tant-body-font-body-regular);
      white-space: nowrap;

      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .total {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
      color: var(--tant-secondary-color-secondary-default);
      background-color: var(--tant-bg-white-color-bg1-1);
      border: none;
    }
  }
}

:deep(.arco-collapse-item-content) {
  background: #fff;
}

:deep(.arco-collapse-item-header) {
  border-bottom: none !important;
}

.spanitem:hover {
  background-color: #f6f6f9;
  cursor: default;
}
.empty {
  height: 85%;
  width: 100%;
  min-height: 400px;
  margin: 0 24px 32px;
  background-color: var(--tant-bg-white-color-bg1-1);
  border-radius: 4px;

  .empty-body {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);

    .empty-img {
      width: 400px;
      height: 256px;
    }

    .empty-description {
      color: var(--tant-text-gray-color-text1-3);
      font-size: 12px;
      width: 400px;
      word-break: break-all;
      text-align: center;
      margin: 0 auto;

      .title {
        margin-bottom: 12px;
        font-weight: 500;
        font-size: 16px;
        color: var(--tant-text-gray-color-text1-2);
      }

      .button-add {
        margin-top: 12px;
        color: var(--tant-white-white-100);
        background-color: var(--tant-primary-color-primary-default);
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 16px;
        font: var(--tant-body-font-body-regular);
        text-transform: capitalize;
        border-radius: var(--tant-border-radius-medium);
        box-shadow: unset;
      }
    }

  }
}
</style>