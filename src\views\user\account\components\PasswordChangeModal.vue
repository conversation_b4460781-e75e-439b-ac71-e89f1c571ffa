<template>
  <a-modal :visible="visible" title="修改密码" :footer="false" title-align="start" @cancel="handleCancel" @update:visible="updateVisible">
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="新密码" field="newPassword">
        <a-input-password v-model="form.newPassword" placeholder="请输入新密码" />
      </a-form-item>
      <a-form-item label="确认密码" field="confirmPassword">
        <a-input-password v-model="form.confirmPassword" placeholder="再次确认密码" />
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleConfirm"> 重置 </a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import type { FormInstance, FieldRule } from '@arco-design/web-vue';
  import { saveSystemMember } from '@/api/setting/api';

  interface Props {
    visible: boolean;
    code?: string;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'refresh'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();
  const loading = ref(false);

  const form = reactive({
    newPassword: '',
    confirmPassword: '',
  });

  const validateConfirmPassword = (value: string, callback: (error?: string) => void) => {
    if (value !== form.newPassword) {
      callback('两次输入的密码不一致');
    } else {
      callback();
    }
  };

  const rules: Record<string, FieldRule[]> = {
    newPassword: [
      { required: true, message: '请输入新密码' },
      { minLength: 6, message: '密码至少6位字符' },
    ],
    confirmPassword: [{ required: true, message: '请再次输入密码' }, { validator: validateConfirmPassword }],
  };

  watch(
    () => props.visible,
    (newVal) => {
      if (!newVal) {
        formRef.value?.resetFields();
      }
    }
  );

  const updateVisible = (value: boolean) => {
    emit('update:visible', value);
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const handleConfirm = async () => {
    formRef.value?.validate(async (valid: any) => {
      if (!valid) {
        loading.value = true;
        try {
          await saveSystemMember({ password: form.newPassword, code: props.code });
          Message.success('密码修改成功');
          emit('refresh');
          updateVisible(false);
        } catch (error) {
          console.error('修改失败，请重试');
        } finally {
          loading.value = false;
        }
      }
    });
  };
</script>

<style scoped lang="less">
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
