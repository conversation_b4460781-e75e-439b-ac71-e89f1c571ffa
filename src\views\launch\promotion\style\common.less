.page-content {
  width: 100%;
  height: calc(100vh - 240px);
  display: flex;
  flex-direction: column;
}
.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px;
}
.setting{
  display: flex;
  .setting-content{
    flex-wrap: wrap;
    display: flex;
    row-gap: 16px;
  }
  .label{
    width: 60px;
  }
}
.table-wrap{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  .wrap-left{
    .text{
      color: rgb(var(--gray-10));
      font-weight: 500;
    }
  }
  .wrap-right{
  }
}
.table-area {
  flex: 1 1 0%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.br4{
  border-radius: 4px;
}
.cell-content{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-box{
    display: flex;
    align-items: center;
    width: 100%;
    .ad-img{
      width: 50px;
      height: 50px;
      margin-right: 10px;
      flex-shrink: 0;
      flex-grow: 0;
    }
    .video-icon{
      position: absolute;
      left: 50%;
      top: 50%;
      width: 24px;
      height: 24px;
      transform: translate(-50%, -50%);
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAAA/1BMVEUAAAAAAAAYGBjw8PDw8PD9/f37+/v7+/v29vby8vLp6enb29vW1tbZ2dn6+vr4+Pj29vb19fXr6+vw8PDg4ODR0dHHx8esrKzi4uJtbW34+Pj4+Pjz8/Po6Ojv7+/Z2dnn5+fm5ua9vb2UlJTe3t5NTU0tLS3b29ve3t78/Pzz8/Pt7e3l5eXj4+Pt7e3s7Ozq6urX19fW1tbT09PLy8u6urrl5eWzs7Pk5OTi4uKhoaGbm5uRkZGKioqDg4Pf399zc3Pe3t7c3NzU1NT+/v729vb39/fs7Ozd3d3o6OjDw8PAwMDh4eHg4OB8fHxkZGReXl5RUVFDQ0PY2Nj///9c9epBAAAAVHRSTlMzADfN0Pfv7d/FlTENBuro2tPCvquUiG9fTeXk1bqzoIl9fV9FQzs6NPLPyLaxq6OdnZuYjXp3c29pZmJdWlZTUE0zGvrg3sWnj4SBZltUSklFQSpJWZECAAABXklEQVQoz23T1XKDQBSA4bNbPBAPEne3pvHGU3fh/Z+ldCEshPyX5xtmlhVAbodSPpbJxPKlEJ25/BaNJCcrw6iUk+HY9oybMUaDU1jLiiEvL7hX8KXyOuVS+gPOWofnJ9YjnxBoF97a/Muv4UK3/JFwIgWkssAsvX6d/+dGBAPpyjS5UY1ynQ9Z3B+DHWNatTXqNwMESusb3K+t2KfdiQ0OQTULlElCGYOd0IBpkrLTXW4DpO4CpBRlN8FZexEGKmUakFISDMcX+NFZ+jMUCwHmUs7fJ6egx885XnEnezi2sI8zKna3jZUBtTXPrrHdDbhNRARo3gG7uGlmZxhojG6xkl46J9grGOBp9kAO9P2+Dhf6Se8Jo34OBxXHh8hmRezhgOYSisNIFjs1v35dJWR6kRWJf8EUscoV/a+kIUZGK0ysUggnQoiyXVOKslGGEdioZCFlmtysVg+yd/IHprk3IgIUwaMAAAAASUVORK5CYII=) no-repeat;
      background-size: cover;
    }
    .icon{
      flex-shrink: 0;
      opacity: 0;
      &:hover{
        color: #0b75ff;
      }
    }
    &:hover{
      .icon{
        opacity: 1;
      }
    }
  }
  .text{
    // color: #0b75ff;
    background: transparent;
    // cursor: pointer;
    transition: color .2s ease;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    text-overflow: ellipsis;
  }
  .text-link{
    color: #0b75ff;
    background: transparent;
    cursor: pointer;
    transition: color .2s ease;
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    text-overflow: ellipsis;
  }
}
.edit-title-box{
  width: 350px;
  background-color: #fff;
  border: 0;
  border-radius: 6px;
  background-clip: padding-box;
  box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
  padding: 5px;
  .edit-footer{
    text-align: right;
    margin-top: 8px;
  }
}
.trigger-content{
  background: #fff;
  color: #000;
  border-radius: 6px;
  border: none !important;
  box-shadow: 0 1px 6px rgba(0, 0, 0, .2) !important;
  .tooltip-icon-list-header{
    padding: 7px 16px;
    font-size: 14px;
    font-weight: 600;
  }
  .dropdown-list-content{
    padding: 0;
    .dropdown-list-item-wrapper{
      margin: 0;
      padding: 0;
      &:hover{
        background-color: #f9f9f9;
      }
      .dropdown-list-item{
        cursor: pointer;
        min-width: 100px;
        padding: 6px 15px !important;
      }
    }
  }
}
.actions-content{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.actions-btn{
  color: #0b75ff;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color .2s ease;
}
.actions-btn-disabled{
  color: #999;
  cursor: default;
}
.modal-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}
.modal-header{
  display: flex;
  align-items: center;
}
.modal-body{
  padding-left: 40px;
  font-size: 14px;
}
.edit-log-box{
  width: 240px;
  .edit-log-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.body-box{
  width: 100%;
  display: flex;
  align-items: center;
  .content{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  .icon{
    flex-shrink: 0;
    cursor: pointer;
    opacity: 0;
    transition: all .3s;
    &:hover{
      color: #0b75ff;
    }
  }
  &:hover{ 
    .icon{
      opacity: 1;
    }
  }
}
.material-content{
    flex: 1 1 0%;
    min-height: 0;
    display: flex;
    flex-direction: column;
}
.thumbnail-box{
    width: 44px;
    height: 44px;
    img{
        width: 100%;
        height: 100%;
    }
}
.thumbnail-content{
    width: 240px;
    height: 240px;
}
.channel-img{
  width: 18px;
  height: 18px;
}