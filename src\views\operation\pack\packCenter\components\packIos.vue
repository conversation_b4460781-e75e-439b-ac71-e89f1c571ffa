<template>
    <div class="cross-content">
        <a-spin :loading="loading" class="group-box">
            <a-anchor
                class="pack-configs-nav"
                :change-hash="false"
                scroll-container="#config-detail"
                @change="configSelectChange">
                <a-anchor-link v-for="(v, k) in iosConfigsGroup" :class="configIndex === k ? 'active' : ''" :key="k" :href="v.link">{{v.name}}</a-anchor-link>
            </a-anchor>
        </a-spin>
        <div class="group-detail">
            <div :style="{'text-align': 'end', 'height': '32px'}">
                <a-space>
                    <a-button :loading="configLoading" class="button" @click="refreshConfig">刷新</a-button>
                    <a-button :loading="saveConfigLoading" class="button" type="primary" @click="saveConfig">保存</a-button>
                </a-space>
            </div>
            <div id="config-detail" ref="containerRef" :style="{'height': 'calc(100% - 10px)', 'overflow-y': 'auto'}">
                <a-form ref="formRef" :model="formData" layout="vertical" :loading="configLoading || saveConfigLoading">
<!--                    <a-spin>-->
                        <section id="jsonversion">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    打包选项
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="dataVersion" :rules="[{required:true,message:'请选择下发Json版本'}]" label="下发Json版本">
                                        <a-select
                                            :options="jsonVersionOptions"
                                            v-model:model-value="formData.dataVersion"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            :loading="jsonVersionLoading"
                                            allow-search >
                                            <template #header>
                                                <div style="padding: 0 12px;" >
                                                    <div class="create-condition" @click="(ev) => drawerTabChange(ev,'default', true)">
                                                        <icon-plus style="margin-right: 12px;"/>
                                                        创建数据版本
                                                    </div>
                                                </div>
                                            </template>
                                        </a-select>
                                    </a-form-item>
                                    <a-form-item field="packType" :rules="[{required:true,message:'请选择打包类型'}]" label="打包类型">
                                        <a-select
                                            :options="packTypeOptions"
                                            v-model:model-value="formData.packType"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            allow-search/>
                                    </a-form-item>
                                    <a-form-item field="packServer" label="服务器">
                                        <a-select
                                            :loading="packServerLoading"
                                            :options="packServerOptions"
                                            @click="loadPackServer"
                                            v-model:model-value="formData.packServer"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :field-names="{value: 'id', label: 'label'}"
                                            allow-search/>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="sdkconfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    SDK配置
                                </div>
                                <a-space :wrap="true">
                                    <!--                                    <a-form-item field="sdkTag" :rules="[{required:true,message:'请填写sdk版本'}]" label="sdk版本">-->
                                    <!--                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.sdkTag" allow-clear placeholder="please enter value..." />-->
                                    <!--                                    </a-form-item>-->
                                    <a-form-item field="sdkTag" :rules="[{required:true,message:'请选择sdk版本'}]" label="sdk版本">
                                        <a-select
                                            :options="sdkOptions"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.sdkTag"
                                            allow-search
                                            allow-create
                                        />
                                    </a-form-item>
                                    <a-form-item field="sdkUrl" :rules="[{required:true,message:'请填写sdk地址'}]" label="sdk地址">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.sdkUrl" allow-clear @change="chageSdkUrl" placeholder="please enter value..." />
                                    </a-form-item>
                                </a-space>
                                <a-form-item field="sdkTypes" :rules="[{required:true,message:'请选择sdk类型'}]" label="sdk类型">
                                    <a-spin :loading="sdkTypesLoading" :style="{width: '100%'}">
                                        <a-space v-if="sdkTypes.length" direction="vertical">
                                            <a-checkbox
                                                :model-value="formData.sdkTypes && formData.sdkTypes.length === sdkTypes.length"
                                                :indeterminate="formData.sdkTypes && formData.sdkTypes.length > 0 && formData.sdkTypes.length < sdkTypes.length"
                                                @change="changeAllSdkTypes">全选
                                            </a-checkbox>
                                            <a-checkbox-group v-model="formData.sdkTypes">
                                                <a-checkbox v-for="item in sdkTypes" :value="item">{{item}}</a-checkbox>
                                            </a-checkbox-group>
                                        </a-space>
                                    </a-spin>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="appinfo">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    应用信息
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="appleAccount" :rules="[{required:true,message:'请选择账户'}]" label="账户">
                                        <a-select
                                            :options="accounts"
                                            v-model:model-value="formData.appleAccount"
                                            :style="{width:'200px'}"
                                            placeholder="Please select ..."
                                            :loading="accountsLoading"
                                            :field-names="{value: 'appleAccount', label: 'appleAccount'}"
                                            @change="changeAccounts"
                                            allow-search/>
                                    </a-form-item>
                                    <a-form-item field="name" :rules="[{required:true,message:'请填写应用名称'}]" label="应用名称">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.name" placeholder="please enter main path..." />
                                    </a-form-item>
                                    <a-form-item field="appStoreId" :rules="[{required:true,message:'请填写appStoreId'}]" label="应用商店ID">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.appStoreId" placeholder="please enter main path..." />
                                    </a-form-item>
                                    <a-form-item field="versionName" :rules="[{required:true,message:'请填写版本号'}]" label="版本号">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.versionName" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="versionCode" :rules="[{required:true,message:'请填写版本code'}]" label="版本code">
                                        <a-input-number
                                            hide-button
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.versionCode"
                                            :min="1" :step="1"
                                            placeholder="please enter value..." />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="icon">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    ICON
                                </div>
                                <a-form-item field="iconUploaded" :rules="[{required:true,message:'请上传icon'}]" label="icon">
                                    <div v-if="formData.iconUploaded" :style="{width: '100%'}">
                                        <a-spin :loading="iconLoading" :style="{width: '100%'}">
                                            <a-space :wrap="true">
                                                <div v-if="iconUrlContent && iconUrlContent.content" class="icon-box">
                                                    <p><a-image width="120" :style="{border: '1px solid #eee'}" :src="iconUrlContent.content" /></p>
                                                    <div class="icon-del-btn">
                                                      <a-tooltip content="删除该图标">
                                                        <icon-delete @click="delIcon(iconUrlContent.name)" :size="16"/>
                                                      </a-tooltip>
                                                    </div>
                                                    <label :style="{'font-size': '11px'}">{{iconUrlContent.name}}</label>
                                                </div>
                                            </a-space>
                                        </a-spin>
                                    </div>
                                    <div v-if="formData.iconUploaded">
                                        <a-tooltip content="更新ICON">
                                            <a-link @click="() => uploadPackFile('icon')">
                                                更新ICON
                                                <template #icon>
                                                    <icon-edit />
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('icon')">上传ICON</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="images">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    图片管理
                                </div>
                                <a-form-item field="imagesUploaded" :rules="[{required:true,message:'请上传启动图'}]" label="图片资源">
                                    <div v-if="formData.imagesUploaded" :style="{width: '100%'}">
                                        <a-spin :loading="imagesLoading" :style="{width: '100%'}">
                                            <a-image-preview-group infinite>
                                                <a-space :wrap="true">
                                                    <div v-for="image in imagesUrlContent" class="icon-box">
                                                        <p :style="{width: '120px', height: '120px'}"><a-image width="100%" height="100%" fit="contain" :style="{border: '1px solid #eee'}" :src="image.content" /></p>
                                                        <div class="icon-del-btn">
                                                          <a-tooltip content="删除该图标">
                                                            <icon-delete @click="delIcon(image.name)" :size="16"/>
                                                          </a-tooltip>
                                                        </div>
                                                        <label :style="{'font-size': '11px'}">{{image.name}}</label>
                                                    </div>
                                                </a-space>
                                            </a-image-preview-group>
                                        </a-spin>
                                    </div>
                                    <div v-if="formData.imagesUploaded">
                                        <a-tooltip content="更新icon">
                                            <a-link @click="() => uploadPackFile('images')">
                                                更新图片资源
                                                <template #icon>
                                                    <icon-edit />
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('images')">上传图片资源</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="names">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    应用名多语言
                                </div>
                                <a-space :wrap="true" class="maven-url">
                                    <a-form-item
                                        v-for="(v,k) in formData.names"
                                        :label="k === 0 ? '应用名多语言' :''">
                                        <div :style="{width: '100%', display: 'flex'}">
                                            <a-select
                                                :options="iosLangOptions"
                                                :style="{'width':'200px'}"
                                                v-model:model-value="v.id"
                                                :field-names="{value: 'id', label: 'label'}"
                                                placeholder="请选择语言"
                                                allow-clear
                                                allow-search
                                                allow-create/>
                                            <span :style="{width: '10px'}"></span>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="v.value" placeholder="请输入语言对应的应用名" />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm content="确认删除吗" type="warning" @ok="formData.names.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <a-form-item>
                                        <a-button class="button" type="primary" @click="formData.names.push({id: '', value: ''})">
                                            <icon-plus :style="{'margin-right': '3px'}"/>
                                            新增配置
                                        </a-button>
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="git">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    代码仓库
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="url" :rules="[{required:true,message:'请填写代码仓库地址'}]" label="仓库地址">
                                        <a-input :style="{'width':'408px'}" v-model:model-value="formData.url" placeholder="please enter url..." />
                                    </a-form-item>
                                    <a-form-item v-if="formData && formData.url && formData.url.startsWith('git@')" field="branch" :rules="[{required:true,message:'请填写代码分支'}]" label="分支名称">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.branch" placeholder="please enter branch..." />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="filemanage">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    FIREBASE
                                </div>
                                <a-form-item field="gsUploaded" :rules="[{required:true,message:'请上传GoogleService-Info.plist'}]" label="GoogleService-Info.plist">
                                    <div v-if="formData.gsUploaded">
                                        <a-tooltip content="更新google-services.json">
                                            <a-link @click="() => uploadPackFile('google_services')">
                                                <template #icon>
                                                    <icon-edit />更新
                                                </template>
                                            </a-link>
                                        </a-tooltip>
                                        <a-tooltip content="点击查看内容详情">
                                            <a-link @click="(ev) => showContentModal('google_services')">GoogleService-Info.plist</a-link>
                                        </a-tooltip>
                                    </div>
                                    <a-button v-else @click="() => uploadPackFile('google_services')">上传GoogleService-Info.plist</a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="buildconfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    注包配置
                                </div>
                                <a-space :wrap="true">
                                    <a-form-item field="screenOrientation" label="屏幕方向">
                                        <a-select
                                            :options="[{label: '竖屏', value: 'portrait'}, {label: '横屏', value: 'landscape'}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.screenOrientation"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="iap" label="游戏内付">
                                        <a-select
                                            :options="[{label: '支持', value: true}, {label: '不支持', value: false}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.iap"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="gameCenter" label="游戏中心">
                                        <a-select
                                            :options="[{label: '支持', value: true}, {label: '不支持', value: false}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.gameCenter"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item field="signInApple" label="Apple登录">
                                        <a-select
                                            :options="[{label: '支持', value: true}, {label: '不支持', value: false}]"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.signInApple"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                    <a-form-item v-if="formData.packType === 'cocos'" field="ivycoreUrl" label="ivycore地址">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.ivycoreUrl" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item v-if="formData.packType === 'cocos'" field="ivycoreBranch" label="ivycore分支">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.ivycoreBranch" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="dingWebhook" label="钉钉通知URL">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.dingWebhook" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item field="dingMsg" label="钉钉通知内容">
                                        <a-input :style="{width:'200px'}" v-model:model-value="formData.dingMsg" placeholder="please enter value..." />
                                    </a-form-item>
                                    <a-form-item v-if="formData.packType==='unity'" field="unityVersion" :rules="[{required:true,message:'请选择Unity版本'}]" label="Unity版本">
                                        <a-select
                                            :options="unityVersions"
                                            :loading="unityVersionsLoading"
                                            :style="{width:'200px'}"
                                            v-model:model-value="formData.unityVersion"
                                            placeholder="Please select ..."
                                            allow-search />
                                    </a-form-item>
                                </a-space>
                            </div>
                        </section>
                        <section id="fbConfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    FB配置
                                </div>
                                <a-switch
                                    size="small"
                                    v-model:model-value="formData.fbConfig"
                                /> {{formData.fbConfig ? '开' : '关'}}
                                <div v-if="formData.fbConfig" :style="{'padding-top': '10px'}">
                                    <a-space :wrap="true">
                                        <a-form-item field="facebookAppId" :rules="[{required:true,message:'请输入FB应用ID'}]" label="FB应用ID">
                                            <a-input :style="{width:'200px'}" v-model:model-value="formData.facebookAppId" placeholder="please enter value..." />
                                        </a-form-item>
                                        <a-form-item field="facebookAppName" :rules="[{required:true,message:'请输入FB应用名称'}]" label="FB应用名称">
                                            <a-input :style="{width:'200px'}" v-model:model-value="formData.facebookAppName" placeholder="please enter value..." />
                                        </a-form-item>
                                        <a-form-item field="facebookClientToken" :rules="[{required:true,message:'请输入FB客户端令牌'}]" label="FB客户端令牌">
                                            <a-input :style="{width:'200px'}" v-model:model-value="formData.facebookClientToken" placeholder="please enter value..." />
                                        </a-form-item>
                                    </a-space>
                                </div>
                            </div>
                        </section>
                        <section id="adConfig">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    广告配置
                                </div>
                                <a-form-item
                                    v-for="(v,k) in formData.metaConfigs"
                                    :label="'配置项'+k">
                                    <div v-if="v.id==='max_consent'" :style="{width: '100%'}">
                                        <div v-for="(_v, _k) in Object.keys(getAttrAdConfig[v.id])" :style="{width: '100%', display: 'flex', 'margin-bottom': '8px'}">
                                            <a-select
                                                :options="getMetaList"
                                                v-model:model-value="v.id"
                                                :style="{width:'200px'}"
                                                :disabled="_k>0"
                                                placeholder="select or create key..."
                                                allow-clear
                                                allow-create
                                                allow-search
                                            />
                                            <div style="padding: 0 3px"><a-input :default-value="_v" disabled/> </div>
                                            <div style="width: 100%;display: flex">
                                                <a-input :style="{'min-width':'100px'}" v-model:model-value="v.value.privacy" placeholder="please enter value..." />
                                                <div :style="{width: '50px', 'padding-left': '10px'}">
                                                    <a-popconfirm v-if="_k === 0" content="确认删除吗" type="warning" @ok="formData.metaConfigs.splice(k, 1)">
                                                        <a-button
                                                            type="primary"
                                                            class="minus-btn"
                                                            status="danger"
                                                            shape="circle">
                                                            <icon-minus />
                                                        </a-button>
                                                    </a-popconfirm>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else :style="{width: '100%', display: 'flex'}">
                                        <a-select
                                            :options="getMetaList"
                                            v-model:model-value="v.id"
                                            :style="{width:'200px'}"
                                            placeholder="select or create key..."
                                            allow-clear
                                            allow-create
                                            allow-search
                                        />
                                        <span :style="{width: '10px'}"></span>
                                        <div style="width: 100%;display: flex">
                                            <a-input :style="{'min-width':'100px'}" v-model:model-value="v.value" placeholder="please enter value..." />
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.metaConfigs.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </div>
                                </a-form-item>
                                <a-form-item>
                                    <a-button class="button" type="primary" @click="formData.metaConfigs.push({id: '', value: ''})">
                                        <icon-plus :style="{'margin-right': '3px'}"/>
                                        新增配置
                                    </a-button>
                                </a-form-item>
                            </div>
                        </section>
                        <section id="3dtouch">
                            <div :style="{'padding-bottom': '30px'}">
                                <div class="section-area">
                                    3D TOUCH
                                </div>
                                <a-form-item
                                    v-for="(v,k) in formData.touch3d"
                                    :label="'配置项'+k">
                                    <div :style="{width: '100%'}">
                                        <div :style="{width: '100%', display: 'flex', 'margin-bottom': '8px'}">
                                            <div :style="{width: '100%'}">
                                                <a-space :wrap="true">
                                                    <a-form-item :field="`touch3d[${k}].key`" :rules="[{required:true,message:'请输入标识'}]" label="唯一标识">
                                                        <a-input :style="{width:'200px'}" v-model:model-value="v.key" placeholder="please enter value..." />
                                                    </a-form-item>
                                                    <a-form-item :field="`touch3d[${k}].title`" :rules="[{required:true,message:'请输入标题'}]" label="标题">
                                                        <a-input :style="{width:'200px'}" v-model:model-value="v.title" placeholder="please enter value..." />
                                                    </a-form-item>
                                                    <a-form-item field="subtitle" label="副标题">
                                                        <a-input :style="{width:'200px'}" v-model:model-value="v.subtitle" placeholder="please enter value..." />
                                                    </a-form-item>
                                                    <a-form-item field="icon" label="图标">
                                                        <a-select
                                                            :options="touch3dIcons"
                                                            :style="{width:'200px'}"
                                                            v-model:model-value="v.icon"
                                                            placeholder="Please select ..."
                                                            allow-search />
                                                    </a-form-item>
                                                </a-space>
                                                <div :style="{width: '80%'}">
                                                    <a-form-item
                                                        v-for="(_v,_k) in v.opts"
                                                        :label="'选项'+_k">
                                                        <div :style="{width: '100%', display: 'flex'}">
                                                            <a-input :style="{width:'300px'}" v-model:model-value="_v.id" placeholder="please enter key..." />
                                                            <span :style="{width: '10px'}"></span>
                                                            <div style="width: 100%;display: flex">
                                                                <a-input :style="{'min-width':'200px'}" v-model:model-value="_v.value" placeholder="please enter value..." />
                                                                <div :style="{width: '50px', 'padding-left': '10px', 'line-height': '32px'}">
                                                                    <a-space>
                                                                        <a-button
                                                                            v-if="v.opts.length - 1 === _k"
                                                                            type="primary"
                                                                            class="minus-btn"
                                                                            shape="circle"
                                                                            @click="v.opts.push({id: '', value: ''})">
                                                                            <icon-plus/>
                                                                        </a-button>
                                                                        <a-popconfirm content="确认删除吗" type="warning" @ok="v.opts.splice(_k, 1)">
                                                                            <a-button
                                                                                v-if="v.opts.length > 1"
                                                                                class="minus-btn"
                                                                                shape="circle">
                                                                                <icon-close />
                                                                            </a-button>
                                                                        </a-popconfirm>
                                                                    </a-space>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </a-form-item>
                                                </div>
                                            </div>
                                            <div :style="{width: '50px', 'padding-left': '10px'}">
                                                <a-popconfirm content="确认删除吗" type="warning" @ok="formData.touch3d.splice(k, 1)">
                                                    <a-button
                                                        type="primary"
                                                        class="minus-btn"
                                                        status="danger"
                                                        shape="circle">
                                                        <icon-minus />
                                                    </a-button>
                                                </a-popconfirm>
                                            </div>
                                        </div>
                                    </div>
                                </a-form-item>
                                <a-form-item v-if="formData.touch3d.length < 4">
                                    <a-button class="button" type="primary" @click="formData.touch3d.push({key: '', title: '', subtitle: '', icon:'', opts:[{id: '', value: ''}]})">
                                        <icon-plus :style="{'margin-right': '3px'}"/>
                                        新增配置
                                    </a-button>
                                </a-form-item>
                            </div>
                        </section>
<!--                    </a-spin>-->
                </a-form>
            </div>
        </div>
        <complete-log-modal ref="completeLogRef" />
        <a-drawer
            :style="{'z-index': 1002}"
            popup-container="#pack-body"
            :visible="drawerTab !== ''"
            :width="'80%'"
            @cancel="closeDraw"
            :footer="false"
        >
            <template #title> {{drawerName}} </template>
            <div v-if="drawerTab === 'task'">
                <a-tabs type="card-gutter" size="medium" @change="taskTabChange">
                    <a-tab-pane key="queue" title="打包队列">
                        <a-table :columns="taskColumns" :data="runWaitList">
                            <template #status="{record}">
                                <a-space>
                                    <a-tag :color="record.status === '打包中' ? 'red' : 'green'">{{record.status}}</a-tag>
                                    <a-tooltip v-if="record.status === '打包中'" content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => showLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="取消任务">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => cancelPack(ev, record)">
                                            <template #icon>
                                                <icon-close />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                    </a-tab-pane>
                    <a-tab-pane key="completed" title="已完成">
                        <a-table :loading="completeList.loading" :columns="taskColumns" :data="completeList.list" :pagination="false">
                            <template #status="{record}">
                                <a-space>
                                    <a-tooltip content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => getCompleteLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="再次配置">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => packAgain(ev, record)">
                                            <template #icon>
                                                <icon-refresh />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                        <div class="pagination">
                            <a-pagination :total="completeList.total" show-total @change="(current) => getHistoryLog(1, current, 10)"/>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane key="failed" title="失败">
                        <a-table :loading="failList.loading" :columns="taskColumns" :data="failList.list" :pagination="false">
                            <template #status="{record}">
                                <a-space>
                                    <a-tooltip content="查看日志">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => getCompleteLog(ev, record)">LOG</a-button>
                                    </a-tooltip>
                                    <a-tooltip content="再次配置">
                                        <a-button class="border-mini" type="primary" size="mini" @click="(ev) => packAgain(ev, record)">
                                            <template #icon>
                                                <icon-refresh />
                                            </template>
                                        </a-button>
                                    </a-tooltip>
                                </a-space>
                            </template>
                        </a-table>
                        <div class="pagination">
                            <a-pagination :total="failList.total" show-total @change="(current) => getHistoryLog(2, current, 10)"/>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div v-else-if="drawerTab === 'backup'">
                <div :style="{'text-align': 'end', 'padding': '5px 0 10px 0'}">
                    <a-button type="primary" :loading="backupLoading" @click="(ev) => backupOperate('1')">添加备份</a-button>
                </div>
                <a-table :columns="backupColumns" :data="backupList">
                    <template #optional="{record}">
                        <a-space>
                            <a-tooltip content="复制备份路径">
                                <a-link @click="(ev) => copyText(record.name)">
                                    <template #icon>
                                        <icon-copy size="15"/>
                                    </template>
                                </a-link>
                            </a-tooltip>
                            <a-tooltip v-if="deleteBackup.includes(record.name)" content="正在删除备份">
                                <a-link loading></a-link>
                            </a-tooltip>
                            <a-tooltip v-else content="删除备份">
                                <a-popconfirm content="确认删除吗" type="warning" @ok="backupOperate('3', record.name)">
                                    <a-link>
                                        <template #icon>
                                            <icon-close size="15"/>
                                        </template>
                                    </a-link>
                                </a-popconfirm>
                            </a-tooltip>
                        </a-space>
                    </template>
                </a-table>
            </div>
            <div v-else-if="drawerTab === 'log'">
                <a-textarea v-if="processLog" :style="{height: '650px','overflow-y': 'auto'}" v-model:model-value="processLog" auto-size></a-textarea>
                <a-empty v-else />
            </div>
            <div :style="{height: '100%'}" v-else-if="drawerTab === 'default'">
                <sendDataConfigure ref="configRef"/>
            </div>
        </a-drawer>
        <div class="sider-btn">
            <a-list>
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'task'}" @click="(ev) => drawerTabChange(ev,'task')">
                    任务记录
                </a-list-item>
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'backup'}" @click="(ev) => drawerTabChange(ev,'backup')">
                    备份列表
                </a-list-item>
                <a-list-item class="sider-btn-item" :class="{'side-btn-active': drawerTab === 'default'}" @click="(ev) => drawerTabChange(ev,'default')">
                    default.json
                </a-list-item>
            </a-list>
        </div>
        <a-modal v-model:visible="uploadFileModal.show" :width="500" title-align="start" :title="uploadFileModal.title" :footer="false" @cancel="cancelUpload">
            <a-spin :loading="uploadFileModal.loading" :style="{width: '100%'}">
                <div :style="{'text-align': 'center'}">
                    <div>
                        <a-upload
                            draggable
                            :custom-request="handleUpload"
                            :show-file-list="false"
                            :accept="uploadFileModal.accept"
                        >
                        </a-upload>
                        <span style="padding: 5px 0;font-size: 11px;color: #ff5722">{{uploadFileModal.tips}}</span>
                    </div>
                </div>
            </a-spin>
        </a-modal>
        <a-modal v-model:visible="contentShowModal.show" :width="700" :title="contentShowModal.title" title-align="start" :footer="false" @cancel="hideContentShow">
            <a-textarea ref="logRef" v-if="contentShowModal.type === 'log'" spellcheck="false" :style="{height: '500px', 'overflow-y': 'auto'}" v-model:model-value="getRunLog"></a-textarea>
            <a-spin v-else :style="{width: '100%'}" :loading="contentShowModal.loading">
                <div v-if="contentShowModal.content">
                    <a-textarea spellcheck="false" :style="{height: '500px', 'overflow-y': 'auto'}" v-model:model-value="contentShowModal.content" auto-size></a-textarea>
                </div>
                <a-empty v-else-if="!contentShowModal.loading" />
            </a-spin>
        </a-modal>
        <a-modal
            width="500px"
            :visible="true" v-for="(v,k) in noticeConfirm"
            :key="k" title="通知"
            title-align="start"
            :hide-cancel="true"
            @cancel="noticeConfirm.splice(k ,1)"
            @ok="noticeConfirm.splice(k ,1)">
            <div :style="{'text-align': 'center'}">
                {{v}}
            </div>
        </a-modal>
        <a-modal
            v-model:visible="digitCode.show" :width="500"
            :title="'请输入账号 ' + digitCode.appleId +' 的两部验证码'"
            :mask-closable="false"
            :hide-cancel="true"
            :closable="false"
            :esc-to-close="false"
            :footer="false"
            title-align="start"
        >
            <a-input v-model:model-value="digitCode.content" placeholder="请输入内容"/>
            <p :style="{'text-align': 'end', 'padding-top': '15px'}">
                <a-button @click="handlerDigitCode" type="primary">确定</a-button>
            </p>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import CompleteLogModal from "@/views/operation/pack/packCenter/components/completeLogModal.vue";
import sendDataConfigure from "@/views/operation/pack/packData/components/sendDataConfigure.vue";
import {
    adDefaultConfigs,
    backupColumns,
    iosConfigsGroup,
    iosLangOptions,
    packTypeOptions,
    taskColumns,
    touch3dIcons
} from "@/views/operation/pack/packCenter/components/listJson";
import {computed, nextTick, onMounted, reactive, ref} from "vue";
import {useEventBus} from '@vueuse/core'
import {useUserStore} from '@/store';
import {Message} from "@arco-design/web-vue";
import {
  getAppleAccounts,
  getAppVersionList,
  getPackConfig,
  getPackHistory,
  getPackHistoryList, getSdkTypes, getUnityVersions,
  savePackConfig,
  savePackHistory
} from "@/api/marketing/api";
import {LocalStorageEventBus} from "@/types/event-bus";
import {formatTimestamp} from "@/utils/dateUtil";

const userStore = useUserStore();
const localStorageEventBus = useEventBus(LocalStorageEventBus)
const emits = defineEmits(['sendMessage', 'updateLoading']);

const loading = ref(false)
const configLoading = ref(false)
const accountsLoading = ref(false)
const sdkTypesLoading = ref(false)
const unityVersionsLoading = ref(false)
const saveConfigLoading = ref(false)
const jsonVersionLoading = ref(false)
const sdkLoading = ref(false)
const iconLoading = ref(false)
const imagesLoading = ref(false)
const backupLoading = ref(false)
const packServerLoading = ref(false)
const uploadServer = ref('')
const configIndex = ref(0)
const jsonVersionOptions= ref<Array<object>>([])
const configRef = ref()
const completeLogRef = ref()
const drawerTab = ref('')
const drawerName = ref('')
const logRef = ref()
const processLog = ref('')
const runningList = ref<Array<any>>([])
const waitingList = ref<Array<any>>([])
const backupList = ref<Array<any>>([])
const sdkOptions= ref<Array<string>>([])
const noticeConfirm = ref<Array<any>>([])
const packServerOptions = ref<Array<any>>([])
const sdkContent= ref({})
const uploadFileModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    method: '',
    tips: '',
    accept: ''
})
const contentShowModal = reactive({
    loading: false,
    show: false,
    type: '',
    title: '',
    content: ''
})
const completeList = reactive({
    loading: false,
    total: 0,
    list: []
})
const failList = reactive({
    loading: false,
    total: 0,
    list: []
})
const digitCode = reactive({
    show: false,
    appleId: "",
    content: "",
    server: "***********"
})
const accounts = ref([])
const sdkTypes = ref([])
const unityVersions = ref([])
const osName = 'ios'
const formRef = ref()
const formData = reactive({
  dataVersion: '',
  packType: 'unity',
  url: '',
  branch: 'master',
  name: '',
  appleAccount: '',
  appStoreId: '',
  versionCode: 1,
  versionName: '1.0.0',
  metaConfigs: [],
  sdkTag: '',
  sdkTypes: [],
  sdkUrl: 'git@*********:iossdk/IvyiOSSdk.git',
  screenOrientation: 'portrait',
  ivycoreUrl: '',
  ivycoreBranch: '',
  iap: true,
  gameCenter: false,
  signInApple: true,
  unityVersion: '',
  fbConfig: true,
  facebookAppId: '',
  facebookAppName: '',
  facebookClientToken: '',
  names: [{id: '', value: ''}],
  iconUploaded: 0,
  gsUploaded: 0,
  imagesUploaded: 0,
  touch3d: []
});
const deleteBackup = ref<Array<string>>([])
const iconUrlContent = ref({})
const imagesUrlContent = ref([])
const imagesReqId = ref()

const configSelectChange = (hash: string) => {
    iosConfigsGroup.forEach((v, k) => {
        if (hash == v.link) {
            configIndex.value = k
        }
    })
}

const handlerDigitCode = () => {
    if (digitCode.content.trim().length === 0) {
        Message.error("验证码长度为零，请重新检查！")
    } else {
        sendMessage(JSON.stringify({
            method: "talkDigitCode",
            osName: osName,
            payload: {
                appleId: digitCode.appleId,
                digitCode: digitCode.content
            },
            to: digitCode.server
        }))
    }
}

const getHistoryLog = (pack_result: number, current: number = 1, page_size: number = 10) => {
    if (pack_result == 1) {
        completeList.loading = true
    } else {
        failList.loading = true
    }
    getPackHistoryList({app_id: formData.appId, current: current, page_size: page_size, pack_result: pack_result}).then(res => {
        if (pack_result == 1) {
            completeList.total = res.total
            completeList.list = []
            res.items.forEach(v => {
                completeList.list.push({
                    id: v.id,
                    time: formatTimestamp(v.createTime),
                    name: `[${v.packType} - ${v.appType}] ${v.appId}:${formData.package}:${v.appOpts.versionName}`,
                    status: '成功',
                    resultType: v.appType,
                    buildType: v.packType,
                    buildVersion: v.dataVersion,
                    appId: v.appId,
                    packageName: formData.package,
                    versionName: v.appOpts.versionName,
                    creator: v.creator,
                    sdkVersion: v.buildOpts.sdkTag
                })
            })
        } else {
            failList.total = res.total;
            failList.list = []
            res.items.forEach(v => {
                failList.list.push({
                    id: v.id,
                    time: formatTimestamp(v.createTime),
                    name: `[${v.packType} - ${v.appType}] ${v.appId}:${formData.package}:${v.appOpts.versionName}`,
                    status: '失败',
                    resultType: v.appType,
                    buildType: v.packType,
                    buildVersion: v.dataVersion,
                    appId: v.appId,
                    packageName: formData.package,
                    versionName: v.appOpts.versionName,
                    creator: v.creator,
                    sdkVersion: v.buildOpts.sdkTag
                })
            })
        }
    }).catch(e => {
        console.log(e)
    }).finally(() => {
        if (pack_result == 1) {
            completeList.loading = false
        } else {
            failList.loading = false
        }
    })
}

const taskTabChange = (tab: string|number) => {
    if (tab === 'completed') {
        getHistoryLog(1)
    } else if (tab === 'failed') {
        getHistoryLog(2)
    }
}

const packAgain = (event: MouseEvent, packRecord: object) => {
    drawerTab.value = ''
    configLoading.value = true
    getPackHistory({id: packRecord.id}).then(res => {
        formatFormData(res)
        Message.success('已恢复至历史配置')
    }).catch(e => {
        console.error(e)
    }).finally(() => {
        configLoading.value = false
    })
}

const getCompleteLog = (event: MouseEvent, packRecord: object) => {
    if (!formData.appId) {
        Message.warning("请选择包名")
        return
    }

    completeLogRef.value.openModal()
    sendMessage(JSON.stringify({
        method: "getCompleteLog",
        osName: osName,
        payload: {
            appId: packRecord.appId,
            buildType: packRecord.buildType,
            packageName: packRecord.packageName,
            logName: `${packRecord.id}`
        }
    }));
}

const closeDraw = () => {
    loadDataVersion(formData.appId)
    drawerTab.value = ''
}

const selectedRunTask = ref()
const showLog = (event: MouseEvent, packRecord: object) => {
    selectedRunTask.value = packRecord
    contentShowModal.loading = false
    contentShowModal.show = true
    contentShowModal.type = 'log'
    contentShowModal.title = "日志"
    contentShowModal.content = ""
}

const getRunLog = computed(() => {
    let log = ''
    runningList.value.some(v => {
        if (v.id === selectedRunTask.value.id) {
            log = v.log
            return true
        }
    })
    nextTick(() => {
        if (logRef.value) {
            logRef.value.textareaRef.scrollTop = logRef.value.textareaRef.scrollHeight;
        }
    })
    return log
})

const cancelPack = (event: MouseEvent, packRecord: object) => {
    sendMessage(JSON.stringify({
        method: "cancelTask",
        osName: osName,
        payload: {
            robotId: packRecord.id,
            robotTime: packRecord.time,
            packageName: packRecord.packageName,
            confirmPackageName: packRecord.packageName,
            confirmTime: formatTimestamp(Date.now()),
            taskStatus: packRecord.status === '打包中' ? 'run' : 'wait'
        },
        to: packRecord.server
    }));
}

const runWaitList = computed(() => {
    let list = []
    list.push(...runningList.value, ...waitingList.value)
    return list
})

const backupOperate = (operate_type: string, removeDir: string = '') => {
    if (operate_type == "1") {
        if (!formData.url) {
            Message.warning('请填写项目地址')
            return
        }
        if (!formData.versionName) {
            Message.warning('请填写版本号')
            return
        }
    }

    if (operate_type == "3" && removeDir == '') {
        Message.warning('删除的备份为空')
        return
    }


    if (operate_type == "1") {
        backupLoading.value = true;
    } else if (operate_type == "3") {
        deleteBackup.value.push(removeDir)
    }
    sendMessage(JSON.stringify({
        method: "backup",
        osName: osName,
        payload: {
            action: operate_type,
            name: formData.appId,
            buildType: formData.packType,
            url: formData.url,
            branch: formData.branch,
            versionName: formData.versionName,
            removeDir: removeDir
        }
    }))
}

const copyText = (content: string) => {
    let inputElement = document.createElement('input')
    inputElement.value = content;
    document.body.appendChild(inputElement);
    inputElement .select(); //选中文本
    document.execCommand("copy"); //执行浏览器复制命令
    inputElement.remove();
    Message.success("复制成功");
}

const refreshPage = (appData: object) => {
    accountsLoading.value = true
    getAppleAccounts().then(res => {
      accounts.value = res
    }).finally(() => {
      accountsLoading.value = false
    })
    // sendMessage(JSON.stringify({
    //     method: 'GetAccounts',
    //     osName: osName,
    //     payload: {}
    // }))

    unityVersionsLoading.value = true
    getUnityVersions().then(res => {
      console.log(res)
      unityVersions.value = res
    }).finally(() => {
      unityVersionsLoading.value = false
    })
    // sendMessage(JSON.stringify({
    //     method: 'GetUnityVersions',
    //     osName: osName,
    //     payload: {}
    // }))

    formData.appId = appData?.code || ''
    formData.package = appData?.package || ''
    formData.name = appData?.name || ''
    loadDataVersion(formData.appId)
    loadPackInfo()
}

const drawerTabChange = (event: MouseEvent, tabName: string, showAdd :boolean = false) => {
    if (!formData.appId) {
        Message.warning("请选择应用")
        return
    }

    drawerTab.value = tabName;
    switch (tabName) {
        case 'task':
            drawerName.value = '任务记录'
            break
        case 'backup':
            drawerName.value = '备份列表'
            break
        case 'default':
            drawerName.value = 'default.json'
            nextTick(() => {
                if (configRef.value) {
                    configRef.value.refresh(formData.appId, showAdd);
                } else {
                    console.error('configRef is undefined');
                }
            });
            break
    }
}

const refreshConfig = () => {
    if (!formData.appId) {
        Message.warning("请选择包名")
        return true
    }

    loadPackInfo()
}

const loadPackInfo = () => {
    if (!formData.appId) {
        Message.warning('请选择打包应用')
        return
    }

    formData.packServer = ''
    configLoading.value = true
    getPackConfig({app_id: formData.appId}).then(res => {
        formatFormData(res)
        GetSdkTypes()
        loadIconContents()
        loadImagesContents()
        loadSdkList()
        loadPackServer()
    }).catch(e => {
        console.log(e)
    }).finally(() => {
        configLoading.value = false
    })

    backupOperate("2")
}

const GetSdkTypes = () => {
    sdkTypesLoading.value = true
    getSdkTypes(formData.sdkUrl).then(res => {
      sdkTypes.value = res
      let tmp = []
      let lowerTypes = formData.sdkTypes.map(v => v.toLowerCase())
      sdkTypes.value.forEach(v => {
        if (lowerTypes.includes(v.toLowerCase())) {
          tmp.push(v)
        }
      })
      formData.sdkTypes = tmp
    }).finally(() => {
      sdkTypesLoading.value = false
    })
    // sendMessage(JSON.stringify({
    //     method: 'GetSdkTypes',
    //     osName: osName,
    //     payload: {
    //         sdkUrl: formData.sdkUrl
    //     }
    // }))
}

const loadSdkList = () => {
    sdkLoading.value = true
    sendMessage(JSON.stringify({
        method: 'GetSdkVerList',
        osName: osName,
        payload: {
            sdkUrl: formData.sdkUrl
        }
    }))
}

const loadPackServer = () => {
    if (!formData.appleAccount) {
        return
    }
    packServerLoading.value = true
    sendMessage(JSON.stringify({
        method: 'GetPackServerList',
        osName: osName,
        payload: {
            appleAccount: formData.appleAccount
        }
    }))
}

const formatFormData = (res) => {
    formData.dataVersion = res?.dataVersion || '';
    formData.packType = res?.packType || 'unity';
    formData.url = res?.gitOpts?.url || '';
    formData.branch = res?.gitOpts?.branch || 'master';
    formData.name = res?.appOpts?.name || formData.name;
    formData.appleAccount = res?.appOpts?.appleAccount || '';
    formData.appStoreId = res?.appOpts?.appStoreId || '';
    formData.versionCode = res?.appOpts?.versionCode || 1;
    formData.versionName = res?.appOpts?.versionName || '1.0.0';
    formData.metaConfigs = res?.metadataOpts || [];
    formData.sdkTag = res?.buildOpts?.sdkTag || '';
    formData.sdkTypes = res?.buildOpts?.sdkType || [];
    formData.sdkUrl = res?.buildOpts?.sdkUrl || 'git@*********:iossdk/IvyiOSSdk.git';
    formData.screenOrientation = res?.buildOpts?.screenOrientation || 'portrait'
    formData.ivycoreUrl = res?.buildOpts?.ivycoreUrl || '';
    formData.ivycoreBranch = res?.buildOpts?.ivycoreBranch || '';
    formData.iap = res?.buildOpts?.hasOwnProperty('iap') ? res.buildOpts.iap : true;
    formData.gameCenter = res?.buildOpts?.gameCenter || false
    formData.signInApple = res?.buildOpts?.hasOwnProperty('signInApple') ? res.buildOpts.signInApple : true
    formData.unityVersion = res?.buildOpts?.unityVersion || ''
    formData.fbConfig = res?.buildOpts?.hasOwnProperty('fbConfig') ? res.buildOpts.fbConfig : true
    formData.facebookAppId = res?.buildOpts?.facebookAppId || ''
    formData.facebookAppName = res?.buildOpts?.facebookAppName || ''
    formData.facebookClientToken = res?.buildOpts?.facebookClientToken || ''
    formData.names = res?.names || [{id: '', value: ''}];
    formData.iconUploaded = res?.filesUploaded?.iconUploaded || 0;
    formData.gsUploaded = res?.filesUploaded?.gsUploaded || 0;
    formData.imagesUploaded = res?.filesUploaded?.imagesUploaded || 0;
    formData.touch3d = res?.buildOpts?.touch3d || []
}

const loadIconContents = () => {
    if (formData.iconUploaded) {
        iconLoading.value = true
        sendMessage(JSON.stringify({
            method: 'GetIconUrlContent',
            osName: osName,
            payload: {
                appId: formData.appId
            }
        }))
    }
}

const loadImagesContents = () => {
    if (formData.imagesUploaded) {
        imagesLoading.value = true
        imagesReqId.value = Date.now()
        imagesUrlContent.value = []
        sendMessage(JSON.stringify({
            method: 'GetImagesUrlContent',
            osName: osName,
            payload: {
                appId: formData.appId,
                imageReqId: Date.now()
            }
        }))
    }
}

const loadDataVersion = (appId: string) => {
    jsonVersionLoading.value = true
    getAppVersionList({appId: appId}).then(res => {
        jsonVersionOptions.value = [];
        if (res) {
            res.forEach(v => {
                jsonVersionOptions.value.push(
                    {id: v.name, label: v.name}
                );
            })
        }
    }).finally(() => {
        jsonVersionLoading.value = false
    })
}

const uploadPackFile = (upload_type: string) => {
    sendMessage(JSON.stringify({
        method: "uploadServer",
        osName: osName,
        payload: {}
    }))

    uploadFileModal.show = true
    uploadFileModal.type = upload_type
    switch (upload_type) {
        case 'images':
            uploadFileModal.title = '上传图片资源'
            uploadFileModal.method = 'upload_images'
            uploadFileModal.accept = '.zip'
            uploadFileModal.tips = '请将图片资源目录压缩到zip文件后上传'
            break
        case 'google_services':
            uploadFileModal.title = '上传GoogleService-Info.plist'
            uploadFileModal.method = 'upload_google_services'
            uploadFileModal.accept = '.plist'
            uploadFileModal.tips = ''
            break
        case 'icon':
            uploadFileModal.title = '上传icon'
            uploadFileModal.method = 'upload_icon'
            uploadFileModal.accept = '.png'
            uploadFileModal.tips = ''
            break
    }
}

const cancelUpload = () => {
    uploadFileModal.show = false
    uploadFileModal.type = ''
    uploadFileModal.title = ''
    uploadFileModal.method = ''
    uploadFileModal.accept = ''
    uploadFileModal.tips = ''
}

const handleUpload = (options: any) => {
    const file = options.fileItem.file
    if (!file) return;

    let filename = `tmpFile_${Date.now()}`
    const chunkSize = 1024 * 1024; // 每次发送1MB的数据块
    let start = 0;
    uploadFileModal.loading = true
    const sendNextChunk = () => {
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        const header = {
            to: uploadServer.value,
            method: uploadFileModal.method,
            osName: osName,
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package,
            fileName: filename,
            chunk: Math.floor(start / chunkSize),
            totalChunks: Math.ceil(file.size / chunkSize),
        };

        const reader = new FileReader();
        reader.readAsArrayBuffer(chunk);
        reader.onloadend = async (e) => {
            if (e.target && e.target.result) {
                const data = e.target.result as ArrayBuffer;
                const message = new Blob([JSON.stringify(header), '', new Uint8Array(data)]);
                message.arrayBuffer().then(res => {
                    sendMessage(res);
                    if (end < file.size) {
                        start = end;
                        sendNextChunk(); // 发送下一个数据块
                    }
                })
            }
        };
    };
    sendNextChunk()
}

const changeAccounts = (value: string) => {
    accounts.value.some(v => {
        if (v.appleAccount === value) {
            formData.appstore = v.appstore
            formData.development = v.development
            formData.teamId = v.teamId
            return true
        }
    })
}

const chageSdkUrl = () => {
    loadSdkList()
    GetSdkTypes()
}

const showContentModal = (show_type: string) => {
    contentShowModal.show = true
    contentShowModal.type = show_type
    contentShowModal.loading = true
    switch (show_type) {
        case 'google_services':
            contentShowModal.title = '【GoogleService-Info.plist】的详情'
            getGoogleServices()
            break
    }
}

const getGoogleServices = () => {
    sendMessage(JSON.stringify({
        method: "GetGoogleServices",
        osName: osName,
        payload: {
            buildType: formData.packType,
            appId: formData.appId,
            packageName: formData.package
        }
    }))
}

const hideContentShow = () => {
    contentShowModal.show = false
    contentShowModal.loading = false
    contentShowModal.type = ''
    contentShowModal.title = ''
    contentShowModal.content = ''
}

const saveConfig = () => {
    if (saveConfigLoading.value) {
        Message.warning("当前有正在保存的任务，请稍后再试")
        return true
    }

    if (!formData.appId) {
        Message.warning("请选择包名")
        return true
    }

    let isValid = false
    formRef.value.validate((errors: any) => {
        isValid = !errors;
    }).then(() => {
        if (!isValid) {
            Message.warning("请检查错误")
            return
        }

        //检查3Dtouch配置
        let keys = formData.touch3d.map(item => item.key)
        if (keys.length !== new Set(keys).size) {
            Message.warning("3D Touch 标识有重复")
            return true
        }


        saveConfigLoading.value = true
        sendSave()
    })
}

const sendSave = () => {
    let saveData = getPackConfigSaveFormat()
    savePackConfig(saveData).then(res => {
        if (saveConfigLoading.value) {
            Message.success('保存成功')
        }
    }).catch(e => {
        if (saveConfigLoading.value) {
            Message.error('保存失败', e)
        }
    }).finally(() => {
        if (saveConfigLoading.value) {
            saveConfigLoading.value = false
        }
    })
}

const getMetaList = computed(() => {
    const options = []

    if (adDefaultConfigs && adDefaultConfigs.length) {
        const usedConfigs = []
        if (formData.metaConfigs && formData.metaConfigs.length) {
            formData.metaConfigs.forEach(v => {
                usedConfigs.push(v.id)
            })
        }
        adDefaultConfigs.forEach(v => {
            if (!usedConfigs.includes(v.id)) {
                options.push(v.id)
            }
        })
    }

    return options
})

const changeAllSdkTypes = (value: boolean) => {
    if (value) {
        formData.sdkTypes = sdkTypes.value
    } else {
        formData.sdkTypes = []
    }
}

const getPackConfigSaveFormat = () => {
    return {
        appId: formData.appId,
        dataVersion: formData.dataVersion,
        packType: formData.packType,
        packMode: '',
        cpuLibs: [],
        gitOpts: {
            branch: formData.branch,
            url: formData.url
        },
        appOpts: {
            appleAccount: formData.appleAccount,
            appStoreId: formData.appStoreId,
            codeSigningIdentity: {
                appstore: formData.appstore,
                development: formData.development
            },
            name: formData.name,
            teamId: formData.teamId,
            versionCode: formData.versionCode,
            versionName: formData.versionName
        },
        pluginOpts: {},
        buildOpts: {
            gameCenter: formData.gameCenter,
            iap: formData.iap,
            signInApple: formData.signInApple,
            unityVersion: formData.unityVersion,
            ivycoreUrl: formData.ivycoreUrl,
            ivycoreBranch: formData.ivycoreBranch,
            sdkTag: formData.sdkTag,
            sdkType: formData.sdkTypes,
            sdkUrl: formData.sdkUrl,
            screenOrientation: formData.screenOrientation,
            fbConfig: formData.fbConfig,
            facebookAppId: formData.facebookAppId,
            facebookAppName: formData.facebookAppName,
            facebookClientToken: formData.facebookClientToken,
            touch3d: formData.touch3d
        },
        metadataOpts: formData.metaConfigs,
        removePermissionOpts: [],
        names: formData.names,
        keystoreOpts: {},
        mavenUrl: [],
        globalDependencies: [],
        globalPlugin: [],
        globalBuildFeatures: [],
        globalAaptOpts: [],
        globalNdkOpts: [],
        globalPackagingOpts: [],
        globalLintOpts: [],
        globalCompileOpts: [],
        globalBuild: {},
        appPlugin: [],
        appDependencies: [],
        appBuildFeatures: [],
        appAaptOpts: [],
        appNdkOpts: [],
        appPackagingOpts: [],
        appLintOpts: [],
        appCompileOpts: [],
        appBuild: {},
        appSourcePath: {},
        bundleOpts: [],
        rootPlugins: [],
        filesUploaded: {
            imagesUploaded: formData.imagesUploaded,
            gsUploaded: formData.gsUploaded,
            iconUploaded: formData.iconUploaded
        }
    }
}

const getAttrAdConfig = computed(() => {
    let attrConfigs = {}
    adDefaultConfigs.forEach(v => {
        attrConfigs[v.id] = v.value
    })

    return attrConfigs
})

const sendMessage = (data: any) => {
    emits('sendMessage', data)
}

const updateLoading = (keyName: string, value: boolean) => {
    emits('updateLoading', keyName, value)
}

const delIcon = (img_name: string) => {
  sendMessage(JSON.stringify({
    method: "DelIcon",
    osName: osName,
    payload: {
      appId: formData.appId,
      imgName: img_name
    }
  }))
}

const handleMessage = (message) => {
    switch (message.method) {
        case 'DelIcon':
          if (message.payload) {
            if (message.payload.notify) {
              Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
            } else {
              if (iconUrlContent.value && iconUrlContent.value['name'] === message.payload.imgName) {
                iconUrlContent.value = {}
                formData.iconUploaded = 0
              }

              imagesUrlContent.value = imagesUrlContent.value.filter(item => item.name != message.payload.imgName)
              if (!imagesUrlContent.value.length) {
                formData.imagesUploaded = 0
              }
            }
          }
          break
        case 'taskList':
            runningList.value = []
            if (message.payload.tasks.running) {
                message.payload.tasks.running.forEach(s => {
                    runningList.value.push({
                        id: s.robotId,
                        time: s.createTime,
                        name: `[${s.buildType}] ${s.appId}:${s.packageName}:${s.versionName}`,
                        status: "打包中",
                        packageName: s.packageName,
                        appId: s.appId,
                        log: s.log,
                        sdkVersion: s.sdkVersion,
                        buildVersion: s.jsonVersion,
                        creator: s.creator,
                        server: s.server
                    })

                    if (s.creator === userStore.name && !digitCode.show && s.buildStatus === 'Pause') {
                        digitCode.show = true
                        digitCode.content = ""
                        digitCode.appleId = s.appleId
                        digitCode.server = s.server
                    }
                })
            }

            waitingList.value = []
            if (message.payload.tasks) {
                message.payload.tasks.waiting.forEach(s => {
                    waitingList.value.push({
                        id: s.taskId,
                        time: s.createTime,
                        name: `[${s.buildType}] ${s.appId}:${s.package}:${s.versionName}`,
                        status: "排队中",
                        packageName: s.packageName,
                        appId: s.appId,
                        sdkVersion: s.sdkVersion,
                        buildVersion: s.jsonVersion,
                        creator: s.creator
                    });
                });
            }
            break
        case 'talkDigitCode':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    digitCode.show = false
                    digitCode.content = ''
                    digitCode.appleId = ''
                    digitCode.server = ''
                }
            }
            break
        case 'cancelTask':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("任务取消成功")
                }
            }
            break
        case 'uploadServer':
            uploadServer.value = message.payload ? message.payload.server : ''
            break
        case 'UploadGoogleServices':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.gsUploaded = 1
                    sendSave()
                }
            }
            break
        case 'UploadIcon':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.iconUploaded = 1
                    sendSave()
                    loadIconContents()
                }
            }
            break
        case 'UploadImages':
            if (message.payload) {
                if (message.payload.notify) {
                    uploadFileModal.loading = false
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    Message.success("文件上传完成")
                    uploadFileModal.loading = false
                    uploadFileModal.show = false
                    formData.imagesUploaded = 1
                    sendSave()
                    loadImagesContents()
                }
            }
            break
        case 'GetAccounts':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    accounts.value = message.payload.accounts

                    if (formData.appleAccount) {
                        accounts.value.some(v => {
                            if (v.appleAccount === formData.appleAccount) {
                                formData.appstore = v.appstore
                                formData.development = v.development
                                formData.teamId = v.teamId
                                return true
                            }
                        })
                    }
                }
            }
            accountsLoading.value = false
            break
        case 'GetSdkTypes':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    sdkTypes.value = message.payload.sdkTypes
                    let tmp = []
                    let lowerTypes = formData.sdkTypes.map(v => v.toLowerCase())
                    sdkTypes.value.forEach(v => {
                        if (lowerTypes.includes(v.toLowerCase())) {
                            tmp.push(v)
                        }
                    })
                    formData.sdkTypes = tmp
                }
            }
            sdkTypesLoading.value = false
            break
        case 'GetPackServerList':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    packServerOptions.value = [{id: '', label: '自动选择'}]
                    packServerOptions.value.push(...message.payload.servers)
                }
            }
            packServerLoading.value = false
            break
        case 'GetUnityVersions':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    unityVersions.value = message.payload.unityVersions
                }
            }
            unityVersionsLoading.value = false
            break
        case 'getCompleteLog':
            let logStr = 'error log of this build task maybe have been delete!'
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    logStr = message.payload.notify.message ? message.payload.notify.message : logStr
                } else {
                    logStr = message.payload.log ? message.payload.log : logStr
                }
            }
            completeLogRef.value.setLogContent(logStr)
            break;
        case 'GetIconUrlContent':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    iconUrlContent.value = {}
                } else {
                    iconUrlContent.value = message.payload.imageUrlContent
                }
            }

            iconLoading.value = false
            break
        case 'GetImagesUrlContent':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    imagesLoading.value = false
                } else {
                    if (message.payload.imageReqId === imagesReqId.value) {
                        imagesUrlContent.value.push(message.payload.imageUrlContent)

                        if (imagesUrlContent.value.length === message.payload.imagesTotal) {
                            imagesLoading.value = false
                        }
                    }
                }
            }

            break
        case 'GetGoogleServices':
            contentShowModal.loading = false
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                    if (contentShowModal.type === 'google_services') {
                        contentShowModal.content = '获取失败'
                    }
                } else {
                    if (contentShowModal.type === 'google_services') {
                        contentShowModal.content = message.payload.GSContent
                    }
                }
            }
            break
        case 'GetSdkVerList':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    sdkOptions.value = JSON.parse(message.payload.sdkVerList)

                    sdkContent.value = {}
                }
            }
            sdkLoading.value = false
            break
        case 'GetSdkContent':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    sdkContent.value = JSON.parse(message.payload.sdkContent)
                    console.log(sdkContent.value)
                }
            }
            sdkTypesLoading.value = false
            break
        case 'OpenXcode':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    noticeConfirm.value.push(`${message.payload.packageName} Xcode工程打开成功，请到打包机器${message.from}上查看`)
                }
            }
            updateLoading('openXcodeLoading', false)
            break
        case 'SettingApplyOnly':
            if (message.payload) {
                if (message.payload.notify) {
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    if (message.payload.action) {
                        if (message.payload.action === "build_result") {
                            if (message.payload.buildResult === "success") {
                                noticeConfirm.value.push(`${message.payload.packageName} 配置成功，请到打包机器${message.from}上查看`)
                            } else {
                                noticeConfirm.value.push(`${message.payload.packageName} 配置失败: 错误：${message.payload.reason}, 请查看日志排查错误`)
                            }
                            getHistoryLog(1)
                            getHistoryLog(2)
                        } else if (message.payload.action === "add_task") {
                            if (message.payload.notify) {
                                noticeConfirm.value.push(`${message.payload.packageName} 配置失败` + ":" + message.payload.notify.message)
                            } else {
                                Message.success(message.payload.message)
                            }
                        }
                    }
                }
            }
            updateLoading('applySettingLoading', false)
            break
        case "backup":
            if (message.payload) {
                if (message.payload.notify) {
                    if (message.payload.action == 1) {
                        backupLoading.value = false;
                    }
                    Message.error(message.payload.notify.title + ":" + message.payload.notify.message)
                } else {
                    backupList.value = message.payload.list;
                    backupList.value.reverse()
                    if (message.payload.action == "1") {
                        backupLoading.value = false;
                        Message.success("成功添加备份");
                    } else if (message.payload.action == "3") {
                        let i = deleteBackup.value.indexOf(message.payload.removeDir)
                        if (i > -1) {
                            deleteBackup.value.splice(i, 1)
                        }

                        Message.success("成功删除备份");
                    }
                }
            }
            break
    }
}

const iosFuncExec = (event: MouseEvent, funcName: string) => {
    switch (funcName) {
        case 'applySetting':
            applySetting()
            break;
        case 'openXcode':
            openXcode()
            break;
        default:
            console.error("unknown function " + funcName)
            break;
    }
}

const applySetting = () => {
    let isValid = false
    formRef.value.validate((errors: any) => {
        isValid = !errors;
    }).then(() => {
        if (!isValid) {
            Message.warning("请检查错误")
            return
        }

        if (accountsLoading.value || sdkTypesLoading.value || unityVersionsLoading.value) {
            Message.warning("请等待页面配置加载完成")
            return
        }

        if (!formData.metaConfigs.length) {
            Message.warning("检测到未配置metadata，请配置")
            return
        }

        //将配置组合成文件内容 保存记录到打包历史中
        let saveData = getPackConfigSaveFormat()
        saveData.appType = ''
        // 将记录id作为任务id 并将文件内容发送至打包后台进行打包
        updateLoading('applySettingLoading', true)

        savePackHistory(saveData).then(res => {
            let payload = getPayload()
            payload['action'] = 'SettingApplyOnly';
            payload['taskId'] = `${res.code}`;
            payload['robotId'] = `${res.code}`;
            payload['sdkVersion'] = formData.sdkTag;
            payload['versionCode'] = formData.versionCode;
            payload['versionName'] = formData.versionName;
            payload['creator'] =  userStore.name;
            payload['createTime'] = formatTimestamp(Date.now());
            sendMessage(JSON.stringify({
                method: 'SettingApplyOnly',
                osName: osName,
                payload: payload,
                to: formData.packServer
            }))
        }).catch(e => {
            Message.error(`打包任务添加失败${e.message}`)
            updateLoading('applySettingLoading', false)
        }).finally(() => {

        })
    })
}

const openXcode = () => {
    updateLoading('openXcodeLoading', true)
    //查询该应用最近一次打包机器，在该打包机器上打开xcode工程
    getPackHistoryList({app_id: formData.appId, current: 1, page_size: 1, pack_result: '1,2'}).then(res => {
        if (res.items.length === 0 || res.items[0].server === '') {
            Message.error("未找到配置服务器，请先完成配置")
            updateLoading('openXcodeLoading', false)
        } else {
            sendMessage(JSON.stringify({
                method: 'OpenXcode',
                osName: osName,
                payload: getPayload(),
                to: res.items[0].server
            }))
        }
    }).catch(e => {
        console.log(e)
        updateLoading('openXcodeLoading', false)
    }).finally(() => {
    })
}

const getPayload = () => {
    return {
        jsonVersion: formData.dataVersion,
        appId: formData.appId,
        package: formData.package,
        packageName: `${formData.appId}_${formData.package}`,
        buildType: formData.packType,
        appleAccount: formData.appleAccount,
        appJson: JSON.stringify(formatAppJson())
    }
}

const formatAppJson = () => {
    let appJson = {};
    appJson['appid'] = formData.appId;
    appJson['apple_account'] = formData.appleAccount;
    appJson['appstoreid'] = formData.appStoreId;
    appJson['codesigning_identity'] = {
        appstore: formData.appstore,
        development: formData.development
    };
    appJson['gamecenter'] = formData.gameCenter;
    appJson['git'] = {
        branch: formData.branch,
        url: formData.url
    };
    appJson['iap'] = formData.iap;
    appJson['main_path'] = formData.packType === 'unity' ? 'ios' : 'proj.ios_mac';
    appJson['name'] = formData.name;
    appJson['screen_orientation'] = formData.screenOrientation;
    appJson['sdk_tag'] = formData.sdkTag;
    appJson['sdk_type'] = formData.sdkTypes.join(',');
    appJson['sdk_url'] = formData.sdkUrl;
    appJson['signInApple'] = formData.signInApple;
    appJson['team_id'] = formData.teamId;
    appJson['versionCode'] = formData.versionCode;
    appJson['versionName'] = formData.versionName;

    if (formData.dingMsg && formData.dingWebhook) {
        appJson['dingMsg'] = formData.dingMsg;
        appJson['dingWebhook'] = formData.dingWebhook;
    }

    if (formData.fbConfig) {
        appJson['facebook'] = {
            facebook_app_id: formData.facebookAppId,
            facebook_app_name: formData.facebookAppName,
            facebook_client_token: formData.facebookClientToken
        }
    }

    if (formData.ivycoreUrl && formData.ivycoreBranch) {
        appJson['ivycore'] = {
            branch: formData.ivycoreBranch,
            git: formData.ivycoreUrl
        }
    }

    if (formData.metaConfigs) {
        formData.metaConfigs.forEach(v => {
            if (v['id'] && v["value"]) {
                appJson[v['id']] = v["value"]
            }
        })
    }

    if (formData.names && formData.names.length) {
        let names = {}
        formData.names.forEach(v => {
            if (v.id && v.value) {
                names[v.id] = v.value;
            }
        })
        if (Object.keys(names).length) {
            appJson['local_name'] = names;
        }
    }

    if (formData.packType === 'unity') {
        appJson['unity_version'] = formData.unityVersion;
    }

    if (formData.touch3d && formData.touch3d.length) {
        let touch3dItems = []
        formData.touch3d.forEach(v => {
            let item = {key: v.key, title: v.title}
            if (v.subtitle) {
                item['subtitle'] = v.subtitle
            }
            if (v.icon) {
                item['icon'] = v.icon
            }

            let opts = {}
            v.opts.forEach(val => {
                if (val.id && val.value) {
                    opts[val.id] = val.value
                }
            })
            if (Object.keys(opts).length) {
                item['opts'] = opts
            }
            touch3dItems.push(item)
        })

        if (touch3dItems.length) {
            appJson['3dtouch'] = touch3dItems
        }
    }


    return appJson
}

defineExpose({
    refreshPage,
    handleMessage,
    iosFuncExec
})
</script>

<style scoped lang="less">
.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
}

.cross-content {
    width: 100%;
    height: 100%;
    display: flex;

    .minus-btn{
        width: 15px;
        height: 15px;
        margin-top: calc(50% - 8px);
    }

    .group-box {
        min-width: 160px;
        height: 100%;
        border-right: 1px solid var(--color-secondary);
        overflow-x: hidden;
        overflow-y: auto;

        :deep(.arco-anchor-link-item) {
            .arco-anchor-link{
                font-size: 16px;
                padding: 5px 8px;
            }
        }
    }

    .active {

    }

    .group-detail {
        padding: 0 16px;
        height: 100%;
        width: 100%;

        .remark {
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: var(--tant-border-radius-medium);
            padding: 10px 15px;
            position: relative;
            margin-top: 15px;
            margin-bottom: 8px;
            width: 100%;
            background-color: var(--tant-fill-color-fill1-2);

            .btn-icon {
                position: absolute;
                right: 12px;
                top: 8px;
                cursor: pointer;
            }
        }

        .section-area {
            font-size: 18px;
            font-weight: 400;
            padding-bottom: 15px
        }

        .maven-url {
            width: 100%;
            :deep(.arco-space-item) {
                width: 100%;
            }
        }

        :deep(.arco-affix) {
            z-index: 100;
        }
        :deep(.arco-transfer-view) {
            width: 270px;
            height: 330px;
        }
    }

    :deep(#sdkTypes) {
        .arco-form-item-content-flex {
            display: block;
        }
        .arco-checkbox-group {
            line-height: 2;
            .arco-checkbox{
                width: 170px;
            }
        }
    }

    :deep(#iconUploaded) {
        .arco-form-item-content{
            display: block;
        }
    }
    :deep(#imagesUploaded) {
        .arco-form-item-content{
            display: block;
        }
    }
}
.sider-btn {
    position: fixed;
    top: 145px;
    right: 0;
    z-index: 300;
    cursor: pointer;

    :deep(.arco-list-item) {
        padding: 10px 0 !important;
    }

    .sider-btn-item{
        writing-mode: vertical-rl;
    }

    .side-btn-active{
        background-color: rgb(143, 184, 243) !important;
        color: #fff !important;
    }
}
.create-condition{
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    color: rgb(var(--primary-6));
    font-size: 14px;
    line-height: 36px;
    text-align: left;
    background-color: var(--color-bg-popup);
    cursor: pointer;
}
.icon-del-btn{
  display: none;
  width: 100%;
  padding: 10px;
  cursor: pointer;
  position: absolute;
  text-align: end;
  color: #fff;
  background: linear-gradient(to top, transparent, #454545d1);
  top: 12px;
}
.icon-box{
  text-align: center;
  position: relative;
}
.icon-box:hover .icon-del-btn {
  display: block;
}
</style>
