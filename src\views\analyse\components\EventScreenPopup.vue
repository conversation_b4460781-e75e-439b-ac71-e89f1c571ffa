<template>
  <div>
    <!-- 事件筛选弹窗 -->
    <a-popover v-if="!props.readOnly" trigger="click" position="tl" :popup-visible="popupVisible" style="z-index: 1002;">
      <a-tooltip content="事件筛选" position="top">
        <div class="filter-icon" @click="openPopup">
          <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"
               :fill="props.info.filter?.filters?.length > 0 ? 'var(--tant-primary-color-primary-hover)' : 'currentColor'">
            <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 20h4v-4h-4v4zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
            </svg>
          </svg>
        </div>
      </a-tooltip>
      <template #content>
        <div class="ta-formula-filter-popup">
          <div class="ffp-head">添加筛选条件</div>
          <div class="ffp-body">
            <event-query-filter v-if="popupVisible" ref="queryFilterRef" :filter="queryFilter" :show-detail-filter="true" :code-list="props.codeList"
                                @query-filters-change="queryFiltersChange"/>
            <div class="ffp-extra">
              <span class="ffp-extra-add" @click="add">
                  <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path
                      d="M14 14v6l-4 2v-8L4 5V3h16v2l-6 9zM6.404 5L12 13.394 17.596 5H6.404z"></path></svg></svg>
                  添加条件
              </span>
            </div>
          </div>
          <div class="ffp-foot">
            <a-button class="cancel" @click.stop="() => popupVisible = false">取消</a-button>
            <a-button type="primary" @click="filterDone">完成</a-button>
          </div>
        </div>
      </template>
    </a-popover>
    <a-popover v-if="props.readOnly && queryFilter?.filters?.length>0" trigger="hover" position="tl" style="z-index: 1002;">
      <div class="filter-icon-read-only">
        <svg xmlns="http://www.w3.org/2000/svg" width="0.8em" height="0.8em" viewBox="0 0 24 24"
             :fill="props.info.filter?.filters?.length > 0 ? 'var(--tant-primary-color-primary-hover)' : 'currentColor'"
             style="padding-bottom: 2px">
          <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 20h4v-4h-4v4zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
          </svg>
        </svg>
      </div>
      <template #content>
        <div class="ta-formula-filter-popup">
          <div class="ffp-head">筛选条件</div>
          <div class="ffp-body">
            <event-query-filter ref="queryFilterRef" :disabled="props.readOnly" :filter="queryFilter" :show-detail-filter="true" :code-list="props.codeList"
                                @query-filters-change="queryFiltersChange"/>
          </div>
        </div>
      </template>
    </a-popover>
    <div class="fixed-modal" :style="{display: popupVisible ?'blank':'none'}"></div>
  </div>
</template>

<script setup lang="ts">
import {ref, watch,computed} from "vue"
import {getAnalyseEvent} from "@/api/analyse/api";
import router from '@/router';
import EventQueryFilter from "@/views/analyse/components/AttrCategoryFilter.vue"

const props = defineProps({
  info: {
    type: Object,
    default() {
      return {};
    },
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  codeList: {
    type: Array,
    default() {
      return []
    }
  }
})
const popupVisible = ref(false)
const pageType = computed(() => router.currentRoute.value.query?.pageType);
const queryFilter = ref(
    {
      logicalOperation: 'and',
      filters: [],
    }
)
const dataList = ref<any>([])

const previousEventCode = ref(''); // 用于存储上一次的 eventName
const getAttrList = async () => {
  // 检查当前的 eventName 是否与上一次不同
  if (props.info.eventCode !== previousEventCode.value) {
    previousEventCode.value = props.info.eventCode; // 更新上一次的 eventName
    const data = {
      event: props.info.eventCode,
      inApp: pageType.value && pageType.value === 'sdk' ? 0 : 1
    }
    await getAnalyseEvent(data).then((res) => {
      dataList.value = res;
    });
  }
};
watch(() => props.info, (newVal) => {
  getAttrList()
}, {immediate: true});

const openPopup = () => {
  queryFilter.value = props.info.filter
  popupVisible.value = true
}
watch(() => props.info.filter, () => {
  queryFilter.value = props.info.filter
}, {immediate: true, deep: true})
const queryFilterRef = ref()
const add = () => {
  queryFilterRef.value.add()
}

const queryFiltersChange = (v) => {
  queryFilter.value = v
}
const emits = defineEmits(['eventScreen'])
const paramsVerify = (params) => {
  const verifyFilter = (item) => {
    if (
      item.calcuSymbol !== 'ex' &&
      item.calcuSymbol !== 'nex' &&
      !((item.calcuSymbol === 'con' || item.calcuSymbol === 'ncon') && item.filterType === 'cluster') &&
      !item.thresholds?.length) {
      return false;
    }
    if (item.subFilters?.length > 0) {
      return item.subFilters.every(subFilter =>
          subFilter.calcuSymbol === 'ex' ||
          subFilter.calcuSymbol === 'nex' ||
          ((subFilter.calcuSymbol === 'con' || subFilter.calcuSymbol === 'ncon') && subFilter.filterType === 'cluster') ||
          subFilter.thresholds?.length > 0
      );
    }
    return true;
  };
  // 验证过滤条件列表的函数
  const verifyFilterList = (filterList = []) => {
    return filterList.length === 0 || filterList.every(items => {
      const itemArray = Array.isArray(items) ? items : [items];
      return itemArray.every(verifyFilter);
    });
  };
  // 获取三个需要验证的列表
  const firstList = params.filters
  // 验证所有列表
  return verifyFilterList(firstList)
};
// 筛选条件完成
const filterDone = () => {
  emits('eventScreen', queryFilter.value)
  popupVisible.value = false
}
</script>

<style scoped lang="less">
.fixed-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: transparent;
  visibility: hidden;
}

.ta-formula-filter-popup {
  width: 520px;
  min-height: 156px;
  font-size: 14px;

  .ffp-head {
    color: var(--tant-text-gray-color-text1-1);
    font-weight: 500;
    line-height: 20px;
  }

  .ffp-body {
    margin: 24px 0 20px;
  }

  .ffp-extra {
    margin-top: 8px;

    .ffp-extra-add {
      display: inline-block;
      margin-right: 16px;
      padding: 4px;
      color: var(--tant-primary-color-primary-default);
      line-height: 18px;
      border-radius: 2px;
      cursor: pointer;
      transition: all .3s;

      &:hover {
        background-color: var(--tant-secondary-color-secondary-fill-hover);
      }
    }
  }

  .ffp-foot {
    padding: 12px 0 0;
    text-align: right;

    :deep(.arco-btn) {
      height: 24px;
      padding: 1px 8px;
      font: var(--tant-body-font-body-regular);
      border-radius: var(--tant-border-radius-medium);
    }

    .cancel {
      background: transparent;
      margin-right: 8px;

      &:hover {
        background-color: var(--color-secondary);
      }
    }
  }
}

.filter-icon {
  font-size: 16px;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--tant-text-gray-color-text1-3);
  transition: all .3s;
  border: 1px solid transparent;
  background-color: var(--tant-secondary-color-secondary-fill);
  margin-left: 8px;

  &:hover {
    border-color: var(--tant-primary-color-primary-hover);
  }
}

.filter-icon-read-only {
  font-size: 16px;
  height: 16px;
  width: 16px;
  text-align: center;
  line-height: 16px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--tant-text-gray-color-text1-3);
  margin-left: 3px;
  transition: all .3s;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-bg {
  background-color: transparent;

  &:hover {
    background-color: var(--tant-secondary-color-secondary-transp-hover);
  }
}

.btn-26 {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}

.btn-bg-delete {
  background-color: transparent;

  &:hover {
    color: var(--tant-status-danger-color-danger-default);
    background-color: var(--tant-status-danger-color-danger-fill-hover);
  }
}
</style>