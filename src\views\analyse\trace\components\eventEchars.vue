<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {ref, watch} from "vue";
import {AnyObject} from "@/types/global";
import {cloneDeep} from "lodash"

interface Props {
  /**
   * 报表数据
   */
  eventData: any;
}

const props = defineProps<Props>()


const xAxis = ref<string[]>([]);
const ySeries = ref<any>([]);
const legendData = ref<any>([]);
const selected = ref<string[]>([]);

const axisPointerType = ref('')
const ySeriesData = ref<any>([])
const xAxisData = ref<any>([])


function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);


const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '6%',
      right: '2.6%',
      top: '60',
      bottom: '60',
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (param:any) => {
            return  `
              <div style="min-width:150px">
              <div style="color:#ccc;font-size:12px;display: flex;justify-content: space-between;line-height: 30px;border-bottom: solid 1px var(--tant-fill-color-fill1-1);">
              <span>${param.name}</span>
              <span>${param.value}(会话数)</span>
              </div>
              <div style="color:#333;font-size:12px;display: flex;justify-content: space-between;line-height: 30px;">
              <span>留存量</span>
              <span>95728(99.99%)</span>
              </div>
              <div style="color:#333;font-size:12px;display: flex;justify-content: space-between;line-height: 30px;">
              <span>流失量</span>
              <span>95728(99.99%)</span>
              </div>
              </div>
              `
      }
    },
    series:  {
      type: 'sankey',
      layout: 'none',
      emphasis: {
        focus: 'adjacency'
      },
      top:'12%',
      left:'3%',
      // right:'3%',
      nodeWidth:100,
      nodeGap: 50,
      layoutIterations: 1,
      itemStyle:{
        color:'rgba(50, 131, 244, 1)'
      },
      lineStyle:{
        opacity:0.1,
        color: 'gradient',
        curveness: 0.7,
      },
      draggable:false,
      label:{
        show:true,
        position: 'top',
        formatter: (params) => {
          return [
        `{b|${params.name}}`,
        `{c|${params.value}}`
          ].join('\n')
        },
        rich: {
            b: {
                color: '#333',
                lineHeight: 18
            },
            c: {
              color: '#ccc',
              lineHeight: 18
            }
        },
        richText: true ,
        offset:[-20,0]
      },
      data: [
        { name: '游戏启动' },
        { name: '用户登录' },
        { name: '账号注册' },
        { name: '角色升级' },
        { name: '发起订单' },
        { name: '游戏登出' },
        { name: '付费事件' },
        { name: '设备激活' },
        { name: '在线数据' },
        { name: '成本事件' },
        { name: '参加活动' },
        { name: '新手教程' },
      ],
      links: [
        { source: '游戏启动', target: '用户登录', value: 4444 },
        { source: '用户登录', target: '新手教程', value: 133 },
        { source: '用户登录', target: '角色升级', value: 111 },
        { source: '账号注册', target: '角色升级', value: 1222 },
        { source: '账号注册', target: '发起订单', value: 121 },
        { source: '账号注册', target: '游戏登出', value: 11 },
        { source: '付费事件', target: '设备激活', value: 331 },
        { source: '付费事件', target: '在线数据', value: 331 },
        { source: '付费事件', target: '成本事件', value: 31 },
        { source: '在线数据', target: '参加活动', value: 11 },
        { source: '用户登录', target: '参加活动', value: 3331 },
        { source: '角色升级', target: '新手教程', value: 441 },
        { source: '设备激活', target: '新手教程', value: 31 },
      ]
    },
  };
});

// 数组累加
const calculateCumulativeSums = (array:any) => {
  const numericArray = array.map(Number);
  const result = [];

  for (let i = 0; i < numericArray.length; i++) {
    if (i === 0) {
      result.push(numericArray[i]); // First item remains unchanged
    } else {
      const sum = numericArray[i] + result[i - 1];
      result.push(sum);
    }
  }
  return result;
};
// 数组相加
const calculateSum = (array:any) => {
  return array.map(Number).reduce((accumulator, currentValue) => accumulator + currentValue, 0);
};

const freshData = () => {
  
  ySeriesData.value = cloneDeep(ySeries.value)
  selected.value = legendData.value;
};


const changeType = ()=>{
  freshData()
}

freshData()


defineExpose({
  changeType
})

</script>

<template>
  <div class="chart-content">
    <a-spin  style="width: 100%;height: 640px;">
      <div style="display: flex;justify-content: center;font-size: 16px;">
        用户登录为初始事件的用户行为路径
      </div>
      <Chart ref="chartRef" :option="chartOption"/>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>