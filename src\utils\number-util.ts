import {NumberDisplayConfig} from "@/api/analyse/type";
import {isNumber} from "lodash";
import {getNumberDisplayConfig, NumberDisplayType} from "@/api/enum";

/**
 * 指标数值格式化
 * @param num 指标数值
 * @param displayType 展示配置
 */
export const formatIndicatorValue = (num: number, displayType?: NumberDisplayConfig) => {
  if (num === null || num === undefined) return '--';

  if (!displayType?.type || !isNumber(num)) {
    return String(num)
  }

  let numStr = '';
  if (displayType.type === 'default') {
    // 小数位处理
    numStr = displayType.decimalNum ? num.toFixed(displayType.decimalNum) : String(num);
  }

  if (displayType?.type === 'percent') {
    // 小数位处理
    numStr = (num*100).toFixed(displayType.decimalNum ?? 2)
  }

  // 千分位处理
  if (displayType?.thousandSep !== 0) {
    numStr = numStr.replace(/(\d)(?=(\d{3})+(?:\.\d+)?$)/g, "$1,")
  }
  return numStr + (displayType?.type === 'percent' ? "%" : '');
}

/**
 * 整数数值格式化
 * @param num 数值
 */
export const formatIntegerValue = (num: number) => {
 return  formatIndicatorValue(num, getNumberDisplayConfig(NumberDisplayType.Integer))
}

/**
 * 百分比数值格式化
 * @param num 数值
 */
export const formatPercentageValue = (num: number) => {
  return  formatIndicatorValue(num, getNumberDisplayConfig(NumberDisplayType.TwoPercent))
}

/**
 * index 映射到 大写字母
 * 0->A
 * ...
 * 25->Z
 */
export function numberToUpperLetter(n: number): string | null {
  if (n >= 0 && n <= 25) {
    return String.fromCharCode(65 + n); // 65 是 'A' 的 ASCII 码
  }
  return null; // 超出范围返回 null
}

/**
 * 大写字母 映射到 index
 * A->0
 * ...
 * Z->25
 */
export function letterToNumber(letter: string): number | null {
  const charCode = letter.toUpperCase().charCodeAt(0);
  if (charCode >= 65 && charCode <= 90) {
    return charCode - 65; // 'A' 对应 0
  }
  return null; // 非 A-Z 返回 null
}
