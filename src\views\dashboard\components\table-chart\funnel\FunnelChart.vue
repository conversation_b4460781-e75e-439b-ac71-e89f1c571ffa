<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import useLoading from '@/hooks/loading';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';

interface Props {
  /**
   * 报表数据
   */
  reportAnalysisData: object;
}

const props = defineProps<Props>()

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const {loading, setLoading} = useLoading(true);
const xAxis = ref<string[]>([]);
const ySeries = ref([]);
const legendData = ref<any>([]);
const groups = ref<any>([]) // 分组
const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);
// 千分位
const formatNumber = (num) => {
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || 0;
}
// 处理间隔
const handleStepList = (xAxis, data) => {
  const markLineData = [];
  const stepData = props.reportAnalysisData?.funnelQueryResult.find(item => item.type === 'conversion_step_rate')?.groupData[0].values || []
  if (stepData.length > 0) {
    stepData.shift();
  }
  const stepValue = stepData.map(item => (Math.round(item * 100 * 100) / 100))
  for (let i = 0; i < stepValue.length; i++) {
    const startValue = data[i];
    const endValue = data[i + 1];
    const currentDiff = stepValue[i];
    // 计算中点位置的y值
    const middleY = (startValue + endValue) / 2;
    markLineData.push([
      {
        name: currentDiff ? `${currentDiff}%` : '',
        xAxis: xAxis[i],
        yAxis: middleY, // 使用中点y值
        label: {
          show: true,
          position: 'middle',
          fontSize: 12,
          color: '#747C94',
          distance: 0 // 调整文字位置
        }
      },
      {
        xAxis: xAxis[i + 1],
        yAxis: middleY, // 使用中点y值
        symbol: 'arrow',
        symbolSize: 12, // [宽度, 高度]调整箭头大小
        symbolOffset: [0, 0]
      }
    ]);
  }
  return markLineData
}

function renderChart(wsResultData: any) {
  if (!wsResultData) {
    return
  }
  if (!wsResultData?.funnelQueryResult?.length) {
    setLoading(false)
    return
  }
  groups.value = wsResultData?.groups?.map(item => {
    return item.map(value => value === null ? 'null' : value).join(','); // 将 null 转换为字符串 'null'
  }) || [];
  xAxis.value = wsResultData?.stepEvents?.map((item, index) =>
      `步骤${index + 1} ${item.eventDisplayName}`
  )
  const rateData = wsResultData?.funnelQueryResult?.find(item => item.type === 'conversion_rate')?.groupData || []
  const numData = wsResultData?.funnelQueryResult?.find(item => item.type === 'conversion_num')?.groupData || []
  ySeries.value = groups.value?.map((groupName, index) => {
    // 找到对应的转化率和转化数数据
    const rateItem = rateData.find(item => item.group[0] === groupName)
    const numItem = numData.find(item => item.group[0] === groupName)
    const rateItemData = rateItem?.values.map(val => (Math.round(val * 100 * 100) / 100)) || []
    return {
      name: groupName,
      data: rateItemData,
      type: 'bar',
      barMaxWidth: 50,
      barMinWidth: 4,
      label: {
        show: groups.value.length < 2,
        formatter: (params: any) => {
          const numValue = numItem?.values[params.dataIndex] || 0
          if (numValue === 0) return '';  // 当数值为0时不显示
          if (params.dataIndex === 0) {
            return `${formatNumber(numValue)}`;
          }
          return `${params.value}%\n${formatNumber(numValue)}`
        },
        position: 'insideTop'
      },
      showBackground: groups.value.length < 2,
      markLine: groups.value.length < 2 ? {
        lineStyle: {
          type: 'solid',
          width: 4, // 加粗线条
          color: '#F6F6F9',
          curveness: 0 // 设为0使线条水平
        },
        symbol: ['none', 'arrow'], // 起点不显示符号，终点显示箭头
        label: {
          show: true,
          position: 'middle',
          fontSize: 14,
          color: '#747C94',
          fontWeight: 900,
          distance: 0
        },
        data: handleStepList(xAxis.value, rateItemData)
      } : {}
    }
  })
  legendData.value = ySeries.value?.map(item => item.name);
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData, oldData) => {
  renderChart(newData);
})

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '0%',
      top: '20',
      bottom: '60',
      containLabel: true,
    },
    legend: {
      data: legendData.value,
      bottom: '30',
      type: 'scroll', // 设置图例为滚动类型
      orient: 'horizontal', // 横向显示图例
    },
    xAxis: {
      type: 'category',
      data: xAxis.value
      // axisLabel: {
      //     interval: 1,
      // },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} %'
      }
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      enterable: true,
      confine: true,
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const xAxisName = params[0].name;
        let tooltipContent = '<div style="max-height: 300px;width:180px; overflow-y: auto;">';
        tooltipContent += `<div
        style="margin-bottom: 8px;color:var(--tant-text-gray-color-text1-3);overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;">${xAxisName}</div>`;
        params.forEach((item) => {
          tooltipContent += `<div style="display:flex;justify-content: space-between;align-items: center;">
          <div> ${item.marker} ${item.seriesName}:</div>
          <div>${item.value}%</div>
          </div>`;
        });
        tooltipContent += '</div>';
        return tooltipContent;
      }
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value,
    dataZoom: groups.value.length > 5 ? [
      {
        // 设置滚动条的隐藏与显示
        show: true,
        // 设置滚动条类型
        type: "slider",
        // 设置背景颜色
        backgroundColor: "rgb(255, 255, 255)",
        // 设置选中范围的填充颜色
        fillerColor: "rgb(167,183,204,0.4)",
        // 设置边框颜色
        borderColor: "#d2dbee",
        // 是否显示detail，即拖拽时候显示详细数值信息
        showDetail: false,
        // 数据窗口范围的起始数值
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 5,
        start: 0,
        end: 10,
        // empty：当前数据窗口外的数据，被设置为空。
        // 即不会影响其他轴的数据范围
        filterMode: "empty",
        // 设置滚动条宽度，相对于盒子宽度
        width: "50%",
        // 设置滚动条高度
        height: 8,
        // 设置滚动条显示位置
        left: "center",
        // 是否锁定选择区域（或叫做数据窗口）的大小
        zoomLoxk: true,
        // 控制手柄的尺寸
        handleSize: 0,
        // dataZoom-slider组件离容器下侧的距离
        bottom: 20,
      },
      {
        // 没有下面这块的话，只能拖动滚动条，
        // 鼠标滚轮在区域内不能控制外部滚动条
        type: "inside",
        // 滚轮是否触发缩放
        zoomOnMouseWheel: false,
        // 鼠标滚轮触发滚动
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
      },
    ] : []
  };
});

onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
