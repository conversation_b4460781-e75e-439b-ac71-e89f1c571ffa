<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {getDays} from "@/utils/dateUtil";

interface Props {
  /**
   * 日期
   */
   date: string[] | undefined

  /**
   * 数据
   */
  data: number[]

  /**
   * 数据名
   */
  dataName: string
}

const props = defineProps<Props>()

const loading = false;
const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '36',
      right: '0',
      top: '16',
      bottom: '24',
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
    },
    xAxis: {
      type: 'category',
      data: getDays(props.date[0], props.date[1]),
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter(value: number) {
          if (value >= 1000000) {
            return `${value / 1000000}M`; // 转换为 k 单位
          }
          if (value >= 1000) {
            return `${value / 1000}k`; // 转换为 k 单位
          }
          return value;
        }
      }
    },
    series: [
      {
        name: props.dataName,
        type: 'line',
        data: props.data,
        label: {
          show: true,
          position: 'top',
          formatter(params) {
            if (!params.value || typeof params.value !== 'number') {
              return params.value;
            }
            if (params.value >= 1000000) {
              return `${Math.round(params.value / 1000000)}M`; // 转换为M单位
            }
            if (params.value >= 1000) {
              return `${Math.round(params.value / 1000)}k`; // 转为k单位并保留整数
            }
            return Math.round(params.value); // 保留整数
          }
        },
        markLine: {
          symbol: 'none',
          data: [{type: 'average', name: 'Avg'}],
          label: {
            show: true,
            position: 'end'
          }
        }
      }
    ]
  };
});


</script>

<template>
  <a-spin :loading="loading" style="width: 100%; height: 100%">
    <Chart :option="chartOption"/>
  </a-spin>
</template>

<style scoped lang="less">

</style>