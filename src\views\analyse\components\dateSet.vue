<template>
    <a-dropdown :popup-max-height="300" position="bl" :update-at-scroll="true" @select="windowSelect">
        <div class="select-label" :style="{border: props.showBorder ? '1px solid var(--tant-border-color-border1-1)' : 'none',padding : props.showBorder? '0 8px' :'0'}">
        {{ windowName }}<icon-down v-if="props.showBorder"/>
        </div>
        <template #content>
            <a-dsubmenu v-if="!props.noMh&& !props.noExtend" value="minutes" trigger="hover">
                <template #default>按分钟</template>
                <template #content>
                    <a-doption
                        :value="{value:TimeParticleSize.MINUTE1,label:'1分钟'}"
                        :style="params.timeParticleSize===TimeParticleSize.MINUTE1 ?'background-color: var(--color-fill-2);':''">
                        1分钟
                    </a-doption>
                    <a-doption :value="{value:TimeParticleSize.MINUTE5,label:'5分钟'}" :style="params.timeParticleSize===TimeParticleSize.MINUTE5 ?'background-color: var(--color-fill-2);':''">
                        5分钟
                    </a-doption>
                    <a-doption :value="{value:TimeParticleSize.MINUTE10,label:'10分钟'}" :style="params.timeParticleSize===TimeParticleSize.MINUTE10 ?'background-color: var(--color-fill-2);':''">
                        10分钟
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-doption
                v-if="!props.noMh && !props.noExtend"
                :value="{value:TimeParticleSize.HOUR1,label:'按小时'}"
                :style="params.timeParticleSize === TimeParticleSize.HOUR1 ?'background-color: var(--color-fill-2);':''">
                按小时
            </a-doption>
            <a-doption
                :value="{value:TimeParticleSize.DAY1,label:'按天'}"
                :style="params.timeParticleSize === TimeParticleSize.DAY1 ?'background-color: var(--color-fill-2);':''">
                按天
            </a-doption>
            <a-doption :value="{value:TimeParticleSize.WEEK1,label:'按周'}" :style="params.timeParticleSize === TimeParticleSize.WEEK1 ?'background-color: var(--color-fill-2);':''">
                <div class="doption-item">
                    <span>按周</span>
                    <a-trigger v-model:popup-visible="visible" trigger="click" position="bl" :update-at-scroll="true"  @click="(event) => event.stopPropagation()">
                        <a-tooltip content="自定义周" position="right">
                            <icon-calendar class="hover-icon"/>
                        </a-tooltip>
                        <template #content>
                            <div class="week-set-body">
                                <div class="week-set-title">自定义周</div>
                                <div class="week-set-desc">
                                    按周统计时，将
                                    <span>星期一</span>
                                    作为周起始日
                                </div>
                                <div class="week-set-weeks">
                                    <div v-for="(item,index) in weekDayList" :key="index" class="week-set-week" :class="{'week-active': weekName == item.label}" @click="weekChange(item)">{{ item.label }}</div>
                                </div>
                                <div class="week-set-btns">
                                    <a-button @click="() => visible = false">取消</a-button>
                                    <a-button type="primary" @click="weekSure">确定</a-button>
                                </div>
                            </div>
                        </template>
                    </a-trigger>
                </div>
            </a-doption>
            <a-doption
                :value="{value:TimeParticleSize.MONTH1,label:'按月'}"
                :style="params.timeParticleSize === TimeParticleSize.MONTH1 ?'background-color: var(--color-fill-2);':''">
                按月
            </a-doption>
            <a-dsubmenu v-if="!props.noExtend" value="more" trigger="hover">
                <template #default>更多</template>
                <template #content>
                    <a-doption
                        :value="{value:TimeParticleSize.QUARTER1,label:'按季'}"
                        :style="params.timeParticleSize === TimeParticleSize.QUARTER1 ?'background-color: var(--color-fill-2);':''">
                        按季
                    </a-doption>
                    <a-doption
                        :value="{value:TimeParticleSize.YEAR1,label:'按年'}"
                        :style="params.timeParticleSize === TimeParticleSize.YEAR1 ?'background-color: var(--color-fill-2);':''">
                        按年
                    </a-doption>
                </template>
            </a-dsubmenu>
            <a-doption :value="{value:TimeParticleSize.TOTAL,label:'合计'}">合计</a-doption>
        </template>
    </a-dropdown>
</template>

<script setup lang="ts">
import {reactive, ref, watch} from "vue";
import {TimeParticleSize} from '@/api/enum';

const props = defineProps({
    timeParticle:{
        type:Object,
        default:() => {}
    },
    noMh:{
        type:Boolean,
        default:false
    },
    showBorder:{
        type:Boolean,
        default:true
    },
    noExtend:{
        type:Boolean,
        default:false
    }
})
const visible = ref(false)
const windowName = ref('按天')
const params = reactive({
    timeParticleSize:TimeParticleSize.DAY1,
    firstDayDfWeek:null
})
const nameLabel = [
    {value:TimeParticleSize.MINUTE1,label:'1分钟'},
    {value:TimeParticleSize.MINUTE5,label:'5分钟'},
    {value:TimeParticleSize.MINUTE10,label:'10分钟'},
    {value:TimeParticleSize.HOUR1,label:'按小时'},
    {value:TimeParticleSize.DAY1,label:'按天'},
    {value:TimeParticleSize.WEEK1,label:'按周'},
    {value:TimeParticleSize.MONTH1,label:'按月'},
    {value:TimeParticleSize.QUARTER1,label:'按季'},
    {value:TimeParticleSize.YEAR1,label:'按年'},
    {value:TimeParticleSize.TOTAL,label:'合计'}
]
// nameLabel.forEach(item => {
//     if(item.value === params.timeParticleSize){
//         windowName.value = item.label 
//     }
// })
watch(() => props.timeParticle, (newVal) => {
  if (newVal) {
    params.timeParticleSize = newVal.timeParticleSize
    params.firstDayDfWeek = newVal.firstDayDfWeek
    nameLabel.forEach(item => {
      if(item.value === params.timeParticleSize){
        windowName.value = item.label
      }
    })
  }
}, {immediate:true, deep: true })
const emits = defineEmits(['timeChange'])
const windowSelect = (v) => {
    params.timeParticleSize = v.value
    windowName.value = v.label
    if (windowName.value !== '按周') {
        params.firstDayDfWeek = null
    }else{
        params.firstDayDfWeek = params.firstDayDfWeek || 1
    }
    emits('timeChange',params)
}
const weekDayList = ref([
    {
        value:1,
        label:'星期一'
    },
    {
        value:2,
        label:'星期二'
    },
    {
        value:3,
        label:'星期三'
    },
    {
        value:4,
        label:'星期四'
    },
    {
        value:5,
        label:'星期五'
    },
    {
        value:6,
        label:'星期六'
    },
    {
        value:7,
        label:'星期天'
    }
])
const weekName = ref('星期一')
const weekChange = (item) => {
    weekName.value = item.label
    params.firstDayDfWeek = item.value
}
const weekSure = () => {
    windowName.value = '按周'
    params.timeParticleSize = TimeParticleSize.WEEK1
    emits('timeChange',params)
    visible.value = false
}

const incomingTimeParticle = (timeParticle:any) => {
  params.timeParticleSize = timeParticle.timeParticleSize
  params.firstDayDfWeek = timeParticle.firstDayDfWeek
  nameLabel.forEach(item => {
    if(item.value === params.timeParticleSize){
      windowName.value = item.label
    }
  })
}

defineExpose({
  incomingTimeParticle
})

</script>

<style lang="less" scoped>
.select-label{
    display: inline-block;
    height: 32px;
    color: var(--tant-text-gray-color-text1-2);
    line-height: 32px;
    background-color: var(--tant-bg-white-color-bg1-1);
    border: 1px solid var(--tant-border-color-border1-1);
    border-radius: 4px;
    cursor: pointer;
    transition: all .3s;
    white-space: nowrap;
}
.arco-dropdown-open .arco-icon-down {
    transform: rotate(180deg);
}
.doption-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 120px;
    .hover-icon{
        opacity: 0;
    }
    &:hover{
        .hover-icon{
            opacity: 1;
        }
    }
}
.week-set-body{
    width: 352px;
    padding: 16px 16px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: var(--tant-medium-shadow-medium-bottom);
    .week-set-title{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-1);
        font-weight: 500;
        font-size: 16px;
    }
    .week-set-desc{
        display: block;
        margin-bottom: 16px;
        color: var(--tant-text-gray-color-text1-3);
        font-size: 14px;
        span{
            color: var(--tant-text-gray-color-text1-1);
            font-weight: 500;
            padding: 0 4px;
        }
    }
    .week-set-weeks {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        margin-right: -16px;
        padding-bottom: 16px;
        .week-set-week {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 74px;
            margin-right: 8px;
            margin-bottom: 8px;
            padding: 5px 16px;
            color: var(--tant-text-gray-color-text1-2);
            font-size: 14px;
            white-space: nowrap;
            border: 1px solid var(--tant-border-color-border1-1);
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
            &:hover{
                background-color: var(--color-fill-2);
                border-color: var(--color-fill-2);
            }
        }
        .week-active{
                background-color: var(--color-fill-2);
                border-color: var(--color-fill-2);
        }

    }
    .week-set-btns {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-right: -16px;
        margin-left: -16px;
        padding-top: 8px;
        padding-right: 16px;
        border-top: 1px solid var(--tant-border-color-border1-1);
        button{
            border-radius: var(--tant-border-radius-medium);
            margin-left: 8px;
        }
    }
}
</style>
