/**
 * 路由名称
 */
export const ROUTE_NAME = {
  // login
  LOGIN: 'login',
  // 404
  NOT_FOUND: 'notFound',
  // 用户条件分群
  USER_GROUP: 'menu.operation.app.userGroup',
  // 用户分群
  USER_GROUP_CONDITION: 'data.user.group.condition',
  // 用户分群创建
  USER_GROUP_CREATE: 'data.user.group.create',
  // 用户标签
  USER_TAG: 'menu.operation.app.user.tag',
  // 用户标签创建
  USER_TAG_CREATE: 'data.user.tag.create',
  // 用户标签编辑
  USER_TAG_EDIT: 'data.user.tag.edit',
  // 用户条件标签
  USER_TAG_CONDITION: 'data.user.tag.create.condition',
  // 用户指标标签
  USER_TAG_INDICATOR: 'data.user.tag.create.indicator',
  // 应用配置
  APP_CONFIG: 'menu.operation.app.config',
  // 维表配置
  SETTING_DIMENSION: 'menu.setting.analyse.dimension',
  // 维表配置属性
  SETTING_DIMENSION_ATTR: 'analyse.dimension.attr',
  // 维表配置数据
  SETTING_DIMENSION_DATA: 'analyse.dimension.data',
  // ab实验管理
  AB_TEST_EXPERIMENT: 'menu.ab.test.management.experiment',
  // ab实验创建
  AB_TEST_EXPERIMENT_CREATE: 'menu.ab.test.management.experiment.create',
  // ab实验详情
  AB_TEST_EXPERIMENT_DETAIL: 'menu.ab.test.management.experiment.detail',
  // ab实验报告
  AB_TEST_EXPERIMENT_REPORT: 'menu.ab.test.management.experiment.report',
  // 事件管理
  SETTING_ANALYSE_EVENT: 'menu.setting.analyse.event',
  // 应用事件管理
  SETTING_OPERATION_ANALYSE_EVENT: 'menu.operation.analyse.event',
  // 事件属性管理
  SETTING_ANALYSE_EVENT_ATTR: 'menu.setting.analyse.eventAttribute',
  // 用户属性管理
  SETTING_ANALYSE_USER_ATTR:'menu.setting.analyse.userAttribute',
  // 指标管理
  SETTING_ANALYSE_INDICATOR: 'menu.setting.analyse.indicator',
  // 应用指标管理
  SETTING_OPERATION_ANALYSE_INDICATOR: 'menu.operation.analyse.indicator',
  // 指标创建
  SETTING_ANALYSE_INDICATOR_CREATE: 'data.analyse.indicator.create',
  // 指标编辑
  SETTING_ANALYSE_INDICATOR_EDIT: 'data.analyse.indicator.edit',
  // 应用事件属性
  OPERATION_ANALYSE_EVENT_ATTRIBUTE: 'menu.operation.analyse.eventAttribute',
  // 应用用户属性
  OPERATION_ANALYSE_USER_ATTRIBUTE: 'menu.operation.analyse.userAttribute',
  // 看板
  DASHBOARD: 'menu.dashboard.dashboard',
  // 自定义sql分析图表
  ANALYSE_CUSTOM_SQL_CHART: 'analyse.custom.sqlChart',
  // 自定义sql分析页面
  ANALYSE_CUSTOM_SQL: 'menu.analyse.toolbox.custom',
  // 事件分析
  ANALYSE_EVENT: 'menu.analyse.toolbox.event',
  // 留存分析
  ANALYSE_RETENTION: 'menu.analyse.toolbox.retention',
  // 漏斗分析
  ANALYSE_FUNNEL: 'menu.analyse.toolbox.funnel',
  // 属性分析
  ANALYSE_PROPERTY: 'menu.analyse.toolbox.property',
  // 路径分析
  ANALYSE_TRACE: 'menu.analyse.toolbox.trace',
  // 分布分析
  ANALYSE_SCATTER: 'menu.analyse.toolbox.scatter',
  // 间隔分析
  ANALYSE_INTERVAL: 'menu.analyse.toolbox.interval',
  // 归因分析
  ANALYSE_ATTRIBUTION: 'menu.analyse.toolbox.attribution',
  // 运营分析
  ANALYSE_APPLICATION: 'menu.analyse.toolbox.application',
  // 群组分析
  ANALYSE_GROUP: 'menu.analyse.toolbox.group',
  // 报表编辑
  REPORT_EDIT: 'data.report.edit',
  // 设置菜单
  SETTING_MENU: 'menu.setting',
  // 分析菜单
  ANALYSE_MENU: 'menu.analyse',
  // 安全菜单
  SECURE_MENU: 'menu.secure',
  // 用户列表
  USER_LIST: 'data.user.info.list',
  // 用户详情
  USER_DETAIL: 'data.user.info.detail',
  // 分组管理
  SETTING_COMMON_EVENT_INDICATOR_GROUP: 'menu.setting.common.eventIndicatorGroup',
  // 热更新管理
  FILE_RESOURCE: 'menu.operation.service.fileResource',
  // 热更新管理文件详情
  FILE_RESOURCE_DETAIL: 'menu.operation.service.fileResource.detail',
  // 用户中心
  USER: 'menu.user',
  // 账户设置
  USER_ACCOUNT: 'menu.user.account',
  // 消息订阅
  USER_NOTIFY: 'menu.user.notify',
}

export const WHITE_LIST = [
  {name: ROUTE_NAME.NOT_FOUND, children: []},
  {name: ROUTE_NAME.LOGIN, children: []},
];

export const NOT_FOUND = {
  name: ROUTE_NAME.NOT_FOUND,
};

export const ANALYSE_MENU = {
  title: 'menu.analyse',
  name: ROUTE_NAME.ANALYSE_MENU,
  fullPath: '/analyse',
};

export const REDIRECT_ROUTE_NAME = 'Redirect';

