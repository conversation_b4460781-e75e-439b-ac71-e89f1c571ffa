<template>
    <!-- 实验详情 -->
    <a-form ref="formRef" :model="form" label-align="left" style="width: 100%;">
        <div class="form-block-label">
            <span class="form-block-label-title">基本配置</span>
            <div class="form-block-label-extra"></div>
        </div>
        <a-form-item field="name" label="实验名称" validate-trigger="blur">
            {{ form.name }}
        </a-form-item>
        <a-form-item field="description" label="实验描述">
            {{ form.description || '暂无' }}
        </a-form-item>
        <a-form-item field="tags" label="实验标签">
            {{ form.tags.join(',') || '暂无' }}
        </a-form-item>
        <a-form-item field="duration" label="实验时长" validate-trigger="blur">
            {{ form.duration }}天
        </a-form-item>
        <a-form-item field="dataSource" label="数据来源">
            {{ getDataSourceText(form.dataSource) }}
        </a-form-item>
        <div class="form-block-label">
            <span class="form-block-label-title">生效逻辑设置</span>
            <div class="form-block-label-extra"></div>
        </div>
        <a-form-item field="isMutualExclusion" label="是否互斥">
            <span>{{ getMutualExclusionText(form.isMutualExclusion) }}</span>
        </a-form-item>
        <a-form-item v-if="form.isMutualExclusion === 1" field="flowLayerCode" label="流量层">
            {{ form.flowLayerName }}
        </a-form-item>
        <a-form-item field="flowPercentage" label="实验流量" validate-trigger="blur">
            {{ form.flowPercentage }}{{form.flowPercentage ? '%' : '暂无'  }}
        </a-form-item>
        <a-form-item field="enableGradual" label="流量生效方式">
            {{ form.enableGradual ? '平滑生效' : '立即生效'  }}
        </a-form-item>
        <a-form-item v-if="form.enableGradual" field="gradualDuration" label="平滑生效时间">
            {{ form.gradualDuration }}min
        </a-form-item>
        <a-form-item field="userScope" label="受众范围">
            {{ getUserScopeText(form.userScope) }}
        </a-form-item>
        <a-form-item field="consistency" label="受众规则">
            <cluster-index v-if="Object.keys(form.userRules).length" :only-evt="true" :disabled="true" :is-provide-client="true" :user-filter="form.userRules"/>
            <span v-else>暂无</span>
        </a-form-item>
        <a-form-item field="consistency" label="体验一致性">
            <a-switch v-model="form.consistency" :checked-value="true" :unchecked-value="false" disabled/>
        </a-form-item>
        <div class="form-block-label">
            <span class="form-block-label-title">实验版本参数配置</span>
            <div class="form-block-label-extra"></div>
        </div>
        <a-form-item field="associatedRemote" label="是否关联远程配置">
            {{ form.associatedRemote === 1? '关联' : '不关联'  }}
        </a-form-item>
        <a-form-item v-if="form.associatedRemote" field="remoteConfigCode" label="关联的远程配置">
            {{ form.remoteConfigCode }}
        </a-form-item>
        <a-form-item field="domains" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }">
            <div v-if="versionList.length" class="domains-content" style="width: 100%">
                <div v-for="(item,index) in versionList" :key="index" class="domain-item">
                    <div class="head">
                        <div class="justify" style="height: 100%;">
                            <div class="content">
                                <a-space>
                                    <icon-up v-if="item.showContent" class="icon-style" @click="() => item.showContent = false"/>
                                    <icon-down v-else class="icon-style" @click="() => item.showContent = true"/>
                                    <span>{{ item.name }}</span>
                                </a-space>
                            </div>
                            <div class="extra">
                            </div>
                        </div>
                    </div>
                    <div v-if="item.showContent" class="content-info">
                        <div class="content-box">
                            <div class="item-left">
                                <a-form-item field="type" label="版本类型">
                                    <a-radio-group v-model:model-value="item.type" disabled>
                                        <a-radio :value="0">对照组</a-radio>
                                        <a-radio :value="1">实验组</a-radio>
                                    </a-radio-group>
                                </a-form-item>
                                <a-form-item field="description" label="描述">
                                    <a-textarea v-model="item.description" disabled/>
                                </a-form-item>
                                <a-form-item field="testUserCodes" label="测试用户">
                                    <a-textarea v-model="item.testUserCodes" disabled/>
                                </a-form-item>
                                <a-form-item field="whitelistCodes" label="白名单">
                                    <a-select
                                        v-model:model-value="item.whitelistCodes"
                                        multiple
                                        disabled>
                                        <a-option v-for="white in whiteList" :key="white.code" :value="white.code">
                                            {{ white.name }}
                                        </a-option>
                                    </a-select>
                                </a-form-item>
                            </div>
                            <div class="item-right">
                                <div style="color: var(--color-text-2);font-size: 14px;line-height: 32px;">参数配置</div>
                                <div class="form-content">
                                    <div v-for="(config,configIndex) in item.configParams" :key="configIndex" class="config-box">
                                        <div class="wrapper">
                                            <a-input v-model:model-value="config.name" disabled/>
                                        </div>
                                        <div class="wrapper">
                                            <a-select
                                                v-model:model-value="config.dataType"
                                                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }"
                                                style="width: 120px;"
                                                disabled>
                                                <a-option v-for="type in dataTypeList" :key="type.value" :value="type.value">
                                                    {{ type.label }}
                                                </a-option>
                                            </a-select>
                                        </div>
                                        <div class="wrapper flex1">
                                            <a-input v-if="config.dataType === 'string'" v-model="config.value" disabled/>
                                            <a-input v-if="config.dataType === 'int' || config.dataType === 'float'" v-model="config.value" disabled/>
                                            <a-select
                                                v-if="config.dataType === 'boolean'"
                                                v-model:model-value="config.value"
                                                disabled
                                                :trigger-props="{ autoFitPopupMinWidth: true,updateAtScroll: true }">
                                                <a-option value="true">True</a-option>
                                                <a-option value="false">False</a-option>
                                            </a-select>
                                            <a-input v-if="config.dataType === 'json'" v-model="config.value" disabled/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <span v-else>暂无实验版本</span>
        </a-form-item>
        <div class="form-block-label">
            <span class="form-block-label-title">流量分配</span>
            <div class="form-block-label-extra">
                <span style="color: #8b8ba6;">流量均匀分配：</span>
                <a-switch v-model:model-value="isEvenDistribution" disabled/>
            </div>
        </div>
        <a-form-item field="ratios" :label-col-props="{ span: 0 }" :wrapper-col-props="{ span: 24 }" validate-trigger="change">
            <a-table :columns="columns" :data="tableData" :pagination="false" :style="{ width: '100%' }">
                <template #flowPercentage="{ record }">
                    <a-input-number
                        v-model="record.flowPercentage"
                        :min="0"
                        :max="100"
                        :step="0.1"
                        :precision="1"
                        disabled
                        :style="{ width: '120px' }"
                    >
                        <template #suffix>%</template>
                    </a-input-number>
                </template>
                <template #actualFlow="{ record }">
                    {{ `${record.flowPercentage}% 实验流量 * 流量分配占比` }}
                </template>
            </a-table>
        </a-form-item>
        <div class="form-block-label">
            <span class="form-block-label-title">实验核心指标配置</span>
            <div class="form-block-label-extra"></div>
        </div>
        <div class="group-select">
            <a-form-item field="coreIndicatorCode" label="" :label-col-props="{ span: 0 }">
                <a-cascader v-model:model-value="form.coreIndicatorCode" :options="options" :style="{width:'300px'}" disabled/>
            </a-form-item>
        </div>
        <div class="form-block-label">
            <span class="form-block-label-title">实验关注指标配置</span>
            <div class="form-block-label-extra"></div>
        </div>
        <div class="indicator-config">
            <a-input-search v-model="searchValue" :style="{width:'320px',marginBottom:'16px'}" placeholder="输入指标关键词"/>
            <a-tree
                v-model:checked-keys="form.concernIndicatorCodes"
                :checkable="true"
                :data="filteredTreeData"
                :default-expanded-keys="[]"
            />
        </div>
    </a-form>
</template>

<script setup lang="ts">
import {computed, reactive, ref, watch} from "vue";
import ClusterIndex from "@/views/analyse/components/userFilterComponent/ClusterIndex.vue";
import {toolStore} from '@/store';
import {cloneDeep} from "lodash";
import {getUserWhiteList} from "@/api/ab/api";

const props = defineProps({
    detailData: {
        type: Object,
        default: () => ({})
    }
})
const toolData = toolStore();
const form = reactive({
    name: '',
    description:'',
    tags:[],
    duration: 30,
    flowPercentage:0,
    enableGradual:false,
    gradualDuration:0,
    userScope:'all',
    userRules:{},
    consistency:true,
    isMutualExclusion:0,
    flowLayerCode:'',
    flowLayerName:'',
    associatedRemote:0,
    remoteConfigCode:'',
    coreIndicatorCode:'',
    concernIndicatorCodes:[],
    dataSource:'',
})

const versionList = ref<any>([])
const whiteList = ref<any>([])
// 表格数据
const tableData = computed(() => {
    return versionList.value.map((item) => ({
        name: item.name,
        flow: '100%',
        flowPercentage: item.flowPercentage,
        actualFlow: ''
    }));
});
// 是否均匀分配
const isEvenDistribution = computed(() => {
    if (!versionList.value.length) return false;
    const first = versionList.value[0].flowPercentage;
    return versionList.value.every(item => item.flowPercentage === first);
});
const columns = [
    {
        title: '版本名称',
        dataIndex: 'name',
        width: 200,
        slotName: 'name'
    },
    {
        title: '实验流量',
        dataIndex: 'flow',
        width: 200,
        slotName: 'flow'
    },
    {
        title: '流量分配占比',
        dataIndex: 'flowPercentage',
        width: 200,
        slotName: 'flowPercentage'
    },
    {
        title: '实际生效流量',
        dataIndex: 'actualFlow',
        width: 200,
        slotName: 'actualFlow'
    }
];
// 是否互斥格式化
function getMutualExclusionText(val: number) {
    if (val === 0) return '否'
    if (val === 1) return '流量层互斥'
    return ''
}
// 受众范围格式化
function getUserScopeText(val: string) {
    if (val === 'all') return '活跃用户'
    if (val === 'new') return '新增用户'
    return ''
}
// 数据来源格式化
function getDataSourceText(val: string) {
    if (val === 'event') return 'IVY'
    if (val === 'appsflyer') return 'AF'
    return '暂无'
}
const dataTypeList = [
    {
        value:'string',
        label:'字符串',
    },
    {
        value:'int',
        label:'整数',
    },
    {
        value:'float',
        label:'小数',
    },
    {
        value:'boolean',
        label:'布尔值',
    },
    {
        value:'json',
        label:'JSON',
    }
]
const searchValue = ref('');
const options = computed(() => {
    const indicatorList = toolData.toolModelList
            .map(category => ({
                ...category,
                items: (category.items || []).filter(item => item.objectType === 'indicator')
            }))
            .filter(category => category.items && category.items.length > 0);
    return indicatorList.map(category => ({
        value: category.category,
        label: category.category,
        children: (category.items || []).map(item => ({
            value: item.code,
            label: item.name ? `${item.name}-${item.displayName}` : item.displayName
        }))
    }));
});
// 树形数据
type TreeNode = {
    title: string;
    key: string;
    children?: TreeNode[];
};
const treeData = ref<TreeNode[]>([]);
// 搜索过滤方法
const filterTreeNode = (node: any, keyword: string) => {
    return node.title.toLowerCase().includes(keyword.toLowerCase());
};

// 递归过滤树节点
const filterTree = (data: any[], keyword: string) => {
    return data.filter(node => {
        if (filterTreeNode(node, keyword)) {
            return true;
        }
        if (node.children) {
            node.children = filterTree(node.children, keyword);
            return node.children.length > 0;
        }
        return false;
    });
};

// 计算过滤后的树数据
const filteredTreeData = computed(() => {
    if (!searchValue.value) {
        return treeData.value;
    }
    return filterTree(cloneDeep(treeData.value), searchValue.value);
});
const buildTreeData = (list) => {
    return list.map(category => ({
        title: category.category,
        key: category.category,
        disabled: true,
        children: (category.items || []).map(item => ({
            title: item.name ? `${item.name}-${item.displayName}` : item.displayName,
            key: item.code,
            disabled: true
        }))
    }));
};
const initTreeData = () => {
    if (toolData.toolModelList && toolData.toolModelList.length) {
        const indicatorList = toolData.toolModelList
            .map(category => ({
                ...category,
                items: (category.items || []).filter(item => item.objectType === 'indicator')
            }))
            .filter(category => category.items && category.items.length > 0);
        treeData.value = buildTreeData(indicatorList);
    }
};
const getWhiteList = async () => { 
    try {
        const res = await getUserWhiteList()
        whiteList.value = res.filter(item => item.status === 1)
    } catch (e) {
        console.error(e)
    }
}
// 初始化阶段调用一次
const init = async () => {
    // if(!toolData.toolModelList.length){
    //     await toolData.fetchAllModalList()
    // }
    getWhiteList()
    await toolData.fetchAllModalList()
    initTreeData();
}
init()
watch(() => props.detailData, (val) => {
    if (val) {
        form.name = val.name || ''
        form.description = val.description || ''
        form.tags = val.tags || []
        form.duration = val.duration
        form.flowPercentage = val.flowPercentage
        form.enableGradual = val.enableGradual || false
        form.gradualDuration = val.gradualDuration
        form.userScope = val.userScope || 'all'
        form.userRules = val.userRules || {}
        form.consistency = val.consistency || true
        form.isMutualExclusion = val?.flowLayer?.code ? 1 : 0
        form.flowLayerCode = val?.flowLayer?.code || ''
        form.flowLayerName = val?.flowLayer?.name || ''
        form.remoteConfigCode = val?.remoteConfig?.name || ''
        form.associatedRemote = form.remoteConfigCode ? 1 : 0
        if(val?.variants?.length){
            versionList.value = val.variants.map(item => {
                return {
                    code:item.code,
                    name:item.name,
                    type:item.type,
                    description:item.description,
                    showContent:true,
                    flowPercentage: item.flowPercentage,
                    configParams: item.configParams,
                    whitelistCodes: item.whitelistCodes || [],
                    testUserCodes:item.testUserCodes?.length ? item.testUserCodes.join(',') : '',
                }
            })  
        }
        form.coreIndicatorCode = val?.coreIndicator?.code || ''
        form.concernIndicatorCodes = val?.concernIndicator?.map(item => item.code) || []
        form.dataSource = val?.dataSource || ''
    }
},{immediate: true,deep: true})
</script>

<style lang="less" scoped>
@import '../style/form.less';

</style>