
<template>
    <!-- 添加账户 -->
    <a-modal v-model:visible="modalVisible" :width="600" title-align="start" :title="modalTitle" :footer="false" @cancel="closeModal">
        <a-form ref="formRef" :model="form" :rules="rules">
            <a-form-item field="channel" label="渠道" validate-trigger="blur">
                Mintegral
            </a-form-item>
            <a-form-item field="email" label="联系邮箱" validate-trigger="blur">
                <a-input v-model="form.email" />
            </a-form-item>
            <a-form-item field="api" label="API Key" validate-trigger="blur">
                <a-textarea v-model="form.api" />
            </a-form-item>
            <a-form-item field="access" label="Access Key" validate-trigger="blur">
                <a-textarea v-model="form.access" />
            </a-form-item>
        </a-form>
        <div class="footer">
            <a-button style="margin-right: 10px;" class="cancel" @click="closeModal">取消</a-button>
            <a-button type="primary" :loading="loading" @click="saveData">
                保存
            </a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import {reactive, ref} from "vue";
import {Message} from '@arco-design/web-vue';

const modalVisible = ref(false)
const modalTitle = ref('添加账号')
const form = reactive({
    channel:'',
    email:'',
    api:'',
    access:'',
})
const rules = {
    email: [
        {
            required: true,
            validator: (value, cb) => {
                return new Promise((resolve) => {
                    if (!value) {
                        cb('请输入邮箱');
                    }
                    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (value && !emailRegex.test(value)) {
                        cb('请输入正确邮箱');
                    }
                    resolve(value);
                });
            }
        }
    ],
    api: [
        {
            required: true,
            message:'请输入API Key',
        }
    ],
    access: [
        {
            required: true,
            message:'请输入Access Key',
        }
    ],
}
const formRef = ref()

const emits = defineEmits(['updateData']);
const openModal = async () => {
    formRef.value.resetFields()
    formRef.value.clearValidate()
    modalVisible.value = true
}
const closeModal = () => {
    modalVisible.value = false
}
const loading = ref(false)
const saveData = () => {
    formRef.value.validate(async (valid:any) => {
        if (!valid) {
            loading.value = true
            try {
                // await updateGa(data).then(res => {
                //     Message.success('更新成功');
                //     modalVisible.value = false;
                //     emits('updateData')
                // })
            } catch (error) {
                console.error('操作失败:', error);
                Message.error('操作失败，请重试');
            }
            loading.value = false
        }
    })
}

defineExpose(
    {
        openModal
    }
)
</script>

<style scoped lang="less">
.footer{
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
}
.cancel {
    background: transparent;
    margin-right: 8px;
    &:hover {
        background-color: var(--color-secondary);
    }
}
</style>