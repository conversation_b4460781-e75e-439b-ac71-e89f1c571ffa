<template>
  <div class="material-content">
    <div class="setting">
      <div class="label">筛选</div>
      <div class="setting-content">
        <a-select placeholder="请选择汇总方式" allow-clear style="width: 240px; margin-right: 24px">
          <template #label="{ data }">
            <span>汇总方式-{{ data?.label }}</span>
          </template>
          <a-option value="1">素材ID</a-option>
          <a-option value="2">素材名称</a-option>
        </a-select>
      </div>
    </div>
    <a-divider />
    <div class="table-wrap">
      <div class="wrap-left">
        <a-dropdown>
          <a-button class="br4" style="margin-left: 12px" :disabled="!selectedKeys.length">
            <template #icon>
              <icon-tags />
            </template>
            <template #default>批量操作<icon-down style="margin-left: 12px" /></template>
          </a-button>
          <template #content>
            <a-doption @click="batchEditTag">编辑标签</a-doption>
            <a-doption @click="batchEditDesigner">编辑设计师</a-doption>
            <a-doption @click="batchEditCreativer">编辑创意人</a-doption>
          </template>
        </a-dropdown>
      </div>
      <div class="wrap-right">
        <a-button class="br4" style="margin-left: 12px">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>刷新</template>
        </a-button>
        <a-dropdown>
          <a-button class="br4" style="margin-left: 12px">
            <template #icon>
              <icon-download />
            </template>
            <template #default>导出<icon-down style="margin-left: 12px" /></template>
          </a-button>
          <template #content>
            <a-doption>仅预览地址列表</a-doption>
            <a-doption>含缩略图列表</a-doption>
            <a-doption>导出拒审信息</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>
    <div class="table-area">
      <a-table
        v-model:selectedKeys="selectedKeys"
        :columns="columns"
        :loading="loading"
        :data="tableData"
        :bordered="false"
        :hoverable="true"
        sticky-header
        :table-layout-fixed="true"
        :filter-icon-align-left="true"
        :column-resizable="true"
        :scroll="scroll"
        :pagination="false"
        :summary="true"
        :row-selection="rowSelection"
        row-key="materialId"
      >
        <template #materialName="{ record }">
          <div class="cell-content">
            <div class="text">{{ record.materialName }}</div>
            <div @click="toDetail(record)">
              <icon-right-circle style="cursor: pointer" />
            </div>
          </div>
        </template>
        <template #thumbnail="{ record }">
          <a-popover position="right">
            <div class="thumbnail-box">
              <img :src="record.thumbnail" alt="" />
            </div>
            <template #content>
              <div class="thumbnail-content">
                <video width="100%" height="100%" controls loop autoplay muted>
                  <source src="https://xmp-material.mobvista.com/video/69/69a97d8995984d7686899718a919b30d.mp4" type="video/mp4" />
                </video>
              </div>
            </template>
          </a-popover>
        </template>
        <template #score="{ record }">
          <a-rate :default-value="record.score" readonly />
        </template>
        <template #tags="{ record }">
          <a class="actions-btn">添加标签</a>
        </template>
        <template #designer="{ record }">
          <a class="actions-btn" @click="singleEditDesigner(record)">添加设计师</a>
        </template>
        <template #summary-cell="{ column, record }">
          <div v-if="column.dataIndex === 'thumbnail'">
            <div class="text">汇总</div>
          </div>
          <div v-else-if="noSumColumns.includes(column.dataIndex)">
            <div class="text">-</div>
          </div>
          <div v-else>{{ record[column.dataIndex] }}</div>
        </template>
      </a-table>
      <div class="pagination">
        <a-pagination :total="total" show-total @change="pageChange" />
      </div>
    </div>
    <DetailDrawer ref="detailRef" :show-channel="false" />
    <!-- 批量编辑标签 -->
    <BatchEditTags ref="tagRef" />
    <!-- 批量编辑设计师 -->
    <BatchEditDesigner ref="designerRef" />
    <!-- 批量编辑创意人 -->
    <BatchEditCreativer ref="creativerRef" />
    <!-- 单个编辑设计师 -->
    <SingleEditDesigner ref="singleDesignerRef" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import BatchEditTags from '@/views/launch/promotion/components/BatchEditTags.vue';
  import DetailDrawer from '@/views/launch/promotion/components/materialDetailDrawer/index.vue';
  import BatchEditDesigner from '@/views/launch/promotion/components/BatchEditDesigner.vue';
  import BatchEditCreativer from '@/views/launch/promotion/components/BatchEditCreativer.vue';
  import SingleEditDesigner from '@/views/launch/promotion/components/EditDesigner.vue';

  // 设置数据
  const filterParams = reactive({
    viewType: '1',
    showChildrenMaterial: false,
    showLaunchData: false,
  });
  const total = ref(0);
  // 模拟table数据
  const loading = ref(false);
  const selectedKeys = ref([]);

  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  const tableData = ref<any>([
    {
      materialId: 1,
      materialName: 'mockName1',
      thumbnail: 'https://xmp-api.mobvista.com/mediacenter/api/cdn?url=http%3A%2F%2Fcdn-adn.rayjump.com%2Fcdn-adn%2Fv2%2Fportal%2F24%2F11%2F26%2F10%2F26%2F674531bb5e448.png&sign=5c4928d59ae0816422e57a3530bcad20&c_id=3003',
      score: 4,
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
    {
      materialId: 2,
      materialName: 'mockName2',
      thumbnail: 'https://xmp-api.mobvista.com/mediacenter/api/cdn?url=http%3A%2F%2Fcdn-adn.rayjump.com%2Fcdn-adn%2Fv2%2Fportal%2F24%2F11%2F26%2F10%2F26%2F674531bb5e448.png&sign=5c4928d59ae0816422e57a3530bcad20&c_id=3003',
      score: 3,
      cost: 1000,
      impressions: 1000,
      cpm: 1000,
      clicks: 1000,
      cpc: 1000,
      ctr: 1000,
      conversions: 1000,
    },
  ]);
  const columns = ref<any>([
    { title: '缩略图', dataIndex: 'thumbnail', slotName: 'thumbnail', width: 150, fixed: 'left' },
    { title: '素材名称', dataIndex: 'materialName', slotName: 'materialName', width: 250, fixed: 'left' },
    { title: '评分(均衡)', dataIndex: 'score', slotName: 'score', width: 200, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '标签', dataIndex: 'tags', slotName: 'tags', minWidth: 150 },
    { title: '设计师', dataIndex: 'designer', slotName: 'designer', minWidth: 150 },
    { title: '来源', dataIndex: 'source', minWidth: 150 },
    { title: '类型', dataIndex: 'type', minWidth: 150 },
    { title: '关联创意数', dataIndex: 'ideas', minWidth: 150 },
    { title: '花费', dataIndex: 'cost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '展示数', dataIndex: 'impressions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '千次展示成本', dataIndex: 'cpm', minWidth: 180, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击数', dataIndex: 'clicks', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击成本', dataIndex: 'cpc', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '点击率', dataIndex: 'ctr', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化数', dataIndex: 'conversions', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化成本', dataIndex: 'conversionCost', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '转化率', dataIndex: 'conversionRate', minWidth: 150, sortable: { sortDirections: ['ascend', 'descend'] } },
  ]);
  const noSumColumns = ref(['materialName', 'score', 'tags', 'designer', 'source', 'type', 'ideas']);
  // Table页面滑动
  const scroll = {
    y: 'calc(100% - 56px)',
  };

  const detailRef = ref();
  const toDetail = (record: any) => {
    detailRef.value.openModal(record);
  };
  // 批量编辑标签
  const tagRef = ref();
  const batchEditTag = () => {
    tagRef.value.openModal('material');
  };
  // 批量编辑设计师
  const designerRef = ref();
  const batchEditDesigner = () => {
    designerRef.value.openModal();
  };
  // 单个编辑设计师
  const singleDesignerRef = ref();
  const singleEditDesigner = (record) => {
    singleDesignerRef.value.openModal();
  };
  // 批量编辑创意人
  const creativerRef = ref();
  const batchEditCreativer = () => {
    creativerRef.value.openModal();
  };
  const init = () => {};
  init();
  // 分页
  const pageChange = (v) => {
    init();
  };
</script>

<style scoped lang="less">
@import '@/views/launch/promotion/style/common.less';
  :deep(.arco-select-view-tag) {
    span:first-child {
      width: 90%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: bottom;
    }
  }
</style>
