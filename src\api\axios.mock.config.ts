import Axios, {AxiosResponse} from 'axios'
import qs from 'query-string'
import {Message, Modal} from '@arco-design/web-vue'
import _ from "lodash"
import {ApiError} from "@/api/type";
import {convertKeysToCamelCase} from "@/utils/strUtil";
import {getMenuList} from "@/api/authorize/api";
import {useUserStore} from '@/store';
import {useSessionStorage} from "@vueuse/core";
import {h} from "vue";
import {getLatestReleaseVersion} from "@/api/system/api";

export const baseURL = 'http://127.0.0.1:4523/m1/4908605-4565314-default'

export const CONTENT_TYPE = 'Content-Type'

export const FORM_URLENCODED = 'application/x-www-form-urlencoded; charset=UTF-8'

export const APPLICATION_JSON = 'application/json; charset=UTF-8'

const serviceMock = Axios.create({
  baseURL,
  timeout: 10 * 60 * 1000,
})

serviceMock.interceptors.request.use((config) => {
    if (!config.headers) {
      config.headers = {} as any
    }
    if (config.headers && !config.headers[CONTENT_TYPE]) {
      config.headers[CONTENT_TYPE] = APPLICATION_JSON
    }
    if (config.headers && config.headers[CONTENT_TYPE] === FORM_URLENCODED) {
      config.data = qs.stringify(config.data)
    }
    const userToken: string | null | undefined = localStorage.getItem('token');
    const appId = sessionStorage.getItem('app-id')
    const timeZone = sessionStorage.getItem('time-zone')
    const appIds = useSessionStorage("app-id-list", []);
    const webVersion = useSessionStorage("web-version", {});
    if (!webVersion.value?.versionCode) {
      webVersion.value = JSON.stringify(localStorage.getItem('web-version'));
    }
    const dataSource = sessionStorage.getItem('data-source')
    if (config.headers && userToken && !config.headers['x-token']) {
      config.headers['x-token'] = userToken;
    }
    if (config.headers && !config.headers['app-id']) {
      config.headers['app-id'] = appId || '';
    }
    if (config.headers && !config.headers['app-ids']) {
      config.headers['app-ids'] = appIds.value?.join(',') || '';
    }
    if (config.headers && !config.headers['data-source']) {
      config.headers['data-source'] = dataSource || '';
    }
    if (config.headers && !config.headers['time-zone']) {
      config.headers['time-zone'] = timeZone || '+00:00';
    }
    if (config.headers && !config.headers['web-version']) {
      config.headers['web-version'] = webVersion.value?.versionCode || '';
    }
    return config
  }, (error) => {
    return Promise.reject(error.response)
  }
)

let webVersionConflict = false;

// 标签和标题映射
const sectionMap: Record<string, string> = {
  Init: '🚀 初始化',
  Optimize: '⚙️ 优化',
  Feature: '🆕 新特性',
  Doc: '📚 文档',
  Refactor: '🔄 重构',
  Todo: '📝 待处理',
  Delete: '🗑️ 删除',
  Fixed: '🐞 修复问题',
  Design: '🎨 设计调整',
  Update: '🔧 更新',
};
// 初始化分组数据
const groupedLogs: Record<string, string[]> = {};
Object.keys(sectionMap).forEach(type => {
  groupedLogs[type] = [];
});

serviceMock.interceptors.response.use((response: AxiosResponse): AxiosResponse => {
    const request = response.request
    const serverRespData = response.data
    const {headers} = response;
    const signature = sessionStorage.getItem('signature')

    const headerSignature = headers?.signature;
    if (headerSignature && headerSignature !== signature && !request?.responseURL.includes('api/sys/release_version/latest') && !request?.responseURL.includes('api/auth/login')) {
      sessionStorage.setItem('signature', headerSignature)
      // 更新用户信息
      const userStore = useUserStore();
      userStore.info()
      // 更新菜单
      getMenuList().then(menus => {
        sessionStorage.setItem("menu-list", JSON.stringify(menus));
      })
    }

    if (response.status === 200) {
      const serverRespCode = serverRespData?.code
      if (!_.isNil(serverRespCode) && serverRespCode !== 0) {
        if (serverRespData.msg) {
          Message.error(serverRespData.msg)
        }
        throw new ApiError(request, response, serverRespData.msg || '响应失败')
      }
      // 如果消息头中增加了 content-disposition 表示为 文件下载，则直接返回
      const contentDisposition = headers['content-disposition']
      if (contentDisposition) {
        return response
      }

      // 非结构化数据
      if (serverRespData['code'] === undefined) {
        return serverRespData
      }

      // 结构化数据，对响应码的处理
      if (serverRespData.code !== 0) {
        throw new Error(serverRespData.msg || '请求失败');
      }

      // 返回实际的数据部分
      return convertKeysToCamelCase(serverRespData.data);

    }

    throw new ApiError(request, response, serverRespData.msg || '响应失败')

  }, (error) => {
    if (import.meta.env.MODE === 'base' || import.meta.env.MODE === 'development') {
      console.error('error', error)
    }

    if (error.code === "ERR_CANCELED" || error.name === "CanceledError") {
      return Promise.reject(error)
    }
    const serverResp = error.response
    const respStatusCode = serverResp?.status
    const serverRespData = serverResp?.data

    if (respStatusCode === 400) {
      // 服务端无法理解客户端请求信息
      return Promise.reject(serverRespData)
    }
    if (respStatusCode === 403) {
      // 没有权限的处理
      Message.warning(serverRespData?.detail || '非常抱歉，你没有权限访问该资源，请联系管理员')
      return Promise.reject(serverRespData)
    }
    if (respStatusCode === 401) {
      // 没有授权
      if (!window.location.href.includes('#/login')) {
        Message.warning(serverRespData?.detail || '非常抱歉，你还没有登录或身份过期，请先登录!');
        window.location.href = '#/login';
      } else {
        Message.warning(serverRespData?.detail || '登录失败，请检查用户名或密码!');
      }
      localStorage.removeItem('token');
      return Promise.reject(serverRespData)
    }
    if (respStatusCode === 409 && !webVersionConflict) {
      webVersionConflict = true
      getLatestReleaseVersion().then(res => {
        const changelog = res?.releaseNotes || ''
        // 处理字符串为数组，过滤空行
        const lines = changelog
          .split('\n')
          .map(line => line.trim())
          .filter(line => line);

        lines.forEach(item => {
          const match = item.match(/^\[(\w+)]\s(.+)/);
          if (match) {
            const [, type, content] = match;
            if (groupedLogs[type]) {
              groupedLogs[type].push(content);
            }
          }
        });

        // 构建内容 VNode
        const contentVNode = h('div', {}, Object.keys(groupedLogs).flatMap(type => {
          const items = groupedLogs[type];
          if (items.length === 0) return [];

          return [
            h('h3', {style: 'margin-top: 12px;'}, sectionMap[type]),
            h('ul', {}, items.map(item => h('li', {}, item))),
          ];
        }));

        Modal.info({
          title: '系统版本已更新',
          content: contentVNode,
          okText: '重启页面',
          maskClosable: false,
          onOk() {
            const token = localStorage.getItem('token'); // 先保存 token
            const loginConfig = localStorage.getItem('login-config'); // 先保存 token
            localStorage.clear();                        // 清空所有
            sessionStorage.clear();
            if (token !== null) {
              localStorage.setItem('token', token);      // 恢复 token
            }
            if (loginConfig !== null) {
              localStorage.setItem('login-config', loginConfig);      // 恢复 token
            }
            sessionStorage.setItem('web-version', JSON.stringify(res));
            localStorage.setItem('web-version', JSON.stringify(res));
            window.location.reload(true);
          },
        });
      });
      return Promise.reject({code: 409, detail: '客户端版本非最新版，请刷新页面！'})
    }
    if (respStatusCode === 500) {
      // 服务端报错
      return Promise.reject(serverRespData)
    }
    if (error.code === 'ERR_NETWORK') {
      return Promise.reject({code: -1, detail: '无法链接后端服务，请稍候重试'})
    }
    return Promise.reject(serverRespData);
  }
)

export default serviceMock
