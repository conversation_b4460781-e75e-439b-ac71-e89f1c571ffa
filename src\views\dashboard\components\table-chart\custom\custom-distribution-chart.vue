<template>
  <div class="chart-content">
    <a-spin :loading="loading" style="width: 100%;height: 100%">
      <Chart :option="chartOption"/>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue';
import {graphic} from 'echarts';
import useLoading from '@/hooks/loading';
import useChartOption from '@/hooks/chart-option';
import {AnyObject} from '@/types/global';
import {IndicatorQueryResult, WsEventAnalysisResultData} from "@/api/report/type";

interface Props {
  /**
   * 展示label
   */
  showLabel: boolean

  /**
   * 报表数据
   */
  reportAnalysisData: WsEventAnalysisResultData;
}

const props = defineProps<Props>()

function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12,
    },
  };
}

const {loading, setLoading} = useLoading(true);
const xAxis = ref<string[]>([]);
const ySeries = ref([]);
const legendData = ref([]);
const graphicElements = ref([
  graphicFactory({left: '2.6%'}),
  graphicFactory({right: 0}),
]);

function renderChart(wsResultData: WsEventAnalysisResultData) {
  if (!wsResultData) {
    return
  }
  const y = wsResultData?.y
  xAxis.value = y.map((record: IndicatorQueryResult) => record.displayName)
  const ySeriesResult = [];

  wsResultData?.groups.forEach(record => {
    const groupsName = record.join('.');
    const yData = [];
    wsResultData.y.forEach(yRecord => {
      wsResultData.y.forEach(yRecord => {
        // yData为null时，跳过
        if(!yRecord.yData){
          return
        }
        yRecord.yData.forEach(yDataRecord => {
          if (record.toString() === yDataRecord.group.toString()) {
            yData.push(yDataRecord.summaryValue.sum);
          }
        })
      });
    });
    ySeriesResult.push({
      data: yData,
      name: groupsName,
      type: 'bar',
      smooth: true,
      symbol: 'circle',
      showSymbol: false,
      label: {
        show: props.showLabel
      },
      areaStyle: {
        opacity: 0.8,
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(17, 126, 255, 0.16)',
          },
          {
            offset: 1,
            color: 'rgba(17, 128, 255, 0)',
          },
        ]),
      },
    })

  });
  ySeries.value = ySeriesResult;
  ySeriesResult?.forEach((item: any, index: number) => {
    legendData.value.push(item.name)
  })
  setLoading(false)
}

watch(() => props.reportAnalysisData, (newData, oldData) => {
  renderChart(newData);
})

const {chartOption} = useChartOption(() => {
  return {
    grid: {
      left: '0%',
      right: '1%',
      top: '20',
      bottom: '50',
      containLabel: true
    },
    legend: {
      data: legendData.value,
      type: 'scroll',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        formatter(value: any, idx: number) {
          if (idx === 0) return value;
          if (value > 1000 && value < 10000) return `${value / 1000}千`
          if (value >= 10000) return `${value / 10000}万`
          return `${value}`;
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E8EF',
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      className: 'echarts-tooltip-diy',
    },
    graphic: {
      elements: graphicElements.value,
    },
    series: ySeries.value
  };
});


onMounted(() => {
  if (!props.reportAnalysisData) {
    return
  }
  renderChart(props.reportAnalysisData)
})

</script>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
