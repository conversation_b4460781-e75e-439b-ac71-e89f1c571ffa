<script setup lang="ts">
import useChartOption from "@/hooks/chart-option";
import {ref, watch} from "vue";
import dayjs from 'dayjs';

interface Props {
    /**
   * 展示label
   */
  showlabel: boolean;
  showRate:boolean;
  seriesData: any;
}

const props = defineProps<Props>()

const {chartOption} = useChartOption(() => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}({d}%)'
      },
      series: [
            {
                type: 'pie',
                radius: ['35%', '58%'],
                center: ['50%', '50%'],
                bottom: '20',
                label:{
                formatter: '{b}'
                },
                labelLine: {
                    normal: {
                        show: true
                    }
                },
                data: props.seriesData,
            }
        ],
    };
});
// 处理 显示数值 显示百分比下 饼图显示格式
const handlePieLabel = () => {
    props.seriesData.forEach((item:any)=>{
        if(props.showRate && props.showlabel){
            item.label = {
                formatter:'{b}:{d}%({c})'
            }
        }else if(props.showRate){
            item.label = {
                formatter:'{b}:{d}%'
            }
        }else if(props.showlabel){
            item.label = {
                formatter:'{b}:({c})'
            }
        }else{
            item.label = {
                formatter:'{b}'
            }
        }
    })
}
watch(() => props.showlabel,(newValue:any,oldValue:any) => {
  handlePieLabel()
},{immediate:true})
watch(() => props.showRate,(newValue:any,oldValue:any) => {
  handlePieLabel()
  
},{immediate:true})
</script>

<template>
    <div style="width: 100%;height: 100%;">
        <Chart :option="chartOption"/>
    </div>
</template>

<style scoped lang="less">
.chart-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.arco-dropdown-open .arco-icon-down {
  transform: rotate(180deg);
}
:deep(.arco-dropdown-option){
  padding: 0;
}
:deep(.arco-checkbox){
  padding: 0 12px;
  &:hover{
    background-color: var(--color-fill-2);
  }
}
</style>