<template>
  <a-modal :visible="visible" title="绑定手机号" :footer="false" title-align="start" @cancel="handleCancel" @update:visible="updateVisible">
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="手机号" field="phone">
        <a-input v-model="form.phone" placeholder="请输入手机号" />
      </a-form-item>
      <a-form-item label="验证码" field="verifyCode">
        <a-input v-model="form.verifyCode" placeholder="请输入验证码" />
        <a-button :disabled="sending || countDown > 0" :loading="sending" style="margin-left: 12px" @click="sendVerifyCode">
          {{ countDown > 0 ? `重新发送(${countDown})` : '获取验证码' }}
        </a-button>
      </a-form-item>
    </a-form>

    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button :loading="loading" type="primary" @click="handleConfirm"> 立即绑定 </a-button>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import type { FormInstance } from '@arco-design/web-vue';
  import { getMsgCode, verifyMsgCode } from '@/api/authorize/api';
  import { saveSystemMember } from '@/api/setting/api';

  interface Props {
    visible: boolean;
    code?: string;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
    (e: 'refresh'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const sending = ref(false);
  const countDown = ref(0);

  const form = reactive({
    phone: '',
    verifyCode: '',
  });

  const rules = {
    phone: [
      { required: true, message: '请输入手机号' },
      { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
    ],
    verifyCode: [
      { required: true, message: '请输入验证码' },
      { match: /^\d{6}$/, message: '验证码为6位数字' },
    ],
  };

  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        form.phone = '';
        form.verifyCode = '';
      } else {
        formRef.value?.resetFields();
      }
    }
  );

  const updateVisible = (value: boolean) => {
    emit('update:visible', value);
  };

  const handleCancel = () => {
    updateVisible(false);
  };

  const sendVerifyCode = async () => {
    const phoneValid = await formRef.value?.validateField('phone');
    if (phoneValid) return;
    sending.value = true;
    try {
      await getMsgCode(form.phone, 'register');
      Message.success('验证码已发送，请注意查收');
      // 启动倒计时
      countDown.value = 60;
      const countdownInterval = setInterval(() => {
        countDown.value -= 1;
        if (countDown.value <= 0) {
          clearInterval(countdownInterval);
        }
      }, 1000);
    } catch (error: any) {
      Message.error('验证码发送失败，请重试');
    } finally {
      sending.value = false;
    }
  };
  // 验证码校验
  const checkCode = async () => {
    try {
      const result = await verifyMsgCode(form.phone, form.verifyCode);
      return result;
    } catch (error) {
      return false;
    }
  };
  const handleConfirm = async () => {
    formRef.value?.validate(async (valid: any) => {
      if (!valid) {
        loading.value = true;
        const isCodeValid = await checkCode();
        if (!isCodeValid) {
          loading.value = false;
          return;
        }
        try {
          await saveSystemMember({
            mobile: form.phone,
            code: props.code,
          });

          Message.success('手机号绑定成功');
          emit('refresh');
          updateVisible(false);
        } catch (error: any) {
          console.error('手机号绑定失败，请重试');
        } finally {
          loading.value = false;
        }
      }
    });
  };
</script>

<style scoped lang="less">
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
